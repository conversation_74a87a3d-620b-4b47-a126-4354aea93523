# Micro Core API 文档

## 核心 API

### MicroCore

主要的微前端管理器类。

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  container: '#app',
  sandbox: true,
  prefetch: true
});
```

#### 配置选项

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| container | string \| HTMLElement | '#app' | 主应用容器 |
| sandbox | boolean | true | 是否启用沙箱隔离 |
| prefetch | boolean | false | 是否预加载应用 |
| singular | boolean | false | 是否单例模式 |

#### 方法

##### registerApp(app)

注册微前端应用。

```typescript
microCore.registerApp({
  name: 'app1',
  entry: 'http://localhost:3001',
  container: '#container',
  activeRule: '/app1'
});
```

**参数：**
- `app` (AppConfig): 应用配置对象

**返回值：**
- `Promise<void>`

##### start()

启动微前端框架。

```typescript
await microCore.start();
```

**返回值：**
- `Promise<void>`

##### loadApp(name)

加载指定的微前端应用。

```typescript
await microCore.loadApp('app1');
```

**参数：**
- `name` (string): 应用名称

**返回值：**
- `Promise<Application>`

##### unloadApp(name)

卸载指定的微前端应用。

```typescript
await microCore.unloadApp('app1');
```

**参数：**
- `name` (string): 应用名称

**返回值：**
- `Promise<void>`

## 应用配置

### AppConfig

```typescript
interface AppConfig {
  name: string;
  entry: string | AppEntry;
  container: string | HTMLElement;
  activeRule: string | ((location: Location) => boolean);
  loader?: (app: AppConfig) => Promise<AppEntry>;
  props?: Record<string, any>;
  sandbox?: boolean | SandboxConfig;
}
```

### AppEntry

```typescript
interface AppEntry {
  scripts?: string[];
  styles?: string[];
  html?: string;
  template?: string;
}
```

## 生命周期钩子

### 应用生命周期

```typescript
interface LifeCycles {
  beforeLoad?: (app: Application) => Promise<void>;
  beforeMount?: (app: Application) => Promise<void>;
  afterMount?: (app: Application) => Promise<void>;
  beforeUnmount?: (app: Application) => Promise<void>;
  afterUnmount?: (app: Application) => Promise<void>;
}
```

### 全局生命周期

```typescript
microCore.addGlobalLifeCycle({
  beforeLoad: async (app) => {
    console.log(`应用 ${app.name} 开始加载`);
  },
  afterMount: async (app) => {
    console.log(`应用 ${app.name} 挂载完成`);
  }
});
```

## 通信 API

### EventBus

应用间事件通信。

```typescript
import { eventBus } from '@micro-core/communication';

// 发送事件
eventBus.emit('user-login', { userId: 123 });

// 监听事件
eventBus.on('user-login', (data) => {
  console.log('用户登录:', data);
});

// 取消监听
eventBus.off('user-login', handler);
```

### MessageChannel

应用间消息通信。

```typescript
import { messageChannel } from '@micro-core/communication';

// 发送消息
messageChannel.send('app1', 'getData', { id: 123 });

// 监听消息
messageChannel.on('getData', async (data, reply) => {
  const result = await fetchData(data.id);
  reply(result);
});
```

## 路由 API

### Router

微前端路由管理。

```typescript
import { Router } from '@micro-core/router';

const router = new Router({
  mode: 'history',
  base: '/'
});

// 注册路由
router.register({
  path: '/app1/*',
  app: 'app1'
});

// 导航
router.push('/app1/dashboard');

// 监听路由变化
router.onRouteChange((route) => {
  console.log('路由变化:', route);
});
```

## 插件 API

### Plugin

插件系统。

```typescript
import { Plugin } from '@micro-core/plugins';

class MyPlugin extends Plugin {
  name = 'my-plugin';
  
  install(microCore: MicroCore) {
    // 插件安装逻辑
  }
  
  beforeLoad(app: Application) {
    // 应用加载前钩子
  }
}

// 使用插件
microCore.use(new MyPlugin());
```

## 工具 API

### 沙箱

```typescript
import { createSandbox } from '@micro-core/sandbox';

const sandbox = createSandbox({
  strictStyleIsolation: true,
  experimentalStyleIsolation: true
});

sandbox.mount(container, app);
```

### 资源加载器

```typescript
import { ResourceLoader } from '@micro-core/resource';

const loader = new ResourceLoader({
  cache: true,
  timeout: 30000
});

const resources = await loader.load({
  scripts: ['app.js'],
  styles: ['app.css']
});
```

## 类型定义

### Application

```typescript
interface Application {
  name: string;
  status: AppStatus;
  entry: AppEntry;
  container: HTMLElement;
  sandbox?: Sandbox;
  props?: Record<string, any>;
  lifeCycles?: LifeCycles;
}
```

### AppStatus

```typescript
enum AppStatus {
  NOT_LOADED = 'NOT_LOADED',
  LOADING = 'LOADING',
  LOADED = 'LOADED',
  MOUNTING = 'MOUNTING',
  MOUNTED = 'MOUNTED',
  UNMOUNTING = 'UNMOUNTING',
  UNMOUNTED = 'UNMOUNTED',
  LOAD_ERROR = 'LOAD_ERROR',
  MOUNT_ERROR = 'MOUNT_ERROR'
}
```

## 错误处理

### ErrorHandler

```typescript
import { ErrorHandler } from '@micro-core/core';

const errorHandler = new ErrorHandler({
  onError: (error, app) => {
    console.error(`应用 ${app?.name} 发生错误:`, error);
  }
});

microCore.setErrorHandler(errorHandler);
```

## 示例

### 基础使用

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  container: '#app',
  sandbox: true
});

// 注册应用
microCore.registerApp({
  name: 'vue-app',
  entry: 'http://localhost:3001',
  container: '#vue-container',
  activeRule: '/vue'
});

microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3002',
  container: '#react-container',
  activeRule: '/react'
});

// 启动
microCore.start();
```

### 高级配置

```typescript
const microCore = new MicroCore({
  container: '#app',
  sandbox: {
    strictStyleIsolation: true,
    experimentalStyleIsolation: true
  },
  prefetch: ['vue-app'],
  singular: false
});

// 添加全局生命周期
microCore.addGlobalLifeCycle({
  beforeLoad: async (app) => {
    console.log(`开始加载应用: ${app.name}`);
  },
  afterMount: async (app) => {
    console.log(`应用挂载完成: ${app.name}`);
  }
});

// 错误处理
microCore.setErrorHandler({
  onError: (error, app) => {
    console.error('应用错误:', error);
    // 上报错误
  }
});
```

更多详细信息请参考 [完整文档](./README.md)。