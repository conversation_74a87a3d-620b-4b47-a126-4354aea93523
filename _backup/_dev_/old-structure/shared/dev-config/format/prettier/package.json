{"name": "@micro-core/prettier-config", "version": "0.1.0", "description": "Shared Prettier configuration for Micro-Core packages", "main": "index.js", "files": ["index.js"], "keywords": ["prettier", "config", "micro-core"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "peerDependencies": {"prettier": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/dev-config/format/prettier"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest run", "test:watch": "vitest", "type-check": "tsc --noEmit"}}