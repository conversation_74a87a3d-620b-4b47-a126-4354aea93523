{"name": "@micro-core/vitest-config", "version": "0.1.0", "description": "Vitest configuration for Micro-Core", "main": "index.js", "types": "index.d.ts", "files": ["*.js", "*.d.ts"], "scripts": {"test": "vitest run __tests__", "test:watch": "vitest __tests__", "build": "vite build", "dev": "vite build --watch", "type-check": "tsc --noEmit"}, "keywords": ["vitest", "vitestconfig", "micro-core"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/dev-config/test/vitest"}, "publishConfig": {"access": "public"}, "dependencies": {"@vitest/coverage-v8": "^3.2.4", "jsdom": "^23.0.1"}, "peerDependencies": {"vitest": ">=1.0.0"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}