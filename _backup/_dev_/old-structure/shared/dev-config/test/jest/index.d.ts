/**
 * @fileoverview Jest configuration types for Micro-Core projects
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { Config } from '@jest/types';

/**
 * Jest configuration object
 */
export interface JestConfig extends Config.InitialOptions {}

/**
 * Base Jest configuration
 */
export declare const base: JestConfig;

/**
 * Node.js specific Jest configuration
 */
export declare const node: JestConfig;

/**
 * React specific Jest configuration
 */
export declare const react: JestConfig;
