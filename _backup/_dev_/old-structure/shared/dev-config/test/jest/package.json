{"name": "@micro-core/jest-config", "version": "0.1.0", "description": "Jest configuration for Micro-Core", "main": "index.js", "types": "index.d.ts", "files": ["*.js", "*.d.ts"], "scripts": {"test": "jest __tests__", "test:watch": "jest __tests__ --watch", "build": "vite build", "dev": "vite build --watch", "type-check": "tsc --noEmit"}, "keywords": ["jest", "jestconfig", "micro-core", "testing"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/dev-config/test/jest"}, "publishConfig": {"access": "public"}, "dependencies": {"@types/jest": "^29.5.0"}, "devDependencies": {"jest": "^29.5.0"}, "peerDependencies": {"jest": ">=29.0.0"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}