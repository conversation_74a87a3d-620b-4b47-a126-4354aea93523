/**
 * @fileoverview Jest configuration tests
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

const { base, node, react } = require('../index');

describe('@micro-core/jest-config', () => {
    describe('base configuration', () => {
        it('should have correct test environment', () => {
            expect(base.testEnvironment).toBe('jsdom');
        });

        it('should have setup files', () => {
            expect(base.setupFilesAfterEnv).toContain('<rootDir>/test/setup.ts');
        });

        it('should have correct module file extensions', () => {
            expect(base.moduleFileExtensions).toEqual(['ts', 'tsx', 'js', 'jsx', 'json']);
        });

        it('should have transform configuration', () => {
            expect(base.transform).toHaveProperty('^.+\\.(ts|tsx)$', 'ts-jest');
            expect(base.transform).toHaveProperty('^.+\\.(js|jsx)$', 'babel-jest');
        });

        it('should have module name mapping', () => {
            expect(base.moduleNameMapping).toHaveProperty('^@/(.*)$', '<rootDir>/src/$1');
            expect(base.moduleNameMapping).toHaveProperty('^@micro-core/(.*)$', '<rootDir>/packages/$1/src');
        });

        it('should have test match patterns', () => {
            expect(base.testMatch).toContain('<rootDir>/src/**/__tests__/**/*.(ts|tsx|js|jsx)');
            expect(base.testMatch).toContain('<rootDir>/src/**/*.(test|spec).(ts|tsx|js|jsx)');
        });

        it('should have coverage configuration', () => {
            expect(base.collectCoverageFrom).toContain('src/**/*.(ts|tsx|js|jsx)');
            expect(base.collectCoverageFrom).toContain('!src/**/*.d.ts');
        });

        it('should have coverage thresholds', () => {
            expect(base.coverageThreshold.global.branches).toBe(80);
            expect(base.coverageThreshold.global.functions).toBe(80);
            expect(base.coverageThreshold.global.lines).toBe(80);
            expect(base.coverageThreshold.global.statements).toBe(80);
        });

        it('should have coverage reporters', () => {
            expect(base.coverageReporters).toEqual(['text', 'lcov', 'html']);
        });

        it('should have ignore patterns', () => {
            expect(base.testPathIgnorePatterns).toContain('<rootDir>/node_modules/');
            expect(base.testPathIgnorePatterns).toContain('<rootDir>/dist/');
        });
    });

    describe('node configuration', () => {
        it('should extend base configuration', () => {
            expect(node.testEnvironment).toBe('node');
            expect(node.moduleFileExtensions).toEqual(base.moduleFileExtensions);
        });

        it('should not have setup files', () => {
            expect(node.setupFilesAfterEnv).toBeUndefined();
        });
    });

    describe('react configuration', () => {
        it('should extend base configuration', () => {
            expect(react.testEnvironment).toBe('jsdom');
            expect(react.moduleFileExtensions).toEqual(base.moduleFileExtensions);
        });

        it('should have additional setup files', () => {
            expect(react.setupFilesAfterEnv).toContain('<rootDir>/test/setup.ts');
            expect(react.setupFilesAfterEnv).toContain('@testing-library/jest-dom');
        });
    });

    describe('configuration validation', () => {
        it('should have valid configuration objects', () => {
            expect(typeof base).toBe('object');
            expect(typeof node).toBe('object');
            expect(typeof react).toBe('object');
        });

        it('should not have circular references', () => {
            expect(() => JSON.stringify(base)).not.toThrow();
            expect(() => JSON.stringify(node)).not.toThrow();
            expect(() => JSON.stringify(react)).not.toThrow();
        });
    });
});
