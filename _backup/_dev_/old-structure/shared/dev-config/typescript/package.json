{"name": "@micro-core/ts-config", "version": "0.1.0", "description": "Shared TypeScript configuration for Micro-Core packages", "main": "base.json", "files": ["*.json"], "keywords": ["typescript", "config", "micro-core"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/dev-config/typescript"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest run", "test:watch": "vitest", "type-check": "tsc --noEmit"}}