{"name": "@micro-core/shared-constants", "version": "0.1.0", "description": "Micro-Core shared constants", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"test": "vitest", "test:coverage": "vitest --coverage", "clean": "<PERSON><PERSON><PERSON> dist", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "constants", "shared"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"rimraf": "^5.0.0", "typescript": "^5.3.3", "vitest": "^3.2.4"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/constants"}}