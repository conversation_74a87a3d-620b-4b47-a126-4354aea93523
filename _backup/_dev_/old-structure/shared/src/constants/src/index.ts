/**
 * @fileoverview 共享常量定义 - 微前端架构核心常量
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 微前端应用状态枚举
 */
export enum AppStatus {
    /** 未初始化 */
    NOT_LOADED = 'NOT_LOADED',
    /** 加载中 */
    LOADING_SOURCE_CODE = 'LOADING_SOURCE_CODE',
    /** 源码已加载 */
    NOT_BOOTSTRAPPED = 'NOT_BOOTSTRAPPED',
    /** 启动中 */
    BOOTSTRAPPING = 'BOOTSTRAPPING',
    /** 未挂载 */
    NOT_MOUNTED = 'NOT_MOUNTED',
    /** 挂载中 */
    MOUNTING = 'MOUNTING',
    /** 已挂载 */
    MOUNTED = 'MOUNTED',
    /** 卸载中 */
    UNMOUNTING = 'UNMOUNTING',
    /** 更新中 */
    UPDATING = 'UPDATING',
    /** 加载失败 */
    LOAD_ERROR = 'LOAD_ERROR',
    /** 跳过加载 */
    SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN'
}

/**
 * 共享错误代码
 */
export enum ErrorCodes {
    // 系统级错误 (1000-1999)
    /** 系统初始化失败 */
    SYSTEM_INIT_FAILED = 1000,
    /** 系统配置错误 */
    SYSTEM_CONFIG_ERROR = 1001,
    /** 系统资源不足 */
    SYSTEM_RESOURCE_ERROR = 1002,

    // 应用级错误 (2000-2999)
    /** 应用加载失败 */
    APP_LOAD_FAILED = 2000,
    /** 应用启动失败 */
    APP_BOOTSTRAP_FAILED = 2001,
    /** 应用挂载失败 */
    APP_MOUNT_FAILED = 2002,
    /** 应用卸载失败 */
    APP_UNMOUNT_FAILED = 2003,
    /** 应用更新失败 */
    APP_UPDATE_FAILED = 2004,
    /** 应用配置无效 */
    APP_CONFIG_INVALID = 2005,

    // 网络级错误 (3000-3999)
    /** 网络连接失败 */
    NETWORK_CONNECTION_FAILED = 3000,
    /** 网络超时 */
    NETWORK_TIMEOUT = 3001,
    /** 资源加载失败 */
    RESOURCE_LOAD_FAILED = 3002,
    /** 跨域错误 */
    CORS_ERROR = 3003,

    // 权限级错误 (4000-4999)
    /** 权限不足 */
    PERMISSION_DENIED = 4000,
    /** 认证失败 */
    AUTHENTICATION_FAILED = 4001,
    /** 授权失败 */
    AUTHORIZATION_FAILED = 4002,

    // 验证级错误 (5000-5999)
    /** 参数验证失败 */
    VALIDATION_FAILED = 5000,
    /** 数据格式错误 */
    DATA_FORMAT_ERROR = 5001,
    /** 类型检查失败 */
    TYPE_CHECK_FAILED = 5002
}

/**
 * 共享事件类型
 */
export enum EventTypes {
    // 系统事件
    /** 系统初始化开始 */
    SYSTEM_INIT_START = 'system:init:start',
    /** 系统初始化完成 */
    SYSTEM_INIT_COMPLETE = 'system:init:complete',
    /** 系统初始化失败 */
    SYSTEM_INIT_FAILED = 'system:init:failed',
    /** 系统销毁 */
    SYSTEM_DESTROY = 'system:destroy',
    /** 系统错误 */
    SYSTEM_ERROR = 'system:error',

    // 应用事件
    /** 应用注册 */
    APP_REGISTER = 'app:register',
    /** 应用注销 */
    APP_UNREGISTER = 'app:unregister',
    /** 应用状态变更 */
    APP_STATUS_CHANGE = 'app:status:change',
    /** 应用加载开始 */
    APP_LOAD_START = 'app:load:start',
    /** 应用加载完成 */
    APP_LOAD_COMPLETE = 'app:load:complete',
    /** 应用加载失败 */
    APP_LOAD_FAILED = 'app:load:failed',
    /** 应用启动开始 */
    APP_BOOTSTRAP_START = 'app:bootstrap:start',
    /** 应用启动完成 */
    APP_BOOTSTRAP_COMPLETE = 'app:bootstrap:complete',
    /** 应用启动失败 */
    APP_BOOTSTRAP_FAILED = 'app:bootstrap:failed',
    /** 应用挂载开始 */
    APP_MOUNT_START = 'app:mount:start',
    /** 应用挂载完成 */
    APP_MOUNT_COMPLETE = 'app:mount:complete',
    /** 应用挂载失败 */
    APP_MOUNT_FAILED = 'app:mount:failed',
    /** 应用卸载开始 */
    APP_UNMOUNT_START = 'app:unmount:start',
    /** 应用卸载完成 */
    APP_UNMOUNT_COMPLETE = 'app:unmount:complete',
    /** 应用卸载失败 */
    APP_UNMOUNT_FAILED = 'app:unmount:failed',

    // 路由事件
    /** 路由变更开始 */
    ROUTE_CHANGE_START = 'route:change:start',
    /** 路由变更完成 */
    ROUTE_CHANGE_COMPLETE = 'route:change:complete',
    /** 路由变更失败 */
    ROUTE_CHANGE_FAILED = 'route:change:failed',

    // 配置事件
    /** 配置更新 */
    CONFIG_UPDATE = 'config:update',
    /** 配置验证失败 */
    CONFIG_VALIDATION_FAILED = 'config:validation:failed',

    // 通信事件
    /** 消息发送 */
    MESSAGE_SEND = 'message:send',
    /** 消息接收 */
    MESSAGE_RECEIVE = 'message:receive',
    /** 通信错误 */
    COMMUNICATION_ERROR = 'communication:error'
}

/**
 * HTTP 状态码常量
 */
export const HTTP_STATUS = {
    // 成功状态码 (2xx)
    OK: 200,
    CREATED: 201,
    ACCEPTED: 202,
    NO_CONTENT: 204,

    // 重定向状态码 (3xx)
    MOVED_PERMANENTLY: 301,
    FOUND: 302,
    NOT_MODIFIED: 304,

    // 客户端错误状态码 (4xx)
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    TOO_MANY_REQUESTS: 429,

    // 服务器错误状态码 (5xx)
    INTERNAL_SERVER_ERROR: 500,
    NOT_IMPLEMENTED: 501,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504
} as const;

/**
 * 共享配置常量
 */
export const CONFIG = {
    // 超时配置
    TIMEOUT: {
        /** 默认超时时间 (30秒) */
        DEFAULT: 30000,
        /** 短超时时间 (5秒) */
        SHORT: 5000,
        /** 长超时时间 (60秒) */
        LONG: 60000,
        /** 资源加载超时 (10秒) */
        RESOURCE_LOAD: 10000,
        /** 应用启动超时 (15秒) */
        APP_BOOTSTRAP: 15000,
        /** 应用挂载超时 (10秒) */
        APP_MOUNT: 10000
    },

    // 重试配置
    RETRY: {
        /** 默认最大重试次数 */
        MAX_ATTEMPTS: 3,
        /** 资源加载重试次数 */
        RESOURCE_LOAD: 2,
        /** 网络请求重试次数 */
        NETWORK_REQUEST: 3,
        /** 重试延迟基数 (毫秒) */
        DELAY_BASE: 1000,
        /** 最大重试延迟 (毫秒) */
        MAX_DELAY: 10000
    },

    // 缓存配置
    CACHE: {
        /** 默认缓存大小 */
        DEFAULT_SIZE: 100,
        /** 最大缓存大小 */
        MAX_SIZE: 1000,
        /** 缓存过期时间 (1小时) */
        DEFAULT_TTL: 3600000,
        /** 短期缓存过期时间 (5分钟) */
        SHORT_TTL: 300000,
        /** 长期缓存过期时间 (24小时) */
        LONG_TTL: 86400000
    },

    // 性能配置
    PERFORMANCE: {
        /** 内存使用警告阈值 (80%) */
        MEMORY_WARNING_THRESHOLD: 80,
        /** 内存使用临界阈值 (90%) */
        MEMORY_CRITICAL_THRESHOLD: 90,
        /** 性能监控间隔 (5秒) */
        MONITOR_INTERVAL: 5000,
        /** 最大并发请求数 */
        MAX_CONCURRENT_REQUESTS: 10
    },

    // 日志配置
    LOG: {
        /** 最大日志条数 */
        MAX_ENTRIES: 1000,
        /** 日志清理间隔 (10分钟) */
        CLEANUP_INTERVAL: 600000,
        /** 控制台日志颜色 */
        COLORS: {
            DEBUG: '#6B7280',
            INFO: '#3B82F6',
            WARN: '#F59E0B',
            ERROR: '#EF4444'
        }
    }
} as const;

/**
 * 正则表达式常量
 */
export const REGEX = {
    /** URL 验证 */
    URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}(\.[a-zA-Z0-9()]{1,6})?(:\d+)?\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
    /** 邮箱验证 */
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    /** 手机号验证 (中国) */
    PHONE_CN: /^1[3-9]\d{9}$/,
    /** IPv4 地址验证 */
    IPV4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    /** 版本号验证 (语义化版本) */
    SEMVER: /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/,
    /** 应用名称验证 (字母、数字、连字符、下划线) */
    APP_NAME: /^[a-zA-Z][a-zA-Z0-9_-]*$/,
    /** 路径验证 */
    PATH: /^\/[a-zA-Z0-9_\-\/]*$/,
    /** HTML 标签清理 */
    HTML_TAGS: /<[^>]*>/g,
    /** 空白字符 */
    WHITESPACE: /\s+/g
} as const;

/**
 * MIME 类型常量
 */
export const MIME_TYPES = {
    // 文本类型
    TEXT_PLAIN: 'text/plain',
    TEXT_HTML: 'text/html',
    TEXT_CSS: 'text/css',
    TEXT_JAVASCRIPT: 'text/javascript',

    // 应用类型
    APPLICATION_JSON: 'application/json',
    APPLICATION_XML: 'application/xml',
    APPLICATION_PDF: 'application/pdf',
    APPLICATION_ZIP: 'application/zip',
    APPLICATION_OCTET_STREAM: 'application/octet-stream',

    // 图片类型
    IMAGE_JPEG: 'image/jpeg',
    IMAGE_PNG: 'image/png',
    IMAGE_GIF: 'image/gif',
    IMAGE_SVG: 'image/svg+xml',
    IMAGE_WEBP: 'image/webp',

    // 音频类型
    AUDIO_MP3: 'audio/mpeg',
    AUDIO_WAV: 'audio/wav',
    AUDIO_OGG: 'audio/ogg',

    // 视频类型
    VIDEO_MP4: 'video/mp4',
    VIDEO_WEBM: 'video/webm',
    VIDEO_OGG: 'video/ogg'
} as const;

/**
 * 浏览器特性检测常量
 */
export const BROWSER_FEATURES = {
    /** 支持的存储类型 */
    STORAGE: ['localStorage', 'sessionStorage', 'indexedDB'] as const,
    /** 支持的网络 API */
    NETWORK: ['fetch', 'XMLHttpRequest', 'WebSocket'] as const,
    /** 支持的 Worker 类型 */
    WORKERS: ['Worker', 'ServiceWorker', 'SharedWorker'] as const,
    /** 支持的媒体 API */
    MEDIA: ['MediaDevices', 'MediaRecorder', 'WebRTC'] as const,
    /** 支持的图形 API */
    GRAPHICS: ['Canvas', 'WebGL', 'WebGL2'] as const
} as const;

/**
 * 默认配置模板
 */
export const DEFAULT_APP_CONFIG = {
    /** 应用名称 */
    name: '',
    /** 应用入口 */
    entry: '',
    /** 容器选择器 */
    container: '',
    /** 激活规则 */
    activeRule: '',
    /** 应用属性 */
    props: {},
    /** 加载配置 */
    loader: {
        timeout: CONFIG.TIMEOUT.RESOURCE_LOAD,
        retries: CONFIG.RETRY.RESOURCE_LOAD,
        cache: true
    },
    /** 生命周期钩子 */
    hooks: {
        beforeLoad: null,
        afterLoad: null,
        beforeMount: null,
        afterMount: null,
        beforeUnmount: null,
        afterUnmount: null
    }
} as const;

/**
 * 环境变量键名
 */
export const ENV_KEYS = {
    /** 运行环境 */
    NODE_ENV: 'NODE_ENV',
    /** 调试模式 */
    DEBUG: 'DEBUG',
    /** API 基础地址 */
    API_BASE_URL: 'API_BASE_URL',
    /** 应用版本 */
    APP_VERSION: 'APP_VERSION',
    /** 构建时间 */
    BUILD_TIME: 'BUILD_TIME'
} as const;

/**
 * 存储键名前缀
 */
export const STORAGE_KEYS = {
    /** 应用配置 */
    APP_CONFIG: 'micro-core:app-config',
    /** 用户设置 */
    USER_SETTINGS: 'micro-core:user-settings',
    /** 缓存数据 */
    CACHE_DATA: 'micro-core:cache',
    /** 日志数据 */
    LOG_DATA: 'micro-core:logs',
    /** 性能数据 */
    PERFORMANCE_DATA: 'micro-core:performance'
} as const;

/**
 * CSS 类名前缀
 */
export const CSS_PREFIXES = {
    /** 微前端容器 */
    MICRO_APP: 'micro-app',
    /** 加载状态 */
    LOADING: 'micro-loading',
    /** 错误状态 */
    ERROR: 'micro-error',
    /** 隐藏状态 */
    HIDDEN: 'micro-hidden'
} as const;

/**
 * 数据属性名
 */
export const DATA_ATTRIBUTES = {
    /** 应用名称 */
    APP_NAME: 'data-micro-app',
    /** 应用状态 */
    APP_STATUS: 'data-micro-status',
    /** 应用版本 */
    APP_VERSION: 'data-micro-version',
    /** 容器标识 */
    CONTAINER: 'data-micro-container'
} as const;

/**
 * 元数据键名
 */
export const META_KEYS = {
    /** 应用元数据 */
    APP_META: '__MICRO_APP_META__',
    /** 全局配置 */
    GLOBAL_CONFIG: '__MICRO_GLOBAL_CONFIG__',
    /** 共享数据 */
    SHARED_DATA: '__MICRO_SHARED_DATA__',
    /** 事件总线 */
    EVENT_BUS: '__MICRO_EVENT_BUS__'
} as const;

/**
 * 常量集合导出
 */
export const constants = {
    AppStatus,
    ErrorCodes,
    EventTypes,
    HTTP_STATUS,
    CONFIG,
    REGEX,
    MIME_TYPES,
    BROWSER_FEATURES,
    DEFAULT_APP_CONFIG,
    ENV_KEYS,
    STORAGE_KEYS,
    CSS_PREFIXES,
    DATA_ATTRIBUTES,
    META_KEYS
} as const;

// 向后兼容的导出
export const SharedErrorCodes = ErrorCodes;
export const SharedEventTypes = EventTypes;
export const SHARED_CONFIG = CONFIG;