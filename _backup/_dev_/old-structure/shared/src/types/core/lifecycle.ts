/**
 * @fileoverview 生命周期相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 生命周期函数参数
 */
export interface LifecycleProps {
    /** 应用名称 */
    name: string;
    /** 容器元素 */
    container: HTMLElement;
    /** 自定义属性 */
    props: Record<string, any>;
    /** 沙箱实例 */
    sandbox?: any;
    /** 事件总线 */
    eventBus?: any;
}

/**
 * 生命周期函数类型
 */
export type LifecycleFunction = (props: LifecycleProps) => void | Promise<void>;

/**
 * 生命周期阶段
 */
export type LifecyclePhase =
    | 'bootstrap'
    | 'mount'
    | 'unmount'
    | 'update';

/**
 * 生命周期状态
 */
export interface LifecycleState {
    /** 当前阶段 */
    phase: LifecyclePhase | null;
    /** 是否正在执行 */
    executing: boolean;
    /** 开始时间 */
    startTime: number;
    /** 结束时间 */
    endTime: number;
    /** 执行时长 */
    duration: number;
    /** 错误信息 */
    error: Error | null;
}