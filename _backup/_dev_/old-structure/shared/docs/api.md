# API Reference

Complete API reference for @micro-core/shared package.

## Quick Reference

### Utils
- **Type Checking**: `isFunction`, `isObject`, `isString`, `isNumber`, `isBoolean`, `isArray`, `isPromise`, `isEmpty`
- **Object Operations**: `deepMerge`, `deepClone`, `get`, `set`, `pick`, `omit`, `flatten`
- **String Processing**: `camelCase`, `kebabCase`, `snakeCase`, `capitalize`, `truncate`, `template`, `uuid`
- **Array Operations**: `unique`, `flatten`, `chunk`, `shuffle`, `groupBy`, `sortBy`
- **Function Utils**: `debounce`, `throttle`, `memoize`, `curry`, `compose`, `once`
- **Async Utils**: `sleep`, `timeout`, `retry`, `parallel`, `series`, `queue`
- **Logger**: `createLogger` with levels and namespaces
- **Event Bus**: Full-featured event system with priorities and filters

### Helpers
- **Error Handling**: `formatErrorMessage`, `safeExecute`
- **App Config**: `validateAppConfig`, `normalizeAppConfig`
- **Performance**: `createPerformanceTimer`, `getMemoryInfo`, `createMemoryMonitor`
- **Browser Compatibility**: `checkBrowserCompatibility`, `detectEnvironmentFeatures`
- **Resource Loading**: `loadResource` with caching and retries
- **Data Validation**: `validateAndSanitize`

### Constants
- **App Status**: `AppStatus` enum with lifecycle states
- **Error Codes**: `ErrorCodes` categorized by type (1000-5999)
- **Event Types**: `EventTypes` for system, app, route events
- **Config**: `CONFIG` with timeout, retry, cache settings
- **Regex**: `REGEX` patterns for validation
- **HTTP Status**: `HTTP_STATUS` common codes

### Types
- **Micro-App**: `MicroAppConfig`, `MicroAppInstance`, `AppLifecycle`
- **Communication**: `Message`, `EventEmitter`, `CommunicationAdapter`
- **Utility**: `DeepReadonly`, `DeepPartial`, `Result`, `Option`
- **Validation**: `ValidationRule`, `ValidationResult`

## Utils

### Type Checking

#### `isFunction(value: any): boolean`
Checks if a value is a function.

```typescript
isFunction(() => {}); // true
isFunction('not a function'); // false
```

#### `isObject(value: any): boolean`
Checks if a value is an object (excluding arrays and null).

```typescript
isObject({}); // true
isObject([]); // false
isObject(null); // false
```

#### `isString(value: any): boolean`
Checks if a value is a string.

```typescript
isString('hello'); // true
isString(123); // false
```

#### `isNumber(value: any): boolean`
Checks if a value is a number (excluding NaN).

```typescript
isNumber(123); // true
isNumber(NaN); // false
```

#### `isBoolean(value: any): boolean`
Checks if a value is a boolean.

```typescript
isBoolean(true); // true
isBoolean(1); // false
```

#### `isArray(value: any): boolean`
Checks if a value is an array.

```typescript
isArray([]); // true
isArray({}); // false
```

#### `isPromise(value: any): boolean`
Checks if a value is a Promise or thenable.

```typescript
isPromise(Promise.resolve()); // true
isPromise({ then: () => {} }); // true
```

#### `isEmpty(value: any): boolean`
Checks if a value is empty (null, undefined, empty string, array, or object).

```typescript
isEmpty(null); // true
isEmpty(''); // true
isEmpty([]); // true
isEmpty({}); // true
isEmpty('hello'); // false
```

#### `isDate(value: any): boolean`
Checks if a value is a valid Date object.

```typescript
isDate(new Date()); // true
isDate(new Date('invalid')); // false
```

#### `isRegExp(value: any): boolean`
Checks if a value is a RegExp object.

```typescript
isRegExp(/test/); // true
isRegExp('test'); // false
```

#### `isError(value: any): boolean`
Checks if a value is an Error object.

```typescript
isError(new Error()); // true
isError('error message'); // false
```

#### `getType(value: any): string`
Gets the type of a value as a string.

```typescript
getType([]); // 'array'
getType({}); // 'object'
getType(null); // 'null'
getType(undefined); // 'undefined'
```

#### `isSameType(a: any, b: any): boolean`
Checks if two values have the same type.

```typescript
isSameType('a', 'b'); // true
isSameType(1, 2); // true
isSameType('a', 1); // false
```

### Object Operations

#### `deepMerge<T>(...objects: Partial<T>[]): T`
Deeply merges multiple objects.

```typescript
const result = deepMerge(
  { a: 1, b: { c: 2 } },
  { b: { d: 3 }, e: 4 }
);
// Result: { a: 1, b: { c: 2, d: 3 }, e: 4 }
```

#### `deepClone<T>(obj: T): T`
Creates a deep copy of an object.

```typescript
const original = { a: { b: 1 } };
const cloned = deepClone(original);
cloned.a.b = 2; // original.a.b is still 1
```

#### `get<T>(obj: any, path: string | string[], defaultValue?: T): T`
Gets a value from an object using a path.

```typescript
const obj = { a: { b: { c: 'value' } } };
get(obj, 'a.b.c'); // 'value'
get(obj, 'a.b.x', 'default'); // 'default'
get(obj, ['a', 'b', 'c']); // 'value'
```

#### `set(obj: any, path: string | string[], value: any): void`
Sets a value in an object using a path.

```typescript
const obj = {};
set(obj, 'a.b.c', 'value');
// obj is now { a: { b: { c: 'value' } } }
```

#### `unset(obj: any, path: string | string[]): boolean`
Removes a property from an object using a path.

```typescript
const obj = { a: { b: { c: 'value' } } };
unset(obj, 'a.b.c'); // true
// obj is now { a: { b: {} } }
```

#### `has(obj: any, path: string | string[]): boolean`
Checks if an object has a property at the given path.

```typescript
const obj = { a: { b: { c: 'value' } } };
has(obj, 'a.b.c'); // true
has(obj, 'a.b.x'); // false
```

#### `pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>`
Creates an object with only the specified keys.

```typescript
const obj = { a: 1, b: 2, c: 3 };
pick(obj, ['a', 'c']); // { a: 1, c: 3 }
```

#### `omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>`
Creates an object without the specified keys.

```typescript
const obj = { a: 1, b: 2, c: 3 };
omit(obj, ['b']); // { a: 1, c: 3 }
```

### String Processing

#### `camelCase(str: string): string`
Converts a string to camelCase.

```typescript
camelCase('hello-world'); // 'helloWorld'
camelCase('hello_world'); // 'helloWorld'
```

#### `kebabCase(str: string): string`
Converts a string to kebab-case.

```typescript
kebabCase('helloWorld'); // 'hello-world'
kebabCase('HelloWorld'); // 'hello-world'
```

#### `snakeCase(str: string): string`
Converts a string to snake_case.

```typescript
snakeCase('helloWorld'); // 'hello_world'
snakeCase('HelloWorld'); // 'hello_world'
```

#### `pascalCase(str: string): string`
Converts a string to PascalCase.

```typescript
pascalCase('hello-world'); // 'HelloWorld'
pascalCase('hello_world'); // 'HelloWorld'
```

#### `capitalize(str: string): string`
Capitalizes the first character of a string.

```typescript
capitalize('hello'); // 'Hello'
capitalize('HELLO'); // 'HELLO'
```

#### `truncate(str: string, length: number, suffix?: string): string`
Truncates a string to the specified length.

```typescript
truncate('hello world', 5); // 'he...'
truncate('hello world', 8, '---'); // 'hello---'
```

#### `template(str: string, data: Record<string, any>): string`
Replaces placeholders in a template string.

```typescript
template('Hello {{name}}!', { name: 'World' }); // 'Hello World!'
```

#### `uuid(): string`
Generates a UUID v4 string.

```typescript
uuid(); // 'f47ac10b-58cc-4372-a567-0e02b2c3d479'
```

### Logger

#### `createLogger(namespace?: string): Logger`
Creates a logger instance with optional namespace.

```typescript
const logger = createLogger('my-app');
logger.info('Application started');
logger.warn('Warning message');
logger.error('Error occurred', error);
```

#### Logger Methods

- `debug(message: string, ...args: any[]): void`
- `info(message: string, ...args: any[]): void`
- `warn(message: string, ...args: any[]): void`
- `error(message: string, ...args: any[]): void`
- `setLevel(level: LogLevel): void`
- `child(namespace: string): Logger`

### Event Bus

#### `new EventBus()`
Creates a new event bus instance.

```typescript
const eventBus = new EventBus();
```

#### Event Bus Methods

##### `on<T>(event: string, listener: EventListener<T>, options?: { once?: boolean; priority?: number }): () => void`
Adds an event listener.

```typescript
const unsubscribe = eventBus.on('user-login', (user) => {
  console.log('User logged in:', user);
});

// Remove listener
unsubscribe();
```

##### `once<T>(event: string, listener: EventListener<T>, priority?: number): () => void`
Adds a one-time event listener.

```typescript
eventBus.once('app-ready', () => {
  console.log('App is ready');
});
```

##### `emit<T>(event: string, data?: T): Promise<void>`
Emits an event asynchronously.

```typescript
await eventBus.emit('user-action', { type: 'click', target: 'button' });
```

##### `emitSync<T>(event: string, data?: T): void`
Emits an event synchronously.

```typescript
eventBus.emitSync('immediate-action', data);
```

## Helpers

### Error Handling

#### `formatErrorMessage(error: Error | string, context?: Record<string, any>): string`
Formats an error message with optional context.

```typescript
const formatted = formatErrorMessage(error, {
  userId: 123,
  action: 'login'
});
```

#### `safeExecute<T>(fn: () => T | Promise<T>, options?: SafeExecuteOptions<T>): Promise<T | undefined>`
Safely executes a function with error handling and retry logic.

```typescript
const result = await safeExecute(
  () => riskyOperation(),
  {
    fallback: 'default-value',
    timeout: 5000,
    retries: 3,
    onError: (error) => console.error('Failed:', error)
  }
);
```

### Performance Monitoring

#### `createPerformanceTimer(label?: string): PerformanceTimer`
Creates a performance timer for measuring execution time.

```typescript
const timer = createPerformanceTimer('operation');
timer.start();
// ... perform operation
const duration = timer.end(); // Returns duration in milliseconds
```

#### `getMemoryInfo(): MemoryInfo | null`
Gets current memory usage information.

```typescript
const memInfo = getMemoryInfo();
if (memInfo) {
  console.log(`Memory: ${memInfo.percentage}%`);
}
```

### Resource Loading

#### `loadResource(url: string, type: 'script' | 'style' | 'json' | 'text', options?: ResourceLoadOptions): Promise<any>`
Loads external resources with caching and retry support.

```typescript
await loadResource('https://cdn.example.com/lib.js', 'script', {
  timeout: 10000,
  retries: 3,
  cache: true
});
```

## Constants

### App Status
```typescript
enum AppStatus {
  NOT_LOADED = 'NOT_LOADED',
  LOADING_SOURCE_CODE = 'LOADING_SOURCE_CODE',
  NOT_BOOTSTRAPPED = 'NOT_BOOTSTRAPPED',
  BOOTSTRAPPING = 'BOOTSTRAPPING',
  NOT_MOUNTED = 'NOT_MOUNTED',
  MOUNTING = 'MOUNTING',
  MOUNTED = 'MOUNTED',
  UNMOUNTING = 'UNMOUNTING',
  UPDATING = 'UPDATING',
  LOAD_ERROR = 'LOAD_ERROR',
  SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN'
}
```

### Error Codes
```typescript
enum ErrorCodes {
  // System errors (1000-1999)
  SYSTEM_INIT_FAILED = 1000,
  SYSTEM_CONFIG_ERROR = 1001,
  
  // App errors (2000-2999)
  APP_LOAD_FAILED = 2000,
  APP_MOUNT_FAILED = 2002,
  
  // Network errors (3000-3999)
  NETWORK_TIMEOUT = 3001,
  CORS_ERROR = 3003,
  
  // Permission errors (4000-4999)
  PERMISSION_DENIED = 4000,
  
  // Validation errors (5000-5999)
  VALIDATION_FAILED = 5000
}
```

## Types

### Micro-App Configuration
```typescript
interface MicroAppConfig {
  name: string;
  entry: string | AppEntry;
  container?: string | HTMLElement;
  activeRule?: string | string[] | ((location: Location) => boolean);
  props?: Record<string, any>;
  loader?: LoaderConfig;
  sandbox?: SandboxConfig;
  styleIsolation?: StyleIsolationConfig;
  hooks?: AppLifecycleHooks;
  meta?: AppMetadata;
}
```

### App Lifecycle
```typescript
interface AppLifecycle {
  bootstrap?: (props?: Record<string, any>) => Promise<void>;
  mount?: (props?: Record<string, any>) => Promise<void>;
  unmount?: (props?: Record<string, any>) => Promise<void>;
  update?: (props?: Record<string, any>) => Promise<void>;
}
```

### Utility Types
```typescript
type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };
```

For complete type definitions, see the [types source code](../types/src/index.ts).
