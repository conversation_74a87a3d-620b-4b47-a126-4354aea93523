/**
 * @fileoverview 测试设置文件
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { afterEach, beforeEach, vi } from 'vitest';

// Mock DOM APIs only if window exists (jsdom environment)
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'location', {
    value: {
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000',
      protocol: 'http:',
      host: 'localhost:3000',
      hostname: 'localhost',
      port: '3000',
      pathname: '/',
      search: '',
      hash: ''
    },
    writable: true
  });

  Object.defineProperty(window, 'navigator', {
    value: {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      language: 'en-US',
      onLine: true
    },
    writable: true
  });
} else {
  // Create minimal window mock for node environment
  (global as any).window = {
    location: {
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000',
      protocol: 'http:',
      host: 'localhost:3000',
      hostname: 'localhost',
      port: '3000',
      pathname: '/',
      search: '',
      hash: ''
    },
    navigator: {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      language: 'en-US',
      onLine: true
    }
  };
}

// Mock performance API
const performanceMock = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByName: vi.fn(() => [{ duration: 100 }]),
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024,
    totalJSHeapSize: 100 * 1024 * 1024
  }
};

if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'performance', {
    value: performanceMock,
    writable: true
  });
} else {
  global.performance = performanceMock as any;
}

// Mock fetch API
global.fetch = vi.fn();

// Mock localStorage and sessionStorage
const createStorage = () => {
  const storage = new Map<string, string>();
  return {
    getItem: vi.fn((key: string) => storage.get(key) || null),
    setItem: vi.fn((key: string, value: string) => storage.set(key, value)),
    removeItem: vi.fn((key: string) => storage.delete(key)),
    clear: vi.fn(() => storage.clear()),
    get length() { return storage.size; },
    key: vi.fn((index: number) => Array.from(storage.keys())[index] || null)
  };
};

const localStorageMock = createStorage();
const sessionStorageMock = createStorage();

if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true
  });

  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
    writable: true
  });
} else {
  (global as any).localStorage = localStorageMock;
  (global as any).sessionStorage = sessionStorageMock;
}

// Mock console methods
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  info: vi.fn()
};

// Don't mock timers - use real ones

// Don't mock document - let jsdom handle it

// Mock URL constructor
global.URL = class MockURL {
  href: string;
  origin: string;
  protocol: string;
  host: string;
  hostname: string;
  port: string;
  pathname: string;
  search: string;
  hash: string;

  constructor(url: string, base?: string) {
    this.href = url;
    this.origin = 'http://localhost:3000';
    this.protocol = 'http:';
    this.host = 'localhost:3000';
    this.hostname = 'localhost';
    this.port = '3000';
    this.pathname = '/';
    this.search = '';
    this.hash = '';
  }

  toString() {
    return this.href;
  }
} as any;

// Mock crypto API
Object.defineProperty(window, 'crypto', {
  value: {
    getRandomValues: vi.fn((arr: Uint8Array) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
    subtle: {}
  },
  writable: true
});

// Mock MutationObserver
global.MutationObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  takeRecords: vi.fn(() => [])
})) as any;

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
})) as any;

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
})) as any;

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((callback) => {
  return setTimeout(callback, 16);
});

global.cancelAnimationFrame = vi.fn(clearTimeout);

// Mock customElements
Object.defineProperty(window, 'customElements', {
  value: {
    define: vi.fn(),
    get: vi.fn(),
    whenDefined: vi.fn(() => Promise.resolve())
  },
  writable: true
});

// Mock ShadowRoot
global.ShadowRoot = vi.fn() as any;

// Mock Proxy if not available
if (typeof Proxy === 'undefined') {
  global.Proxy = class MockProxy {
    constructor(target: any, handler: any) {
      return target;
    }
  } as any;
}

// Mock Promise if not available
if (typeof Promise === 'undefined') {
  global.Promise = class MockPromise {
    constructor(executor: any) {
      // Minimal Promise implementation for testing
    }

    static resolve(value?: any) {
      return new MockPromise(() => { });
    }

    static reject(reason?: any) {
      return new MockPromise(() => { });
    }

    then(onFulfilled?: any, onRejected?: any) {
      return new MockPromise(() => { });
    }

    catch(onRejected?: any) {
      return new MockPromise(() => { });
    }
  } as any;
}

// Setup test environment
beforeEach(() => {
  vi.clearAllMocks();
});

afterEach(() => {
  vi.restoreAllMocks();
});
