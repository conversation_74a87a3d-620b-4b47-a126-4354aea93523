# Rollup Builder

The Rollup Builder provides powerful bundling capabilities for micro-frontend applications with Micro-Core, offering fine-grained control over module federation and optimization.

## Installation

```bash
npm install @micro-core/builder-rollup
```

## Quick Start

### 1. Basic Configuration

```javascript
// rollup.config.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';

export default {
  input: 'src/main.ts',
  
  plugins: [
    resolve(),
    commonjs(),
    typescript(),
    
    microCorePlugin({
      name: 'user-app',
      
      // Expose modules
      exposes: {
        './UserProfile': './src/components/UserProfile',
        './UserList': './src/components/UserList',
        './userService': './src/services/userService'
      },
      
      // Shared dependencies
      shared: {
        'react': { singleton: true },
        'react-dom': { singleton: true },
        'lodash': { requiredVersion: '^4.17.0' }
      }
    })
  ],
  
  output: {
    dir: 'dist',
    format: 'es',
    entryFileNames: '[name]-[hash].js',
    chunkFileNames: '[name]-[hash].js'
  },
  
  external: ['react', 'react-dom']
};
```

### 2. Host Application Configuration

```javascript
// rollup.config.js for host app
import { microCorePlugin } from '@micro-core/builder-rollup';

export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      type: 'host',
      name: 'main-app',
      
      // Remote micro-apps
      remotes: {
        'user-app': 'http://localhost:3001/remoteEntry.js',
        'product-app': 'http://localhost:3002/remoteEntry.js',
        'order-app': 'http://localhost:3003/remoteEntry.js'
      },
      
      // Shared dependencies
      shared: {
        'react': { singleton: true, eager: true },
        'react-dom': { singleton: true, eager: true },
        '@micro-core/core': { singleton: true, eager: true }
      }
    })
  ],
  
  output: {
    dir: 'dist',
    format: 'es'
  }
};
```

### 3. Multi-Entry Configuration

```javascript
// rollup.config.js
export default [
  // Main application bundle
  {
    input: 'src/main.ts',
    plugins: [
      microCorePlugin({
        name: 'user-app',
        type: 'remote'
      })
    ],
    output: {
      file: 'dist/main.js',
      format: 'es'
    }
  },
  
  // Component library bundle
  {
    input: 'src/components/index.ts',
    plugins: [
      microCorePlugin({
        name: 'user-components',
        exposes: {
          './UserProfile': './src/components/UserProfile',
          './UserList': './src/components/UserList'
        }
      })
    ],
    output: {
      file: 'dist/components.js',
      format: 'es'
    }
  }
];
```

## Configuration Options

### MicroCoreRollupConfig

```typescript
interface MicroCoreRollupConfig {
  // Application type
  type?: 'host' | 'remote';
  
  // Application name
  name: string;
  
  // Exposed modules (for remote apps)
  exposes?: Record<string, string>;
  
  // Remote applications (for host apps)
  remotes?: Record<string, string>;
  
  // Shared dependencies
  shared?: SharedConfig;
  
  // Output configuration
  output?: {
    // Remote entry filename
    remoteEntry?: string;
    
    // Public path
    publicPath?: string;
    
    // Asset naming
    assetFileNames?: string;
    
    // Chunk naming
    chunkFileNames?: string;
  };
  
  // Development configuration
  dev?: {
    // Enable development mode
    enabled?: boolean;
    
    // Development server port
    port?: number;
    
    // Live reload
    liveReload?: boolean;
  };
  
  // Optimization options
  optimization?: {
    // Tree shaking
    treeshake?: boolean;
    
    // Code splitting
    codeSplitting?: boolean;
    
    // Minification
    minify?: boolean;
    
    // Source maps
    sourcemap?: boolean;
  };
}
```

## Advanced Configuration

### Custom Module Federation

```javascript
// rollup.config.js
import { microCorePlugin } from '@micro-core/builder-rollup';

export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'advanced-app',
      
      // Advanced module federation
      federation: {
        // Custom module resolution
        resolveModule: (moduleName) => {
          if (moduleName.startsWith('@shared/')) {
            return `https://shared-cdn.example.com/${moduleName}`;
          }
          return null;
        },
        
        // Module transformation
        transformModule: (code, moduleName) => {
          // Add custom transformations
          return code.replace('__MODULE_NAME__', moduleName);
        },
        
        // Dependency injection
        injectDependencies: {
          'logger': './src/utils/logger',
          'config': './src/config'
        }
      }
    })
  ]
};
```

### Dynamic Imports

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'dynamic-app',
      
      // Dynamic import configuration
      dynamicImports: {
        // Chunk naming for dynamic imports
        chunkFileNames: 'chunks/[name]-[hash].js',
        
        // Import map generation
        generateImportMap: true,
        
        // Preload strategy
        preload: {
          strategy: 'hover',
          delay: 200
        }
      }
    })
  ],
  
  output: {
    dir: 'dist',
    format: 'es',
    
    // Dynamic import configuration
    manualChunks: (id) => {
      if (id.includes('node_modules')) {
        return 'vendor';
      }
      if (id.includes('components')) {
        return 'components';
      }
    }
  }
};
```

### Environment-Specific Builds

```javascript
// rollup.config.js
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'user-app',
      
      // Environment-specific remotes
      remotes: isDevelopment ? {
        'product-app': 'http://localhost:3002/remoteEntry.js'
      } : {
        'product-app': 'https://product-app.example.com/remoteEntry.js'
      },
      
      // Development vs production shared config
      shared: {
        'react': {
          singleton: true,
          eager: isProduction
        },
        'react-dom': {
          singleton: true,
          eager: isProduction
        }
      }
    }),
    
    // Conditional plugins
    ...(isProduction ? [
      terser(), // Minification for production
      gzipPlugin() // Gzip compression
    ] : [
      livereload() // Live reload for development
    ])
  ],
  
  output: {
    dir: 'dist',
    format: 'es',
    sourcemap: isDevelopment
  }
};
```

## Build Optimization

### Tree Shaking

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'optimized-app',
      
      // Tree shaking configuration
      treeshake: {
        // Enable aggressive tree shaking
        moduleSideEffects: false,
        
        // Custom tree shaking rules
        pureExternalModules: ['lodash', 'date-fns'],
        
        // Preserve specific exports
        preserveEntrySignatures: 'strict'
      }
    })
  ],
  
  // External dependencies for tree shaking
  external: (id) => {
    return /^(react|react-dom|lodash)/.test(id);
  }
};
```

### Code Splitting

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'split-app',
      
      // Code splitting configuration
      codeSplitting: {
        // Automatic chunk splitting
        automatic: true,
        
        // Manual chunk configuration
        chunks: {
          'vendor': ['react', 'react-dom', 'lodash'],
          'utils': ['./src/utils/**'],
          'components': ['./src/components/**']
        },
        
        // Chunk size limits
        maxChunkSize: 500000, // 500KB
        minChunkSize: 50000   // 50KB
      }
    })
  ],
  
  output: {
    dir: 'dist',
    format: 'es',
    
    // Manual chunk splitting
    manualChunks: {
      'react-vendor': ['react', 'react-dom'],
      'utility-vendor': ['lodash', 'date-fns'],
      'ui-vendor': ['@mui/material', '@mui/icons-material']
    }
  }
};
```

### Asset Optimization

```javascript
// rollup.config.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import image from '@rollup/plugin-image';
import postcss from 'rollup-plugin-postcss';
import { terser } from 'rollup-plugin-terser';

export default {
  input: 'src/main.ts',
  
  plugins: [
    // CSS processing
    postcss({
      extract: true,
      minimize: true,
      sourceMap: true,
      plugins: [
        require('autoprefixer'),
        require('cssnano')
      ]
    }),
    
    // Image optimization
    image({
      // Convert images to base64 if smaller than limit
      limit: 8192,
      
      // Image formats to process
      include: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg']
    }),
    
    microCorePlugin({
      name: 'optimized-app',
      
      // Asset optimization
      assets: {
        // Image optimization
        images: {
          quality: 80,
          progressive: true,
          mozjpeg: true
        },
        
        // Font optimization
        fonts: {
          preload: ['woff2'],
          display: 'swap'
        }
      }
    }),
    
    // JavaScript minification
    terser({
      compress: {
        drop_console: true,
        drop_debugger: true
      },
      mangle: {
        reserved: ['__MICRO_CORE__']
      }
    })
  ]
};
```

## Development Workflow

### Development Server

```javascript
// rollup.config.dev.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import serve from 'rollup-plugin-serve';
import livereload from 'rollup-plugin-livereload';

export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'user-app',
      
      // Development configuration
      dev: {
        enabled: true,
        port: 3001,
        cors: true
      }
    }),
    
    // Development server
    serve({
      open: true,
      contentBase: ['dist', 'public'],
      host: 'localhost',
      port: 3001,
      
      // CORS headers for micro-frontend
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    }),
    
    // Live reload
    livereload({
      watch: 'dist',
      verbose: true
    })
  ],
  
  output: {
    dir: 'dist',
    format: 'es',
    sourcemap: true
  }
};
```

### Watch Mode

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'user-app',
      
      // Watch mode configuration
      watch: {
        // Files to watch
        include: ['src/**/*'],
        exclude: ['node_modules/**'],
        
        // Watch options
        chokidar: {
          usePolling: true,
          interval: 1000
        },
        
        // Build on change
        buildDelay: 100
      }
    })
  ],
  
  // Rollup watch options
  watch: {
    clearScreen: false,
    exclude: ['node_modules/**']
  }
};
```

### Hot Module Replacement

```javascript
// rollup.config.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import hmr from 'rollup-plugin-hot';

export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'user-app',
      
      // HMR configuration
      hmr: {
        enabled: true,
        port: 3001,
        
        // HMR boundaries
        boundaries: [
          './src/components/**',
          './src/pages/**'
        ]
      }
    }),
    
    // HMR plugin
    hmr({
      public: 'dist',
      inMemory: true,
      
      // Custom HMR logic
      transform: (code, id) => {
        if (id.includes('components/')) {
          return `
            ${code}
            
            if (module.hot) {
              module.hot.accept();
            }
          `;
        }
        return code;
      }
    })
  ]
};
```

## Framework Integration

### React Integration

```javascript
// rollup.config.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import babel from '@rollup/plugin-babel';

export default {
  input: 'src/main.tsx',
  
  plugins: [
    babel({
      babelHelpers: 'bundled',
      presets: [
        '@babel/preset-env',
        '@babel/preset-react',
        '@babel/preset-typescript'
      ],
      extensions: ['.js', '.jsx', '.ts', '.tsx']
    }),
    
    microCorePlugin({
      name: 'react-app',
      
      // React-specific configuration
      framework: 'react',
      
      exposes: {
        './App': './src/App',
        './components/UserProfile': './src/components/UserProfile'
      },
      
      shared: {
        'react': { singleton: true },
        'react-dom': { singleton: true }
      }
    })
  ],
  
  external: ['react', 'react-dom']
};
```

### Vue Integration

```javascript
// rollup.config.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import vue from 'rollup-plugin-vue';
import typescript from '@rollup/plugin-typescript';

export default {
  input: 'src/main.ts',
  
  plugins: [
    vue({
      css: true,
      compileTemplate: true
    }),
    
    typescript({
      tsconfig: './tsconfig.json'
    }),
    
    microCorePlugin({
      name: 'vue-app',
      
      // Vue-specific configuration
      framework: 'vue',
      
      exposes: {
        './App': './src/App.vue',
        './components/UserProfile': './src/components/UserProfile.vue'
      },
      
      shared: {
        'vue': { singleton: true }
      }
    })
  ],
  
  external: ['vue']
};
```

### Angular Integration

```javascript
// rollup.config.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import typescript from '@rollup/plugin-typescript';
import angular from 'rollup-plugin-angular';

export default {
  input: 'src/main.ts',
  
  plugins: [
    angular(),
    
    typescript({
      tsconfig: './tsconfig.json',
      target: 'es2015'
    }),
    
    microCorePlugin({
      name: 'angular-app',
      
      // Angular-specific configuration
      framework: 'angular',
      
      exposes: {
        './AppModule': './src/app/app.module',
        './UserModule': './src/app/user/user.module'
      },
      
      shared: {
        '@angular/core': { singleton: true },
        '@angular/common': { singleton: true },
        '@angular/platform-browser': { singleton: true }
      }
    })
  ],
  
  external: [
    '@angular/core',
    '@angular/common',
    '@angular/platform-browser'
  ]
};
```

## Testing

### Unit Testing

```javascript
// rollup.config.test.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';

export default {
  input: 'src/test/index.ts',
  
  plugins: [
    nodeResolve(),
    commonjs(),
    
    microCorePlugin({
      name: 'test-app',
      
      // Test-specific configuration
      test: true,
      
      // Mock remote modules
      mocks: {
        'user-app/UserProfile': './src/test/mocks/UserProfile',
        'product-app/ProductList': './src/test/mocks/ProductList'
      }
    })
  ],
  
  output: {
    dir: 'dist/test',
    format: 'cjs'
  }
};
```

### Integration Testing

```javascript
// rollup.config.integration.js
export default {
  input: 'tests/integration/index.ts',
  
  plugins: [
    microCorePlugin({
      name: 'integration-test',
      
      // Integration test configuration
      remotes: {
        'user-app': './dist/user-app/remoteEntry.js',
        'product-app': './dist/product-app/remoteEntry.js'
      },
      
      // Test environment
      env: 'test'
    })
  ],
  
  output: {
    dir: 'dist/integration',
    format: 'es'
  }
};
```

## Performance Optimization

### Bundle Analysis

```javascript
// rollup.config.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import { visualizer } from 'rollup-plugin-visualizer';
import bundleSize from 'rollup-plugin-bundle-size';

export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'analyzed-app'
    }),
    
    // Bundle visualization
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true
    }),
    
    // Bundle size analysis
    bundleSize()
  ]
};
```

### Lazy Loading

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'lazy-app',
      
      // Lazy loading configuration
      lazyLoading: {
        // Routes to lazy load
        routes: [
          './src/pages/UserPage',
          './src/pages/ProductPage'
        ],
        
        // Components to lazy load
        components: [
          './src/components/HeavyChart',
          './src/components/DataTable'
        ],
        
        // Preload strategy
        preload: 'hover'
      }
    })
  ],
  
  output: {
    dir: 'dist',
    format: 'es',
    
    // Dynamic import naming
    chunkFileNames: 'lazy/[name]-[hash].js'
  }
};
```

### Caching Strategy

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'cached-app',
      
      // Caching configuration
      cache: {
        // Long-term caching for vendor chunks
        vendor: {
          test: /node_modules/,
          name: 'vendor',
          chunks: 'all'
        },
        
        // Runtime caching
        runtime: {
          name: 'runtime',
          chunks: 'all'
        }
      }
    })
  ],
  
  output: {
    dir: 'dist',
    format: 'es',
    
    // File naming for caching
    entryFileNames: '[name]-[hash].js',
    chunkFileNames: '[name]-[hash].js',
    assetFileNames: 'assets/[name]-[hash][extname]'
  }
};
```

## Deployment

### Production Build

```javascript
// rollup.config.prod.js
import { microCorePlugin } from '@micro-core/builder-rollup';
import { terser } from 'rollup-plugin-terser';
import gzipPlugin from 'rollup-plugin-gzip';

export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'production-app',
      
      // Production configuration
      production: true,
      
      // CDN configuration
      cdn: {
        baseUrl: 'https://cdn.example.com',
        version: process.env.APP_VERSION
      }
    }),
    
    // Minification
    terser({
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log']
      }
    }),
    
    // Compression
    gzipPlugin()
  ],
  
  output: {
    dir: 'dist',
    format: 'es',
    sourcemap: false
  }
};
```

### Docker Build

```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### CI/CD Pipeline

```yaml
# .github/workflows/build.yml
name: Build and Deploy

on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
      env:
        NODE_ENV: production
        APP_VERSION: ${{ github.sha }}
    
    - name: Deploy to CDN
      run: |
        aws s3 sync dist/ s3://micro-apps-cdn/user-app/ --delete
        aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_ID }} --paths "/*"
```

## Best Practices

### 1. Module Organization

```javascript
// Good: Organized module structure
export default {
  input: {
    main: 'src/main.ts',
    components: 'src/components/index.ts',
    services: 'src/services/index.ts'
  },
  
  plugins: [
    microCorePlugin({
      name: 'well-organized-app',
      
      exposes: {
        // Main application
        './App': './src/main.ts',
        
        // Component library
        './components': './src/components/index.ts',
        './UserProfile': './src/components/UserProfile',
        './UserList': './src/components/UserList',
        
        // Services
        './services': './src/services/index.ts',
        './userService': './src/services/userService',
        './apiService': './src/services/apiService'
      }
    })
  ]
};
```

### 2. Error Handling

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'robust-app',
      
      // Error handling configuration
      errorHandling: {
        // Graceful degradation
        fallback: './src/components/ErrorBoundary',
        
        // Error reporting
        onError: (error, context) => {
          console.error('Build error:', error);
          
          // Send to error tracking
          if (process.env.NODE_ENV === 'production') {
            errorTracker.captureException(error, { context });
          }
        }
      }
    })
  ],
  
  // Rollup error handling
  onwarn: (warning, warn) => {
    // Skip certain warnings
    if (warning.code === 'THIS_IS_UNDEFINED') return;
    
    // Use default for everything else
    warn(warning);
  }
};
```

### 3. Security Considerations

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'secure-app',
      
      // Security configuration
      security: {
        // Content Security Policy
        csp: {
          'script-src': ["'self'", 'https://trusted-cdn.com'],
          'style-src': ["'self'", "'unsafe-inline'"],
          'img-src': ["'self'", 'data:', 'https:']
        },
        
        // Subresource Integrity
        sri: true,
        
        // CORS configuration
        cors: {
          origin: ['https://app.example.com'],
          credentials: true
        }
      }
    })
  ]
};
```

### 4. Performance Monitoring

```javascript
// rollup.config.js
export default {
  input: 'src/main.ts',
  
  plugins: [
    microCorePlugin({
      name: 'monitored-app',
      
      // Performance monitoring
      performance: {
        // Bundle size limits
        budgets: {
          maximumError: 500000, // 500KB
          maximumWarning: 400000 // 400KB
        },
        
        // Performance metrics
        metrics: {
          buildTime: true,
          bundleSize: true,
          chunkCount: true
        },
        
        // Reporting
        report: {
          console: true,
          file: 'dist/performance-report.json'
        }
      }
    })
  ]
};
```

## Troubleshooting

### Common Issues

1. **Module Resolution Issues**
   ```javascript
   // Debug module resolution
   export default {
     plugins: [
       microCorePlugin({
         name: 'debug-app',
         
         debug: {
           moduleResolution: true,
           federation: true
         }
       })
     ]
   };
   ```

2. **Shared Dependency Conflicts**
   ```javascript
   // Check shared dependencies
   export default {
     plugins: [
       microCorePlugin({
         name: 'conflict-app',
         
         shared: {
           'react': {
             singleton: true,
             requiredVersion: '18.2.0', // Exact version
             strictVersion: true
           }
         }
       })
     ]
   };
   ```

3. **Build Performance Issues**
   ```javascript
   // Optimize build performance
   export default {
     plugins: [
       microCorePlugin({
         name: 'fast-app',
         
         optimization: {
           // Parallel processing
           parallel: true,
           
           // Incremental builds
           incremental: true,
           
           // Cache configuration
           cache: {
             type: 'filesystem',
             cacheDirectory: 'node_modules/.cache/rollup'
           }
         }
       })
     ]
   };
   ```

---

This completes the Rollup Builder documentation. For more information, see:

- **[Core API](/en/api/core)** - Main Micro-Core functionality
- **[Vite Builder](/en/ecosystem/builders/vite)** - Vite integration
- **[React Adapter](/en/ecosystem/adapters/react)** - React integration
- **[Vue Adapter](/en/ecosystem/adapters/vue)** - Vue integration