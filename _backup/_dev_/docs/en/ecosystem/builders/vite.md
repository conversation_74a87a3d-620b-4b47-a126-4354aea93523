# Vite Builder

The Vite Builder provides seamless integration between Vite and Micro-Core, enabling fast development and optimized builds for micro-frontend applications.

## Installation

```bash
npm install @micro-core/builder-vite
```

## Quick Start

### 1. Basic Configuration

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { microCorePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    microCorePlugin({
      // Micro-frontend configuration
      name: 'user-app',
      entry: './src/main.ts',
      
      // Expose components/modules
      exposes: {
        './UserProfile': './src/components/UserProfile',
        './UserList': './src/components/UserList'
      },
      
      // Shared dependencies
      shared: {
        'react': { singleton: true },
        'react-dom': { singleton: true },
        'lodash': { requiredVersion: '^4.17.0' }
      }
    })
  ],
  
  build: {
    // Optimize for micro-frontend
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        format: 'es'
      }
    }
  }
});
```

### 2. Main Application Setup

```typescript
// vite.config.ts for main app
import { defineConfig } from 'vite';
import { microCorePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    microCorePlugin({
      // Main application configuration
      type: 'host',
      name: 'main-app',
      
      // Remote micro-apps
      remotes: {
        'user-app': 'http://localhost:3001/assets/remoteEntry.js',
        'product-app': 'http://localhost:3002/assets/remoteEntry.js'
      },
      
      // Shared dependencies
      shared: {
        'react': { singleton: true, eager: true },
        'react-dom': { singleton: true, eager: true }
      }
    })
  ]
});
```

### 3. Micro-App Entry Point

```typescript
// src/main.ts
import { createApp } from './app';
import { createMicroAppAdapter } from '@micro-core/builder-vite/runtime';

// Create micro-app adapter
const adapter = createMicroAppAdapter({
  name: 'user-app',
  createApp,
  
  // Lifecycle hooks
  async bootstrap(props) {
    console.log('Bootstrapping user-app', props);
    // Initialize app dependencies
    await initializeServices();
  },
  
  async mount(props) {
    console.log('Mounting user-app', props);
    const app = createApp(props);
    app.mount(props.container);
    return app;
  },
  
  async unmount(props) {
    console.log('Unmounting user-app', props);
    if (props.app) {
      props.app.unmount();
    }
  }
});

// Export lifecycle functions
export const { bootstrap, mount, unmount } = adapter;

// Standalone mode support
if (!window.__POWERED_BY_MICRO_CORE__) {
  const app = createApp();
  app.mount('#app');
}
```

## Configuration Options

### MicroCoreViteConfig

```typescript
interface MicroCoreViteConfig {
  // Application type
  type?: 'host' | 'remote';
  
  // Application name
  name: string;
  
  // Entry point
  entry?: string;
  
  // Exposed modules (for remote apps)
  exposes?: Record<string, string>;
  
  // Remote applications (for host apps)
  remotes?: Record<string, string>;
  
  // Shared dependencies
  shared?: SharedConfig;
  
  // Build configuration
  build?: {
    // Output directory
    outDir?: string;
    
    // Asset directory
    assetsDir?: string;
    
    // Manifest generation
    manifest?: boolean;
    
    // Source maps
    sourcemap?: boolean;
    
    // Minification
    minify?: boolean;
  };
  
  // Development configuration
  dev?: {
    // Development server port
    port?: number;
    
    // Hot module replacement
    hmr?: boolean;
    
    // CORS configuration
    cors?: boolean;
    
    // Proxy configuration
    proxy?: Record<string, string>;
  };
  
  // Runtime configuration
  runtime?: {
    // Global variable name
    globalName?: string;
    
    // Module format
    format?: 'es' | 'umd' | 'iife';
    
    // External dependencies
    externals?: string[];
  };
}
```

### Shared Dependencies Configuration

```typescript
interface SharedConfig {
  [packageName: string]: {
    // Package version
    requiredVersion?: string;
    
    // Singleton mode
    singleton?: boolean;
    
    // Eager loading
    eager?: boolean;
    
    // Import strategy
    import?: string | false;
    
    // Package name override
    packageName?: string;
    
    // Share scope
    shareScope?: string;
  };
}

// Example shared configuration
const shared: SharedConfig = {
  'react': {
    singleton: true,
    eager: true,
    requiredVersion: '^18.0.0'
  },
  'react-dom': {
    singleton: true,
    eager: true,
    requiredVersion: '^18.0.0'
  },
  'lodash': {
    singleton: false,
    requiredVersion: '^4.17.0',
    import: 'lodash/es'
  },
  '@micro-core/core': {
    singleton: true,
    eager: true
  }
};
```

## Advanced Configuration

### Multi-Framework Support

```typescript
// vite.config.ts for React + Vue micro-app
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import vue from '@vitejs/plugin-vue';
import { microCorePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    react(),
    vue(),
    microCorePlugin({
      name: 'multi-framework-app',
      
      exposes: {
        './ReactComponent': './src/react/Component.tsx',
        './VueComponent': './src/vue/Component.vue'
      },
      
      shared: {
        'react': { singleton: true },
        'react-dom': { singleton: true },
        'vue': { singleton: true }
      }
    })
  ]
});
```

### Dynamic Imports

```typescript
// Dynamic component loading
const LazyUserProfile = lazy(() => 
  import('user-app/UserProfile').then(module => ({
    default: module.UserProfile
  }))
);

// Dynamic micro-app loading
const loadMicroApp = async (appName: string) => {
  const { bootstrap, mount, unmount } = await import(
    /* webpackChunkName: "[request]" */
    `${appName}/lifecycle`
  );
  
  return { bootstrap, mount, unmount };
};
```

### Environment-Specific Configuration

```typescript
// vite.config.ts
import { defineConfig, loadEnv } from 'vite';
import { microCorePlugin } from '@micro-core/builder-vite';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
    plugins: [
      microCorePlugin({
        name: 'user-app',
        
        // Environment-specific remotes
        remotes: {
          'product-app': env.VITE_PRODUCT_APP_URL || 'http://localhost:3002/assets/remoteEntry.js'
        },
        
        // Development vs production shared config
        shared: {
          'react': {
            singleton: true,
            eager: mode === 'production'
          }
        }
      })
    ],
    
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_MODE__: JSON.stringify(mode)
    }
  };
});
```

## Development Workflow

### Development Server

```bash
# Start development server
npm run dev

# Start with specific port
npm run dev -- --port 3001

# Start with host binding
npm run dev -- --host 0.0.0.0
```

### Hot Module Replacement

```typescript
// Enable HMR for micro-app
if (import.meta.hot) {
  import.meta.hot.accept('./app', (newModule) => {
    if (newModule) {
      // Re-mount the app with new module
      unmount();
      mount({ container: '#app' });
    }
  });
  
  // Handle component updates
  import.meta.hot.accept('./components/UserProfile', (newModule) => {
    // Update specific component
    updateComponent('UserProfile', newModule.default);
  });
}
```

### Development Proxy

```typescript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      // Proxy API requests
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      
      // Proxy micro-app requests
      '/micro-apps': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/micro-apps/, '')
      }
    }
  }
});
```

## Build Optimization

### Code Splitting

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        // Manual chunk splitting
        manualChunks: {
          'vendor': ['react', 'react-dom'],
          'utils': ['lodash', 'date-fns'],
          'ui': ['@mui/material', '@mui/icons-material']
        },
        
        // Dynamic chunk naming
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId 
            ? chunkInfo.facadeModuleId.split('/').pop().replace('.js', '') 
            : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        }
      }
    }
  }
});
```

### Asset Optimization

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    // Asset inlining threshold
    assetsInlineLimit: 4096,
    
    // CSS code splitting
    cssCodeSplit: true,
    
    // Rollup options
    rollupOptions: {
      output: {
        // Asset file naming
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`;
          }
          
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`;
          }
          
          return `assets/[name]-[hash][extname]`;
        }
      }
    }
  }
});
```

### Bundle Analysis

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    // Bundle analyzer
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true
    })
  ]
});
```

## Framework Integration

### React Integration

```typescript
// src/react-app.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { createMicroAppAdapter } from '@micro-core/builder-vite/runtime';
import App from './App';

const adapter = createMicroAppAdapter({
  name: 'react-app',
  
  createApp: (props) => {
    return {
      mount: (container) => {
        const root = ReactDOM.createRoot(container);
        root.render(<App {...props} />);
        return { root };
      },
      
      unmount: ({ root }) => {
        root.unmount();
      }
    };
  }
});

export const { bootstrap, mount, unmount } = adapter;
```

### Vue Integration

```typescript
// src/vue-app.ts
import { createApp } from 'vue';
import { createMicroAppAdapter } from '@micro-core/builder-vite/runtime';
import App from './App.vue';

const adapter = createMicroAppAdapter({
  name: 'vue-app',
  
  createApp: (props) => {
    const app = createApp(App, props);
    
    return {
      mount: (container) => {
        app.mount(container);
        return { app };
      },
      
      unmount: ({ app }) => {
        app.unmount();
      }
    };
  }
});

export const { bootstrap, mount, unmount } = adapter;
```

### Angular Integration

```typescript
// src/main.ts
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { createMicroAppAdapter } from '@micro-core/builder-vite/runtime';
import { AppModule } from './app/app.module';

const adapter = createMicroAppAdapter({
  name: 'angular-app',
  
  createApp: (props) => {
    return {
      mount: async (container) => {
        const platformRef = platformBrowserDynamic();
        const moduleRef = await platformRef.bootstrapModule(AppModule);
        
        return { moduleRef };
      },
      
      unmount: ({ moduleRef }) => {
        moduleRef.destroy();
      }
    };
  }
});

export const { bootstrap, mount, unmount } = adapter;
```

## Testing

### Unit Testing

```typescript
// vite.config.test.ts
import { defineConfig } from 'vite';
import { microCorePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    microCorePlugin({
      name: 'test-app',
      // Test-specific configuration
      test: true
    })
  ],
  
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    
    // Mock remote modules
    deps: {
      external: ['user-app/*', 'product-app/*']
    }
  }
});

// src/test/setup.ts
import { vi } from 'vitest';

// Mock remote modules
vi.mock('user-app/UserProfile', () => ({
  UserProfile: () => '<div>Mocked UserProfile</div>'
}));

vi.mock('product-app/ProductList', () => ({
  ProductList: () => '<div>Mocked ProductList</div>'
}));
```

### Integration Testing

```typescript
// tests/integration.test.ts
import { describe, it, expect } from 'vitest';
import { createTestApp } from '@micro-core/builder-vite/testing';

describe('Micro-App Integration', () => {
  it('should load and mount micro-app', async () => {
    const testApp = await createTestApp({
      name: 'user-app',
      remotes: {
        'user-app': './dist/remoteEntry.js'
      }
    });
    
    const container = document.createElement('div');
    await testApp.mount(container);
    
    expect(container.innerHTML).toContain('User App');
    
    await testApp.unmount();
  });
});
```

## Performance Optimization

### Lazy Loading

```typescript
// Lazy load micro-apps
const LazyMicroApp = lazy(() => 
  import('./micro-apps/UserApp').then(module => ({
    default: module.UserApp
  }))
);

// Preload critical micro-apps
const preloadMicroApp = (appName: string) => {
  const link = document.createElement('link');
  link.rel = 'modulepreload';
  link.href = `/micro-apps/${appName}/remoteEntry.js`;
  document.head.appendChild(link);
};

// Preload on hover
const handleMouseEnter = (appName: string) => {
  preloadMicroApp(appName);
};
```

### Caching Strategy

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        // Long-term caching for vendor chunks
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['lodash', 'date-fns']
        }
      }
    }
  },
  
  // Service worker for caching
  plugins: [
    {
      name: 'service-worker',
      generateBundle() {
        this.emitFile({
          type: 'asset',
          fileName: 'sw.js',
          source: generateServiceWorker()
        });
      }
    }
  ]
});
```

### Resource Hints

```typescript
// Generate resource hints
const generateResourceHints = (remotes: Record<string, string>) => {
  return Object.entries(remotes).map(([name, url]) => ({
    rel: 'dns-prefetch',
    href: new URL(url).origin
  }));
};

// Add to HTML
const resourceHints = generateResourceHints({
  'user-app': 'https://user-app.example.com/remoteEntry.js',
  'product-app': 'https://product-app.example.com/remoteEntry.js'
});
```

## Deployment

### Static Deployment

```bash
# Build for production
npm run build

# Deploy to CDN
aws s3 sync dist/ s3://my-micro-app-bucket --delete

# Update remote entry URLs
sed -i 's|http://localhost:3001|https://cdn.example.com/user-app|g' dist/remoteEntry.js
```

### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Kubernetes Deployment

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-app
  template:
    metadata:
      labels:
        app: user-app
    spec:
      containers:
      - name: user-app
        image: user-app:latest
        ports:
        - containerPort: 80
        env:
        - name: REMOTE_ENTRY_URL
          value: "https://cdn.example.com/user-app/remoteEntry.js"
---
apiVersion: v1
kind: Service
metadata:
  name: user-app-service
spec:
  selector:
    app: user-app
  ports:
  - port: 80
    targetPort: 80
  type: LoadBalancer
```

## Best Practices

### 1. Dependency Management

```typescript
// Use exact versions for shared dependencies
const shared = {
  'react': {
    singleton: true,
    requiredVersion: '18.2.0', // Exact version
    eager: true
  },
  
  // Avoid sharing large libraries
  'moment': false, // Don't share, let each app bundle its own
  
  // Share core utilities
  '@micro-core/core': {
    singleton: true,
    eager: true
  }
};
```

### 2. Error Boundaries

```typescript
// Error boundary for micro-apps
const MicroAppErrorBoundary = ({ children, appName }) => {
  const [hasError, setHasError] = useState(false);
  
  useEffect(() => {
    const handleError = (error) => {
      console.error(`Error in ${appName}:`, error);
      setHasError(true);
    };
    
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, [appName]);
  
  if (hasError) {
    return (
      <div className="micro-app-error">
        <h3>Something went wrong in {appName}</h3>
        <button onClick={() => setHasError(false)}>
          Retry
        </button>
      </div>
    );
  }
  
  return children;
};
```

### 3. Performance Monitoring

```typescript
// Performance monitoring
const monitorMicroApp = (appName: string) => {
  const startTime = performance.now();
  
  return {
    onMount: () => {
      const mountTime = performance.now() - startTime;
      console.log(`${appName} mounted in ${mountTime}ms`);
      
      // Send to analytics
      analytics.track('micro_app_mounted', {
        app: appName,
        mountTime
      });
    },
    
    onError: (error: Error) => {
      console.error(`${appName} error:`, error);
      
      // Send to error tracking
      errorTracker.captureException(error, {
        tags: { microApp: appName }
      });
    }
  };
};
```

### 4. Security Considerations

```typescript
// Content Security Policy
const cspConfig = {
  'script-src': [
    "'self'",
    'https://trusted-cdn.example.com',
    'https://user-app.example.com',
    'https://product-app.example.com'
  ],
  'connect-src': [
    "'self'",
    'https://api.example.com'
  ]
};

// Validate remote entry URLs
const validateRemoteUrl = (url: string) => {
  const allowedOrigins = [
    'https://user-app.example.com',
    'https://product-app.example.com'
  ];
  
  const urlOrigin = new URL(url).origin;
  return allowedOrigins.includes(urlOrigin);
};
```

## Troubleshooting

### Common Issues

1. **Module Not Found**
   ```typescript
   // Check remote entry URL
   console.log('Remote entries:', window.__MICRO_CORE_REMOTES__);
   
   // Verify module exposure
   const remoteApp = await import('user-app/UserProfile');
   console.log('Remote module:', remoteApp);
   ```

2. **Shared Dependency Conflicts**
   ```typescript
   // Debug shared dependencies
   console.log('Shared modules:', window.__webpack_share_scopes__);
   
   // Check version compatibility
   const checkSharedVersion = (packageName: string) => {
     const shared = window.__webpack_share_scopes__.default[packageName];
     console.log(`${packageName} versions:`, Object.keys(shared));
   };
   ```

3. **Build Issues**
   ```bash
   # Clear cache and rebuild
   rm -rf node_modules/.vite
   npm run build
   
   # Debug build
   npm run build -- --debug
   
   # Analyze bundle
   npm run build -- --analyze
   ```

---

This completes the Vite Builder documentation. For more information, see:

- **[Core API](/en/api/core)** - Main Micro-Core functionality
- **[Rollup Builder](/en/ecosystem/builders/rollup)** - Rollup integration
- **[React Adapter](/en/ecosystem/adapters/react)** - React integration
- **[Vue Adapter](/en/ecosystem/adapters/vue)** - Vue integration