# Vue Adapter

The Vue Adapter provides seamless integration between Vue applications and Micro-Core, supporting both Vue 2 and Vue 3 with Composition API.

## Installation

```bash
npm install @micro-core/adapter-vue
```

## Quick Start

### 1. Main Application Setup

```typescript
import { MicroCore } from '@micro-core/core';
import { VueAdapter } from '@micro-core/adapter-vue';

// Create Vue adapter
const vueAdapter = new VueAdapter({
  version: '3',
  devtools: true
});

// Create Micro-Core instance
const microCore = new MicroCore({
  container: '#app',
  adapters: [vueAdapter]
});

// Register Vue micro-app
microCore.registerApp({
  name: 'product-app',
  entry: 'http://localhost:3002',
  container: '#product-container',
  activeRule: '/products'
});

// Start the system
microCore.start();
```

### 2. Vue 3 Micro-App Setup

```typescript
// src/main.ts
import { createApp } from 'vue';
import { createVueAdapter } from '@micro-core/adapter-vue';
import App from './App.vue';

// Create adapter for this micro-app
const adapter = createVueAdapter({
  version: '3'
});

// Export lifecycle functions
export const { bootstrap, mount, unmount, update } = adapter({
  rootComponent: App,
  container: '#app'
});

// Standalone mode support
if (!window.__POWERED_BY_MICRO_CORE__) {
  createApp(App).mount('#app');
}
```

### 3. Vue 2 Micro-App Setup

```typescript
// src/main.js
import Vue from 'vue';
import { createVueAdapter } from '@micro-core/adapter-vue';
import App from './App.vue';

// Create adapter for Vue 2
const adapter = createVueAdapter({
  version: '2'
});

// Export lifecycle functions
export const { bootstrap, mount, unmount, update } = adapter({
  rootComponent: App,
  container: '#app'
});

// Standalone mode support
if (!window.__POWERED_BY_MICRO_CORE__) {
  new Vue({
    render: h => h(App)
  }).$mount('#app');
}
```

### 4. Vue Component with Micro-Core Integration

```vue
<!-- App.vue -->
<template>
  <div class="vue-micro-app">
    <h1>Vue Micro-App</h1>
    <UserProfile :user="user" @update="handleUserUpdate" />
    <ProductList :products="products" />
    <NotificationBanner :notifications="notifications" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useMicroCore, useEventBus, useGlobalState } from '@micro-core/adapter-vue';

// Micro-Core integration
const { microCore } = useMicroCore();
const eventBus = useEventBus();
const [user, setUser] = useGlobalState('user');
const [products, setProducts] = useGlobalState('products', []);

// Local state
const notifications = ref([]);

// Event handling
let unsubscribeNotifications: (() => void) | null = null;

onMounted(() => {
  // Listen for notifications from other micro-apps
  unsubscribeNotifications = eventBus.on('notification:new', (notification) => {
    notifications.value.push(notification);
  });
  
  // Load initial data
  loadProducts();
});

onUnmounted(() => {
  // Cleanup event listeners
  if (unsubscribeNotifications) {
    unsubscribeNotifications();
  }
});

// Methods
const handleUserUpdate = (userData: any) => {
  // Update global state
  setUser(userData);
  
  // Emit event to other micro-apps
  eventBus.emit('user:updated', userData);
};

const loadProducts = async () => {
  try {
    const response = await fetch('/api/products');
    const productData = await response.json();
    setProducts(productData);
  } catch (error) {
    console.error('Failed to load products:', error);
  }
};
</script>

<style scoped>
.vue-micro-app {
  padding: 20px;
  border: 2px solid #4fc08d;
  border-radius: 8px;
}
</style>
```

## Configuration

### VueAdapterConfig

```typescript
interface VueAdapterConfig {
  // Vue version
  version?: '2' | '3';
  
  // Global properties (Vue 3) or prototype properties (Vue 2)
  globalProperties?: Record<string, any>;
  
  // Plugins to install
  plugins?: any[];
  
  // Error handler
  errorHandler?: (error: Error, instance: any, info: string) => void;
  
  // Development tools
  devtools?: boolean;
  
  // Custom app creation (Vue 3 only)
  createApp?: (component: any) => any;
}
```

### Example Configuration

```typescript
const vueAdapter = new VueAdapter({
  version: '3',
  
  globalProperties: {
    $http: axios,
    $utils: utils
  },
  
  plugins: [
    router,
    store,
    i18n
  ],
  
  errorHandler: (error, instance, info) => {
    console.error('Vue Error:', error, info);
    // Send to error tracking service
    errorTracker.captureException(error, {
      framework: 'vue',
      instance: instance?.$options.name,
      info
    });
  },
  
  devtools: process.env.NODE_ENV === 'development'
});
```

## Composition API

### useMicroCore()

Access the Micro-Core context.

```vue
<script setup>
import { useMicroCore } from '@micro-core/adapter-vue';

const { microCore, eventBus, globalState, router } = useMicroCore();

console.log('Micro-Core Version:', microCore.version);
</script>
```

### useEventBus()

Access the event bus for inter-app communication.

```vue
<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useEventBus } from '@micro-core/adapter-vue';

const eventBus = useEventBus();
const messages = ref([]);

let unsubscribe = null;

onMounted(() => {
  unsubscribe = eventBus.on('message:received', (message) => {
    messages.value.push(message);
  });
});

onUnmounted(() => {
  if (unsubscribe) {
    unsubscribe();
  }
});

const sendMessage = () => {
  eventBus.emit('message:send', {
    text: 'Hello from Vue!',
    timestamp: Date.now()
  });
};
</script>

<template>
  <div>
    <button @click="sendMessage">Send Message</button>
    <div v-for="message in messages" :key="message.timestamp">
      {{ message.text }}
    </div>
  </div>
</template>
```

### useGlobalState()

Manage global state across micro-apps.

```vue
<script setup>
import { useGlobalState } from '@micro-core/adapter-vue';

const [user, setUser] = useGlobalState('user', null);
const [theme, setTheme] = useGlobalState('theme', 'light');

const updateUser = (userData) => {
  setUser(userData);
};

const toggleTheme = () => {
  setTheme(theme.value === 'light' ? 'dark' : 'light');
};
</script>

<template>
  <div :class="`user-component theme-${theme}`">
    <h2>Welcome, {{ user?.name || 'Guest' }}</h2>
    <button @click="toggleTheme">
      Switch to {{ theme === 'light' ? 'Dark' : 'Light' }} Theme
    </button>
  </div>
</template>
```

### useRouter()

Access routing functionality.

```vue
<script setup>
import { useRouter } from '@micro-core/adapter-vue';

const router = useRouter();

const navigateToProduct = (productId) => {
  router.push(`/products/${productId}`);
};

const goBack = () => {
  router.back();
};
</script>

<template>
  <nav>
    <button @click="navigateToProduct(123)">
      View Product Details
    </button>
    <button @click="goBack">
      Go Back
    </button>
  </nav>
</template>
```

## Options API (Vue 2 Style)

### Component Integration

```vue
<template>
  <div class="vue2-component">
    <h2>{{ title }}</h2>
    <p>User: {{ user?.name }}</p>
    <button @click="sendNotification">Send Notification</button>
  </div>
</template>

<script>
export default {
  name: 'Vue2Component',
  
  data() {
    return {
      title: 'Vue 2 Component',
      notifications: []
    };
  },
  
  computed: {
    user() {
      return this.$globalState.get('user');
    }
  },
  
  mounted() {
    // Listen for events
    this.unsubscribe = this.$eventBus.on('notification:received', this.handleNotification);
  },
  
  beforeDestroy() {
    // Cleanup
    if (this.unsubscribe) {
      this.unsubscribe();
    }
  },
  
  methods: {
    sendNotification() {
      this.$eventBus.emit('notification:send', {
        message: 'Hello from Vue 2!',
        timestamp: Date.now()
      });
    },
    
    handleNotification(notification) {
      this.notifications.push(notification);
    }
  }
};
</script>
```

### Mixins

```javascript
// microCoreMixin.js
export const microCoreMixin = {
  computed: {
    $microCore() {
      return this.$root.$microCore;
    },
    
    $eventBus() {
      return this.$root.$eventBus;
    },
    
    $globalState() {
      return this.$root.$globalState;
    }
  },
  
  methods: {
    emitEvent(event, data) {
      this.$eventBus.emit(event, data);
    },
    
    listenToEvent(event, handler) {
      return this.$eventBus.on(event, handler);
    },
    
    getGlobalState(key, defaultValue) {
      return this.$globalState.get(key) || defaultValue;
    },
    
    setGlobalState(key, value) {
      this.$globalState.set(key, value);
    }
  }
};

// Usage in component
export default {
  mixins: [microCoreMixin],
  
  mounted() {
    const user = this.getGlobalState('user');
    console.log('Current user:', user);
  }
};
```

## State Management Integration

### Vuex Integration

```typescript
// store/index.ts
import { createStore } from 'vuex';
import { useGlobalState } from '@micro-core/adapter-vue';

const store = createStore({
  state: {
    localData: {}
  },
  
  mutations: {
    SET_LOCAL_DATA(state, data) {
      state.localData = data;
    }
  },
  
  actions: {
    async syncWithGlobalState({ commit }) {
      const [globalData, setGlobalData] = useGlobalState('shared-data');
      
      // Sync local state with global state
      if (globalData) {
        commit('SET_LOCAL_DATA', globalData);
      }
      
      // Watch for global state changes
      return globalData.subscribe((newData) => {
        commit('SET_LOCAL_DATA', newData);
      });
    }
  }
});

export default store;
```

### Pinia Integration

```typescript
// stores/user.ts
import { defineStore } from 'pinia';
import { useGlobalState } from '@micro-core/adapter-vue';

export const useUserStore = defineStore('user', () => {
  const [globalUser, setGlobalUser] = useGlobalState('user');
  
  // Local reactive state
  const localPreferences = ref({});
  
  // Computed
  const displayName = computed(() => {
    return globalUser.value?.name || 'Guest';
  });
  
  // Actions
  const updateUser = (userData) => {
    setGlobalUser(userData);
  };
  
  const updatePreferences = (prefs) => {
    localPreferences.value = { ...localPreferences.value, ...prefs };
  };
  
  return {
    user: globalUser,
    localPreferences,
    displayName,
    updateUser,
    updatePreferences
  };
});
```

## Router Integration

### Vue Router Integration

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router';
import { useRouter as useMicroRouter } from '@micro-core/adapter-vue';

const routes = [
  { path: '/products', component: () => import('../views/ProductList.vue') },
  { path: '/products/:id', component: () => import('../views/ProductDetail.vue') }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// Sync with Micro-Core router
router.beforeEach((to, from, next) => {
  const microRouter = useMicroRouter();
  
  // Notify Micro-Core about route change
  microRouter.emit('route:change', {
    to: to.path,
    from: from.path
  });
  
  next();
});

export default router;
```

### Route Guards

```vue
<script setup>
import { onMounted } from 'vue';
import { useRouter, useGlobalState } from '@micro-core/adapter-vue';

const router = useRouter();
const [user] = useGlobalState('user');

// Route guard
onMounted(() => {
  router.beforeEach((to, from, next) => {
    if (to.meta?.requiresAuth && !user.value) {
      next('/login');
    } else {
      next();
    }
  });
});
</script>
```

## Advanced Features

### Teleport Integration (Vue 3)

```vue
<template>
  <div>
    <h2>Main Content</h2>
    
    <!-- Teleport to global modal container -->
    <Teleport to="#global-modal" v-if="showModal">
      <div class="modal">
        <h3>Modal from Vue App</h3>
        <button @click="closeModal">Close</button>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useEventBus } from '@micro-core/adapter-vue';

const eventBus = useEventBus();
const showModal = ref(false);

const openModal = () => {
  showModal.value = true;
  eventBus.emit('modal:opened', { source: 'vue-app' });
};

const closeModal = () => {
  showModal.value = false;
  eventBus.emit('modal:closed', { source: 'vue-app' });
};
</script>
```

### Provide/Inject Pattern

```vue
<!-- Parent Component -->
<template>
  <div>
    <ChildComponent />
  </div>
</template>

<script setup>
import { provide } from 'vue';
import { useMicroCore } from '@micro-core/adapter-vue';

const { eventBus, globalState } = useMicroCore();

// Provide Micro-Core services to child components
provide('eventBus', eventBus);
provide('globalState', globalState);
</script>

<!-- Child Component -->
<script setup>
import { inject } from 'vue';

const eventBus = inject('eventBus');
const globalState = inject('globalState');

const sendMessage = () => {
  eventBus.emit('child:message', 'Hello from child!');
};
</script>
```

### Custom Directives

```typescript
// directives/microCore.ts
import { useEventBus } from '@micro-core/adapter-vue';

export const vTrackClick = {
  mounted(el: HTMLElement, binding: any) {
    const eventBus = useEventBus();
    
    el.addEventListener('click', () => {
      eventBus.emit('element:clicked', {
        element: binding.arg,
        value: binding.value,
        timestamp: Date.now()
      });
    });
  }
};

// Usage in component
<template>
  <button v-track-click:button="'primary-cta'">
    Click Me
  </button>
</template>
```

## Performance Optimization

### Lazy Loading

```vue
<script setup>
import { defineAsyncComponent, ref } from 'vue';
import { useRouter } from '@micro-core/adapter-vue';

const router = useRouter();
const currentComponent = ref(null);

// Lazy load components based on route
const LazyProductList = defineAsyncComponent(() => import('./ProductList.vue'));
const LazyProductDetail = defineAsyncComponent(() => import('./ProductDetail.vue'));

const loadComponent = (route) => {
  switch (route.path) {
    case '/products':
      currentComponent.value = LazyProductList;
      break;
    case '/products/:id':
      currentComponent.value = LazyProductDetail;
      break;
    default:
      currentComponent.value = null;
  }
};

// Watch route changes
router.onRouteChange(loadComponent);
</script>

<template>
  <div>
    <Suspense>
      <template #default>
        <component :is="currentComponent" v-if="currentComponent" />
      </template>
      <template #fallback>
        <div>Loading...</div>
      </template>
    </Suspense>
  </div>
</template>
```

### Memoization

```vue
<script setup>
import { computed, shallowRef } from 'vue';
import { useGlobalState } from '@micro-core/adapter-vue';

const [products] = useGlobalState('products', []);
const [filters] = useGlobalState('filters', {});

// Memoized computed property
const filteredProducts = computed(() => {
  return products.value.filter(product => {
    return Object.entries(filters.value).every(([key, value]) => {
      return !value || product[key] === value;
    });
  });
});

// Shallow ref for performance
const expensiveData = shallowRef({});
</script>
```

## Testing

### Unit Testing

```typescript
// ProductList.spec.ts
import { mount } from '@vue/test-utils';
import { createMicroCoreTestProvider } from '@micro-core/adapter-vue/testing';
import ProductList from './ProductList.vue';

describe('ProductList', () => {
  const mockMicroCore = {
    eventBus: {
      emit: vi.fn(),
      on: vi.fn(() => vi.fn())
    },
    globalState: {
      get: vi.fn(),
      set: vi.fn(),
      subscribe: vi.fn(() => vi.fn())
    }
  };
  
  it('should render product list', () => {
    const wrapper = mount(ProductList, {
      global: {
        plugins: [createMicroCoreTestProvider(mockMicroCore)]
      }
    });
    
    expect(wrapper.find('.product-list')).toBeTruthy();
  });
  
  it('should emit event on product click', async () => {
    const wrapper = mount(ProductList, {
      global: {
        plugins: [createMicroCoreTestProvider(mockMicroCore)]
      }
    });
    
    await wrapper.find('.product-item').trigger('click');
    
    expect(mockMicroCore.eventBus.emit).toHaveBeenCalledWith(
      'product:clicked',
      expect.any(Object)
    );
  });
});
```

### Integration Testing

```typescript
// integration.spec.ts
import { createApp } from 'vue';
import { MicroCore } from '@micro-core/core';
import { VueAdapter } from '@micro-core/adapter-vue';
import App from './App.vue';

describe('Vue Integration', () => {
  let microCore: MicroCore;
  let vueAdapter: VueAdapter;
  
  beforeEach(() => {
    vueAdapter = new VueAdapter({ version: '3' });
    microCore = new MicroCore({
      container: document.createElement('div'),
      adapters: [vueAdapter]
    });
  });
  
  afterEach(async () => {
    await microCore.destroy();
  });
  
  it('should integrate with Micro-Core', async () => {
    const wrapper = vueAdapter.createLifecycleWrapper(App, {
      container: document.createElement('div')
    });
    
    await wrapper.bootstrap({});
    await wrapper.mount({ name: 'test-app' });
    
    expect(wrapper).toBeDefined();
  });
});
```

## Best Practices

### 1. Component Organization

```
src/
  components/
    common/          # 共享组件
    product/         # 产品相关组件
    user/           # 用户相关组件
  composables/      # 组合式函数
  services/         # API 服务
  utils/           # 工具函数
  types/           # TypeScript 类型
```

### 2. Error Handling

```vue
<script setup>
import { onErrorCaptured, ref } from 'vue';
import { useEventBus } from '@micro-core/adapter-vue';

const eventBus = useEventBus();
const error = ref(null);

onErrorCaptured((err, instance, info) => {
  console.error('Vue Error:', err, info);
  error.value = err;
  
  // 发送错误到监控服务
  eventBus.emit('error:captured', {
    error: err.message,
    component: instance?.$options.name,
    info
  });
  
  return false; // 阻止错误继续传播
});
</script>

<template>
  <div v-if="error" class="error-boundary">
    <h3>出现错误</h3>
    <p>{{ error.message }}</p>
    <button @click="error = null">重试</button>
  </div>
  <slot v-else />
</template>
```

### 3. Memory Management

```vue
<script setup>
import { onUnmounted, ref } from 'vue';
import { useEventBus } from '@micro-core/adapter-vue';

const eventBus = useEventBus();
const cleanupTasks = ref([]);

// 添加清理任务
const addCleanupTask = (task) => {
  cleanupTasks.value.push(task);
};

// 组件卸载时清理
onUnmounted(() => {
  cleanupTasks.value.forEach(task => task());
  cleanupTasks.value = [];
});

// 使用示例
const unsubscribe = eventBus.on('some:event', handler);
addCleanupTask(unsubscribe);
</script>
```

### 4. Performance Monitoring

```vue
<script setup>
import { onMounted, onUnmounted } from 'vue';
import { useMicroCore } from '@micro-core/adapter-vue';

const { microCore } = useMicroCore();

onMounted(() => {
  const startTime = performance.now();
  
  // 组件挂载性能监控
  microCore.eventBus.emit('performance:component-mounted', {
    component: 'ProductList',
    mountTime: performance.now() - startTime
  });
});

onUnmounted(() => {
  // 组件卸载监控
  microCore.eventBus.emit('performance:component-unmounted', {
    component: 'ProductList'
  });
});
</script>
```

## Troubleshooting

### 常见问题

1. **响应式丢失**
   ```vue
   <!-- ❌ 错误 - 解构会丢失响应式 -->
   <script setup>
   const { user } = useGlobalState('user');
   </script>
   
   <!-- ✅ 正确 - 保持响应式 -->
   <script setup>
   const [user] = useGlobalState('user');
   </script>
   ```

2. **事件监听器未清理**
   ```vue
   <!-- ❌ 错误 - 未清理监听器 -->
   <script setup>
   onMounted(() => {
     eventBus.on('event', handler);
   });
   </script>
   
   <!-- ✅ 正确 - 清理监听器 -->
   <script setup>
   let unsubscribe = null;
   
   onMounted(() => {
     unsubscribe = eventBus.on('event', handler);
   });
   
   onUnmounted(() => {
     if (unsubscribe) {
       unsubscribe();
     }
   });
   </script>
   ```

3. **状态同步问题**
   ```vue
   <!-- ❌ 错误 - 直接修改状态 -->
   <script setup>
   const [state, setState] = useGlobalState('data');
   state.value.push(newItem); // 直接修改
   </script>
   
   <!-- ✅ 正确 - 不可变更新 -->
   <script setup>
   const [state, setState] = useGlobalState('data');
   setState([...state.value, newItem]);
   </script>
   ```

### 调试工具

```vue
<script setup>
import { onMounted } from 'vue';
import { useMicroCore } from '@micro-core/adapter-vue';

const { microCore, eventBus, globalState } = useMicroCore();

onMounted(() => {
  if (process.env.NODE_ENV === 'development') {
    // 添加调试工具到 window
    window.__VUE_MICRO_CORE_DEBUG__ = {
      microCore,
      eventBus,
      globalState,
      logEvents: () => {
        eventBus.on('*', (event, data) => {
          console.log(`[Vue Event] ${event}:`, data);
        });
      }
    };
  }
});
</script>
```

## Migration Guide

### 从 Vue 2 迁移到 Vue 3

```typescript
// Vue 2 风格
export default {
  data() {
    return {
      user: null
    };
  },
  
  mounted() {
    this.user = this.$globalState.get('user');
    this.unsubscribe = this.$eventBus.on('user:updated', this.handleUserUpdate);
  },
  
  beforeDestroy() {
    if (this.unsubscribe) {
      this.unsubscribe();
    }
  },
  
  methods: {
    handleUserUpdate(userData) {
      this.user = userData;
    }
  }
};

// Vue 3 Composition API 风格
<script setup>
import { onMounted, onUnmounted } from 'vue';
import { useGlobalState, useEventBus } from '@micro-core/adapter-vue';

const [user, setUser] = useGlobalState('user');
const eventBus = useEventBus();

let unsubscribe = null;

onMounted(() => {
  unsubscribe = eventBus.on('user:updated', (userData) => {
    setUser(userData);
  });
});

onUnmounted(() => {
  if (unsubscribe) {
    unsubscribe();
  }
});
</script>
```

---

这完成了 Vue 适配器文档。更多信息请参考：

- **[核心 API](/en/api/core)** - Micro-Core 主要功能
- **[适配器 API](/en/api/adapter-api)** - 适配器开发指南
- **[React 适配器](/en/ecosystem/adapters/react)** - React 集成
- **[Angular 适配器](/en/ecosystem/adapters/angular)** - Angular 集成
