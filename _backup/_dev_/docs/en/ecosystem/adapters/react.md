# React Adapter

The React Adapter provides seamless integration between React applications and Micro-Core, enabling React components to work as micro-frontend applications.

## Installation

```bash
npm install @micro-core/adapter-react
```

## Quick Start

### 1. Main Application Setup

```typescript
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';

// Create React adapter
const reactAdapter = new ReactAdapter({
  version: '18',
  concurrent: true,
  strictMode: true
});

// Create Micro-Core instance
const microCore = new MicroCore({
  container: '#app',
  adapters: [reactAdapter]
});

// Register React micro-app
microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-container',
  activeRule: '/users'
});

// Start the system
microCore.start();
```

### 2. React Micro-App Setup

```tsx
// src/index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { createReactAdapter } from '@micro-core/adapter-react';
import App from './App';

// Create adapter for this micro-app
const adapter = createReactAdapter({
  concurrent: true,
  strictMode: true
});

// Export lifecycle functions
export const { bootstrap, mount, unmount, update } = adapter({
  rootComponent: App,
  container: '#root'
});

// Standalone mode support
if (!window.__POWERED_BY_MICRO_CORE__) {
  const root = ReactDOM.createRoot(document.getElementById('root')!);
  root.render(<App />);
}
```

### 3. React Component with Micro-Core Integration

```tsx
// src/App.tsx
import React, { useState, useEffect } from 'react';
import { useMicroCore, useEventBus, useGlobalState } from '@micro-core/adapter-react';

function App() {
  const { microCore } = useMicroCore();
  const eventBus = useEventBus();
  const [user, setUser] = useGlobalState('user');
  const [notifications, setNotifications] = useState([]);
  
  useEffect(() => {
    // Listen for events from other micro-apps
    const unsubscribe = eventBus.on('notification:new', (notification) => {
      setNotifications(prev => [...prev, notification]);
    });
    
    return unsubscribe;
  }, [eventBus]);
  
  const handleUserUpdate = (userData) => {
    // Update global state
    setUser(userData);
    
    // Emit event to other micro-apps
    eventBus.emit('user:updated', userData);
  };
  
  return (
    <div className="react-micro-app">
      <h1>React Micro-App</h1>
      <UserProfile user={user} onUpdate={handleUserUpdate} />
      <NotificationList notifications={notifications} />
    </div>
  );
}

export default App;
```

## Configuration

### ReactAdapterConfig

```typescript
interface ReactAdapterConfig {
  // React version compatibility
  version?: '16' | '17' | '18';
  
  // Enable concurrent features (React 18+)
  concurrent?: boolean;
  
  // Enable strict mode
  strictMode?: boolean;
  
  // Error boundary configuration
  errorBoundary?: {
    enabled: boolean;
    fallback?: React.ComponentType<ErrorBoundaryProps>;
    onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  };
  
  // Development tools
  devtools?: boolean;
  
  // Custom render function
  customRender?: (component: React.ReactElement, container: Element) => void;
}
```

### Example Configuration

```typescript
const reactAdapter = new ReactAdapter({
  version: '18',
  concurrent: true,
  strictMode: process.env.NODE_ENV === 'development',
  
  errorBoundary: {
    enabled: true,
    fallback: ({ error, retry }) => (
      <div className="error-boundary">
        <h2>Something went wrong</h2>
        <p>{error.message}</p>
        <button onClick={retry}>Try Again</button>
      </div>
    ),
    onError: (error, errorInfo) => {
      console.error('React Error:', error, errorInfo);
      // Send to error tracking service
      errorTracker.captureException(error, { errorInfo });
    }
  },
  
  devtools: process.env.NODE_ENV === 'development'
});
```

## Hooks API

### useMicroCore()

Access the Micro-Core context.

```tsx
import { useMicroCore } from '@micro-core/adapter-react';

function MyComponent() {
  const { microCore, eventBus, globalState, router } = useMicroCore();
  
  return (
    <div>
      <p>Micro-Core Version: {microCore.version}</p>
    </div>
  );
}
```

### useEventBus()

Access the event bus for inter-app communication.

```tsx
import { useEventBus } from '@micro-core/adapter-react';

function EventComponent() {
  const eventBus = useEventBus();
  
  const sendMessage = () => {
    eventBus.emit('message:send', {
      text: 'Hello from React!',
      timestamp: Date.now()
    });
  };
  
  useEffect(() => {
    const unsubscribe = eventBus.on('message:received', (message) => {
      console.log('Received message:', message);
    });
    
    return unsubscribe;
  }, [eventBus]);
  
  return (
    <button onClick={sendMessage}>
      Send Message
    </button>
  );
}
```

### useGlobalState()

Manage global state across micro-apps.

```tsx
import { useGlobalState } from '@micro-core/adapter-react';

function UserComponent() {
  const [user, setUser] = useGlobalState('user', null);
  const [theme, setTheme] = useGlobalState('theme', 'light');
  
  const updateUser = (userData) => {
    setUser(userData);
  };
  
  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };
  
  return (
    <div className={`user-component theme-${theme}`}>
      <h2>Welcome, {user?.name || 'Guest'}</h2>
      <button onClick={toggleTheme}>
        Switch to {theme === 'light' ? 'Dark' : 'Light'} Theme
      </button>
    </div>
  );
}
```

### useRouter()

Access routing functionality.

```tsx
import { useRouter } from '@micro-core/adapter-react';

function NavigationComponent() {
  const router = useRouter();
  
  const navigateToUser = (userId) => {
    router.push(`/users/${userId}`);
  };
  
  const goBack = () => {
    router.back();
  };
  
  return (
    <nav>
      <button onClick={() => navigateToUser(123)}>
        View User Profile
      </button>
      <button onClick={goBack}>
        Go Back
      </button>
    </nav>
  );
}
```

### useAppCommunication()

Advanced inter-app communication.

```tsx
import { useAppCommunication } from '@micro-core/adapter-react';

function CommunicationComponent() {
  const { sendMessage, onMessage, broadcast } = useAppCommunication();
  const [messages, setMessages] = useState([]);
  
  useEffect(() => {
    const unsubscribe = onMessage((message) => {
      setMessages(prev => [...prev, message]);
    });
    
    return unsubscribe;
  }, [onMessage]);
  
  const sendToVueApp = () => {
    sendMessage('vue-app', {
      type: 'greeting',
      data: 'Hello from React!'
    });
  };
  
  const broadcastUpdate = () => {
    broadcast({
      type: 'data-update',
      timestamp: Date.now()
    });
  };
  
  return (
    <div>
      <button onClick={sendToVueApp}>Send to Vue App</button>
      <button onClick={broadcastUpdate}>Broadcast Update</button>
      
      <div className="messages">
        {messages.map((msg, index) => (
          <div key={index}>{JSON.stringify(msg)}</div>
        ))}
      </div>
    </div>
  );
}
```

## Advanced Features

### Error Boundaries

```tsx
import { ErrorBoundary } from '@micro-core/adapter-react';

function App() {
  return (
    <ErrorBoundary
      fallback={({ error, retry }) => (
        <div className="error-fallback">
          <h2>Oops! Something went wrong</h2>
          <details>
            <summary>Error details</summary>
            <pre>{error.message}</pre>
          </details>
          <button onClick={retry}>Try again</button>
        </div>
      )}
      onError={(error, errorInfo) => {
        console.error('App Error:', error, errorInfo);
      }}
    >
      <UserInterface />
    </ErrorBoundary>
  );
}
```

### Suspense Integration

```tsx
import React, { Suspense } from 'react';
import { useMicroCore } from '@micro-core/adapter-react';

const LazyComponent = React.lazy(() => import('./LazyComponent'));

function App() {
  const { microCore } = useMicroCore();
  
  return (
    <div>
      <Suspense fallback={<div>Loading...</div>}>
        <LazyComponent />
      </Suspense>
    </div>
  );
}
```

### Context Providers

```tsx
import { MicroCoreProvider } from '@micro-core/adapter-react';

function AppWrapper() {
  return (
    <MicroCoreProvider>
      <App />
    </MicroCoreProvider>
  );
}

// Custom context for app-specific data
const AppContext = React.createContext();

function App() {
  const [appData, setAppData] = useState({});
  
  return (
    <AppContext.Provider value={{ appData, setAppData }}>
      <UserInterface />
    </AppContext.Provider>
  );
}
```

### Higher-Order Components

```tsx
import { withMicroCore } from '@micro-core/adapter-react';

// HOC for injecting Micro-Core context
const withMicroCore = (WrappedComponent) => {
  return function WithMicroCoreComponent(props) {
    const microCoreContext = useMicroCore();
    
    return (
      <WrappedComponent
        {...props}
        microCore={microCoreContext}
      />
    );
  };
};

// Usage
const EnhancedComponent = withMicroCore(MyComponent);
```

## Lifecycle Management

### Custom Lifecycle Wrapper

```tsx
import { createReactAdapter } from '@micro-core/adapter-react';

const customAdapter = createReactAdapter({
  concurrent: true,
  
  // Custom lifecycle hooks
  beforeMount: async (props) => {
    console.log('Before mounting React app');
    await initializeServices();
    return props;
  },
  
  afterMount: async (props) => {
    console.log('After mounting React app');
    trackAppMount(props.name);
  },
  
  beforeUnmount: async (props) => {
    console.log('Before unmounting React app');
    await saveAppState();
  },
  
  afterUnmount: async (props) => {
    console.log('After unmounting React app');
    cleanupResources();
  }
});

export const { bootstrap, mount, unmount, update } = customAdapter({
  rootComponent: App,
  container: '#root'
});
```

### Conditional Rendering

```tsx
function App() {
  const { microCore } = useMicroCore();
  const [isStandalone, setIsStandalone] = useState(false);
  
  useEffect(() => {
    setIsStandalone(!window.__POWERED_BY_MICRO_CORE__);
  }, []);
  
  if (isStandalone) {
    return <StandaloneApp />;
  }
  
  return <MicroFrontendApp />;
}
```

## State Management Integration

### Redux Integration

```tsx
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import { useGlobalState } from '@micro-core/adapter-react';

// Redux store with Micro-Core integration
const createStoreWithMicroCore = (reducer, globalState) => {
  const store = createStore(reducer);
  
  // Sync Redux state with global state
  store.subscribe(() => {
    const state = store.getState();
    globalState.set('redux-state', state);
  });
  
  return store;
};

function App() {
  const [, setGlobalState] = useGlobalState('redux-state');
  const store = useMemo(() => 
    createStoreWithMicroCore(rootReducer, { set: setGlobalState }), 
    []
  );
  
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}
```

### Context API Integration

```tsx
import { useGlobalState } from '@micro-core/adapter-react';

const AppStateContext = React.createContext();

function AppStateProvider({ children }) {
  const [user, setUser] = useGlobalState('user');
  const [theme, setTheme] = useGlobalState('theme', 'light');
  
  const value = {
    user,
    setUser,
    theme,
    setTheme
  };
  
  return (
    <AppStateContext.Provider value={value}>
      {children}
    </AppStateContext.Provider>
  );
}

function useAppState() {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within AppStateProvider');
  }
  return context;
}
```

## Routing Integration

### React Router Integration

```tsx
import { BrowserRouter, Routes, Route, useNavigate } from 'react-router-dom';
import { useRouter } from '@micro-core/adapter-react';

function AppRouter() {
  const microRouter = useRouter();
  const navigate = useNavigate();
  
  useEffect(() => {
    // Sync React Router with Micro-Core router
    const unsubscribe = microRouter.onRouteChange((route) => {
      navigate(route.path, { replace: true });
    });
    
    return unsubscribe;
  }, [microRouter, navigate]);
  
  return (
    <Routes>
      <Route path="/users/*" element={<UserPages />} />
      <Route path="/profile/*" element={<ProfilePages />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

function App() {
  return (
    <BrowserRouter>
      <AppRouter />
    </BrowserRouter>
  );
}
```

### Route Guards

```tsx
import { useRouter, useGlobalState } from '@micro-core/adapter-react';

function ProtectedRoute({ children, requiredRole }) {
  const router = useRouter();
  const [user] = useGlobalState('user');
  
  useEffect(() => {
    if (!user || !user.roles.includes(requiredRole)) {
      router.push('/login');
    }
  }, [user, requiredRole, router]);
  
  if (!user || !user.roles.includes(requiredRole)) {
    return <div>Redirecting...</div>;
  }
  
  return children;
}

// Usage
function AdminPanel() {
  return (
    <ProtectedRoute requiredRole="admin">
      <AdminContent />
    </ProtectedRoute>
  );
}
```

## Performance Optimization

### Memoization

```tsx
import { memo, useMemo, useCallback } from 'react';
import { useGlobalState, useEventBus } from '@micro-core/adapter-react';

const OptimizedComponent = memo(function OptimizedComponent({ data }) {
  const [user] = useGlobalState('user');
  const eventBus = useEventBus();
  
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      processed: true,
      userId: user?.id
    }));
  }, [data, user?.id]);
  
  const handleClick = useCallback((item) => {
    eventBus.emit('item:clicked', item);
  }, [eventBus]);
  
  return (
    <div>
      {processedData.map(item => (
        <div key={item.id} onClick={() => handleClick(item)}>
          {item.name}
        </div>
      ))}
    </div>
  );
});
```

### Code Splitting

```tsx
import { lazy, Suspense } from 'react';
import { useRouter } from '@micro-core/adapter-react';

// Lazy load components
const UserList = lazy(() => import('./components/UserList'));
const UserDetail = lazy(() => import('./components/UserDetail'));

function App() {
  const router = useRouter();
  const [currentRoute, setCurrentRoute] = useState(router.getCurrentRoute());
  
  useEffect(() => {
    const unsubscribe = router.onRouteChange(setCurrentRoute);
    return unsubscribe;
  }, [router]);
  
  const renderComponent = () => {
    switch (currentRoute.path) {
      case '/users':
        return <UserList />;
      case '/users/:id':
        return <UserDetail />;
      default:
        return <div>Not Found</div>;
    }
  };
  
  return (
    <Suspense fallback={<div>Loading...</div>}>
      {renderComponent()}
    </Suspense>
  );
}
```

## Testing

### Unit Testing

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { MicroCoreTestProvider } from '@micro-core/adapter-react/testing';
import MyComponent from './MyComponent';

describe('MyComponent', () => {
  const mockMicroCore = {
    eventBus: {
      emit: jest.fn(),
      on: jest.fn(() => jest.fn())
    },
    globalState: {
      get: jest.fn(),
      set: jest.fn(),
      subscribe: jest.fn(() => jest.fn())
    }
  };
  
  it('should render correctly', () => {
    render(
      <MicroCoreTestProvider value={mockMicroCore}>
        <MyComponent />
      </MicroCoreTestProvider>
    );
    
    expect(screen.getByText('My Component')).toBeInTheDocument();
  });
  
  it('should emit event on button click', () => {
    render(
      <MicroCoreTestProvider value={mockMicroCore}>
        <MyComponent />
      </MicroCoreTestProvider>
    );
    
    fireEvent.click(screen.getByText('Click Me'));
    
    expect(mockMicroCore.eventBus.emit).toHaveBeenCalledWith(
      'button:clicked',
      expect.any(Object)
    );
  });
});
```

### Integration Testing

```tsx
import { render } from '@testing-library/react';
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';
import App from './App';

describe('App Integration', () => {
  let microCore;
  let reactAdapter;
  
  beforeEach(() => {
    reactAdapter = new ReactAdapter();
    microCore = new MicroCore({
      container: document.createElement('div'),
      adapters: [reactAdapter]
    });
  });
  
  afterEach(async () => {
    await microCore.destroy();
  });
  
  it('should integrate with Micro-Core', async () => {
    const wrapper = reactAdapter.createLifecycleWrapper(App, {
      container: document.createElement('div')
    });
    
    await wrapper.bootstrap({});
    await wrapper.mount({ name: 'test-app' });
    
    // Test integration
    expect(wrapper).toBeDefined();
  });
});
```

## Best Practices

### 1. Component Organization

```tsx
// Organize components by feature
src/
  components/
    common/          # Shared components
    user/           # User-related components
    navigation/     # Navigation components
  hooks/            # Custom hooks
  services/         # API services
  utils/           # Utility functions
  types/           # TypeScript types
```

### 2. Error Handling

```tsx
import { ErrorBoundary } from '@micro-core/adapter-react';

function App() {
  return (
    <ErrorBoundary
      fallback={ErrorFallback}
      onError={(error, errorInfo) => {
        // Log error to monitoring service
        console.error('App Error:', error, errorInfo);
      }}
    >
      <AppContent />
    </ErrorBoundary>
  );
}

function ErrorFallback({ error, retry }) {
  return (
    <div className="error-fallback">
      <h2>Something went wrong</h2>
      <p>{error.message}</p>
      <button onClick={retry}>Try again</button>
    </div>
  );
}
```

### 3. Performance Monitoring

```tsx
import { useEffect } from 'react';
import { useMicroCore } from '@micro-core/adapter-react';

function usePerformanceMonitoring() {
  const { microCore } = useMicroCore();
  
  useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        microCore.eventBus.emit('performance:metric', {
          name: entry.name,
          duration: entry.duration,
          type: entry.entryType
        });
      });
    });
    
    observer.observe({ entryTypes: ['measure', 'navigation'] });
    
    return () => observer.disconnect();
  }, [microCore]);
}
```

### 4. Memory Management

```tsx
function useCleanup() {
  const cleanupTasks = useRef([]);
  
  const addCleanupTask = useCallback((task) => {
    cleanupTasks.current.push(task);
  }, []);
  
  useEffect(() => {
    return () => {
      cleanupTasks.current.forEach(task => task());
      cleanupTasks.current = [];
    };
  }, []);
  
  return addCleanupTask;
}

function MyComponent() {
  const addCleanupTask = useCleanup();
  const eventBus = useEventBus();
  
  useEffect(() => {
    const unsubscribe = eventBus.on('some:event', handler);
    addCleanupTask(unsubscribe);
  }, [eventBus, addCleanupTask]);
  
  return <div>Component Content</div>;
}
```

## Troubleshooting

### Common Issues

1. **Hook Rules Violation**
   ```tsx
   // ❌ Wrong - calling hooks conditionally
   if (condition) {
     const value = useGlobalState('key');
   }
   
   // ✅ Correct - always call hooks
   const value = useGlobalState('key');
   if (condition && value) {
     // use value
   }
   ```

2. **Memory Leaks**
   ```tsx
   // ❌ Wrong - not cleaning up subscriptions
   useEffect(() => {
     eventBus.on('event', handler);
   }, []);
   
   // ✅ Correct - cleanup subscriptions
   useEffect(() => {
     const unsubscribe = eventBus.on('event', handler);
     return unsubscribe;
   }, []);
   ```

3. **State Synchronization Issues**
   ```tsx
   // ❌ Wrong - direct state mutation
   const [state, setState] = useGlobalState('data');
   state.push(newItem); // Mutating state directly
   
   // ✅ Correct - immutable updates
   const [state, setState] = useGlobalState('data');
   setState([...state, newItem]);
   ```

### Debug Tools

```tsx
import { useEffect } from 'react';
import { useMicroCore } from '@micro-core/adapter-react';

function DebugPanel() {
  const { microCore, eventBus, globalState } = useMicroCore();
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Add debug tools to window
      window.__MICRO_CORE_DEBUG__ = {
        microCore,
        eventBus,
        globalState,
        logEvents: () => {
          eventBus.on('*', (event, data) => {
            console.log(`[Event] ${event}:`, data);
          });
        }
      };
    }
  }, [microCore, eventBus, globalState]);
  
  return null;
}
```

---

This completes the React Adapter documentation. For more information, see:

- **[Core API](/en/api/core)** - Main Micro-Core functionality
- **[Adapter API](/en/api/adapter-api)** - Adapter development guide
- **[Vue Adapter](/en/ecosystem/adapters/vue)** - Vue integration
- **[Angular Adapter](/en/ecosystem/adapters/angular)** - Angular integration
