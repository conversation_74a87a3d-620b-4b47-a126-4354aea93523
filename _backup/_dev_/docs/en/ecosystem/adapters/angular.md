# Angular Adapter

The Angular Adapter provides seamless integration between Angular applications and Micro-Core, supporting Angular 12+ with dependency injection and zone.js compatibility.

## Installation

```bash
npm install @micro-core/adapter-angular
```

## Quick Start

### 1. Main Application Setup

```typescript
import { MicroCore } from '@micro-core/core';
import { AngularAdapter } from '@micro-core/adapter-angular';

// Create Angular adapter
const angularAdapter = new AngularAdapter({
  version: '16',
  enableIvy: true
});

// Create Micro-Core instance
const microCore = new MicroCore({
  container: '#app',
  adapters: [angularAdapter]
});

// Register Angular micro-app
microCore.registerApp({
  name: 'order-app',
  entry: 'http://localhost:3003',
  container: '#order-container',
  activeRule: '/orders'
});

// Start the system
microCore.start();
```

### 2. Angular Micro-App Setup

```typescript
// src/main.ts
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { createAngularAdapter } from '@micro-core/adapter-angular';
import { AppModule } from './app/app.module';

// Create adapter for this micro-app
const adapter = createAngularAdapter({
  enableIvy: true
});

// Export lifecycle functions
export const { bootstrap, mount, unmount, update } = adapter({
  rootModule: AppModule,
  container: '#app'
});

// Standalone mode support
if (!window.__POWERED_BY_MICRO_CORE__) {
  platformBrowserDynamic()
    .bootstrapModule(AppModule)
    .catch(err => console.error(err));
}
```

### 3. App Module Configuration

```typescript
// src/app/app.module.ts
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { MicroCoreModule } from '@micro-core/adapter-angular';

import { AppComponent } from './app.component';
import { OrderListComponent } from './order-list/order-list.component';

@NgModule({
  declarations: [
    AppComponent,
    OrderListComponent
  ],
  imports: [
    BrowserModule,
    MicroCoreModule.forRoot({
      enableDevtools: true
    })
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
```

### 4. Angular Component with Micro-Core Integration

```typescript
// src/app/app.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MicroCoreService, EventBusService, GlobalStateService } from '@micro-core/adapter-angular';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-root',
  template: `
    <div class="angular-micro-app">
      <h1>Angular Micro-App</h1>
      <app-order-list 
        [orders]="orders" 
        [user]="user"
        (orderSelected)="handleOrderSelected($event)">
      </app-order-list>
      <div class="notifications">
        <div *ngFor="let notification of notifications" class="notification">
          {{ notification.message }}
        </div>
      </div>
    </div>
  `,
  styles: [`
    .angular-micro-app {
      padding: 20px;
      border: 2px solid #dd0031;
      border-radius: 8px;
    }
    .notification {
      padding: 8px;
      margin: 4px 0;
      background: #f0f0f0;
      border-radius: 4px;
    }
  `]
})
export class AppComponent implements OnInit, OnDestroy {
  orders: any[] = [];
  user: any = null;
  notifications: any[] = [];
  
  private subscriptions: Subscription[] = [];

  constructor(
    private microCore: MicroCoreService,
    private eventBus: EventBusService,
    private globalState: GlobalStateService
  ) {}

  ngOnInit() {
    // Subscribe to global state
    this.subscriptions.push(
      this.globalState.get$('user').subscribe(user => {
        this.user = user;
      })
    );

    this.subscriptions.push(
      this.globalState.get$('orders').subscribe(orders => {
        this.orders = orders || [];
      })
    );

    // Listen for events from other micro-apps
    this.subscriptions.push(
      this.eventBus.on$('notification:new').subscribe(notification => {
        this.notifications.push(notification);
      })
    );

    // Load initial data
    this.loadOrders();
  }

  ngOnDestroy() {
    // Cleanup subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  handleOrderSelected(order: any) {
    // Update global state
    this.globalState.set('selectedOrder', order);
    
    // Emit event to other micro-apps
    this.eventBus.emit('order:selected', order);
  }

  private async loadOrders() {
    try {
      const response = await fetch('/api/orders');
      const orderData = await response.json();
      this.globalState.set('orders', orderData);
    } catch (error) {
      console.error('Failed to load orders:', error);
    }
  }
}
```

## Configuration

### AngularAdapterConfig

```typescript
interface AngularAdapterConfig {
  // Angular version
  version?: string;
  
  // Enable Ivy renderer
  enableIvy?: boolean;
  
  // Zone.js configuration
  zone?: {
    enabled: boolean;
    config?: any;
  };
  
  // Providers to inject
  providers?: any[];
  
  // Modules to import
  imports?: any[];
  
  // Error handler
  errorHandler?: (error: Error) => void;
  
  // Development tools
  enableDevtools?: boolean;
}
```

### Example Configuration

```typescript
const angularAdapter = new AngularAdapter({
  version: '16',
  enableIvy: true,
  
  zone: {
    enabled: true,
    config: {
      eventCoalescing: true,
      runCoalescing: true
    }
  },
  
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }
  ],
  
  imports: [
    HttpClientModule,
    RouterModule
  ],
  
  errorHandler: (error) => {
    console.error('Angular Error:', error);
    // Send to error tracking service
    errorTracker.captureException(error, {
      framework: 'angular'
    });
  },
  
  enableDevtools: process.env['NODE_ENV'] === 'development'
});
```

## Services

### MicroCoreService

Access the Micro-Core context.

```typescript
import { Injectable } from '@angular/core';
import { MicroCoreService } from '@micro-core/adapter-angular';

@Injectable({
  providedIn: 'root'
})
export class DataService {
  constructor(private microCore: MicroCoreService) {}

  getMicroCoreVersion(): string {
    return this.microCore.getVersion();
  }

  getEventBus() {
    return this.microCore.getEventBus();
  }

  getGlobalState() {
    return this.microCore.getGlobalState();
  }

  getRouter() {
    return this.microCore.getRouter();
  }
}
```

### EventBusService

Inter-app communication service.

```typescript
import { Injectable } from '@angular/core';
import { EventBusService } from '@micro-core/adapter-angular';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  constructor(private eventBus: EventBusService) {}

  // Send notification to other apps
  sendNotification(message: string, type: 'info' | 'warning' | 'error' = 'info') {
    this.eventBus.emit('notification:send', {
      message,
      type,
      timestamp: Date.now(),
      source: 'angular-app'
    });
  }

  // Listen for notifications
  onNotification(): Observable<any> {
    return this.eventBus.on$('notification:received');
  }

  // Broadcast data update
  broadcastDataUpdate(data: any) {
    this.eventBus.emit('data:updated', {
      data,
      timestamp: Date.now()
    });
  }
}
```

### GlobalStateService

Global state management service.

```typescript
import { Injectable } from '@angular/core';
import { GlobalStateService } from '@micro-core/adapter-angular';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, distinctUntilChanged } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private userSubject = new BehaviorSubject<any>(null);

  constructor(private globalState: GlobalStateService) {
    // Initialize with global state
    this.globalState.get$('user').subscribe(user => {
      this.userSubject.next(user);
    });
  }

  // Get current user
  getCurrentUser(): any {
    return this.userSubject.value;
  }

  // Get user as observable
  getUser$(): Observable<any> {
    return this.userSubject.asObservable();
  }

  // Update user
  updateUser(userData: any): void {
    this.globalState.set('user', userData);
  }

  // Check if user is authenticated
  isAuthenticated$(): Observable<boolean> {
    return this.getUser$().pipe(
      map(user => !!user),
      distinctUntilChanged()
    );
  }

  // Get user role
  getUserRole$(): Observable<string> {
    return this.getUser$().pipe(
      map(user => user?.role || 'guest'),
      distinctUntilChanged()
    );
  }
}
```

### RouterService

Routing integration service.

```typescript
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { RouterService as MicroRouterService } from '@micro-core/adapter-angular';

@Injectable({
  providedIn: 'root'
})
export class NavigationService {
  constructor(
    private router: Router,
    private microRouter: MicroRouterService
  ) {
    // Sync Angular router with Micro-Core router
    this.setupRouterSync();
  }

  // Navigate within the micro-app
  navigateLocal(path: string): void {
    this.router.navigate([path]);
  }

  // Navigate to other micro-apps
  navigateGlobal(path: string): void {
    this.microRouter.push(path);
  }

  // Go back
  goBack(): void {
    this.microRouter.back();
  }

  private setupRouterSync(): void {
    // Listen for global route changes
    this.microRouter.onRouteChange((route) => {
      if (route.app === 'angular-app') {
        this.router.navigateByUrl(route.path);
      }
    });

    // Notify global router of local route changes
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.microRouter.emit('route:changed', {
          path: event.url,
          app: 'angular-app'
        });
      }
    });
  }
}
```

## Directives

### MicroCore Directives

```typescript
// src/app/directives/track-click.directive.ts
import { Directive, Input, HostListener } from '@angular/core';
import { EventBusService } from '@micro-core/adapter-angular';

@Directive({
  selector: '[appTrackClick]'
})
export class TrackClickDirective {
  @Input() appTrackClick: string = '';
  @Input() trackData: any = {};

  constructor(private eventBus: EventBusService) {}

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    this.eventBus.emit('element:clicked', {
      element: this.appTrackClick,
      data: this.trackData,
      timestamp: Date.now(),
      coordinates: {
        x: event.clientX,
        y: event.clientY
      }
    });
  }
}

// Usage in template
<button appTrackClick="order-button" [trackData]="{ orderId: order.id }">
  Place Order
</button>
```

### State Binding Directive

```typescript
// src/app/directives/global-state.directive.ts
import { Directive, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { GlobalStateService } from '@micro-core/adapter-angular';
import { Subscription } from 'rxjs';

@Directive({
  selector: '[appGlobalState]'
})
export class GlobalStateDirective implements OnInit, OnDestroy {
  @Input() appGlobalState: string = '';
  @Output() stateChange = new EventEmitter<any>();

  private subscription?: Subscription;

  constructor(private globalState: GlobalStateService) {}

  ngOnInit() {
    if (this.appGlobalState) {
      this.subscription = this.globalState.get$(this.appGlobalState)
        .subscribe(value => {
          this.stateChange.emit(value);
        });
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

// Usage in template
<div appGlobalState="theme" (stateChange)="onThemeChange($event)">
  Current theme: {{ currentTheme }}
</div>
```

## Guards

### Authentication Guard

```typescript
// src/app/guards/auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { UserService } from '../services/user.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private userService: UserService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    return this.userService.isAuthenticated$().pipe(
      take(1),
      map(isAuthenticated => {
        if (!isAuthenticated) {
          this.router.navigate(['/login']);
          return false;
        }
        return true;
      })
    );
  }
}
```

### Role Guard

```typescript
// src/app/guards/role.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { UserService } from '../services/user.service';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {
  constructor(
    private userService: UserService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean> {
    const requiredRole = route.data['role'];
    
    return this.userService.getUserRole$().pipe(
      take(1),
      map(userRole => {
        if (userRole !== requiredRole) {
          this.router.navigate(['/unauthorized']);
          return false;
        }
        return true;
      })
    );
  }
}

// Usage in routing
const routes: Routes = [
  {
    path: 'admin',
    component: AdminComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: { role: 'admin' }
  }
];
```

## Pipes

### Global State Pipe

```typescript
// src/app/pipes/global-state.pipe.ts
import { Pipe, PipeTransform } from '@angular/core';
import { Observable } from 'rxjs';
import { GlobalStateService } from '@micro-core/adapter-angular';

@Pipe({
  name: 'globalState',
  pure: false
})
export class GlobalStatePipe implements PipeTransform {
  constructor(private globalState: GlobalStateService) {}

  transform(key: string): Observable<any> {
    return this.globalState.get$(key);
  }
}

// Usage in template
<div>
  User: {{ 'user' | globalState | async | json }}
</div>
```

### Event Pipe

```typescript
// src/app/pipes/event.pipe.ts
import { Pipe, PipeTransform } from '@angular/core';
import { Observable } from 'rxjs';
import { EventBusService } from '@micro-core/adapter-angular';

@Pipe({
  name: 'event',
  pure: false
})
export class EventPipe implements PipeTransform {
  constructor(private eventBus: EventBusService) {}

  transform(eventName: string): Observable<any> {
    return this.eventBus.on$(eventName);
  }
}

// Usage in template
<div *ngIf="'notification:new' | event | async as notification">
  New notification: {{ notification.message }}
</div>
```

## Interceptors

### Micro-Core HTTP Interceptor

```typescript
// src/app/interceptors/micro-core.interceptor.ts
import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { EventBusService, GlobalStateService } from '@micro-core/adapter-angular';

@Injectable()
export class MicroCoreInterceptor implements HttpInterceptor {
  constructor(
    private eventBus: EventBusService,
    private globalState: GlobalStateService
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Add global headers
    const user = this.globalState.get('user');
    let modifiedReq = req;

    if (user?.token) {
      modifiedReq = req.clone({
        setHeaders: {
          'Authorization': `Bearer ${user.token}`
        }
      });
    }

    // Emit request event
    this.eventBus.emit('http:request', {
      url: req.url,
      method: req.method,
      timestamp: Date.now()
    });

    return next.handle(modifiedReq).pipe(
      tap(event => {
        if (event.type === HttpEventType.Response) {
          // Emit response event
          this.eventBus.emit('http:response', {
            url: req.url,
            status: event.status,
            timestamp: Date.now()
          });
        }
      }),
      catchError(error => {
        // Emit error event
        this.eventBus.emit('http:error', {
          url: req.url,
          error: error.message,
          timestamp: Date.now()
        });
        throw error;
      })
    );
  }
}
```

## Advanced Features

### Lazy Loading

```typescript
// src/app/app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';

const routes: Routes = [
  {
    path: 'orders',
    loadChildren: () => import('./orders/orders.module').then(m => m.OrdersModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'products',
    loadChildren: () => import('./products/products.module').then(m => m.ProductsModule)
  },
  {
    path: '',
    redirectTo: '/orders',
    pathMatch: 'full'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
```

### Dynamic Component Loading

```typescript
// src/app/services/dynamic-component.service.ts
import { Injectable, ComponentFactoryResolver, ViewContainerRef, ComponentRef } from '@angular/core';
import { EventBusService } from '@micro-core/adapter-angular';

@Injectable({
  providedIn: 'root'
})
export class DynamicComponentService {
  constructor(
    private componentFactoryResolver: ComponentFactoryResolver,
    private eventBus: EventBusService
  ) {}

  loadComponent<T>(
    component: any,
    viewContainer: ViewContainerRef,
    data?: any
  ): ComponentRef<T> {
    const componentFactory = this.componentFactoryResolver.resolveComponentFactory(component);
    const componentRef = viewContainer.createComponent(componentFactory);

    // Pass data to component
    if (data && componentRef.instance) {
      Object.assign(componentRef.instance, data);
    }

    // Emit component loaded event
    this.eventBus.emit('component:loaded', {
      component: component.name,
      timestamp: Date.now()
    });

    return componentRef;
  }
}
```

## Testing

### Unit Testing

```typescript
// src/app/app.component.spec.ts
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AppComponent } from './app.component';
import { MicroCoreTestingModule } from '@micro-core/adapter-angular/testing';

describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  let mockEventBus: jasmine.SpyObj<EventBusService>;
  let mockGlobalState: jasmine.SpyObj<GlobalStateService>;

  beforeEach(async () => {
    const eventBusSpy = jasmine.createSpyObj('EventBusService', ['emit', 'on$']);
    const globalStateSpy = jasmine.createSpyObj('GlobalStateService', ['get$', 'set']);

    await TestBed.configureTestingModule({
      declarations: [AppComponent],
      imports: [MicroCoreTestingModule],
      providers: [
        { provide: EventBusService, useValue: eventBusSpy },
        { provide: GlobalStateService, useValue: globalStateSpy }
      ]
    }).compileComponents();

    mockEventBus = TestBed.inject(EventBusService) as jasmine.SpyObj<EventBusService>;
    mockGlobalState = TestBed.inject(GlobalStateService) as jasmine.SpyObj<GlobalStateService>;
    
    // Setup mock returns
    mockGlobalState.get$.and.returnValue(of(null));
    mockEventBus.on$.and.returnValue(of({}));
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle order selection', () => {
    const order = { id: 1, name: 'Test Order' };
    
    component.handleOrderSelected(order);
    
    expect(mockGlobalState.set).toHaveBeenCalledWith('selectedOrder', order);
    expect(mockEventBus.emit).toHaveBeenCalledWith('order:selected', order);
  });
});
```

### Integration Testing

```typescript
// src/app/integration.spec.ts
import { TestBed } from '@angular/core/testing';
import { MicroCore } from '@micro-core/core';
import { AngularAdapter } from '@micro-core/adapter-angular';
import { AppModule } from './app.module';

describe('Angular Integration', () => {
  let microCore: MicroCore;
  let angularAdapter: AngularAdapter;

  beforeEach(() => {
    angularAdapter = new AngularAdapter({ enableIvy: true });
    microCore = new MicroCore({
      container: document.createElement('div'),
      adapters: [angularAdapter]
    });
  });

  afterEach(async () => {
    await microCore.destroy();
  });

  it('should integrate with Micro-Core', async () => {
    const wrapper = angularAdapter.createLifecycleWrapper(AppModule, {
      container: document.createElement('div')
    });

    await wrapper.bootstrap({});
    await wrapper.mount({ name: 'test-app' });

    expect(wrapper).toBeDefined();
  });
});
```

## Best Practices

### 1. Module Organization

```
src/app/
  core/              # Core services and guards
  shared/            # Shared components and pipes
  features/          # Feature modules
    orders/
    products/
    users/
  directives/        # Custom directives
  pipes/            # Custom pipes
  interceptors/     # HTTP interceptors
```

### 2. Error Handling

```typescript
// src/app/services/error-handler.service.ts
import { Injectable, ErrorHandler } from '@angular/core';
import { EventBusService } from '@micro-core/adapter-angular';

@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
  constructor(private eventBus: EventBusService) {}

  handleError(error: any): void {
    console.error('Angular Error:', error);
    
    // Emit error event
    this.eventBus.emit('error:angular', {
      error: error.message,
      stack: error.stack,
      timestamp: Date.now()
    });
  }
}

// Register in app.module.ts
providers: [
  { provide: ErrorHandler, useClass: GlobalErrorHandler }
]
```

### 3. Memory Management

```typescript
// src/app/components/base.component.ts
import { OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';

export abstract class BaseComponent implements OnDestroy {
  protected destroy$ = new Subject<void>();

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}

// Usage in components
export class MyComponent extends BaseComponent implements OnInit {
  ngOnInit() {
    this.eventBus.on$('some:event')
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        // Handle event
      });
  }
}
```

### 4. Performance Monitoring

```typescript
// src/app/services/performance.service.ts
import { Injectable } from '@angular/core';
import { EventBusService } from '@micro-core/adapter-angular';

@Injectable({
  providedIn: 'root'
})
export class PerformanceService {
  constructor(private eventBus: EventBusService) {}

  measureComponentLoad(componentName: string, startTime: number) {
    const loadTime = performance.now() - startTime;
    
    this.eventBus.emit('performance:component-load', {
      component: componentName,
      loadTime,
      timestamp: Date.now()
    });
  }

  measureHttpRequest(url: string, startTime: number) {
    const requestTime = performance.now() - startTime;
    
    this.eventBus.emit('performance:http-request', {
      url,
      requestTime,
      timestamp: Date.now()
    });
  }
}
```

## Troubleshooting

### Common Issues

1. **Zone.js Conflicts**
   ```typescript
   // Disable zone.js patching for specific operations
   import { NgZone } from '@angular/core';
   
   constructor(private ngZone: NgZone) {}
   
   runOutsideAngular() {
     this.ngZone.runOutsideAngular(() => {
       // Code that doesn't need change detection
     });
   }
   ```

2. **Dependency Injection Issues**
   ```typescript
   // Use Injector for dynamic service resolution
   import { Injector } from '@angular/core';
   
   constructor(private injector: Injector) {}
   
   getService<T>(token: any): T {
     return this.injector.get(token);
   }
   ```

3. **Change Detection Problems**
   ```typescript
   // Manual change detection trigger
   import { ChangeDetectorRef } from '@angular/core';
   
   constructor(private cdr: ChangeDetectorRef) {}
   
   updateView() {
     this.cdr.detectChanges();
   }
   ```

---

This completes the Angular Adapter documentation. For more information, see:

- **[Core API](/en/api/core)** - Main Micro-Core functionality
- **[Adapter API](/en/api/adapter-api)** - Adapter development guide
- **[React Adapter](/en/ecosystem/adapters/react)** - React integration
- **[Vue Adapter](/en/ecosystem/adapters/vue)** - Vue integration