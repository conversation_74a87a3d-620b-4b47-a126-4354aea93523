# Sandbox API (Part 2)

## StyleSandbox (Continued)

### API Reference (Continued)

#### addStyle(cssText)

Add CSS style text.

```typescript
// Enable style isolation
const styleSandbox = new StyleSandbox({
  name: 'app1',
  el: '#app1-container',
  scopeSelector: '[data-app="app1"]',
  useStrictMode: true
});

// Use CSS namespacing
styleSandbox.addStyle(`
  /* Use application prefix */
  .app1-title {
    color: red;
  }
  
  .app1-container {
    padding: 10px;
  }
`);

// Use BEM naming convention
styleSandbox.addStyle(`
  /* Use BEM naming convention */
  .app1__title {
    color: red;
  }
  
  .app1__container {
    padding: 10px;
  }
  
  .app1__button--primary {
    background-color: blue;
  }
`);
```

### Event Conflicts

**Issue**: Multiple micro-applications listen to the same global events, causing conflicts.

**Solutions**:

1. Enable event isolation
2. Use event namespacing
3. Clean up event listeners during unmounting

```typescript
// Enable event isolation
const sandbox = new Sandbox({
  name: 'app1',
  isolateWindowEvents: true
});

// Use event namespacing
sandbox.execScript(`
  // Use application prefix
  window.addEventListener('app1:click', (event) => {
    console.log('App1 click event');
  });
  
  // Trigger namespaced event
  const event = new CustomEvent('app1:click');
  window.dispatchEvent(event);
`);

// Clean up event listeners during unmounting
sandbox.execScript(`
  // Save event listener references
  const listeners = [];
  
  function addListener(target, type, handler, options) {
    target.addEventListener(type, handler, options);
    listeners.push({ target, type, handler });
  }
  
  // Add event listeners
  addListener(window, 'resize', handleResize);
  addListener(document, 'click', handleClick);
  
  // Cleanup function
  window.__cleanupEventListeners = function() {
    listeners.forEach(({ target, type, handler }) => {
      target.removeEventListener(type, handler);
    });
    listeners.length = 0;
  };
`);

// Call cleanup function during unmounting
function unmountApp() {
  sandbox.execScript(`
    if (typeof window.__cleanupEventListeners === 'function') {
      window.__cleanupEventListeners();
    }
  `);
  
  // Deactivate sandbox
  sandbox.deactivate();
}
```

### Memory Leaks

**Issue**: Sandbox not properly cleaned up, causing memory leaks.

**Solutions**:

1. Properly clean up sandbox resources
2. Use weak references
3. Implement automatic cleanup

```typescript
// Properly clean up sandbox resources
function cleanupSandbox(sandbox) {
  // Clean up global variables
  sandbox.clearGlobalValues();
  
  // Clean up event listeners
  sandbox.clearEventListeners();
  
  // Clean up timers
  sandbox.clearTimers();
  
  // Clean up DOM references
  sandbox.clearDOMReferences();
  
  // Deactivate sandbox
  sandbox.deactivate();
}

// Use weak references
const sandboxWeakMap = new WeakMap();

function createSandbox(name) {
  const sandbox = new Sandbox({ name });
  
  // Use weak reference to store sandbox
  sandboxWeakMap.set(sandbox, {
    name,
    createdAt: Date.now()
  });
  
  return sandbox;
}

// Implement automatic cleanup
class SandboxAutoCleanup {
  constructor() {
    this.sandboxes = new Set();
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute
  }
  
  addSandbox(sandbox) {
    this.sandboxes.add(sandbox);
  }
  
  removeSandbox(sandbox) {
    this.sandboxes.delete(sandbox);
    cleanupSandbox(sandbox);
  }
  
  cleanup() {
    this.sandboxes.forEach(sandbox => {
      // Check if sandbox is still active
      if (!sandbox.isActive()) {
        this.removeSandbox(sandbox);
      }
    });
  }
  
  destroy() {
    // Clean up all sandboxes
    this.sandboxes.forEach(sandbox => {
      cleanupSandbox(sandbox);
    });
    this.sandboxes.clear();
    
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}

// Use automatic cleanup
const autoCleanup = new SandboxAutoCleanup();

// Add sandbox to auto cleanup
const sandbox = new Sandbox({ name: 'app1' });
autoCleanup.addSandbox(sandbox);
```

### Performance Issues

**Issue**: Sandbox operations are slow, affecting application performance.

**Solutions**:

1. Use sandbox pooling
2. Optimize sandbox configuration
3. Implement lazy loading

```typescript
// Use sandbox pooling
class SandboxPool {
  constructor(options = {}) {
    this.maxSize = options.maxSize || 10;
    this.pool = [];
    this.activeCount = 0;
  }
  
  acquire(name) {
    let sandbox;
    
    if (this.pool.length > 0) {
      // Reuse existing sandbox
      sandbox = this.pool.pop();
      sandbox.reset();
      sandbox.setName(name);
    } else {
      // Create new sandbox
      sandbox = new Sandbox({ name });
    }
    
    this.activeCount++;
    return sandbox;
  }
  
  release(sandbox) {
    if (this.pool.length < this.maxSize) {
      // Return to pool
      sandbox.cleanup();
      this.pool.push(sandbox);
    } else {
      // Destroy sandbox
      sandbox.destroy();
    }
    
    this.activeCount--;
  }
  
  destroy() {
    // Destroy all sandboxes in pool
    this.pool.forEach(sandbox => {
      sandbox.destroy();
    });
    this.pool = [];
    this.activeCount = 0;
  }
}

// Use sandbox pool
const sandboxPool = new SandboxPool({ maxSize: 5 });

function createApp(name) {
  const sandbox = sandboxPool.acquire(name);
  
  return {
    sandbox,
    destroy() {
      sandboxPool.release(sandbox);
    }
  };
}

// Optimize sandbox configuration
const optimizedSandbox = new Sandbox({
  name: 'app1',
  // Disable unnecessary features
  isolateWindowEvents: false,
  isolateTimers: false,
  // Use lightweight proxy
  useProxy: 'lightweight',
  // Enable caching
  enableCache: true
});

// Implement lazy loading
class LazySandbox {
  constructor(options) {
    this.options = options;
    this._sandbox = null;
  }
  
  get sandbox() {
    if (!this._sandbox) {
      this._sandbox = new Sandbox(this.options);
    }
    return this._sandbox;
  }
  
  activate() {
    return this.sandbox.activate();
  }
  
  deactivate() {
    if (this._sandbox) {
      this._sandbox.deactivate();
    }
  }
  
  destroy() {
    if (this._sandbox) {
      this._sandbox.destroy();
      this._sandbox = null;
    }
  }
}

// Use lazy sandbox
const lazySandbox = new LazySandbox({ name: 'app1' });

// Sandbox is created only when first accessed
lazySandbox.activate();
```

## Summary

The Micro-Core Sandbox API provides comprehensive isolation capabilities for micro-frontend applications, including JavaScript execution environment isolation, style isolation, and storage isolation. Through proper configuration and usage, you can effectively avoid conflicts between micro-applications and ensure system stability and security.

Key features include:

1. **JavaScript Sandbox**: Isolate global variables, functions, and execution context
2. **Style Sandbox**: Isolate CSS styles to prevent style conflicts
3. **Storage Sandbox**: Isolate localStorage and sessionStorage
4. **Multi-Sandbox Management**: Support managing multiple sandbox instances
5. **Sandbox Snapshots**: Support creating and restoring sandbox state snapshots
6. **Plugin System**: Support extending sandbox functionality through plugins
7. **Middleware System**: Support intercepting and processing sandbox operations through middleware
8. **Framework Integration**: Seamless integration with qiankun, wujie and other micro-frontend frameworks

By properly using these features, you can build stable, secure, and high-performance micro-frontend applications.
addStyle(cssText: string): HTMLStyleElement
```

**Parameters:**
- `cssText` (string): CSS style text

**Return Value:** Created style element

**Example:**

```typescript
// Add styles
const styleElement = styleSandbox.addStyle(`
  .title {
    color: red;
    font-size: 20px;
  }
  
  #container {
    padding: 10px;
    margin: 20px;
  }
  
  body {
    background-color: #f0f0f0;
  }
`);

// Add styles with comments
styleSandbox.addStyle(`
  /* Title styles */
  .title {
    color: blue;
    font-weight: bold;
  }
  
  /* Container styles */
  .container {
    border: 1px solid #ccc;
    border-radius: 4px;
  }
`);

// Add media queries
styleSandbox.addStyle(`
  @media (max-width: 768px) {
    .title {
      font-size: 16px;
    }
    
    .container {
      padding: 5px;
    }
  }
`);

// Add keyframe animations
styleSandbox.addStyle(`
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .animated {
    animation: fadeIn 1s ease-in-out;
  }
`);
```

#### addStylesheet(url)

Add external stylesheet.

```typescript
addStylesheet(url: string): Promise<HTMLLinkElement>
```

**Parameters:**
- `url` (string): Stylesheet URL

**Return Value:** Returns a Promise that resolves to the created link element

**Example:**

```typescript
// Add external stylesheet
styleSandbox.addStylesheet('https://example.com/app1/style.css')
  .then(linkElement => {
    console.log('Stylesheet loaded successfully:', linkElement);
  })
  .catch(error => {
    console.error('Stylesheet loading failed:', error);
  });

// Add multiple stylesheets
Promise.all([
  styleSandbox.addStylesheet('https://example.com/app1/base.css'),
  styleSandbox.addStylesheet('https://example.com/app1/theme.css'),
  styleSandbox.addStylesheet('https://example.com/app1/components.css')
])
  .then(linkElements => {
    console.log('All stylesheets loaded successfully');
  })
  .catch(error => {
    console.error('Stylesheet loading failed:', error);
  });
```

#### removeStyle(styleElement)

Remove style element.

```typescript
removeStyle(styleElement: HTMLStyleElement | HTMLLinkElement): boolean
```

**Parameters:**
- `styleElement` (HTMLStyleElement | HTMLLinkElement): Style element

**Return Value:** Whether removal was successful

**Example:**

```typescript
// Add style and save reference
const styleElement = styleSandbox.addStyle(`
  .title {
    color: red;
  }
`);

// Remove style
const removed = styleSandbox.removeStyle(styleElement);
console.log('Removal successful:', removed);

// Add stylesheet and save reference
styleSandbox.addStylesheet('https://example.com/app1/style.css')
  .then(linkElement => {
    // Remove stylesheet
    const removed = styleSandbox.removeStyle(linkElement);
    console.log('Removal successful:', removed);
  });
```

#### removeAllStyles()

Remove all styles.

```typescript
removeAllStyles(): void
```

**Example:**

```typescript
// Add multiple styles
styleSandbox.addStyle(`
  .title {
    color: red;
  }
`);

styleSandbox.addStyle(`
  .container {
    padding: 10px;
  }
`);

styleSandbox.addStylesheet('https://example.com/app1/style.css');

// Remove all styles
styleSandbox.removeAllStyles();

// Clean up styles when component unmounts
function unmountComponent() {
  // Remove all styles
  styleSandbox.removeAllStyles();
  
  // Deactivate style sandbox
  styleSandbox.deactivate();
}
```

#### scopeCssText(cssText)

Apply scope isolation to CSS text.

```typescript
scopeCssText(cssText: string): string
```

**Parameters:**
- `cssText` (string): CSS text

**Return Value:** Processed CSS text

**Example:**

```typescript
// Original CSS
const originalCss = `
  .title {
    color: red;
    font-size: 20px;
  }
  
  #container {
    padding: 10px;
    margin: 20px;
  }
  
  body {
    background-color: #f0f0f0;
  }
`;

// Scope processing
const scopedCss = styleSandbox.scopeCssText(originalCss);
console.log('Scoped CSS:', scopedCss);

// Manually add to document
const style = document.createElement('style');
style.textContent = scopedCss;
document.head.appendChild(style);
```

#### isActive()

Check if style sandbox is active.

```typescript
isActive(): boolean
```

**Return Value:** Whether style sandbox is active

**Example:**

```typescript
// Check style sandbox status
const isActive = styleSandbox.isActive();
console.log('Style sandbox active:', isActive);

// Conditional activation
if (!styleSandbox.isActive()) {
  console.log('Style sandbox not active, activating...');
  styleSandbox.activate();
}

// Conditional style addition
if (styleSandbox.isActive()) {
  styleSandbox.addStyle(`
    .title {
      color: red;
    }
  `);
} else {
  console.warn('Style sandbox not active, cannot add styles');
}
```

## StorageSandbox

`StorageSandbox` is the storage sandbox class provided by Micro-Core for isolating micro-application localStorage and sessionStorage.

### Basic Usage

```typescript
import { StorageSandbox } from '@micro-core/core';

// Create storage sandbox instance
const storageSandbox = new StorageSandbox({
  name: 'app1',
  type: 'localStorage',
  prefix: 'app1:'
});

// Activate storage sandbox
storageSandbox.activate();

// Set storage item
storageSandbox.setItem('user', JSON.stringify({ id: 1, name: 'Admin' }));

// Get storage item
const user = JSON.parse(storageSandbox.getItem('user'));
console.log('User:', user);

// Remove storage item
storageSandbox.removeItem('user');

// Clear all storage items
storageSandbox.clear();

// Deactivate storage sandbox
storageSandbox.deactivate();
```

### API Reference

#### Constructor

Create storage sandbox instance.

```typescript
constructor(options?: StorageSandboxOptions)
```

**Parameters:**
- `options` (StorageSandboxOptions): Optional, storage sandbox configuration options
  - `name` (string): Sandbox name
  - `type` (string): Storage type, 'localStorage' or 'sessionStorage', defaults to 'localStorage'
  - `prefix` (string): Key prefix, defaults to '{name}:'
  - `useProxy` (boolean): Whether to use proxy, defaults to true
  - `initialData` (Record<string, string>): Initial data

**Example:**

```typescript
// Basic usage
const storageSandbox = new StorageSandbox();

// localStorage sandbox
const localStorageSandbox = new StorageSandbox({
  name: 'app1',
  type: 'localStorage',
  prefix: 'app1:'
});

// sessionStorage sandbox
const sessionStorageSandbox = new StorageSandbox({
  name: 'app1',
  type: 'sessionStorage',
  prefix: 'app1:'
});

// Sandbox with initial data
const storageSandbox = new StorageSandbox({
  name: 'app1',
  type: 'localStorage',
  prefix: 'app1:',
  initialData: {
    'theme': 'light',
    'language': 'zh-CN',
    'user': JSON.stringify({ id: 1, name: 'Admin' })
  }
});
```

#### activate()

Activate storage sandbox.

```typescript
activate(): void
```

**Example:**

```typescript
// Activate storage sandbox
storageSandbox.activate();

// Activate storage sandbox when component mounts
function mountComponent() {
  // Activate storage sandbox
  storageSandbox.activate();
  
  // Render component
  renderComponent();
}
```

#### deactivate()

Deactivate storage sandbox.

```typescript
deactivate(): void
```

**Example:**

```typescript
// Deactivate storage sandbox
storageSandbox.deactivate();

// Deactivate storage sandbox when component unmounts
function unmountComponent() {
  // Deactivate storage sandbox
  storageSandbox.deactivate();
  
  // Clean up resources
  cleanupResources();
}
```

#### getItem(key)

Get storage item.

```typescript
getItem(key: string): string | null
```

**Parameters:**
- `key` (string): Key name

**Return Value:** Storage item value, returns null if not exists

**Example:**

```typescript
// Get simple value
const theme = storageSandbox.getItem('theme');
console.log('Theme:', theme);

// Get JSON value
const userJson = storageSandbox.getItem('user');
if (userJson) {
  const user = JSON.parse(userJson);
  console.log('User:', user);
}

// Check if item exists
const language = storageSandbox.getItem('language');
if (language) {
  console.log('Language:', language);
} else {
  console.log('Language not set');
}
```

#### setItem(key, value)

Set storage item.

```typescript
setItem(key: string, value: string): void
```

**Parameters:**
- `key` (string): Key name
- `value` (string): Value

**Example:**

```typescript
// Set simple values
storageSandbox.setItem('theme', 'dark');
storageSandbox.setItem('language', 'zh-CN');

// Set JSON values
const user = { id: 1, name: 'Admin', role: 'admin' };
storageSandbox.setItem('user', JSON.stringify(user));

// Set arrays
const permissions = ['read', 'write', 'delete'];
storageSandbox.setItem('permissions', JSON.stringify(permissions));

// Set values with expiry time
function setWithExpiry(key, value, ttl) {
  const item = {
    value: value,
    expiry: Date.now() + ttl
  };
  storageSandbox.setItem(key, JSON.stringify(item));
}

// Set value that expires in 1 hour
setWithExpiry('token', 'abc123', 3600000);

// Get values with expiry time
function getWithExpiry(key) {
  const itemStr = storageSandbox.getItem(key);
  if (!itemStr) {
    return null;
  }
  
  const item = JSON.parse(itemStr);
  if (Date.now() > item.expiry) {
    storageSandbox.removeItem(key);
    return null;
  }
  
  return item.value;
}

// Get token
const token = getWithExpiry('token');
console.log('Token:', token);
```

#### removeItem(key)

Remove storage item.

```typescript
removeItem(key: string): void
```

**Parameters:**
- `key` (string): Key name

**Example:**

```typescript
// Remove single item
storageSandbox.removeItem('theme');

// Conditional removal
const user = JSON.parse(storageSandbox.getItem('user') || '{}');
if (user.role === 'guest') {
  storageSandbox.removeItem('user');
}

// Remove expired items
function removeExpiredItems() {
  const keys = storageSandbox.keys();
  
  keys.forEach(key => {
    if (key.endsWith('_expiry')) {
      const expiryStr = storageSandbox.getItem(key);
      if (expiryStr) {
        const expiry = parseInt(expiryStr, 10);
        if (Date.now() > expiry) {
          // Remove expired item and expiry marker
          const actualKey = key.replace('_expiry', '');
          storageSandbox.removeItem(actualKey);
          storageSandbox.removeItem(key);
        }
      }
    }
  });
}
```

#### clear()

Clear all storage items.

```typescript
clear(): void
```

**Example:**

```typescript
// Clear all items
storageSandbox.clear();

// Clear when user logs out
function logout() {
  // Clear user-related storage
  storageSandbox.clear();
  
  // Redirect to login page
  window.location.href = '/login';
}
```

#### key(index)

Get key name at specified index.

```typescript
key(index: number): string | null
```

**Parameters:**
- `index` (number): Index

**Return Value:** Key name, returns null if not exists

**Example:**

```typescript
// Get first key
const firstKey = storageSandbox.key(0);
console.log('First key:', firstKey);

// Iterate through all keys
for (let i = 0; i < storageSandbox.length; i++) {
  const key = storageSandbox.key(i);
  if (key) {
    const value = storageSandbox.getItem(key);
    console.log(`${key}: ${value}`);
  }
}
```

#### keys()

Get all key names.

```typescript
keys(): string[]
```

**Return Value:** Array of key names

**Example:**

```typescript
// Get all keys
const keys = storageSandbox.keys();
console.log('All keys:', keys);

// Iterate through all key-value pairs
keys.forEach(key => {
  const value = storageSandbox.getItem(key);
  console.log(`${key}: ${value}`);
});

// Filter keys
const userKeys = keys.filter(key => key.startsWith('user.'));
console.log('User-related keys:', userKeys);
```

#### has(key)

Check if specified key exists.

```typescript
has(key: string): boolean
```

**Parameters:**
- `key` (string): Key name

**Return Value:** Whether exists

**Example:**

```typescript
// Check if key exists
const hasTheme = storageSandbox.has('theme');
console.log('Has theme setting:', hasTheme);

// Conditional operations
if (storageSandbox.has('user')) {
  const user = JSON.parse(storageSandbox.getItem('user') || '{}');
  console.log('User:', user);
} else {
  console.log('Not logged in');
}
```

#### size()

Get number of storage items.

```typescript
size(): number
```

**Return Value:** Number of storage items

**Example:**

```typescript
// Get number of storage items
const count = storageSandbox.size();
console.log('Number of storage items:', count);

// Check if empty
if (storageSandbox.size() === 0) {
  console.log('Storage is empty');
} else {
  console.log(`Storage contains ${storageSandbox.size()} items`);
}
```

#### getStorage()

Get original storage object.

```typescript
getStorage(): Storage
```

**Return Value:** Original storage object

**Example:**

```typescript
// Get original storage object
const storage = storageSandbox.getStorage();

// Directly operate on original storage
storage.setItem('global-key', 'global-value');

// Check value in original storage
console.log('Value in original storage:', storage.getItem('global-key'));
```

#### getProxyStorage()

Get proxy storage object.

```typescript
getProxyStorage(): Storage
```

**Return Value:** Proxy storage object

**Example:**

```typescript
// Get proxy storage object
const proxyStorage = storageSandbox.getProxyStorage();

// Use proxy storage
proxyStorage.setItem('key', 'value');
const value = proxyStorage.getItem('key');
console.log('Value:', value);

// Execute code in sandbox
sandbox.execScript(`
  // Use sandbox storage
  localStorage.setItem('app-key', 'app-value');
  console.log('App value:', localStorage.getItem('app-key'));
`);
```

## Advanced Features

### Multi-Sandbox Management

The Micro-Core sandbox system supports management of multiple sandbox instances for isolating multiple micro-applications.

```typescript
import { SandboxManager } from '@micro-core/core';

// Create sandbox manager
const sandboxManager = new SandboxManager();

// Create sandboxes
const app1Sandbox = sandboxManager.createSandbox({
  name: 'app1',
  el: '#app1-container'
});

const app2Sandbox = sandboxManager.createSandbox({
  name: 'app2',
  el: '#app2-container'
});

// Get sandbox
const sandbox = sandboxManager.getSandbox('app1');

// Activate sandbox
sandboxManager.activateSandbox('app1');

// Deactivate sandbox
sandboxManager.deactivateSandbox('app1');

// Remove sandbox
sandboxManager.removeSandbox('app1');

// Clear all sandboxes
sandboxManager.clear();
```

### Sandbox Snapshots

The Micro-Core sandbox system supports creating and restoring sandbox state snapshots for saving and restoring sandbox states.

```typescript
// Create sandbox
const sandbox = new Sandbox({
  name: 'app1'
});

// Activate sandbox
sandbox.activate();

// Set initial state
sandbox.setGlobalValue('counter', 0);
sandbox.setGlobalValue('theme', 'light');

// Create snapshot
const snapshot = sandbox.snapshot();

// Modify state
sandbox.setGlobalValue('counter', 10);
sandbox.setGlobalValue('theme', 'dark');
sandbox.setGlobalValue('newValue', 'added after snapshot');

// Restore from snapshot
sandbox.restore(snapshot);

// Verify restoration
console.log('Counter after restore:', sandbox.getGlobalValue('counter')); // Output: Counter after restore: 0
console.log('Theme after restore:', sandbox.getGlobalValue('theme')); // Output: Theme after restore: light
console.log('NewValue after restore:', sandbox.getGlobalValue('newValue')); // Output: NewValue after restore: undefined
```

### Sandbox Plugins

The Micro-Core sandbox system supports a plugin mechanism for extending sandbox functionality.

```typescript
// Define sandbox plugin
const loggingPlugin = {
  name: 'logging',
  
  // Called when sandbox is created
  onCreate(sandbox) {
    console.log(`Sandbox ${sandbox.name} created`);
  },
  
  // Called when sandbox is activated
  onActivate(sandbox) {
    console.log(`Sandbox ${sandbox.name} activated`);
  },
  
  // Called when sandbox is deactivated
  onDeactivate(sandbox) {
    console.log(`Sandbox ${sandbox.name} deactivated`);
  },
  
  // Called when sandbox is destroyed
  onDestroy(sandbox) {
    console.log(`Sandbox ${sandbox.name} destroyed`);
  },
  
  // Called before executing code
  beforeExecScript(code, sandbox) {
    console.log(`Before executing code in sandbox ${sandbox.name}, code length: ${code.length}`);
    return code; // Can modify and return code
  },
  
  // Called after executing code
  afterExecScript(result, sandbox) {
    console.log(`After executing code in sandbox ${sandbox.name}`);
    return result; // Can modify and return result
  }
};

// Register plugin
Sandbox.registerPlugin(loggingPlugin);

// Create sandbox
const sandbox = new Sandbox({
  name: 'app1',
  plugins: ['logging'] // Enable plugin
});
```

### Sandbox Middleware

The Micro-Core sandbox system supports a middleware mechanism for intercepting and processing sandbox operations.

```typescript
// Create sandbox
const sandbox = new Sandbox({
  name: 'app1'
});

// Add global variable access middleware
sandbox.use((context, next) => {
  const { type, key, value } = context;
  
  if (type === 'get') {
    console.log(`Getting global variable: ${key}`);
  } else if (type === 'set') {
    console.log(`Setting global variable: ${key} = ${value}`);
  }
  
  return next();
});

// Add code execution middleware
sandbox.use((context, next) => {
  if (context.type === 'execScript') {
    console.log(`Executing code, length: ${context.code.length}`);
    
    const startTime = Date.now();
    const result = next();
    const endTime = Date.now();
    
    console.log(`Code execution completed, time: ${endTime - startTime}ms`);
    
    return result;
  }
  
  return next();
});

// Add error handling middleware
sandbox.use((context, next) => {
  try {
    return next();
  } catch (error) {
    console.error(`Sandbox operation error: ${error.message}`);
    throw error;
  }
});
```

## Integration with Other Frameworks

### Integration with qiankun

```typescript
import { registerMicroApps, start } from 'qiankun';
import { Sandbox } from '@micro-core/core';

// Create sandbox
const sandbox = new Sandbox({
  name: 'micro-app',
  el: '#micro-container'
});

// Register micro-application
registerMicroApps([
  {
    name: 'micro-app',
    entry: '//localhost:8081',
    container: '#micro-container',
    activeRule: '/micro-app',
    props: {
      sandbox // Pass sandbox to micro-application
    }
  }
]);

// Start qiankun
start({
  sandbox: {
    // Use custom sandbox
    ...sandbox.getQiankunOptions()
  }
});
```

### Integration with wujie

```typescript
import { bus, setupApp, preloadApp, startApp } from 'wujie';
import { Sandbox } from '@micro-core/core';

// Create sandbox
const sandbox = new Sandbox({
  name: 'micro-app',
  el: '#micro-container'
});

// Setup micro-application
setupApp({
  name: 'micro-app',
  url: '//localhost:8081',
  exec: true,
  props: {
    sandbox // Pass sandbox to micro-application
  },
  beforeLoad: (appWindow) => {
    // Activate sandbox
    sandbox.activate();
    
    // Integrate sandbox proxy window object with wujie's appWindow
    Object.assign(appWindow, sandbox.getProxyWindow());
  },
  beforeUnmount: () => {
    // Deactivate sandbox
    sandbox.deactivate();
  }
});

// Start micro-application
startApp({
  name: 'micro-app',
  container: '#micro-container',
  url: '//localhost:8081'
});
```

## Common Issues and Solutions

### Global Variable Conflicts

**Issue**: Multiple micro-applications use the same global variable names, causing conflicts.

**Solutions**:

1. Enable strict isolation mode
2. Use custom global variables
3. Implement variable namespacing

```typescript
// Enable strict isolation mode
const sandbox = new Sandbox({
  name: 'app1',
  strictIsolation: true
});

// Use custom global variables
const sandbox = new Sandbox({
  name: 'app1',
  customGlobals: {
    // Application-specific global variables
    appName: 'App1',
    appVersion: '1.0.0',
    
    // Override potentially conflicting variables
    jQuery: customJQuery,
    $: customJQuery
  }
});

// Implement variable namespacing
sandbox.execScript(`
  // Create application namespace
  window.APP1 = {};
  
  // Define variables in namespace
  APP1.config = {
    theme: 'light',
    language: 'zh-CN'
  };
  
  // Define functions in namespace
  APP1.init = function() {
    console.log('App1 initialization');
  };
`);
```

### Style Conflicts

**Issue**: CSS styles from multiple micro-applications affect each other.

**Solutions**:

1. Enable style isolation
2. Use CSS namespacing
3. Use CSS Modules or CSS-in-JS

```typescript