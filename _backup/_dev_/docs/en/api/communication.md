# Communication API

Micro-Core provides multiple communication patterns for inter-application communication and data sharing.

## Event Bus

### Basic Usage

```typescript
import { eventBus } from '@micro-core/core';

// Emit event
eventBus.emit('user:login', { userId: 123, name: '<PERSON>' });

// Listen to event
const unsubscribe = eventBus.on('user:login', (data) => {
  console.log('User logged in:', data);
});

// Listen once
eventBus.once('app:ready', () => {
  console.log('App is ready');
});

// Remove listener
unsubscribe();
```

### Event Patterns

#### Wildcard Events

```typescript
// Listen to all user events
eventBus.on('user:*', (eventName, data) => {
  console.log(`User event: ${eventName}`, data);
});

// Listen to all events
eventBus.on('*', (eventName, data) => {
  console.log(`Event: ${eventName}`, data);
});
```

#### Namespaced Events

```typescript
// App-specific events
eventBus.on('app1:data:updated', (data) => {
  console.log('App1 data updated:', data);
});

eventBus.on('app2:status:changed', (status) => {
  console.log('App2 status changed:', status);
});
```

## Global State Communication

### Shared State

```typescript
import { globalState } from '@micro-core/core';

// Set shared data
globalState.set('currentUser', {
  id: 123,
  name: 'John',
  role: 'admin'
});

// Get shared data
const user = globalState.get('currentUser');

// Watch for changes
const unwatch = globalState.watch('currentUser', (newUser, oldUser) => {
  console.log('User changed:', { newUser, oldUser });
});
```

### State Synchronization

```typescript
// Sync state between apps
globalState.sync('sharedData', {
  apps: ['app1', 'app2'],
  bidirectional: true
});

// Manual sync
globalState.syncTo('app1', 'userData');
globalState.syncFrom('app2', 'settings');
```

## Props Communication

### Parent to Child

```typescript
// Register app with props
await microCore.registerApp({
  name: 'child-app',
  entry: 'http://localhost:3001',
  container: '#child-container',
  activeRule: '/child',
  props: {
    theme: 'dark',
    apiUrl: 'https://api.example.com',
    permissions: ['read', 'write']
  }
});

// Dynamic props
await microCore.updateAppProps('child-app', {
  theme: 'light',
  newFeature: true
});
```

### Props in Child App

```typescript
// In child application
export async function mount(props) {
  const { container, theme, apiUrl, permissions } = props;
  
  // Use props
  console.log('Received props:', { theme, apiUrl, permissions });
  
  // Initialize app with props
  initializeApp({
    theme,
    apiUrl,
    permissions
  });
}
```

## Custom Communication Channels

### Creating Channels

```typescript
import { createChannel } from '@micro-core/core';

// Create direct channel between two apps
const channel = createChannel('app1-app2', {
  type: 'direct',
  apps: ['app1', 'app2'],
  encryption: true
});

// Send message
channel.send('app2', 'hello', { message: 'Hello from app1' });

// Receive message
channel.on('message', (from, type, data) => {
  console.log(`Message from ${from}:`, { type, data });
});
```

### Broadcast Channel

```typescript
// Create broadcast channel
const broadcast = createChannel('notifications', {
  type: 'broadcast',
  apps: '*' // All apps
});

// Broadcast message
broadcast.broadcast('notification', {
  type: 'info',
  message: 'System maintenance in 5 minutes'
});

// Listen to broadcasts
broadcast.on('notification', (data) => {
  showNotification(data);
});
```

## Message Queue

### Queue Configuration

```typescript
import { MessageQueue } from '@micro-core/core';

const queue = new MessageQueue({
  maxSize: 1000,
  persistence: true,
  storage: 'localStorage'
});

// Send message to queue
queue.push('user-actions', {
  action: 'click',
  element: 'button',
  timestamp: Date.now()
});

// Process queue
queue.process('user-actions', (message) => {
  console.log('Processing action:', message);
  // Process the action
  return processUserAction(message);
});
```

### Queue Events

```typescript
// Queue events
queue.on('message:added', (queueName, message) => {
  console.log(`Message added to ${queueName}:`, message);
});

queue.on('message:processed', (queueName, message, result) => {
  console.log(`Message processed in ${queueName}:`, { message, result });
});

queue.on('queue:full', (queueName) => {
  console.warn(`Queue ${queueName} is full`);
});
```

## Request-Response Pattern

### Service Communication

```typescript
import { createService } from '@micro-core/core';

// Create service
const userService = createService('user-service', {
  async getUser(id) {
    const response = await fetch(`/api/users/${id}`);
    return response.json();
  },
  
  async updateUser(id, data) {
    const response = await fetch(`/api/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
      headers: { 'Content-Type': 'application/json' }
    });
    return response.json();
  }
});

// Use service from another app
const user = await microCore.callService('user-service', 'getUser', 123);
const updated = await microCore.callService('user-service', 'updateUser', 123, {
  name: 'Updated Name'
});
```

### RPC Communication

```typescript
import { createRPC } from '@micro-core/core';

// Create RPC endpoint
const rpc = createRPC('data-api', {
  async fetchData(params) {
    // Fetch data logic
    return await dataService.fetch(params);
  },
  
  async saveData(data) {
    // Save data logic
    return await dataService.save(data);
  }
});

// Call RPC from another app
const data = await microCore.rpc('data-api', 'fetchData', { 
  type: 'users', 
  limit: 10 
});

const result = await microCore.rpc('data-api', 'saveData', {
  id: 1,
  name: 'John'
});
```

## WebSocket Communication

### Real-time Communication

```typescript
import { WebSocketManager } from '@micro-core/core';

const wsManager = new WebSocketManager({
  url: 'ws://localhost:8080',
  reconnect: true,
  heartbeat: true
});

// Connect
await wsManager.connect();

// Send message
wsManager.send('chat:message', {
  room: 'general',
  message: 'Hello everyone!'
});

// Listen to messages
wsManager.on('chat:message', (data) => {
  displayChatMessage(data);
});

// Handle connection events
wsManager.on('connected', () => {
  console.log('WebSocket connected');
});

wsManager.on('disconnected', () => {
  console.log('WebSocket disconnected');
});
```

### Room-based Communication

```typescript
// Join room
wsManager.join('room1');

// Send to room
wsManager.sendToRoom('room1', 'user:joined', {
  userId: 123,
  name: 'John'
});

// Listen to room messages
wsManager.onRoom('room1', 'user:joined', (data) => {
  console.log('User joined room:', data);
});

// Leave room
wsManager.leave('room1');
```

## Communication Security

### Message Encryption

```typescript
import { SecureCommunication } from '@micro-core/core';

const secure = new SecureCommunication({
  encryption: 'AES-256',
  keyExchange: 'ECDH'
});

// Send encrypted message
secure.sendEncrypted('app2', 'sensitive-data', {
  creditCard: '1234-5678-9012-3456',
  ssn: '***********'
});

// Receive encrypted message
secure.onEncrypted('sensitive-data', (data) => {
  console.log('Decrypted data:', data);
});
```

### Message Authentication

```typescript
// Sign message
const signedMessage = secure.sign('important-message', {
  action: 'transfer',
  amount: 1000
});

// Verify message
secure.onSigned('important-message', (data, signature) => {
  if (secure.verify(data, signature)) {
    console.log('Message verified:', data);
    processImportantAction(data);
  } else {
    console.error('Message verification failed');
  }
});
```

## Communication Middleware

### Message Middleware

```typescript
import { CommunicationMiddleware } from '@micro-core/core';

// Create logging middleware
const loggingMiddleware = new CommunicationMiddleware({
  name: 'logging',
  before: (message, context) => {
    console.log('Sending message:', message);
  },
  after: (message, context, result) => {
    console.log('Message sent:', { message, result });
  }
});

// Create validation middleware
const validationMiddleware = new CommunicationMiddleware({
  name: 'validation',
  before: (message, context) => {
    if (!message.type) {
      throw new Error('Message type is required');
    }
    if (!message.data) {
      throw new Error('Message data is required');
    }
  }
});

// Apply middleware
microCore.useCommunicationMiddleware([
  loggingMiddleware,
  validationMiddleware
]);
```

## Performance Optimization

### Message Batching

```typescript
import { MessageBatcher } from '@micro-core/core';

const batcher = new MessageBatcher({
  maxSize: 10,
  maxWait: 100, // 100ms
  flush: (messages) => {
    // Send batched messages
    eventBus.emit('batch:messages', messages);
  }
});

// Add messages to batch
batcher.add('user:action', { action: 'click' });
batcher.add('user:action', { action: 'scroll' });
batcher.add('user:action', { action: 'hover' });

// Messages will be automatically flushed when batch is full or timeout
```

### Message Compression

```typescript
import { MessageCompressor } from '@micro-core/core';

const compressor = new MessageCompressor({
  algorithm: 'gzip',
  threshold: 1024 // Compress messages larger than 1KB
});

// Compress large messages automatically
eventBus.use(compressor);

// Send large data
eventBus.emit('large:data', {
  dataset: largeDataArray, // Will be compressed automatically
  metadata: { size: largeDataArray.length }
});
```

## Error Handling

### Communication Errors

```typescript
// Handle communication errors
eventBus.on('error', (error, eventName, data) => {
  console.error(`Communication error for ${eventName}:`, error);
  
  // Retry logic
  if (error.code === 'NETWORK_ERROR') {
    setTimeout(() => {
      eventBus.emit(eventName, data);
    }, 1000);
  }
});

// Handle timeout errors
eventBus.setTimeout(5000); // 5 second timeout

eventBus.on('timeout', (eventName, data) => {
  console.warn(`Communication timeout for ${eventName}`);
  showTimeoutError(eventName);
});
```

### Fallback Communication

```typescript
// Setup fallback communication
const communication = new CommunicationManager({
  primary: 'websocket',
  fallbacks: ['sse', 'polling'],
  fallbackDelay: 3000
});

// Automatic fallback on failure
communication.on('fallback', (from, to) => {
  console.log(`Communication fallback: ${from} -> ${to}`);
});
```

## Best Practices

### 1. Event Naming

```typescript
// Good - Clear, hierarchical naming
eventBus.emit('user:profile:updated', userData);
eventBus.emit('app:navigation:changed', routeData);
eventBus.emit('system:error:occurred', errorData);

// Avoid - Generic or unclear names
eventBus.emit('update', userData);
eventBus.emit('change', routeData);
eventBus.emit('error', errorData);
```

### 2. Data Structure

```typescript
// Good - Consistent data structure
const eventData = {
  type: 'user:login',
  payload: { userId: 123, name: 'John' },
  timestamp: Date.now(),
  source: 'auth-app'
};

eventBus.emit('user:login', eventData);
```

### 3. Error Handling

```typescript
// Robust error handling
try {
  await microCore.callService('user-service', 'getUser', userId);
} catch (error) {
  if (error.code === 'SERVICE_UNAVAILABLE') {
    // Use cached data
    return getCachedUser(userId);
  } else if (error.code === 'UNAUTHORIZED') {
    // Redirect to login
    redirectToLogin();
  } else {
    // Log and show generic error
    console.error('Service call failed:', error);
    showErrorMessage('Failed to load user data');
  }
}
```

### 4. Memory Management

```typescript
// Clean up listeners
const listeners = [];

// Add listeners
listeners.push(eventBus.on('user:login', handleUserLogin));
listeners.push(eventBus.on('user:logout', handleUserLogout));

// Cleanup when component unmounts
function cleanup() {
  listeners.forEach(unsubscribe => unsubscribe());
  listeners.length = 0;
}
```

## Type Definitions

```typescript
interface EventBus {
  emit(eventName: string, data?: any): void;
  on(eventName: string, handler: EventHandler): () => void;
  once(eventName: string, handler: EventHandler): () => void;
  off(eventName: string, handler?: EventHandler): void;
}

interface EventHandler {
  (data?: any): void;
}

interface CommunicationChannel {
  send(target: string, type: string, data: any): Promise<void>;
  broadcast(type: string, data: any): Promise<void>;
  on(type: string, handler: MessageHandler): () => void;
}

interface MessageHandler {
  (from: string, type: string, data: any): void;
}

interface ServiceDefinition {
  [methodName: string]: (...args: any[]) => Promise<any>;
}

interface RPCEndpoint {
  [methodName: string]: (...args: any[]) => Promise<any>;
}
```

## References

- [Core API](./core.md)
- [Event Bus API](./event-bus.md)
- [State Management API](./state-management.md)
- [Communication Guide](../guide/communication.md)