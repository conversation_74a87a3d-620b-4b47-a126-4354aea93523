# State Management API

Micro-Core provides a comprehensive state management system for sharing data between micro-applications.

## Global State

### GlobalState Class

The global state manager allows sharing data across all micro-applications.

```typescript
import { globalState } from '@micro-core/core';

// Set global state
globalState.set('user', { id: 1, name: '<PERSON>' });

// Get global state
const user = globalState.get('user');

// Watch state changes
const unwatch = globalState.watch('user', (newUser, oldUser) => {
  console.log('User changed:', { newUser, oldUser });
});

// Remove state
globalState.remove('user');

// Clear all state
globalState.clear();
```

## State Methods

### set(key, value)

Set a state value.

```typescript
set<T = any>(key: string, value: T): void
```

**Example:**

```typescript
// Set simple value
globalState.set('theme', 'dark');

// Set object value
globalState.set('user', {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>'
});

// Set nested value
globalState.set('app.settings.language', 'en');
```

### get(key)

Get a state value.

```typescript
get<T = any>(key: string): T | undefined
```

**Example:**

```typescript
// Get simple value
const theme = globalState.get('theme');

// Get object value
const user = globalState.get<User>('user');

// Get nested value
const language = globalState.get('app.settings.language');

// Get with default value
const notifications = globalState.get('notifications') || [];
```

### has(key)

Check if a state key exists.

```typescript
has(key: string): boolean
```

**Example:**

```typescript
if (globalState.has('user')) {
  console.log('User is logged in');
}
```

### remove(key)

Remove a state value.

```typescript
remove(key: string): boolean
```

**Example:**

```typescript
// Remove specific key
globalState.remove('user');

// Remove nested key
globalState.remove('app.settings.language');
```

### clear()

Clear all state.

```typescript
clear(): void
```

### keys()

Get all state keys.

```typescript
keys(): string[]
```

**Example:**

```typescript
const allKeys = globalState.keys();
console.log('All state keys:', allKeys);
```

## State Watching

### watch(key, callback)

Watch for state changes.

```typescript
watch<T = any>(
  key: string, 
  callback: (newValue: T, oldValue: T) => void
): () => void
```

**Example:**

```typescript
// Watch specific key
const unwatch = globalState.watch('user', (newUser, oldUser) => {
  console.log('User updated:', { newUser, oldUser });
});

// Watch nested key
const unwatchTheme = globalState.watch('app.settings.theme', (newTheme) => {
  document.body.className = `theme-${newTheme}`;
});

// Unwatch
unwatch();
unwatchTheme();
```

### watchAll(callback)

Watch all state changes.

```typescript
watchAll(callback: (key: string, newValue: any, oldValue: any) => void): () => void
```

**Example:**

```typescript
const unwatchAll = globalState.watchAll((key, newValue, oldValue) => {
  console.log(`State changed: ${key}`, { newValue, oldValue });
});

// Unwatch all
unwatchAll();
```

## State Persistence

### Configuration

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  globalState: {
    persistence: {
      enabled: true,
      storage: 'localStorage', // 'localStorage' | 'sessionStorage' | 'indexedDB'
      key: 'micro-core-state',
      include: ['user', 'settings'], // Only persist these keys
      exclude: ['temp', 'cache']     // Don't persist these keys
    }
  }
});
```

### Manual Persistence

```typescript
// Save state to storage
globalState.save();

// Load state from storage
globalState.load();

// Save specific keys
globalState.save(['user', 'settings']);

// Load with merge strategy
globalState.load({ merge: true });
```

## State Modules

### Creating State Modules

```typescript
import { createStateModule } from '@micro-core/core';

// Create user state module
const userModule = createStateModule('user', {
  state: {
    id: null,
    name: '',
    email: '',
    isLoggedIn: false
  },
  
  getters: {
    fullName: (state) => `${state.firstName} ${state.lastName}`,
    isAdmin: (state) => state.role === 'admin'
  },
  
  mutations: {
    setUser(state, user) {
      Object.assign(state, user);
      state.isLoggedIn = true;
    },
    
    logout(state) {
      state.id = null;
      state.name = '';
      state.email = '';
      state.isLoggedIn = false;
    }
  },
  
  actions: {
    async login({ commit }, credentials) {
      try {
        const user = await api.login(credentials);
        commit('setUser', user);
        return user;
      } catch (error) {
        throw error;
      }
    },
    
    async fetchProfile({ commit, state }) {
      if (!state.isLoggedIn) return;
      
      const profile = await api.getProfile(state.id);
      commit('setUser', profile);
    }
  }
});

// Register module
globalState.registerModule('user', userModule);
```

### Using State Modules

```typescript
// Access module state
const user = globalState.getModule('user').state;

// Use getters
const fullName = globalState.getModule('user').getters.fullName;

// Commit mutations
globalState.getModule('user').commit('setUser', {
  id: 1,
  name: 'John',
  email: '<EMAIL>'
});

// Dispatch actions
await globalState.getModule('user').dispatch('login', {
  email: '<EMAIL>',
  password: 'password'
});
```

## Application-Level State

### Local State Management

```typescript
// In micro-application
export async function mount(props) {
  const { container, localState } = props;
  
  // Set local state
  localState.set('currentPage', 'dashboard');
  
  // Watch local state
  localState.watch('currentPage', (newPage) => {
    updateNavigation(newPage);
  });
  
  // Share with global state
  localState.sync('user', globalState);
}
```

### State Isolation

```typescript
await microCore.registerApp({
  name: 'isolated-app',
  entry: 'http://localhost:3001',
  container: '#isolated-container',
  activeRule: '/isolated',
  state: {
    isolated: true, // Isolate state from other apps
    namespace: 'isolated-app', // State namespace
    initialState: {
      theme: 'light',
      language: 'en'
    }
  }
});
```

## State Synchronization

### Cross-Application Sync

```typescript
// Sync state between applications
globalState.sync('user', {
  apps: ['app1', 'app2'], // Sync only with specific apps
  bidirectional: true,    // Two-way sync
  debounce: 100          // Debounce sync operations
});

// Manual sync
globalState.syncTo('app1', 'user');
globalState.syncFrom('app2', 'settings');
```

### Real-time Sync

```typescript
// Enable real-time synchronization
const microCore = new MicroCore({
  globalState: {
    realtime: {
      enabled: true,
      transport: 'websocket', // 'websocket' | 'sse' | 'polling'
      url: 'ws://localhost:8080/state-sync',
      reconnect: true
    }
  }
});

// Handle sync events
globalState.on('sync:connected', () => {
  console.log('State sync connected');
});

globalState.on('sync:disconnected', () => {
  console.log('State sync disconnected');
});

globalState.on('sync:error', (error) => {
  console.error('State sync error:', error);
});
```

## State Validation

### Schema Validation

```typescript
import { createStateModule } from '@micro-core/core';

const userModule = createStateModule('user', {
  state: {
    id: null,
    name: '',
    email: ''
  },
  
  schema: {
    id: { type: 'number', required: false },
    name: { type: 'string', required: true, minLength: 1 },
    email: { type: 'string', required: true, format: 'email' }
  },
  
  mutations: {
    setUser(state, user) {
      // Validation happens automatically
      Object.assign(state, user);
    }
  }
});
```

### Custom Validators

```typescript
const settingsModule = createStateModule('settings', {
  state: {
    theme: 'light',
    language: 'en'
  },
  
  validators: {
    theme: (value) => {
      const validThemes = ['light', 'dark', 'auto'];
      if (!validThemes.includes(value)) {
        throw new Error(`Invalid theme: ${value}`);
      }
    },
    
    language: (value) => {
      const supportedLanguages = ['en', 'zh', 'es', 'fr'];
      if (!supportedLanguages.includes(value)) {
        throw new Error(`Unsupported language: ${value}`);
      }
    }
  }
});
```

## State Events

### State Change Events

```typescript
// Listen to state changes
globalState.on('change', (key, newValue, oldValue) => {
  console.log(`State changed: ${key}`, { newValue, oldValue });
});

// Listen to specific key changes
globalState.on('change:user', (newUser, oldUser) => {
  console.log('User changed:', { newUser, oldUser });
});

// Listen to state additions
globalState.on('add', (key, value) => {
  console.log(`State added: ${key}`, value);
});

// Listen to state removals
globalState.on('remove', (key, oldValue) => {
  console.log(`State removed: ${key}`, oldValue);
});
```

## State Middleware

### Creating Middleware

```typescript
// Create logging middleware
const loggingMiddleware = {
  name: 'logging',
  before: (action, state) => {
    console.log(`Before ${action.type}:`, state);
  },
  after: (action, state) => {
    console.log(`After ${action.type}:`, state);
  }
};

// Create validation middleware
const validationMiddleware = {
  name: 'validation',
  before: (action, state) => {
    if (action.type === 'setUser' && !action.payload.email) {
      throw new Error('User email is required');
    }
  }
};

// Apply middleware
globalState.use([loggingMiddleware, validationMiddleware]);
```

## Performance Optimization

### State Batching

```typescript
// Batch multiple state updates
globalState.batch(() => {
  globalState.set('user.name', 'John');
  globalState.set('user.email', '<EMAIL>');
  globalState.set('user.role', 'admin');
});
// Only triggers one change event
```

### Computed State

```typescript
// Create computed state
const computedState = globalState.computed('userDisplayName', () => {
  const user = globalState.get('user');
  return user ? `${user.name} (${user.email})` : 'Guest';
});

// Use computed state
console.log(computedState.value); // "John (<EMAIL>)"

// Computed state updates automatically when dependencies change
globalState.set('user.name', 'Jane');
console.log(computedState.value); // "Jane (<EMAIL>)"
```

### State Memoization

```typescript
// Memoize expensive state calculations
const expensiveComputation = globalState.memo('expensiveResult', () => {
  const data = globalState.get('largeDataSet');
  return performExpensiveCalculation(data);
}, ['largeDataSet']); // Dependencies

// Result is cached until dependencies change
```

## Framework Integration

### React Integration

```typescript
// React hook for global state
import { useGlobalState } from '@micro-core/react';

const UserProfile = () => {
  const [user, setUser] = useGlobalState('user');
  
  const handleUpdate = (newData) => {
    setUser({ ...user, ...newData });
  };
  
  return (
    <div>
      <h1>{user?.name}</h1>
      <button onClick={() => handleUpdate({ name: 'Updated Name' })}>
        Update Name
      </button>
    </div>
  );
};

// Selector hook
const UserName = () => {
  const userName = useGlobalState('user', user => user?.name);
  return <span>{userName}</span>;
};
```

### Vue Integration

```typescript
// Vue composable for global state
import { useGlobalState } from '@micro-core/vue';

export default {
  setup() {
    const { state: user, setState: setUser } = useGlobalState('user');
    
    const updateUser = (newData) => {
      setUser({ ...user.value, ...newData });
    };
    
    return {
      user,
      updateUser
    };
  }
};
```

## Best Practices

### 1. State Structure

```typescript
// Good - Normalized structure
globalState.set('users', {
  byId: {
    1: { id: 1, name: 'John' },
    2: { id: 2, name: 'Jane' }
  },
  allIds: [1, 2]
});

// Avoid - Nested arrays
globalState.set('users', [
  { id: 1, name: 'John', posts: [...] },
  { id: 2, name: 'Jane', posts: [...] }
]);
```

### 2. State Updates

```typescript
// Good - Immutable updates
const currentUser = globalState.get('user');
globalState.set('user', { ...currentUser, name: 'New Name' });

// Avoid - Direct mutation
const user = globalState.get('user');
user.name = 'New Name'; // This won't trigger watchers
```

### 3. Error Handling

```typescript
// Handle state errors gracefully
globalState.on('error', (error, key, value) => {
  console.error(`State error for ${key}:`, error);
  
  // Fallback to default value
  if (key === 'user') {
    globalState.set('user', getDefaultUser());
  }
});
```

### 4. Memory Management

```typescript
// Clean up watchers
const watchers = [];

// Add watchers
watchers.push(globalState.watch('user', handleUserChange));
watchers.push(globalState.watch('settings', handleSettingsChange));

// Cleanup when component unmounts
function cleanup() {
  watchers.forEach(unwatch => unwatch());
  watchers.length = 0;
}
```

## Debugging

### State Inspector

```typescript
// Enable state debugging
const microCore = new MicroCore({
  globalState: {
    debug: true,
    devtools: true // Enable browser devtools integration
  }
});

// Get state snapshot
const snapshot = globalState.getSnapshot();
console.log('Current state:', snapshot);

// Get state history
const history = globalState.getHistory();
console.log('State history:', history);
```

### State Logging

```typescript
// Log all state changes
globalState.on('*', (event, ...args) => {
  console.log(`State event: ${event}`, args);
});

// Custom logger
const stateLogger = {
  log: (level, message, data) => {
    console[level](`[GlobalState] ${message}`, data);
  }
};

globalState.setLogger(stateLogger);
```

## Type Definitions

```typescript
interface GlobalStateConfig {
  persistence?: {
    enabled: boolean;
    storage: 'localStorage' | 'sessionStorage' | 'indexedDB';
    key: string;
    include?: string[];
    exclude?: string[];
  };
  realtime?: {
    enabled: boolean;
    transport: 'websocket' | 'sse' | 'polling';
    url: string;
    reconnect: boolean;
  };
  debug?: boolean;
  devtools?: boolean;
}

interface StateModule<T = any> {
  state: T;
  getters?: Record<string, (state: T) => any>;
  mutations?: Record<string, (state: T, payload: any) => void>;
  actions?: Record<string, (context: ActionContext<T>, payload: any) => any>;
  schema?: Record<string, SchemaDefinition>;
  validators?: Record<string, (value: any) => void>;
}

interface ActionContext<T> {
  state: T;
  commit: (mutation: string, payload?: any) => void;
  dispatch: (action: string, payload?: any) => Promise<any>;
  getters: Record<string, any>;
}

interface StateWatcher<T = any> {
  (newValue: T, oldValue: T): void;
}

interface StateMiddleware {
  name: string;
  before?: (action: any, state: any) => void;
  after?: (action: any, state: any) => void;
  error?: (error: Error, action: any, state: any) => void;
}
```

## References

- [Core API](./core.md)
- [Event Bus API](./event-bus.md)
- [Application Management](./app-management.md)
- [State Management Guide](../guide/state-management.md)
