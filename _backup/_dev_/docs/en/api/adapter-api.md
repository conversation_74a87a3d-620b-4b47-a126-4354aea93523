# Adapter API

The Adapter API enables framework-specific integration with Micro-Core, providing seamless support for React, Vue, Angular, and other frontend frameworks.

## Adapter Base Class

Abstract base class for all framework adapters.

### Constructor

```typescript
abstract class Adapter {
  abstract name: string;
  abstract framework: string;
  abstract version: string;
  
  constructor(config?: AdapterConfig) {
    this.config = config || {};
  }
}
```

### Required Properties

```typescript
interface Adapter {
  // Unique adapter name
  name: string;
  
  // Target framework name
  framework: string;
  
  // Adapter version
  version: string;
  
  // Supported framework versions
  supportedVersions: string[];
  
  // Adapter configuration
  config?: AdapterConfig;
}
```

### Lifecycle Methods

#### initialize()

Initialize the adapter.

```typescript
abstract initialize(context: AdapterContext): Promise<void>
```

**Parameters:**
- `context` - Adapter context with framework-specific utilities

**Example:**
```typescript
class ReactAdapter extends Adapter {
  name = 'react-adapter';
  framework = 'react';
  version = '1.0.0';
  supportedVersions = ['16.8+', '17.x', '18.x'];
  
  async initialize(context: AdapterContext): Promise<void> {
    console.log('Initializing React Adapter');
    
    // Setup React-specific utilities
    this.reactUtils = new ReactUtils(this.config);
    this.componentRegistry = new ComponentRegistry();
    
    // Register lifecycle handlers
    this.registerLifecycleHandlers(context);
  }
}
```

#### createLifecycleWrapper()

Create framework-specific lifecycle wrapper.

```typescript
abstract createLifecycleWrapper(
  component: any,
  options: LifecycleOptions
): LifecycleWrapper
```

**Parameters:**
- `component` - Framework component
- `options` - Lifecycle options

**Returns:** Lifecycle wrapper with standard interface

**Example:**
```typescript
createLifecycleWrapper(
  component: React.ComponentType,
  options: LifecycleOptions
): LifecycleWrapper {
  return {
    bootstrap: async (props) => {
      console.log('Bootstrapping React component');
      await this.setupReactEnvironment(props);
    },
    
    mount: async (props) => {
      console.log('Mounting React component');
      const root = ReactDOM.createRoot(props.container);
      root.render(React.createElement(component, props));
      
      // Store root for cleanup
      this.componentRegistry.set(props.name, { root, component });
    },
    
    unmount: async (props) => {
      console.log('Unmounting React component');
      const entry = this.componentRegistry.get(props.name);
      if (entry) {
        entry.root.unmount();
        this.componentRegistry.delete(props.name);
      }
    },
    
    update: async (props) => {
      console.log('Updating React component');
      const entry = this.componentRegistry.get(props.name);
      if (entry) {
        entry.root.render(React.createElement(entry.component, props));
      }
    }
  };
}
```

#### createHooks()

Create framework-specific hooks for micro-frontend integration.

```typescript
abstract createHooks(): FrameworkHooks
```

**Returns:** Framework-specific hooks

**Example:**
```typescript
createHooks(): ReactHooks {
  return {
    useMicroCore: () => {
      const context = useContext(MicroCoreContext);
      if (!context) {
        throw new Error('useMicroCore must be used within MicroCoreProvider');
      }
      return context;
    },
    
    useEventBus: () => {
      const { eventBus } = this.useMicroCore();
      return eventBus;
    },
    
    useGlobalState: (key: string) => {
      const { globalState } = this.useMicroCore();
      const [value, setValue] = useState(globalState.get(key));
      
      useEffect(() => {
        const unsubscribe = globalState.subscribe(key, setValue);
        return unsubscribe;
      }, [key]);
      
      return [value, (newValue: any) => globalState.set(key, newValue)];
    },
    
    useRouter: () => {
      const { router } = this.useMicroCore();
      return router;
    }
  };
}
```

## Built-in Adapters

### ReactAdapter

React framework adapter with full lifecycle support.

```typescript
class ReactAdapter extends Adapter {
  name = 'react-adapter';
  framework = 'react';
  version = '1.0.0';
  supportedVersions = ['16.8+', '17.x', '18.x'];
  
  constructor(config?: ReactAdapterConfig) {
    super(config);
  }
  
  async initialize(context: AdapterContext): Promise<void> {
    // Initialize React-specific utilities
    this.reactUtils = new ReactUtils(this.config);
    this.componentRegistry = new Map();
    
    // Setup error boundaries
    this.setupErrorBoundaries();
    
    // Create context provider
    this.createContextProvider(context);
  }
  
  createLifecycleWrapper(
    component: React.ComponentType,
    options: LifecycleOptions
  ): LifecycleWrapper {
    return {
      bootstrap: async (props) => {
        await this.bootstrapReactApp(props);
      },
      
      mount: async (props) => {
        await this.mountReactApp(component, props, options);
      },
      
      unmount: async (props) => {
        await this.unmountReactApp(props);
      },
      
      update: async (props) => {
        await this.updateReactApp(props);
      }
    };
  }
  
  createHooks(): ReactHooks {
    return {
      useMicroCore: this.createUseMicroCore(),
      useEventBus: this.createUseEventBus(),
      useGlobalState: this.createUseGlobalState(),
      useRouter: this.createUseRouter(),
      useAppCommunication: this.createUseAppCommunication()
    };
  }
  
  // React-specific methods
  private async mountReactApp(
    component: React.ComponentType,
    props: any,
    options: LifecycleOptions
  ): Promise<void> {
    const container = props.container;
    
    // Create React 18 root or fallback to legacy render
    if (this.isReact18()) {
      const root = ReactDOM.createRoot(container);
      const WrappedComponent = this.wrapWithProviders(component, props);
      root.render(<WrappedComponent {...props} />);
      
      this.componentRegistry.set(props.name, { root, component });
    } else {
      const WrappedComponent = this.wrapWithProviders(component, props);
      ReactDOM.render(<WrappedComponent {...props} />, container);
      
      this.componentRegistry.set(props.name, { container, component });
    }
  }
  
  private wrapWithProviders(
    component: React.ComponentType,
    props: any
  ): React.ComponentType {
    return (componentProps: any) => (
      <MicroCoreProvider value={this.microCoreContext}>
        <ErrorBoundary onError={this.handleError}>
          {React.createElement(component, componentProps)}
        </ErrorBoundary>
      </MicroCoreProvider>
    );
  }
}
```

**Configuration:**
```typescript
interface ReactAdapterConfig extends AdapterConfig {
  // React version compatibility
  version?: '16' | '17' | '18';
  
  // Concurrent features (React 18+)
  concurrent?: boolean;
  
  // Strict mode
  strictMode?: boolean;
  
  // Error boundary configuration
  errorBoundary?: {
    enabled: boolean;
    fallback?: React.ComponentType<any>;
    onError?: (error: Error, errorInfo: any) => void;
  };
  
  // Development tools
  devtools?: boolean;
}
```

**Usage:**
```typescript
// Create React adapter
const reactAdapter = new ReactAdapter({
  version: '18',
  concurrent: true,
  strictMode: true,
  errorBoundary: {
    enabled: true,
    fallback: ErrorFallback,
    onError: (error, errorInfo) => {
      console.error('React app error:', error, errorInfo);
    }
  }
});

// Register with Micro-Core
microCore.registerAdapter(reactAdapter);

// Create lifecycle wrapper for React component
const lifecycleWrapper = reactAdapter.createLifecycleWrapper(MyReactApp, {
  container: '#react-container'
});

// Export lifecycle functions
export const { bootstrap, mount, unmount, update } = lifecycleWrapper;
```

### VueAdapter

Vue framework adapter with composition API support.

```typescript
class VueAdapter extends Adapter {
  name = 'vue-adapter';
  framework = 'vue';
  version = '1.0.0';
  supportedVersions = ['2.7+', '3.x'];
  
  constructor(config?: VueAdapterConfig) {
    super(config);
  }
  
  async initialize(context: AdapterContext): Promise<void> {
    // Initialize Vue-specific utilities
    this.vueUtils = new VueUtils(this.config);
    this.appRegistry = new Map();
    
    // Setup global properties
    this.setupGlobalProperties(context);
  }
  
  createLifecycleWrapper(
    component: any,
    options: LifecycleOptions
  ): LifecycleWrapper {
    return {
      bootstrap: async (props) => {
        await this.bootstrapVueApp(props);
      },
      
      mount: async (props) => {
        await this.mountVueApp(component, props, options);
      },
      
      unmount: async (props) => {
        await this.unmountVueApp(props);
      },
      
      update: async (props) => {
        await this.updateVueApp(props);
      }
    };
  }
  
  createHooks(): VueHooks {
    return {
      useMicroCore: this.createUseMicroCore(),
      useEventBus: this.createUseEventBus(),
      useGlobalState: this.createUseGlobalState(),
      useRouter: this.createUseRouter()
    };
  }
  
  // Vue-specific methods
  private async mountVueApp(
    component: any,
    props: any,
    options: LifecycleOptions
  ): Promise<void> {
    const container = props.container;
    
    if (this.isVue3()) {
      // Vue 3 implementation
      const app = createApp(component, props);
      
      // Install plugins and provide context
      this.setupVue3App(app, props);
      
      app.mount(container);
      this.appRegistry.set(props.name, { app, component });
    } else {
      // Vue 2 implementation
      const vm = new Vue({
        render: h => h(component, { props }),
        ...this.getVue2Options(props)
      });
      
      vm.$mount(container);
      this.appRegistry.set(props.name, { vm, component });
    }
  }
  
  private setupVue3App(app: any, props: any): void {
    // Provide Micro-Core context
    app.provide('microCore', this.microCoreContext);
    
    // Install global properties
    app.config.globalProperties.$microCore = this.microCoreContext;
    app.config.globalProperties.$eventBus = this.microCoreContext.eventBus;
    app.config.globalProperties.$globalState = this.microCoreContext.globalState;
  }
}
```

**Configuration:**
```typescript
interface VueAdapterConfig extends AdapterConfig {
  // Vue version
  version?: '2' | '3';
  
  // Global properties
  globalProperties?: Record<string, any>;
  
  // Plugin installation
  plugins?: any[];
  
  // Error handling
  errorHandler?: (error: Error, instance: any, info: string) => void;
  
  // Development tools
  devtools?: boolean;
}
```

### AngularAdapter

Angular framework adapter with dependency injection support.

```typescript
class AngularAdapter extends Adapter {
  name = 'angular-adapter';
  framework = 'angular';
  version = '1.0.0';
  supportedVersions = ['12+', '13.x', '14.x', '15.x', '16.x'];
  
  constructor(config?: AngularAdapterConfig) {
    super(config);
  }
  
  async initialize(context: AdapterContext): Promise<void> {
    // Initialize Angular-specific utilities
    this.angularUtils = new AngularUtils(this.config);
    this.moduleRegistry = new Map();
    
    // Setup dependency injection
    this.setupDependencyInjection(context);
  }
  
  createLifecycleWrapper(
    module: any,
    options: LifecycleOptions
  ): LifecycleWrapper {
    return {
      bootstrap: async (props) => {
        await this.bootstrapAngularApp(props);
      },
      
      mount: async (props) => {
        await this.mountAngularApp(module, props, options);
      },
      
      unmount: async (props) => {
        await this.unmountAngularApp(props);
      },
      
      update: async (props) => {
        await this.updateAngularApp(props);
      }
    };
  }
  
  createServices(): AngularServices {
    return {
      MicroCoreService: this.createMicroCoreService(),
      EventBusService: this.createEventBusService(),
      GlobalStateService: this.createGlobalStateService(),
      RouterService: this.createRouterService()
    };
  }
  
  // Angular-specific methods
  private async mountAngularApp(
    module: any,
    props: any,
    options: LifecycleOptions
  ): Promise<void> {
    const container = props.container;
    
    // Create platform and bootstrap module
    const platform = platformBrowserDynamic();
    const moduleRef = await platform.bootstrapModule(module);
    
    // Store reference for cleanup
    this.moduleRegistry.set(props.name, { moduleRef, platform });
    
    // Inject Micro-Core services
    this.injectMicroCoreServices(moduleRef);
  }
  
  private injectMicroCoreServices(moduleRef: any): void {
    const injector = moduleRef.injector;
    
    // Provide Micro-Core context to Angular services
    const microCoreService = injector.get(MicroCoreService);
    microCoreService.setContext(this.microCoreContext);
  }
}
```

## Adapter Utilities

### Framework Detection

```typescript
class FrameworkDetector {
  static detectFramework(): FrameworkInfo {
    // Detect React
    if (typeof React !== 'undefined') {
      return {
        name: 'react',
        version: React.version,
        detected: true
      };
    }
    
    // Detect Vue
    if (typeof Vue !== 'undefined') {
      return {
        name: 'vue',
        version: Vue.version,
        detected: true
      };
    }
    
    // Detect Angular
    if (typeof ng !== 'undefined') {
      return {
        name: 'angular',
        version: ng.version?.full,
        detected: true
      };
    }
    
    return {
      name: 'unknown',
      version: null,
      detected: false
    };
  }
  
  static isCompatible(adapter: Adapter): boolean {
    const framework = this.detectFramework();
    
    if (framework.name !== adapter.framework) {
      return false;
    }
    
    return adapter.supportedVersions.some(version => 
      this.versionMatches(framework.version, version)
    );
  }
  
  private static versionMatches(actual: string, supported: string): boolean {
    // Implement version matching logic
    return true; // Simplified for example
  }
}
```

### Component Registry

```typescript
class ComponentRegistry {
  private components = new Map<string, ComponentEntry>();
  
  register(name: string, component: any, metadata?: any): void {
    this.components.set(name, {
      component,
      metadata,
      registered: Date.now()
    });
  }
  
  unregister(name: string): boolean {
    return this.components.delete(name);
  }
  
  get(name: string): ComponentEntry | undefined {
    return this.components.get(name);
  }
  
  list(): ComponentEntry[] {
    return Array.from(this.components.values());
  }
  
  clear(): void {
    this.components.clear();
  }
}
```

### Error Boundaries

```typescript
// React Error Boundary
class MicroCoreErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Micro-Core React Error:', error, errorInfo);
    
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }
  
  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="micro-core-error">
          <h3>Something went wrong</h3>
          <p>This micro-frontend encountered an error.</p>
          <button onClick={() => this.setState({ hasError: false, error: null })}>
            Retry
          </button>
        </div>
      );
    }
    
    return this.props.children;
  }
}

// Vue Error Handler
const vueErrorHandler = (error: Error, instance: any, info: string) => {
  console.error('Micro-Core Vue Error:', error, info);
  
  // Send to error tracking
  if (window.errorTracker) {
    window.errorTracker.captureException(error, {
      framework: 'vue',
      instance: instance?.$options.name,
      info
    });
  }
};
```

## Advanced Adapter Features

### State Synchronization

```typescript
class AdapterStateSync {
  constructor(
    private adapter: Adapter,
    private globalState: StateManager
  ) {}
  
  // React state synchronization
  createReactStateSync() {
    return {
      useGlobalState: <T>(key: string, defaultValue?: T) => {
        const [value, setValue] = useState<T>(
          this.globalState.get(key) ?? defaultValue
        );
        
        useEffect(() => {
          const unsubscribe = this.globalState.subscribe(key, setValue);
          return unsubscribe;
        }, [key]);
        
        const setGlobalValue = useCallback((newValue: T) => {
          this.globalState.set(key, newValue);
        }, [key]);
        
        return [value, setGlobalValue] as const;
      }
    };
  }
  
  // Vue state synchronization
  createVueStateSync() {
    return {
      useGlobalState: <T>(key: string, defaultValue?: T) => {
        const value = ref<T>(this.globalState.get(key) ?? defaultValue);
        
        const unsubscribe = this.globalState.subscribe(key, (newValue: T) => {
          value.value = newValue;
        });
        
        const setGlobalValue = (newValue: T) => {
          this.globalState.set(key, newValue);
        };
        
        onUnmounted(() => {
          unsubscribe();
        });
        
        return [readonly(value), setGlobalValue] as const;
      }
    };
  }
}
```

### Event Integration

```typescript
class AdapterEventIntegration {
  constructor(
    private adapter: Adapter,
    private eventBus: EventBus
  ) {}
  
  // React event integration
  createReactEventHooks() {
    return {
      useEventListener: (event: string, handler: Function, deps: any[] = []) => {
        useEffect(() => {
          const unsubscribe = this.eventBus.on(event, handler);
          return unsubscribe;
        }, deps);
      },
      
      useEventEmitter: () => {
        return useCallback((event: string, data?: any) => {
          this.eventBus.emit(event, data);
        }, []);
      }
    };
  }
  
  // Vue event integration
  createVueEventComposables() {
    return {
      useEventListener: (event: string, handler: Function) => {
        onMounted(() => {
          const unsubscribe = this.eventBus.on(event, handler);
          
          onUnmounted(() => {
            unsubscribe();
          });
        });
      },
      
      useEventEmitter: () => {
        return (event: string, data?: any) => {
          this.eventBus.emit(event, data);
        };
      }
    };
  }
}
```

## Adapter Testing

### Testing Utilities

```typescript
class AdapterTestUtils {
  static createMockAdapter(framework: string): Adapter {
    return {
      name: `mock-${framework}-adapter`,
      framework,
      version: '1.0.0',
      supportedVersions: ['*'],
      
      async initialize(context: AdapterContext) {
        // Mock initialization
      },
      
      createLifecycleWrapper(component: any, options: LifecycleOptions) {
        return {
          bootstrap: jest.fn(),
          mount: jest.fn(),
          unmount: jest.fn(),
          update: jest.fn()
        };
      },
      
      createHooks() {
        return {
          useMicroCore: jest.fn(),
          useEventBus: jest.fn(),
          useGlobalState: jest.fn(),
          useRouter: jest.fn()
        };
      }
    };
  }
  
  static async testAdapterLifecycle(adapter: Adapter): Promise<void> {
    const mockContext = this.createMockContext();
    const mockComponent = {};
    const mockOptions = { container: document.createElement('div') };
    
    // Test initialization
    await adapter.initialize(mockContext);
    
    // Test lifecycle wrapper creation
    const wrapper = adapter.createLifecycleWrapper(mockComponent, mockOptions);
    
    // Test lifecycle methods
    await wrapper.bootstrap({});
    await wrapper.mount({});
    await wrapper.update({});
    await wrapper.unmount({});
  }
  
  private static createMockContext(): AdapterContext {
    return {
      eventBus: new MockEventBus(),
      globalState: new MockStateManager(),
      router: new MockRouter(),
      microCore: {} as any
    };
  }
}
```

### Integration Tests

```typescript
describe('Adapter Integration', () => {
  let microCore: MicroCore;
  let reactAdapter: ReactAdapter;
  
  beforeEach(() => {
    microCore = new MicroCore({
      container: '#test-container'
    });
    
    reactAdapter = new ReactAdapter({
      version: '18',
      concurrent: true
    });
  });
  
  afterEach(async () => {
    await microCore.destroy();
  });
  
  it('should register and initialize adapter', async () => {
    await microCore.registerAdapter(reactAdapter);
    
    const registeredAdapter = microCore.getAdapter('react-adapter');
    expect(registeredAdapter).toBe(reactAdapter);
  });
  
  it('should create lifecycle wrapper', () => {
    const TestComponent = () => <div>Test</div>;
    const wrapper = reactAdapter.createLifecycleWrapper(TestComponent, {
      container: document.createElement('div')
    });
    
    expect(wrapper.bootstrap).toBeDefined();
    expect(wrapper.mount).toBeDefined();
    expect(wrapper.unmount).toBeDefined();
    expect(wrapper.update).toBeDefined();
  });
  
  it('should provide framework hooks', () => {
    const hooks = reactAdapter.createHooks();
    
    expect(hooks.useMicroCore).toBeDefined();
    expect(hooks.useEventBus).toBeDefined();
    expect(hooks.useGlobalState).toBeDefined();
    expect(hooks.useRouter).toBeDefined();
  });
});
```

## Type Definitions

### Complete TypeScript Definitions

```typescript
// Adapter base types
export abstract class Adapter {
  abstract name: string;
  abstract framework: string;
  abstract version: string;
  supportedVersions: string[];
  config?: AdapterConfig;
  
  constructor(config?: AdapterConfig);
  abstract initialize(context: AdapterContext): Promise<void>;
  abstract createLifecycleWrapper(component: any, options: LifecycleOptions): LifecycleWrapper;
  abstract createHooks(): FrameworkHooks;
}

export interface AdapterConfig {
  [key: string]: any;
}

export interface AdapterContext {
  eventBus: EventBus;
  globalState: StateManager;
  router: Router;
  microCore: MicroCore;
}

export interface LifecycleOptions {
  container: HTMLElement | string;
  props?: Record<string, any>;
  [key: string]: any;
}

export interface LifecycleWrapper {
  bootstrap: (props: any) => Promise<void>;
  mount: (props: any) => Promise<void>;
  unmount: (props: any) => Promise<void>;
  update: (props: any) => Promise<void>;
}

// Framework-specific types
export interface ReactAdapterConfig extends AdapterConfig {
  version?: '16' | '17' | '18';
  concurrent?: boolean;
  strictMode?: boolean;
  errorBoundary?: {
    enabled: boolean;
    fallback?: React.ComponentType<any>;
    onError?: (error: Error, errorInfo: any) => void;
  };
  devtools?: boolean;
}

export interface VueAdapterConfig extends AdapterConfig {
  version?: '2' | '3';
  globalProperties?: Record<string, any>;
  plugins?: any[];
  errorHandler?: (error: Error, instance: any, info: string) => void;
  devtools?: boolean;
}

export interface AngularAdapterConfig extends AdapterConfig {
  version?: string;
  providers?: any[];
  imports?: any[];
  errorHandler?: (error: Error) => void;
}

// Hook types
export interface FrameworkHooks {
  [key: string]: Function;
}

export interface ReactHooks extends FrameworkHooks {
  useMicroCore: () => MicroCoreContext;
  useEventBus: () => EventBus;
  useGlobalState: <T>(key: string, defaultValue?: T) => [T, (value: T) => void];
  useRouter: () => Router;
  useAppCommunication: () => AppCommunication;
}

export interface VueHooks extends FrameworkHooks {
  useMicroCore: () => MicroCoreContext;
  useEventBus: () => EventBus;
  useGlobalState: <T>(key: string, defaultValue?: T) => [Readonly<Ref<T>>, (value: T) => void];
  useRouter: () => Router;
}

export interface AngularServices {
  MicroCoreService: any;
  EventBusService: any;
  GlobalStateService: any;
  RouterService: any;
}

// Utility types
export interface FrameworkInfo {
  name: string;
  version: string | null;
  detected: boolean;
}

export interface ComponentEntry {
  component: any;
  metadata?: any;
  registered: number;
}

export interface MicroCoreContext {
  eventBus: EventBus;
  globalState: StateManager;
  router: Router;
  microCore: MicroCore;
}

export interface AppCommunication {
  sendMessage: (target: string, message: any) => Promise<void>;
  onMessage: (handler: (message: any) => void) => () => void;
  broadcast: (message: any) => void;
}
```

## Best Practices

### Adapter Development Guidelines

1. **Framework Compatibility**: Support multiple versions of the target framework
2. **Lifecycle Management**: Properly handle all lifecycle phases
3. **Error Handling**: Implement comprehensive error boundaries and handlers
4. **Memory Management**: Ensure proper cleanup to prevent memory leaks
5. **Hook Design**: Create intuitive and consistent hooks/composables
6. **Testing**: Write thorough unit and integration tests
7. **Documentation**: Provide clear usage examples and API documentation

### Performance Considerations

1. **Lazy Loading**: Load framework-specific code only when needed
2. **Bundle Optimization**: Minimize adapter bundle size
3. **Caching**: Cache framework instances and components appropriately
4. **Event Handling**: Use efficient event handling patterns
5. **State Management**: Optimize state synchronization performance

---

This completes the Adapter API documentation. For related APIs, see:

- **[Core API](/en/api/core)** - Main Micro-Core functionality
- **[Communication API](/en/api/communication)** - Inter-app communication
- **[Event System API](/en/api/event-system)** - Event handling
- **[Plugin API](/en/api/plugin-api)** - Plugin development
