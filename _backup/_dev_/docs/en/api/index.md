# API Reference

Welcome to the Micro-Core API Reference documentation. This document provides complete API descriptions for the Micro-Core framework, including core modules, plugin system, adapters, and all functionality details.

## Quick Navigation

### Core API

- **[Core Module](./core.md)** - MicroCore main class and basic functionality
- **[Application Management](./app-management.md)** - App registration and lifecycle management
- **[Routing System](./routing.md)** - Route configuration and navigation control
- **[Event Bus](./event-bus.md)** - Inter-application event communication
- **[State Management](./state-management.md)** - Global state management
- **[Sandbox System](./sandbox.md)** - Application isolation and security
- **[Loader](./loader.md)** - Resource loading and management
- **[Communication System](./communication.md)** - Cross-application communication protocol

### Plugin API

- **[Plugin Base](./plugins/base.md)** - Plugin development foundation
- **[Router Plugin](./plugins/router.md)** - Routing functionality extension
- **[Communication Plugin](./plugins/communication.md)** - Communication functionality extension
- **[Auth Plugin](./plugins/auth.md)** - Authentication functionality

### Adapter API

- **[Adapter Base](./adapters/base.md)** - Framework adapter foundation
- **[React Adapter](./adapters/react.md)** - React framework support
- **[Vue Adapter](./adapters/vue.md)** - Vue framework support
- **[Angular Adapter](./adapters/angular.md)** - Angular framework support

## Core Concepts

### MicroCore Main Class

`MicroCore` is the core class of the framework, responsible for managing the entire micro-frontend system:

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  container: '#app',
  router: { mode: 'history' },
  sandbox: { type: 'proxy' }
});
```

### Application Configuration

Each micro-application needs to be registered through a configuration object:

```typescript
interface AppConfig {
  name: string;                    // Application name
  entry: string | AppEntry;        // Application entry
  container?: string | Element;    // Container element
  activeRule: ActiveRule;          // Activation rule
  props?: Record<string, any>;     // Application properties
  sandbox?: SandboxConfig;         // Sandbox configuration
  // ... more configuration options
}
```

### Lifecycle Hooks

Application lifecycle provides rich hook functions:

```typescript
interface LifecycleHooks {
  beforeBootstrap?: (app: MicroApp) => Promise<void>;
  afterBootstrap?: (app: MicroApp) => Promise<void>;
  beforeMount?: (app: MicroApp) => Promise<void>;
  afterMount?: (app: MicroApp) => Promise<void>;
  beforeUnmount?: (app: MicroApp) => Promise<void>;
  afterUnmount?: (app: MicroApp) => Promise<void>;
  beforeUpdate?: (app: MicroApp) => Promise<void>;
  afterUpdate?: (app: MicroApp) => Promise<void>;
}
```

## Type Definitions

### Basic Types

```typescript
// Application status
type AppStatus = 'NOT_LOADED' | 'LOADING' | 'LOADED' | 'BOOTSTRAPPING' | 
                 'NOT_MOUNTED' | 'MOUNTING' | 'MOUNTED' | 'UNMOUNTING' | 
                 'UNLOADING' | 'SKIP_BECAUSE_BROKEN';

// Activation rule
type ActiveRule = string | RegExp | Array<string | RegExp> | 
                  ((location: Location) => boolean);

// Application entry
interface AppEntry {
  scripts?: string[];
  styles?: string[];
  html?: string;
}
```

### Configuration Types

```typescript
// Main configuration
interface MicroCoreConfig {
  container?: string | Element;
  router?: RouterConfig;
  sandbox?: SandboxConfig;
  prefetch?: PrefetchConfig;
  errorHandler?: ErrorHandlerConfig;
  plugins?: Plugin[];
  adapters?: Record<string, FrameworkAdapter>;
}

// Router configuration
interface RouterConfig {
  mode?: 'hash' | 'history' | 'memory';
  base?: string;
  linkActiveClass?: string;
  linkExactActiveClass?: string;
}

// Sandbox configuration
interface SandboxConfig {
  type?: 'proxy' | 'iframe' | 'web-components';
  css?: boolean | CSSIsolationConfig;
  js?: boolean | JSIsolationConfig;
  globalWhitelist?: string[];
  globalBlacklist?: string[];
}
```

## Usage Examples

### Basic Usage

```typescript
import { MicroCore } from '@micro-core/core';

// Create instance
const microCore = new MicroCore({
  container: '#micro-app-container',
  router: {
    mode: 'history',
    base: '/'
  }
});

// Register application
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  activeRule: '/react-app',
  container: '#react-container'
});

// Start framework
microCore.start();
```

### Advanced Configuration

```typescript
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';
import { RouterPlugin } from '@micro-core/plugin-router';

const microCore = new MicroCore({
  // Container configuration
  container: document.getElementById('app'),
  
  // Router configuration
  router: {
    mode: 'history',
    base: '/micro-apps/',
    linkActiveClass: 'active',
    linkExactActiveClass: 'exact-active'
  },
  
  // Sandbox configuration
  sandbox: {
    type: 'proxy',
    css: {
      enabled: true,
      prefix: 'micro-app-'
    },
    js: {
      enabled: true,
      strict: true
    },
    globalWhitelist: ['console', 'location'],
    globalBlacklist: ['eval', 'Function']
  },
  
  // Prefetch configuration
  prefetch: {
    idle: ['app1', 'app2'],
    hover: ['app3']
  },
  
  // Error handling
  errorHandler: {
    onJSError: (error, app) => {
      console.error('JS Error:', error);
    },
    onLoadError: (error, app) => {
      console.error('Load Error:', error);
    }
  }
});

// Register adapter
microCore.registerAdapter('react', new ReactAdapter());

// Use plugin
microCore.use(RouterPlugin, {
  guards: {
    beforeEach: (to, from, next) => {
      console.log('Route change:', from.path, '->', to.path);
      next();
    }
  }
});

// Register multiple applications
microCore.registerApps([
  {
    name: 'header',
    entry: 'http://localhost:3001',
    activeRule: () => true, // Always active
    container: '#header'
  },
  {
    name: 'main-app',
    entry: 'http://localhost:3002',
    activeRule: '/main',
    container: '#main',
    props: {
      userId: '123',
      theme: 'dark'
    }
  },
  {
    name: 'admin-panel',
    entry: 'http://localhost:3003',
    activeRule: ['/admin', '/admin/*'],
    container: '#admin',
    beforeMount: async (app) => {
      // Permission check
      const hasPermission = await checkAdminPermission();
      if (!hasPermission) {
        throw new Error('No admin permission');
      }
    }
  }
]);

// Start framework
microCore.start().then(() => {
  console.log('Micro-Core started successfully');
});
```

### Inter-Application Communication

```typescript
// Event communication
microCore.eventBus.emit('user-login', {
  userId: '123',
  username: 'john'
});

microCore.eventBus.on('user-login', (data) => {
  console.log('User logged in:', data);
});

// Global state
microCore.globalState.set('currentUser', {
  id: '123',
  name: 'John Doe'
});

const user = microCore.globalState.get('currentUser');

microCore.globalState.subscribe('currentUser', (newUser, oldUser) => {
  console.log('User changed:', oldUser, '->', newUser);
});

// Shared dependencies
microCore.shared.register('utils', {
  formatDate: (date) => date.toISOString(),
  request: axios.create({ baseURL: '/api' })
});

const utils = microCore.shared.get('utils');
```

## Error Handling

### Common Error Types

```typescript
// Application load error
class AppLoadError extends Error {
  constructor(appName: string, originalError: Error) {
    super(`Failed to load app: ${appName}`);
    this.name = 'AppLoadError';
    this.cause = originalError;
  }
}

// Application mount error
class AppMountError extends Error {
  constructor(appName: string, originalError: Error) {
    super(`Failed to mount app: ${appName}`);
    this.name = 'AppMountError';
    this.cause = originalError;
  }
}

// Route error
class RouteError extends Error {
  constructor(path: string, originalError: Error) {
    super(`Route error for path: ${path}`);
    this.name = 'RouteError';
    this.cause = originalError;
  }
}
```

### Error Handling Best Practices

```typescript
const microCore = new MicroCore({
  errorHandler: {
    // JavaScript runtime errors
    onJSError: (error, app) => {
      // Error reporting
      reportError({
        type: 'js-error',
        app: app?.name,
        error: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
      
      // Error recovery
      if (app && error.name === 'ChunkLoadError') {
        // Reload application
        microCore.reloadApp(app.name);
      }
    },
    
    // Resource loading errors
    onLoadError: (error, app) => {
      console.error(`Failed to load resources for app: ${app?.name}`, error);
      
      // Show fallback UI
      showFallbackUI(app);
      
      // Try backup resources
      if (app && app.entry.includes('cdn.example.com')) {
        app.entry = app.entry.replace('cdn.example.com', 'backup-cdn.example.com');
        microCore.reloadApp(app.name);
      }
    },
    
    // Application-level errors
    onAppError: (error, app) => {
      console.error(`App error in ${app.name}:`, error);
      
      // Application isolation: errors don't affect other apps
      if (error.name === 'CriticalError') {
        microCore.unloadApp(app.name);
        showErrorBoundary(app.name, error);
      }
    }
  }
});
```

## Performance Optimization

### Prefetch Strategy

```typescript
const microCore = new MicroCore({
  prefetch: {
    // Idle time prefetch
    idle: {
      apps: ['frequently-used-app'],
      timeout: 5000
    },
    
    // Mouse hover prefetch
    hover: {
      apps: ['on-demand-app'],
      delay: 200,
      selector: '[data-prefetch]'
    },
    
    // Viewport prefetch
    viewport: {
      apps: ['below-fold-app'],
      threshold: 0.1,
      rootMargin: '50px'
    }
  }
});
```

### Cache Configuration

```typescript
const microCore = new MicroCore({
  cache: {
    // Application cache
    apps: {
      enabled: true,
      maxAge: 5 * 60 * 1000,    // 5 minutes
      maxSize: 10,              // Cache max 10 apps
      strategy: 'lru'           // LRU strategy
    },
    
    // Asset cache
    assets: {
      enabled: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      types: ['js', 'css', 'json'],
      compression: true
    }
  }
});
```

## Debugging and Monitoring

### Development Tools

```typescript
// Enable debugging tools in development environment
if (process.env.NODE_ENV === 'development') {
  import('@micro-core/dev-tools').then(({ DevTools }) => {
    microCore.use(DevTools, {
      position: 'bottom-right',
      theme: 'dark',
      features: {
        appInspector: true,      // Application inspector
        eventMonitor: true,      // Event monitoring
        performanceMonitor: true, // Performance monitoring
        stateInspector: true,    // State inspector
        networkMonitor: true,    // Network monitoring
        consoleIntegration: true // Console integration
      }
    });
  });
}
```

### Performance Monitoring

```typescript
// Performance monitoring plugin
microCore.use(PerformancePlugin, {
  // Monitoring metrics
  metrics: {
    appLoadTime: true,        // Application load time
    appMountTime: true,       // Application mount time
    routeChangeTime: true,    // Route change time
    memoryUsage: true,        // Memory usage
    bundleSize: true,         // Bundle size
    errorRate: true           // Error rate
  },
  
  // Performance thresholds
  thresholds: {
    appLoadTime: 3000,        // 3 seconds
    appMountTime: 1000,       // 1 second
    memoryUsage: 100 * 1024 * 1024 // 100MB
  },
  
  // Reporting configuration
  report: {
    url: '/api/performance',
    interval: 30000,          // Report every 30 seconds
    batch: true,              // Batch reporting
    compress: true            // Compress data
  }
});
```

## Version Compatibility

### API Versions

| Version | Status | Description |
|---------|--------|-------------|
| v3.x | Current | Latest stable version, recommended |
| v2.x | Maintenance | Only critical bug fixes, upgrade recommended |
| v1.x | End of Support | No longer maintained, please upgrade |

### Migration Guide

If you are using an older version of Micro-Core, please refer to:

- **[Upgrade from v2.x to v3.x](../migration/v2-to-v3.md)**
- **[Upgrade from v1.x to v3.x](../migration/v1-to-v3.md)**
- **[Breaking Changes](../migration/breaking-changes.md)**

## Getting Help

If you encounter problems using the API, you can get help through:

- **[GitHub Issues](https://github.com/micro-core/micro-core/issues)** - Report bugs or request features
- **[GitHub Discussions](https://github.com/micro-core/micro-core/discussions)** - Community discussions and Q&A
- **[Official Documentation](https://micro-core.dev)** - Complete usage guide
- **[Example Projects](https://github.com/micro-core/examples)** - Practical use cases

## Contributing Guide

If you want to contribute to Micro-Core's API documentation:

1. **Fork** the project repository
2. **Create** a feature branch
3. **Write** or improve documentation
4. **Submit** a Pull Request

For detailed contribution guidelines, please refer to [CONTRIBUTING.md](../CONTRIBUTING.md).

---

**Note**: This documentation is continuously updated with Micro-Core updates. It is recommended to regularly check the latest version of the documentation for the most up-to-date API information.