# Router API (Part 2)

## Route Configuration

### Router Modes

Micro-Core router supports two modes:

1. **Hash Mode**: Uses the hash part of the URL (after `#`) to simulate a complete URL
2. **History Mode**: Uses HTML5 History API to implement real URLs

```typescript
// Hash mode
const router = new Router({
  mode: 'hash',
  routes: [...]
});
// URL example: https://example.com/#/users/123

// History mode
const router = new Router({
  mode: 'history',
  routes: [...]
});
// URL example: https://example.com/users/123
```

### Route Matching

Route matching supports multiple patterns:

1. **Static Paths**: Exact match for specified paths
2. **Dynamic Path Parameters**: Match part of the path and use it as parameters
3. **Nested Routes**: Define nested route structure through parent-child relationships
4. **Named Views**: Display multiple views simultaneously
5. **Wildcards**: Match any path

```typescript
const router = new Router({
  routes: [
    // Static path
    { path: '/home', component: 'home-app' },
    
    // Dynamic path parameters
    { path: '/users/:userId', component: 'user-app' },
    
    // Optional parameters
    { path: '/posts/:postId?', component: 'post-app' },
    
    // Multiple parameters
    { path: '/categories/:categoryId/products/:productId', component: 'product-app' },
    
    // Nested routes
    {
      path: '/settings',
      component: 'settings-app',
      children: [
        { path: 'profile', component: 'profile-app' },
        { path: 'security', component: 'security-app' }
      ]
    },
    
    // Named views
    {
      path: '/dashboard',
      components: {
        default: 'dashboard-main-app',
        sidebar: 'dashboard-sidebar-app',
        header: 'dashboard-header-app'
      }
    },
    
    // Wildcard
    { path: '*', component: 'not-found-app' }
  ]
});
```

### Route Meta Information

Route meta information allows you to attach arbitrary information to routes:

```typescript
const router = new Router({
  routes: [
    {
      path: '/admin',
      component: 'admin-app',
      meta: {
        requiresAuth: true,
        roles: ['admin'],
        title: 'Admin Panel',
        transition: 'fade',
        keepAlive: true
      }
    }
  ]
});

// Use meta information in navigation guards
router.beforeEach((to, from, next) => {
  // Set page title
  document.title = to.meta.title || 'Default Title';
  
  // Check permissions
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
  } else if (to.meta.roles && !hasRole(to.meta.roles)) {
    next('/403');
  } else {
    next();
  }
});
```

### Route Parameters

There are multiple ways to pass route parameters:

1. **Dynamic Path Parameters**: Pass parameters through path
2. **Query Parameters**: Pass parameters through URL query string
3. **Props Parameters**: Pass route parameters as component props

```typescript
const router = new Router({
  routes: [
    // Dynamic path parameters
    {
      path: '/users/:userId',
      component: 'user-app'
    },
    
    // Props parameters (boolean mode)
    {
      path: '/products/:productId',
      component: 'product-app',
      props: true // Pass route parameters as component props
    },
    
    // Props parameters (object mode)
    {
      path: '/about',
      component: 'about-app',
      props: { version: '1.0.0' } // Pass static values
    },
    
    // Props parameters (function mode)
    {
      path: '/search',
      component: 'search-app',
      props: (route) => ({
        query: route.query.q,
        page: Number(route.query.page) || 1,
        filters: route.query.filters
      })
    }
  ]
});

// Pass parameters during navigation
router.push({
  path: '/users/123',
  query: { tab: 'profile', mode: 'edit' }
});

// Access parameters in component
// Path parameters: this.$route.params.userId
// Query parameters: this.$route.query.tab, this.$route.query.mode
```

## Navigation Guards

Navigation guards are used to control navigation behavior, such as permission checks, data preloading, etc.

### Global Guards

Global guards apply to all route navigation:

```typescript
// Global before guard
router.beforeEach((to, from, next) => {
  console.log(`Navigation started: ${from.path} -> ${to.path}`);
  
  // Permission check
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
  } else {
    next();
  }
});

// Global resolve guard
router.beforeResolve((to, from, next) => {
  console.log('Route resolving...');
  
  // Data preloading
  if (to.meta.fetchData) {
    loadData()
      .then(() => next())
      .catch(() => next(false));
  } else {
    next();
  }
});

// Global after hook
router.afterEach((to, from) => {
  console.log(`Navigation completed: ${from.path} -> ${to.path}`);
  
  // Update page title
  document.title = to.meta.title || 'Default Title';
  
  // Send analytics event
  trackPageView(to.path);
});
```

### Per-Route Guards

Per-route guards only apply to specific routes:

```typescript
const router = new Router({
  routes: [
    {
      path: '/admin',
      component: 'admin-app',
      beforeEnter: (to, from, next) => {
        // Check if user is admin
        if (isAdmin()) {
          next();
        } else {
          next('/403');
        }
      }
    }
  ]
});
```

### In-Component Guards

In-component guards are defined within components:

```typescript
const UserComponent = {
  template: '...',
  
  // Before entering component
  beforeRouteEnter(to, from, next) {
    // Called before the route that renders this component is confirmed
    // Component instance is not yet created, cannot access this
    
    // Load user data
    fetchUser(to.params.userId)
      .then(user => {
        // Access component instance through callback
        next(vm => {
          vm.user = user;
        });
      })
      .catch(error => {
        next(false);
      });
  },
  
  // Before component update
  beforeRouteUpdate(to, from, next) {
    // Called when current route changes but component is reused
    // Can access this
    
    // Save scroll position
    const scrollPosition = this.saveScrollPosition();
    
    // Update user data
    this.fetchUser(to.params.userId)
      .then(() => {
        next();
        // Restore scroll position
        this.$nextTick(() => {
          this.restoreScrollPosition(scrollPosition);
        });
      })
      .catch(error => {
        next(false);
      });
  },
  
  // Before leaving component
  beforeRouteLeave(to, from, next) {
    // Called when navigating away from the route that renders this component
    // Can access this
    
    // Check for unsaved changes
    if (this.hasUnsavedChanges) {
      // Show confirmation dialog
      const confirmed = window.confirm('You have unsaved changes. Are you sure you want to leave?');
      if (confirmed) {
        next();
      } else {
        next(false);
      }
    } else {
      next();
    }
  }
};
```

## Route Lazy Loading

Route lazy loading allows you to load route components on demand, improving application performance:

```typescript
const router = new Router({
  routes: [
    {
      path: '/',
      component: 'home-app' // Direct loading
    },
    {
      path: '/users',
      component: () => loadMicroApp('users-app') // Lazy loading
    },
    {
      path: '/settings',
      component: () => {
        // Show loading indicator
        showLoadingIndicator();
        
        // Load component
        return loadMicroApp('settings-app')
          .finally(() => {
            // Hide loading indicator
            hideLoadingIndicator();
          });
      }
    },
    {
      path: '/admin',
      component: () => {
        // Lazy loading with timeout
        return Promise.race([
          loadMicroApp('admin-app'),
          new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Loading timeout')), 5000);
          })
        ]);
      }
    }
  ]
});
```

## Scroll Behavior

Control page scroll behavior:

```typescript
const router = new Router({
  scrollBehavior(to, from, savedPosition) {
    // If there's a saved position, restore to saved position
    if (savedPosition) {
      return savedPosition;
    }
    
    // If there's a hash, scroll to anchor
    if (to.hash) {
      return { selector: to.hash };
    }
    
    // Otherwise scroll to top
    return { x: 0, y: 0 };
  }
});

// More complex scroll behavior
const router = new Router({
  scrollBehavior(to, from, savedPosition) {
    // Delayed scrolling
    return new Promise((resolve) => {
      setTimeout(() => {
        if (savedPosition) {
          resolve(savedPosition);
        } else if (to.hash) {
          resolve({ selector: to.hash, behavior: 'smooth' });
        } else {
          resolve({ x: 0, y: 0, behavior: 'smooth' });
        }
      }, 500);
    });
  }
});
```

## Route Transitions

Add transition effects for route switching:

```typescript
// In main application
function renderApp(route) {
  const app = document.getElementById('app');
  
  // Apply transition effect
  const transition = route.meta.transition || 'fade';
  app.className = `transition-${transition}-enter`;
  
  // Load micro-application
  loadMicroApp(route.component)
    .then(microApp => {
      // Render micro-application
      microApp.mount(app);
      
      // Complete transition
      setTimeout(() => {
        app.className = `transition-${transition}-enter-active`;
      }, 20);
      
      setTimeout(() => {
        app.className = '';
      }, 300);
    });
}

// Listen to route changes
router.afterEach((to, from) => {
  renderApp(to);
});
```

## Router Modes

### History Mode

Use HTML5 History API to implement real URLs:

```typescript
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [...]
});
```

**Notes:**

1. Requires server configuration support, all routes should return the same HTML file
2. Server configuration examples:

```nginx
# Nginx configuration
location /app/ {
  try_files $uri $uri/ /app/index.html;
}
```

```apache
# Apache configuration
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /app/
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /app/index.html [L]
</IfModule>
```

### Hash Mode

Use the hash part of the URL to simulate a complete URL:

```typescript
const router = new Router({
  mode: 'hash',
  routes: [...]
});
```

**Advantages:**

1. No server configuration support required
2. Better compatibility, works with all browsers

**Disadvantages:**

1. URLs are not aesthetically pleasing, contain `#` symbol
2. Not SEO-friendly

## Micro-Frontend Router Integration

### Main Application and Micro-Application Route Synchronization

In micro-frontend architecture, route synchronization between main application and micro-applications is key:

```typescript
// In main application
const mainRouter = new Router({
  mode: 'history',
  base: '/',
  routes: [...]
});

// Register micro-application
registerMicroApp({
  name: 'user-app',
  entry: '//localhost:8081',
  container: '#micro-container',
  activeRule: '/users',
  props: {
    // Pass router instance to micro-application
    mainRouter
  }
});

// In micro-application
export async function bootstrap({ mainRouter }) {
  // Create micro-application router
  const microRouter = new Router({
    mode: 'history',
    base: '/users',
    routes: [...]
  });
  
  // Listen to main application route changes
  mainRouter.beforeEach((to, from, next) => {
    // If route belongs to current micro-application
    if (to.path.startsWith('/users')) {
      // Sync to micro-application router
      const microPath = to.path.replace('/users', '');
      microRouter.push(microPath);
    }
    next();
  });
  
  // Listen to micro-application route changes
  microRouter.beforeEach((to, from, next) => {
    // Sync to main application router
    const mainPath = '/users' + to.path;
    mainRouter.push(mainPath);
    next();
  });
}
```

### Route Isolation

To avoid route conflicts, you can use route isolation:

```typescript
// In main application
const mainRouter = new Router({
  mode: 'history',
  base: '/',
  routes: [...]
});

// In micro-application
export async function bootstrap() {
  // Create isolated micro-application router
  const microRouter = new Router({
    mode: 'hash', // Use hash mode to avoid conflicts with main application
    base: '/',
    routes: [...]
  });
}
```

### Route Communication

Route communication between main application and micro-applications:

```typescript
// In main application
const mainRouter = new Router({
  mode: 'history',
  base: '/',
  routes: [...]
});

// Publish route change events
mainRouter.afterEach((to, from) => {
  window.dispatchEvent(new CustomEvent('main-route-change', {
    detail: { to, from }
  }));
});

// In micro-application
export async function bootstrap() {
  // Create micro-application router
  const microRouter = new Router({
    mode: 'history',
    base: '/users',
    routes: [...]
  });
  
  // Listen to main application route change events
  window.addEventListener('main-route-change', (event) => {
    const { to, from } = event.detail;
    console.log('Main application route changed:', to.path);
    
    // Handle route change
    if (to.path.startsWith('/users')) {
      const microPath = to.path.replace('/users', '');
      microRouter.push(microPath);
    }
  });
  
  // Publish micro-application route change events
  microRouter.afterEach((to, from) => {
    window.dispatchEvent(new CustomEvent('micro-route-change', {
      detail: { to, from, appName: 'user-app' }
    }));
  });
}

// Listen to micro-application route changes in main application
window.addEventListener('micro-route-change', (event) => {
  const { to, from, appName } = event.detail;
  console.log(`Micro-application ${appName} route changed:`, to.path);
});
```

## Best Practices

### Route Organization

Organize routes by functional modules:

```typescript
// User module routes
const userRoutes = [
  {
    path: '/users',
    component: 'user-list-app',
    children: [
      { path: ':userId', component: 'user-detail-app' },
      { path: ':userId/edit', component: 'user-edit-app' }
    ]
  }
];

// Product module routes
const productRoutes = [
  {
    path: '/products',
    component: 'product-list-app',
    children: [
      { path: ':productId', component: 'product-detail-app' },
      { path: ':productId/edit', component: 'product-edit-app' }
    ]
  }
];

// Settings module routes
const settingRoutes = [
  {
    path: '/settings',
    component: 'settings-app',
    meta: { requiresAuth: true },
    children: [
      { path: 'profile', component: 'profile-app' },
      { path: 'security', component: 'security-app' },
      { path: 'notifications', component: 'notifications-app' }
    ]
  }
];

// Merge all routes
const routes = [
  { path: '/', component: 'home-app' },
  ...userRoutes,
  ...productRoutes,
  ...settingRoutes,
  { path: '*', component: 'not-found-app' }
];

// Create router instance
const router = new Router({
  mode: 'history',
  routes
});
```

### Permission Control

Role-based route permission control:

```typescript
// Route permission configuration
const routePermissions = {
  '/admin': ['admin'],
  '/users/manage': ['admin', 'manager'],
  '/settings/security': ['admin']
};

// Permission check function
function hasPermission(route, userRoles) {
  if (!routePermissions[route]) {
    return true; // No permission configuration, allow access by default
  }
  
  return routePermissions[route].some(role => userRoles.includes(role));
}

// Global before guard
router.beforeEach((to, from, next) => {
  const userRoles = getUserRoles(); // Get user roles
  
  if (hasPermission(to.path, userRoles)) {
    next(); // Has permission, continue navigation
  } else {
    next('/403'); // No permission, redirect to 403 page
  }
});

// Dynamically generate routes
function generateRoutesBasedOnRoles(roles) {
  const accessibleRoutes = allRoutes.filter(route => {
    if (route.meta && route.meta.roles) {
      return route.meta.roles.some(role => roles.includes(role));
    }
    return true; // No roles configuration, allow access by default
  });
  
  return accessibleRoutes;
}

// Dynamically add routes after user login
function addRoutesAfterLogin(user) {
  const accessibleRoutes = generateRoutesBasedOnRoles(user.roles);
  router.addRoutes(accessibleRoutes);
}
```

### Route Caching

Cache route components to improve performance:

```typescript
// In main application
const cachedComponents = new Map();

function loadCachedComponent(name) {
  if (cachedComponents.has(name)) {
    return Promise.resolve(cachedComponents.get(name));
  }
  
  return loadMicroApp(name)
    .then(component => {
      cachedComponents.set(name, component);
      return component;
    });
}

const router = new Router({
  routes: [
    {
      path: '/users',
      component: () => loadCachedComponent('users-app'),
      meta: { keepAlive: true }
    },
    {
      path: '/products',
      component: () => loadCachedComponent('products-app'),
      meta: { keepAlive: true }
    },
    {
      path: '/settings',
      component: () => loadCachedComponent('settings-app'),
      meta: { keepAlive: false } // Don't cache
    }
  ]
});

// Handle caching on route changes
router.beforeEach((to, from, next) => {
  // If leaving route doesn't need caching
  if (from.meta && from.meta.keepAlive === false) {
    // Clear cache
    const componentName = from.matched[0].components.default.name;
    if (cachedComponents.has(componentName)) {
      cachedComponents.delete(componentName);
    }
  }
  
  next();
});
```

### Route Analytics

Track route changes for analytics:

```typescript
// Route analytics middleware
router.afterEach((to, from) => {
  // Record route change
  trackRouteChange({
    from: from.path,
    to: to.path,
    timestamp: Date.now(),
    duration: performance.now() - routeStartTime,
    userId: getCurrentUserId()
  });
  
  // Page view event
  trackPageView({
    path: to.path,
    title: to.meta.title,
    referrer: from.path
  });
});

// Record time before route starts
let routeStartTime = 0;
router.beforeEach((to, from, next) => {
  routeStartTime = performance.now();
  next();
});

// Error tracking
router.onError((error) => {
  // Record route error
  trackRouteError({
    error: error.message,
    stack: error.stack,
    path: router.currentRoute.path
  });
});
```

### Route Testing

Test route configuration and navigation guards:

```typescript
// Route configuration testing
describe('Router Configuration', () => {
  let router;
  
  beforeEach(() => {
    router = new Router({
      mode: 'abstract', // Mode for testing
      routes: [...]
    });
  });
  
  test('should match home route', () => {
    router.push('/');
    expect(router.currentRoute.matched[0].path).toBe('/');
    expect(router.currentRoute.matched[0].component).toBe('home-app');
  });
  
  test('should match user detail route with params', () => {
    router.push('/users/123');
    expect(router.currentRoute.params.userId).toBe('123');
    expect(router.currentRoute.matched[0].path).toBe('/users/:userId');
  });
});

// Navigation guard testing
describe('Navigation Guards', () => {
  let router;
  let authService;
  
  beforeEach(() => {
    authService = {
      isAuthenticated: jest.fn(),
      hasRole: jest.fn()
    };
    
    router = new Router({
      mode: 'abstract',
      routes: [...]
    });
    
    router.beforeEach((to, from, next) => {
      if (to.meta.requiresAuth && !authService.isAuthenticated()) {
        next('/login');
      } else if (to.meta.roles && !authService.hasRole(to.meta.roles)) {
        next('/403');
      } else {
        next();
      }
    });
  });
  
  test('should redirect to login when route requires auth and user is not authenticated', () => {
    authService.isAuthenticated.mockReturnValue(false);
    
    router.push('/settings');
    expect(router.currentRoute.path).toBe('/login');
  });
  
  test('should allow navigation when user is authenticated', () => {
    authService.isAuthenticated.mockReturnValue(true);
    
    router.push('/settings');
    expect(router.currentRoute.path).toBe('/settings');
  });
});
```

## Integration with Other Frameworks

### Integration with qiankun

```typescript
// In main application
import { registerMicroApps, start } from 'qiankun';
import { Router } from '@micro-core/core';

// Create router instance
const router = new Router({
  mode: 'history',
  routes: [...]
});

// Register micro-applications
registerMicroApps([
  {
    name: 'user-app',
    entry: '//localhost:8081',
    container: '#micro-container',
    activeRule: '/users',
    props: {
      mainRouter: router // Pass router instance to micro-application
    }
  },
  {
    name: 'product-app',
    entry: '//localhost:8082',
    container: '#micro-container',
    activeRule: '/products',
    props: {
      mainRouter: router
    }
  }
]);

// Start qiankun
start();

// In micro-application
export async function bootstrap(props) {
  const { mainRouter } = props;
  
  // Create micro-application router
  const microRouter = new Router({
    mode: 'history',
    base: '/users', // Keep consistent with activeRule
    routes: [...]
  });
  
  // Router synchronization
  setupRouterSync(mainRouter, microRouter);
}

function setupRouterSync(mainRouter, microRouter) {
  // Sync from main application router to micro-application router
  mainRouter.afterEach((to) => {
    if (to.path.startsWith('/users')) {
      const microPath = to.path.replace('/users', '') || '/';
      if (microRouter.currentRoute.path !== microPath) {
        microRouter.push(microPath);
      }
    }
  });
  
  // Sync from micro-application router to main application router
  microRouter.afterEach((to) => {
    const mainPath = '/users' + (to.path === '/' ? '' : to.path);
    if (mainRouter.currentRoute.path !== mainPath) {
      mainRouter.push(mainPath);
    }
  });
}
```

### Integration with wujie

```typescript
// In main application
import { bus, setupApp, preloadApp, startApp } from 'wujie';
import { Router } from '@micro-core/core';

// Create router instance
const router = new Router({
  mode: 'history',
  routes: [...]
});

// Setup micro-application
setupApp({
  name: 'user-app',
  url: '//localhost:8081',
  exec: true,
  props: {
    mainRouter: router
  }
});

// Preload micro-application
preloadApp({
  name: 'user-app'
});

// Start micro-application in route guard
router.beforeEach((to, from, next) => {
  if (to.path.startsWith('/users')) {
    // Start micro-application
    startApp({
      name: 'user-app',
      container: '#micro-container',
      url: '//localhost:8081',
      props: {
        mainRouter: router,
        currentPath: to.path
      }
    });
  }
  next();
});

// Route communication
router.afterEach((to, from) => {
  if (to.path.startsWith('/users')) {
    // Send route change event through bus
    bus.$emit('main-route-change', {
      to,
      from
    });
  }
});

// In micro-application
export function bootstrap(props) {
  const { mainRouter, currentPath } = props;
  
  // Create micro-application router
  const microRouter = new Router({
    mode: 'history',
    base: '/users',
    routes: [...]
  });
  
  // Initialize route
  if (currentPath && currentPath.startsWith('/users')) {
    const microPath = currentPath.replace('/users', '') || '/';
    microRouter.push(microPath);
  }
  
  // Listen to main application route changes
  window.$wujie.bus.$on('main-route-change', ({ to, from }) => {
    if (to.path.startsWith('/users')) {
      const microPath = to.path.replace('/users', '') || '/';
      if (microRouter.currentRoute.path !== microPath) {
        microRouter.push(microPath);
      }
    }
  });
  
  // Sync micro-application route changes to main application
  microRouter.afterEach((to, from) => {
    const mainPath = '/users' + (to.path === '/' ? '' : to.path);
    if (mainRouter.currentRoute.path !== mainPath) {
      mainRouter.push(mainPath);
    }
  });
}
```

## Common Issues and Solutions

### Route Conflicts

**Issue**: Route conflicts between main application and micro-applications.

**Solutions**:

1. Use different router modes: main application uses history mode, micro-applications use hash mode
2. Set unique route prefixes for each micro-application
3. Use route synchronization mechanism to ensure consistent route state between main application and micro-applications

```typescript
// Main application uses history mode
const mainRouter = new Router({
  mode: 'history',
  routes: [...]
});

// Micro-application uses hash mode
const microRouter = new Router({
  mode: 'hash',
  routes: [...]
});

// Or set unique prefix for micro-application
const microRouter = new Router({
  mode: 'history',
  base: '/app1', // Unique prefix
  routes: [...]
});
```
