# Event Bus API

Micro-Core provides a powerful event bus system for communication and message passing between micro-applications.

## Basic Concepts

The event bus is an implementation of the publish-subscribe pattern that allows loose coupling communication between different micro-applications.

## EventBus Class

### Import and Creation

```typescript
import { EventBus } from '@micro-core/core';

// Create event bus instance
const eventBus = new EventBus();

// Use global event bus
import { globalEventBus } from '@micro-core/core';
```

### Constructor

```typescript
new EventBus(options?: EventBusOptions)
```

**Parameters:**
- `options.scope` (string): Event scope
- `options.maxListeners` (number): Maximum number of listeners, default 10

**Example:**

```typescript
const eventBus = new EventBus({
  scope: 'app1',
  maxListeners: 20
});
```

## Core Methods

### on(event, listener)

Subscribe to events.

```typescript
on<T = any>(event: string, listener: (data: T) => void): () => void
```

**Parameters:**
- `event` (string): Event name
- `listener` (function): Event listener function

**Returns:** Unsubscribe function

**Example:**

```typescript
// Basic subscription
const unsubscribe = eventBus.on('user:login', (data) => {
  console.log('User login:', data);
});

// Typed subscription
interface UserData {
  id: number;
  name: string;
}

const unsubscribe = eventBus.on<UserData>('user:login', (data) => {
  console.log(`User ${data.name} logged in`);
});

// Unsubscribe
unsubscribe();
```

### once(event, listener)

Subscribe to one-time events.

```typescript
once<T = any>(event: string, listener: (data: T) => void): () => void
```

**Example:**

```typescript
// Listen only once
eventBus.once('app:ready', () => {
  console.log('Application ready');
});
```

### off(event, listener?)

Unsubscribe from events.

```typescript
off(event: string, listener?: Function): void
```

**Example:**

```typescript
// Remove specific listener
eventBus.off('user:login', handleUserLogin);

// Remove all listeners for event
eventBus.off('user:login');
```

### emit(event, data?)

Publish events.

```typescript
emit<T = any>(event: string, data?: T): void
```

**Example:**

```typescript
// Emit event without data
eventBus.emit('app:initialized');

// Emit event with data
eventBus.emit('user:login', { 
  id: 1, 
  name: 'John' 
});
```

### clear()

Clear all event subscribers.

```typescript
clear(): void
```

## Utility Methods

### hasListeners(event)

Check if event has listeners.

```typescript
hasListeners(event: string): boolean
```

### getListeners(event)

Get all listeners for an event.

```typescript
getListeners(event: string): Function[]
```

### setMaxListeners(n)

Set maximum number of listeners.

```typescript
setMaxListeners(n: number): void
```

## Event Naming Conventions

Recommended naming conventions:

```typescript
// System events
eventBus.emit('system:app-mounted', { appName: 'user-center' });

// Business events - use domain:action format
eventBus.emit('user:login', { userId: 123 });
eventBus.emit('order:created', { orderId: 'ORD001' });

// UI events
eventBus.emit('ui:modal-opened', { modalId: 'confirm' });

// Error events
eventBus.emit('error:api-failed', { endpoint: '/api/users' });
```

## Inter-Application Communication

### Usage in Main Application

```typescript
import { globalEventBus } from '@micro-core/core';

// Listen to micro-application events
globalEventBus.on('app1:data-updated', (data) => {
  console.log('App1 data updated:', data);
});

// Send events to micro-applications
globalEventBus.emit('main:theme-changed', { theme: 'dark' });
```

### Usage in Micro-Applications

```typescript
// In micro-application lifecycle functions
export async function mount(props) {
  const { eventBus } = props;
  
  // Listen to main application events
  eventBus.on('main:theme-changed', (data) => {
    updateTheme(data.theme);
  });
  
  // Send events to main application
  eventBus.emit('app1:ready', { status: 'mounted' });
}
```

## Framework Integration

### React Hook

```typescript
import { useEffect, useCallback } from 'react';
import { globalEventBus } from '@micro-core/core';

export function useEventBus<T = any>(
  event: string, 
  handler: (data: T) => void, 
  deps: any[] = []
) {
  useEffect(() => {
    const unsubscribe = globalEventBus.on<T>(event, handler);
    return unsubscribe;
  }, [event, ...deps]);
  
  const emit = useCallback(<D = any>(eventName: string, data?: D) => {
    globalEventBus.emit(eventName, data);
  }, []);
  
  return { emit };
}

// Usage example
const MyComponent = () => {
  const { emit } = useEventBus('user:updated', (user) => {
    console.log('User updated:', user);
  });
  
  const handleClick = () => {
    emit('button:clicked', { id: 'btn1' });
  };
  
  return <button onClick={handleClick}>Click</button>;
};
```

### Vue Composable

```typescript
import { onMounted, onUnmounted } from 'vue';
import { globalEventBus } from '@micro-core/core';

export function useEventBus<T = any>(
  event: string, 
  handler?: (data: T) => void
) {
  let unsubscribe: (() => void) | null = null;
  
  onMounted(() => {
    if (handler) {
      unsubscribe = globalEventBus.on<T>(event, handler);
    }
  });
  
  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe();
    }
  });
  
  const emit = <D = any>(eventName: string, data?: D) => {
    globalEventBus.emit(eventName, data);
  };
  
  return { emit };
}
```

## Best Practices

### 1. Event Naming

- Use namespaces to avoid conflicts
- Use verb:noun format
- Maintain naming consistency

### 2. Error Handling

```typescript
eventBus.on('user:login', (data) => {
  try {
    processUserLogin(data);
  } catch (error) {
    console.error('User login processing failed:', error);
    eventBus.emit('user:login:error', { error, data });
  }
});
```

### 3. Unsubscribing

```typescript
class MyComponent {
  private unsubscribers: Array<() => void> = [];
  
  constructor() {
    this.unsubscribers.push(
      eventBus.on('event1', this.handleEvent1),
      eventBus.on('event2', this.handleEvent2)
    );
  }
  
  destroy() {
    this.unsubscribers.forEach(fn => fn());
    this.unsubscribers = [];
  }
}
```

### 4. Avoid Event Loops

```typescript
// Not recommended - may cause loops
eventBus.on('data:update', (data) => {
  updateData(data);
  eventBus.emit('data:update', newData); // Dangerous!
});

// Recommended
eventBus.on('data:update:requested', (data) => {
  const result = updateData(data);
  eventBus.emit('data:update:completed', result);
});
```

## Debugging

Enable debug mode to view event activity:

```typescript
const eventBus = new EventBus({
  debug: true
});

// Or listen to all events
eventBus.on('*', (data, eventName) => {
  console.log(`Event: ${eventName}`, data);
});
```

## Type Definitions

```typescript
interface EventBusOptions {
  scope?: string;
  maxListeners?: number;
  debug?: boolean;
}

type EventListener<T = any> = (data: T) => void;
type Unsubscribe = () => void;
```

## References

- [Core API](./core.md)
- [Application Communication Guide](../guide/communication.md)
- [Best Practices](../guide/best-practices.md)