# Router API (Part 3)

## Common Issues and Solutions

### Route Navigation Failures

**Issue**: Route navigation fails, unable to navigate to target route.

**Solutions**:

1. Check if route configuration is correct
2. Check if navigation guards are blocking navigation
3. Use route error handling

```typescript
// Register error handling
router.onError((error) => {
  console.error('Router error:', error);
  
  // Try to recover navigation
  if (error.type === 'NavigationDuplicated') {
    // Ignore duplicate navigation errors
    return;
  }
  
  // Navigate to error page
  router.push('/error');
});

// Add timeout handling
function navigateWithTimeout(location, timeout = 5000) {
  return Promise.race([
    router.push(location),
    new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Navigation timeout')), timeout);
    })
  ]).catch(error => {
    console.error('Navigation failed:', error);
    // Fallback to home page
    router.push('/');
  });
}
```

### Route Parameter Loss

**Issue**: Parameters are lost after route navigation.

**Solutions**:

1. Use named routes and params
2. Use query parameters
3. Use route meta information

```typescript
// Use named routes and params
router.push({
  name: 'user-detail',
  params: { userId: '123' }
});

// Use query parameters
router.push({
  path: '/users',
  query: { userId: '123' }
});

// Use route meta information
router.beforeEach((to, from, next) => {
  // Save parameters to meta information
  if (from.params.userId && !to.params.userId) {
    to.params.userId = from.params.userId;
  }
  next();
});
```

### Route State Loss After Page Refresh

**Issue**: Route state is lost after page refresh.

**Solutions**:

1. Use localStorage or sessionStorage to save route state
2. Use URL parameters to save state
3. Implement route persistence

```typescript
// Use localStorage to save route state
router.beforeEach((to, from, next) => {
  // Save route state
  localStorage.setItem('lastRoute', JSON.stringify({
    path: to.path,
    query: to.query,
    params: to.params
  }));
  next();
});

// Restore route state on application startup
function restoreRouteState() {
  const lastRoute = localStorage.getItem('lastRoute');
  if (lastRoute) {
    const { path, query, params } = JSON.parse(lastRoute);
    router.push({ path, query, params });
  }
}

// Route persistence
const persistedState = {
  route: {
    path: window.location.pathname,
    query: parseQuery(window.location.search),
    hash: window.location.hash
  }
};

// Use persisted state when creating router
const router = new Router({
  mode: 'history',
  routes: [...],
  // Use persisted state during initialization
  initialState: persistedState.route
});
```

### Navigation Between Micro-Applications

**Issue**: Navigating between different micro-applications.

**Solutions**:

1. Navigate through main application router
2. Use event communication
3. Use shared router service

```typescript
// Navigate through main application router
function navigateToAnotherApp(appName, path) {
  // Get main application router
  const mainRouter = window.mainRouter;
  
  // Build complete path
  const fullPath = `/${appName}${path}`;
  
  // Use main application router for navigation
  mainRouter.push(fullPath);
}

// Use event communication
function navigateToAnotherApp(appName, path) {
  // Publish navigation event
  window.dispatchEvent(new CustomEvent('navigate', {
    detail: { appName, path }
  }));
}

// Listen to navigation events in main application
window.addEventListener('navigate', (event) => {
  const { appName, path } = event.detail;
  const fullPath = `/${appName}${path}`;
  router.push(fullPath);
});

// Use shared router service
const routerService = {
  routers: new Map(),
  
  // Register router
  register(appName, router) {
    this.routers.set(appName, router);
  },
  
  // Navigate to specified application route
  navigateTo(appName, path) {
    const targetRouter = this.routers.get(appName);
    if (targetRouter) {
      targetRouter.push(path);
    } else {
      // Fallback to main application router
      const mainRouter = this.routers.get('main');
      mainRouter.push(`/${appName}${path}`);
    }
  }
};

// Register main application router
routerService.register('main', mainRouter);

// Register router in micro-application
routerService.register('user-app', microRouter);

// Navigate to another micro-application
routerService.navigateTo('product-app', '/detail/123');
```

### Route Permission Control

**Issue**: How to implement complex route permission control.

**Solutions**:

1. Use route meta information and navigation guards
2. Implement dynamic route generation
3. Use route permission middleware

```typescript
// Use route meta information and navigation guards
const router = new Router({
  routes: [
    {
      path: '/admin',
      component: 'admin-app',
      meta: {
        requiresAuth: true,
        roles: ['admin']
      }
    },
    {
      path: '/users',
      component: 'users-app',
      meta: {
        requiresAuth: true,
        roles: ['admin', 'manager']
      }
    }
  ]
});

// Global before guard
router.beforeEach((to, from, next) => {
  const { requiresAuth, roles } = to.meta;
  
  // Check if authentication is required
  if (requiresAuth) {
    if (!isAuthenticated()) {
      // Save target route
      saveTargetRoute(to.fullPath);
      // Redirect to login page
      return next('/login');
    }
    
    // Check role permissions
    if (roles && !hasRole(roles)) {
      return next('/403');
    }
  }
  
  next();
});

// Dynamic route generation
const asyncRoutes = [
  {
    path: '/admin',
    component: 'admin-app',
    meta: { roles: ['admin'] },
    children: [
      {
        path: 'users',
        component: 'admin-users-app',
        meta: { roles: ['admin'] }
      },
      {
        path: 'settings',
        component: 'admin-settings-app',
        meta: { roles: ['admin'] }
      }
    ]
  },
  {
    path: '/users',
    component: 'users-app',
    meta: { roles: ['admin', 'manager'] }
  }
];

// Filter routes based on user roles
function filterAsyncRoutes(routes, roles) {
  const res = [];
  
  routes.forEach(route => {
    const tmp = { ...route };
    
    // Check if has permission to access
    if (hasPermission(roles, tmp)) {
      // Handle child routes
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles);
      }
      
      res.push(tmp);
    }
  });
  
  return res;
}

// Check permissions
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role));
  }
  
  return true; // No permission requirements set, allow access by default
}

// Generate accessible routes
function generateAccessibleRoutes(roles) {
  const accessibleRoutes = filterAsyncRoutes(asyncRoutes, roles);
  return accessibleRoutes;
}

// Dynamically add routes after user login
function addRoutesAfterLogin(user) {
  const accessibleRoutes = generateAccessibleRoutes(user.roles);
  router.addRoutes(accessibleRoutes);
}

// Route permission middleware
const authMiddleware = {
  // Check authentication
  checkAuth(to, from, next) {
    if (to.meta.requiresAuth && !isAuthenticated()) {
      saveTargetRoute(to.fullPath);
      return next('/login');
    }
    
    next();
  },
  
  // Check role
  checkRole(to, from, next) {
    if (to.meta.roles && !hasRole(to.meta.roles)) {
      return next('/403');
    }
    
    next();
  },
  
  // Check permissions
  checkPermission(to, from, next) {
    if (to.meta.permissions && !hasPermission(to.meta.permissions)) {
      return next('/403');
    }
    
    next();
  }
};

// Apply middleware
router.beforeEach(authMiddleware.checkAuth);
router.beforeEach(authMiddleware.checkRole);
router.beforeEach(authMiddleware.checkPermission);
```

### Route Performance Optimization

**Issue**: Poor route navigation performance affecting user experience.

**Solutions**:

1. Use route lazy loading
2. Implement route preloading
3. Optimize route configuration

```typescript
// Use route lazy loading
const router = new Router({
  routes: [
    {
      path: '/',
      component: 'home-app' // Direct loading
    },
    {
      path: '/users',
      component: () => loadMicroApp('users-app') // Lazy loading
    }
  ]
});

// Implement route preloading
const preloadedApps = new Set();

// Preload function
function preloadMicroApp(name) {
  if (preloadedApps.has(name)) {
    return Promise.resolve();
  }
  
  return loadMicroApp(name, { mount: false })
    .then(() => {
      preloadedApps.add(name);
    });
}

// Preload during idle time
if ('requestIdleCallback' in window) {
  requestIdleCallback(() => {
    preloadMicroApp('users-app');
    preloadMicroApp('settings-app');
  });
} else {
  setTimeout(() => {
    preloadMicroApp('users-app');
    preloadMicroApp('settings-app');
  }, 1000);
}

// Optimize route configuration
const router = new Router({
  routes: [
    // Use more precise route matching
    { path: '/users/:userId(\\d+)', component: 'user-app' },
    
    // Reduce nesting levels
    { path: '/settings/profile', component: 'profile-app' },
    { path: '/settings/security', component: 'security-app' },
    
    // Use route meta information for optimization
    {
      path: '/dashboard',
      component: 'dashboard-app',
      meta: {
        keepAlive: true, // Cache component
        transition: 'fade' // Transition effect
      }
    }
  ]
});
```

## Advanced Features

### Route Meta Information Extension

Extend route meta information to support more features:

```typescript
const router = new Router({
  routes: [
    {
      path: '/users',
      component: 'users-app',
      meta: {
        // Basic information
        title: 'User Management',
        icon: 'user',
        
        // Permission control
        requiresAuth: true,
        roles: ['admin', 'manager'],
        permissions: ['user:view', 'user:edit'],
        
        // Cache control
        keepAlive: true,
        cacheTimeout: 300000, // 5 minutes
        
        // Transition effects
        transition: 'fade',
        transitionDuration: 300,
        
        // Layout control
        layout: 'admin',
        sidebar: true,
        
        // Data preloading
        fetchData: true,
        fetchFunction: 'fetchUserList',
        
        // Breadcrumbs
        breadcrumb: [
          { name: 'Home', path: '/' },
          { name: 'User Management' }
        ],
        
        // Analytics tracking
        track: true,
        trackEvent: 'page_view',
        trackCategory: 'user_management'
      }
    }
  ]
});

// Use meta information
router.beforeEach((to, from, next) => {
  // Set page title
  if (to.meta.title) {
    document.title = to.meta.title;
  }
  
  // Permission check
  if (to.meta.requiresAuth && !isAuthenticated()) {
    return next('/login');
  }
  
  // Role check
  if (to.meta.roles && !hasRole(to.meta.roles)) {
    return next('/403');
  }
  
  // Permission check
  if (to.meta.permissions && !hasPermission(to.meta.permissions)) {
    return next('/403');
  }
  
  next();
});

router.afterEach((to, from) => {
  // Set layout
  if (to.meta.layout) {
    setLayout(to.meta.layout);
  }
  
  // Set sidebar
  if (to.meta.sidebar !== undefined) {
    setSidebar(to.meta.sidebar);
  }
  
  // Set breadcrumbs
  if (to.meta.breadcrumb) {
    setBreadcrumb(to.meta.breadcrumb);
  }
  
  // Analytics tracking
  if (to.meta.track) {
    trackPageView({
      event: to.meta.trackEvent || 'page_view',
      category: to.meta.trackCategory,
      path: to.path,
      title: to.meta.title
    });
  }
});
```

### Route Middleware

Implement route middleware system:

```typescript
// Middleware manager
class RouterMiddlewareManager {
  constructor(router) {
    this.router = router;
    this.middlewares = [];
    
    // Register global before guard
    this.router.beforeEach(this.handle.bind(this));
  }
  
  // Add middleware
  use(middleware) {
    this.middlewares.push(middleware);
    return this;
  }
  
  // Handle middleware
  handle(to, from, next) {
    const middlewares = [...this.middlewares];
    
    // Execute middleware chain
    const runMiddleware = (index) => {
      // All middlewares have been executed
      if (index >= middlewares.length) {
        return next();
      }
      
      // Execute current middleware
      const middleware = middlewares[index];
      middleware(to, from, () => runMiddleware(index + 1), this.router);
    };
    
    runMiddleware(0);
  }
}

// Create router instance
const router = new Router({
  mode: 'history',
  routes: [...]
});

// Create middleware manager
const middlewareManager = new RouterMiddlewareManager(router);

// Authentication middleware
const authMiddleware = (to, from, next, router) => {
  if (to.meta.requiresAuth && !isAuthenticated()) {
    return next('/login');
  }
  
  next();
};

// Role middleware
const roleMiddleware = (to, from, next, router) => {
  if (to.meta.roles && !hasRole(to.meta.roles)) {
    return next('/403');
  }
  
  next();
};

// Logging middleware
const logMiddleware = (to, from, next, router) => {
  console.log(`Route navigation: ${from.path} -> ${to.path}`);
  next();
};

// Performance monitoring middleware
const performanceMiddleware = (to, from, next, router) => {
  const startTime = performance.now();
  
  // Record performance after navigation completes
  const unregister = router.afterEach(() => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`Route navigation time: ${duration.toFixed(2)}ms`);
    
    // Remove after hook
    unregister();
  });
  
  next();
};

// Register middleware
middlewareManager
  .use(logMiddleware)
  .use(performanceMiddleware)
  .use(authMiddleware)
  .use(roleMiddleware);
```

### Route Plugin System

Implement route plugin system:

```typescript
// Route plugin manager
class RouterPluginManager {
  constructor(router) {
    this.router = router;
    this.plugins = new Map();
  }
  
  // Register plugin
  register(name, plugin) {
    if (this.plugins.has(name)) {
      console.warn(`Plugin "${name}" is already registered and will be overwritten`);
    }
    
    this.plugins.set(name, plugin);
    
    // Initialize plugin
    if (typeof plugin.install === 'function') {
      plugin.install(this.router);
    }
    
    return this;
  }
  
  // Get plugin
  getPlugin(name) {
    return this.plugins.get(name);
  }
  
  // Unregister plugin
  unregister(name) {
    const plugin = this.plugins.get(name);
    
    if (plugin && typeof plugin.uninstall === 'function') {
      plugin.uninstall(this.router);
    }
    
    this.plugins.delete(name);
    return this;
  }
}

// Create router instance
const router = new Router({
  mode: 'history',
  routes: [...]
});

// Create plugin manager
const pluginManager = new RouterPluginManager(router);

// Breadcrumb plugin
const breadcrumbPlugin = {
  install(router) {
    // Add global after hook
    this.unregisterHook = router.afterEach((to) => {
      // Generate breadcrumbs
      const breadcrumbs = [];
      
      // Add home
      breadcrumbs.push({
        name: 'Home',
        path: '/'
      });
      
      // Generate breadcrumbs based on route matching
      to.matched.forEach(route => {
        if (route.meta && route.meta.title) {
          breadcrumbs.push({
            name: route.meta.title,
            path: route.path
          });
        }
      });
      
      // Update breadcrumbs
      updateBreadcrumbs(breadcrumbs);
    });
  },
  
  uninstall(router) {
    // Remove hook
    if (this.unregisterHook) {
      this.unregisterHook();
    }
  }
};

// Progress bar plugin
const progressPlugin = {
  install(router) {
    // Create progress bar
    const progressBar = createProgressBar();
    
    // Add global before guard
    this.beforeHook = router.beforeEach((to, from, next) => {
      // Start progress bar
      progressBar.start();
      next();
    });
    
    // Add global after hook
    this.afterHook = router.afterEach(() => {
      // Complete progress bar
      progressBar.finish();
    });
    
    // Add error handling
    this.errorHook = router.onError(() => {
      // Stop progress bar on error
      progressBar.fail();
    });
  },
  
  uninstall(router) {
    // Remove hooks
    if (this.beforeHook) this.beforeHook();
    if (this.afterHook) this.afterHook();
    if (this.errorHook) this.errorHook();
  }
};

// Analytics plugin
const analyticsPlugin = {
  install(router) {
    // Add global after hook
    this.unregisterHook = router.afterEach((to, from) => {
      // Send page view event
      trackPageView({
        path: to.path,
        title: to.meta.title || document.title,
        referrer: from.path
      });
    });
  },
  
  uninstall(router) {
    // Remove hook
    if (this.unregisterHook) {
      this.unregisterHook();
    }
  }
};

// Register plugins
pluginManager
  .register('breadcrumb', breadcrumbPlugin)
  .register('progress', progressPlugin)
  .register('analytics', analyticsPlugin);
```

### Route History Management

Implement route history management:

```typescript
// Route history manager
class RouterHistoryManager {
  constructor(router, options = {}) {
    this.router = router;
    this.options = {
      maxSize: options.maxSize || 50,
      storageKey: options.storageKey || 'router_history',
      persist: options.persist !== false
    };
    
    // History records
    this.history = [];
    
    // Restore history from storage
    if (this.options.persist) {
      this.restoreFromStorage();
    }
    
    // Listen to route changes
    this.unregisterHook = router.afterEach((to) => {
      this.addToHistory(to);
    });
  }
  
  // Add to history
  addToHistory(route) {
    // Create history item
    const historyItem = {
      path: route.path,
      fullPath: route.fullPath,
      name: route.name,
      params: { ...route.params },
      query: { ...route.query },
      meta: route.meta ? { ...route.meta } : {},
      timestamp: Date.now()
    };
    
    // Check if same route already exists
    const existingIndex = this.history.findIndex(item => item.fullPath === historyItem.fullPath);
    if (existingIndex !== -1) {
      // Remove existing route
      this.history.splice(existingIndex, 1);
    }
    
    // Add to history
    this.history.unshift(historyItem);
    
    // Limit history size
    if (this.history.length > this.options.maxSize) {
      this.history = this.history.slice(0, this.options.maxSize);
    }
    
    // Save to storage
    if (this.options.persist) {
      this.saveToStorage();
    }
  }
  
  // Get history
  getHistory() {
    return [...this.history];
  }
  
  // Clear history
  clearHistory() {
    this.history = [];
    
    // Clear storage
    if (this.options.persist) {
      localStorage.removeItem(this.options.storageKey);
    }
  }
  
  // Navigate to history item
  navigateTo(index) {
    if (index >= 0 && index < this.history.length) {
      const item = this.history[index];
      this.router.push({
        path: item.path,
        params: item.params,
        query: item.query
      });
      return true;
    }
    return false;
  }
  
  // Restore from storage
  restoreFromStorage() {
    try {
      const stored = localStorage.getItem(this.options.storageKey);
      if (stored) {
        this.history = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to restore route history:', error);
      this.history = [];
    }
  }
  
  // Save to storage
  saveToStorage() {
    try {
      localStorage.setItem(this.options.storageKey, JSON.stringify(this.history));
    } catch (error) {
      console.error('Failed to save route history:', error);
    }
  }
  
  // Destroy
  destroy() {
    if (this.unregisterHook) {
      this.unregisterHook();
    }
  }
}

// Create router instance
const router = new Router({
  mode: 'history',
  routes: [...]
});

// Create history manager
const historyManager = new RouterHistoryManager(router, {
  maxSize: 100,
  storageKey: 'app_router_history',
  persist: true
});

// Use history
function showHistory() {
  const history = historyManager.getHistory();
  
  // Display history
  const historyList = history.map((item, index) => {
    return {
      index,
      path: item.path,
      title: item.meta.title || item.path,
      timestamp: new Date(item.timestamp).toLocaleString()
    };
  });
  
  return historyList;
}

// Navigate to history item
function navigateToHistoryItem(index) {
  historyManager.navigateTo(index);
}

// Clear history
function clearHistory() {
  historyManager.clearHistory();
}
```

## Summary

The Micro-Core Router API provides powerful and flexible routing management capabilities suitable for various scenarios in micro-frontend architecture. Through this documentation, you can understand the basic usage, advanced features, and best practices of the Router API, helping you build efficient and reliable micro-frontend applications.

Main features include:

1. **Flexible Router Modes**: Support for history and hash modes
2. **Powerful Route Matching**: Support for static paths, dynamic parameters, nested routes, and wildcards
3. **Complete Navigation Guards**: Provide global guards, per-route guards, and in-component guards
4. **Route Meta Information**: Support for attaching arbitrary information to routes
5. **Route Lazy Loading**: Improve application performance
6. **Scroll Behavior Control**: Customize page scroll behavior
7. **Route Transition Effects**: Add animation effects for route switching
8. **Micro-Frontend Router Integration**: Integration with micro-frontend frameworks like qiankun and wujie

By properly using these features, you can build micro-frontend applications with good user experience and excellent performance.
