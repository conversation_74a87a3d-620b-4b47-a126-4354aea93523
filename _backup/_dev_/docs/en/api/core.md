# Core API

This section provides detailed documentation for Micro-Core's core API, including main classes, interfaces, and methods.

## MicroCore

`MicroCore` is the core class of Micro-Core, responsible for managing the entire lifecycle of micro-applications.

### Constructor

```typescript
constructor(config?: MicroCoreConfig)
```

#### Parameters

- `config` - Optional configuration options

```typescript
interface MicroCoreConfig {
  // Container element
  container?: string | HTMLElement
  
  // Debug mode
  debug?: boolean
  
  // Router configuration
  router?: RouterConfig
  
  // Sandbox configuration
  sandbox?: SandboxConfig
  
  // Prefetch configuration
  prefetch?: PrefetchConfig
  
  // Error handler configuration
  errorHandler?: ErrorHandlerConfig
  
  // Global state
  globalState?: Record<string, any>
  
  // Custom fetcher
  fetch?: (url: string, options?: RequestInit) => Promise<Response>
}
```

#### Example

```typescript
import { MicroCore, type MicroCoreConfig } from '@micro-core/core'

const config: MicroCoreConfig = {
  container: '#app',
  debug: process.env.NODE_ENV === 'development',
  router: {
    mode: 'history',
    base: '/'
  },
  sandbox: {
    type: 'proxy',
    css: true,
    js: true
  },
  errorHandler: {
    onJSError: (error, app) => {
      console.error(`JS error in app ${app?.name}:`, error)
    },
    onLoadError: (error, app) => {
      console.error(`Load error in app ${app?.name}:`, error)
    }
  }
}

const microCore = new MicroCore(config)
```

### Methods

#### registerApp

Register a micro-application.

```typescript
registerApp(config: AppConfig): Promise<void>
```

##### Parameters

```typescript
interface AppConfig {
  // Application name (required, unique)
  name: string
  
  // Application entry (required)
  entry: string | AppEntry
  
  // Mount container (optional, uses global container by default)
  container?: string | HTMLElement | (() => HTMLElement)
  
  // Activation condition (required)
  activeRule: string | RegExp | Array<string | RegExp> | ((location: Location) => boolean)
  
  // Application properties (optional)
  props?: Record<string, any>
  
  // Loader (optional)
  loader?: () => Promise<LifecycleFns>
  
  // Custom fetcher (optional)
  fetch?: (url: string, options?: RequestInit) => Promise<Response>
  
  // Sandbox configuration (optional)
  sandbox?: SandboxConfig
  
  // Lifecycle hooks
  beforeBootstrap?: LifecycleHook
  afterBootstrap?: LifecycleHook
  beforeMount?: LifecycleHook
  afterMount?: LifecycleHook
  beforeUnmount?: LifecycleHook
  afterUnmount?: LifecycleHook
  
  // Error boundary (optional)
  errorBoundary?: ErrorBoundaryConfig
}
```

##### Example

```typescript
// Basic registration
await microCore.registerApp({
  name: 'user-center',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeRule: '/user'
})

// Advanced configuration
await microCore.registerApp({
  name: 'order-system',
  entry: {
    scripts: ['http://localhost:3002/static/js/main.js'],
    styles: ['http://localhost:3002/static/css/main.css']
  },
  container: () => document.getElementById('order-container'),
  activeRule: ['/order', '/orders', /^\/order\//],
  props: {
    theme: 'dark',
    apiBaseUrl: process.env.API_BASE_URL
  },
  sandbox: {
    type: 'proxy',
    css: true,
    js: true
  },
  beforeMount: (app) => {
    console.log(`App ${app.name} is about to mount`)
  },
  afterMount: (app) => {
    console.log(`App ${app.name} has been mounted`)
  },
  errorBoundary: {
    fallback: '<div class="error">Order system temporarily unavailable</div>',
    onError: (error) => {
      console.error('Order system error:', error)
    }
  }
})
```

#### unregisterApp

Unregister a micro-application.

```typescript
unregisterApp(name: string): Promise<void>
```

##### Parameters

- `name` - Application name

##### Example

```typescript
await microCore.unregisterApp('user-center')
```

#### start

Start the micro-frontend system.

```typescript
start(options?: StartOptions): Promise<void>
```

##### Parameters

```typescript
interface StartOptions {
  // Whether to preload all applications
  preload?: boolean
  
  // Initial route
  initialRoute?: string
  
  // Start callback
  onStart?: () => void
}
```

##### Example

```typescript
await microCore.start({
  preload: true,
  initialRoute: '/dashboard',
  onStart: () => {
    console.log('Micro-frontend system started')
  }
})
```

#### loadApp

Manually load a micro-application.

```typescript
loadApp(name: string, props?: Record<string, any>): Promise<void>
```

##### Parameters

- `name` - Application name
- `props` - Optional properties object

##### Example

```typescript
await microCore.loadApp('user-center', {
  userId: 123,
  theme: 'light'
})
```

#### unloadApp

Manually unload a micro-application.

```typescript
unloadApp(name: string): Promise<void>
```

##### Parameters

- `name` - Application name

##### Example

```typescript
await microCore.unloadApp('user-center')
```

#### preloadApp

Preload a micro-application.

```typescript
preloadApp(name: string): Promise<void>
```

##### Parameters

- `name` - Application name

##### Example

```typescript
// Preload user center application
await microCore.preloadApp('user-center')
```

#### prefetchApp

Prefetch resources for a micro-application.

```typescript
prefetchApp(name: string): Promise<void>
```

##### Parameters

- `name` - Application name

##### Example

```typescript
// Prefetch order system resources
await microCore.prefetchApp('order-system')
```

#### getApp

Get information about a registered micro-application.

```typescript
getApp(name: string): MicroApp | undefined
```

##### Parameters

- `name` - Application name

##### Return Value

Returns the micro-application object, or `undefined` if it doesn't exist.

##### Example

```typescript
const app = microCore.getApp('user-center')
if (app) {
  console.log('Application entry:', app.entry)
  console.log('Activation rule:', app.activeRule)
}
```

#### getApps

Get all registered micro-applications.

```typescript
getApps(): MicroApp[]
```

##### Return Value

Returns an array of all micro-applications.

##### Example

```typescript
const apps = microCore.getApps()
console.log('Number of registered apps:', apps.length)
apps.forEach(app => {
  console.log(`App: ${app.name}, Status: ${app.status}`)
})
```

#### getActiveApps

Get currently active micro-applications.

```typescript
getActiveApps(): MicroApp[]
```

##### Return Value

Returns an array of currently active micro-applications.

##### Example

```typescript
const activeApps = microCore.getActiveApps()
console.log('Currently active apps:', activeApps.map(app => app.name))
```

#### use

Register a plugin.

```typescript
use<T extends MicroCorePlugin>(plugin: T | PluginConstructor<T>, options?: any): void
```

##### Parameters

- `plugin` - Plugin instance or constructor
- `options` - Optional plugin configuration

##### Example

```typescript
import { RouterPlugin } from '@micro-core/plugin-router'
import { CommunicationPlugin } from '@micro-core/plugin-communication'

// Use plugin instance
microCore.use(new RouterPlugin({
  mode: 'history',
  base: '/'
}))

// Use plugin constructor
microCore.use(CommunicationPlugin, {
  enableGlobalState: true,
  enableEventBus: true
})
```

#### on

Listen to events.

```typescript
on(event: string, listener: (...args: any[]) => void): void
```

##### Parameters

- `event` - Event name
- `listener` - Event listener

##### Example

```typescript
// Listen to app mount events
microCore.on('app-mount', (app) => {
  console.log(`App ${app.name} has been mounted`)
})

// Listen to app unmount events
microCore.on('app-unmount', (app) => {
  console.log(`App ${app.name} has been unmounted`)
})

// Listen to route change events
microCore.on('route-change', (route) => {
  console.log('Route changed:', route)
})
```

#### off

Remove event listeners.

```typescript
off(event: string, listener?: (...args: any[]) => void): void
```

##### Parameters

- `event` - Event name
- `listener` - Optional event listener, removes all listeners if not provided

##### Example

```typescript
const handleAppMount = (app) => {
  console.log(`App ${app.name} has been mounted`)
}

// Add listener
microCore.on('app-mount', handleAppMount)

// Remove specific listener
microCore.off('app-mount', handleAppMount)

// Remove all listeners
microCore.off('app-mount')
```

#### emit

Trigger events.

```typescript
emit(event: string, ...args: any[]): void
```

##### Parameters

- `event` - Event name
- `args` - Event arguments

##### Example

```typescript
// Trigger custom event
microCore.emit('custom-event', { data: 'hello world' })

// Trigger app event
microCore.emit('app-ready', app)
```

### Properties

#### version

Get Micro-Core version.

```typescript
readonly version: string
```

##### Example

```typescript
console.log('Micro-Core version:', microCore.version)
```

#### status

Get system status.

```typescript
readonly status: 'not-started' | 'starting' | 'started' | 'stopped'
```

##### Example

```typescript
console.log('System status:', microCore.status)
```

## Lifecycle Functions

Micro-applications need to export standard lifecycle functions:

### bootstrap

Called when the application starts, used to initialize the application.

```typescript
export async function bootstrap(props: any): Promise<void> {
  console.log('Application bootstrap', props)
  // Initialization logic
}
```

### mount

Called when the application is mounted, used to render the application to the DOM.

```typescript
export async function mount(props: any): Promise<void> {
  console.log('Application mount', props)
  
  const container = props.container
  // Render application to container
  ReactDOM.render(<App {...props} />, container)
}
```

### unmount

Called when the application is unmounted, used to clean up resources.

```typescript
export async function unmount(props: any): Promise<void> {
  console.log('Application unmount', props)
  
  const container = props.container
  // Clean up DOM
  ReactDOM.unmountComponentAtNode(container)
}
```

### update

Called when the application is updated, used to handle property changes.

```typescript
export async function update(props: any): Promise<void> {
  console.log('Application update', props)
  // Handle property updates
}
```

## Type Definitions

### AppEntry

```typescript
type AppEntry = string | {
  scripts?: string[]
  styles?: string[]
  html?: string
}
```

### SandboxConfig

```typescript
interface SandboxConfig {
  // Sandbox type
  type?: 'proxy' | 'snapshot' | 'iframe'
  
  // CSS isolation
  css?: boolean | {
    enabled: boolean
    prefix?: string
  }
  
  // JavaScript isolation
  js?: boolean | {
    enabled: boolean
    strict?: boolean
  }
  
  // Global variable whitelist
  globalWhitelist?: string[]
  
  // Global variable blacklist
  globalBlacklist?: string[]
}
```

### ErrorBoundaryConfig

```typescript
interface ErrorBoundaryConfig {
  // Error fallback content
  fallback?: string | HTMLElement | (() => HTMLElement)
  
  // Error handler function
  onError?: (error: Error, errorInfo: any) => void
}
```

### ErrorHandlerConfig

```typescript
interface ErrorHandlerConfig {
  // JavaScript error handler
  onJSError?: (error: Error, app?: MicroApp) => void
  
  // Load error handler
  onLoadError?: (error: Error, app?: MicroApp) => void
  
  // Application error handler
  onAppError?: (error: Error, app: MicroApp) => void
}
```

### RouterConfig

```typescript
interface RouterConfig {
  // Router mode
  mode?: 'hash' | 'history' | 'memory'
  
  // Base path
  base?: string
  
  // Link active class
  linkActiveClass?: string
}
```

### PrefetchConfig

```typescript
interface PrefetchConfig {
  // Preload during idle time
  idle?: string[]
  
  // Preload on hover
  hover?: string[] | {
    apps: string[]
    delay?: number
  }
  
  // Preload in viewport
  viewport?: string[]
}
```

## Event System

Micro-Core includes a complete event system that supports the following events:

### System Events

- `system-start` - System start
- `system-stop` - System stop
- `route-change` - Route change

### Application Events

- `app-register` - Application registration
- `app-unregister` - Application unregistration
- `app-load` - Application load
- `app-mount` - Application mount
- `app-unmount` - Application unmount
- `app-update` - Application update
- `app-error` - Application error

### Custom Events

You can trigger and listen to custom events:

```typescript
// Trigger custom event
microCore.emit('user-login', { userId: 123 })

// Listen to custom event
microCore.on('user-login', (data) => {
  console.log('User login:', data.userId)
})
```

## Error Handling

Micro-Core provides multi-level error handling mechanisms:

### Global Error Handling

```typescript
const microCore = new MicroCore({
  errorHandler: {
    onJSError: (error, app) => {
      // JavaScript error handling
      console.error('JS error:', error)
      
      // Error reporting
      reportError(error, app)
      
      // Show error message
      showErrorMessage('System error occurred, please try again later')
    },
    
    onLoadError: (error, app) => {
      // Load error handling
      console.error('Load error:', error)
      
      // Show fallback UI
      showFallbackUI(app)
    },
    
    onAppError: (error, app) => {
      // Application error handling
      console.error('Application error:', error)
      
      // Restart application
      microCore.restartApp(app.name)
    }
  }
})
```

### Application-level Error Handling

```typescript
await microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeRule: '/user',
  errorBoundary: {
    fallback: '<div class="error">User center temporarily unavailable</div>',
    onError: (error, errorInfo) => {
      console.error('User application error:', error, errorInfo)
    }
  }
})
```

### Error Recovery

```typescript
// Listen to error events
microCore.on('app-error', async (error, app) => {
  console.error(`Error in app ${app.name}:`, error)
  
  // Try to reload the application
  try {
    await microCore.unloadApp(app.name)
    await microCore.loadApp(app.name)
    console.log(`App ${app.name} reloaded successfully`)
  } catch (reloadError) {
    console.error(`App ${app.name} reload failed:`, reloadError)
  }
})
```

## Next Steps

After understanding the core API, you can:

1. Check [Application Management API](./app-management) to learn detailed application management interfaces
2. Learn [Routing System API](./routing) to master routing-related APIs
3. Explore [Communication System API](./communication) to understand inter-application communication interfaces
4. Read [Plugin API](./plugin-api) to learn how to develop custom plugins

If you encounter issues during usage, please check [FAQ](../guide/troubleshooting) or submit an Issue on GitHub.