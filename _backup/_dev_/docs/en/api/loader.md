# Loader API

Micro-Core provides a flexible loader system for loading and managing micro-application resources.

## Resource Loader

### Basic Usage

```typescript
import { ResourceLoader } from '@micro-core/core';

const loader = new ResourceLoader({
  cache: true,
  timeout: 10000,
  retries: 3
});

// Load JavaScript
const script = await loader.loadScript('https://example.com/app.js');

// Load CSS
const style = await loader.loadStyle('https://example.com/app.css');

// Load HTML
const html = await loader.loadHTML('https://example.com/app.html');
```

## Loading Methods

### loadScript()

Load JavaScript files.

```typescript
loadScript(url: string, options?: ScriptLoadOptions): Promise<HTMLScriptElement>
```

**Options:**

```typescript
interface ScriptLoadOptions {
  async?: boolean;
  defer?: boolean;
  crossOrigin?: string;
  integrity?: string;
  timeout?: number;
  retries?: number;
  cache?: boolean;
}
```

**Example:**

```typescript
// Basic script loading
const script = await loader.loadScript('/static/js/app.js');

// Advanced options
const script = await loader.loadScript('/static/js/app.js', {
  async: true,
  crossOrigin: 'anonymous',
  integrity: 'sha384-...',
  timeout: 5000,
  retries: 2
});
```

### loadStyle()

Load CSS files.

```typescript
loadStyle(url: string, options?: StyleLoadOptions): Promise<HTMLLinkElement>
```

**Options:**

```typescript
interface StyleLoadOptions {
  media?: string;
  crossOrigin?: string;
  integrity?: string;
  timeout?: number;
  retries?: number;
  cache?: boolean;
}
```

**Example:**

```typescript
// Basic style loading
const style = await loader.loadStyle('/static/css/app.css');

// With media query
const style = await loader.loadStyle('/static/css/mobile.css', {
  media: '(max-width: 768px)'
});
```

### loadHTML()

Load HTML content.

```typescript
loadHTML(url: string, options?: HTMLLoadOptions): Promise<string>
```

**Example:**

```typescript
// Load HTML template
const html = await loader.loadHTML('/templates/app.html');

// Parse and use
const parser = new DOMParser();
const doc = parser.parseFromString(html, 'text/html');
```

## Application Loader

### Entry Loading

```typescript
import { AppLoader } from '@micro-core/core';

const appLoader = new AppLoader();

// Load application entry
const app = await appLoader.loadApp({
  name: 'user-app',
  entry: 'https://localhost:3001',
  container: '#user-container'
});
```

### Entry Types

#### HTML Entry

```typescript
// Load from HTML entry
const app = await appLoader.loadApp({
  name: 'html-app',
  entry: 'https://localhost:3001/index.html',
  container: '#html-container'
});
```

#### JavaScript Entry

```typescript
// Load from JS entry
const app = await appLoader.loadApp({
  name: 'js-app',
  entry: 'https://localhost:3001/main.js',
  container: '#js-container'
});
```

#### Multi-Entry

```typescript
// Load from multiple entries
const app = await appLoader.loadApp({
  name: 'multi-app',
  entry: {
    scripts: [
      'https://localhost:3001/vendor.js',
      'https://localhost:3001/main.js'
    ],
    styles: [
      'https://localhost:3001/vendor.css',
      'https://localhost:3001/main.css'
    ]
  },
  container: '#multi-container'
});
```

## Loading Strategies

### Lazy Loading

```typescript
// Lazy load when needed
const lazyLoader = new AppLoader({
  strategy: 'lazy'
});

// Application is loaded only when activated
await microCore.registerApp({
  name: 'lazy-app',
  entry: 'https://localhost:3001',
  container: '#lazy-container',
  activeRule: '/lazy',
  loader: () => lazyLoader.loadApp({
    name: 'lazy-app',
    entry: 'https://localhost:3001'
  })
});
```

### Prefetching

```typescript
// Prefetch during idle time
const prefetchLoader = new AppLoader({
  strategy: 'prefetch',
  prefetchDelay: 2000
});

await microCore.registerApp({
  name: 'prefetch-app',
  entry: 'https://localhost:3001',
  container: '#prefetch-container',
  activeRule: '/prefetch',
  prefetch: true
});
```

### Preloading

```typescript
// Preload immediately
const preloadLoader = new AppLoader({
  strategy: 'preload'
});

await microCore.registerApp({
  name: 'preload-app',
  entry: 'https://localhost:3001',
  container: '#preload-container',
  activeRule: '/preload',
  preload: true
});
```

## Caching

### Cache Configuration

```typescript
const loader = new ResourceLoader({
  cache: {
    enabled: true,
    storage: 'memory', // 'memory' | 'localStorage' | 'sessionStorage'
    maxAge: 300000,    // 5 minutes
    maxSize: 50        // Maximum cached items
  }
});
```

### Cache Management

```typescript
// Check cache
const isCached = loader.isCached('/static/js/app.js');

// Get from cache
const cached = loader.getFromCache('/static/js/app.js');

// Clear specific cache
loader.clearCache('/static/js/app.js');

// Clear all cache
loader.clearAllCache();

// Get cache statistics
const stats = loader.getCacheStats();
console.log('Cache stats:', stats);
```

## Error Handling

### Retry Configuration

```typescript
const loader = new ResourceLoader({
  retries: 3,
  retryDelay: 1000,
  retryBackoff: 2 // Exponential backoff
});

// Custom retry logic
const customLoader = new ResourceLoader({
  retryCondition: (error, attempt) => {
    // Retry on network errors, but not on 404
    return error.status !== 404 && attempt < 3;
  }
});
```

### Error Recovery

```typescript
// Handle loading errors
loader.on('error', (error, resource) => {
  console.error(`Failed to load ${resource.url}:`, error);
  
  // Try fallback
  if (resource.fallback) {
    return loader.loadScript(resource.fallback);
  }
  
  // Show error UI
  showLoadingError(resource);
});

// Fallback configuration
await loader.loadScript('/static/js/app.js', {
  fallback: '/static/js/app.fallback.js'
});
```

## Loading Events

### Resource Events

```typescript
// Loading started
loader.on('load:start', (resource) => {
  console.log(`Loading started: ${resource.url}`);
  showLoadingIndicator(resource);
});

// Loading progress
loader.on('load:progress', (resource, progress) => {
  console.log(`Loading progress: ${resource.url} - ${progress}%`);
  updateLoadingProgress(resource, progress);
});

// Loading completed
loader.on('load:complete', (resource) => {
  console.log(`Loading completed: ${resource.url}`);
  hideLoadingIndicator(resource);
});

// Loading failed
loader.on('load:error', (resource, error) => {
  console.error(`Loading failed: ${resource.url}`, error);
  showLoadingError(resource, error);
});
```

### Application Events

```typescript
// Application loading events
microCore.on('app:load:start', (app) => {
  console.log(`App loading started: ${app.name}`);
});

microCore.on('app:load:progress', (app, progress) => {
  console.log(`App loading progress: ${app.name} - ${progress}%`);
});

microCore.on('app:load:complete', (app) => {
  console.log(`App loading completed: ${app.name}`);
});

microCore.on('app:load:error', (app, error) => {
  console.error(`App loading failed: ${app.name}`, error);
});
```

## Custom Loaders

### Creating Custom Loader

```typescript
import { BaseLoader } from '@micro-core/core';

class CustomLoader extends BaseLoader {
  async loadResource(url: string, options: any = {}): Promise<any> {
    // Custom loading logic
    const response = await this.fetch(url, {
      ...options,
      headers: {
        'X-Custom-Header': 'custom-value',
        ...options.headers
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to load ${url}: ${response.status}`);
    }
    
    return response;
  }
  
  async loadScript(url: string, options: any = {}): Promise<HTMLScriptElement> {
    // Custom script loading
    const script = document.createElement('script');
    script.src = url;
    script.async = options.async || true;
    
    // Add custom attributes
    if (options.customAttr) {
      script.setAttribute('data-custom', options.customAttr);
    }
    
    return new Promise((resolve, reject) => {
      script.onload = () => resolve(script);
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
}

// Use custom loader
const customLoader = new CustomLoader();
await customLoader.loadScript('/custom/app.js', {
  customAttr: 'value'
});
```

### Loader Plugins

```typescript
// Create loader plugin
class LoaderPlugin {
  name = 'custom-loader-plugin';
  
  install(loader: ResourceLoader) {
    // Add custom methods
    loader.loadJSON = this.loadJSON.bind(this);
    loader.loadImage = this.loadImage.bind(this);
    
    // Add middleware
    loader.use(this.middleware);
  }
  
  async loadJSON(url: string): Promise<any> {
    const response = await fetch(url);
    return response.json();
  }
  
  async loadImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = url;
    });
  }
  
  middleware = async (context: any, next: Function) => {
    // Add custom headers
    context.headers = {
      ...context.headers,
      'X-Loader-Plugin': this.name
    };
    
    await next();
  };
}

// Install plugin
const loader = new ResourceLoader();
loader.use(new LoaderPlugin());

// Use plugin methods
const data = await loader.loadJSON('/api/data.json');
const image = await loader.loadImage('/images/logo.png');
```

## Performance Optimization

### Resource Bundling

```typescript
// Bundle multiple resources
const bundleLoader = new ResourceLoader({
  bundle: {
    enabled: true,
    maxSize: 1024 * 1024, // 1MB
    compression: 'gzip'
  }
});

// Load bundle
const bundle = await bundleLoader.loadBundle([
  '/static/js/vendor.js',
  '/static/js/app.js',
  '/static/css/app.css'
]);
```

### Parallel Loading

```typescript
// Load resources in parallel
const parallelLoader = new ResourceLoader({
  parallel: {
    enabled: true,
    maxConcurrent: 6
  }
});

// Load multiple resources
const resources = await Promise.all([
  parallelLoader.loadScript('/js/app1.js'),
  parallelLoader.loadScript('/js/app2.js'),
  parallelLoader.loadStyle('/css/app1.css'),
  parallelLoader.loadStyle('/css/app2.css')
]);
```

### Resource Compression

```typescript
// Enable compression
const compressedLoader = new ResourceLoader({
  compression: {
    enabled: true,
    algorithms: ['gzip', 'br'], // Brotli, Gzip
    threshold: 1024 // Compress files larger than 1KB
  }
});
```

## Best Practices

### 1. Resource Organization

```typescript
// Good - Organize by type and priority
const criticalResources = [
  '/css/critical.css',
  '/js/polyfills.js'
];

const appResources = [
  '/js/vendor.js',
  '/js/app.js',
  '/css/app.css'
];

// Load critical resources first
await Promise.all(criticalResources.map(url => loader.loadScript(url)));

// Then load app resources
await Promise.all(appResources.map(url => loader.loadScript(url)));
```

### 2. Error Handling

```typescript
// Implement comprehensive error handling
const robustLoader = new ResourceLoader({
  retries: 3,
  timeout: 10000,
  fallback: {
    '/js/app.js': '/js/app.min.js',
    '/css/app.css': '/css/app.min.css'
  }
});

robustLoader.on('error', async (error, resource) => {
  // Log error
  console.error(`Resource loading failed: ${resource.url}`, error);
  
  // Report to monitoring service
  errorReporter.report(error, { resource: resource.url });
  
  // Try alternative CDN
  if (resource.url.includes('cdn1.example.com')) {
    const fallbackUrl = resource.url.replace('cdn1.example.com', 'cdn2.example.com');
    try {
      return await robustLoader.loadScript(fallbackUrl);
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
    }
  }
});
```

### 3. Performance Monitoring

```typescript
// Monitor loading performance
const monitoredLoader = new ResourceLoader();

monitoredLoader.on('load:complete', (resource, timing) => {
  // Track loading performance
  analytics.track('resource_loaded', {
    url: resource.url,
    size: resource.size,
    loadTime: timing.loadTime,
    cacheHit: timing.cacheHit
  });
  
  // Alert on slow loading
  if (timing.loadTime > 5000) {
    console.warn(`Slow resource loading: ${resource.url} took ${timing.loadTime}ms`);
  }
});
```

## Type Definitions

```typescript
interface ResourceLoader {
  loadScript(url: string, options?: ScriptLoadOptions): Promise<HTMLScriptElement>;
  loadStyle(url: string, options?: StyleLoadOptions): Promise<HTMLLinkElement>;
  loadHTML(url: string, options?: HTMLLoadOptions): Promise<string>;
  isCached(url: string): boolean;
  clearCache(url?: string): void;
}

interface LoadOptions {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  fallback?: string;
}

interface ScriptLoadOptions extends LoadOptions {
  async?: boolean;
  defer?: boolean;
  crossOrigin?: string;
  integrity?: string;
}

interface StyleLoadOptions extends LoadOptions {
  media?: string;
  crossOrigin?: string;
  integrity?: string;
}

interface AppLoader {
  loadApp(config: AppConfig): Promise<AppInstance>;
}

interface LoadingStrategy {
  name: string;
  load(resource: Resource): Promise<any>;
}
```

## References

- [Core API](./core.md)
- [Application Management](./app-management.md)
- [Performance Guide](../guide/performance.md)
- [Best Practices](../guide/best-practices.md)