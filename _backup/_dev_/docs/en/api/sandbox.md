# Sandbox API

Micro-Core provides powerful sandbox isolation to ensure micro-applications don't interfere with each other or the main application.

## Sandbox Configuration

### SandboxConfig

```typescript
interface SandboxConfig {
  type?: 'proxy' | 'snapshot' | 'iframe';
  css?: boolean | CSSIsolationConfig;
  js?: boolean | JSIsolationConfig;
  globalWhitelist?: string[];
  globalBlacklist?: string[];
  multiMode?: boolean;
  strictGlobal?: boolean;
}
```

### Basic Setup

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  sandbox: {
    type: 'proxy',
    css: true,
    js: true,
    multiMode: true
  }
});
```

## Sandbox Types

### Proxy Sandbox

High-performance JavaScript isolation using Proxy:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'proxy',
    multiMode: true, // Support multiple applications
    strictGlobal: true // Strict global variable isolation
  }
});
```

**Features:**
- High performance
- Supports multiple applications
- Real-time isolation
- Minimal memory overhead

### Snapshot Sandbox

Global state snapshot for restoration:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'snapshot',
    strictGlobal: false // Compatible mode
  }
});
```

**Features:**
- Simple implementation
- Good compatibility
- Single application mode
- State restoration

### Iframe Sandbox

Complete isolation using iframe:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'iframe',
    css: true,
    js: true
  }
});
```

**Features:**
- Complete isolation
- Maximum security
- Independent context
- Higher resource usage

## JavaScript Isolation

### JS Isolation Configuration

```typescript
interface JSIsolationConfig {
  enabled: boolean;
  strict?: boolean;
  whitelist?: string[];
  blacklist?: string[];
  customGlobals?: Record<string, any>;
}
```

### Example Configuration

```typescript
await microCore.registerApp({
  name: 'isolated-app',
  entry: 'http://localhost:3001',
  container: '#app-container',
  activeRule: '/app',
  sandbox: {
    js: {
      enabled: true,
      strict: true,
      whitelist: ['console', 'setTimeout', 'setInterval'],
      blacklist: ['eval', 'Function'],
      customGlobals: {
        APP_NAME: 'isolated-app',
        API_BASE: 'https://api.example.com'
      }
    }
  }
});
```

### Global Variable Control

```typescript
// Whitelist specific globals
const microCore = new MicroCore({
  sandbox: {
    globalWhitelist: [
      'console',
      'setTimeout',
      'setInterval',
      'clearTimeout',
      'clearInterval',
      'fetch',
      'XMLHttpRequest'
    ]
  }
});

// Blacklist dangerous globals
const microCore = new MicroCore({
  sandbox: {
    globalBlacklist: [
      'eval',
      'Function',
      'document.write',
      'document.writeln'
    ]
  }
});
```

## CSS Isolation

### CSS Isolation Configuration

```typescript
interface CSSIsolationConfig {
  enabled: boolean;
  strictStyleIsolation?: boolean;
  experimentalStyleIsolation?: boolean;
  prefix?: string;
  excludeSelectors?: string[];
}
```

### Strict Style Isolation

```typescript
await microCore.registerApp({
  name: 'styled-app',
  entry: 'http://localhost:3001',
  container: '#styled-container',
  activeRule: '/styled',
  sandbox: {
    css: {
      enabled: true,
      strictStyleIsolation: true, // Complete CSS isolation
      prefix: 'micro-app-styled' // CSS prefix
    }
  }
});
```

### Experimental Style Isolation

```typescript
await microCore.registerApp({
  name: 'experimental-app',
  entry: 'http://localhost:3002',
  container: '#experimental-container',
  activeRule: '/experimental',
  sandbox: {
    css: {
      enabled: true,
      experimentalStyleIsolation: true, // Use Shadow DOM
      excludeSelectors: ['body', 'html'] // Exclude global selectors
    }
  }
});
```

## Sandbox API Methods

### createSandbox()

Create a sandbox instance:

```typescript
import { createSandbox } from '@micro-core/core';

const sandbox = createSandbox({
  type: 'proxy',
  name: 'my-sandbox'
});
```

### Sandbox Instance Methods

```typescript
// Activate sandbox
sandbox.activate();

// Deactivate sandbox
sandbox.deactivate();

// Execute code in sandbox
sandbox.exec(`
  console.log('Running in sandbox');
  window.myVar = 'isolated value';
`);

// Get sandbox globals
const globals = sandbox.getGlobals();

// Set sandbox globals
sandbox.setGlobals({
  customAPI: myCustomAPI,
  config: appConfig
});

// Clear sandbox
sandbox.clear();

// Destroy sandbox
sandbox.destroy();
```

## Sandbox Events

### Sandbox Lifecycle Events

```typescript
// Sandbox created
microCore.on('sandbox:created', (sandbox) => {
  console.log('Sandbox created:', sandbox.name);
});

// Sandbox activated
microCore.on('sandbox:activated', (sandbox) => {
  console.log('Sandbox activated:', sandbox.name);
});

// Sandbox deactivated
microCore.on('sandbox:deactivated', (sandbox) => {
  console.log('Sandbox deactivated:', sandbox.name);
});

// Sandbox destroyed
microCore.on('sandbox:destroyed', (sandbox) => {
  console.log('Sandbox destroyed:', sandbox.name);
});
```

### Security Events

```typescript
// Blocked global access
microCore.on('sandbox:blocked-access', (sandbox, property) => {
  console.warn(`Blocked access to ${property} in ${sandbox.name}`);
});

// Security violation
microCore.on('sandbox:security-violation', (sandbox, violation) => {
  console.error('Security violation:', violation);
  // Report security incident
  reportSecurityIncident(sandbox, violation);
});
```

## Advanced Features

### Custom Sandbox

```typescript
import { BaseSandbox } from '@micro-core/core';

class CustomSandbox extends BaseSandbox {
  constructor(options) {
    super(options);
    this.customFeature = true;
  }
  
  activate() {
    super.activate();
    // Custom activation logic
    this.setupCustomFeatures();
  }
  
  deactivate() {
    // Custom deactivation logic
    this.cleanupCustomFeatures();
    super.deactivate();
  }
  
  setupCustomFeatures() {
    // Implement custom features
  }
  
  cleanupCustomFeatures() {
    // Cleanup custom features
  }
}

// Register custom sandbox
microCore.registerSandboxType('custom', CustomSandbox);

// Use custom sandbox
await microCore.registerApp({
  name: 'custom-app',
  entry: 'http://localhost:3001',
  container: '#custom-container',
  activeRule: '/custom',
  sandbox: {
    type: 'custom',
    customOption: true
  }
});
```

### Sandbox Communication

```typescript
// Setup communication between sandbox and main app
const sandbox = createSandbox({
  type: 'proxy',
  name: 'communicating-sandbox'
});

// Add communication channel
sandbox.addChannel('api', {
  request: (method, url, data) => {
    // Handle API requests from sandbox
    return fetch(url, {
      method,
      body: JSON.stringify(data),
      headers: { 'Content-Type': 'application/json' }
    }).then(res => res.json());
  },
  
  notify: (message) => {
    // Handle notifications from sandbox
    console.log('Sandbox notification:', message);
  }
});

// In sandbox code
// window.microCore.api.request('POST', '/api/data', { key: 'value' });
// window.microCore.api.notify('Operation completed');
```

## Security Features

### Content Security Policy

```typescript
await microCore.registerApp({
  name: 'secure-app',
  entry: 'http://localhost:3001',
  container: '#secure-container',
  activeRule: '/secure',
  sandbox: {
    csp: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'"],
      'style-src': ["'self'", "'unsafe-inline'"],
      'img-src': ["'self'", 'data:', 'https:'],
      'connect-src': ["'self'", 'https://api.example.com']
    }
  }
});
```

### Resource Access Control

```typescript
await microCore.registerApp({
  name: 'controlled-app',
  entry: 'http://localhost:3001',
  container: '#controlled-container',
  activeRule: '/controlled',
  sandbox: {
    resourceControl: {
      allowedDomains: ['api.example.com', 'cdn.example.com'],
      blockedDomains: ['malicious.com'],
      maxRequestSize: 1024 * 1024, // 1MB
      maxConcurrentRequests: 10
    }
  }
});
```

## Performance Optimization

### Sandbox Pooling

```typescript
const microCore = new MicroCore({
  sandbox: {
    pool: {
      enabled: true,
      maxSize: 5, // Maximum pool size
      idleTimeout: 300000 // 5 minutes idle timeout
    }
  }
});
```

### Memory Management

```typescript
// Monitor sandbox memory usage
microCore.on('sandbox:memory-warning', (sandbox, usage) => {
  console.warn(`Sandbox ${sandbox.name} memory usage: ${usage.used}MB`);
  
  if (usage.used > 100) {
    // Force garbage collection
    sandbox.gc();
  }
});

// Automatic cleanup
const microCore = new MicroCore({
  sandbox: {
    autoCleanup: {
      enabled: true,
      interval: 60000, // Check every minute
      memoryThreshold: 50 // MB
    }
  }
});
```

## Debugging

### Sandbox Inspector

```typescript
// Enable sandbox debugging
const microCore = new MicroCore({
  sandbox: {
    debug: true,
    inspector: {
      enabled: true,
      port: 9229
    }
  }
});

// Get sandbox information
const sandboxInfo = microCore.getSandboxInfo('app-name');
console.log('Sandbox info:', sandboxInfo);

// List all sandboxes
const allSandboxes = microCore.getAllSandboxes();
allSandboxes.forEach(sandbox => {
  console.log(`Sandbox: ${sandbox.name}, Status: ${sandbox.status}`);
});
```

### Performance Monitoring

```typescript
// Monitor sandbox performance
microCore.on('sandbox:performance', (sandbox, metrics) => {
  console.log(`Sandbox ${sandbox.name} performance:`, {
    cpuUsage: metrics.cpu,
    memoryUsage: metrics.memory,
    executionTime: metrics.executionTime
  });
});
```

## Best Practices

### 1. Choose Appropriate Sandbox Type

```typescript
// For modern browsers with multiple apps
sandbox: { type: 'proxy', multiMode: true }

// For legacy browsers or single app
sandbox: { type: 'snapshot' }

// For maximum security
sandbox: { type: 'iframe' }
```

### 2. Configure Global Access Carefully

```typescript
// Minimal necessary globals
sandbox: {
  globalWhitelist: [
    'console', 'setTimeout', 'setInterval', 
    'fetch', 'XMLHttpRequest'
  ],
  globalBlacklist: [
    'eval', 'Function', 'document.write'
  ]
}
```

### 3. Handle Sandbox Errors

```typescript
microCore.on('sandbox:error', (sandbox, error) => {
  console.error(`Sandbox ${sandbox.name} error:`, error);
  
  // Try to recover
  try {
    sandbox.reset();
  } catch (resetError) {
    console.error('Failed to reset sandbox:', resetError);
    // Recreate sandbox
    microCore.recreateSandbox(sandbox.name);
  }
});
```

## Type Definitions

```typescript
interface SandboxConfig {
  type?: 'proxy' | 'snapshot' | 'iframe';
  css?: boolean | CSSIsolationConfig;
  js?: boolean | JSIsolationConfig;
  globalWhitelist?: string[];
  globalBlacklist?: string[];
  multiMode?: boolean;
  strictGlobal?: boolean;
  csp?: Record<string, string[]>;
  resourceControl?: ResourceControlConfig;
  pool?: PoolConfig;
  autoCleanup?: AutoCleanupConfig;
  debug?: boolean;
}

interface CSSIsolationConfig {
  enabled: boolean;
  strictStyleIsolation?: boolean;
  experimentalStyleIsolation?: boolean;
  prefix?: string;
  excludeSelectors?: string[];
}

interface JSIsolationConfig {
  enabled: boolean;
  strict?: boolean;
  whitelist?: string[];
  blacklist?: string[];
  customGlobals?: Record<string, any>;
}

interface SandboxInstance {
  name: string;
  type: string;
  status: 'inactive' | 'active' | 'destroyed';
  activate(): void;
  deactivate(): void;
  exec(code: string): any;
  getGlobals(): Record<string, any>;
  setGlobals(globals: Record<string, any>): void;
  clear(): void;
  destroy(): void;
}
```

## References

- [Core API](./core.md)
- [Application Management](./app-management.md)
- [Security Guide](../guide/security.md)
- [Performance Guide](../guide/performance.md)