# Router API (Part 1)

Micro-Core provides a powerful routing system for managing micro-application routing and navigation. This document details the usage methods and best practices of the Router API.

## Basic Concepts

In micro-frontend architecture, the routing system is responsible for the following tasks:

1. **Route Matching**: Determine which micro-application should be activated based on the current URL
2. **Route Navigation**: Navigate between different micro-applications
3. **Route State Synchronization**: Keep route state synchronized between main application and micro-applications
4. **Route Interception**: Intercept route changes to perform permission checks and other logic

## Router

`Router` is the core routing class provided by Micro-Core for creating and managing routing systems.

### Basic Usage

```typescript
import { Router } from '@micro-core/core';

// Create router instance
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    {
      path: '/',
      name: 'home',
      component: 'home-app'
    },
    {
      path: '/users/:userId',
      name: 'user-detail',
      component: 'user-app'
    },
    {
      path: '/settings',
      name: 'settings',
      component: 'settings-app',
      meta: {
        requiresAuth: true
      }
    }
  ]
});

// Listen to route changes
router.beforeEach((to, from, next) => {
  console.log(`Route changing from ${from.path} to ${to.path}`);
  
  // Check permissions
  if (to.meta.requiresAuth && !isAuthenticated()) {
    // Redirect to login page
    next('/login');
  } else {
    // Continue navigation
    next();
  }
});

// Navigate to specified route
router.push('/users/123');

// Replace current route
router.replace('/settings');

// Get current route
const currentRoute = router.currentRoute;
console.log('Current path:', currentRoute.path);
console.log('Route params:', currentRoute.params);
console.log('Query params:', currentRoute.query);
```

### API Reference

#### Constructor

Create router instance.

```typescript
constructor(options: RouterOptions)
```

**Parameters:**
- `options` (RouterOptions): Router configuration options
  - `mode` (string): Router mode, can be 'hash' or 'history', defaults to 'history'
  - `base` (string): Base path of the application, defaults to '/'
  - `routes` (RouteConfig[]): Array of route configurations
  - `scrollBehavior` (Function): Function to control scroll behavior
  - `parseQuery` (Function): Custom query string parsing function
  - `stringifyQuery` (Function): Custom query string generation function
  - `fallback` (boolean): Whether to fallback to hash mode when browser doesn't support history.pushState, defaults to true

**Example:**

```typescript
// Basic usage
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    { path: '/', component: 'home-app' },
    { path: '/users/:userId', component: 'user-app' }
  ]
});

// Complete configuration
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    {
      path: '/',
      name: 'home',
      component: 'home-app',
      meta: { title: 'Home' }
    },
    {
      path: '/users/:userId',
      name: 'user-detail',
      component: 'user-app',
      props: true,
      meta: { title: 'User Detail' }
    },
    {
      path: '/settings',
      name: 'settings',
      component: 'settings-app',
      meta: { requiresAuth: true, title: 'Settings' },
      children: [
        {
          path: 'profile',
          name: 'profile',
          component: 'profile-app',
          meta: { title: 'Profile' }
        },
        {
          path: 'security',
          name: 'security',
          component: 'security-app',
          meta: { title: 'Security Settings' }
        }
      ]
    },
    {
      path: '*',
      component: 'not-found-app'
    }
  ],
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { x: 0, y: 0 };
    }
  },
  parseQuery: (query) => {
    // Custom query string parsing
    return customParseQuery(query);
  },
  stringifyQuery: (query) => {
    // Custom query string generation
    return customStringifyQuery(query);
  },
  fallback: true
});
```

#### push(location, onComplete?, onAbort?)

Navigate to specified route.

```typescript
push(location: RawLocation, onComplete?: Function, onAbort?: Function): void
```

**Parameters:**
- `location` (RawLocation): Target route location, can be string path or route location object
- `onComplete` (Function): Optional, callback function when navigation completes successfully
- `onAbort` (Function): Optional, callback function when navigation is aborted

**Example:**

```typescript
// Using string path
router.push('/users/123');

// Using route location object
router.push({
  path: '/users/123',
  query: { tab: 'profile' }
});

// Using named route
router.push({
  name: 'user-detail',
  params: { userId: '123' },
  query: { tab: 'profile' }
});

// With callback functions
router.push('/settings', 
  () => {
    console.log('Navigation completed successfully');
    showSettingsUI();
  },
  (error) => {
    console.error('Navigation aborted:', error);
    showErrorMessage(error.message);
  }
);
```

#### replace(location, onComplete?, onAbort?)

Replace current route.

```typescript
replace(location: RawLocation, onComplete?: Function, onAbort?: Function): void
```

**Parameters:**
- `location` (RawLocation): Target route location, can be string path or route location object
- `onComplete` (Function): Optional, callback function when navigation completes successfully
- `onAbort` (Function): Optional, callback function when navigation is aborted

**Example:**

```typescript
// Using string path
router.replace('/users/123');

// Using route location object
router.replace({
  path: '/users/123',
  query: { tab: 'profile' }
});

// Using named route
router.replace({
  name: 'user-detail',
  params: { userId: '123' },
  query: { tab: 'profile' }
});

// With callback functions
router.replace('/settings', 
  () => {
    console.log('Navigation completed successfully');
    showSettingsUI();
  },
  (error) => {
    console.error('Navigation aborted:', error);
    showErrorMessage(error.message);
  }
);
```

#### go(n)

Navigate to a position in the history stack.

```typescript
go(n: number): void
```

**Parameters:**
- `n` (number): Position in history stack relative to current page, positive numbers go forward, negative numbers go back

**Example:**

```typescript
// Go back one step
router.go(-1);

// Go forward one step
router.go(1);

// Go forward two steps
router.go(2);

// Go back two steps
router.go(-2);
```

#### back()

Go back one step.

```typescript
back(): void
```

**Parameters:** None

**Example:**

```typescript
// Go back one step
router.back();

// Use when user clicks back button
backButton.addEventListener('click', () => {
  router.back();
});
```

#### forward()

Go forward one step.

```typescript
forward(): void
```

**Parameters:** None

**Example:**

```typescript
// Go forward one step
router.forward();

// Use when user clicks forward button
forwardButton.addEventListener('click', () => {
  router.forward();
});
```

#### beforeEach(guard)

Add global before guard.

```typescript
beforeEach(guard: NavigationGuard): Function
```

**Parameters:**
- `guard` (NavigationGuard): Navigation guard function that receives to, from, and next parameters

**Return Value:** Function to remove the registered guard

**Example:**

```typescript
// Add global before guard
const unregister = router.beforeEach((to, from, next) => {
  console.log(`Route changing from ${from.path} to ${to.path}`);
  
  // Check permissions
  if (to.meta.requiresAuth && !isAuthenticated()) {
    // Redirect to login page
    next('/login');
  } else {
    // Continue navigation
    next();
  }
});

// Remove guard
unregister();

// Permission check guard
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth) {
    if (!isAuthenticated()) {
      // Save target route
      saveTargetRoute(to.fullPath);
      // Redirect to login page
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      });
    } else if (to.meta.roles && !hasRole(to.meta.roles)) {
      // Check role permissions
      next('/403'); // No permission page
    } else {
      // Has permission, continue navigation
      next();
    }
  } else {
    // No permission required, continue navigation
    next();
  }
});

// Progress bar guard
router.beforeEach((to, from, next) => {
  // Start progress bar
  startProgressBar();
  next();
});
```

#### afterEach(hook)

Add global after hook.

```typescript
afterEach(hook: (to: Route, from: Route) => void): Function
```

**Parameters:**
- `hook` ((to: Route, from: Route) => void): After hook function that receives to and from parameters

**Return Value:** Function to remove the registered hook

**Example:**

```typescript
// Add global after hook
const unregister = router.afterEach((to, from) => {
  console.log(`Route navigation completed: ${from.path} -> ${to.path}`);
  
  // Update page title
  document.title = to.meta.title || 'Default Title';
});

// Remove hook
unregister();

// Progress bar hook
router.afterEach((to, from) => {
  // Stop progress bar
  stopProgressBar();
});

// Analytics hook
router.afterEach((to, from) => {
  // Send page view event
  trackPageView({
    path: to.path,
    title: to.meta.title,
    referrer: from.path
  });
});
```

#### beforeResolve(guard)

Add global resolve guard.

```typescript
beforeResolve(guard: NavigationGuard): Function
```

**Parameters:**
- `guard` (NavigationGuard): Navigation guard function that receives to, from, and next parameters

**Return Value:** Function to remove the registered guard

**Example:**

```typescript
// Add global resolve guard
const unregister = router.beforeResolve((to, from, next) => {
  // Before navigation is confirmed, components have been resolved
  console.log('Route resolving...');
  
  // Async data preloading
  if (to.meta.fetchData) {
    loadAsyncData(to.params.id)
      .then(data => {
        // Attach data to route
        to.meta.data = data;
        next();
      })
      .catch(error => {
        console.error('Data loading failed:', error);
        next(false); // Abort navigation
      });
  } else {
    next();
  }
});

// Remove guard
unregister();
```

#### onError(callback)

Register error handling callback.

```typescript
onError(callback: (error: Error) => void): Function
```

**Parameters:**
- `callback` ((error: Error) => void): Error handling callback function

**Return Value:** Function to remove the registered callback

**Example:**

```typescript
// Register error handling callback
const unregister = router.onError((error) => {
  console.error('Router error:', error);
  
  // Show error notification
  showErrorNotification(`Route navigation failed: ${error.message}`);
  
  // Report error
  reportError({
    type: 'router_error',
    message: error.message,
    stack: error.stack
  });
});

// Remove callback
unregister();
```

#### addRoutes(routes)

Dynamically add route rules.

```typescript
addRoutes(routes: RouteConfig[]): void
```

**Parameters:**
- `routes` (RouteConfig[]): Array of route configurations

**Example:**

```typescript
// Dynamically add routes
router.addRoutes([
  {
    path: '/admin',
    name: 'admin',
    component: 'admin-app',
    meta: { requiresAuth: true, roles: ['admin'] },
    children: [
      {
        path: 'users',
        name: 'admin-users',
        component: 'admin-users-app'
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: 'admin-settings-app'
      }
    ]
  }
]);

// Dynamically add routes based on user role
function addRoutesBasedOnUserRole(role) {
  let routes = [];
  
  if (role === 'admin') {
    routes = adminRoutes;
  } else if (role === 'manager') {
    routes = managerRoutes;
  } else {
    routes = userRoutes;
  }
  
  router.addRoutes(routes);
}

// Call after user login
function onUserLogin(user) {
  addRoutesBasedOnUserRole(user.role);
}
```

#### getMatchedComponents(location?)

Return array of components matched by specified route.

```typescript
getMatchedComponents(location?: RawLocation): Array<string>
```

**Parameters:**
- `location` (RawLocation): Optional, route location, defaults to current route

**Return Value:** Array of matched components

**Example:**

```typescript
// Get components matched by current route
const components = router.getMatchedComponents();
console.log('Components matched by current route:', components);

// Get components matched by specified route
const components = router.getMatchedComponents('/users/123');
console.log('Components matched by specified route:', components);

// Preload components
function preloadComponents(path) {
  const components = router.getMatchedComponents(path);
  components.forEach(component => {
    // Preload component
    loadComponent(component);
  });
}
```

#### resolve(location, current?, append?)

Resolve target location.

```typescript
resolve(location: RawLocation, current?: Route, append?: boolean): {
  location: Location;
  route: Route;
  href: string;
}
```

**Parameters:**
- `location` (RawLocation): Target route location
- `current` (Route): Optional, current route, defaults to current route
- `append` (boolean): Optional, whether to append path, defaults to false

**Return Value:** Object containing resolved location, route, and href

**Example:**

```typescript
// Resolve route
const resolved = router.resolve('/users/123');
console.log('Resolved href:', resolved.href);
console.log('Resolved route:', resolved.route);

// Resolve named route
const resolved = router.resolve({
  name: 'user-detail',
  params: { userId: '123' }
});
console.log('Resolved href:', resolved.href);

// Use when generating links
function generateLink(location) {
  const resolved = router.resolve(location);
  return resolved.href;
}

const userLink = generateLink({
  name: 'user-detail',
  params: { userId: '123' }
});
console.log('User link:', userLink);