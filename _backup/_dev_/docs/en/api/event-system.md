# Event System API

The Event System API provides comprehensive event handling and management capabilities for micro-frontend applications in Micro-Core.

## EventManager Class

Central event management system that coordinates events across all micro-applications.

### Constructor

```typescript
new EventManager(config?: EventManagerConfig)
```

**Parameters:**
- `config` - Optional configuration object

**Example:**
```typescript
import { EventManager } from '@micro-core/core';

const eventManager = new EventManager({
  namespace: 'micro-core',
  isolation: true,
  debugging: true
});
```

### Configuration Interface

```typescript
interface EventManagerConfig {
  // Event namespace for isolation
  namespace?: string;
  
  // Enable event isolation between apps
  isolation?: boolean;
  
  // Maximum event history size
  historySize?: number;
  
  // Enable event debugging
  debugging?: boolean;
  
  // Event interceptors
  interceptors?: EventInterceptor[];
  
  // Performance monitoring
  monitoring?: EventMonitoringConfig;
  
  // Event persistence
  persistence?: EventPersistenceConfig;
}
```

### Methods

#### registerEventHandler()

Register a global event handler.

```typescript
registerEventHandler(
  event: string, 
  handler: EventHandler, 
  options?: HandlerOptions
): string
```

**Parameters:**
- `event` - Event name or pattern
- `handler` - Event handler function
- `options` - Optional handler configuration

**Returns:** Handler ID for removal

**Example:**
```typescript
const handlerId = eventManager.registerEventHandler(
  'app:*',
  (event, data, context) => {
    console.log(`App event: ${event}`, data);
    logEvent(event, data, context.appName);
  },
  {
    priority: 'high',
    persistent: true,
    filter: (data) => data.important === true
  }
);
```

#### unregisterEventHandler()

Remove a registered event handler.

```typescript
unregisterEventHandler(handlerId: string): boolean
```

**Parameters:**
- `handlerId` - ID of the handler to remove

**Returns:** True if handler was removed

**Example:**
```typescript
const removed = eventManager.unregisterEventHandler(handlerId);
console.log('Handler removed:', removed);
```

#### emitEvent()

Emit an event through the event system.

```typescript
emitEvent(
  event: string, 
  data?: any, 
  context?: EventContext
): Promise<EventResult>
```

**Parameters:**
- `event` - Event name
- `data` - Optional event data
- `context` - Optional event context

**Returns:** Promise resolving to event result

**Example:**
```typescript
const result = await eventManager.emitEvent(
  'user:login',
  {
    userId: 123,
    username: 'john_doe',
    timestamp: Date.now()
  },
  {
    appName: 'auth-app',
    priority: 'high',
    broadcast: true
  }
);

console.log('Event handled by', result.handlerCount, 'handlers');
```

#### getEventHistory()

Get event history for debugging and monitoring.

```typescript
getEventHistory(filter?: EventHistoryFilter): EventHistoryEntry[]
```

**Parameters:**
- `filter` - Optional filter criteria

**Returns:** Array of event history entries

**Example:**
```typescript
// Get all events from last hour
const recentEvents = eventManager.getEventHistory({
  timeRange: {
    start: Date.now() - 3600000,
    end: Date.now()
  },
  eventPattern: 'user:*'
});

console.log('Recent user events:', recentEvents);
```

#### clearEventHistory()

Clear event history.

```typescript
clearEventHistory(filter?: EventHistoryFilter): number
```

**Parameters:**
- `filter` - Optional filter for selective clearing

**Returns:** Number of entries cleared

**Example:**
```typescript
// Clear events older than 24 hours
const cleared = eventManager.clearEventHistory({
  timeRange: {
    start: 0,
    end: Date.now() - 86400000
  }
});

console.log(`Cleared ${cleared} old events`);
```

## EventBridge Class

Bridges events between different micro-applications and contexts.

### Constructor

```typescript
new EventBridge(config: EventBridgeConfig)
```

### Configuration

```typescript
interface EventBridgeConfig {
  // Bridge name
  name: string;
  
  // Source and target configurations
  source: BridgeEndpoint;
  target: BridgeEndpoint;
  
  // Event transformation
  transform?: EventTransformer;
  
  // Event filtering
  filter?: EventFilter;
  
  // Bidirectional bridging
  bidirectional?: boolean;
  
  // Error handling
  onError?: (error: Error, event: string, data: any) => void;
}
```

### Methods

#### bridge()

Start bridging events between endpoints.

```typescript
bridge(): void
```

**Example:**
```typescript
const bridge = new EventBridge({
  name: 'main-to-iframe',
  source: {
    type: 'eventbus',
    instance: mainEventBus
  },
  target: {
    type: 'postmessage',
    window: iframeWindow,
    origin: 'https://iframe-app.com'
  },
  transform: (event, data) => ({
    type: `iframe:${event}`,
    payload: data
  }),
  bidirectional: true
});

bridge.bridge();
```

#### unbridgeEvent()

Stop bridging events.

```typescript
unbridgeEvent(): void
```

**Example:**
```typescript
bridge.unbridgeEvent();
console.log('Event bridging stopped');
```

#### transformEvent()

Transform an event before bridging.

```typescript
transformEvent(event: string, data: any): TransformedEvent
```

**Parameters:**
- `event` - Original event name
- `data` - Original event data

**Returns:** Transformed event

**Example:**
```typescript
const transformed = bridge.transformEvent('user:login', {
  userId: 123,
  name: 'John'
});

console.log('Transformed event:', transformed);
```

## EventRouter Class

Routes events based on patterns and conditions.

### Constructor

```typescript
new EventRouter(config?: EventRouterConfig)
```

### Configuration

```typescript
interface EventRouterConfig {
  // Default routing strategy
  strategy?: 'broadcast' | 'unicast' | 'multicast';
  
  // Route definitions
  routes?: EventRoute[];
  
  // Fallback handler
  fallback?: EventHandler;
  
  // Route caching
  caching?: boolean;
}
```

### Methods

#### addRoute()

Add an event route.

```typescript
addRoute(route: EventRoute): string
```

**Parameters:**
- `route` - Route configuration

**Returns:** Route ID

**Example:**
```typescript
const routeId = eventRouter.addRoute({
  pattern: 'user:*',
  targets: ['user-app', 'analytics-app'],
  condition: (event, data) => data.userId != null,
  transform: (event, data) => ({
    ...data,
    timestamp: Date.now()
  }),
  priority: 'high'
});
```

#### removeRoute()

Remove an event route.

```typescript
removeRoute(routeId: string): boolean
```

**Parameters:**
- `routeId` - ID of the route to remove

**Returns:** True if route was removed

**Example:**
```typescript
const removed = eventRouter.removeRoute(routeId);
console.log('Route removed:', removed);
```

#### routeEvent()

Route an event based on configured routes.

```typescript
routeEvent(event: string, data: any, context?: EventContext): Promise<RoutingResult>
```

**Parameters:**
- `event` - Event name
- `data` - Event data
- `context` - Optional event context

**Returns:** Promise resolving to routing result

**Example:**
```typescript
const result = await eventRouter.routeEvent('user:profile:update', {
  userId: 123,
  changes: { name: 'Jane Doe' }
});

console.log(`Event routed to ${result.targetCount} targets`);
```

## EventValidator Class

Validates events against schemas and rules.

### Constructor

```typescript
new EventValidator(config?: EventValidatorConfig)
```

### Configuration

```typescript
interface EventValidatorConfig {
  // Validation schemas
  schemas?: Record<string, EventSchema>;
  
  // Strict validation mode
  strict?: boolean;
  
  // Custom validators
  validators?: Record<string, CustomValidator>;
  
  // Validation error handling
  onValidationError?: (error: ValidationError) => void;
}
```

### Methods

#### addSchema()

Add validation schema for an event.

```typescript
addSchema(event: string, schema: EventSchema): void
```

**Parameters:**
- `event` - Event name or pattern
- `schema` - Validation schema

**Example:**
```typescript
eventValidator.addSchema('user:login', {
  type: 'object',
  required: ['userId', 'username'],
  properties: {
    userId: { type: 'number' },
    username: { type: 'string', minLength: 3 },
    timestamp: { type: 'number' }
  }
});
```

#### validate()

Validate event data against schema.

```typescript
validate(event: string, data: any): ValidationResult
```

**Parameters:**
- `event` - Event name
- `data` - Event data to validate

**Returns:** Validation result

**Example:**
```typescript
const result = eventValidator.validate('user:login', {
  userId: 123,
  username: 'john_doe',
  timestamp: Date.now()
});

if (!result.valid) {
  console.error('Validation errors:', result.errors);
}
```

#### removeSchema()

Remove validation schema.

```typescript
removeSchema(event: string): boolean
```

**Parameters:**
- `event` - Event name

**Returns:** True if schema was removed

**Example:**
```typescript
const removed = eventValidator.removeSchema('user:login');
console.log('Schema removed:', removed);
```

## EventAggregator Class

Aggregates and processes multiple events.

### Constructor

```typescript
new EventAggregator(config: EventAggregatorConfig)
```

### Configuration

```typescript
interface EventAggregatorConfig {
  // Aggregation window size
  windowSize: number;
  
  // Aggregation strategy
  strategy: 'count' | 'sum' | 'average' | 'custom';
  
  // Custom aggregation function
  aggregator?: (events: AggregatedEvent[]) => any;
  
  // Trigger conditions
  triggers?: AggregationTrigger[];
}
```

### Methods

#### aggregate()

Start aggregating events.

```typescript
aggregate(pattern: string, handler: AggregationHandler): string
```

**Parameters:**
- `pattern` - Event pattern to aggregate
- `handler` - Function to handle aggregated results

**Returns:** Aggregation ID

**Example:**
```typescript
const aggregationId = eventAggregator.aggregate(
  'user:action:*',
  (aggregatedData) => {
    console.log('User actions in last minute:', aggregatedData);
    
    if (aggregatedData.count > 100) {
      eventBus.emit('user:high-activity', {
        count: aggregatedData.count,
        timeWindow: '1m'
      });
    }
  }
);
```

#### stopAggregation()

Stop event aggregation.

```typescript
stopAggregation(aggregationId: string): boolean
```

**Parameters:**
- `aggregationId` - ID of the aggregation to stop

**Returns:** True if aggregation was stopped

**Example:**
```typescript
const stopped = eventAggregator.stopAggregation(aggregationId);
console.log('Aggregation stopped:', stopped);
```

## EventReplay Class

Records and replays events for debugging and testing.

### Constructor

```typescript
new EventReplay(config?: EventReplayConfig)
```

### Configuration

```typescript
interface EventReplayConfig {
  // Maximum recording size
  maxRecordingSize?: number;
  
  // Recording filters
  filters?: EventFilter[];
  
  // Storage backend
  storage?: 'memory' | 'localStorage' | 'indexedDB';
  
  // Compression
  compression?: boolean;
}
```

### Methods

#### startRecording()

Start recording events.

```typescript
startRecording(name: string, filter?: EventFilter): string
```

**Parameters:**
- `name` - Recording name
- `filter` - Optional event filter

**Returns:** Recording ID

**Example:**
```typescript
const recordingId = eventReplay.startRecording('user-session', {
  patterns: ['user:*', 'navigation:*'],
  timeRange: {
    start: Date.now(),
    duration: 3600000 // 1 hour
  }
});
```

#### stopRecording()

Stop recording events.

```typescript
stopRecording(recordingId: string): EventRecording
```

**Parameters:**
- `recordingId` - ID of the recording to stop

**Returns:** Event recording data

**Example:**
```typescript
const recording = eventReplay.stopRecording(recordingId);
console.log(`Recorded ${recording.events.length} events`);
```

#### replay()

Replay recorded events.

```typescript
replay(
  recording: EventRecording, 
  options?: ReplayOptions
): Promise<ReplayResult>
```

**Parameters:**
- `recording` - Recording to replay
- `options` - Optional replay options

**Returns:** Promise resolving to replay result

**Example:**
```typescript
const result = await eventReplay.replay(recording, {
  speed: 2.0, // 2x speed
  skipErrors: true,
  filter: (event) => event.name.startsWith('user:')
});

console.log(`Replayed ${result.eventCount} events`);
```

## Advanced Event Patterns

### Event Sourcing

```typescript
class EventStore {
  private events: StoredEvent[] = [];
  private snapshots = new Map<string, any>();
  
  append(event: DomainEvent): void {
    const storedEvent: StoredEvent = {
      id: this.generateId(),
      aggregateId: event.aggregateId,
      eventType: event.constructor.name,
      eventData: event,
      version: this.getNextVersion(event.aggregateId),
      timestamp: Date.now()
    };
    
    this.events.push(storedEvent);
    this.updateSnapshot(event.aggregateId);
  }
  
  getEvents(aggregateId: string, fromVersion?: number): StoredEvent[] {
    return this.events.filter(event => 
      event.aggregateId === aggregateId &&
      (!fromVersion || event.version >= fromVersion)
    );
  }
  
  replayEvents(aggregateId: string): any {
    const snapshot = this.snapshots.get(aggregateId);
    const events = this.getEvents(aggregateId, snapshot?.version);
    
    let aggregate = snapshot?.data || this.createAggregate(aggregateId);
    
    for (const event of events) {
      aggregate = this.applyEvent(aggregate, event.eventData);
    }
    
    return aggregate;
  }
  
  private updateSnapshot(aggregateId: string): void {
    const eventCount = this.getEvents(aggregateId).length;
    
    // Create snapshot every 100 events
    if (eventCount % 100 === 0) {
      const aggregate = this.replayEvents(aggregateId);
      this.snapshots.set(aggregateId, {
        data: aggregate,
        version: eventCount,
        timestamp: Date.now()
      });
    }
  }
}
```

### CQRS Pattern

```typescript
class CommandHandler {
  constructor(
    private eventStore: EventStore,
    private eventBus: EventBus
  ) {}
  
  async handle(command: Command): Promise<void> {
    // Load aggregate from event store
    const aggregate = this.eventStore.replayEvents(command.aggregateId);
    
    // Execute command on aggregate
    const events = aggregate.handle(command);
    
    // Store events
    for (const event of events) {
      this.eventStore.append(event);
      
      // Publish event
      await this.eventBus.emit(event.constructor.name, event);
    }
  }
}

class QueryHandler {
  constructor(private readModel: ReadModel) {}
  
  async handle(query: Query): Promise<any> {
    return this.readModel.query(query);
  }
}

class EventHandler {
  constructor(private readModel: ReadModel) {}
  
  async handle(event: DomainEvent): Promise<void> {
    // Update read model based on event
    await this.readModel.update(event);
  }
}
```

### Saga Pattern

```typescript
class SagaManager {
  private sagas = new Map<string, Saga>();
  
  constructor(private eventBus: EventBus) {
    this.setupEventHandlers();
  }
  
  private setupEventHandlers(): void {
    this.eventBus.on('*', (event, data) => {
      this.handleEvent(event, data);
    });
  }
  
  private async handleEvent(eventName: string, eventData: any): Promise<void> {
    for (const [sagaId, saga] of this.sagas) {
      if (saga.canHandle(eventName)) {
        try {
          const commands = await saga.handle(eventName, eventData);
          
          // Execute generated commands
          for (const command of commands) {
            await this.executeCommand(command);
          }
          
          // Check if saga is complete
          if (saga.isComplete()) {
            this.sagas.delete(sagaId);
          }
        } catch (error) {
          await this.handleSagaError(saga, error);
        }
      }
    }
  }
  
  startSaga(saga: Saga): void {
    this.sagas.set(saga.id, saga);
  }
  
  private async executeCommand(command: Command): Promise<void> {
    this.eventBus.emit('command:execute', command);
  }
  
  private async handleSagaError(saga: Saga, error: Error): Promise<void> {
    console.error(`Saga ${saga.id} error:`, error);
    
    // Implement compensation logic
    const compensationCommands = saga.getCompensationCommands();
    for (const command of compensationCommands) {
      await this.executeCommand(command);
    }
  }
}
```

## Type Definitions

### Complete TypeScript Definitions

```typescript
// Event Manager types
export interface EventManagerConfig {
  namespace?: string;
  isolation?: boolean;
  historySize?: number;
  debugging?: boolean;
  interceptors?: EventInterceptor[];
  monitoring?: EventMonitoringConfig;
  persistence?: EventPersistenceConfig;
}

export interface EventContext {
  appName?: string;
  priority?: 'high' | 'normal' | 'low';
  broadcast?: boolean;
  timestamp?: number;
  metadata?: Record<string, any>;
}

export interface EventResult {
  success: boolean;
  handlerCount: number;
  errors: Error[];
  duration: number;
}

export interface HandlerOptions {
  priority?: 'high' | 'normal' | 'low';
  persistent?: boolean;
  filter?: (data: any) => boolean;
  timeout?: number;
}

export type EventHandler = (
  event: string,
  data: any,
  context: EventContext
) => void | Promise<void>;

// Event Bridge types
export interface EventBridgeConfig {
  name: string;
  source: BridgeEndpoint;
  target: BridgeEndpoint;
  transform?: EventTransformer;
  filter?: EventFilter;
  bidirectional?: boolean;
  onError?: (error: Error, event: string, data: any) => void;
}

export interface BridgeEndpoint {
  type: 'eventbus' | 'postmessage' | 'websocket' | 'custom';
  instance?: any;
  window?: Window;
  origin?: string;
  url?: string;
}

export interface TransformedEvent {
  event: string;
  data: any;
  metadata?: Record<string, any>;
}

export type EventTransformer = (event: string, data: any) => TransformedEvent;
export type EventFilter = (event: string, data: any) => boolean;

// Event Router types
export interface EventRouterConfig {
  strategy?: 'broadcast' | 'unicast' | 'multicast';
  routes?: EventRoute[];
  fallback?: EventHandler;
  caching?: boolean;
}

export interface EventRoute {
  pattern: string;
  targets: string[];
  condition?: (event: string, data: any) => boolean;
  transform?: (event: string, data: any) => any;
  priority?: 'high' | 'normal' | 'low';
}

export interface RoutingResult {
  targetCount: number;
  successCount: number;
  errors: Error[];
  duration: number;
}

// Event Validator types
export interface EventValidatorConfig {
  schemas?: Record<string, EventSchema>;
  strict?: boolean;
  validators?: Record<string, CustomValidator>;
  onValidationError?: (error: ValidationError) => void;
}

export interface EventSchema {
  type: string;
  required?: string[];
  properties?: Record<string, any>;
  additionalProperties?: boolean;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  value: any;
}

export type CustomValidator = (data: any) => ValidationResult;

// Event Aggregator types
export interface EventAggregatorConfig {
  windowSize: number;
  strategy: 'count' | 'sum' | 'average' | 'custom';
  aggregator?: (events: AggregatedEvent[]) => any;
  triggers?: AggregationTrigger[];
}

export interface AggregatedEvent {
  event: string;
  data: any;
  timestamp: number;
}

export interface AggregationTrigger {
  condition: (aggregatedData: any) => boolean;
  action: (aggregatedData: any) => void;
}

export type AggregationHandler = (aggregatedData: any) => void;

// Event Replay types
export interface EventReplayConfig {
  maxRecordingSize?: number;
  filters?: EventFilter[];
  storage?: 'memory' | 'localStorage' | 'indexedDB';
  compression?: boolean;
}

export interface EventRecording {
  id: string;
  name: string;
  events: RecordedEvent[];
  startTime: number;
  endTime: number;
  metadata: Record<string, any>;
}

export interface RecordedEvent {
  event: string;
  data: any;
  timestamp: number;
  context: EventContext;
}

export interface ReplayOptions {
  speed?: number;
  skipErrors?: boolean;
  filter?: (event: RecordedEvent) => boolean;
  startTime?: number;
  endTime?: number;
}

export interface ReplayResult {
  eventCount: number;
  successCount: number;
  errorCount: number;
  duration: number;
}

// History types
export interface EventHistoryEntry {
  id: string;
  event: string;
  data: any;
  context: EventContext;
  timestamp: number;
  handlerCount: number;
  duration: number;
}

export interface EventHistoryFilter {
  eventPattern?: string;
  timeRange?: {
    start: number;
    end: number;
  };
  appName?: string;
  minHandlerCount?: number;
}

// Monitoring types
export interface EventMonitoringConfig {
  enabled: boolean;
  metrics: string[];
  interval: number;
  onMetric: (metric: string, value: number) => void;
}

export interface EventPersistenceConfig {
  enabled: boolean;
  storage: 'localStorage' | 'sessionStorage' | 'indexedDB';
  key: string;
  maxSize: number;
}

// Advanced pattern types
export interface DomainEvent {
  aggregateId: string;
  version: number;
  timestamp: number;
}

export interface StoredEvent {
  id: string;
  aggregateId: string;
  eventType: string;
  eventData: DomainEvent;
  version: number;
  timestamp: number;
}

export interface Command {
  aggregateId: string;
  commandType: string;
  data: any;
}

export interface Query {
  queryType: string;
  parameters: any;
}

export interface Saga {
  id: string;
  canHandle(eventName: string): boolean;
  handle(eventName: string, eventData: any): Promise<Command[]>;
  isComplete(): boolean;
  getCompensationCommands(): Command[];
}

export interface EventInterceptor {
  intercept(event: string, data: any, context: EventContext): Promise<{
    event: string;
    data: any;
    context: EventContext;
  }>;
}
```

---

This completes the Event System API documentation. For related APIs, see:

- **[Core API](/en/api/core)** - Main Micro-Core functionality
- **[Communication API](/en/api/communication)** - Inter-app communication
- **[Plugin API](/en/api/plugin-api)** - Plugin development
- **[Adapter API](/en/api/adapter-api)** - Framework adapter development