# Plugins API

Micro-Core provides a powerful plugin system that allows you to extend functionality without modifying the core.

## Plugin Architecture

### Plugin Interface

```typescript
interface MicroCorePlugin {
  name: string;
  version: string;
  install(microCore: MicroCore, options?: any): Promise<void> | void;
  uninstall?(microCore: MicroCore): Promise<void> | void;
}
```

### Basic Plugin

```typescript
class MyPlugin implements MicroCorePlugin {
  name = 'my-plugin';
  version = '1.0.0';
  
  async install(microCore: MicroCore, options: any = {}) {
    console.log('Installing MyPlugin');
    
    // Add methods to microCore
    microCore.myMethod = () => {
      console.log('Custom method called');
    };
    
    // Listen to events
    microCore.on('app:mounted', this.handleAppMounted);
  }
  
  async uninstall(microCore: MicroCore) {
    console.log('Uninstalling MyPlugin');
    
    // Cleanup
    delete microCore.myMethod;
    microCore.off('app:mounted', this.handleAppMounted);
  }
  
  private handleAppMounted = (app: any) => {
    console.log(`App ${app.name} mounted`);
  };
}
```

## Using Plugins

### Installing Plugins

```typescript
import { MicroCore } from '@micro-core/core';
import MyPlugin from './my-plugin';

const microCore = new MicroCore();

// Install plugin
await microCore.use(MyPlugin, {
  option1: 'value1',
  option2: true
});

// Install multiple plugins
await microCore.use([
  [MyPlugin, { option1: 'value1' }],
  [AnotherPlugin, { option2: 'value2' }]
]);
```

### Plugin Management

```typescript
// Check if plugin is installed
const isInstalled = microCore.hasPlugin('my-plugin');

// Get plugin instance
const plugin = microCore.getPlugin('my-plugin');

// Get all plugins
const allPlugins = microCore.getPlugins();

// Uninstall plugin
await microCore.unuse('my-plugin');
```

## Built-in Plugins

### Router Plugin

```typescript
import { RouterPlugin } from '@micro-core/plugin-router';

await microCore.use(RouterPlugin, {
  mode: 'history',
  base: '/app/',
  routes: [
    {
      path: '/user',
      app: 'user-app'
    },
    {
      path: '/admin',
      app: 'admin-app',
      beforeEnter: (to, from, next) => {
        if (hasAdminPermission()) {
          next();
        } else {
          next('/unauthorized');
        }
      }
    }
  ]
});
```

### State Plugin

```typescript
import { StatePlugin } from '@micro-core/plugin-state';

await microCore.use(StatePlugin, {
  persistence: {
    enabled: true,
    storage: 'localStorage'
  },
  modules: {
    user: {
      state: { id: null, name: '' },
      mutations: {
        setUser(state, user) {
          Object.assign(state, user);
        }
      }
    }
  }
});
```

### Communication Plugin

```typescript
import { CommunicationPlugin } from '@micro-core/plugin-communication';

await microCore.use(CommunicationPlugin, {
  enableGlobalState: true,
  enableEventBus: true,
  channels: {
    'app1-app2': {
      type: 'direct',
      apps: ['app1', 'app2']
    },
    'broadcast': {
      type: 'broadcast',
      apps: '*'
    }
  }
});
```

## Plugin Development

### Plugin Hooks

```typescript
class AdvancedPlugin implements MicroCorePlugin {
  name = 'advanced-plugin';
  version = '1.0.0';
  
  async install(microCore: MicroCore, options: any) {
    // Hook into application lifecycle
    microCore.addHook('app:before-load', this.beforeLoad);
    microCore.addHook('app:after-mount', this.afterMount);
    
    // Add custom API
    microCore.addAPI('myAPI', {
      doSomething: this.doSomething,
      getData: this.getData
    });
  }
  
  private beforeLoad = async (app: any) => {
    console.log(`Before loading ${app.name}`);
    // Modify app configuration
    app.customProperty = 'added by plugin';
  };
  
  private afterMount = async (app: any) => {
    console.log(`After mounting ${app.name}`);
    // Post-mount operations
  };
  
  private doSomething = (data: any) => {
    // Custom functionality
    return processData(data);
  };
  
  private getData = async () => {
    // Fetch data
    return await fetchPluginData();
  };
}
```

### Plugin Configuration

```typescript
interface PluginOptions {
  enabled?: boolean;
  debug?: boolean;
  [key: string]: any;
}

class ConfigurablePlugin implements MicroCorePlugin {
  name = 'configurable-plugin';
  version = '1.0.0';
  private options: PluginOptions;
  
  async install(microCore: MicroCore, options: PluginOptions = {}) {
    this.options = {
      enabled: true,
      debug: false,
      ...options
    };
    
    if (!this.options.enabled) {
      return;
    }
    
    if (this.options.debug) {
      console.log('Debug mode enabled for plugin');
    }
    
    // Plugin logic
    this.setupPlugin(microCore);
  }
  
  private setupPlugin(microCore: MicroCore) {
    // Setup based on configuration
  }
}
```

### Plugin Dependencies

```typescript
class DependentPlugin implements MicroCorePlugin {
  name = 'dependent-plugin';
  version = '1.0.0';
  dependencies = ['router-plugin', 'state-plugin'];
  
  async install(microCore: MicroCore, options: any) {
    // Check dependencies
    for (const dep of this.dependencies) {
      if (!microCore.hasPlugin(dep)) {
        throw new Error(`Plugin ${dep} is required`);
      }
    }
    
    // Use dependency APIs
    const router = microCore.getPlugin('router-plugin');
    const state = microCore.getPlugin('state-plugin');
    
    // Plugin logic using dependencies
  }
}
```

## Plugin Types

### Middleware Plugin

```typescript
class MiddlewarePlugin implements MicroCorePlugin {
  name = 'middleware-plugin';
  version = '1.0.0';
  
  async install(microCore: MicroCore, options: any) {
    // Add middleware to request pipeline
    microCore.addMiddleware('request', this.requestMiddleware);
    microCore.addMiddleware('response', this.responseMiddleware);
  }
  
  private requestMiddleware = async (context: any, next: Function) => {
    console.log('Processing request:', context.url);
    
    // Modify request
    context.headers['X-Plugin'] = 'middleware-plugin';
    
    // Continue to next middleware
    await next();
  };
  
  private responseMiddleware = async (context: any, next: Function) => {
    await next();
    
    console.log('Processing response:', context.status);
    
    // Modify response
    if (context.status === 404) {
      context.body = 'Custom 404 page';
    }
  };
}
```

### Service Plugin

```typescript
class ServicePlugin implements MicroCorePlugin {
  name = 'service-plugin';
  version = '1.0.0';
  private apiService: any;
  
  async install(microCore: MicroCore, options: any) {
    // Initialize service
    this.apiService = new APIService(options.apiUrl);
    
    // Register service
    microCore.registerService('api', this.apiService);
    
    // Add service methods to microCore
    microCore.api = {
      get: this.apiService.get.bind(this.apiService),
      post: this.apiService.post.bind(this.apiService),
      put: this.apiService.put.bind(this.apiService),
      delete: this.apiService.delete.bind(this.apiService)
    };
  }
  
  async uninstall(microCore: MicroCore) {
    // Cleanup service
    if (this.apiService) {
      await this.apiService.destroy();
    }
    
    // Remove service
    microCore.unregisterService('api');
    delete microCore.api;
  }
}
```

### UI Plugin

```typescript
class UIPlugin implements MicroCorePlugin {
  name = 'ui-plugin';
  version = '1.0.0';
  private components: Map<string, any> = new Map();
  
  async install(microCore: MicroCore, options: any) {
    // Register UI components
    this.registerComponents(microCore);
    
    // Add UI methods
    microCore.ui = {
      showModal: this.showModal,
      hideModal: this.hideModal,
      showNotification: this.showNotification,
      showLoading: this.showLoading,
      hideLoading: this.hideLoading
    };
  }
  
  private registerComponents(microCore: MicroCore) {
    // Register modal component
    this.components.set('modal', new ModalComponent());
    this.components.set('notification', new NotificationComponent());
    this.components.set('loading', new LoadingComponent());
  }
  
  private showModal = (options: any) => {
    const modal = this.components.get('modal');
    modal.show(options);
  };
  
  private hideModal = () => {
    const modal = this.components.get('modal');
    modal.hide();
  };
  
  private showNotification = (message: string, type: string = 'info') => {
    const notification = this.components.get('notification');
    notification.show({ message, type });
  };
  
  private showLoading = (message?: string) => {
    const loading = this.components.get('loading');
    loading.show(message);
  };
  
  private hideLoading = () => {
    const loading = this.components.get('loading');
    loading.hide();
  };
}
```

## Plugin Events

### Plugin Lifecycle Events

```typescript
// Plugin installed
microCore.on('plugin:installed', (plugin) => {
  console.log(`Plugin ${plugin.name} installed`);
});

// Plugin uninstalled
microCore.on('plugin:uninstalled', (pluginName) => {
  console.log(`Plugin ${pluginName} uninstalled`);
});

// Plugin error
microCore.on('plugin:error', (plugin, error) => {
  console.error(`Plugin ${plugin.name} error:`, error);
});
```

### Custom Plugin Events

```typescript
class EventfulPlugin implements MicroCorePlugin {
  name = 'eventful-plugin';
  version = '1.0.0';
  
  async install(microCore: MicroCore, options: any) {
    // Emit custom events
    microCore.emit('plugin:eventful:ready', { plugin: this });
    
    // Listen to app events and emit plugin events
    microCore.on('app:mounted', (app) => {
      microCore.emit('plugin:eventful:app-processed', { app, plugin: this });
    });
  }
}

// Listen to custom plugin events
microCore.on('plugin:eventful:ready', (data) => {
  console.log('Eventful plugin is ready:', data.plugin);
});

microCore.on('plugin:eventful:app-processed', (data) => {
  console.log('App processed by eventful plugin:', data.app.name);
});
```

## Advanced Plugin Features

### Plugin Hot Reload

```typescript
class HotReloadablePlugin implements MicroCorePlugin {
  name = 'hot-reloadable-plugin';
  version = '1.0.0';
  private hotReloadEnabled = false;
  
  async install(microCore: MicroCore, options: any) {
    this.hotReloadEnabled = options.hotReload || false;
    
    if (this.hotReloadEnabled && process.env.NODE_ENV === 'development') {
      this.setupHotReload(microCore);
    }
    
    // Plugin logic
    this.setupPlugin(microCore);
  }
  
  private setupHotReload(microCore: MicroCore) {
    // Watch for plugin file changes
    if (typeof window !== 'undefined' && window.WebSocket) {
      const ws = new WebSocket('ws://localhost:8080/plugin-reload');
      
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.plugin === this.name) {
          this.reload(microCore);
        }
      };
    }
  }
  
  private async reload(microCore: MicroCore) {
    console.log(`Hot reloading plugin: ${this.name}`);
    
    // Uninstall current version
    await this.uninstall(microCore);
    
    // Reinstall with new code
    const newPlugin = await import('./hot-reloadable-plugin?t=' + Date.now());
    await microCore.use(newPlugin.default);
  }
  
  private setupPlugin(microCore: MicroCore) {
    // Plugin implementation
  }
}
```

### Plugin Composition

```typescript
// Base plugin with common functionality
abstract class BasePlugin implements MicroCorePlugin {
  abstract name: string;
  abstract version: string;
  
  protected microCore: MicroCore;
  protected options: any;
  
  async install(microCore: MicroCore, options: any = {}) {
    this.microCore = microCore;
    this.options = options;
    
    // Common setup
    this.setupCommon();
    
    // Plugin-specific setup
    await this.setupPlugin();
  }
  
  protected setupCommon() {
    // Common functionality for all plugins
    this.microCore.on('app:error', this.handleAppError);
  }
  
  protected abstract setupPlugin(): Promise<void>;
  
  protected handleAppError = (app: any, error: Error) => {
    console.error(`App ${app.name} error handled by ${this.name}:`, error);
  };
}

// Specific plugin extending base
class SpecificPlugin extends BasePlugin {
  name = 'specific-plugin';
  version = '1.0.0';
  
  protected async setupPlugin() {
    // Specific plugin logic
    this.microCore.specificFeature = this.specificFeature;
  }
  
  private specificFeature = (data: any) => {
    // Specific functionality
    return this.processSpecificData(data);
  };
  
  private processSpecificData(data: any) {
    // Process data
    return data;
  }
}
```

## Plugin Testing

### Unit Testing

```typescript
// plugin.test.ts
import { MicroCore } from '@micro-core/core';
import MyPlugin from './my-plugin';

describe('MyPlugin', () => {
  let microCore: MicroCore;
  let plugin: MyPlugin;
  
  beforeEach(() => {
    microCore = new MicroCore();
    plugin = new MyPlugin();
  });
  
  afterEach(async () => {
    if (microCore.hasPlugin('my-plugin')) {
      await microCore.unuse('my-plugin');
    }
  });
  
  test('should install plugin correctly', async () => {
    await microCore.use(plugin);
    
    expect(microCore.hasPlugin('my-plugin')).toBe(true);
    expect(microCore.myMethod).toBeDefined();
  });
  
  test('should handle app mounted event', async () => {
    const handleAppMounted = jest.spyOn(plugin, 'handleAppMounted');
    
    await microCore.use(plugin);
    
    // Simulate app mounted event
    microCore.emit('app:mounted', { name: 'test-app' });
    
    expect(handleAppMounted).toHaveBeenCalledWith({ name: 'test-app' });
  });
  
  test('should uninstall plugin correctly', async () => {
    await microCore.use(plugin);
    await microCore.unuse('my-plugin');
    
    expect(microCore.hasPlugin('my-plugin')).toBe(false);
    expect(microCore.myMethod).toBeUndefined();
  });
});
```

### Integration Testing

```typescript
// integration.test.ts
import { MicroCore } from '@micro-core/core';
import MyPlugin from './my-plugin';

describe('Plugin Integration', () => {
  let microCore: MicroCore;
  
  beforeEach(() => {
    microCore = new MicroCore();
  });
  
  test('should work with multiple plugins', async () => {
    await microCore.use([
      MyPlugin,
      AnotherPlugin,
      ThirdPlugin
    ]);
    
    // Test plugin interactions
    expect(microCore.hasPlugin('my-plugin')).toBe(true);
    expect(microCore.hasPlugin('another-plugin')).toBe(true);
    expect(microCore.hasPlugin('third-plugin')).toBe(true);
    
    // Test combined functionality
    const result = microCore.combinedMethod();
    expect(result).toBeDefined();
  });
  
  test('should handle plugin dependencies', async () => {
    // Install dependency first
    await microCore.use(DependencyPlugin);
    
    // Then install dependent plugin
    await microCore.use(DependentPlugin);
    
    expect(microCore.hasPlugin('dependency-plugin')).toBe(true);
    expect(microCore.hasPlugin('dependent-plugin')).toBe(true);
  });
});
```

## Best Practices

### 1. Plugin Naming

```typescript
// Good - Clear, descriptive names
class RouterPlugin implements MicroCorePlugin {
  name = 'router-plugin';
  version = '1.0.0';
}

// Avoid - Generic or unclear names
class Plugin implements MicroCorePlugin {
  name = 'plugin';
  version = '1.0.0';
}
```

### 2. Error Handling

```typescript
class RobustPlugin implements MicroCorePlugin {
  name = 'robust-plugin';
  version = '1.0.0';
  
  async install(microCore: MicroCore, options: any) {
    try {
      // Plugin setup
      await this.setupPlugin(microCore, options);
    } catch (error) {
      console.error(`Failed to install ${this.name}:`, error);
      
      // Cleanup partial installation
      await this.cleanup(microCore);
      
      throw error;
    }
  }
  
  private async setupPlugin(microCore: MicroCore, options: any) {
    // Plugin implementation with error handling
    if (!options.required) {
      throw new Error('Required option is missing');
    }
    
    // Setup with validation
    this.validateOptions(options);
    this.initializePlugin(microCore, options);
  }
  
  private validateOptions(options: any) {
    // Validate plugin options
    if (typeof options.required !== 'string') {
      throw new Error('Required option must be a string');
    }
  }
  
  private async cleanup(microCore: MicroCore) {
    // Cleanup partial installation
    try {
      await this.uninstall(microCore);
    } catch (cleanupError) {
      console.error('Cleanup failed:', cleanupError);
    }
  }
}
```

### 3. Resource Management

```typescript
class ResourceManagedPlugin implements MicroCorePlugin {
  name = 'resource-managed-plugin';
  version = '1.0.0';
  private resources: any[] = [];
  private timers: NodeJS.Timeout[] = [];
  private listeners: Array<() => void> = [];
  
  async install(microCore: MicroCore, options: any) {
    // Track resources
    const resource = await this.createResource();
    this.resources.push(resource);
    
    // Track timers
    const timer = setInterval(this.periodicTask, 1000);
    this.timers.push(timer);
    
    // Track event listeners
    const unlisten = microCore.on('app:mounted', this.handleAppMounted);
    this.listeners.push(unlisten);
  }
  
  async uninstall(microCore: MicroCore) {
    // Cleanup resources
    for (const resource of this.resources) {
      await resource.destroy();
    }
    this.resources = [];
    
    // Clear timers
    for (const timer of this.timers) {
      clearInterval(timer);
    }
    this.timers = [];
    
    // Remove listeners
    for (const unlisten of this.listeners) {
      unlisten();
    }
    this.listeners = [];
  }
  
  private async createResource() {
    // Create and return resource
    return new SomeResource();
  }
  
  private periodicTask = () => {
    // Periodic task
  };
  
  private handleAppMounted = (app: any) => {
    // Handle app mounted
  };
}
```

### 4. Plugin Configuration

```typescript
interface PluginConfig {
  enabled?: boolean;
  debug?: boolean;
  [key: string]: any;
}

class ConfigurablePlugin implements MicroCorePlugin {
  name = 'configurable-plugin';
  version = '1.0.0';
  private config: PluginConfig;
  
  async install(microCore: MicroCore, options: PluginConfig = {}) {
    // Merge with defaults
    this.config = {
      enabled: true,
      debug: false,
      ...options
    };
    
    // Validate configuration
    this.validateConfig(this.config);
    
    if (!this.config.enabled) {
      console.log(`Plugin ${this.name} is disabled`);
      return;
    }
    
    // Setup plugin with configuration
    this.setupWithConfig(microCore);
  }
  
  private validateConfig(config: PluginConfig) {
    // Validate configuration
    if (typeof config.enabled !== 'boolean') {
      throw new Error('enabled option must be a boolean');
    }
  }
  
  private setupWithConfig(microCore: MicroCore) {
    // Setup based on configuration
    if (this.config.debug) {
      console.log(`Setting up ${this.name} in debug mode`);
    }
  }
}
```

## Type Definitions

```typescript
interface MicroCorePlugin {
  name: string;
  version: string;
  dependencies?: string[];
  install(microCore: MicroCore, options?: any): Promise<void> | void;
  uninstall?(microCore: MicroCore): Promise<void> | void;
}

interface PluginManager {
  use(plugin: MicroCorePlugin | MicroCorePlugin[], options?: any): Promise<void>;
  unuse(pluginName: string): Promise<void>;
  hasPlugin(pluginName: string): boolean;
  getPlugin(pluginName: string): MicroCorePlugin | undefined;
  getPlugins(): MicroCorePlugin[];
}

interface PluginHook {
  (context: any, next?: Function): Promise<void> | void;
}

interface PluginAPI {
  [key: string]: any;
}
```

## References

- [Core API](./core.md)
- [Plugin Development Guide](../guide/plugin-development.md)
- [Built-in Plugins](../guide/built-in-plugins.md)
- [Best Practices](../guide/best-practices.md)
