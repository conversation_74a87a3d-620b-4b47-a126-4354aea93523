# Adapters API

Micro-Core provides framework adapters to seamlessly integrate with different frontend frameworks.

## Overview

Adapters bridge the gap between Micro-Core and specific frontend frameworks, handling framework-specific lifecycle requirements and optimizations.

## React Adapter

### Installation

```bash
npm install @micro-core/adapter-react
```

### Basic Usage

```typescript
import { createReactAdapter } from '@micro-core/adapter-react';
import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';

export const { bootstrap, mount, unmount, update } = createReactAdapter({
  rootComponent: App,
  container: '#root',
  domElementGetter: (container) => {
    return typeof container === 'string' 
      ? document.querySelector(container)
      : container;
  }
});
```

### Advanced Configuration

```typescript
export const { bootstrap, mount, unmount, update } = createReactAdapter({
  rootComponent: App,
  container: '#root',
  
  // React 18 features
  useCreateRoot: true,
  useConcurrentFeatures: true,
  
  // Error boundary
  errorBoundary: {
    fallback: ({ error, resetError }) => (
      <div>
        <h2>Something went wrong</h2>
        <button onClick={resetError}>Try again</button>
      </div>
    ),
    onError: (error, errorInfo) => {
      console.error('React app error:', error, errorInfo);
    }
  },
  
  // Props transformation
  transformProps: (props) => ({
    ...props,
    basename: props.routerBase || '/'
  })
});
```

### React Router Integration

```typescript
import { BrowserRouter, Routes, Route } from 'react-router-dom';

const App = ({ basename }) => (
  <BrowserRouter basename={basename}>
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/profile" element={<Profile />} />
    </Routes>
  </BrowserRouter>
);

export const { bootstrap, mount, unmount, update } = createReactAdapter({
  rootComponent: App,
  container: '#root',
  transformProps: (props) => ({
    basename: props.routerBase || '/'
  })
});
```

## Vue Adapter

### Installation

```bash
npm install @micro-core/adapter-vue
```

### Vue 3 Usage

```typescript
import { createVueAdapter } from '@micro-core/adapter-vue';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';

export const { bootstrap, mount, unmount, update } = createVueAdapter({
  rootComponent: App,
  container: '#app',
  
  // Vue app configuration
  appOptions: {
    use: [router, store],
    provide: {
      globalConfig: {}
    },
    config: {
      errorHandler: (error, instance, info) => {
        console.error('Vue app error:', error, info);
      }
    }
  },
  
  // Props handling
  propsToAttrs: true
});
```

### Vue 2 Usage

```typescript
import { createVue2Adapter } from '@micro-core/adapter-vue';
import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';

export const { bootstrap, mount, unmount, update } = createVue2Adapter({
  rootComponent: App,
  container: '#app',
  
  // Vue instance options
  vueOptions: {
    router,
    store,
    render: h => h(App)
  }
});
```

## Angular Adapter

### Installation

```bash
npm install @micro-core/adapter-angular
```

### Basic Usage

```typescript
import { createAngularAdapter } from '@micro-core/adapter-angular';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';

export const { bootstrap, mount, unmount, update } = createAngularAdapter({
  module: AppModule,
  container: '#angular-app',
  
  // Platform configuration
  platformFactory: platformBrowserDynamic,
  
  // Bootstrap options
  bootstrapOptions: {
    ngZone: 'zone.js',
    preserveWhitespaces: false
  }
});
```

### Module Configuration

```typescript
// app.module.ts
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { AppComponent } from './app.component';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    RouterModule.forRoot([
      { path: '', component: HomeComponent },
      { path: 'profile', component: ProfileComponent }
    ])
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule {}

// Adapter usage
export const { bootstrap, mount, unmount, update } = createAngularAdapter({
  module: AppModule,
  container: '#angular-app',
  
  // Router base href
  transformProps: (props) => ({
    baseHref: props.routerBase || '/'
  })
});
```

## Svelte Adapter

### Installation

```bash
npm install @micro-core/adapter-svelte
```

### Basic Usage

```typescript
import { createSvelteAdapter } from '@micro-core/adapter-svelte';
import App from './App.svelte';

export const { bootstrap, mount, unmount, update } = createSvelteAdapter({
  component: App,
  container: '#svelte-app',
  
  // Svelte component props
  props: {
    name: 'Micro-Core'
  },
  
  // Component options
  options: {
    hydrate: false,
    intro: true
  }
});
```

### Advanced Configuration

```typescript
export const { bootstrap, mount, unmount, update } = createSvelteAdapter({
  component: App,
  container: '#svelte-app',
  
  // Props transformation
  transformProps: (props) => ({
    ...props,
    theme: props.theme || 'light'
  }),
  
  // Event handling
  onMount: (component) => {
    component.$on('navigate', (event) => {
      // Handle navigation events
      window.history.pushState(null, '', event.detail.path);
    });
  },
  
  onDestroy: (component) => {
    // Cleanup
    component.$destroy();
  }
});
```

## Vanilla JS Adapter

### Basic Usage

```typescript
import { createVanillaAdapter } from '@micro-core/adapter-vanilla';

export const { bootstrap, mount, unmount, update } = createVanillaAdapter({
  container: '#vanilla-app',
  
  // Lifecycle functions
  onBootstrap: async (props) => {
    console.log('Vanilla app bootstrapping');
    // Initialize app
    await initializeApp(props);
  },
  
  onMount: async (props) => {
    const container = document.querySelector(props.container);
    
    // Render app
    container.innerHTML = `
      <div class="vanilla-app">
        <h1>Hello from Vanilla JS</h1>
        <p>Theme: ${props.theme}</p>
      </div>
    `;
    
    // Add event listeners
    setupEventListeners(container);
  },
  
  onUnmount: async (props) => {
    const container = document.querySelector(props.container);
    
    // Cleanup
    removeEventListeners(container);
    container.innerHTML = '';
  },
  
  onUpdate: async (props) => {
    // Update app with new props
    updateApp(props);
  }
});
```

## Custom Adapter

### Creating Custom Adapter

```typescript
import { BaseAdapter } from '@micro-core/core';

class CustomFrameworkAdapter extends BaseAdapter {
  private app: any = null;
  
  async bootstrap(props: any): Promise<void> {
    console.log('Custom framework bootstrapping');
    // Initialize framework-specific setup
  }
  
  async mount(props: any): Promise<void> {
    const container = this.getContainer(props.container);
    
    // Create and mount framework app
    this.app = new CustomFramework({
      container,
      props: this.transformProps(props)
    });
    
    await this.app.mount();
  }
  
  async unmount(props: any): Promise<void> {
    if (this.app) {
      await this.app.unmount();
      this.app = null;
    }
  }
  
  async update(props: any): Promise<void> {
    if (this.app) {
      this.app.updateProps(this.transformProps(props));
    }
  }
  
  private transformProps(props: any): any {
    // Transform props for custom framework
    return {
      ...props,
      customProperty: 'custom-value'
    };
  }
}

// Factory function
export function createCustomAdapter(options: any) {
  const adapter = new CustomFrameworkAdapter(options);
  
  return {
    bootstrap: adapter.bootstrap.bind(adapter),
    mount: adapter.mount.bind(adapter),
    unmount: adapter.unmount.bind(adapter),
    update: adapter.update.bind(adapter)
  };
}
```

## Adapter Configuration

### Common Options

```typescript
interface AdapterOptions {
  // Container selector or element
  container: string | HTMLElement;
  
  // Root component
  rootComponent?: any;
  
  // Props transformation
  transformProps?: (props: any) => any;
  
  // Error handling
  errorBoundary?: {
    fallback?: any;
    onError?: (error: Error, info?: any) => void;
  };
  
  // Lifecycle hooks
  onBootstrap?: (props: any) => Promise<void>;
  onMount?: (props: any) => Promise<void>;
  onUnmount?: (props: any) => Promise<void>;
  onUpdate?: (props: any) => Promise<void>;
}
```

### Framework-Specific Options

```typescript
// React-specific options
interface ReactAdapterOptions extends AdapterOptions {
  useCreateRoot?: boolean;
  useConcurrentFeatures?: boolean;
  domElementGetter?: (container: any) => HTMLElement;
}

// Vue-specific options
interface VueAdapterOptions extends AdapterOptions {
  appOptions?: {
    use?: any[];
    provide?: Record<string, any>;
    config?: Record<string, any>;
  };
  propsToAttrs?: boolean;
}

// Angular-specific options
interface AngularAdapterOptions extends AdapterOptions {
  module: any;
  platformFactory?: any;
  bootstrapOptions?: Record<string, any>;
}
```

## Adapter Events

### Lifecycle Events

```typescript
// Listen to adapter events
microCore.on('adapter:bootstrap', (appName, adapter) => {
  console.log(`Adapter bootstrapped for ${appName}`);
});

microCore.on('adapter:mount', (appName, adapter) => {
  console.log(`Adapter mounted for ${appName}`);
});

microCore.on('adapter:unmount', (appName, adapter) => {
  console.log(`Adapter unmounted for ${appName}`);
});

microCore.on('adapter:error', (appName, adapter, error) => {
  console.error(`Adapter error for ${appName}:`, error);
});
```

## Best Practices

### 1. Props Handling

```typescript
// Good - Transform props appropriately
transformProps: (props) => ({
  ...props,
  // Convert router base for framework
  basename: props.routerBase || '/',
  // Provide framework-specific defaults
  theme: props.theme || 'light'
})

// Avoid - Passing all props directly
// This may cause issues with framework-specific props
```

### 2. Error Boundaries

```typescript
// Always provide error boundaries
errorBoundary: {
  fallback: ({ error, resetError }) => (
    <div className="error-boundary">
      <h2>Application Error</h2>
      <p>{error.message}</p>
      <button onClick={resetError}>Retry</button>
    </div>
  ),
  onError: (error, errorInfo) => {
    // Log error for debugging
    console.error('Adapter error:', error, errorInfo);
    
    // Report to error tracking service
    errorTracker.report(error, { context: 'micro-app-adapter' });
  }
}
```

### 3. Memory Management

```typescript
// Proper cleanup in unmount
onUnmount: async (props) => {
  // Remove event listeners
  removeEventListeners();
  
  // Clear timers
  clearAllTimers();
  
  // Cleanup framework resources
  if (this.app) {
    await this.app.destroy();
    this.app = null;
  }
}
```

### 4. Performance Optimization

```typescript
// Lazy load heavy dependencies
onBootstrap: async (props) => {
  // Only load when needed
  const { heavyLibrary } = await import('./heavy-library');
  this.heavyLibrary = heavyLibrary;
}

// Use framework-specific optimizations
// React: useMemo, useCallback, React.memo
// Vue: computed, watch with deep option
// Angular: OnPush change detection
```

## Type Definitions

```typescript
interface LifecycleFunctions {
  bootstrap?: (props: any) => Promise<void>;
  mount?: (props: any) => Promise<void>;
  unmount?: (props: any) => Promise<void>;
  update?: (props: any) => Promise<void>;
}

interface BaseAdapter {
  bootstrap(props: any): Promise<void>;
  mount(props: any): Promise<void>;
  unmount(props: any): Promise<void>;
  update(props: any): Promise<void>;
}

interface AdapterFactory<T = any> {
  (options: T): LifecycleFunctions;
}
```

## References

- [Core API](./core.md)
- [Application Management](./app-management.md)
- [Framework Integration Guide](../guide/framework-integration.md)
- [Best Practices](../guide/best-practices.md)