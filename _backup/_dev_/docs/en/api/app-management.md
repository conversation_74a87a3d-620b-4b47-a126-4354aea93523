# Application Management API

Micro-Core provides comprehensive application management capabilities for registering, loading, mounting, and controlling micro-applications.

## MicroCore Class

The main class for managing micro-applications.

### Constructor

```typescript
import { MicroCore, type MicroCoreConfig } from '@micro-core/core';

const microCore = new MicroCore(config?: MicroCoreConfig);
```

### Configuration

```typescript
interface MicroCoreConfig {
  container?: string | HTMLElement;
  router?: RouterConfig;
  sandbox?: SandboxConfig;
  prefetch?: PrefetchConfig;
  globalState?: Record<string, any>;
  errorHandler?: ErrorHandlerConfig;
  debug?: boolean;
  fetch?: (url: string, options?: RequestInit) => Promise<Response>;
}
```

## Application Registration

### registerApp()

Register a micro-application.

```typescript
registerApp(app: AppConfig): Promise<void>
```

**Parameters:**

```typescript
interface AppConfig {
  name: string;
  entry: string | { scripts?: string[]; styles?: string[] };
  container: string | HTMLElement;
  activeRule: string | RegExp | ((location: Location) => boolean);
  loader?: () => Promise<LifecycleFns>;
  props?: Record<string, any>;
  sandbox?: boolean | SandboxConfig;
  prefetch?: boolean | PrefetchConfig;
}
```

**Example:**

```typescript
await microCore.registerApp({
  name: 'user-center',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeRule: '/user',
  props: {
    theme: 'light',
    apiUrl: process.env.API_URL
  },
  sandbox: {
    type: 'proxy',
    css: true,
    js: true
  }
});
```

### unregisterApp()

Unregister a micro-application.

```typescript
unregisterApp(name: string): Promise<void>
```

**Example:**

```typescript
await microCore.unregisterApp('user-center');
```

## Application Control

### start()

Start the micro-core system.

```typescript
start(): Promise<void>
```

**Example:**

```typescript
await microCore.start();
console.log('Micro-Core started successfully');
```

### loadApp()

Manually load an application.

```typescript
loadApp(name: string): Promise<void>
```

### mountApp()

Manually mount an application.

```typescript
mountApp(name: string, props?: Record<string, any>): Promise<void>
```

### unmountApp()

Manually unmount an application.

```typescript
unmountApp(name: string): Promise<void>
```

### updateApp()

Update application props.

```typescript
updateApp(name: string, props: Record<string, any>): Promise<void>
```

**Example:**

```typescript
// Load application
await microCore.loadApp('user-center');

// Mount with props
await microCore.mountApp('user-center', {
  theme: 'dark',
  userId: 123
});

// Update props
await microCore.updateApp('user-center', {
  theme: 'light'
});

// Unmount
await microCore.unmountApp('user-center');
```

## Application Information

### getApps()

Get all registered applications.

```typescript
getApps(): AppInfo[]
```

### getApp()

Get specific application information.

```typescript
getApp(name: string): AppInfo | null
```

### getActiveApps()

Get currently active applications.

```typescript
getActiveApps(): AppInfo[]
```

**Example:**

```typescript
// Get all apps
const allApps = microCore.getApps();
console.log('Registered apps:', allApps.map(app => app.name));

// Get specific app
const userApp = microCore.getApp('user-center');
if (userApp) {
  console.log('User app status:', userApp.status);
}

// Get active apps
const activeApps = microCore.getActiveApps();
console.log('Active apps:', activeApps.map(app => app.name));
```

## Application Status

Applications have the following lifecycle states:

```typescript
type AppStatus = 
  | 'NOT_LOADED'
  | 'LOADING'
  | 'LOADED'
  | 'BOOTSTRAPPING'
  | 'NOT_MOUNTED'
  | 'MOUNTING'
  | 'MOUNTED'
  | 'UNMOUNTING'
  | 'UNLOADING'
  | 'SKIP_BECAUSE_BROKEN'
  | 'LOAD_ERROR';
```

### Application Info

```typescript
interface AppInfo {
  name: string;
  status: AppStatus;
  entry: string;
  container: string | HTMLElement;
  activeRule: string | RegExp | Function;
  props?: Record<string, any>;
  loadTime?: number;
  mountTime?: number;
  error?: Error;
}
```

## Lifecycle Functions

Micro-applications must export lifecycle functions:

```typescript
interface LifecycleFns {
  bootstrap?: (props: any) => Promise<void>;
  mount?: (props: any) => Promise<void>;
  unmount?: (props: any) => Promise<void>;
  update?: (props: any) => Promise<void>;
}
```

### Example Implementation

```typescript
// React micro-application
import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';

let root: any = null;

export async function bootstrap(props: any) {
  console.log('React app bootstrapped');
}

export async function mount(props: any) {
  const { container } = props;
  root = ReactDOM.createRoot(
    typeof container === 'string' 
      ? document.querySelector(container)
      : container
  );
  root.render(<App {...props} />);
}

export async function unmount(props: any) {
  if (root) {
    root.unmount();
    root = null;
  }
}

export async function update(props: any) {
  if (root) {
    root.render(<App {...props} />);
  }
}
```

## Event Hooks

Listen to application lifecycle events:

```typescript
// Application mounted
microCore.on('app:mounted', (app: AppInfo) => {
  console.log(`Application ${app.name} mounted`);
});

// Application unmounted
microCore.on('app:unmounted', (app: AppInfo) => {
  console.log(`Application ${app.name} unmounted`);
});

// Application error
microCore.on('app:error', (app: AppInfo, error: Error) => {
  console.error(`Application ${app.name} error:`, error);
});

// Application status changed
microCore.on('app:status-changed', (app: AppInfo, oldStatus: AppStatus) => {
  console.log(`Application ${app.name} status: ${oldStatus} -> ${app.status}`);
});
```

## Error Handling

### Global Error Handler

```typescript
const microCore = new MicroCore({
  errorHandler: {
    onJSError: (error: Error, app?: AppConfig) => {
      console.error(`JS Error in ${app?.name}:`, error);
      // Report error
      reportError(error, app);
    },
    onLoadError: (error: Error, app?: AppConfig) => {
      console.error(`Load Error in ${app?.name}:`, error);
      // Show fallback UI
      showFallbackUI(app);
    },
    onAppError: (error: Error, app: AppConfig) => {
      console.error(`App Error in ${app.name}:`, error);
      // Handle application-specific error
      handleAppError(error, app);
    }
  }
});
```

### Application-Level Error Boundary

```typescript
await microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeRule: '/user',
  errorBoundary: {
    fallback: '<div>Application failed to load</div>',
    onError: (error: Error) => {
      console.error('User app error:', error);
    }
  }
});
```

## Performance Optimization

### Prefetching

```typescript
// Enable prefetching for specific app
await microCore.registerApp({
  name: 'order-app',
  entry: 'http://localhost:3002',
  container: '#order-app',
  activeRule: '/order',
  prefetch: {
    enabled: true,
    delay: 2000, // Prefetch after 2 seconds
    idle: true   // Prefetch during idle time
  }
});

// Manual prefetch
await microCore.prefetchApp('order-app');
```

### Preloading

```typescript
// Preload application resources
await microCore.preloadApp('user-center');

// Preload multiple applications
await Promise.all([
  microCore.preloadApp('user-center'),
  microCore.preloadApp('order-app')
]);
```

## Advanced Usage

### Dynamic Registration

```typescript
// Register application dynamically based on user permissions
async function registerUserApps(userPermissions: string[]) {
  if (userPermissions.includes('user-management')) {
    await microCore.registerApp({
      name: 'user-management',
      entry: 'http://localhost:3003',
      container: '#user-mgmt',
      activeRule: '/admin/users'
    });
  }
  
  if (userPermissions.includes('order-management')) {
    await microCore.registerApp({
      name: 'order-management',
      entry: 'http://localhost:3004',
      container: '#order-mgmt',
      activeRule: '/admin/orders'
    });
  }
}
```

### Conditional Loading

```typescript
// Load application based on conditions
await microCore.registerApp({
  name: 'mobile-app',
  entry: 'http://localhost:3005',
  container: '#mobile-container',
  activeRule: (location) => {
    // Only load on mobile devices
    return /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) &&
           location.pathname.startsWith('/mobile');
  }
});
```

## Best Practices

### 1. Application Naming

Use consistent naming conventions:

```typescript
// Good
'user-center'
'order-management'
'product-catalog'

// Avoid
'userCenter'
'OrderMgmt'
'products'
```

### 2. Container Management

```typescript
// Use specific containers for each app
await microCore.registerApp({
  name: 'user-app',
  container: '#user-app-container', // Specific container
  // ...
});

// Avoid sharing containers
// container: '#shared-container' // Not recommended
```

### 3. Props Management

```typescript
// Pass only necessary props
await microCore.registerApp({
  name: 'user-app',
  props: {
    theme: 'light',
    apiBaseUrl: process.env.API_URL,
    userId: getCurrentUserId()
  }
  // Avoid passing large objects or functions
});
```

### 4. Error Recovery

```typescript
// Implement error recovery
microCore.on('app:error', async (app, error) => {
  console.error(`App ${app.name} failed:`, error);
  
  // Try to recover
  try {
    await microCore.unmountApp(app.name);
    await microCore.mountApp(app.name);
    console.log(`App ${app.name} recovered`);
  } catch (recoveryError) {
    console.error(`Failed to recover app ${app.name}:`, recoveryError);
    // Show fallback UI
    showFallbackUI(app);
  }
});
```

## Type Definitions

```typescript
interface MicroCoreConfig {
  container?: string | HTMLElement;
  router?: RouterConfig;
  sandbox?: SandboxConfig;
  prefetch?: PrefetchConfig;
  globalState?: Record<string, any>;
  errorHandler?: ErrorHandlerConfig;
  debug?: boolean;
  fetch?: (url: string, options?: RequestInit) => Promise<Response>;
}

interface AppConfig {
  name: string;
  entry: string | { scripts?: string[]; styles?: string[] };
  container: string | HTMLElement;
  activeRule: string | RegExp | ((location: Location) => boolean);
  loader?: () => Promise<LifecycleFns>;
  props?: Record<string, any>;
  sandbox?: boolean | SandboxConfig;
  prefetch?: boolean | PrefetchConfig;
  errorBoundary?: {
    fallback?: string | HTMLElement;
    onError?: (error: Error) => void;
  };
}

interface AppInfo extends AppConfig {
  status: AppStatus;
  loadTime?: number;
  mountTime?: number;
  error?: Error;
}
```

## References

- [Core API](./core.md)
- [Routing API](./routing.md)
- [Sandbox API](./sandbox.md)
- [Getting Started Guide](../guide/getting-started.md)