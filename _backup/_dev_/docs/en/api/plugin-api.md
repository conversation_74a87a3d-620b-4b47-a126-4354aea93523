# Plugin API

The Plugin API enables developers to extend Micro-Core functionality through a comprehensive plugin system.

## Plugin Base Class

Abstract base class for all Micro-Core plugins.

### Constructor

```typescript
abstract class Plugin {
  abstract name: string;
  abstract version: string;
  
  constructor(config?: PluginConfig) {
    this.config = config || {};
  }
}
```

### Required Properties

```typescript
interface Plugin {
  // Unique plugin name
  name: string;
  
  // Plugin version
  version: string;
  
  // Plugin dependencies
  dependencies?: string[];
  
  // Plugin configuration
  config?: PluginConfig;
}
```

### Lifecycle Methods

#### initialize()

Initialize the plugin.

```typescript
abstract initialize(context: PluginContext): Promise<void>
```

**Parameters:**
- `context` - Plugin context with access to core services

**Example:**
```typescript
class MyPlugin extends Plugin {
  name = 'my-plugin';
  version = '1.0.0';
  
  async initialize(context: PluginContext): Promise<void> {
    console.log('Initializing MyPlugin');
    
    // Access core services
    this.eventBus = context.eventBus;
    this.stateManager = context.stateManager;
    this.router = context.router;
    
    // Setup plugin functionality
    await this.setupEventHandlers();
    await this.registerRoutes();
  }
}
```

#### activate()

Activate the plugin.

```typescript
abstract activate(): Promise<void>
```

**Example:**
```typescript
async activate(): Promise<void> {
  console.log('Activating MyPlugin');
  
  // Start plugin services
  this.startBackgroundTasks();
  this.enableFeatures();
  
  // Emit activation event
  this.eventBus.emit('plugin:activated', {
    name: this.name,
    version: this.version
  });
}
```

#### deactivate()

Deactivate the plugin.

```typescript
abstract deactivate(): Promise<void>
```

**Example:**
```typescript
async deactivate(): Promise<void> {
  console.log('Deactivating MyPlugin');
  
  // Stop plugin services
  this.stopBackgroundTasks();
  this.disableFeatures();
  
  // Cleanup resources
  this.cleanup();
}
```

#### destroy()

Destroy the plugin and clean up resources.

```typescript
abstract destroy(): Promise<void>
```

**Example:**
```typescript
async destroy(): Promise<void> {
  console.log('Destroying MyPlugin');
  
  // Remove event listeners
  this.removeEventListeners();
  
  // Clear timers and intervals
  this.clearTimers();
  
  // Release resources
  this.releaseResources();
}
```

## PluginManager Class

Manages plugin lifecycle and dependencies.

### Constructor

```typescript
new PluginManager(config?: PluginManagerConfig)
```

### Methods

#### register()

Register a plugin.

```typescript
register(plugin: Plugin): Promise<void>
```

**Parameters:**
- `plugin` - Plugin instance to register

**Example:**
```typescript
const pluginManager = new PluginManager();

await pluginManager.register(new RouterPlugin({
  mode: 'history',
  base: '/'
}));

await pluginManager.register(new AuthPlugin({
  provider: 'oauth2',
  clientId: 'your-client-id'
}));
```

#### unregister()

Unregister a plugin.

```typescript
unregister(pluginName: string): Promise<void>
```

**Parameters:**
- `pluginName` - Name of the plugin to unregister

**Example:**
```typescript
await pluginManager.unregister('router-plugin');
```

#### activate()

Activate a plugin.

```typescript
activate(pluginName: string): Promise<void>
```

**Parameters:**
- `pluginName` - Name of the plugin to activate

**Example:**
```typescript
await pluginManager.activate('auth-plugin');
```

#### deactivate()

Deactivate a plugin.

```typescript
deactivate(pluginName: string): Promise<void>
```

**Parameters:**
- `pluginName` - Name of the plugin to deactivate

**Example:**
```typescript
await pluginManager.deactivate('auth-plugin');
```

#### getPlugin()

Get a registered plugin.

```typescript
getPlugin<T extends Plugin>(pluginName: string): T | undefined
```

**Parameters:**
- `pluginName` - Name of the plugin

**Returns:** Plugin instance or undefined

**Example:**
```typescript
const routerPlugin = pluginManager.getPlugin<RouterPlugin>('router-plugin');
if (routerPlugin) {
  routerPlugin.navigate('/users');
}
```

#### listPlugins()

List all registered plugins.

```typescript
listPlugins(): PluginInfo[]
```

**Returns:** Array of plugin information

**Example:**
```typescript
const plugins = pluginManager.listPlugins();
plugins.forEach(plugin => {
  console.log(`${plugin.name} v${plugin.version} - ${plugin.status}`);
});
```

## Built-in Plugins

### RouterPlugin

Advanced routing plugin for micro-frontend applications.

```typescript
class RouterPlugin extends Plugin {
  name = 'router-plugin';
  version = '1.0.0';
  
  constructor(config: RouterPluginConfig) {
    super(config);
  }
  
  async initialize(context: PluginContext): Promise<void> {
    this.router = new AdvancedRouter(this.config);
    context.router = this.router;
  }
  
  // Router-specific methods
  navigate(path: string, options?: NavigationOptions): void {
    this.router.navigate(path, options);
  }
  
  addRoute(route: RouteConfig): void {
    this.router.addRoute(route);
  }
  
  removeRoute(routeId: string): void {
    this.router.removeRoute(routeId);
  }
}
```

**Configuration:**
```typescript
interface RouterPluginConfig extends PluginConfig {
  mode: 'history' | 'hash';
  base?: string;
  routes?: RouteConfig[];
  guards?: RouteGuard[];
  onError?: (error: Error) => void;
}
```

**Usage:**
```typescript
const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/app',
  routes: [
    {
      path: '/users/*',
      app: 'user-app',
      meta: { requiresAuth: true }
    },
    {
      path: '/products/*',
      app: 'product-app'
    }
  ],
  guards: [
    {
      name: 'auth-guard',
      handler: async (to, from, next) => {
        if (to.meta?.requiresAuth && !isAuthenticated()) {
          next('/login');
        } else {
          next();
        }
      }
    }
  ]
});
```

### AuthPlugin

Authentication and authorization plugin.

```typescript
class AuthPlugin extends Plugin {
  name = 'auth-plugin';
  version = '1.0.0';
  
  constructor(config: AuthPluginConfig) {
    super(config);
  }
  
  async initialize(context: PluginContext): Promise<void> {
    this.authService = new AuthService(this.config);
    this.setupEventHandlers(context.eventBus);
  }
  
  // Auth-specific methods
  async login(credentials: LoginCredentials): Promise<AuthResult> {
    return this.authService.login(credentials);
  }
  
  async logout(): Promise<void> {
    return this.authService.logout();
  }
  
  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }
  
  getUser(): User | null {
    return this.authService.getCurrentUser();
  }
  
  hasPermission(permission: string): boolean {
    return this.authService.hasPermission(permission);
  }
}
```

**Configuration:**
```typescript
interface AuthPluginConfig extends PluginConfig {
  provider: 'oauth2' | 'jwt' | 'saml' | 'custom';
  clientId?: string;
  redirectUri?: string;
  scope?: string;
  tokenStorage?: 'localStorage' | 'sessionStorage' | 'memory';
  autoRefresh?: boolean;
  onAuthChange?: (user: User | null) => void;
}
```

### CommunicationPlugin

Enhanced inter-app communication plugin.

```typescript
class CommunicationPlugin extends Plugin {
  name = 'communication-plugin';
  version = '1.0.0';
  
  constructor(config: CommunicationPluginConfig) {
    super(config);
  }
  
  async initialize(context: PluginContext): Promise<void> {
    this.messageQueue = new MessageQueue(this.config.queue);
    this.stateSync = new StateSync(this.config.state);
    this.setupCommunicationChannels(context);
  }
  
  // Communication methods
  async sendMessage(target: string, message: any): Promise<void> {
    return this.messageQueue.send(target, message);
  }
  
  onMessage(handler: MessageHandler): () => void {
    return this.messageQueue.onMessage(handler);
  }
  
  syncState(key: string, value: any): Promise<void> {
    return this.stateSync.setState(key, value);
  }
  
  subscribeToState(key: string, handler: StateHandler): () => void {
    return this.stateSync.subscribe(key, handler);
  }
}
```

## Plugin Development Guide

### Creating a Custom Plugin

```typescript
import { Plugin, PluginContext, PluginConfig } from '@micro-core/core';

interface MyPluginConfig extends PluginConfig {
  apiEndpoint: string;
  refreshInterval: number;
  enableLogging: boolean;
}

class MyCustomPlugin extends Plugin {
  name = 'my-custom-plugin';
  version = '1.0.0';
  dependencies = ['router-plugin']; // Optional dependencies
  
  private apiEndpoint: string;
  private refreshInterval: number;
  private intervalId?: NodeJS.Timeout;
  private logger?: Logger;
  
  constructor(config: MyPluginConfig) {
    super(config);
    this.apiEndpoint = config.apiEndpoint;
    this.refreshInterval = config.refreshInterval || 30000;
    
    if (config.enableLogging) {
      this.logger = new Logger(`[${this.name}]`);
    }
  }
  
  async initialize(context: PluginContext): Promise<void> {
    this.log('Initializing plugin');
    
    // Store context references
    this.eventBus = context.eventBus;
    this.stateManager = context.stateManager;
    this.router = context.router;
    
    // Setup event handlers
    this.setupEventHandlers();
    
    // Initialize API client
    this.apiClient = new ApiClient(this.apiEndpoint);
    
    this.log('Plugin initialized successfully');
  }
  
  async activate(): Promise<void> {
    this.log('Activating plugin');
    
    // Start background tasks
    this.startDataRefresh();
    
    // Register routes
    this.registerRoutes();
    
    // Emit activation event
    this.eventBus.emit('plugin:my-custom:activated', {
      name: this.name,
      version: this.version
    });
    
    this.log('Plugin activated successfully');
  }
  
  async deactivate(): Promise<void> {
    this.log('Deactivating plugin');
    
    // Stop background tasks
    this.stopDataRefresh();
    
    // Unregister routes
    this.unregisterRoutes();
    
    this.log('Plugin deactivated successfully');
  }
  
  async destroy(): Promise<void> {
    this.log('Destroying plugin');
    
    // Cleanup resources
    this.cleanup();
    
    this.log('Plugin destroyed successfully');
  }
  
  private setupEventHandlers(): void {
    this.eventBus.on('app:mounted', this.handleAppMounted.bind(this));
    this.eventBus.on('user:login', this.handleUserLogin.bind(this));
  }
  
  private handleAppMounted(data: any): void {
    this.log('App mounted:', data.appName);
    // Handle app mount event
  }
  
  private handleUserLogin(data: any): void {
    this.log('User logged in:', data.userId);
    // Handle user login event
  }
  
  private startDataRefresh(): void {
    this.intervalId = setInterval(() => {
      this.refreshData();
    }, this.refreshInterval);
  }
  
  private stopDataRefresh(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }
  
  private async refreshData(): Promise<void> {
    try {
      const data = await this.apiClient.fetchData();
      this.stateManager.set('my-plugin:data', data);
      this.eventBus.emit('my-plugin:data-updated', data);
    } catch (error) {
      this.log('Error refreshing data:', error);
    }
  }
  
  private registerRoutes(): void {
    this.router.addRoute({
      path: '/my-plugin/*',
      handler: this.handleRoute.bind(this)
    });
  }
  
  private unregisterRoutes(): void {
    this.router.removeRoute('/my-plugin/*');
  }
  
  private handleRoute(route: string): void {
    this.log('Handling route:', route);
    // Handle plugin-specific routes
  }
  
  private cleanup(): void {
    // Remove event listeners
    this.eventBus.off('app:mounted', this.handleAppMounted);
    this.eventBus.off('user:login', this.handleUserLogin);
    
    // Stop background tasks
    this.stopDataRefresh();
    
    // Clear references
    this.apiClient = null;
    this.eventBus = null;
    this.stateManager = null;
    this.router = null;
  }
  
  private log(message: string, ...args: any[]): void {
    if (this.logger) {
      this.logger.info(message, ...args);
    }
  }
  
  // Public API methods
  public async getData(): Promise<any> {
    return this.stateManager.get('my-plugin:data');
  }
  
  public async refreshNow(): Promise<void> {
    await this.refreshData();
  }
}
```

### Plugin Registration

```typescript
// Register the custom plugin
const myPlugin = new MyCustomPlugin({
  apiEndpoint: 'https://api.example.com',
  refreshInterval: 60000,
  enableLogging: true
});

await pluginManager.register(myPlugin);
await pluginManager.activate('my-custom-plugin');
```

## Plugin Hooks System

### Hook Types

```typescript
enum PluginHookType {
  BEFORE_APP_LOAD = 'before-app-load',
  AFTER_APP_LOAD = 'after-app-load',
  BEFORE_APP_MOUNT = 'before-app-mount',
  AFTER_APP_MOUNT = 'after-app-mount',
  BEFORE_APP_UNMOUNT = 'before-app-unmount',
  AFTER_APP_UNMOUNT = 'after-app-unmount',
  BEFORE_ROUTE_CHANGE = 'before-route-change',
  AFTER_ROUTE_CHANGE = 'after-route-change'
}
```

### Registering Hooks

```typescript
class HookPlugin extends Plugin {
  name = 'hook-plugin';
  version = '1.0.0';
  
  async initialize(context: PluginContext): Promise<void> {
    // Register hooks
    context.hooks.register(PluginHookType.BEFORE_APP_LOAD, this.beforeAppLoad.bind(this));
    context.hooks.register(PluginHookType.AFTER_APP_MOUNT, this.afterAppMount.bind(this));
    context.hooks.register(PluginHookType.BEFORE_ROUTE_CHANGE, this.beforeRouteChange.bind(this));
  }
  
  private async beforeAppLoad(app: AppConfig): Promise<AppConfig> {
    console.log('Before app load:', app.name);
    
    // Modify app configuration
    return {
      ...app,
      props: {
        ...app.props,
        pluginData: 'injected-by-hook-plugin'
      }
    };
  }
  
  private async afterAppMount(app: AppConfig): Promise<void> {
    console.log('After app mount:', app.name);
    
    // Perform post-mount actions
    this.trackAppMount(app.name);
  }
  
  private async beforeRouteChange(to: Route, from: Route): Promise<boolean> {
    console.log('Before route change:', from.path, '->', to.path);
    
    // Return false to cancel navigation
    if (to.path === '/forbidden') {
      return false;
    }
    
    return true;
  }
}
```

## Plugin Communication

### Inter-Plugin Communication

```typescript
class PluginA extends Plugin {
  name = 'plugin-a';
  version = '1.0.0';
  
  async initialize(context: PluginContext): Promise<void> {
    // Listen for events from other plugins
    context.eventBus.on('plugin-b:data-ready', this.handlePluginBData.bind(this));
  }
  
  private handlePluginBData(data: any): void {
    console.log('Received data from Plugin B:', data);
    
    // Process data and emit response
    const processedData = this.processData(data);
    this.eventBus.emit('plugin-a:data-processed', processedData);
  }
  
  public shareData(data: any): void {
    this.eventBus.emit('plugin-a:data-shared', data);
  }
}

class PluginB extends Plugin {
  name = 'plugin-b';
  version = '1.0.0';
  
  async initialize(context: PluginContext): Promise<void> {
    // Listen for events from Plugin A
    context.eventBus.on('plugin-a:data-processed', this.handlePluginAData.bind(this));
  }
  
  private handlePluginAData(data: any): void {
    console.log('Received processed data from Plugin A:', data);
  }
  
  public sendDataToPluginA(data: any): void {
    this.eventBus.emit('plugin-b:data-ready', data);
  }
}
```

### Plugin Dependencies

```typescript
class DependentPlugin extends Plugin {
  name = 'dependent-plugin';
  version = '1.0.0';
  dependencies = ['router-plugin', 'auth-plugin'];
  
  async initialize(context: PluginContext): Promise<void> {
    // Access dependency plugins
    this.routerPlugin = context.getPlugin<RouterPlugin>('router-plugin');
    this.authPlugin = context.getPlugin<AuthPlugin>('auth-plugin');
    
    if (!this.routerPlugin || !this.authPlugin) {
      throw new Error('Required dependencies not available');
    }
    
    // Use dependency functionality
    this.setupAuthenticatedRoutes();
  }
  
  private setupAuthenticatedRoutes(): void {
    this.routerPlugin.addGuard({
      name: 'dependent-plugin-auth',
      handler: async (to, from, next) => {
        if (to.path.startsWith('/protected/') && !this.authPlugin.isAuthenticated()) {
          next('/login');
        } else {
          next();
        }
      }
    });
  }
}
```

## Plugin Configuration Schema

### Configuration Validation

```typescript
import Joi from 'joi';

class ValidatedPlugin extends Plugin {
  name = 'validated-plugin';
  version = '1.0.0';
  
  // Define configuration schema
  private static configSchema = Joi.object({
    apiUrl: Joi.string().uri().required(),
    timeout: Joi.number().min(1000).max(60000).default(5000),
    retries: Joi.number().min(0).max(5).default(3),
    features: Joi.object({
      caching: Joi.boolean().default(true),
      logging: Joi.boolean().default(false)
    }).default({})
  });
  
  constructor(config: any) {
    // Validate configuration
    const { error, value } = ValidatedPlugin.configSchema.validate(config);
    if (error) {
      throw new Error(`Invalid plugin configuration: ${error.message}`);
    }
    
    super(value);
  }
}
```

## Plugin Testing

### Unit Testing

```typescript
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { PluginTestUtils } from '@micro-core/testing';

describe('MyCustomPlugin', () => {
  let plugin: MyCustomPlugin;
  let testUtils: PluginTestUtils;
  let mockContext: PluginContext;
  
  beforeEach(() => {
    testUtils = new PluginTestUtils();
    mockContext = testUtils.createMockContext();
    
    plugin = new MyCustomPlugin({
      apiEndpoint: 'https://test-api.example.com',
      refreshInterval: 1000,
      enableLogging: false
    });
  });
  
  afterEach(async () => {
    await plugin.destroy();
    testUtils.cleanup();
  });
  
  it('should initialize correctly', async () => {
    await plugin.initialize(mockContext);
    
    expect(plugin.name).toBe('my-custom-plugin');
    expect(plugin.version).toBe('1.0.0');
  });
  
  it('should handle app mount events', async () => {
    await plugin.initialize(mockContext);
    await plugin.activate();
    
    const spy = jest.spyOn(plugin as any, 'handleAppMounted');
    
    mockContext.eventBus.emit('app:mounted', { appName: 'test-app' });
    
    expect(spy).toHaveBeenCalledWith({ appName: 'test-app' });
  });
  
  it('should refresh data periodically', async () => {
    const mockApiClient = {
      fetchData: jest.fn().mockResolvedValue({ data: 'test' })
    };
    
    (plugin as any).apiClient = mockApiClient;
    
    await plugin.initialize(mockContext);
    await plugin.activate();
    
    // Wait for refresh interval
    await testUtils.waitFor(1100);
    
    expect(mockApiClient.fetchData).toHaveBeenCalled();
    expect(mockContext.stateManager.set).toHaveBeenCalledWith(
      'my-plugin:data',
      { data: 'test' }
    );
  });
});
```

### Integration Testing

```typescript
describe('Plugin Integration', () => {
  let microCore: MicroCore;
  let pluginManager: PluginManager;
  
  beforeEach(async () => {
    microCore = new MicroCore({
      container: '#test-container'
    });
    
    pluginManager = microCore.pluginManager;
  });
  
  afterEach(async () => {
    await microCore.destroy();
  });
  
  it('should integrate multiple plugins', async () => {
    const routerPlugin = new RouterPlugin({ mode: 'hash' });
    const authPlugin = new AuthPlugin({ provider: 'jwt' });
    const customPlugin = new MyCustomPlugin({
      apiEndpoint: 'https://api.example.com',
      refreshInterval: 5000
    });
    
    await pluginManager.register(routerPlugin);
    await pluginManager.register(authPlugin);
    await pluginManager.register(customPlugin);
    
    await pluginManager.activate('router-plugin');
    await pluginManager.activate('auth-plugin');
    await pluginManager.activate('my-custom-plugin');
    
    const plugins = pluginManager.listPlugins();
    expect(plugins).toHaveLength(3);
    expect(plugins.every(p => p.status === 'active')).toBe(true);
  });
});
```

## Type Definitions

### Complete TypeScript Definitions

```typescript
// Plugin base types
export abstract class Plugin {
  abstract name: string;
  abstract version: string;
  dependencies?: string[];
  config?: PluginConfig;
  
  constructor(config?: PluginConfig);
  abstract initialize(context: PluginContext): Promise<void>;
  abstract activate(): Promise<void>;
  abstract deactivate(): Promise<void>;
  abstract destroy(): Promise<void>;
}

export interface PluginConfig {
  [key: string]: any;
}

export interface PluginContext {
  eventBus: EventBus;
  stateManager: StateManager;
  router: Router;
  hooks: HookManager;
  getPlugin<T extends Plugin>(name: string): T | undefined;
  microCore: MicroCore;
}

export interface PluginInfo {
  name: string;
  version: string;
  status: 'registered' | 'initialized' | 'active' | 'inactive' | 'error';
  dependencies: string[];
  config: PluginConfig;
}

// Plugin Manager types
export interface PluginManagerConfig {
  autoActivate?: boolean;
  dependencyResolution?: boolean;
  errorHandling?: 'strict' | 'lenient';
  onError?: (error: Error, plugin: Plugin) => void;
}

// Hook types
export interface HookManager {
  register(type: PluginHookType, handler: HookHandler): void;
  unregister(type: PluginHookType, handler: HookHandler): void;
  execute(type: PluginHookType, ...args: any[]): Promise<any>;
}

export type HookHandler = (...args: any[]) => any | Promise<any>;

// Built-in plugin types
export interface RouterPluginConfig extends PluginConfig {
  mode: 'history' | 'hash';
  base?: string;
  routes?: RouteConfig[];
  guards?: RouteGuard[];
  onError?: (error: Error) => void;
}

export interface AuthPluginConfig extends PluginConfig {
  provider: 'oauth2' | 'jwt' | 'saml' | 'custom';
  clientId?: string;
  redirectUri?: string;
  scope?: string;
  tokenStorage?: 'localStorage' | 'sessionStorage' | 'memory';
  autoRefresh?: boolean;
  onAuthChange?: (user: User | null) => void;
}

export interface CommunicationPluginConfig extends PluginConfig {
  queue?: MessageQueueConfig;
  state?: StateSyncConfig;
  crossFrame?: CrossFrameConfig;
}

// Route types
export interface RouteConfig {
  path: string;
  app?: string;
  handler?: RouteHandler;
  meta?: Record<string, any>;
  children?: RouteConfig[];
}

export interface RouteGuard {
  name: string;
  handler: GuardHandler;
  priority?: number;
}

export type RouteHandler = (route: Route) => void | Promise<void>;
export type GuardHandler = (to: Route, from: Route, next: NextFunction) => void | Promise<void>;
export type NextFunction = (path?: string | false) => void;

// Auth types
export interface LoginCredentials {
  username?: string;
  email?: string;
  password?: string;
  [key: string]: any;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  token?: string;
  error?: string;
}

export interface User {
  id: string;
  username: string;
  email?: string;
  roles: string[];
  permissions: string[];
  [key: string]: any;
}

// Testing types
export interface PluginTestUtils {
  createMockContext(): PluginContext;
  waitFor(ms: number): Promise<void>;
  cleanup(): void;
}
```

## Best Practices

### Plugin Development Guidelines

1. **Single Responsibility**: Each plugin should have a single, well-defined purpose
2. **Dependency Management**: Clearly declare dependencies and handle missing dependencies gracefully
3. **Error Handling**: Implement comprehensive error handling and recovery
4. **Resource Cleanup**: Always clean up resources in the destroy method
5. **Configuration Validation**: Validate plugin configuration on initialization
6. **Testing**: Write comprehensive unit and integration tests
7. **Documentation**: Provide clear documentation and examples

### Performance Considerations

1. **Lazy Loading**: Load plugin resources only when needed
2. **Memory Management**: Monitor and manage memory usage
3. **Event Handling**: Use efficient event handling patterns
4. **Background Tasks**: Implement proper cleanup for background tasks
5. **Caching**: Use caching where appropriate to improve performance

---

This completes the Plugin API documentation. For related APIs, see:

- **[Core API](/en/api/core)** - Main Micro-Core functionality
- **[Communication API](/en/api/communication)** - Inter-app communication
- **[Event System API](/en/api/event-system)** - Event handling
- **[Adapter API](/en/api/adapter-api)** - Framework adapter development
