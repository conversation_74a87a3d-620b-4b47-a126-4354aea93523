# Routing API

Micro-Core provides a flexible routing system that determines which applications should be active based on the current URL.

## Router Configuration

### RouterConfig

```typescript
interface RouterConfig {
  mode?: 'hash' | 'history' | 'memory';
  base?: string;
  linkActiveClass?: string;
  linkExactActiveClass?: string;
  parseQuery?: (query: string) => Record<string, any>;
  stringifyQuery?: (query: Record<string, any>) => string;
  fallback?: boolean;
}
```

### Basic Setup

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  router: {
    mode: 'history',
    base: '/app/',
    fallback: true
  }
});
```

## Route Matching

### String Matching

Exact path matching:

```typescript
await microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-container',
  activeRule: '/user' // Matches exactly '/user'
});
```

### Wildcard Matching

Pattern-based matching with wildcards:

```typescript
await microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-container',
  activeRule: '/user/*' // Matches '/user/profile', '/user/settings', etc.
});
```

### Regular Expression Matching

Complex pattern matching:

```typescript
await microCore.registerApp({
  name: 'product-app',
  entry: 'http://localhost:3002',
  container: '#product-container',
  activeRule: /^\/product\/\d+$/ // Matches '/product/123', '/product/456', etc.
});
```

### Function Matching

Custom logic for complex scenarios:

```typescript
await microCore.registerApp({
  name: 'admin-app',
  entry: 'http://localhost:3003',
  container: '#admin-container',
  activeRule: (location) => {
    // Custom logic
    return location.pathname.startsWith('/admin') && 
           hasAdminPermission();
  }
});
```

### Array Matching

Multiple conditions:

```typescript
await microCore.registerApp({
  name: 'dashboard-app',
  entry: 'http://localhost:3004',
  container: '#dashboard-container',
  activeRule: ['/dashboard', '/home', '/'] // Matches any of these paths
});
```

## Router Methods

### navigate()

Programmatically navigate to a route:

```typescript
// Navigate to a path
microCore.navigate('/user/profile');

// Navigate with query parameters
microCore.navigate('/user/profile', { 
  query: { tab: 'settings' } 
});

// Navigate with state
microCore.navigate('/user/profile', { 
  state: { from: 'dashboard' } 
});

// Replace current history entry
microCore.navigate('/user/profile', { 
  replace: true 
});
```

### getCurrentRoute()

Get current route information:

```typescript
const currentRoute = microCore.getCurrentRoute();
console.log('Current route:', currentRoute);

// Route object structure
interface Route {
  path: string;
  query: Record<string, any>;
  params: Record<string, any>;
  hash: string;
  state?: any;
}
```

### getActiveRoutes()

Get all currently active routes:

```typescript
const activeRoutes = microCore.getActiveRoutes();
activeRoutes.forEach(route => {
  console.log(`Active route: ${route.path} -> ${route.appName}`);
});
```

## Route Guards

### beforeEach()

Global before guard:

```typescript
microCore.beforeEach((to, from, next) => {
  console.log(`Navigating from ${from.path} to ${to.path}`);
  
  // Check authentication
  if (to.path.startsWith('/admin') && !isAuthenticated()) {
    next('/login');
    return;
  }
  
  // Check permissions
  if (to.path.startsWith('/user') && !hasUserPermission()) {
    next('/unauthorized');
    return;
  }
  
  // Continue navigation
  next();
});
```

### afterEach()

Global after guard:

```typescript
microCore.afterEach((to, from) => {
  console.log(`Navigated from ${from.path} to ${to.path}`);
  
  // Update page title
  document.title = getPageTitle(to.path);
  
  // Track page view
  analytics.track('page_view', {
    path: to.path,
    from: from.path
  });
});
```

### Application-Level Guards

```typescript
await microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-container',
  activeRule: '/user',
  beforeEnter: (to, from, next) => {
    // Application-specific guard
    if (!hasUserAccess()) {
      next('/access-denied');
      return;
    }
    next();
  },
  beforeLeave: (to, from, next) => {
    // Confirm before leaving
    if (hasUnsavedChanges()) {
      const confirmed = confirm('You have unsaved changes. Are you sure you want to leave?');
      if (!confirmed) {
        next(false);
        return;
      }
    }
    next();
  }
});
```

## Route Events

### Route Change Events

```typescript
// Listen to route changes
microCore.on('route:before-change', (to, from) => {
  console.log('Route about to change:', { to, from });
});

microCore.on('route:after-change', (to, from) => {
  console.log('Route changed:', { to, from });
});

microCore.on('route:error', (error, to, from) => {
  console.error('Route error:', error);
});
```

### Application Route Events

```typescript
// Application activated by route
microCore.on('app:route-activated', (app, route) => {
  console.log(`App ${app.name} activated by route ${route.path}`);
});

// Application deactivated by route
microCore.on('app:route-deactivated', (app, route) => {
  console.log(`App ${app.name} deactivated from route ${route.path}`);
});
```

## Dynamic Routing

### Route Parameters

```typescript
await microCore.registerApp({
  name: 'user-detail-app',
  entry: 'http://localhost:3001',
  container: '#user-detail-container',
  activeRule: '/user/:id',
  props: (route) => ({
    userId: route.params.id,
    tab: route.query.tab || 'profile'
  })
});

// When navigating to '/user/123?tab=settings'
// The app will receive props: { userId: '123', tab: 'settings' }
```

### Nested Routes

```typescript
await microCore.registerApp({
  name: 'admin-app',
  entry: 'http://localhost:3003',
  container: '#admin-container',
  activeRule: '/admin',
  children: [
    {
      name: 'admin-users',
      entry: 'http://localhost:3003/users',
      container: '#admin-users-container',
      activeRule: '/admin/users'
    },
    {
      name: 'admin-settings',
      entry: 'http://localhost:3003/settings',
      container: '#admin-settings-container',
      activeRule: '/admin/settings'
    }
  ]
});
```

## Route Transitions

### Transition Configuration

```typescript
const microCore = new MicroCore({
  router: {
    mode: 'history',
    transition: {
      name: 'fade',
      duration: 300,
      beforeEnter: (el) => {
        el.style.opacity = '0';
      },
      enter: (el, done) => {
        el.style.transition = 'opacity 0.3s';
        el.style.opacity = '1';
        setTimeout(done, 300);
      },
      leave: (el, done) => {
        el.style.transition = 'opacity 0.3s';
        el.style.opacity = '0';
        setTimeout(done, 300);
      }
    }
  }
});
```

### Custom Transitions

```typescript
// Define custom transition
const slideTransition = {
  name: 'slide',
  beforeEnter: (el, direction) => {
    el.style.transform = direction === 'forward' 
      ? 'translateX(100%)' 
      : 'translateX(-100%)';
  },
  enter: (el, done) => {
    el.style.transition = 'transform 0.3s ease-out';
    el.style.transform = 'translateX(0)';
    setTimeout(done, 300);
  },
  leave: (el, done, direction) => {
    el.style.transition = 'transform 0.3s ease-out';
    el.style.transform = direction === 'forward' 
      ? 'translateX(-100%)' 
      : 'translateX(100%)';
    setTimeout(done, 300);
  }
};

// Apply to specific application
await microCore.registerApp({
  name: 'animated-app',
  entry: 'http://localhost:3005',
  container: '#animated-container',
  activeRule: '/animated',
  transition: slideTransition
});
```

## Route Caching

### Cache Configuration

```typescript
const microCore = new MicroCore({
  router: {
    mode: 'history',
    cache: {
      enabled: true,
      maxAge: 300000, // 5 minutes
      maxSize: 50     // Maximum cached routes
    }
  }
});
```

### Manual Cache Control

```typescript
// Cache current route
microCore.cacheRoute('/user/profile');

// Clear specific route cache
microCore.clearRouteCache('/user/profile');

// Clear all route cache
microCore.clearAllRouteCache();

// Get cache statistics
const cacheStats = microCore.getRouteCacheStats();
console.log('Cache stats:', cacheStats);
```

## Route Utilities

### Path Matching

```typescript
import { pathToRegexp, match, compile } from '@micro-core/router-utils';

// Convert path to regexp
const regexp = pathToRegexp('/user/:id');
console.log(regexp.test('/user/123')); // true

// Match path with parameters
const matcher = match('/user/:id');
const result = matcher('/user/123');
console.log(result); // { params: { id: '123' }, path: '/user/123' }

// Compile path with parameters
const toPath = compile('/user/:id');
console.log(toPath({ id: '123' })); // '/user/123'
```

### Query String Handling

```typescript
// Parse query string
const query = microCore.parseQuery('?name=john&age=25');
console.log(query); // { name: 'john', age: '25' }

// Stringify query object
const queryString = microCore.stringifyQuery({ name: 'john', age: 25 });
console.log(queryString); // 'name=john&age=25'
```

## Best Practices

### 1. Route Organization

```typescript
// Group related routes
const userRoutes = [
  {
    name: 'user-profile',
    activeRule: '/user/profile',
    entry: 'http://localhost:3001/profile'
  },
  {
    name: 'user-settings',
    activeRule: '/user/settings',
    entry: 'http://localhost:3001/settings'
  }
];

// Register routes in groups
userRoutes.forEach(route => {
  microCore.registerApp({
    ...route,
    container: `#${route.name}-container`
  });
});
```

### 2. Route Guards

```typescript
// Centralized authentication check
function requireAuth(to, from, next) {
  if (!isAuthenticated()) {
    next('/login');
    return;
  }
  next();
}

// Apply to multiple routes
const protectedRoutes = ['/user', '/admin', '/dashboard'];
protectedRoutes.forEach(route => {
  microCore.beforeEach((to, from, next) => {
    if (to.path.startsWith(route)) {
      requireAuth(to, from, next);
    } else {
      next();
    }
  });
});
```

### 3. Error Handling

```typescript
// Handle route errors
microCore.on('route:error', (error, to, from) => {
  console.error('Route error:', error);
  
  // Redirect to error page
  microCore.navigate('/error', {
    state: { 
      originalPath: to.path,
      error: error.message 
    }
  });
});

// Handle 404 errors
microCore.beforeEach((to, from, next) => {
  const matchedApps = microCore.getMatchedApps(to.path);
  if (matchedApps.length === 0) {
    next('/404');
    return;
  }
  next();
});
```

### 4. Performance Optimization

```typescript
// Lazy load routes
const lazyRoutes = [
  {
    name: 'heavy-app',
    activeRule: '/heavy',
    loader: () => import('./heavy-app-config')
  }
];

// Preload critical routes
const criticalRoutes = ['/dashboard', '/user'];
criticalRoutes.forEach(route => {
  microCore.preloadRoute(route);
});
```

## Advanced Features

### Route Middleware

```typescript
// Create route middleware
const loggerMiddleware = (to, from, next) => {
  console.log(`Route: ${from.path} -> ${to.path}`);
  next();
};

const authMiddleware = (to, from, next) => {
  if (to.meta?.requiresAuth && !isAuthenticated()) {
    next('/login');
    return;
  }
  next();
};

// Apply middleware
microCore.use([loggerMiddleware, authMiddleware]);
```

### Route Meta Information

```typescript
await microCore.registerApp({
  name: 'admin-app',
  entry: 'http://localhost:3003',
  container: '#admin-container',
  activeRule: '/admin',
  meta: {
    requiresAuth: true,
    roles: ['admin'],
    title: 'Admin Panel',
    icon: 'admin-icon'
  }
});

// Access meta in guards
microCore.beforeEach((to, from, next) => {
  if (to.meta?.requiresAuth) {
    // Handle authentication
  }
  
  if (to.meta?.title) {
    document.title = to.meta.title;
  }
  
  next();
});
```

## Type Definitions

```typescript
interface RouterConfig {
  mode?: 'hash' | 'history' | 'memory';
  base?: string;
  linkActiveClass?: string;
  linkExactActiveClass?: string;
  parseQuery?: (query: string) => Record<string, any>;
  stringifyQuery?: (query: Record<string, any>) => string;
  fallback?: boolean;
  cache?: {
    enabled: boolean;
    maxAge: number;
    maxSize: number;
  };
  transition?: TransitionConfig;
}

interface Route {
  path: string;
  query: Record<string, any>;
  params: Record<string, any>;
  hash: string;
  state?: any;
  meta?: Record<string, any>;
}

interface NavigationGuard {
  (to: Route, from: Route, next: NavigationGuardNext): void;
}

interface NavigationGuardNext {
  (to?: string | false | Route): void;
}

interface TransitionConfig {
  name: string;
  duration?: number;
  beforeEnter?: (el: HTMLElement, direction?: string) => void;
  enter?: (el: HTMLElement, done: () => void, direction?: string) => void;
  leave?: (el: HTMLElement, done: () => void, direction?: string) => void;
}
```

## References

- [Core API](./core.md)
- [Application Management](./app-management.md)
- [Getting Started Guide](../guide/getting-started.md)
- [Routing Guide](../guide/routing.md)
