# Sandbox API (Part 3)

## Advanced Sandbox Features

### Sandbox Communication

Micro-Core provides various ways for sandboxes to communicate with each other and with the main application.

#### Event-Based Communication

```typescript
import { Sandbox, SandboxEventBus } from '@micro-core/core';

// Create event bus for sandbox communication
const eventBus = new SandboxEventBus();

// Create sandboxes
const sandbox1 = new Sandbox({
  name: 'app1',
  eventBus
});

const sandbox2 = new Sandbox({
  name: 'app2',
  eventBus
});

// Activate sandboxes
sandbox1.activate();
sandbox2.activate();

// Send message from sandbox1
sandbox1.execScript(`
  // Send message to other sandboxes
  window.__sandboxEventBus.emit('user-login', {
    userId: 123,
    username: 'john_doe',
    timestamp: Date.now()
  });
`);

// Listen for messages in sandbox2
sandbox2.execScript(`
  // Listen for user login events
  window.__sandboxEventBus.on('user-login', (data) => {
    console.log('User logged in:', data);
    
    // Update UI based on login event
    updateUserInterface(data);
  });
`);

// Listen for messages in main application
eventBus.on('user-login', (data) => {
  console.log('User login event received in main app:', data);
  
  // Update global state
  updateGlobalUserState(data);
});
```

#### Shared State Communication

```typescript
import { Sandbox, SharedState } from '@micro-core/core';

// Create shared state
const sharedState = new SharedState({
  user: null,
  theme: 'light',
  language: 'en'
});

// Create sandboxes with shared state
const sandbox1 = new Sandbox({
  name: 'app1',
  sharedState
});

const sandbox2 = new Sandbox({
  name: 'app2',
  sharedState
});

// Update shared state from sandbox1
sandbox1.execScript(`
  // Update user information
  window.__sharedState.set('user', {
    id: 123,
    name: 'John Doe',
    email: '<EMAIL>'
  });
  
  // Update theme
  window.__sharedState.set('theme', 'dark');
`);

// Listen for state changes in sandbox2
sandbox2.execScript(`
  // Listen for user changes
  window.__sharedState.watch('user', (newUser, oldUser) => {
    console.log('User changed:', newUser);
    updateUserProfile(newUser);
  });
  
  // Listen for theme changes
  window.__sharedState.watch('theme', (newTheme, oldTheme) => {
    console.log('Theme changed:', newTheme);
    applyTheme(newTheme);
  });
`);

// Access shared state in main application
const currentUser = sharedState.get('user');
console.log('Current user:', currentUser);

// Watch for changes in main application
sharedState.watch('user', (newUser) => {
  console.log('User updated in main app:', newUser);
  updateGlobalUserInterface(newUser);
});
```

#### Message Passing Communication

```typescript
import { Sandbox, MessageChannel } from '@micro-core/core';

// Create message channel
const messageChannel = new MessageChannel();

// Create sandboxes with message channel
const sandbox1 = new Sandbox({
  name: 'app1',
  messageChannel
});

const sandbox2 = new Sandbox({
  name: 'app2',
  messageChannel
});

// Send message from sandbox1 to sandbox2
sandbox1.execScript(`
  // Send direct message to specific sandbox
  window.__messageChannel.sendTo('app2', {
    type: 'DATA_REQUEST',
    payload: {
      dataType: 'user-list',
      filters: { active: true }
    }
  });
`);

// Handle messages in sandbox2
sandbox2.execScript(`
  // Listen for messages
  window.__messageChannel.onMessage((message, sender) => {
    console.log('Message received from:', sender);
    console.log('Message:', message);
    
    if (message.type === 'DATA_REQUEST') {
      // Process data request
      const data = fetchData(message.payload);
      
      // Send response back
      window.__messageChannel.sendTo(sender, {
        type: 'DATA_RESPONSE',
        payload: data
      });
    }
  });
`);

// Broadcast message to all sandboxes
sandbox1.execScript(`
  // Broadcast to all sandboxes
  window.__messageChannel.broadcast({
    type: 'GLOBAL_NOTIFICATION',
    payload: {
      message: 'System maintenance scheduled',
      timestamp: Date.now()
    }
  });
`);
```

### Sandbox Security

#### Content Security Policy (CSP)

```typescript
import { Sandbox } from '@micro-core/core';

// Create sandbox with CSP
const sandbox = new Sandbox({
  name: 'secure-app',
  security: {
    csp: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'"],
      'style-src': ["'self'", "'unsafe-inline'"],
      'img-src': ["'self'", "data:", "https:"],
      'connect-src': ["'self'", "https://api.example.com"],
      'font-src': ["'self'", "https://fonts.googleapis.com"],
      'object-src': ["'none'"],
      'media-src': ["'self'"],
      'frame-src': ["'none'"]
    }
  }
});

// Activate sandbox
sandbox.activate();

// Execute code with CSP restrictions
sandbox.execScript(`
  // This will be allowed
  fetch('https://api.example.com/data')
    .then(response => response.json())
    .then(data => console.log(data));
  
  // This will be blocked by CSP
  // fetch('https://malicious-site.com/steal-data');
`);
```

#### API Access Control

```typescript
import { Sandbox } from '@micro-core/core';

// Create sandbox with API access control
const sandbox = new Sandbox({
  name: 'restricted-app',
  security: {
    allowedAPIs: [
      'console.log',
      'console.warn',
      'console.error',
      'fetch',
      'localStorage.getItem',
      'localStorage.setItem',
      'document.getElementById',
      'document.querySelector'
    ],
    blockedAPIs: [
      'eval',
      'Function',
      'document.write',
      'document.writeln',
      'window.open',
      'location.href'
    ]
  }
});

// Activate sandbox
sandbox.activate();

// Execute code with API restrictions
sandbox.execScript(`
  // This will be allowed
  console.log('Hello from sandbox');
  const element = document.getElementById('app');
  
  // This will be blocked
  // eval('malicious code');
  // window.open('https://malicious-site.com');
`);
```

#### Resource Limits

```typescript
import { Sandbox } from '@micro-core/core';

// Create sandbox with resource limits
const sandbox = new Sandbox({
  name: 'limited-app',
  limits: {
    // Memory limit (in MB)
    memory: 100,
    
    // CPU time limit (in milliseconds)
    cpuTime: 5000,
    
    // Maximum number of DOM elements
    domElements: 1000,
    
    // Maximum number of event listeners
    eventListeners: 100,
    
    // Maximum number of timers
    timers: 50,
    
    // Network request limits
    network: {
      maxRequests: 100,
      maxRequestSize: 1024 * 1024, // 1MB
      allowedDomains: ['api.example.com', 'cdn.example.com']
    }
  }
});

// Monitor resource usage
sandbox.on('resource-limit-exceeded', (event) => {
  console.warn('Resource limit exceeded:', event);
  
  switch (event.type) {
    case 'memory':
      console.warn(`Memory usage exceeded: ${event.usage}MB > ${event.limit}MB`);
      break;
    case 'cpu-time':
      console.warn(`CPU time exceeded: ${event.usage}ms > ${event.limit}ms`);
      break;
    case 'dom-elements':
      console.warn(`DOM elements exceeded: ${event.usage} > ${event.limit}`);
      break;
  }
});
```

### Sandbox Performance Optimization

#### Code Splitting and Lazy Loading

```typescript
import { Sandbox } from '@micro-core/core';

// Create sandbox with lazy loading
const sandbox = new Sandbox({
  name: 'optimized-app',
  lazyLoading: {
    enabled: true,
    chunkSize: 1024 * 10, // 10KB chunks
    preloadChunks: 2 // Preload 2 chunks ahead
  }
});

// Load code in chunks
async function loadAppCode() {
  const chunks = [
    'chunk1.js', // Core functionality
    'chunk2.js', // UI components
    'chunk3.js', // Business logic
    'chunk4.js'  // Additional features
  ];
  
  // Load chunks progressively
  for (const chunk of chunks) {
    const code = await fetch(`/apps/optimized-app/${chunk}`).then(r => r.text());
    await sandbox.execScript(code);
    
    // Show loading progress
    updateLoadingProgress((chunks.indexOf(chunk) + 1) / chunks.length * 100);
  }
}

// Start loading
loadAppCode().then(() => {
  console.log('App loaded successfully');
});
```

#### Memory Management

```typescript
import { Sandbox } from '@micro-core/core';

// Create sandbox with memory management
const sandbox = new Sandbox({
  name: 'memory-managed-app',
  memoryManagement: {
    enabled: true,
    gcInterval: 30000, // Run garbage collection every 30 seconds
    memoryThreshold: 50 * 1024 * 1024, // 50MB threshold
    autoCleanup: true
  }
});

// Monitor memory usage
sandbox.on('memory-usage', (usage) => {
  console.log(`Memory usage: ${usage.used}MB / ${usage.limit}MB`);
  
  if (usage.used > usage.limit * 0.8) {
    console.warn('High memory usage detected, triggering cleanup');
    sandbox.cleanup();
  }
});

// Manual memory cleanup
function cleanupSandboxMemory() {
  // Clear unused variables
  sandbox.execScript(`
    // Clear large objects
    if (window.largeDataCache) {
      window.largeDataCache.clear();
    }
    
    // Remove unused event listeners
    if (window.unusedListeners) {
      window.unusedListeners.forEach(listener => {
        listener.remove();
      });
      window.unusedListeners = [];
    }
    
    // Force garbage collection (if available)
    if (window.gc) {
      window.gc();
    }
  `);
}

// Schedule periodic cleanup
setInterval(cleanupSandboxMemory, 60000); // Every minute
```

#### Caching and Optimization

```typescript
import { Sandbox, SandboxCache } from '@micro-core/core';

// Create cache for sandbox resources
const sandboxCache = new SandboxCache({
  maxSize: 100 * 1024 * 1024, // 100MB cache
  ttl: 3600000, // 1 hour TTL
  compression: true
});

// Create sandbox with caching
const sandbox = new Sandbox({
  name: 'cached-app',
  cache: sandboxCache,
  optimization: {
    // Enable code optimization
    optimizeCode: true,
    
    // Enable dead code elimination
    eliminateDeadCode: true,
    
    // Enable constant folding
    constantFolding: true,
    
    // Enable function inlining
    inlineFunctions: true
  }
});

// Cache frequently used code
async function loadAndCacheCode(url) {
  const cacheKey = `code:${url}`;
  
  // Try to get from cache first
  let code = await sandboxCache.get(cacheKey);
  
  if (!code) {
    // Load from network
    code = await fetch(url).then(r => r.text());
    
    // Cache for future use
    await sandboxCache.set(cacheKey, code);
  }
  
  return code;
}

// Load cached code
const appCode = await loadAndCacheCode('/apps/cached-app/main.js');
sandbox.execScript(appCode);
```

### Sandbox Testing and Debugging

#### Testing Sandbox Isolation

```typescript
import { Sandbox } from '@micro-core/core';

// Test suite for sandbox isolation
describe('Sandbox Isolation', () => {
  let sandbox1, sandbox2;
  
  beforeEach(() => {
    sandbox1 = new Sandbox({ name: 'test-app-1' });
    sandbox2 = new Sandbox({ name: 'test-app-2' });
    
    sandbox1.activate();
    sandbox2.activate();
  });
  
  afterEach(() => {
    sandbox1.deactivate();
    sandbox2.deactivate();
  });
  
  test('should isolate global variables', () => {
    // Set variable in sandbox1
    sandbox1.execScript('window.testVar = "sandbox1"');
    
    // Set variable in sandbox2
    sandbox2.execScript('window.testVar = "sandbox2"');
    
    // Check isolation
    const value1 = sandbox1.execScript('return window.testVar');
    const value2 = sandbox2.execScript('return window.testVar');
    
    expect(value1).toBe('sandbox1');
    expect(value2).toBe('sandbox2');
    
    // Check main window is not affected
    expect(window.testVar).toBeUndefined();
  });
  
  test('should isolate localStorage', () => {
    // Set localStorage in sandbox1
    sandbox1.execScript('localStorage.setItem("test", "sandbox1")');
    
    // Set localStorage in sandbox2
    sandbox2.execScript('localStorage.setItem("test", "sandbox2")');
    
    // Check isolation
    const value1 = sandbox1.execScript('return localStorage.getItem("test")');
    const value2 = sandbox2.execScript('return localStorage.getItem("test")');
    
    expect(value1).toBe('sandbox1');
    expect(value2).toBe('sandbox2');
  });
  
  test('should isolate event listeners', () => {
    let mainAppEventFired = false;
    let sandbox1EventFired = false;
    let sandbox2EventFired = false;
    
    // Add event listener in main app
    window.addEventListener('test-event', () => {
      mainAppEventFired = true;
    });
    
    // Add event listener in sandbox1
    sandbox1.execScript(`
      window.addEventListener('test-event', () => {
        window.sandbox1EventFired = true;
      });
    `);
    
    // Add event listener in sandbox2
    sandbox2.execScript(`
      window.addEventListener('test-event', () => {
        window.sandbox2EventFired = true;
      });
    `);
    
    // Trigger event in sandbox1
    sandbox1.execScript('window.dispatchEvent(new Event("test-event"))');
    
    // Check isolation
    expect(mainAppEventFired).toBe(false);
    expect(sandbox1.execScript('return window.sandbox1EventFired')).toBe(true);
    expect(sandbox2.execScript('return window.sandbox2EventFired')).toBeFalsy();
  });
});
```

#### Debugging Sandbox Issues

```typescript
import { Sandbox } from '@micro-core/core';

// Create sandbox with debugging enabled
const sandbox = new Sandbox({
  name: 'debug-app',
  debug: {
    enabled: true,
    logLevel: 'verbose',
    captureErrors: true,
    captureWarnings: true,
    captureConsole: true
  }
});

// Enable debug logging
sandbox.on('debug', (event) => {
  console.log(`[${event.level}] ${event.message}`, event.data);
});

// Capture and log errors
sandbox.on('error', (error) => {
  console.error('Sandbox error:', error);
  
  // Send error to monitoring service
  sendErrorToMonitoring({
    sandboxName: sandbox.name,
    error: error.message,
    stack: error.stack,
    timestamp: Date.now()
  });
});

// Capture console output
sandbox.on('console', (event) => {
  console.log(`[${sandbox.name}] ${event.method}:`, ...event.args);
});

// Debug sandbox state
function debugSandboxState() {
  const state = sandbox.getDebugInfo();
  
  console.log('Sandbox Debug Info:', {
    name: state.name,
    active: state.active,
    memoryUsage: state.memoryUsage,
    globalVariables: state.globalVariables,
    eventListeners: state.eventListeners,
    timers: state.timers,
    domElements: state.domElements
  });
}

// Performance profiling
sandbox.startProfiling();

// Execute code to profile
sandbox.execScript(`
  // Some code to profile
  for (let i = 0; i < 1000000; i++) {
    Math.random();
  }
`);

const profile = sandbox.stopProfiling();
console.log('Performance Profile:', profile);
```

### Sandbox Best Practices

#### Proper Lifecycle Management

```typescript
import { Sandbox } from '@micro-core/core';

class MicroAppManager {
  constructor() {
    this.apps = new Map();
  }
  
  async loadApp(name, config) {
    // Check if app is already loaded
    if (this.apps.has(name)) {
      return this.apps.get(name);
    }
    
    // Create sandbox
    const sandbox = new Sandbox({
      name,
      ...config.sandbox
    });
    
    // Create app instance
    const app = {
      name,
      sandbox,
      config,
      state: 'loading'
    };
    
    try {
      // Activate sandbox
      await sandbox.activate();
      
      // Load app code
      const code = await fetch(config.entry).then(r => r.text());
      await sandbox.execScript(code);
      
      // Initialize app
      await sandbox.execScript(`
        if (typeof window.bootstrap === 'function') {
          window.bootstrap(${JSON.stringify(config.props)});
        }
      `);
      
      app.state = 'loaded';
      this.apps.set(name, app);
      
      return app;
    } catch (error) {
      app.state = 'error';
      console.error(`Failed to load app ${name}:`, error);
      throw error;
    }
  }
  
  async mountApp(name, container) {
    const app = this.apps.get(name);
    if (!app) {
      throw new Error(`App ${name} not found`);
    }
    
    try {
      // Mount app
      await app.sandbox.execScript(`
        if (typeof window.mount === 'function') {
          window.mount('${container}');
        }
      `);
      
      app.state = 'mounted';
    } catch (error) {
      app.state = 'error';
      console.error(`Failed to mount app ${name}:`, error);
      throw error;
    }
  }
  
  async unmountApp(name) {
    const app = this.apps.get(name);
    if (!app) {
      return;
    }
    
    try {
      // Unmount app
      await app.sandbox.execScript(`
        if (typeof window.unmount === 'function') {
          window.unmount();
        }
      `);
      
      app.state = 'unmounted';
    } catch (error) {
      console.error(`Failed to unmount app ${name}:`, error);
    }
  }
  
  async unloadApp(name) {
    const app = this.apps.get(name);
    if (!app) {
      return;
    }
    
    try {
      // Unmount if mounted
      if (app.state === 'mounted') {
        await this.unmountApp(name);
      }
      
      // Cleanup app
      await app.sandbox.execScript(`
        if (typeof window.cleanup === 'function') {
          window.cleanup();
        }
      `);
      
      // Deactivate sandbox
      await app.sandbox.deactivate();
      
      // Remove from registry
      this.apps.delete(name);
    } catch (error) {
      console.error(`Failed to unload app ${name}:`, error);
    }
  }
  
  getApp(name) {
    return this.apps.get(name);
  }
  
  getAllApps() {
    return Array.from(this.apps.values());
  }
  
  async destroy() {
    // Unload all apps
    const appNames = Array.from(this.apps.keys());
    await Promise.all(appNames.map(name => this.unloadApp(name)));
  }
}

// Usage
const appManager = new MicroAppManager();

// Load and mount app
await appManager.loadApp('user-app', {
  entry: '/apps/user-app/main.js',
  sandbox: {
    strictIsolation: true
  },
  props: {
    apiUrl: 'https://api.example.com'
  }
});

await appManager.mountApp('user-app', '#user-container');

// Later, unmount and unload
await appManager.unmountApp('user-app');
await appManager.unloadApp('user-app');
```

#### Error Handling and Recovery

```typescript
import { Sandbox } from '@micro-core/core';

class ResilientSandbox extends Sandbox {
  constructor(options) {
    super(options);
    
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000;
    this.errorCount = 0;
    
    this.setupErrorHandling();
  }
  
    setupErrorHandling() {
    this.on('error', (error) => {
      this.handleError(error);
    });
    
    this.on('unhandled-rejection', (error) => {
      this.handleError(error);
    });
  }
  
  async handleError(error) {
    this.errorCount++;
    
    console.error(`Sandbox error (${this.errorCount}/${this.maxRetries}):`, error);
    
    if (this.errorCount < this.maxRetries) {
      // Try to recover
      console.log('Attempting to recover sandbox...');
      
      try {
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        
        // Reset sandbox state
        await this.reset();
        
        // Reactivate sandbox
        await this.activate();
        
        console.log('Sandbox recovery successful');
        this.errorCount = 0; // Reset error count on successful recovery
      } catch (recoveryError) {
        console.error('Sandbox recovery failed:', recoveryError);
      }
    } else {
      // Max retries reached, enter safe mode
      console.error('Max retries reached, entering safe mode');
      this.enterSafeMode();
    }
  }
  
  async reset() {
    // Clear all global variables
    this.clearGlobalValues();
    
    // Clear all event listeners
    this.clearEventListeners();
    
    // Clear all timers
    this.clearTimers();
    
    // Reset DOM state
    this.resetDOM();
  }
  
  enterSafeMode() {
    // Disable potentially problematic features
    this.options.strictIsolation = true;
    this.options.isolateWindowEvents = true;
    this.options.isolateTimers = true;
    
    // Emit safe mode event
    this.emit('safe-mode-entered');
  }
}

// Usage
const resilientSandbox = new ResilientSandbox({
  name: 'resilient-app',
  maxRetries: 3,
  retryDelay: 2000
});

resilientSandbox.on('safe-mode-entered', () => {
  console.log('Sandbox entered safe mode');
  // Notify user or take appropriate action
  showSafeModeNotification();
});
```

#### Resource Management

```typescript
import { Sandbox } from '@micro-core/core';

class ResourceManagedSandbox extends Sandbox {
  constructor(options) {
    super(options);
    
    this.resources = {
      timers: new Set(),
      eventListeners: new Set(),
      observers: new Set(),
      connections: new Set()
    };
    
    this.setupResourceTracking();
  }
  
  setupResourceTracking() {
    // Track timers
    this.interceptTimers();
    
    // Track event listeners
    this.interceptEventListeners();
    
    // Track observers
    this.interceptObservers();
    
    // Track network connections
    this.interceptNetworkConnections();
  }
  
  interceptTimers() {
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;
    const originalClearTimeout = window.clearTimeout;
    const originalClearInterval = window.clearInterval;
    
    this.execScript(`
      const timers = new Set();
      
      window.setTimeout = function(callback, delay, ...args) {
        const id = ${originalSetTimeout.name}.call(window, callback, delay, ...args);
        timers.add(id);
        return id;
      };
      
      window.setInterval = function(callback, delay, ...args) {
        const id = ${originalSetInterval.name}.call(window, callback, delay, ...args);
        timers.add(id);
        return id;
      };
      
      window.clearTimeout = function(id) {
        timers.delete(id);
        return ${originalClearTimeout.name}.call(window, id);
      };
      
      window.clearInterval = function(id) {
        timers.delete(id);
        return ${originalClearInterval.name}.call(window, id);
      };
      
      window.__getActiveTimers = function() {
        return Array.from(timers);
      };
      
      window.__clearAllTimers = function() {
        timers.forEach(id => {
          ${originalClearTimeout.name}.call(window, id);
          ${originalClearInterval.name}.call(window, id);
        });
        timers.clear();
      };
    `);
  }
  
  interceptEventListeners() {
    this.execScript(`
      const eventListeners = new Set();
      
      const originalAddEventListener = EventTarget.prototype.addEventListener;
      const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
      
      EventTarget.prototype.addEventListener = function(type, listener, options) {
        const listenerInfo = { target: this, type, listener, options };
        eventListeners.add(listenerInfo);
        return originalAddEventListener.call(this, type, listener, options);
      };
      
      EventTarget.prototype.removeEventListener = function(type, listener, options) {
        const listenerInfo = Array.from(eventListeners).find(info => 
          info.target === this && info.type === type && info.listener === listener
        );
        if (listenerInfo) {
          eventListeners.delete(listenerInfo);
        }
        return originalRemoveEventListener.call(this, type, listener, options);
      };
      
      window.__getActiveEventListeners = function() {
        return Array.from(eventListeners);
      };
      
      window.__clearAllEventListeners = function() {
        eventListeners.forEach(info => {
          originalRemoveEventListener.call(info.target, info.type, info.listener, info.options);
        });
        eventListeners.clear();
      };
    `);
  }
  
  getResourceUsage() {
    return {
      timers: this.execScript('return window.__getActiveTimers ? window.__getActiveTimers().length : 0'),
      eventListeners: this.execScript('return window.__getActiveEventListeners ? window.__getActiveEventListeners().length : 0'),
      memoryUsage: this.getMemoryUsage(),
      domElements: this.getDOMElementCount()
    };
  }
  
  cleanupResources() {
    // Clear all timers
    this.execScript(`
      if (window.__clearAllTimers) {
        window.__clearAllTimers();
      }
    `);
    
    // Clear all event listeners
    this.execScript(`
      if (window.__clearAllEventListeners) {
        window.__clearAllEventListeners();
      }
    `);
    
    // Clear other resources
    this.clearObservers();
    this.clearConnections();
  }
  
  async deactivate() {
    // Clean up resources before deactivation
    this.cleanupResources();
    
    // Call parent deactivate
    await super.deactivate();
  }
}

// Usage
const resourceManagedSandbox = new ResourceManagedSandbox({
  name: 'resource-managed-app'
});

// Monitor resource usage
setInterval(() => {
  const usage = resourceManagedSandbox.getResourceUsage();
  console.log('Resource usage:', usage);
  
  // Alert if resource usage is too high
  if (usage.timers > 50 || usage.eventListeners > 100) {
    console.warn('High resource usage detected');
    resourceManagedSandbox.cleanupResources();
  }
}, 10000); // Check every 10 seconds
```

## Summary

The Micro-Core Sandbox API provides a comprehensive solution for isolating micro-frontend applications, ensuring they can run independently without interfering with each other or the main application. The sandbox system includes:

### Core Features

1. **JavaScript Execution Isolation**: Complete isolation of global variables, functions, and execution context
2. **Style Isolation**: CSS scoping and style conflict prevention
3. **Storage Isolation**: Separate localStorage and sessionStorage for each application
4. **Event Isolation**: Isolated event handling and communication
5. **Resource Management**: Automatic tracking and cleanup of resources

### Advanced Capabilities

1. **Multi-Sandbox Management**: Support for managing multiple sandbox instances
2. **Sandbox Communication**: Event-based, shared state, and message passing communication
3. **Security Features**: CSP support, API access control, and resource limits
4. **Performance Optimization**: Code splitting, lazy loading, memory management, and caching
5. **Error Handling**: Resilient error recovery and safe mode operation
6. **Testing and Debugging**: Comprehensive testing utilities and debugging tools

### Best Practices

1. **Proper Lifecycle Management**: Systematic loading, mounting, unmounting, and cleanup
2. **Resource Management**: Automatic tracking and cleanup of timers, event listeners, and other resources
3. **Error Handling**: Robust error recovery mechanisms with retry logic
4. **Performance Monitoring**: Regular monitoring of resource usage and performance metrics
5. **Security Considerations**: Proper isolation and access control for sensitive operations

### Integration Support

The sandbox system seamlessly integrates with popular micro-frontend frameworks:

- **qiankun**: Full compatibility with qiankun's sandbox requirements
- **wujie**: Native support for wujie's isolation mechanisms
- **single-spa**: Compatible with single-spa's application lifecycle

By leveraging these features and following the best practices outlined in this documentation, developers can build robust, secure, and high-performance micro-frontend applications that scale effectively while maintaining proper isolation and resource management.

The Micro-Core Sandbox API represents a mature solution for the complex challenges of micro-frontend architecture, providing the tools and patterns necessary to build production-ready applications with confidence.
