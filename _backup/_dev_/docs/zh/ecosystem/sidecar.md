# 边车模式 (Sidecar Mode)

Micro-Core 的边车模式是一种创新的微前端集成方案，允许传统应用以最小的代码变更接入微前端架构。通过边车容器的代理和增强，传统应用可以获得现代微前端的所有能力，实现渐进式的架构升级。

## 核心概念

### 边车架构

边车模式借鉴了微服务架构中的 Sidecar Pattern，为每个传统应用提供一个独立的边车容器：

```
┌─────────────────┐    ┌─────────────────┐
│   传统应用       │    │   边车容器       │
│                │    │                │
│  ┌───────────┐  │    │  ┌───────────┐  │
│  │ 业务逻辑   │  │◄──►│  │ 微前端能力 │  │
│  └───────────┘  │    │  └───────────┘  │
│                │    │                │
│  ┌───────────┐  │    │  ┌───────────┐  │
│  │   UI 层   │  │◄──►│  │ 路由代理   │  │
│  └───────────┘  │    │  └───────────┘  │
└─────────────────┘    └─────────────────┘
```

### 零侵入集成

传统应用只需要添加一行代码即可接入微前端架构：

```html
<!-- 传统 HTML 应用 -->
<!DOCTYPE html>
<html>
<head>
    <title>传统应用</title>
</head>
<body>
    <div id="app">
        <!-- 现有应用内容 -->
        <h1>我的传统应用</h1>
        <div id="content">
            <!-- 业务内容 -->
        </div>
    </div>
    
    <!-- 只需要添加这一行 -->
    <script src="https://cdn.micro-core.dev/sidecar/latest.js"></script>
</body>
</html>
```

## 边车容器实现

### SidecarContainer 核心类

```typescript
import { SidecarContainer } from '@micro-core/sidecar';

class SidecarContainer {
  private config: SidecarConfig;
  private kernel: MicroCoreKernel;
  private legacyApp: LegacyAppAdapter;
  
  constructor(config: SidecarConfig) {
    this.config = config;
    this.kernel = new MicroCoreKernel();
    this.legacyApp = this.createLegacyAdapter();
  }
  
  async bootstrap(): Promise<void> {
    // 1. 初始化边车环境
    await this.initializeSidecarEnvironment();
    
    // 2. 分析传统应用结构
    const appStructure = await this.analyzeLegacyApp();
    
    // 3. 创建微前端包装器
    const wrapper = await this.createMicroFrontendWrapper(appStructure);
    
    // 4. 启动边车服务
    await this.startSidecarServices();
    
    // 5. 注入微前端能力
    await this.injectMicroFrontendCapabilities();
  }
  
  private async initializeSidecarEnvironment(): Promise<void> {
    // 创建隔离的执行环境
    this.createIsolatedEnvironment();
    
    // 初始化通信桥梁
    this.initializeCommunicationBridge();
    
    // 设置资源代理
    this.setupResourceProxy();
  }
  
  private async analyzeLegacyApp(): Promise<AppStructure> {
    // 分析 DOM 结构
    const domStructure = this.analyzeDOMStructure();
    
    // 分析脚本依赖
    const scriptDependencies = this.analyzeScriptDependencies();
    
    // 分析样式资源
    const styleResources = this.analyzeStyleResources();
    
    return {
      dom: domStructure,
      scripts: scriptDependencies,
      styles: styleResources
    };
  }
}
```

### 自动发现和适配

```typescript
class LegacyAppDiscovery {
  static async discover(): Promise<LegacyAppInfo> {
    const info: LegacyAppInfo = {
      framework: 'unknown',
      version: 'unknown',
      structure: {},
      dependencies: []
    };
    
    // 检测 jQuery
    if (window.jQuery) {
      info.framework = 'jquery';
      info.version = window.jQuery.fn.jquery;
    }
    
    // 检测 React (传统版本)
    if (window.React) {
      info.framework = 'react';
      info.version = window.React.version;
    }
    
    // 检测 Vue (传统版本)
    if (window.Vue) {
      info.framework = 'vue';
      info.version = window.Vue.version;
    }
    
    // 检测 Angular (传统版本)
    if (window.angular) {
      info.framework = 'angularjs';
      info.version = window.angular.version.full;
    }
    
    // 分析 DOM 结构
    info.structure = this.analyzeDOMStructure();
    
    // 分析依赖关系
    info.dependencies = this.analyzeDependencies();
    
    return info;
  }
  
  private static analyzeDOMStructure(): DOMStructure {
    const structure: DOMStructure = {
      root: document.documentElement,
      containers: [],
      components: []
    };
    
    // 查找可能的应用容器
    const containers = document.querySelectorAll('[id*="app"], [class*="app"], [data-app]');
    structure.containers = Array.from(containers);
    
    // 查找组件化结构
    const components = document.querySelectorAll('[data-component], [class*="component"]');
    structure.components = Array.from(components);
    
    return structure;
  }
}
```

## 传统应用适配器

### jQuery 应用适配器

```typescript
class JQueryAppAdapter extends LegacyAppAdapter {
  name = 'jquery-adapter';
  
  async adapt(): Promise<void> {
    // 1. 保护 jQuery 全局变量
    this.protectGlobalVariables(['$', 'jQuery']);
    
    // 2. 创建事件桥梁
    this.createEventBridge();
    
    // 3. 路由集成
    this.integrateRouting();
    
    // 4. 状态管理集成
    this.integrateStateManagement();
  }
  
  private createEventBridge(): void {
    const originalOn = $.fn.on;
    const originalOff = $.fn.off;
    
    // 拦截 jQuery 事件
    $.fn.on = function(events: string, selector: any, data: any, handler: any) {
      // 注册到微前端事件系统
      this.sidecar.registerEvent(events, handler);
      
      // 调用原始方法
      return originalOn.call(this, events, selector, data, handler);
    };
    
    $.fn.off = function(events: string, selector: any, handler: any) {
      // 从微前端事件系统注销
      this.sidecar.unregisterEvent(events, handler);
      
      // 调用原始方法
      return originalOff.call(this, events, selector, handler);
    };
  }
  
  private integrateRouting(): void {
    // 监听 hashchange 事件
    $(window).on('hashchange', (event) => {
      const hash = window.location.hash;
      
      // 通知微前端路由系统
      this.sidecar.kernel.getRouter().navigate(hash);
    });
    
    // 监听微前端路由变化
    this.sidecar.kernel.getRouter().onRouteChange((route) => {
      // 更新 jQuery 应用路由
      if (route.hash !== window.location.hash) {
        window.location.hash = route.hash;
      }
    });
  }
}
```

### Vanilla JS 应用适配器

```typescript
class VanillaJSAppAdapter extends LegacyAppAdapter {
  name = 'vanilla-adapter';
  
  async adapt(): Promise<void> {
    // 1. DOM 操作代理
    this.proxyDOMOperations();
    
    // 2. 事件系统集成
    this.integrateEventSystem();
    
    // 3. 模块化改造
    this.enableModularization();
  }
  
  private proxyDOMOperations(): void {
    const originalQuerySelector = document.querySelector;
    const originalQuerySelectorAll = document.querySelectorAll;
    
    // 代理 DOM 查询
    document.querySelector = function(selector: string) {
      const element = originalQuerySelector.call(this, selector);
      
      // 记录 DOM 操作
      this.sidecar.recordDOMOperation('querySelector', selector, element);
      
      return element;
    };
    
    document.querySelectorAll = function(selector: string) {
      const elements = originalQuerySelectorAll.call(this, selector);
      
      // 记录 DOM 操作
      this.sidecar.recordDOMOperation('querySelectorAll', selector, elements);
      
      return elements;
    };
  }
  
  private integrateEventSystem(): void {
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
    
    // 代理事件监听
    EventTarget.prototype.addEventListener = function(type: string, listener: any, options?: any) {
      // 注册到微前端事件系统
      this.sidecar.registerEventListener(this, type, listener, options);
      
      // 调用原始方法
      return originalAddEventListener.call(this, type, listener, options);
    };
    
    EventTarget.prototype.removeEventListener = function(type: string, listener: any, options?: any) {
      // 从微前端事件系统注销
      this.sidecar.unregisterEventListener(this, type, listener, options);
      
      // 调用原始方法
      return originalRemoveEventListener.call(this, type, listener, options);
    };
  }
}
```

## 边车服务

### 路由代理服务

```typescript
class SidecarRoutingService {
  private routes: Map<string, RouteHandler> = new Map();
  private currentRoute: string = '/';
  
  constructor(private sidecar: SidecarContainer) {
    this.initializeRouting();
  }
  
  private initializeRouting(): void {
    // 监听浏览器路由变化
    window.addEventListener('popstate', this.handleRouteChange.bind(this));
    
    // 监听 hash 变化
    window.addEventListener('hashchange', this.handleHashChange.bind(this));
    
    // 代理 history API
    this.proxyHistoryAPI();
  }
  
  private proxyHistoryAPI(): void {
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = (state: any, title: string, url?: string | URL | null) => {
      // 通知微前端路由系统
      this.sidecar.kernel.getRouter().pushState(state, title, url);
      
      // 调用原始方法
      return originalPushState.call(history, state, title, url);
    };
    
    history.replaceState = (state: any, title: string, url?: string | URL | null) => {
      // 通知微前端路由系统
      this.sidecar.kernel.getRouter().replaceState(state, title, url);
      
      // 调用原始方法
      return originalReplaceState.call(history, state, title, url);
    };
  }
  
  registerRoute(path: string, handler: RouteHandler): void {
    this.routes.set(path, handler);
  }
  
  navigate(path: string): void {
    if (this.routes.has(path)) {
      const handler = this.routes.get(path)!;
      handler(path);
    }
    
    this.currentRoute = path;
  }
}
```

### 状态同步服务

```typescript
class SidecarStateService {
  private localState: Map<string, any> = new Map();
  private globalState: GlobalState;
  
  constructor(private sidecar: SidecarContainer) {
    this.globalState = sidecar.kernel.getGlobalState();
    this.initializeStateSync();
  }
  
  private initializeStateSync(): void {
    // 监听全局状态变化
    this.globalState.subscribe((key: string, value: any) => {
      this.syncToLegacyApp(key, value);
    });
    
    // 监听传统应用状态变化
    this.watchLegacyAppState();
  }
  
  private syncToLegacyApp(key: string, value: any): void {
    // 同步到 jQuery 应用
    if (window.jQuery) {
      $(document).trigger(`state:${key}`, [value]);
    }
    
    // 同步到 Vanilla JS 应用
    window.dispatchEvent(new CustomEvent(`state:${key}`, {
      detail: { key, value }
    }));
    
    // 更新本地状态
    this.localState.set(key, value);
  }
  
  private watchLegacyAppState(): void {
    // 监听自定义状态事件
    window.addEventListener('legacy-state-change', (event: CustomEvent) => {
      const { key, value } = event.detail;
      
      // 同步到全局状态
      this.globalState.setState(key, value);
    });
  }
  
  setState(key: string, value: any): void {
    this.localState.set(key, value);
    this.globalState.setState(key, value);
  }
  
  getState(key: string): any {
    return this.localState.get(key) || this.globalState.getState(key);
  }
}
```

### 通信桥梁服务

```typescript
class SidecarCommunicationService {
  private eventBus: EventBus;
  private messageChannels: Map<string, MessageChannel> = new Map();
  
  constructor(private sidecar: SidecarContainer) {
    this.eventBus = sidecar.kernel.getEventBus();
    this.initializeCommunication();
  }
  
  private initializeCommunication(): void {
    // 创建全局通信接口
    this.createGlobalAPI();
    
    // 监听微前端事件
    this.listenToMicroFrontendEvents();
    
    // 监听传统应用事件
    this.listenToLegacyAppEvents();
  }
  
  private createGlobalAPI(): void {
    // 为传统应用提供通信 API
    (window as any).__MICRO_CORE_SIDECAR__ = {
      // 发送消息到其他应用
      sendMessage: (target: string, message: any) => {
        this.eventBus.emit(`app:${target}:message`, message);
      },
      
      // 监听来自其他应用的消息
      onMessage: (callback: (message: any) => void) => {
        this.eventBus.on(`app:${this.sidecar.config.name}:message`, callback);
      },
      
      // 广播消息到所有应用
      broadcast: (message: any) => {
        this.eventBus.emit('app:broadcast', message);
      },
      
      // 获取全局状态
      getGlobalState: (key: string) => {
        return this.sidecar.kernel.getGlobalState().getState(key);
      },
      
      // 设置全局状态
      setGlobalState: (key: string, value: any) => {
        this.sidecar.kernel.getGlobalState().setState(key, value);
      }
    };
  }
  
  private listenToMicroFrontendEvents(): void {
    // 监听应用间通信
    this.eventBus.on('app:communication', (event) => {
      // 转发给传统应用
      window.dispatchEvent(new CustomEvent('micro-frontend-message', {
        detail: event
      }));
    });
  }
  
  private listenToLegacyAppEvents(): void {
    // 监听传统应用的自定义事件
    window.addEventListener('legacy-app-message', (event: CustomEvent) => {
      // 转发到微前端事件系统
      this.eventBus.emit('app:legacy:message', event.detail);
    });
  }
}
```

## 边车配置

### 基础配置

```typescript
interface SidecarConfig {
  // 应用信息
  name: string;
  version: string;
  
  // 自动发现配置
  discovery: {
    enabled: boolean;
    frameworks: string[]; // ['jquery', 'vanilla', 'react', 'vue']
    timeout: number;
  };
  
  // 适配器配置
  adapters: {
    jquery?: JQueryAdapterConfig;
    vanilla?: VanillaAdapterConfig;
    react?: ReactAdapterConfig;
    vue?: VueAdapterConfig;
  };
  
  // 服务配置
  services: {
    routing: RoutingServiceConfig;
    state: StateServiceConfig;
    communication: CommunicationServiceConfig;
  };
  
  // 沙箱配置
  sandbox: {
    enabled: boolean;
    type: 'proxy' | 'iframe' | 'webcomponent';
    isolation: {
      js: boolean;
      css: boolean;
      dom: boolean;
    };
  };
}
```

### 使用示例

```typescript
// 自动配置
const sidecar = new SidecarContainer({
  name: 'legacy-app',
  discovery: {
    enabled: true,
    frameworks: ['jquery', 'vanilla'],
    timeout: 5000
  }
});

// 手动配置
const sidecar = new SidecarContainer({
  name: 'jquery-app',
  discovery: { enabled: false },
  
  adapters: {
    jquery: {
      version: '3.6.0',
      noConflict: true,
      eventBridge: true,
      routingIntegration: true
    }
  },
  
  services: {
    routing: {
      mode: 'hash',
      base: '/legacy-app'
    },
    
    state: {
      sync: ['user', 'theme'],
      persistence: true
    },
    
    communication: {
      channels: ['broadcast', 'direct'],
      middleware: []
    }
  },
  
  sandbox: {
    enabled: true,
    type: 'proxy',
    isolation: {
      js: true,
      css: true,
      dom: false
    }
  }
});

// 启动边车
await sidecar.bootstrap();
```

## 渐进式迁移

### 迁移策略

```typescript
class ProgressiveMigrationStrategy {
  private phases: MigrationPhase[] = [];
  
  constructor(private sidecar: SidecarContainer) {
    this.planMigration();
  }
  
  private planMigration(): void {
    // 阶段 1: 边车接入
    this.phases.push({
      name: 'sidecar-integration',
      description: '接入边车容器',
      tasks: [
        'install-sidecar-script',
        'configure-basic-settings',
        'verify-compatibility'
      ]
    });
    
    // 阶段 2: 功能增强
    this.phases.push({
      name: 'feature-enhancement',
      description: '增强现有功能',
      tasks: [
        'enable-routing-integration',
        'enable-state-management',
        'enable-communication'
      ]
    });
    
    // 阶段 3: 组件化改造
    this.phases.push({
      name: 'componentization',
      description: '组件化改造',
      tasks: [
        'identify-components',
        'extract-components',
        'create-component-registry'
      ]
    });
    
    // 阶段 4: 现代化重构
    this.phases.push({
      name: 'modernization',
      description: '现代化重构',
      tasks: [
        'migrate-to-modern-framework',
        'update-build-system',
        'optimize-performance'
      ]
    });
  }
  
  async executeMigration(): Promise<void> {
    for (const phase of this.phases) {
      console.log(`开始执行迁移阶段: ${phase.name}`);
      
      for (const task of phase.tasks) {
        await this.executeTask(task);
      }
      
      console.log(`完成迁移阶段: ${phase.name}`);
    }
  }
  
  private async executeTask(task: string): Promise<void> {
    switch (task) {
      case 'install-sidecar-script':
        await this.installSidecarScript();
        break;
      
      case 'configure-basic-settings':
        await this.configureBasicSettings();
        break;
      
      case 'verify-compatibility':
        await this.verifyCompatibility();
        break;
      
      // ... 其他任务
    }
  }
}
```

## 性能优化

### 懒加载和代码分割

```typescript
class SidecarPerformanceOptimizer {
  constructor(private sidecar: SidecarContainer) {}
  
  async optimizeLoading(): Promise<void> {
    // 1. 懒加载适配器
    await this.lazyLoadAdapters();
    
    // 2. 代码分割
    await this.enableCodeSplitting();
    
    // 3. 资源预加载
    await this.enableResourcePrefetch();
  }
  
  private async lazyLoadAdapters(): Promise<void> {
    const { framework } = await LegacyAppDiscovery.discover();
    
    // 只加载需要的适配器
    switch (framework) {
      case 'jquery':
        const { JQueryAppAdapter } = await import('./adapters/jquery-adapter');
        this.sidecar.setAdapter(new JQueryAppAdapter());
        break;
      
      case 'vanilla':
        const { VanillaJSAppAdapter } = await import('./adapters/vanilla-adapter');
        this.sidecar.setAdapter(new VanillaJSAppAdapter());
        break;
    }
  }
  
  private async enableCodeSplitting(): Promise<void> {
    // 按功能分割代码
    const modules = {
      routing: () => import('./services/routing-service'),
      state: () => import('./services/state-service'),
      communication: () => import('./services/communication-service')
    };
    
    // 按需加载模块
    for (const [name, loader] of Object.entries(modules)) {
      if (this.sidecar.config.services[name]?.enabled) {
        await loader();
      }
    }
  }
}
```

## 调试和监控

### 边车调试工具

```typescript
class SidecarDebugger {
  private logs: DebugLog[] = [];
  private metrics: PerformanceMetrics = {};
  
  constructor(private sidecar: SidecarContainer) {
    this.initializeDebugging();
  }
  
  private initializeDebugging(): void {
    // 创建调试面板
    this.createDebugPanel();
    
    // 监控性能指标
    this.monitorPerformance();
    
    // 记录操作日志
    this.recordOperations();
  }
  
  private createDebugPanel(): void {
    if (process.env.NODE_ENV === 'development') {
      const panel = document.createElement('div');
      panel.id = 'sidecar-debug-panel';
      panel.innerHTML = `
        <div class="sidecar-debug">
          <h3>Sidecar 调试面板</h3>
          <div class="tabs">
            <button onclick="showTab('logs')">日志</button>
            <button onclick="showTab('metrics')">性能</button>
            <button onclick="showTab('state')">状态</button>
          </div>
          <div id="debug-content"></div>
        </div>
      `;
      
      document.body.appendChild(panel);
    }
  }
  
  log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const log: DebugLog = {
      timestamp: Date.now(),
      level,
      message,
      data
    };
    
    this.logs.push(log);
    console[level](`[Sidecar] ${message}`, data);
    
    // 更新调试面板
    this.updateDebugPanel();
  }
  
  private monitorPerformance(): void {
    // 监控加载时间
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name.includes('sidecar')) {
          this.metrics[entry.name] = entry.duration;
        }
      }
    });
    
    observer.observe({ entryTypes: ['measure', 'navigation'] });
  }
}
```

## 最佳实践

### 1. 边车配置建议

- **自动发现优先**: 启用自动发现功能，减少手动配置
- **渐进式接入**: 分阶段接入边车功能，避免一次性改动过大
- **性能监控**: 启用性能监控，及时发现和解决性能问题
- **错误处理**: 完善错误处理机制，确保传统应用稳定运行

### 2. 迁移策略建议

- **风险评估**: 在迁移前进行充分的风险评估和测试
- **回滚机制**: 准备完整的回滚方案，确保可以快速恢复
- **用户体验**: 确保迁移过程中用户体验不受影响
- **团队培训**: 对开发团队进行边车模式的培训

### 3. 维护和监控

- **日志记录**: 完整记录边车操作日志，便于问题排查
- **性能监控**: 持续监控应用性能，及时优化
- **版本管理**: 谨慎管理边车版本升级，确保兼容性
- **安全考虑**: 注意边车模式可能带来的安全风险

通过 Micro-Core 的边车模式，传统应用可以以最小的成本获得现代微前端的所有能力，实现平滑的架构升级和技术栈迁移。
