# 适配器系统

Micro-Core 适配器系统提供了对主流前端框架的全面支持，让不同技术栈的应用能够无缝集成到微前端架构中。通过统一的适配器接口，开发者可以轻松地将 React、Vue、Angular 等应用接入 Micro-Core 生态。

## 设计理念

### 统一接口

所有适配器都实现了统一的 `MicroAppAdapter` 接口，确保不同框架应用的一致性体验：

```typescript
interface MicroAppAdapter {
  name: string;
  version: string;
  
  // 生命周期方法
  bootstrap(props: BootstrapProps): Promise<void>;
  mount(props: MountProps): Promise<void>;
  unmount(props: UnmountProps): Promise<void>;
  update?(props: UpdateProps): Promise<void>;
  
  // 框架特定方法
  getAppInstance(): any;
  getRouter?(): any;
  getStore?(): any;
}
```

### 框架无关

适配器层抽象了框架特定的实现细节，让主应用和子应用能够跨框架通信和协作：

```typescript
// 主应用（React）
import { MicroCore } from '@micro-core/core';
import { ReactAdapterPlugin } from '@micro-core/plugin-adapter-react';

const microCore = new MicroCore({
  adapters: [new ReactAdapterPlugin()]
});

// 注册 Vue 子应用
microCore.registerApp({
  name: 'vue-app',
  entry: 'http://localhost:8080',
  adapter: 'vue3'
});
```

## 官方适配器

### React 适配器 (@micro-core/adapter-react)

支持 React 16.8+、17.x、18.x 版本，提供完整的 React 生态集成。

#### 基础使用

```typescript
import { createReactAdapter } from '@micro-core/adapter-react';
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const adapter = createReactAdapter({
  // React 版本配置
  version: '18.x',
  
  // 渲染配置
  render: {
    mode: 'concurrent', // 'legacy' | 'concurrent'
    strictMode: true,
    suspense: true
  },
  
  // 路由集成
  router: {
    type: 'react-router', // 'react-router' | 'reach-router'
    basename: '/react-app'
  },
  
  // 状态管理集成
  store: {
    type: 'redux', // 'redux' | 'zustand' | 'recoil'
    provider: true
  }
});

// 导出生命周期函数
export const { bootstrap, mount, unmount, update } = adapter({
  rootComponent: App,
  container: '#root'
});
```

#### 高级特性

**1. Concurrent Features 支持**

```typescript
import { Suspense } from 'react';
import { createReactAdapter } from '@micro-core/adapter-react';

const adapter = createReactAdapter({
  render: {
    mode: 'concurrent',
    features: {
      suspense: true,
      transitions: true,
      offscreen: true
    }
  }
});

// 应用组件
function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
}
```

**2. React Router 集成**

```typescript
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { createReactAdapter } from '@micro-core/adapter-react';

const adapter = createReactAdapter({
  router: {
    type: 'react-router',
    basename: '/react-app',
    memory: false, // 是否使用 MemoryRouter
    
    // 路由守卫
    guards: {
      beforeEach: (to, from, next) => {
        // 全局前置守卫
        next();
      }
    }
  }
});

function App() {
  return (
    <BrowserRouter basename="/react-app">
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<About />} />
      </Routes>
    </BrowserRouter>
  );
}
```

**3. Redux 状态管理集成**

```typescript
import { configureStore } from '@reduxjs/toolkit';
import { createReactAdapter } from '@micro-core/adapter-react';

const store = configureStore({
  reducer: {
    counter: counterReducer,
    user: userReducer
  }
});

const adapter = createReactAdapter({
  store: {
    type: 'redux',
    instance: store,
    provider: true,
    
    // 全局状态同步
    sync: {
      enabled: true,
      keys: ['user'] // 同步到全局状态的 keys
    }
  }
});
```

### Vue 适配器 (@micro-core/adapter-vue)

支持 Vue 2.7+ 和 Vue 3.x，提供完整的 Vue 生态集成。

#### Vue 3 适配器

```typescript
import { createVue3Adapter } from '@micro-core/adapter-vue';
import { createApp } from 'vue';
import App from './App.vue';

const adapter = createVue3Adapter({
  // Vue 版本配置
  version: '3.x',
  
  // 应用配置
  app: {
    globalProperties: {
      $microCore: true
    },
    config: {
      errorHandler: (err, vm, info) => {
        console.error('Vue error:', err, info);
      }
    }
  },
  
  // 路由集成
  router: {
    type: 'vue-router', // 'vue-router@4'
    mode: 'history',
    base: '/vue-app'
  },
  
  // 状态管理集成
  store: {
    type: 'pinia', // 'vuex' | 'pinia'
    instance: null // 自动创建或传入实例
  }
});

// 导出生命周期函数
export const { bootstrap, mount, unmount, update } = adapter({
  rootComponent: App,
  container: '#app'
});
```

#### Vue 2 适配器

```typescript
import { createVue2Adapter } from '@micro-core/adapter-vue';
import Vue from 'vue';
import App from './App.vue';

const adapter = createVue2Adapter({
  version: '2.7+',
  
  // Vue 实例配置
  vue: {
    config: {
      productionTip: false,
      devtools: true
    }
  },
  
  // 路由集成 (Vue Router 3.x)
  router: {
    type: 'vue-router',
    mode: 'history',
    base: '/vue2-app'
  },
  
  // 状态管理集成 (Vuex 3.x)
  store: {
    type: 'vuex',
    modules: {
      user: userModule,
      app: appModule
    }
  }
});

export const { bootstrap, mount, unmount } = adapter({
  rootComponent: App,
  container: '#app'
});
```

#### 高级特性

**1. Composition API 支持**

```typescript
// Vue 3
import { ref, computed } from 'vue';
import { createVue3Adapter } from '@micro-core/adapter-vue';

const adapter = createVue3Adapter({
  composition: {
    enabled: true,
    
    // 全局组合式函数
    provides: {
      useMicroCore: () => ({
        appName: ref('vue-app'),
        isActive: computed(() => true)
      })
    }
  }
});

// Vue 2.7
import { ref, computed } from '@vue/composition-api';
import { createVue2Adapter } from '@micro-core/adapter-vue';

const adapter = createVue2Adapter({
  composition: {
    enabled: true,
    plugin: '@vue/composition-api' // 自动安装
  }
});
```

**2. Pinia 状态管理**

```typescript
import { createPinia, defineStore } from 'pinia';
import { createVue3Adapter } from '@micro-core/adapter-vue';

const pinia = createPinia();

const useUserStore = defineStore('user', {
  state: () => ({
    name: '',
    email: ''
  }),
  
  actions: {
    updateUser(user) {
      this.name = user.name;
      this.email = user.email;
    }
  }
});

const adapter = createVue3Adapter({
  store: {
    type: 'pinia',
    instance: pinia,
    
    // 全局状态同步
    sync: {
      enabled: true,
      stores: ['user'] // 同步的 store
    }
  }
});
```

### Angular 适配器 (@micro-core/adapter-angular)

支持 Angular 12+ 版本，提供完整的 Angular 生态集成。

#### 基础使用

```typescript
import { createAngularAdapter } from '@micro-core/adapter-angular';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';

const adapter = createAngularAdapter({
  // Angular 版本配置
  version: '15.x',
  
  // 模块配置
  module: AppModule,
  platform: platformBrowserDynamic(),
  
  // 路由配置
  router: {
    enableTracing: false,
    initialNavigation: 'enabledBlocking',
    
    // 路由守卫
    guards: {
      canActivate: [AuthGuard],
      canDeactivate: [CanDeactivateGuard]
    }
  },
  
  // 依赖注入配置
  providers: [
    { provide: 'MICRO_CORE_CONFIG', useValue: { appName: 'angular-app' } }
  ]
});

export const { bootstrap, mount, unmount } = adapter({
  selector: 'app-root',
  container: '#angular-app'
});
```

#### 高级特性

**1. 依赖注入集成**

```typescript
import { Injectable, Inject } from '@angular/core';
import { createAngularAdapter } from '@micro-core/adapter-angular';

@Injectable({
  providedIn: 'root'
})
export class MicroCoreService {
  constructor(
    @Inject('MICRO_CORE_KERNEL') private kernel: any
  ) {}
  
  navigateToApp(appName: string, path: string) {
    return this.kernel.navigateToApp(appName, path);
  }
}

const adapter = createAngularAdapter({
  providers: [
    {
      provide: 'MICRO_CORE_KERNEL',
      useFactory: () => window.__MICRO_CORE_KERNEL__
    },
    MicroCoreService
  ]
});
```

**2. RxJS 集成**

```typescript
import { Observable } from 'rxjs';
import { createAngularAdapter } from '@micro-core/adapter-angular';

const adapter = createAngularAdapter({
  rxjs: {
    // 全局状态流
    globalState$: new Observable(subscriber => {
      window.__MICRO_CORE_KERNEL__.onStateChange(state => {
        subscriber.next(state);
      });
    }),
    
    // 路由变化流
    routeChange$: new Observable(subscriber => {
      window.__MICRO_CORE_KERNEL__.onRouteChange(route => {
        subscriber.next(route);
      });
    })
  }
});
```

### HTML 适配器 (@micro-core/adapter-html)

支持传统 HTML/JavaScript 应用，提供零配置接入能力。

```typescript
import { createHtmlAdapter } from '@micro-core/adapter-html';

const adapter = createHtmlAdapter({
  // 脚本加载配置
  scripts: {
    // 是否沙箱化脚本执行
    sandbox: true,
    
    // 脚本执行顺序
    order: 'document', // 'document' | 'network'
    
    // 异步脚本处理
    async: {
      enabled: true,
      timeout: 5000
    }
  },
  
  // 样式处理配置
  styles: {
    // 样式隔离
    isolation: true,
    
    // CSS 作用域
    scoped: true,
    
    // 样式前缀
    prefix: 'html-app'
  },
  
  // DOM 处理配置
  dom: {
    // 是否解析 DOM
    parse: true,
    
    // 根元素选择器
    root: 'body',
    
    // DOM 事件处理
    events: {
      capture: true,
      passive: true
    }
  }
});

export const { bootstrap, mount, unmount } = adapter({
  html: `
    <div id="html-app">
      <h1>HTML Micro App</h1>
      <script>
        console.log('HTML app loaded');
      </script>
    </div>
  `,
  container: '#html-container'
});
```

### Svelte 适配器 (@micro-core/adapter-svelte)

支持 Svelte 3.x+ 和 SvelteKit，提供响应式应用集成。

```typescript
import { createSvelteAdapter } from '@micro-core/adapter-svelte';
import App from './App.svelte';

const adapter = createSvelteAdapter({
  // Svelte 配置
  svelte: {
    dev: process.env.NODE_ENV === 'development',
    hydratable: true,
    immutable: false
  },
  
  // SvelteKit 集成
  kit: {
    enabled: true,
    adapter: '@sveltejs/adapter-static',
    
    // 路由配置
    router: {
      base: '/svelte-app',
      trailing_slash: 'never'
    }
  },
  
  // 状态管理
  stores: {
    // 自定义 stores
    global: writable({}),
    user: writable(null)
  }
});

export const { bootstrap, mount, unmount } = adapter({
  component: App,
  container: '#svelte-app',
  props: {
    name: 'Svelte Micro App'
  }
});
```

## 适配器开发指南

### 创建自定义适配器

#### 1. 基础适配器结构

```typescript
import { MicroAppAdapter, AdapterOptions } from '@micro-core/core';

export interface CustomAdapterOptions extends AdapterOptions {
  framework: string;
  version: string;
  customConfig?: any;
}

export class CustomAdapter implements MicroAppAdapter {
  name = 'custom-adapter';
  version = '1.0.0';
  
  private options: CustomAdapterOptions;
  private appInstance: any = null;
  
  constructor(options: CustomAdapterOptions) {
    this.options = options;
  }
  
  async bootstrap(props: BootstrapProps): Promise<void> {
    // 适配器初始化逻辑
    console.log(`Bootstrapping ${this.options.framework} app`);
    
    // 加载框架依赖
    await this.loadFrameworkDependencies();
    
    // 初始化框架特定配置
    this.initializeFrameworkConfig();
  }
  
  async mount(props: MountProps): Promise<void> {
    // 应用挂载逻辑
    const { container, rootComponent, ...otherProps } = props;
    
    // 创建应用实例
    this.appInstance = this.createAppInstance(rootComponent, otherProps);
    
    // 挂载到容器
    await this.mountToContainer(this.appInstance, container);
    
    // 触发挂载完成事件
    this.emitMountedEvent();
  }
  
  async unmount(props: UnmountProps): Promise<void> {
    // 应用卸载逻辑
    if (this.appInstance) {
      await this.unmountAppInstance(this.appInstance);
      this.appInstance = null;
    }
    
    // 清理资源
    this.cleanup();
  }
  
  async update(props: UpdateProps): Promise<void> {
    // 应用更新逻辑（可选）
    if (this.appInstance) {
      await this.updateAppInstance(this.appInstance, props);
    }
  }
  
  getAppInstance(): any {
    return this.appInstance;
  }
  
  // 框架特定方法
  private async loadFrameworkDependencies(): Promise<void> {
    // 动态加载框架依赖
  }
  
  private initializeFrameworkConfig(): void {
    // 初始化框架配置
  }
  
  private createAppInstance(component: any, props: any): any {
    // 创建框架应用实例
    return null;
  }
  
  private async mountToContainer(instance: any, container: string | Element): Promise<void> {
    // 挂载应用到容器
  }
  
  private async unmountAppInstance(instance: any): Promise<void> {
    // 卸载应用实例
  }
  
  private async updateAppInstance(instance: any, props: any): Promise<void> {
    // 更新应用实例
  }
  
  private cleanup(): void {
    // 清理资源
  }
  
  private emitMountedEvent(): void {
    // 触发挂载完成事件
    window.dispatchEvent(new CustomEvent('micro-app-mounted', {
      detail: { adapter: this.name, app: this.appInstance }
    }));
  }
}
```

#### 2. 适配器工厂函数

```typescript
export function createCustomAdapter(options: CustomAdapterOptions) {
  const adapter = new CustomAdapter(options);
  
  return function(appConfig: AppConfig) {
    return {
      async bootstrap(props: BootstrapProps) {
        await adapter.bootstrap({ ...props, ...appConfig });
      },
      
      async mount(props: MountProps) {
        await adapter.mount({ ...props, ...appConfig });
      },
      
      async unmount(props: UnmountProps) {
        await adapter.unmount({ ...props, ...appConfig });
      },
      
      async update(props: UpdateProps) {
        await adapter.update({ ...props, ...appConfig });
      },
      
      getAppInstance() {
        return adapter.getAppInstance();
      }
    };
  };
}
```

### 适配器测试

#### 1. 单元测试

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CustomAdapter } from './custom-adapter';

describe('CustomAdapter', () => {
  let adapter: CustomAdapter;
  
  beforeEach(() => {
    adapter = new CustomAdapter({
      framework: 'custom',
      version: '1.0.0'
    });
  });
  
  it('should bootstrap correctly', async () => {
    const props = {
      name: 'test-app',
      entry: 'http://localhost:3000'
    };
    
    await adapter.bootstrap(props);
    
    expect(adapter.name).toBe('custom-adapter');
  });
  
  it('should mount and unmount correctly', async () => {
    const mountProps = {
      container: document.createElement('div'),
      rootComponent: () => 'Test Component'
    };
    
    await adapter.mount(mountProps);
    expect(adapter.getAppInstance()).toBeDefined();
    
    await adapter.unmount({});
    expect(adapter.getAppInstance()).toBeNull();
  });
});
```

#### 2. 集成测试

```typescript
import { describe, it, expect } from 'vitest';
import { MicroCore } from '@micro-core/core';
import { CustomAdapter } from './custom-adapter';

describe('CustomAdapter Integration', () => {
  it('should integrate with MicroCore', async () => {
    const microCore = new MicroCore({
      adapters: [
        new CustomAdapter({
          framework: 'custom',
          version: '1.0.0'
        })
      ]
    });
    
    await microCore.registerApp({
      name: 'custom-app',
      entry: 'http://localhost:3000',
      adapter: 'custom-adapter'
    });
    
    await microCore.mountApp('custom-app');
    
    expect(microCore.getApp('custom-app').status).toBe('mounted');
  });
});
```

## 适配器配置和优化

### 性能优化

#### 1. 懒加载

```typescript
const adapter = createReactAdapter({
  lazy: {
    enabled: true,
    
    // 组件懒加载
    components: {
      threshold: 0.1, // 视口阈值
      rootMargin: '50px'
    },
    
    // 路由懒加载
    routes: {
      preload: true,
      prefetch: true
    }
  }
});
```

#### 2. 代码分割

```typescript
const adapter = createVue3Adapter({
  codeSplitting: {
    enabled: true,
    
    // 按路由分割
    routes: true,
    
    // 按组件分割
    components: {
      threshold: 10, // KB
      async: true
    },
    
    // 公共依赖提取
    vendor: {
      chunks: ['vue', 'vue-router', 'pinia']
    }
  }
});
```

#### 3. 缓存策略

```typescript
const adapter = createAngularAdapter({
  cache: {
    // 模块缓存
    modules: {
      enabled: true,
      maxAge: 300000, // 5分钟
      maxSize: 50
    },
    
    // 组件缓存
    components: {
      enabled: true,
      strategy: 'lru', // 'lru' | 'fifo'
      maxSize: 100
    }
  }
});
```

### 错误处理

```typescript
const adapter = createReactAdapter({
  errorHandling: {
    // 全局错误边界
    errorBoundary: {
      enabled: true,
      fallback: ErrorFallbackComponent,
      onError: (error, errorInfo) => {
        console.error('React app error:', error, errorInfo);
        
        // 发送错误报告
        window.__MICRO_CORE_KERNEL__.reportError({
          type: 'react-error',
          error,
          errorInfo,
          app: 'react-app'
        });
      }
    },
    
    // 异步错误处理
    async: {
      enabled: true,
      timeout: 5000,
      retry: 3
    }
  }
});
```

## 最佳实践

### 1. 适配器选择指南

- **React 应用**: 使用 `@micro-core/adapter-react`
- **Vue 3 应用**: 使用 `@micro-core/adapter-vue` (Vue 3 模式)
- **Vue 2 应用**: 使用 `@micro-core/adapter-vue` (Vue 2 模式)
- **Angular 应用**: 使用 `@micro-core/adapter-angular`
- **传统应用**: 使用 `@micro-core/adapter-html`
- **Svelte 应用**: 使用 `@micro-core/adapter-svelte`

### 2. 版本兼容性

```typescript
// 检查框架版本兼容性
const adapter = createReactAdapter({
  version: 'auto', // 自动检测
  
  compatibility: {
    // 最小支持版本
    min: '16.8.0',
    
    // 最大支持版本
    max: '18.x',
    
    // 不兼容版本
    exclude: ['17.0.0', '17.0.1']
  }
});
```

### 3. 开发调试

```typescript
const adapter = createVue3Adapter({
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    
    // 性能监控
    performance: true,
    
    // 生命周期日志
    lifecycle: true,
    
    // 状态变化日志
    state: true
  }
});
```

通过 Micro-Core 的适配器系统，开发者可以轻松地将不同技术栈的应用集成到统一的微前端架构中，实现真正的技术栈无关和渐进式迁移。
