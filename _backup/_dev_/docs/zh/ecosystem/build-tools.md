# 构建工具集成

Micro-Core 支持多种主流构建工具，提供了完整的插件和配置方案来简化微前端应用的构建过程。

## 支持的构建工具

### Webpack 集成

#### 安装

```bash
npm install @micro-core/webpack-plugin --save-dev
```

#### 基础配置

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')

module.exports = {
  entry: './src/index.js',
  
  plugins: [
    new MicroCoreWebpackPlugin({
      // 微应用配置
      name: 'my-micro-app',
      
      // 导出配置
      exposes: {
        './App': './src/App',
        './utils': './src/utils'
      },
      
      // 共享依赖
      shared: {
        'react': { singleton: true },
        'react-dom': { singleton: true },
        '@micro-core/core': { singleton: true }
      },
      
      // 构建优化
      optimization: {
        splitChunks: true,
        runtimeChunk: 'single'
      }
    })
  ]
}
```

#### 高级配置

```javascript
// webpack.config.js - 高级配置
const path = require('path')
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')

module.exports = {
  mode: process.env.NODE_ENV || 'development',
  
  entry: {
    main: './src/index.js',
    vendor: ['react', 'react-dom']
  },
  
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        use: 'ts-loader',
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              modules: {
                localIdentName: '[name]__[local]--[hash:base64:5]'
              }
            }
          }
        ]
      }
    ]
  },
  
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'advanced-micro-app',
      
      // 多入口配置
      entries: {
        main: './src/index.js',
        admin: './src/admin/index.js'
      },
      
      // 条件导出
      exposes: {
        './App': {
          import: './src/App',
          name: 'App'
        },
        './AdminApp': {
          import: './src/admin/AdminApp',
          name: 'AdminApp',
          condition: 'admin'
        }
      },
      
      // 动态共享
      shared: {
        'react': {
          singleton: true,
          requiredVersion: '^18.0.0',
          eager: false
        },
        'lodash': {
          singleton: false,
          requiredVersion: '^4.17.0'
        }
      },
      
      // 自定义运行时
      runtime: {
        name: 'micro-core-runtime',
        chunks: ['runtime']
      }
    })
  ],
  
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        },
        microCore: {
          test: /[\\/]node_modules[\\/]@micro-core[\\/]/,
          name: 'micro-core',
          chunks: 'all',
          priority: 10
        }
      }
    }
  },
  
  devServer: {
    port: 3000,
    hot: true,
    historyApiFallback: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
    }
  }
}
```

### Vite 集成

#### 安装

```bash
npm install @micro-core/vite-plugin --save-dev
```

#### 基础配置

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/vite-plugin'

export default defineConfig({
  plugins: [
    microCore({
      // 应用名称
      name: 'my-vite-app',
      
      // 导出模块
      expose: {
        './App': './src/App.vue',
        './components': './src/components/index.js'
      },
      
      // 共享依赖
      shared: ['vue', '@micro-core/core'],
      
      // 构建配置
      build: {
        target: 'es2015',
        formats: ['es', 'umd']
      }
    })
  ],
  
  build: {
    rollupOptions: {
      external: ['vue', 'react'],
      output: {
        globals: {
          vue: 'Vue',
          react: 'React'
        }
      }
    }
  }
})
```

#### Vue 项目配置

```javascript
// vite.config.js - Vue 项目
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { microCore } from '@micro-core/vite-plugin'

export default defineConfig({
  plugins: [
    vue(),
    microCore({
      name: 'vue-micro-app',
      
      // Vue 特定配置
      framework: 'vue',
      
      // 组件导出
      expose: {
        './App': './src/App.vue',
        './router': './src/router/index.js',
        './store': './src/store/index.js'
      },
      
      // Vue 相关共享
      shared: {
        'vue': { singleton: true },
        'vue-router': { singleton: true },
        'vuex': { singleton: true }
      }
    })
  ],
  
  // 开发服务器配置
  server: {
    port: 3001,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
})
```

### Rollup 集成

#### 安装

```bash
npm install @micro-core/rollup-plugin --save-dev
```

#### 配置

```javascript
// rollup.config.js
import { microCore } from '@micro-core/rollup-plugin'
import resolve from '@rollup/plugin-node-resolve'
import commonjs from '@rollup/plugin-commonjs'
import typescript from '@rollup/plugin-typescript'

export default {
  input: 'src/index.ts',
  
  output: [
    {
      dir: 'dist/es',
      format: 'es',
      sourcemap: true
    },
    {
      dir: 'dist/umd',
      format: 'umd',
      name: 'MicroApp',
      sourcemap: true
    }
  ],
  
  plugins: [
    resolve(),
    commonjs(),
    typescript(),
    
    microCore({
      name: 'rollup-micro-app',
      
      // 多格式输出
      formats: ['es', 'umd', 'cjs'],
      
      // 外部依赖
      external: ['react', 'react-dom'],
      
      // 全局变量映射
      globals: {
        'react': 'React',
        'react-dom': 'ReactDOM'
      }
    })
  ],
  
  external: ['react', 'react-dom', '@micro-core/core']
}
```

### esbuild 集成

#### 安装

```bash
npm install @micro-core/esbuild-plugin --save-dev
```

#### 配置

```javascript
// build.js
const esbuild = require('esbuild')
const { microCorePlugin } = require('@micro-core/esbuild-plugin')

esbuild.build({
  entryPoints: ['src/index.ts'],
  bundle: true,
  outdir: 'dist',
  format: 'esm',
  target: 'es2020',
  sourcemap: true,
  
  plugins: [
    microCorePlugin({
      name: 'esbuild-micro-app',
      
      // 快速构建配置
      minify: true,
      splitting: true,
      
      // 代码分割
      chunkNames: '[name]-[hash]',
      
      // 外部依赖
      external: ['react', 'react-dom']
    })
  ],
  
  // 开发服务器
  serve: process.env.NODE_ENV === 'development' ? {
    servedir: 'dist',
    port: 3002
  } : undefined
})
```

### Rspack 集成

#### 安装

```bash
npm install @micro-core/rspack-plugin --save-dev
```

#### 配置

```javascript
// rspack.config.js
const { MicroCoreRspackPlugin } = require('@micro-core/rspack-plugin')

module.exports = {
  entry: './src/index.js',
  
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'builtin:swc-loader',
        options: {
          jsc: {
            parser: {
              syntax: 'typescript',
              tsx: true
            }
          }
        }
      }
    ]
  },
  
  plugins: [
    new MicroCoreRspackPlugin({
      name: 'rspack-micro-app',
      
      // Rust 优化
      optimization: {
        rustOptimization: true,
        parallelBuild: true
      },
      
      // 模块联邦兼容
      moduleFederation: {
        name: 'rspack_app',
        exposes: {
          './App': './src/App'
        }
      }
    })
  ]
}
```

### Parcel 集成

#### 安装

```bash
npm install @micro-core/parcel-plugin --save-dev
```

#### 配置

```json
// package.json
{
  "name": "parcel-micro-app",
  "scripts": {
    "dev": "parcel src/index.html --port 3003",
    "build": "parcel build src/index.html --dist-dir dist"
  },
  "@micro-core/parcel-plugin": {
    "name": "parcel-micro-app",
    "expose": {
      "./App": "./src/App.js"
    },
    "shared": ["react", "react-dom"]
  }
}
```

```javascript
// .parcelrc
{
  "extends": "@parcel/config-default",
  "plugins": {
    "@micro-core/parcel-plugin": {}
  }
}
```

### Turbopack 集成

#### 安装

```bash
npm install @micro-core/turbopack-plugin --save-dev
```

#### 配置

```javascript
// turbo.config.js
const { microCore } = require('@micro-core/turbopack-plugin')

module.exports = {
  plugins: [
    microCore({
      name: 'turbo-micro-app',
      
      // Turbopack 特性
      incremental: true,
      hotReload: true,
      
      // 优化配置
      optimization: {
        treeShaking: true,
        minification: true
      }
    })
  ]
}
```

## 构建工具对比

### 性能对比

| 构建工具 | 冷启动时间 | 热更新时间 | 构建时间 | 包大小 |
|---------|-----------|-----------|---------|--------|
| Webpack | 3-5s | 200-500ms | 30-60s | 中等 |
| Vite | 1-2s | 50-100ms | 15-30s | 小 |
| Rollup | 2-3s | N/A | 20-40s | 最小 |
| esbuild | 0.5-1s | 10-50ms | 5-15s | 小 |
| Rspack | 1-2s | 100-200ms | 10-25s | 中等 |
| Parcel | 2-4s | 100-300ms | 20-45s | 中等 |
| Turbopack | 0.3-0.8s | 5-20ms | 3-10s | 小 |

### 功能对比

| 功能 | Webpack | Vite | Rollup | esbuild | Rspack | Parcel | Turbopack |
|------|---------|------|--------|---------|--------|--------|-----------|
| 模块联邦 | ✅ | ⚠️ | ❌ | ❌ | ✅ | ❌ | ✅ |
| 代码分割 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 热更新 | ✅ | ✅ | ❌ | ⚠️ | ✅ | ✅ | ✅ |
| TypeScript | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| CSS Modules | ✅ | ✅ | ⚠️ | ⚠️ | ✅ | ✅ | ✅ |
| 插件生态 | 丰富 | 丰富 | 中等 | 有限 | 中等 | 中等 | 新兴 |

## 最佳实践

### 1. 选择合适的构建工具

```javascript
// 根据项目需求选择构建工具
const buildToolSelection = {
  // 大型企业项目
  enterprise: 'webpack', // 成熟稳定，插件丰富
  
  // 现代 Vue/React 项目
  modern: 'vite', // 快速开发，良好体验
  
  // 库开发
  library: 'rollup', // 体积小，输出干净
  
  // 极致性能要求
  performance: 'esbuild', // 构建速度最快
  
  // Rust 生态
  rust: 'rspack', // Webpack 兼容 + Rust 性能
  
  // 零配置需求
  zeroConfig: 'parcel', // 开箱即用
  
  // 下一代工具
  nextGen: 'turbopack' // 最新技术
}
```

### 2. 统一构建配置

```javascript
// shared-config.js - 共享构建配置
module.exports = {
  // 通用配置
  common: {
    resolve: {
      extensions: ['.ts', '.tsx', '.js', '.jsx', '.vue']
    },
    
    module: {
      rules: [
        {
          test: /\.(ts|tsx)$/,
          exclude: /node_modules/,
          use: 'ts-loader'
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    }
  },
  
  // 微前端特定配置
  microFrontend: {
    shared: {
      'react': { singleton: true },
      'react-dom': { singleton: true },
      '@micro-core/core': { singleton: true }
    },
    
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    }
  }
}
```

### 3. 环境配置管理

```javascript
// config/webpack.base.js
const path = require('path')
const sharedConfig = require('./shared-config')

module.exports = {
  ...sharedConfig.common,
  
  resolve: {
    ...sharedConfig.common.resolve,
    alias: {
      '@': path.resolve(__dirname, '../src'),
      '@shared': path.resolve(__dirname, '../shared')
    }
  }
}

// config/webpack.dev.js
const { merge } = require('webpack-merge')
const baseConfig = require('./webpack.base')

module.exports = merge(baseConfig, {
  mode: 'development',
  devtool: 'eval-source-map',
  
  devServer: {
    hot: true,
    port: 3000,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
})

// config/webpack.prod.js
const { merge } = require('webpack-merge')
const baseConfig = require('./webpack.base')

module.exports = merge(baseConfig, {
  mode: 'production',
  
  optimization: {
    minimize: true,
    sideEffects: false
  }
})
```

## 构建优化

### 1. 依赖优化

```javascript
// 外部化大型依赖
module.exports = {
  externals: {
    'react': 'React',
    'react-dom': 'ReactDOM',
    'lodash': '_',
    'moment': 'moment'
  },
  
  // CDN 配置
  plugins: [
    new HtmlWebpackPlugin({
      template: 'src/index.html',
      cdnModules: [
        'https://unpkg.com/react@18/umd/react.production.min.js',
        'https://unpkg.com/react-dom@18/umd/react-dom.production.min.js'
      ]
    })
  ]
}
```

### 2. 缓存策略

```javascript
// 构建缓存配置
module.exports = {
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    }
  },
  
  output: {
    filename: '[name].[contenthash].js',
    chunkFilename: '[name].[contenthash].chunk.js'
  }
}
```

### 3. 并行构建

```javascript
// 并行构建配置
const os = require('os')

module.exports = {
  module: {
    rules: [
      {
        test: /\.js$/,
        use: [
          {
            loader: 'thread-loader',
            options: {
              workers: os.cpus().length - 1
            }
          },
          'babel-loader'
        ]
      }
    ]
  }
}
```

## 调试和监控

### 1. 构建分析

```javascript
// webpack-bundle-analyzer 配置
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

module.exports = {
  plugins: [
    new BundleAnalyzerPlugin({
      analyzerMode: process.env.ANALYZE ? 'server' : 'disabled',
      openAnalyzer: false
    })
  ]
}
```

### 2. 构建监控

```javascript
// 构建性能监控
class BuildMonitorPlugin {
  apply(compiler) {
    compiler.hooks.compile.tap('BuildMonitorPlugin', () => {
      console.time('Build Time')
    })
    
    compiler.hooks.done.tap('BuildMonitorPlugin', (stats) => {
      console.timeEnd('Build Time')
      
      const { errors, warnings } = stats.compilation
      console.log(`Errors: ${errors.length}, Warnings: ${warnings.length}`)
      
      // 发送构建统计到监控系统
      this.sendBuildStats(stats)
    })
  }
  
  sendBuildStats(stats) {
    // 实现构建统计上报
  }
}
```

## 故障排除

### 常见问题

1. **模块解析失败**
   ```javascript
   // 解决方案：配置模块别名
   resolve: {
     alias: {
       '@': path.resolve(__dirname, 'src'),
       '@micro-core': path.resolve(__dirname, 'node_modules/@micro-core')
     }
   }
   ```

2. **构建速度慢**
   ```javascript
   // 解决方案：启用缓存和并行构建
   cache: { type: 'filesystem' },
   module: {
     rules: [
       {
         test: /\.js$/,
         use: ['thread-loader', 'babel-loader']
       }
     ]
   }
   ```

3. **包体积过大**
   ```javascript
   // 解决方案：代码分割和外部化
   optimization: {
     splitChunks: { chunks: 'all' }
   },
   externals: {
     'react': 'React',
     'lodash': '_'
   }
   ```

## 参考资料

- [Webpack 插件文档](/ecosystem/webpack-plugin)
- [Vite 插件文档](/ecosystem/vite-plugin)
- [构建集成指南](/guide/build-integration)
- [性能优化指南](/guide/performance)
