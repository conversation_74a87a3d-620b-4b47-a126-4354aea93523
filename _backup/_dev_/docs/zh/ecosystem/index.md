# 生态系统

Micro-Core 拥有丰富的生态系统，包括官方工具、社区插件、第三方集成和开发工具，为微前端开发提供全方位支持。

## 📋 目录

- [官方工具](#官方工具)
- [社区插件](#社区插件)
- [第三方集成](#第三方集成)
- [开发工具](#开发工具)
- [模板项目](#模板项目)
- [学习资源](#学习资源)
- [社区支持](#社区支持)

## 官方工具

### 核心包

| 包名 | 版本 | 描述 | 安装 |
|------|------|------|------|
| `@micro-core/core` | ![npm](https://img.shields.io/npm/v/@micro-core/core) | 核心运行时 | `npm i @micro-core/core` |
| `@micro-core/shared` | ![npm](https://img.shields.io/npm/v/@micro-core/shared) | 共享工具库 | `npm i @micro-core/shared` |
| `@micro-core/sidecar` | ![npm](https://img.shields.io/npm/v/@micro-core/sidecar) | Sidecar 模式支持 | `npm i @micro-core/sidecar` |

### 适配器

| 适配器 | 支持版本 | 特性 | 安装 |
|--------|----------|------|------|
| `@micro-core/adapter-react` | React 16+ | Hooks, Suspense, 错误边界 | `npm i @micro-core/adapter-react` |
| `@micro-core/adapter-vue` | Vue 2.6+, Vue 3+ | Composition API, 响应式 | `npm i @micro-core/adapter-vue` |
| `@micro-core/adapter-angular` | Angular 12+ | 依赖注入, RxJS | `npm i @micro-core/adapter-angular` |
| `@micro-core/adapter-svelte` | Svelte 3+ | 编译时优化 | `npm i @micro-core/adapter-svelte` |
| `@micro-core/adapter-solid` | Solid 1+ | 细粒度响应式 | `npm i @micro-core/adapter-solid` |

### 构建工具

| 工具 | 用途 | 特性 | 安装 |
|------|------|------|------|
| `@micro-core/builder-vite` | Vite 构建 | HMR, 快速构建 | `npm i @micro-core/builder-vite` |
| `@micro-core/builder-webpack` | Webpack 构建 | 模块联邦, 代码分割 | `npm i @micro-core/builder-webpack` |
| `@micro-core/builder-rollup` | Rollup 构建 | Tree-shaking, 体积优化 | `npm i @micro-core/builder-rollup` |
| `@micro-core/builder-esbuild` | ESBuild 构建 | 极速构建 | `npm i @micro-core/builder-esbuild` |

### 插件系统

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 插件生态                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   核心插件       │    │   官方插件       │    │   社区插件       ││
│  │                 │    │                 │    │                 ││
│  │ • 路由插件      │    │ • 状态管理      │    │ • UI 组件库     ││
│  │ • 通信插件      │    │ • 国际化        │    │ • 图表库        ││
│  │ • 认证插件      │    │ • 主题系统      │    │ • 工具库        ││
│  │ • 沙箱插件      │    │ • 监控埋点      │    │ • 业务组件      ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│           │                       │                       │       │
│           └───────────────────────┼───────────────────────┘       │
│                                   │                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    插件注册中心                             │ │
│  │  • 插件发现 • 版本管理 • 依赖解析 • 自动更新              │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 核心插件

```typescript
// 路由插件
import { RouterPlugin } from '@micro-core/plugin-router'

const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/app',
  routes: [
    { path: '/user/*', app: 'user-app' },
    { path: '/order/*', app: 'order-app' }
  ]
})

// 通信插件
import { CommunicationPlugin } from '@micro-core/plugin-communication'

const commPlugin = new CommunicationPlugin({
  eventBus: {
    maxListeners: 100,
    enableWildcard: true
  },
  globalState: {
    persist: true,
    storage: 'localStorage'
  }
})

// 认证插件
import { AuthPlugin } from '@micro-core/plugin-auth'

const authPlugin = new AuthPlugin({
  tokenStorage: 'localStorage',
  refreshThreshold: 300, // 5分钟
  loginUrl: '/auth/login'
})
```

#### 官方插件

```typescript
// 状态管理插件
import { StatePlugin } from '@micro-core/plugin-state'

const statePlugin = new StatePlugin({
  store: 'redux', // 'redux' | 'mobx' | 'zustand'
  devtools: true,
  persistence: {
    key: 'micro-core-state',
    whitelist: ['user', 'settings']
  }
})

// 国际化插件
import { I18nPlugin } from '@micro-core/plugin-i18n'

const i18nPlugin = new I18nPlugin({
  locale: 'zh-CN',
  fallback: 'en-US',
  resources: {
    'zh-CN': () => import('./locales/zh-CN.json'),
    'en-US': () => import('./locales/en-US.json')
  }
})

// 主题插件
import { ThemePlugin } from '@micro-core/plugin-theme'

const themePlugin = new ThemePlugin({
  themes: {
    light: () => import('./themes/light.css'),
    dark: () => import('./themes/dark.css')
  },
  defaultTheme: 'light',
  storage: 'localStorage'
})
```

## 社区插件

### UI 组件库集成

```typescript
// Ant Design 集成
import { AntdPlugin } from '@micro-core/plugin-antd'

const antdPlugin = new AntdPlugin({
  theme: {
    primaryColor: '#1890ff',
    borderRadius: '6px'
  },
  locale: 'zh_CN',
  components: ['Button', 'Table', 'Form'] // 按需加载
})

// Element Plus 集成
import { ElementPlusPlugin } from '@micro-core/plugin-element-plus'

const elementPlugin = new ElementPlusPlugin({
  theme: {
    'el-color-primary': '#409eff'
  },
  locale: 'zh-cn'
})

// Material-UI 集成
import { MuiPlugin } from '@micro-core/plugin-mui'

const muiPlugin = new MuiPlugin({
  theme: {
    palette: {
      primary: { main: '#1976d2' },
      secondary: { main: '#dc004e' }
    }
  }
})
```

### 数据可视化

```typescript
// ECharts 集成
import { EChartsPlugin } from '@micro-core/plugin-echarts'

const echartsPlugin = new EChartsPlugin({
  theme: 'dark',
  renderer: 'canvas', // 'canvas' | 'svg'
  lazyLoad: true
})

// D3.js 集成
import { D3Plugin } from '@micro-core/plugin-d3'

const d3Plugin = new D3Plugin({
  version: '7.x',
  modules: ['d3-selection', 'd3-scale', 'd3-axis']
})
```

### 状态管理

```typescript
// Redux Toolkit 集成
import { RTKPlugin } from '@micro-core/plugin-rtk'

const rtkPlugin = new RTKPlugin({
  devtools: true,
  middleware: ['thunk', 'logger'],
  preloadedState: {}
})

// MobX 集成
import { MobXPlugin } from '@micro-core/plugin-mobx'

const mobxPlugin = new MobXPlugin({
  enforceActions: 'always',
  computedRequiresReaction: true,
  reactionRequiresObservable: true
})
```

## 第三方集成

### 监控和分析

```typescript
// Sentry 错误监控
import { SentryPlugin } from '@micro-core/plugin-sentry'

const sentryPlugin = new SentryPlugin({
  dsn: 'https://<EMAIL>/project-id',
  environment: 'production',
  tracesSampleRate: 0.1,
  beforeSend: (event) => {
    // 过滤敏感信息
    return event
  }
})

// Google Analytics
import { GAPlugin } from '@micro-core/plugin-ga'

const gaPlugin = new GAPlugin({
  trackingId: 'GA_TRACKING_ID',
  anonymizeIp: true,
  trackPageviews: true,
  trackEvents: true
})

// 百度统计
import { BaiduAnalyticsPlugin } from '@micro-core/plugin-baidu'

const baiduPlugin = new BaiduAnalyticsPlugin({
  siteId: 'your-site-id',
  trackPageviews: true,
  trackEvents: true
})
```

### CDN 和缓存

```typescript
// CDN 插件
import { CDNPlugin } from '@micro-core/plugin-cdn'

const cdnPlugin = new CDNPlugin({
  provider: 'aliyun', // 'aliyun' | 'tencent' | 'aws'
  domain: 'https://cdn.example.com',
  cache: {
    maxAge: 86400, // 24小时
    staleWhileRevalidate: 3600 // 1小时
  }
})

// Service Worker 缓存
import { SWPlugin } from '@micro-core/plugin-sw'

const swPlugin = new SWPlugin({
  cacheStrategy: 'networkFirst',
  cacheName: 'micro-core-cache',
  maxEntries: 100,
  maxAgeSeconds: 86400
})
```

## 开发工具

### CLI 工具

```bash
# 安装 CLI
npm install -g @micro-core/cli

# 创建新项目
micro-core create my-project

# 添加微应用
micro-core add app my-app --framework react

# 启动开发服务器
micro-core dev

# 构建项目
micro-core build

# 部署项目
micro-core deploy --env production
```

### 开发者工具

```typescript
// DevTools 插件
import { DevToolsPlugin } from '@micro-core/plugin-devtools'

const devToolsPlugin = new DevToolsPlugin({
  enabled: process.env.NODE_ENV === 'development',
  features: {
    inspector: true,      // 应用检查器
    profiler: true,       // 性能分析器
    logger: true,         // 日志查看器
    stateViewer: true,    // 状态查看器
    eventTracker: true    // 事件追踪器
  }
})

// 热重载插件
import { HMRPlugin } from '@micro-core/plugin-hmr'

const hmrPlugin = new HMRPlugin({
  port: 3000,
  overlay: true,
  reload: {
    delay: 100,
    maxRetries: 3
  }
})
```

### 测试工具

```typescript
// 测试工具包
import { TestUtils } from '@micro-core/test-utils'

// 微应用测试
describe('MicroApp Tests', () => {
  let testUtils

  beforeEach(() => {
    testUtils = new TestUtils({
      apps: [
        { name: 'test-app', entry: './test-app' }
      ]
    })
  })

  test('should mount app correctly', async () => {
    await testUtils.mountApp('test-app')
    expect(testUtils.getApp('test-app')).toBeDefined()
  })

  test('should communicate between apps', async () => {
    await testUtils.mountApp('test-app')
    
    const result = await testUtils.sendMessage('test-app', {
      type: 'test',
      data: 'hello'
    })
    
    expect(result).toBe('hello')
  })
})
```

## 模板项目

### 官方模板

```bash
# React + TypeScript 模板
npx @micro-core/create-app my-app --template react-ts

# Vue 3 + TypeScript 模板
npx @micro-core/create-app my-app --template vue3-ts

# Angular 模板
npx @micro-core/create-app my-app --template angular

# 多框架模板
npx @micro-core/create-app my-app --template multi-framework
```

### 社区模板

| 模板名称 | 技术栈 | 特性 | 维护者 |
|----------|--------|------|--------|
| `ecommerce-template` | React + Redux + Ant Design | 电商系统完整模板 | @community |
| `admin-dashboard` | Vue 3 + Element Plus | 管理后台模板 | @community |
| `blog-template` | Next.js + Tailwind CSS | 博客系统模板 | @community |
| `saas-template` | Angular + Material | SaaS 应用模板 | @community |

### 企业级模板

```typescript
// 企业级配置模板
const enterpriseConfig = {
  // 安全配置
  security: {
    csp: {
      enabled: true,
      directives: {
        'default-src': ["'self'"],
        'script-src': ["'self'", "'unsafe-inline'"],
        'style-src': ["'self'", "'unsafe-inline'"]
      }
    },
    cors: {
      origin: ['https://example.com'],
      credentials: true
    }
  },

  // 性能配置
  performance: {
    preload: ['critical-app'],
    prefetch: ['secondary-app'],
    lazy: ['admin-app'],
    cache: {
      strategy: 'stale-while-revalidate',
      maxAge: 3600
    }
  },

  // 监控配置
  monitoring: {
    performance: true,
    errors: true,
    userBehavior: true,
    customMetrics: true
  }
}
```

## 学习资源

### 官方文档

- 📚 [快速开始](/guide/getting-started)
- 🏗️ [架构设计](/guide/architecture)
- 🔧 [API 参考](/api/)
- 💡 [示例项目](/examples/)
- 🚀 [迁移指南](/migration/)

### 视频教程

| 标题 | 时长 | 难度 | 链接 |
|------|------|------|------|
| Micro-Core 入门教程 | 30分钟 | 初级 | [观看](https://example.com/tutorial-1) |
| 微前端架构设计 | 45分钟 | 中级 | [观看](https://example.com/tutorial-2) |
| 企业级实战案例 | 60分钟 | 高级 | [观看](https://example.com/tutorial-3) |
| 性能优化技巧 | 35分钟 | 中级 | [观看](https://example.com/tutorial-4) |

### 博客文章

- [微前端架构的演进之路](https://blog.example.com/micro-frontend-evolution)
- [Micro-Core vs qiankun 对比分析](https://blog.example.com/micro-core-vs-qiankun)
- [大型企业微前端落地实践](https://blog.example.com/enterprise-micro-frontend)
- [微前端性能优化最佳实践](https://blog.example.com/micro-frontend-performance)

### 开源项目

| 项目名称 | 描述 | Stars | 技术栈 |
|----------|------|-------|--------|
| [micro-core-examples](https://github.com/micro-core/examples) | 官方示例项目 | ⭐ 1.2k | 多框架 |
| [micro-core-admin](https://github.com/community/admin) | 管理后台模板 | ⭐ 800 | React + Ant Design |
| [micro-core-ecommerce](https://github.com/community/ecommerce) | 电商系统 | ⭐ 600 | Vue 3 + Element Plus |
| [micro-core-tools](https://github.com/community/tools) | 开发工具集 | ⭐ 400 | Node.js |

## 社区支持

### 官方渠道

- 🌐 **官方网站**: [https://micro-core.dev](https://micro-core.dev)
- 📖 **文档站点**: [https://docs.micro-core.dev](https://docs.micro-core.dev)
- 🐙 **GitHub**: [https://github.com/micro-core/micro-core](https://github.com/micro-core/micro-core)
- 📦 **NPM**: [https://www.npmjs.com/org/micro-core](https://www.npmjs.com/org/micro-core)

### 社区渠道

- 💬 **Discord**: [加入讨论](https://discord.gg/micro-core)
- 🐦 **Twitter**: [@MicroCoreDev](https://twitter.com/MicroCoreDev)
- 📺 **知乎**: [Micro-Core 官方](https://zhihu.com/org/micro-core)
- 📱 **微信群**: 扫码加入技术交流群

### 贡献指南

```typescript
// 贡献类型
const contributionTypes = {
  code: {
    description: '代码贡献',
    examples: ['Bug 修复', '新功能开发', '性能优化'],
    process: '提交 PR → 代码审查 → 合并'
  },
  
  documentation: {
    description: '文档贡献',
    examples: ['文档完善', '示例补充', '翻译工作'],
    process: '提交 Issue → 讨论 → 实施'
  },
  
  community: {
    description: '社区贡献',
    examples: ['问题解答', '插件开发', '模板分享'],
    process: '参与讨论 → 分享经验 → 帮助他人'
  }
}
```

### 技术支持

| 支持类型 | 响应时间 | 渠道 | 说明 |
|----------|----------|------|------|
| 🆓 社区支持 | 1-3 天 | GitHub Issues | 开源社区支持 |
| 💼 商业支持 | 4-8 小时 | 邮件/电话 | 付费技术支持 |
| 🚀 企业服务 | 1-2 小时 | 专属渠道 | 企业级定制服务 |
| 🎓 培训服务 | 预约制 | 线上/线下 | 技术培训和咨询 |

### 版本发布

```typescript
// 发布计划
const releaseSchedule = {
  major: {
    frequency: '6-12 个月',
    features: ['重大架构升级', '破坏性变更'],
    nextVersion: 'v2.0.0',
    eta: '2024 Q4'
  },
  
  minor: {
    frequency: '1-2 个月',
    features: ['新功能', '性能优化'],
    nextVersion: 'v1.8.0',
    eta: '2024 Q2'
  },
  
  patch: {
    frequency: '1-2 周',
    features: ['Bug 修复', '安全更新'],
    nextVersion: 'v1.7.3',
    eta: '本周'
  }
}
```

---

## 加入我们

Micro-Core 是一个开放的项目，我们欢迎所有形式的贡献：

- 🐛 **报告 Bug**: 帮助我们发现和修复问题
- 💡 **提出建议**: 分享你的想法和需求
- 🔧 **贡献代码**: 参与核心开发
- 📝 **完善文档**: 帮助改进文档质量
- 🌍 **推广项目**: 在社区中分享 Micro-Core

让我们一起构建更好的微前端生态系统！

---

更多信息请访问：
- [贡献指南](https://github.com/micro-core/micro-core/blob/main/CONTRIBUTING.md)
- [行为准则](https://github.com/micro-core/micro-core/blob/main/CODE_OF_CONDUCT.md)
- [安全政策](https://github.com/micro-core/micro-core/blob/main/SECURITY.md)
