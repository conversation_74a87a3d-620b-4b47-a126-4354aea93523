# 路由插件

Micro-Core 路由插件提供了统一的路由管理和协调功能，支持多种路由模式和高级特性。

## 📋 目录

- [插件概述](#插件概述)
- [安装配置](#安装配置)
- [基础用法](#基础用法)
- [路由模式](#路由模式)
- [路由守卫](#路由守卫)
- [嵌套路由](#嵌套路由)
- [动态路由](#动态路由)
- [路由缓存](#路由缓存)
- [API 参考](#api-参考)

## 插件概述

### 核心特性

```typescript
// 路由插件特性
const routerFeatures = {
  // 路由模式
  modes: [
    'hash',      // Hash 模式
    'history',   // History 模式
    'memory'     // 内存模式
  ],
  
  // 高级功能
  advanced: [
    '路由守卫',
    '嵌套路由',
    '动态路由',
    '路由缓存',
    '路由动画',
    '懒加载'
  ],
  
  // 框架支持
  frameworks: [
    'React Router',
    'Vue Router',
    'Angular Router',
    '原生路由'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由插件架构                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    路由协调器                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 路由监听器   │  │ 路由匹配器   │  │ 路由执行器           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    应用路由管理                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ App A 路由  │  │ App B 路由  │  │ App C 路由          │ │ │
│  │  │ React Router│  │ Vue Router  │  │ Angular Router      │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    浏览器路由 API                           │ │
│  │  • History API    • Hash Change    • PopState             │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装插件

```bash
# 安装路由插件
npm install @micro-core/plugin-router

# 或使用 yarn
yarn add @micro-core/plugin-router

# 或使用 pnpm
pnpm add @micro-core/plugin-router
```

### 基础配置

```typescript
import { MicroCore } from '@micro-core/core'
import { RouterPlugin } from '@micro-core/plugin-router'

// 创建路由插件实例
const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/',
  
  // 路由配置
  routes: [
    {
      path: '/app1',
      name: 'app1',
      activeWhen: '/app1',
      meta: {
        title: 'App 1',
        requiresAuth: true
      }
    },
    {
      path: '/app2',
      name: 'app2',
      activeWhen: ['/app2', '/app2/*'],
      meta: {
        title: 'App 2'
      }
    }
  ],
  
  // 路由守卫
  beforeEach: (to, from, next) => {
    console.log('路由跳转:', from.path, '->', to.path)
    next()
  }
})

// 注册插件
const microCore = new MicroCore()
microCore.use(routerPlugin)
```

### 高级配置

```typescript
// 高级路由配置
const advancedRouterConfig = {
  // 路由模式
  mode: 'history',
  
  // 基础路径
  base: '/micro-app/',
  
  // 路由表
  routes: [
    {
      path: '/dashboard',
      name: 'dashboard',
      activeWhen: '/dashboard',
      component: 'dashboard-app',
      meta: {
        title: '仪表板',
        icon: 'dashboard',
        requiresAuth: true,
        roles: ['admin', 'user']
      },
      // 嵌套路由
      children: [
        {
          path: 'analytics',
          name: 'analytics',
          activeWhen: '/dashboard/analytics',
          component: 'analytics-app'
        },
        {
          path: 'reports',
          name: 'reports',
          activeWhen: '/dashboard/reports',
          component: 'reports-app'
        }
      ]
    },
    {
      path: '/user/:id',
      name: 'user-detail',
      activeWhen: (location) => {
        return /^\/user\/\d+/.test(location.pathname)
      },
      component: 'user-app',
      // 路由参数验证
      beforeEnter: (to, from, next) => {
        const userId = to.params.id
        if (isValidUserId(userId)) {
          next()
        } else {
          next('/404')
        }
      }
    }
  ],
  
  // 全局路由守卫
  beforeEach: async (to, from, next) => {
    // 权限检查
    if (to.meta.requiresAuth && !isAuthenticated()) {
      next('/login')
      return
    }
    
    // 角色检查
    if (to.meta.roles && !hasRequiredRole(to.meta.roles)) {
      next('/403')
      return
    }
    
    next()
  },
  
  afterEach: (to, from) => {
    // 更新页面标题
    if (to.meta.title) {
      document.title = to.meta.title
    }
    
    // 埋点统计
    analytics.track('page_view', {
      path: to.path,
      name: to.name
    })
  },
  
  // 路由错误处理
  onError: (error) => {
    console.error('路由错误:', error)
    // 错误上报
    errorReporter.report(error)
  }
}
```

## 基础用法

### 路由导航

```typescript
// 路由导航 API
class RouterAPI {
  // 编程式导航
  static push(location: string | RouteLocation): Promise<void> {
    return router.push(location)
  }
  
  static replace(location: string | RouteLocation): Promise<void> {
    return router.replace(location)
  }
  
  static go(delta: number): void {
    return router.go(delta)
  }
  
  static back(): void {
    return router.back()
  }
  
  static forward(): void {
    return router.forward()
  }
  
  // 获取当前路由
  static getCurrentRoute(): RouteLocation {
    return router.currentRoute
  }
  
  // 路由匹配
  static resolve(location: string | RouteLocation): RouteResolved {
    return router.resolve(location)
  }
}

// 使用示例
export class NavigationService {
  // 导航到指定路由
  async navigateTo(path: string, params?: Record<string, any>) {
    try {
      await RouterAPI.push({
        path,
        params,
        query: { timestamp: Date.now() }
      })
    } catch (error) {
      console.error('导航失败:', error)
    }
  }
  
  // 带确认的导航
  async navigateWithConfirm(path: string, message: string) {
    if (confirm(message)) {
      await this.navigateTo(path)
    }
  }
  
  // 返回上一页
  goBack() {
    RouterAPI.back()
  }
  
  // 刷新当前页面
  refresh() {
    const current = RouterAPI.getCurrentRoute()
    RouterAPI.replace(current.path)
  }
}
```

### 路由组件

```typescript
// React 路由组件
import { useRouter, useRoute } from '@micro-core/plugin-router/react'

export function AppNavigation() {
  const router = useRouter()
  const route = useRoute()
  
  const handleNavigation = (path: string) => {
    router.push(path)
  }
  
  return (
    <nav>
      <button 
        onClick={() => handleNavigation('/dashboard')}
        className={route.path === '/dashboard' ? 'active' : ''}
      >
        仪表板
      </button>
      <button 
        onClick={() => handleNavigation('/profile')}
        className={route.path === '/profile' ? 'active' : ''}
      >
        个人资料
      </button>
    </nav>
  )
}

// Vue 路由组件
import { useRouter, useRoute } from '@micro-core/plugin-router/vue'

export default {
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    const navigateTo = (path: string) => {
      router.push(path)
    }
    
    return {
      router,
      route,
      navigateTo
    }
  },
  
  template: `
    <nav>
      <button 
        @click="navigateTo('/dashboard')"
        :class="{ active: route.path === '/dashboard' }"
      >
        仪表板
      </button>
      <button 
        @click="navigateTo('/profile')"
        :class="{ active: route.path === '/profile' }"
      >
        个人资料
      </button>
    </nav>
  `
}
```

## 路由模式

### Hash 模式

```typescript
// Hash 模式配置
const hashModeConfig = {
  mode: 'hash',
  
  // Hash 模式特定配置
  hashPrefix: '#',
  
  // URL 示例: http://example.com/#/dashboard
  routes: [
    {
      path: '/dashboard',
      activeWhen: '#/dashboard'
    }
  ]
}

// Hash 模式实现
class HashRouter {
  private listeners: Set<RouteListener> = new Set()
  
  constructor() {
    window.addEventListener('hashchange', this.handleHashChange.bind(this))
  }
  
  private handleHashChange() {
    const hash = window.location.hash.slice(1) || '/'
    this.notifyListeners(hash)
  }
  
  push(path: string) {
    window.location.hash = path
  }
  
  replace(path: string) {
    const url = window.location.href.replace(/#.*$/, '') + '#' + path
    window.location.replace(url)
  }
  
  private notifyListeners(path: string) {
    this.listeners.forEach(listener => listener(path))
  }
}
```

### History 模式

```typescript
// History 模式配置
const historyModeConfig = {
  mode: 'history',
  
  // History 模式特定配置
  base: '/',
  
  // URL 示例: http://example.com/dashboard
  routes: [
    {
      path: '/dashboard',
      activeWhen: '/dashboard'
    }
  ]
}

// History 模式实现
class HistoryRouter {
  private listeners: Set<RouteListener> = new Set()
  private base: string
  
  constructor(base: string = '/') {
    this.base = base
    window.addEventListener('popstate', this.handlePopState.bind(this))
  }
  
  private handlePopState(event: PopStateEvent) {
    const path = this.getPath()
    this.notifyListeners(path)
  }
  
  push(path: string) {
    const fullPath = this.base + path.replace(/^\//, '')
    window.history.pushState({}, '', fullPath)
    this.notifyListeners(path)
  }
  
  replace(path: string) {
    const fullPath = this.base + path.replace(/^\//, '')
    window.history.replaceState({}, '', fullPath)
    this.notifyListeners(path)
  }
  
  private getPath(): string {
    const path = window.location.pathname
    return path.startsWith(this.base) 
      ? path.slice(this.base.length) || '/'
      : path
  }
}
```

## 路由守卫

### 全局守卫

```typescript
// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  console.log(`导航: ${from.path} -> ${to.path}`)
  
  // 1. 加载状态
  showLoading()
  
  try {
    // 2. 权限验证
    if (to.meta.requiresAuth) {
      const isAuthenticated = await checkAuth()
      if (!isAuthenticated) {
        next('/login')
        return
      }
    }
    
    // 3. 角色验证
    if (to.meta.roles) {
      const hasPermission = await checkPermission(to.meta.roles)
      if (!hasPermission) {
        next('/403')
        return
      }
    }
    
    // 4. 数据预加载
    if (to.meta.preload) {
      await preloadData(to.meta.preload)
    }
    
    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    next('/error')
  } finally {
    hideLoading()
  }
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 1. 更新页面标题
  document.title = to.meta.title || 'Micro App'
  
  // 2. 埋点统计
  analytics.track('page_view', {
    path: to.path,
    referrer: from.path
  })
  
  // 3. 滚动位置
  if (to.meta.scrollToTop) {
    window.scrollTo(0, 0)
  }
})

// 全局错误守卫
router.onError((error, to, from) => {
  console.error('路由错误:', error)
  
  // 错误上报
  errorReporter.report({
    error,
    route: { to, from },
    timestamp: Date.now()
  })
  
  // 显示错误页面
  router.push('/error')
})
```

### 路由级守卫

```typescript
// 路由级守卫配置
const routeWithGuards = {
  path: '/admin',
  name: 'admin',
  component: 'admin-app',
  
  // 路由独享守卫
  beforeEnter: async (to, from, next) => {
    // 管理员权限检查
    const user = await getCurrentUser()
    
    if (!user || user.role !== 'admin') {
      next('/403')
      return
    }
    
    // 检查管理员会话
    const sessionValid = await validateAdminSession()
    if (!sessionValid) {
      next('/admin/login')
      return
    }
    
    next()
  },
  
  // 离开守卫
  beforeLeave: async (to, from, next) => {
    // 检查是否有未保存的更改
    const hasUnsavedChanges = checkUnsavedChanges()
    
    if (hasUnsavedChanges) {
      const confirmed = await showConfirmDialog(
        '您有未保存的更改，确定要离开吗？'
      )
      
      if (!confirmed) {
        next(false)
        return
      }
    }
    
    next()
  }
}
```

### 组件级守卫

```typescript
// React 组件级守卫
import { useRouteGuard } from '@micro-core/plugin-router/react'

export function ProtectedComponent() {
  const { canActivate, canDeactivate } = useRouteGuard({
    // 激活守卫
    beforeEnter: async () => {
      const hasPermission = await checkPermission()
      return hasPermission
    },
    
    // 离开守卫
    beforeLeave: async () => {
      const hasUnsavedData = checkUnsavedData()
      if (hasUnsavedData) {
        return confirm('确定要离开吗？')
      }
      return true
    }
  })
  
  if (!canActivate) {
    return <div>权限不足</div>
  }
  
  return (
    <div>
      <h1>受保护的组件</h1>
      {/* 组件内容 */}
    </div>
  )
}

// Vue 组件级守卫
export default {
  beforeRouteEnter(to, from, next) {
    // 进入路由前
    checkPermission().then(hasPermission => {
      if (hasPermission) {
        next()
      } else {
        next('/403')
      }
    })
  },
  
  beforeRouteUpdate(to, from, next) {
    // 路由更新时
    this.loadData(to.params.id).then(() => {
      next()
    })
  },
  
  beforeRouteLeave(to, from, next) {
    // 离开路由前
    if (this.hasUnsavedChanges) {
      const answer = confirm('确定要离开吗？')
      if (answer) {
        next()
      } else {
        next(false)
      }
    } else {
      next()
    }
  }
}
```

## 嵌套路由

### 嵌套路由配置

```typescript
// 嵌套路由配置
const nestedRoutes = {
  path: '/dashboard',
  name: 'dashboard',
  component: 'dashboard-app',
  
  // 子路由
  children: [
    {
      path: '',
      name: 'dashboard-home',
      component: 'dashboard-home'
    },
    {
      path: 'analytics',
      name: 'dashboard-analytics',
      component: 'analytics-app',
      children: [
        {
          path: 'reports',
          name: 'analytics-reports',
          component: 'reports-app'
        },
        {
          path: 'charts',
          name: 'analytics-charts',
          component: 'charts-app'
        }
      ]
    },
    {
      path: 'settings',
      name: 'dashboard-settings',
      component: 'settings-app',
      children: [
        {
          path: 'profile',
          name: 'settings-profile',
          component: 'profile-app'
        },
        {
          path: 'security',
          name: 'settings-security',
          component: 'security-app'
        }
      ]
    }
  ]
}
```

### 嵌套路由实现

```typescript
// 嵌套路由管理器
class NestedRouteManager {
  private routeTree: RouteNode
  private activeRoutes: RouteNode[] = []
  
  constructor(routes: RouteConfig[]) {
    this.routeTree = this.buildRouteTree(routes)
  }
  
  // 构建路由树
  private buildRouteTree(routes: RouteConfig[]): RouteNode {
    const root: RouteNode = {
      path: '',
      children: new Map()
    }
    
    routes.forEach(route => {
      this.addRouteToTree(root, route)
    })
    
    return root
  }
  
  private addRouteToTree(parent: RouteNode, route: RouteConfig) {
    const segments = route.path.split('/').filter(Boolean)
    let current = parent
    
    segments.forEach((segment, index) => {
      if (!current.children.has(segment)) {
        current.children.set(segment, {
          path: segments.slice(0, index + 1).join('/'),
          children: new Map()
        })
      }
      current = current.children.get(segment)!
    })
    
    // 设置路由配置
    current.config = route
    
    // 处理子路由
    if (route.children) {
      route.children.forEach(child => {
        this.addRouteToTree(current, {
          ...child,
          path: route.path + '/' + child.path
        })
      })
    }
  }
  
  // 匹配嵌套路由
  matchNestedRoute(path: string): RouteMatch[] {
    const segments = path.split('/').filter(Boolean)
    const matches: RouteMatch[] = []
    let current = this.routeTree
    
    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i]
      
      if (current.children.has(segment)) {
        current = current.children.get(segment)!
        
        if (current.config) {
          matches.push({
            route: current.config,
            params: this.extractParams(current.config.path, path),
            depth: i + 1
          })
        }
      } else {
        break
      }
    }
    
    return matches
  }
  
  // 激活嵌套路由
  async activateNestedRoutes(matches: RouteMatch[]) {
    // 卸载不再活跃的路由
    await this.deactivateUnusedRoutes(matches)
    
    // 激活新的路由
    for (const match of matches) {
      await this.activateRoute(match)
    }
    
    this.activeRoutes = matches.map(m => m.route)
  }
  
  private async activateRoute(match: RouteMatch) {
    const { route, params } = match
    
    // 加载路由组件
    if (route.component && !route.loaded) {
      await this.loadRouteComponent(route)
    }
    
    // 执行路由守卫
    if (route.beforeEnter) {
      const canEnter = await route.beforeEnter(match, params)
      if (!canEnter) {
        throw new Error(`Route guard rejected: ${route.path}`)
      }
    }
    
    // 渲染组件
    await this.renderRouteComponent(route, params)
  }
}

interface RouteNode {
  path: string
  children: Map<string, RouteNode>
  config?: RouteConfig
}

interface RouteMatch {
  route: RouteConfig
  params: Record<string, string>
  depth: number
}
```

## 动态路由

### 动态路由配置

```typescript
// 动态路由配置
const dynamicRoutes = [
  {
    path: '/user/:id',
    name: 'user-detail',
    component: 'user-app',
    // 参数验证
    props: (route) => ({
      userId: Number(route.params.id),
      tab: route.query.tab || 'profile'
    })
  },
  {
    path: '/post/:category/:slug',
    name: 'post-detail',
    component: 'post-app',
    // 复杂参数处理
    props: (route) => ({
      category: route.params.category,
      slug: route.params.slug,
      preview: route.query.preview === 'true'
    })
  },
  {
    path: '/search/:keyword?',
    name: 'search',
    component: 'search-app',
    // 可选参数
    props: (route) => ({
      keyword: route.params.keyword || '',
      filters: JSON.parse(route.query.filters || '{}')
    })
  }
]
```

### 动态路由匹配

```typescript
// 动态路由匹配器
class DynamicRouteMatcher {
  private routes: CompiledRoute[] = []
  
  constructor(routes: RouteConfig[]) {
    this.routes = routes.map(route => this.compileRoute(route))
  }
  
  // 编译路由
  private compileRoute(route: RouteConfig): CompiledRoute {
    const paramNames: string[] = []
    const regexPattern = route.path
      .replace(/\/:([^\/]+)\?/g, (match, paramName) => {
        paramNames.push(paramName)
        return '(?:/([^/]+))?'
      })
      .replace(/\/:([^\/]+)/g, (match, paramName) => {
        paramNames.push(paramName)
        return '/([^/]+)'
      })
    
    return {
      ...route,
      regex: new RegExp(`^${regexPattern}$`),
      paramNames
    }
  }
  
  // 匹配路由
  match(path: string): RouteMatch | null {
    for (const route of this.routes) {
      const match = path.match(route.regex)
      
      if (match) {
        const params: Record<string, string> = {}
        
        route.paramNames.forEach((name, index) => {
          params[name] = match[index + 1] || ''
        })
        
        return {
          route: route,
          params,
          path,
          query: this.parseQuery(window.location.search)
        }
      }
    }
    
    return null
  }
  
  // 生成路由路径
  generate(name: string, params: Record<string, any> = {}): string {
    const route = this.routes.find(r => r.name === name)
    
    if (!route) {
      throw new Error(`Route not found: ${name}`)
    }
    
    let path = route.path
    
    // 替换参数
    route.paramNames.forEach(paramName => {
      const value = params[paramName]
      if (value !== undefined) {
        path = path.replace(`:${paramName}`, String(value))
      }
    })
    
    // 处理可选参数
    path = path.replace(/\/:[^\/]+\?/g, (match) => {
      const paramName = match.slice(2, -1)
      const value = params[paramName]
      return value !== undefined ? `/${value}` : ''
    })
    
    return path
  }
  
  private parseQuery(search: string): Record<string, string> {
    const query: Record<string, string> = {}
    const params = new URLSearchParams(search)
    
    for (const [key, value] of params) {
      query[key] = value
    }
    
    return query
  }
}

interface CompiledRoute extends RouteConfig {
  regex: RegExp
  paramNames: string[]
}
```

## 路由缓存

### 缓存策略

```typescript
// 路由缓存管理器
class RouteCacheManager {
  private cache: Map<string, CacheEntry> = new Map()
  private maxCacheSize: number = 10
  private cacheStrategy: CacheStrategy = 'lru'
  
  constructor(options: CacheOptions = {}) {
    this.maxCacheSize = options.maxSize || 10
    this.cacheStrategy = options.strategy || 'lru'
  }
  
  // 缓存路由
  cacheRoute(route: RouteConfig, data: any): void {
    const key = this.generateCacheKey(route)
    
    // 检查缓存大小
    if (this.cache.size >= this.maxCacheSize) {
      this.evictCache()
    }
    
    this.cache.set(key, {
      route,
      data,
      timestamp: Date.now(),
      accessCount: 1
    })
  }
  
  // 获取缓存
  getCachedRoute(route: RouteConfig): any | null {
    const key = this.generateCacheKey(route)
    const entry = this.cache.get(key)
    
    if (entry) {
      // 更新访问信息
      entry.accessCount++
      entry.timestamp = Date.now()
      
      return entry.data
    }
    
    return null
  }
  
  // 清除缓存
  clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern)
      for (const [key] of this.cache) {
        if (regex.test(key)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
  }
  
  // 缓存淘汰
  private evictCache(): void {
    switch (this.cacheStrategy) {
      case 'lru':
        this.evictLRU()
        break
      case 'lfu':
        this.evictLFU()
        break
      case 'fifo':
        this.evictFIFO()
        break
    }
  }
  
  private evictLRU(): void {
    let oldestKey = ''
    let oldestTime = Date.now()
    
    for (const [key, entry] of this.cache) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }
  
  private evictLFU(): void {
    let leastUsedKey = ''
    let leastCount = Infinity
    
    for (const [key, entry] of this.cache) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount
        leastUsedKey = key
      }
    }
    
    if (leastUsedKey) {
      this.cache.delete(leastUsedKey)
    }
  }
  
  private evictFIFO(): void {
    const firstKey = this.cache.keys().next().value
    if (firstKey) {
      this.cache.delete(firstKey)
    }
  }
  
  private generateCacheKey(route: RouteConfig): string {
    return `${route.path}:${JSON.stringify(route.params || {})}`
  }
}

interface CacheEntry {
  route: RouteConfig
  data: any
  timestamp: number
  accessCount: number
}

interface CacheOptions {
  maxSize?: number
  strategy?: CacheStrategy
}

type CacheStrategy = 'lru' | 'lfu' | 'fifo'
```

### 预加载策略

```typescript
// 路由预加载管理器
class RoutePreloader {
  private preloadQueue: Set<string> = new Set()
  private preloadedRoutes: Map<string, any> = new Map()
  
  // 预加载路由
  async preloadRoute(route: RouteConfig): Promise<void> {
    const key = route.path
    
    if (this.preloadedRoutes.has(key) || this.preloadQueue.has(key)) {
      return
    }
    
    this.preloadQueue.add(key)
    
    try {
      // 预加载组件
      if (route.component && typeof route.component === 'string') {
        const component = await this.loadComponent(route.component)
        this.preloadedRoutes.set(key, component)
      }
      
      // 预加载数据
      if (route.meta?.preload) {
        const data = await this.preloadData(route.meta.preload)
        this.preloadedRoutes.set(`${key}:data`, data)
      }
    } catch (error) {
      console.warn(`预加载路由失败: ${key}`, error)
    } finally {
      this.preloadQueue.delete(key)
    }
  }
  
  // 智能预加载
  async smartPreload(currentRoute: RouteConfig): Promise<void> {
    const candidates = this.getPredictedRoutes(currentRoute)
    
    // 使用 requestIdleCallback 在空闲时预加载
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        candidates.forEach(route => this.preloadRoute(route))
      })
    } else {
      // 降级到 setTimeout
      setTimeout(() => {
        candidates.forEach(route => this.preloadRoute(route))
      }, 100)
    }
  }
  
  // 预测可能访问的路由
  private getPredictedRoutes(currentRoute: RouteConfig): RouteConfig[] {
    const predictions: RouteConfig[] = []
    
    // 基于历史访问模式预测
    const history = this.getNavigationHistory()
    const nextRoutes = this.analyzeNavigationPatterns(history, currentRoute)
    predictions.push(...nextRoutes)
    
    // 基于页面链接预测
    const pageLinks = this.extractPageLinks()
    const linkedRoutes = this.matchLinksToRoutes(pageLinks)
    predictions.push(...linkedRoutes)
    
    return predictions
  }
  
  private async loadComponent(componentName: string): Promise<any> {
    // 动态导入组件
    return await import(`@/components/${componentName}`)
  }
  
  private async preloadData(preloadConfig: any): Promise<any> {
    // 预加载数据
    if (typeof preloadConfig === 'function') {
      return await preloadConfig()
    } else if (typeof preloadConfig === 'string') {
      const response = await fetch(preloadConfig)
      return await response.json()
    }
    
    return null
  }
}
```

## API 参考

### 核心 API

```typescript
// 路由插件主要 API
interface RouterPlugin {
  // 路由导航
  push(location: string | RouteLocation): Promise<void>
  replace(location: string | RouteLocation): Promise<void>
  go(delta: number): void
  back(): void
  forward(): void
  
  // 路由信息
  getCurrentRoute(): RouteLocation
  resolve(location: string | RouteLocation): RouteResolved
  
  // 路由守卫
  beforeEach(guard: NavigationGuard): void
  afterEach(guard: NavigationHookAfter): void
  onError(handler: ErrorHandler): void
  
  // 路由管理
  addRoute(route: RouteConfig): void
  removeRoute(name: string): void
  hasRoute(name: string): boolean
  getRoutes(): RouteConfig[]
  
  // 缓存管理
  clearCache(pattern?: string): void
  preloadRoute(route: string | RouteConfig): Promise<void>
}

// 路由配置接口
interface RouteConfig {
  path: string
  name?: string
  component?: string | (() => Promise<any>)
  activeWhen?: string | string[] | ((location: Location) => boolean)
  meta?: RouteMeta
  children?: RouteConfig[]
  beforeEnter?: NavigationGuard
  beforeLeave?: NavigationGuard
  props?: boolean | object | ((route: RouteLocation) => object)
}

// 路由元信息
interface RouteMeta {
  title?: string
  icon?: string
  requiresAuth?: boolean
  roles?: string[]
  preload?: any
  cache?: boolean
  scrollToTop?: boolean
  [key: string]: any
}

// 路由位置
interface RouteLocation {
  path: string
  name?: string
  params: Record<string, string>
  query: Record<string, string>
  hash: string
  meta: RouteMeta
}

// 导航守卫
type NavigationGuard = (
  to: RouteLocation,
  from: RouteLocation,
  next: NavigationGuardNext
) => void | Promise<void>

type NavigationGuardNext = (
  to?: string | RouteLocation | boolean | Error
) => void
```

### 工具函数

```typescript
// 路由工具函数
export const RouterUtils = {
  // 路径匹配
  isPathMatch(pattern: string, path: string): boolean {
    const regex = new RegExp(
      pattern.replace(/\*/g, '.*').replace(/\?/g, '\\?')
    )
    return regex.test(path)
  },
  
  // 参数提取
  extractParams(pattern: string, path: string): Record<string, string> {
    const params: Record<string, string> = {}
    const patternParts = pattern.split('/')
    const pathParts = path.split('/')
    
    patternParts.forEach((part, index) => {
      if (part.startsWith(':')) {
        const paramName = part.slice(1)
        params[paramName] = pathParts[index] || ''
      }
    })
    
    return params
  },
  
  // 查询参数解析
  parseQuery(search: string): Record<string, string> {
    const params = new URLSearchParams(search)
    const query: Record<string, string> = {}
    
    for (const [key, value] of params) {
      query[key] = value
    }
    
    return query
  },
  
  // 查询参数序列化
  stringifyQuery(query: Record<string, any>): string {
    const params = new URLSearchParams()
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        params.append(key, String(value))
      }
    })
    
    return params.toString()
  },
  
  // 路径规范化
  normalizePath(path: string): string {
    return path
      .replace(/\/+/g, '/') // 合并多个斜杠
      .replace(/\/$/, '') || '/' // 移除尾部斜杠
  },
  
  // 相对路径解析
  resolvePath(base: string, relative: string): string {
    if (relative.startsWith('/')) {
      return relative
    }
    
    const baseParts = base.split('/').slice(0, -1)
    const relativeParts = relative.split('/')
    
    relativeParts.forEach(part => {
      if (part === '..') {
        baseParts.pop()
      } else if (part !== '.') {
        baseParts.push(part)
      }
    })
    
    return baseParts.join('/') || '/'
  }
}
```

## 最佳实践

### 路由设计原则

```typescript
// 路由设计最佳实践
const routeDesignPrinciples = {
  // 1. 语义化路径
  semantic: {
    good: '/users/123/profile',
    bad: '/page1/item/detail'
  },
  
  // 2. 层次结构清晰
  hierarchy: {
    good: '/dashboard/analytics/reports',
    bad: '/dashboard-analytics-reports'
  },
  
  // 3. 参数命名规范
  parameters: {
    good: '/users/:userId/posts/:postId',
    bad: '/users/:id1/posts/:id2'
  },
  
  // 4. 查询参数用于过滤
  query: {
    good: '/products?category=electronics&sort=price',
    bad: '/products/electronics/price-sort'
  }
}
```

### 性能优化

```typescript
// 路由性能优化策略
class RoutePerformanceOptimizer {
  // 1. 路由懒加载
  static createLazyRoute(componentPath: string): RouteConfig {
    return {
      path: '/lazy-route',
      component: () => import(componentPath),
      meta: {
        preload: false // 禁用预加载
      }
    }
  }
  
  // 2. 路由预加载
  static enablePreloading(routes: RouteConfig[]): void {
    routes.forEach(route => {
      if (route.meta?.preload !== false) {
        // 在空闲时预加载
        requestIdleCallback(() => {
          if (typeof route.component === 'function') {
            route.component()
          }
        })
      }
    })
  }
  
  // 3. 路由缓存
  static enableCaching(cacheSize: number = 5): void {
    const cache = new RouteCacheManager({
      maxSize: cacheSize,
      strategy: 'lru'
    })
    
    // 在路由切换时缓存组件
    router.afterEach((to, from) => {
      if (from.meta?.cache !== false) {
        cache.cacheRoute(from, {
          component: from.component,
          data: from.data
        })
      }
    })
  }
}
```

## 总结

路由插件提供了完整的路由管理解决方案：

1. **多种路由模式** - 支持 Hash、History、Memory 模式
2. **强大的路由守卫** - 全局、路由级、组件级守卫
3. **嵌套路由支持** - 完整的嵌套路由解决方案
4. **动态路由匹配** - 灵活的参数路由支持
5. **智能缓存策略** - 提升路由切换性能
6. **预加载机制** - 优化用户体验

通过合理使用路由插件，可以构建高性能、用户体验良好的微前端应用。

## 相关链接

- [通信插件](/ecosystem/plugins/communication) - 应用间通信
- [认证插件](/ecosystem/plugins/auth) - 统一认证
- [核心 API](/api/core) - 核心功能 API
- [路由系统 API](/api/routing) - 路由系统详细 API
