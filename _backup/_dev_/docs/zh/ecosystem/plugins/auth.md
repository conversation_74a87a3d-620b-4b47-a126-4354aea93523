# 认证插件

Micro-Core 认证插件提供了统一的身份认证和授权管理功能，支持多种认证方式和权限控制策略。

## 📋 目录

- [插件概述](#插件概述)
- [安装配置](#安装配置)
- [身份认证](#身份认证)
- [权限控制](#权限控制)
- [单点登录](#单点登录)
- [会话管理](#会话管理)
- [安全特性](#安全特性)
- [API 参考](#api-参考)

## 插件概述

### 核心特性

```typescript
// 认证插件特性
const authFeatures = {
  // 认证方式
  authentication: [
    'JWT Token',     // JWT 令牌认证
    'OAuth 2.0',     // OAuth 2.0 认证
    'SAML',          // SAML 认证
    'LDAP',          // LDAP 认证
    'Basic Auth',    // 基础认证
    'API Key'        // API 密钥认证
  ],
  
  // 授权模式
  authorization: [
    'RBAC',          // 基于角色的访问控制
    'ABAC',          // 基于属性的访问控制
    'ACL',           // 访问控制列表
    'Custom'         // 自定义权限
  ],
  
  // 高级功能
  advanced: [
    '单点登录 (SSO)',
    '多因子认证 (MFA)',
    '会话管理',
    '权限缓存',
    '安全审计',
    '密码策略'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    认证插件架构                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    认证协调器                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 认证管理器   │  │ 授权管理器   │  │ 会话管理器           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    认证提供者                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ JWT Provider│  │OAuth Provider│  │ SAML Provider       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    应用集成层                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ App A       │  │ App B       │  │ App C               │ │ │
│  │  │ 用户管理     │  │ 订单系统     │  │ 报表系统             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装插件

```bash
# 安装认证插件
npm install @micro-core/plugin-auth

# 或使用 yarn
yarn add @micro-core/plugin-auth

# 或使用 pnpm
pnpm add @micro-core/plugin-auth
```

### 基础配置

```typescript
import { MicroCore } from '@micro-core/core'
import { AuthPlugin } from '@micro-core/plugin-auth'

// 创建认证插件实例
const authPlugin = new AuthPlugin({
  // 认证提供者配置
  providers: {
    jwt: {
      secret: 'your-jwt-secret',
      expiresIn: '24h',
      algorithm: 'HS256'
    },
    oauth: {
      clientId: 'your-oauth-client-id',
      clientSecret: 'your-oauth-client-secret',
      redirectUri: 'http://localhost:3000/auth/callback'
    }
  },
  
  // 权限配置
  authorization: {
    mode: 'rbac',
    roles: ['admin', 'user', 'guest'],
    permissions: ['read', 'write', 'delete']
  },
  
  // 会话配置
  session: {
    timeout: 30 * 60 * 1000, // 30分钟
    renewThreshold: 5 * 60 * 1000, // 5分钟
    storage: 'localStorage'
  },
  
  // 安全配置
  security: {
    enableMFA: false,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireNumbers: true,
      requireSymbols: true
    }
  }
})

// 注册插件
const microCore = new MicroCore()
microCore.use(authPlugin)
```

### 高级配置

```typescript
// 高级认证配置
const advancedAuthConfig = {
  // 多认证提供者
  providers: {
    jwt: {
      secret: process.env.JWT_SECRET,
      expiresIn: '24h',
      refreshTokenExpiry: '7d',
      algorithm: 'RS256',
      publicKey: process.env.JWT_PUBLIC_KEY,
      privateKey: process.env.JWT_PRIVATE_KEY
    },
    
    oauth: {
      google: {
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        scope: ['profile', 'email']
      },
      github: {
        clientId: process.env.GITHUB_CLIENT_ID,
        clientSecret: process.env.GITHUB_CLIENT_SECRET,
        scope: ['user:email']
      }
    },
    
    saml: {
      entryPoint: 'https://your-idp.com/sso',
      issuer: 'your-app-identifier',
      cert: process.env.SAML_CERT
    }
  },
  
  // 高级授权配置
  authorization: {
    mode: 'abac', // 基于属性的访问控制
    
    // 角色定义
    roles: {
      admin: {
        permissions: ['*'],
        inherits: []
      },
      manager: {
        permissions: ['user:read', 'user:write', 'report:read'],
        inherits: ['user']
      },
      user: {
        permissions: ['profile:read', 'profile:write'],
        inherits: ['guest']
      },
      guest: {
        permissions: ['public:read'],
        inherits: []
      }
    },
    
    // 资源定义
    resources: {
      'user': ['read', 'write', 'delete'],
      'order': ['read', 'write', 'cancel'],
      'report': ['read', 'export'],
      'system': ['config', 'monitor']
    },
    
    // 策略规则
    policies: [
      {
        name: 'owner-access',
        rule: 'resource.ownerId === user.id',
        effect: 'allow'
      },
      {
        name: 'admin-full-access',
        rule: 'user.role === "admin"',
        effect: 'allow'
      }
    ]
  },
  
  // 单点登录配置
  sso: {
    enabled: true,
    domain: '.example.com',
    cookieName: 'sso-token',
    crossDomain: true
  },
  
  // 多因子认证
  mfa: {
    enabled: true,
    methods: ['totp', 'sms', 'email'],
    required: ['admin'],
    optional: ['user']
  }
}
```

## 身份认证

### JWT 认证

```typescript
// JWT 认证服务
import { useAuth } from '@micro-core/plugin-auth'

export class JWTAuthService {
  private auth = useAuth()
  
  // 用户登录
  async login(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      // 验证用户凭据
      const user = await this.validateCredentials(credentials)
      
      if (!user) {
        throw new Error('Invalid credentials')
      }
      
      // 生成 JWT Token
      const token = await this.auth.generateToken({
        userId: user.id,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      })
      
      // 生成刷新令牌
      const refreshToken = await this.auth.generateRefreshToken(user.id)
      
      // 设置认证状态
      await this.auth.setAuthState({
        user,
        token,
        refreshToken,
        expiresAt: Date.now() + 24 * 60 * 60 * 1000 // 24小时
      })
      
      return {
        success: true,
        user,
        token,
        refreshToken
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  // 用户登出
  async logout(): Promise<void> {
    try {
      // 清除认证状态
      await this.auth.clearAuthState()
      
      // 撤销令牌
      const token = this.auth.getToken()
      if (token) {
        await this.auth.revokeToken(token)
      }
      
      // 发送登出事件
      this.auth.emit('auth:logout')
    } catch (error) {
      console.error('登出失败:', error)
    }
  }
  
  // 刷新令牌
  async refreshToken(): Promise<string | null> {
    try {
      const refreshToken = this.auth.getRefreshToken()
      
      if (!refreshToken) {
        throw new Error('No refresh token available')
      }
      
      // 验证刷新令牌
      const isValid = await this.auth.validateRefreshToken(refreshToken)
      
      if (!isValid) {
        throw new Error('Invalid refresh token')
      }
      
      // 生成新的访问令牌
      const newToken = await this.auth.refreshAccessToken(refreshToken)
      
      // 更新认证状态
      await this.auth.updateAuthState({ token: newToken })
      
      return newToken
    } catch (error) {
      console.error('令牌刷新失败:', error)
      await this.logout()
      return null
    }
  }
  
  // 验证用户凭据
  private async validateCredentials(credentials: LoginCredentials): Promise<User | null> {
    // 实现用户验证逻辑
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(credentials)
    })
    
    if (response.ok) {
      return await response.json()
    }
    
    return null
  }
}

interface LoginCredentials {
  email: string
  password: string
}

interface AuthResult {
  success: boolean
  user?: User
  token?: string
  refreshToken?: string
  error?: string
}

interface User {
  id: string
  email: string
  name: string
  role: string
  permissions: string[]
}
```

### OAuth 2.0 认证

```typescript
// OAuth 2.0 认证服务
export class OAuthService {
  private auth = useAuth()
  
  // 启动 OAuth 认证流程
  async startOAuthFlow(provider: string): Promise<void> {
    const config = this.auth.getProviderConfig(provider)
    
    if (!config) {
      throw new Error(`OAuth provider ${provider} not configured`)
    }
    
    // 生成状态参数
    const state = this.generateState()
    
    // 构建授权 URL
    const authUrl = this.buildAuthUrl(provider, config, state)
    
    // 保存状态
    sessionStorage.setItem('oauth_state', state)
    sessionStorage.setItem('oauth_provider', provider)
    
    // 重定向到授权服务器
    window.location.href = authUrl
  }
  
  // 处理 OAuth 回调
  async handleOAuthCallback(code: string, state: string): Promise<AuthResult> {
    try {
      // 验证状态参数
      const savedState = sessionStorage.getItem('oauth_state')
      const provider = sessionStorage.getItem('oauth_provider')
      
      if (state !== savedState) {
        throw new Error('Invalid state parameter')
      }
      
      if (!provider) {
        throw new Error('No OAuth provider found')
      }
      
      // 交换授权码获取访问令牌
      const tokenResponse = await this.exchangeCodeForToken(provider, code)
      
      // 获取用户信息
      const userInfo = await this.getUserInfo(provider, tokenResponse.access_token)
      
      // 创建本地用户会话
      const authResult = await this.createUserSession(userInfo, tokenResponse)
      
      // 清理临时数据
      sessionStorage.removeItem('oauth_state')
      sessionStorage.removeItem('oauth_provider')
      
      return authResult
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  private buildAuthUrl(provider: string, config: any, state: string): string {
    const params = new URLSearchParams({
      client_id: config.clientId,
      redirect_uri: config.redirectUri,
      scope: config.scope.join(' '),
      state,
      response_type: 'code'
    })
    
    const baseUrls = {
      google: 'https://accounts.google.com/o/oauth2/v2/auth',
      github: 'https://github.com/login/oauth/authorize',
      microsoft: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize'
    }
    
    return `${baseUrls[provider]}?${params.toString()}`
  }
  
  private async exchangeCodeForToken(provider: string, code: string): Promise<any> {
    const config = this.auth.getProviderConfig(provider)
    
    const response = await fetch('/api/auth/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        provider,
        code,
        client_id: config.clientId,
        client_secret: config.clientSecret,
        redirect_uri: config.redirectUri
      })
    })
    
    if (!response.ok) {
      throw new Error('Failed to exchange code for token')
    }
    
    return await response.json()
  }
  
  private async getUserInfo(provider: string, accessToken: string): Promise<any> {
    const apiUrls = {
      google: 'https://www.googleapis.com/oauth2/v2/userinfo',
      github: 'https://api.github.com/user',
      microsoft: 'https://graph.microsoft.com/v1.0/me'
    }
    
    const response = await fetch(apiUrls[provider], {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    })
    
    if (!response.ok) {
      throw new Error('Failed to get user info')
    }
    
    return await response.json()
  }
  
  private generateState(): string {
    return btoa(crypto.getRandomValues(new Uint8Array(32)).toString())
  }
  
  private async createUserSession(userInfo: any, tokenResponse: any): Promise<AuthResult> {
    // 创建或更新用户
    const user = await this.createOrUpdateUser(userInfo)
    
    // 生成本地 JWT
    const token = await this.auth.generateToken({
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions
    })
    
    // 设置认证状态
    await this.auth.setAuthState({
      user,
      token,
      oauthToken: tokenResponse.access_token,
      expiresAt: Date.now() + tokenResponse.expires_in * 1000
    })
    
    return {
      success: true,
      user,
      token
    }
  }
  
  private async createOrUpdateUser(userInfo: any): Promise<User> {
    // 实现用户创建或更新逻辑
    const response = await fetch('/api/users/oauth', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userInfo)
    })
    
    return await response.json()
  }
}
```

## 权限控制

### RBAC 权限模型

```typescript
// 基于角色的访问控制 (RBAC)
export class RBACService {
  private auth = useAuth()
  private rolePermissions: Map<string, Set<string>> = new Map()
  private userRoles: Map<string, Set<string>> = new Map()
  
  constructor() {
    this.initializeRoles()
  }
  
  // 初始化角色权限
  private initializeRoles(): void {
    // 管理员权限
    this.rolePermissions.set('admin', new Set([
      'user:create', 'user:read', 'user:update', 'user:delete',
      'role:create', 'role:read', 'role:update', 'role:delete',
      'system:config', 'system:monitor', 'system:backup'
    ]))
    
    // 管理者权限
    this.rolePermissions.set('manager', new Set([
      'user:read', 'user:update',
      'report:read', 'report:export',
      'order:read', 'order:update', 'order:cancel'
    ]))
    
    // 普通用户权限
    this.rolePermissions.set('user', new Set([
      'profile:read', 'profile:update',
      'order:read', 'order:create'
    ]))
    
    // 访客权限
    this.rolePermissions.set('guest', new Set([
      'public:read'
    ]))
  }
  
  // 检查用户权限
  async hasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const userRoles = await this.getUserRoles(userId)
      
      for (const role of userRoles) {
        const rolePerms = this.rolePermissions.get(role)
        if (rolePerms && (rolePerms.has(permission) || rolePerms.has('*'))) {
          return true
        }
      }
      
      return false
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  }
  
  // 检查多个权限
  async hasAnyPermission(userId: string, permissions: string[]): Promise<boolean> {
    for (const permission of permissions) {
      if (await this.hasPermission(userId, permission)) {
        return true
      }
    }
    return false
  }
  
  // 检查所有权限
  async hasAllPermissions(userId: string, permissions: string[]): Promise<boolean> {
    for (const permission of permissions) {
      if (!(await this.hasPermission(userId, permission))) {
        return false
      }
    }
    return true
  }
  
  // 获取用户角色
  async getUserRoles(userId: string): Promise<string[]> {
    if (this.userRoles.has(userId)) {
      return Array.from(this.userRoles.get(userId)!)
    }
    
    // 从数据库或 API 获取用户角色
    const roles = await this.fetchUserRoles(userId)
    this.userRoles.set(userId, new Set(roles))
    
    return roles
  }
  
  // 分配角色给用户
  async assignRole(userId: string, role: string): Promise<void> {
    if (!this.rolePermissions.has(role)) {
      throw new Error(`Role ${role} does not exist`)
    }
    
    const userRoles = this.userRoles.get(userId) || new Set()
    userRoles.add(role)
    this.userRoles.set(userId, userRoles)
    
    // 持久化到数据库
    await this.saveUserRoles(userId, Array.from(userRoles))
  }
  
  // 移除用户角色
  async removeRole(userId: string, role: string): Promise<void> {
    const userRoles = this.userRoles.get(userId)
    if (userRoles) {
      userRoles.delete(role)
      await this.saveUserRoles(userId, Array.from(userRoles))
    }
  }
  
  // 获取角色权限
  getRolePermissions(role: string): string[] {
    const permissions = this.rolePermissions.get(role)
    return permissions ? Array.from(permissions) : []
  }
  
  private async fetchUserRoles(userId: string): Promise<string[]> {
    // 从 API 获取用户角色
    const response = await fetch(`/api/users/${userId}/roles`)
    if (response.ok) {
      return await response.json()
    }
    return ['guest'] // 默认角色
  }
  
  private async saveUserRoles(userId: string, roles: string[]): Promise<void> {
    await fetch(`/api/users/${userId}/roles`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ roles })
    })
  }
}
```

### 权限装饰器

```typescript
// 权限装饰器
export function RequirePermission(permission: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const auth = useAuth()
      const user = auth.getCurrentUser()
      
      if (!user) {
        throw new Error('用户未登录')
      }
      
      const hasPermission = await auth.hasPermission(user.id, permission)
      if (!hasPermission) {
        throw new Error(`权限不足: 需要 ${permission} 权限`)
      }
      
      return method.apply(this, args)
    }
    
    return descriptor
  }
}

// 角色装饰器
export function RequireRole(role: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const auth = useAuth()
      const user = auth.getCurrentUser()
      
      if (!user) {
        throw new Error('用户未登录')
      }
      
      const userRoles = await auth.getUserRoles(user.id)
      if (!userRoles.includes(role)) {
        throw new Error(`权限不足: 需要 ${role} 角色`)
      }
      
      return method.apply(this, args)
    }
    
    return descriptor
  }
}

// 使用示例
export class UserService {
  @RequirePermission('user:read')
  async getUsers(): Promise<User[]> {
    // 获取用户列表
    return []
  }
  
  @RequirePermission('user:create')
  async createUser(userData: any): Promise<User> {
    // 创建用户
    return {} as User
  }
  
  @RequireRole('admin')
  async deleteUser(userId: string): Promise<void> {
    // 删除用户
  }
}
```

## 单点登录

### SSO 实现

```typescript
// 单点登录服务
export class SSOService {
  private auth = useAuth()
  private ssoConfig: SSOConfig
  
  constructor(config: SSOConfig) {
    this.ssoConfig = config
    this.initializeSSO()
  }
  
  // 初始化 SSO
  private initializeSSO(): void {
    if (this.ssoConfig.enabled) {
      this.setupCrossDomainAuth()
      this.setupTokenSync()
    }
  }
  
  // 设置跨域认证
  private setupCrossDomainAuth(): void {
    // 监听来自其他域的认证消息
    window.addEventListener('message', (event) => {
      if (this.isValidSSODomain(event.origin)) {
        this.handleSSOMessage(event.data)
      }
    })
    
    // 检查是否已在其他域登录
    this.checkExistingSSO()
  }
  
  // 处理 SSO 消息
  private handleSSOMessage(data: any): void {
    switch (data.type) {
      case 'sso:login':
        this.handleSSOLogin(data.payload)
        break
      case 'sso:logout':
        this.handleSSOLogout()
        break
      case 'sso:token-refresh':
        this.handleTokenRefresh(data.payload)
        break
    }
  }
  
  // 处理 SSO 登录
  private async handleSSOLogin(payload: any): Promise<void> {
    try {
      // 验证 SSO 令牌
      const isValid = await this.validateSSOToken(payload.token)
      
      if (isValid) {
        // 设置本地认证状态
        await this.auth.setAuthState({
          user: payload.user,
          token: payload.token,
          ssoToken: payload.ssoToken,
          expiresAt: payload.expiresAt
        })
        
        // 通知应用登录成功
        this.auth.emit('sso:login:success', payload.user)
      }
    } catch (error) {
      console.error('SSO 登录失败:', error)
      this.auth.emit('sso:login:error', error)
    }
  }
  
  // 处理 SSO 登出
  private async handleSSOLogout(): Promise<void> {
    try {
      await this.auth.clearAuthState()
      this.auth.emit('sso:logout:success')
    } catch (error) {
      console.error('SSO 登出失败:', error)
    }
  }
  
  // 启动 SSO 登录
  async startSSOLogin(): Promise<void> {
    const ssoUrl = this.buildSSOUrl()
    
    // 在新窗口中打开 SSO 登录页面
    const popup = window.open(
      ssoUrl,
      'sso-login',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    )
    
    // 监听登录完成
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed)
        this.checkSSOResult()
      }
    }, 1000)
  }
  
  // 执行 SSO 登出
  async performSSOLogout(): Promise<void> {
    // 通知所有相关域进行登出
    this.broadcastSSOMessage({
      type: 'sso:logout',
      timestamp: Date.now()
    })
    
    // 清除 SSO Cookie
    this.clearSSOCookie()
    
    // 清除本地认证状态
    await this.auth.clearAuthState()
  }
  
  // 检查现有 SSO 状态
  private async checkExistingSSO(): Promise<void> {
    const ssoToken = this.getSSOToken()
    
    if (ssoToken) {
      try {
        const isValid = await this.validateSSOToken(ssoToken)
        
        if (isValid) {
          // 获取用户信息
          const userInfo = await this.getSSOUserInfo(ssoToken)
          
          if (userInfo) {
            await this.handleSSOLogin({
              user: userInfo,
              token: ssoToken,
              ssoToken,
              expiresAt: userInfo.expiresAt
            })
          }
        } else {
          this.clearSSOCookie()
        }
      } catch (error) {
        console.error('SSO 状态检查失败:', error)
        this.clearSSOCookie()
      }
    }
  }
  
  // 广播 SSO 消息
  private broadcastSSOMessage(message: any): void {
    // 向所有相关域发送消息
    this.ssoConfig.domains?.forEach(domain => {
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      iframe.src = `${domain}/sso-receiver.html`
      
      iframe.onload = () => {
        iframe.contentWindow?.postMessage(message, domain)
        setTimeout(() => document.body.removeChild(iframe), 1000)
      }
      
      document.body.appendChild(iframe)
    })
  }
  
  private buildSSOUrl(): string {
    const params = new URLSearchParams({
      client_id: this.ssoConfig.clientId,
      redirect_uri: this.ssoConfig.redirectUri,
      response_type: 'token',
      state: this.generateState()
    })
    
    return `${this.ssoConfig.ssoUrl}?${params.toString()}`
  }
  
  private getSSOToken(): string | null {
    return this.getCookie(this.ssoConfig.cookieName)
  }
  
  private setSSOToken(token: string): void {
    this.setCookie(this.ssoConfig.cookieName, token, {
      domain: this.ssoConfig.domain,
      secure: true,
      httpOnly: false,
      sameSite: 'none'
    })
  }
  
  private clearSSOCookie(): void {
    this.deleteCookie(this.ssoConfig.cookieName, this.ssoConfig.domain)
  }
  
  private getCookie(name: string): string | null {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null
    }
    return null
  }
  
  private setCookie(name: string, value: string, options: any): void {
    let cookieString = `${name}=${value}`
    
    if (options.domain) cookieString += `; Domain=${options.domain}`
    if (options.secure) cookieString += '; Secure'
    if (options.httpOnly) cookieString += '; HttpOnly'
    if (options.sameSite) cookieString += `; SameSite=${options.sameSite}`
    
    document.cookie = cookieString
  }
  
  private deleteCookie(name: string, domain?: string): void {
    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`
    if (domain) cookieString += `; Domain=${domain}`
    document.cookie = cookieString
  }
  
  private isValidSSODomain(origin: string): boolean {
    return this.ssoConfig.domains?.includes(origin) || false
  }
  
  private generateState(): string {
    return Math.random().toString(36).substring(2, 15)
  }
  
  private async validateSSOToken(token: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.ssoConfig.validateUrl}`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      })
      return response.ok
    } catch {
      return false
    }
  }
  
  private async getSSOUserInfo(token: string): Promise<any> {
    const response = await fetch(`${this.ssoConfig.userInfoUrl}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    return response.ok ? await response.json() : null
  }
  
  private async checkSSOResult(): Promise<void> {
    // 检查 SSO 登录结果
    const ssoToken = this.getSSOToken()
    if (ssoToken) {
      await this.checkExistingSSO()
    }
  }
}

interface SSOConfig {
  enabled: boolean
  ssoUrl: string
  clientId: string
  redirectUri: string
  validateUrl: string
  userInfoUrl: string
  cookieName: string
  domain: string
  domains?: string[]
}
```

## 会话管理

### 会话控制

```typescript
// 会话管理服务
export class SessionManager {
  private auth = useAuth()
  private sessionConfig: SessionConfig
  private sessionTimer: NodeJS.Timeout | null = null
  private renewTimer: NodeJS.Timeout | null = null
  
  constructor(config: SessionConfig) {
    this.sessionConfig = config
    this.initializeSession()
  }
  
  // 初始化会话管理
  private initializeSession(): void {
    // 监听用户活动
    this.setupActivityMonitoring()
    
    // 检查现有会话
    this.checkExistingSession()
    
    // 监听认证状态变化
    this.auth.on('auth:login', () => this.startSession())
    this.auth.on('auth:logout', () => this.endSession())
  }
  
  // 开始会话
  private startSession(): void {
    this.resetSessionTimer()
    this.setupRenewalTimer()
    
    // 保存会话信息
    this.saveSessionInfo({
      startTime: Date.now(),
      lastActivity: Date.now(),
      expiresAt: Date.now() + this.sessionConfig.timeout
    })
  }
  
  // 结束会话
  private endSession(): void {
    this.clearSessionTimer()
    this.clearRenewalTimer()
    this.clearSessionInfo()
  }
  
  // 重置会话计时器
  private resetSessionTimer(): void {
    this.clearSessionTimer()
    
    this.sessionTimer = setTimeout(() => {
      this.handleSessionTimeout()
    }, this.sessionConfig.timeout)
  }
  
  // 设置续期计时器
  private setupRenewalTimer(): void {
    this.clearRenewalTimer()
    
    const renewTime = this.sessionConfig.timeout - this.sessionConfig.renewThreshold
    
    this.renewTimer = setTimeout(() => {
      this.handleSessionRenewal()
    }, renewTime)
  }
  
  // 处理会话超时
  private async handleSessionTimeout(): Promise<void> {
    try {
      // 发送会话超时警告
      this.auth.emit('session:timeout:warning')
      
      // 给用户一些时间响应
      await this.showTimeoutWarning()
      
      // 如果用户没有响应，执行登出
      if (!this.isUserActive()) {
        await this.auth.logout()
        this.auth.emit('session:timeout:logout')
      } else {
        // 用户仍然活跃，续期会话
        this.renewSession()
      }
    } catch (error) {
      console.error('会话超时处理失败:', error)
    }
  }
  
  // 处理会话续期
  private async handleSessionRenewal(): Promise<void> {
    try {
      const token = this.auth.getToken()
      
      if (token) {
        // 尝试刷新令牌
        const newToken = await this.auth.refreshToken()
        
        if (newToken) {
          // 续期成功，重置计时器
          this.renewSession()
          this.auth.emit('session:renewed')
        } else {
          // 续期失败，执行登出
          await this.auth.logout()
          this.auth.emit('session:renewal:failed')
        }
      }
    } catch (error) {
      console.error('会话续期失败:', error)
      await this.auth.logout()
    }
  }
  
  // 续期会话
  private renewSession(): void {
    const now = Date.now()
    
    this.updateSessionInfo({
      lastActivity: now,
      expiresAt: now + this.sessionConfig.timeout
    })
    
    this.resetSessionTimer()
    this.setupRenewalTimer()
  }
  
  // 设置活动监控
  private setupActivityMonitoring(): void {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    
    const activityHandler = () => {
      this.updateLastActivity()
    }
    
    events.forEach(event => {
      document.addEventListener(event, activityHandler, true)
    })
  }
  
  // 更新最后活动时间
  private updateLastActivity(): void {
    const sessionInfo = this.getSessionInfo()
    
    if (sessionInfo) {
      sessionInfo.lastActivity = Date.now()
      this.saveSessionInfo(sessionInfo)
    }
  }
  
  // 检查用户是否活跃
  private isUserActive(): boolean {
    const sessionInfo = this.getSessionInfo()
    
    if (!sessionInfo) return false
    
    const inactiveTime = Date.now() - sessionInfo.lastActivity
    return inactiveTime < this.sessionConfig.inactivityThreshold
  }
  
  // 显示超时警告
  private async showTimeoutWarning(): Promise<void> {
    return new Promise((resolve) => {
      const modal = this.createTimeoutModal()
      document.body.appendChild(modal)
      
      setTimeout(() => {
        if (document.body.contains(modal)) {
          document.body.removeChild(modal)
        }
        resolve()
      }, 30000) // 30秒警告时间
    })
  }
  
  // 创建超时警告模态框
  private createTimeoutModal(): HTMLElement {
    const modal = document.createElement('div')
    modal.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      ">
        <div style="
          background: white;
          padding: 20px;
          border-radius: 8px;
          text-align: center;
          max-width: 400px;
        ">
          <h3>会话即将过期</h3>
          <p>您的会话将在 30 秒后过期，请点击继续以保持登录状态。</p>
          <button id="continue-session" style="
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
          ">继续会话</button>
          <button id="logout-now" style="
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
          ">立即登出</button>
        </div>
      </div>
    `
    
    // 绑定事件
    modal.querySelector('#continue-session')?.addEventListener('click', () => {
      this.renewSession()
      document.body.removeChild(modal)
    })
    
    modal.querySelector('#logout-now')?.addEventListener('click', () => {
      this.auth.logout()
      document.body.removeChild(modal)
    })
    
    return modal
  }
  
  // 会话信息管理
  private saveSessionInfo(info: SessionInfo): void {
    const storage = this.getStorage()
    storage.setItem('session_info', JSON.stringify(info))
  }
  
  private getSessionInfo(): SessionInfo | null {
    const storage = this.getStorage()
    const info = storage.getItem('session_info')
    return info ? JSON.parse(info) : null
  }
  
  private updateSessionInfo(updates: Partial<SessionInfo>): void {
    const current = this.getSessionInfo()
    if (current) {
      this.saveSessionInfo({ ...current, ...updates })
    }
  }
  
  private clearSessionInfo(): void {
    const storage = this.getStorage()
    storage.removeItem('session_info')
  }
  
  private checkExistingSession(): void {
    const sessionInfo = this.getSessionInfo()
    
    if (sessionInfo && sessionInfo.expiresAt > Date.now()) {
      // 会话仍然有效
      const remainingTime = sessionInfo.expiresAt - Date.now()
      
      this.sessionTimer = setTimeout(() => {
        this.handleSessionTimeout()
      }, remainingTime)
      
      // 设置续期计时器
      const renewTime = remainingTime - this.sessionConfig.renewThreshold
      if (renewTime > 0) {
        this.renewTimer = setTimeout(() => {
          this.handleSessionRenewal()
        }, renewTime)
      }
    }
  }
  
  private getStorage(): Storage {
    return this.sessionConfig.storage === 'sessionStorage' 
      ? sessionStorage 
      : localStorage
  }
  
  private clearSessionTimer(): void {
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer)
      this.sessionTimer = null
    }
  }
  
  private clearRenewalTimer(): void {
    if (this.renewTimer) {
      clearTimeout(this.renewTimer)
      this.renewTimer = null
    }
  }
}

interface SessionConfig {
  timeout: number
  renewThreshold: number
  inactivityThreshold: number
  storage: 'localStorage' | 'sessionStorage'
}

interface SessionInfo {
  startTime: number
  lastActivity: number
  expiresAt: number
}
```

## API 参考

### 核心 API

```typescript
// 认证插件主要 API
interface AuthPlugin {
  // 认证管理
  login(credentials: any): Promise<AuthResult>
  logout(): Promise<void>
  refreshToken(): Promise<string | null>
  getCurrentUser(): User | null
  isAuthenticated(): boolean
  
  // 权限管理
  hasPermission(userId: string, permission: string): Promise<boolean>
  hasRole(userId: string, role: string): Promise<boolean>
  getUserRoles(userId: string): Promise<string[]>
  getUserPermissions(userId: string): Promise<string[]>
  
  // 会话管理
  getSession(): SessionInfo | null
  renewSession(): Promise<void>
  endSession(): Promise<void>
  
  // 事件监听
  on(event: string, handler: Function): void
  off(event: string, handler: Function): void
  emit(event: string, data?: any): void
}

// 认证结果接口
interface AuthResult {
  success: boolean
  user?: User
  token?: string
  refreshToken?: string
  error?: string
}

// 用户接口
interface User {
  id: string
  email: string
  name: string
  role: string
  permissions: string[]
  avatar?: string
  lastLoginAt?: number
}
```

## 总结

认证插件提供了完整的身份认证和授权解决方案：

1. **多种认证方式** - JWT、OAuth 2.0、SAML 等
2. **灵活的权限控制** - RBAC、ABAC 等权限模型
3. **单点登录支持** - 跨域 SSO 解决方案
4. **智能会话管理** - 自动续期和超时处理
5. **安全特性** - 多因子认证、密码策略等

通过合理配置和使用认证插件，可以构建安全可靠的微前端应用认证体系。

## 相关链接

- [路由插件](/ecosystem/plugins/router) - 路由权限控制
- [通信插件](/ecosystem/plugins/communication) - 认证状态同步
- [核心 API](/api/core) - 核心功能 API
- [安全指南](/guide/security) - 安全最佳实践
