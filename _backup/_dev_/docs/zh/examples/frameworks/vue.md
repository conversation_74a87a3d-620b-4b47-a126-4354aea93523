# Vue 微应用示例

本示例展示如何创建和集成 Vue 微应用到 Micro-Core 系统中，支持 Vue 2 和 Vue 3。

## 完整示例

### 主应用配置

```typescript
// main.ts
import { MicroCoreKernel } from '@micro-core/core';
import { RouterPlugin } from '@micro-core/plugin-router';
import { CommunicationPlugin } from '@micro-core/plugin-communication';
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';

// 创建内核实例
const kernel = new MicroCoreKernel({
  devMode: process.env.NODE_ENV === 'development',
  errorHandler: (error, app) => {
    console.error(`应用 ${app?.name} 发生错误:`, error);
  }
});

// 注册插件
kernel
  .use(RouterPlugin, { mode: 'history' })
  .use(CommunicationPlugin)
  .use(ProxySandboxPlugin, { strict: true });

// 注册 Vue 微应用
kernel.registerApplication({
  name: 'vue-app',
  entry: 'http://localhost:3002',
  container: '#vue-app-container',
  activeWhen: '/vue-app',
  sandbox: 'proxy',
  props: {
    basename: '/vue-app',
    theme: 'light',
    apiConfig: {
      baseUrl: 'https://api.example.com',
      timeout: 5000
    }
  },
  lifecycle: {
    beforeMount: async (app) => {
      console.log('Vue 应用即将挂载:', app.name);
    },
    afterMount: async (app) => {
      console.log('Vue 应用挂载完成:', app.name);
    }
  }
});

// 启动系统
kernel.start();
```

## Vue 3 微应用

### 1. 项目结构

```
vue3-app/
├── src/
│   ├── components/
│   │   ├── App.vue
│   │   ├── Header.vue
│   │   └── ProductList.vue
│   ├── composables/
│   │   ├── useMicroCore.ts
│   │   └── useGlobalState.ts
│   ├── router/
│   │   └── index.ts
│   ├── store/
│   │   └── index.ts
│   ├── main.ts
│   └── types.ts
├── public/
│   └── index.html
├── package.json
├── vite.config.ts
└── tsconfig.json
```

### 2. 入口文件

```typescript
// src/main.ts
import { createApp, App as VueApp } from 'vue';
import { VueAdapter } from '@micro-core/adapter-vue3';
import App from './App.vue';
import router from './router';
import store from './store';
import { MicroCoreProps } from './types';

let app: VueApp | null = null;

// 微前端生命周期函数
export const mount = VueAdapter.mount<MicroCoreProps>(async (props) => {
  const { container, basename, theme, apiConfig, ...otherProps } = props;
  
  // 创建 Vue 应用实例
  app = createApp(App);
  
  // 配置路由基础路径
  if (basename && router.options.history) {
    router.options.history.base = basename;
  }
  
  // 注入 props 到全局属性
  app.config.globalProperties.$microProps = {
    theme,
    apiConfig,
    ...otherProps
  };
  
  // 使用插件
  app.use(router);
  app.use(store);
  
  // 挂载应用
  app.mount(container);
  
  return {
    instance: app,
    router,
    store
  };
});

export const unmount = VueAdapter.unmount(async () => {
  if (app) {
    app.unmount();
    app = null;
  }
  
  // 重置路由
  if (router.currentRoute.value.path !== '/') {
    router.replace('/');
  }
  
  // 清理 store
  store.commit('RESET_STATE');
});

export const update = VueAdapter.update<MicroCoreProps>(async (props) => {
  if (app) {
    // 更新全局属性
    app.config.globalProperties.$microProps = {
      ...app.config.globalProperties.$microProps,
      ...props
    };
    
    // 触发响应式更新
    store.commit('UPDATE_PROPS', props);
  }
});

// 独立运行模式
if (!window.__MICRO_CORE__) {
  app = createApp(App);
  app.use(router);
  app.use(store);
  app.mount('#app');
}
```

### 3. 主组件

```vue
<!-- src/App.vue -->
<template>
  <div :class="`vue-app theme-${currentTheme}`">
    <Header 
      :theme="currentTheme" 
      :user="currentUser"
      @theme-change="handleThemeChange"
    />
    
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
    
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">加载中...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import Header from './components/Header.vue';
import { useMicroCore } from './composables/useMicroCore';

const store = useStore();
const { eventBus, globalState, isInMicroCore } = useMicroCore();

// 响应式数据
const loading = ref(false);

// 计算属性
const currentTheme = computed(() => store.state.theme);
const currentUser = computed(() => store.state.user);

// 方法
const handleThemeChange = (theme: string) => {
  store.commit('SET_THEME', theme);
  
  // 通知主应用主题变化
  if (eventBus) {
    eventBus.emit('child:theme-change', theme);
  }
};

const handleUserLogin = (userData: any) => {
  store.commit('SET_USER', userData);
  
  // 通知主应用用户登录
  if (eventBus) {
    eventBus.emit('child:user-login', userData);
  }
};

// 生命周期
onMounted(() => {
  // 获取初始状态
  if (globalState) {
    const initialTheme = globalState.get('theme');
    const currentUser = globalState.get('currentUser');
    
    if (initialTheme) {
      store.commit('SET_THEME', initialTheme);
    }
    
    if (currentUser) {
      store.commit('SET_USER', currentUser);
    }
  }
  
  // 监听主应用事件
  if (eventBus) {
    eventBus.on('theme:changed', handleThemeChange);
    eventBus.on('user:login-success', handleUserLogin);
  }
  
  // 监听全局状态变化
  if (globalState) {
    globalState.watch('theme', (newTheme: string) => {
      store.commit('SET_THEME', newTheme);
    });
    
    globalState.watch('currentUser', (newUser: any) => {
      store.commit('SET_USER', newUser);
    });
  }
  
  // 向主应用报告应用状态
  if (eventBus) {
    eventBus.emit('app:ready', {
      name: 'vue-app',
      version: '1.0.0',
      features: ['routing', 'state-management', 'composition-api']
    });
  }
});

onUnmounted(() => {
  // 清理事件监听器
  if (eventBus) {
    eventBus.off('theme:changed', handleThemeChange);
    eventBus.off('user:login-success', handleUserLogin);
  }
});
</script>

<style scoped>
.vue-app {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.theme-light {
  background-color: #ffffff;
  color: #333333;
}

.theme-dark {
  background-color: #1a1a1a;
  color: #ffffff;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
```

### 4. 自定义 Composables

```typescript
// src/composables/useMicroCore.ts
import { ref, onMounted, onUnmounted } from 'vue';
import { EventBus, GlobalState } from '@micro-core/core';

interface MicroCoreContext {
  eventBus: typeof EventBus | null;
  globalState: typeof GlobalState | null;
  isInMicroCore: boolean;
}

export function useMicroCore(): MicroCoreContext {
  const eventBus = ref<typeof EventBus | null>(null);
  const globalState = ref<typeof GlobalState | null>(null);
  const isInMicroCore = ref(false);

  onMounted(() => {
    if (window.__MICRO_CORE__) {
      eventBus.value = EventBus;
      globalState.value = GlobalState;
      isInMicroCore.value = true;
    }
  });

  return {
    eventBus: eventBus.value,
    globalState: globalState.value,
    isInMicroCore: isInMicroCore.value
  };
}

// 全局状态 Composable
export function useGlobalState<T>(key: string, defaultValue?: T) {
  const state = ref<T>(defaultValue as T);
  const { globalState } = useMicroCore();

  onMounted(() => {
    if (globalState) {
      // 获取初始值
      const currentValue = globalState.get(key);
      if (currentValue !== undefined) {
        state.value = currentValue;
      }

      // 监听状态变化
      const unwatch = globalState.watch(key, (newValue: T) => {
        state.value = newValue;
      });

      onUnmounted(() => {
        unwatch();
      });
    }
  });

  const updateState = (newValue: T) => {
    if (globalState) {
      globalState.set(key, newValue);
    } else {
      state.value = newValue;
    }
  };

  return {
    state,
    updateState
  };
}
```

### 5. Vuex Store

```typescript
// src/store/index.ts
import { createStore } from 'vuex';

interface State {
  theme: string;
  user: any;
  products: any[];
  cart: {
    items: any[];
    total: number;
  };
  loading: boolean;
  error: string | null;
}

export default createStore<State>({
  state: {
    theme: 'light',
    user: null,
    products: [],
    cart: {
      items: [],
      total: 0
    },
    loading: false,
    error: null
  },

  mutations: {
    SET_THEME(state, theme: string) {
      state.theme = theme;
    },

    SET_USER(state, user: any) {
      state.user = user;
    },

    SET_PRODUCTS(state, products: any[]) {
      state.products = products;
    },

    ADD_TO_CART(state, product: any) {
      const existingItem = state.cart.items.find(item => item.id === product.id);
      
      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        state.cart.items.push({ ...product, quantity: 1 });
      }
      
      state.cart.total = state.cart.items.reduce(
        (total, item) => total + item.price * item.quantity,
        0
      );
    },

    REMOVE_FROM_CART(state, productId: string) {
      state.cart.items = state.cart.items.filter(item => item.id !== productId);
      state.cart.total = state.cart.items.reduce(
        (total, item) => total + item.price * item.quantity,
        0
      );
    },

    SET_LOADING(state, loading: boolean) {
      state.loading = loading;
    },

    SET_ERROR(state, error: string | null) {
      state.error = error;
    },

    UPDATE_PROPS(state, props: any) {
      // 更新来自主应用的属性
      if (props.theme) {
        state.theme = props.theme;
      }
    },

    RESET_STATE(state) {
      // 重置状态到初始值
      state.theme = 'light';
      state.user = null;
      state.products = [];
      state.cart = { items: [], total: 0 };
      state.loading = false;
      state.error = null;
    }
  },

  actions: {
    async fetchProducts({ commit }) {
      commit('SET_LOADING', true);
      commit('SET_ERROR', null);

      try {
        const response = await fetch('/api/products');
        const products = await response.json();
        commit('SET_PRODUCTS', products);
      } catch (error) {
        commit('SET_ERROR', '获取商品列表失败');
        console.error('获取商品失败:', error);
      } finally {
        commit('SET_LOADING', false);
      }
    },

    addToCart({ commit, state }, product: any) {
      commit('ADD_TO_CART', product);
      
      // 通知主应用购物车变化
      if (window.__MICRO_CORE__) {
        window.__MICRO_CORE__.eventBus.emit('cart:item-added', {
          product,
          cartTotal: state.cart.total,
          itemCount: state.cart.items.length
        });
      }
    }
  },

  getters: {
    cartItemCount: (state) => state.cart.items.length,
    
    cartTotal: (state) => state.cart.total,
    
    isLoggedIn: (state) => !!state.user,
    
    userDisplayName: (state) => {
      return state.user ? `${state.user.firstName} ${state.user.lastName}` : '未登录';
    }
  }
});
```

### 6. Vue Router 配置

```typescript
// src/router/index.ts
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import Home from '../views/Home.vue';
import Products from '../views/Products.vue';
import Cart from '../views/Cart.vue';
import Profile from '../views/Profile.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      requiresAuth: false
    }
  },
  {
    path: '/products',
    name: 'Products',
    component: Products,
    meta: {
      title: '商品列表',
      requiresAuth: false
    }
  },
  {
    path: '/cart',
    name: 'Cart',
    component: Cart,
    meta: {
      title: '购物车',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人资料',
      requiresAuth: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  }
];

const router = createRouter({
  history: createWebHistory('/vue-app/'),
  routes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 更新页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Vue App`;
  }
  
  // 检查身份验证
  if (to.meta.requiresAuth) {
    // 检查用户是否登录
    const isLoggedIn = checkUserLogin();
    
    if (!isLoggedIn) {
      // 通知主应用需要登录
      if (window.__MICRO_CORE__) {
        window.__MICRO_CORE__.eventBus.emit('auth:login-required', {
          from: to.fullPath,
          app: 'vue-app'
        });
      }
      return;
    }
  }
  
  next();
});

function checkUserLogin(): boolean {
  // 检查全局状态中的用户信息
  if (window.__MICRO_CORE__) {
    const user = window.__MICRO_CORE__.globalState.get('currentUser');
    return !!user;
  }
  
  // 独立运行时检查本地存储
  const user = localStorage.getItem('user');
  return !!user;
}

export default router;
```

### 7. Vite 配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { microCoreVitePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    vue(),
    microCoreVitePlugin({
      name: 'vue-app',
      entry: './src/main.ts',
      exposes: {
        './App': './src/App.vue',
        './ProductList': './src/components/ProductList.vue'
      }
    })
  ],
  build: {
    rollupOptions: {
      external: ['vue', 'vue-router', 'vuex'],
      output: {
        globals: {
          'vue': 'Vue',
          'vue-router': 'VueRouter',
          'vuex': 'Vuex'
        }
      }
    }
  },
  server: {
    port: 3002,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
});
```

### 8. Package.json

```json
{
  "name": "vue3-micro-app",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc --noEmit && vite build",
    "preview": "vite preview",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "vuex": "^4.1.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@vitejs/plugin-vue": "^5.0.0",
    "@micro-core/adapter-vue3": "^1.0.0",
    "@micro-core/builder-vite": "^1.0.0",
    "typescript": "^5.0.0",
    "vite": "^7.0.6",
    "vue-tsc": "^1.8.0"
  }
}
```

## Vue 2 微应用

### 入口文件 (Vue 2)

```javascript
// src/main.js
import Vue from 'vue';
import VueRouter from 'vue-router';
import Vuex from 'vuex';
import App from './App.vue';
import router from './router';
import store from './store';

Vue.use(VueRouter);
Vue.use(Vuex);

let instance = null;

// 微前端生命周期函数
export async function mount(props) {
  const { container, basename, ...otherProps } = props;
  
  // 配置路由基础路径
  if (basename) {
    router.options.base = basename;
  }
  
  // 创建 Vue 实例
  instance = new Vue({
    router,
    store,
    data() {
      return {
        microProps: otherProps
      };
    },
    render: h => h(App)
  }).$mount(container ? container.querySelector('#app') : '#app');
  
  return instance;
}

export async function unmount() {
  if (instance) {
    instance.$destroy();
    instance.$el.innerHTML = '';
    instance = null;
  }
  
  // 重置路由
  router.replace('/');
  
  // 清理 store
  store.commit('RESET_STATE');
}

export async function update(props) {
  if (instance) {
    // 更新属性
    instance.microProps = { ...instance.microProps, ...props };
    
    // 触发响应式更新
    instance.$forceUpdate();
  }
}

// 独立运行模式
if (!window.__MICRO_CORE__) {
  instance = new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount('#app');
}
```

## 高级特性

### 1. 错误边界

```vue
<!-- src/components/ErrorBoundary.vue -->
<template>
  <div v-if="hasError" class="error-boundary">
    <h2>应用出现错误</h2>
    <p>{{ errorMessage }}</p>
    <button @click="retry">重试</button>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue';

const hasError = ref(false);
const errorMessage = ref('');

onErrorCaptured((error: Error) => {
  hasError.value = true;
  errorMessage.value = error.message;
  
  console.error('Vue 微应用错误:', error);
  
  // 向主应用报告错误
  if (window.__MICRO_CORE__) {
    window.__MICRO_CORE__.eventBus.emit('app:error', {
      name: 'vue-app',
      error: error.message,
      stack: error.stack
    });
  }
  
  return false; // 阻止错误继续传播
});

const retry = () => {
  hasError.value = false;
  errorMessage.value = '';
};
</script>

<style scoped>
.error-boundary {
  padding: 20px;
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  background-color: #ffe0e0;
  color: #d63031;
  text-align: center;
}

.error-boundary button {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: #d63031;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
```

### 2. 懒加载组件

```vue
<!-- src/views/Products.vue -->
<template>
  <div class="products-view">
    <h1>商品列表</h1>
    
    <Suspense>
      <template #default>
        <LazyProductList />
      </template>
      <template #fallback>
        <div class="loading">加载商品列表中...</div>
      </template>
    </Suspense>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue';

const LazyProductList = defineAsyncComponent(() => 
  import('../components/ProductList.vue')
);
</script>
```

### 3. 性能监控

```typescript
// src/utils/performance.ts
export const reportPerformance = () => {
  if (window.__MICRO_CORE__) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    window.__MICRO_CORE__.eventBus.emit('app:performance', {
      name: 'vue-app',
      loadTime: navigation.loadEventEnd - navigation.fetchStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
      firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
      framework: 'Vue 3'
    });
  }
};

// 在 main.ts 中调用
// reportPerformance();
```

## 运行示例

### 1. 启动主应用

```bash
cd main-app
pnpm dev
```

### 2. 启动 Vue 微应用

```bash
cd vue3-app
pnpm dev
```

### 3. 访问应用

在浏览器中访问 `http://localhost:3000`，然后导航到 `/vue-app` 路径。

## 最佳实践

1. **使用 Composition API** - Vue 3 推荐使用 Composition API
2. **错误边界** - 使用 onErrorCaptured 处理组件错误
3. **性能监控** - 监控应用性能指标
4. **懒加载** - 使用 defineAsyncComponent 实现组件懒加载
5. **状态管理** - 合理使用 Vuex/Pinia 和全局状态
6. **路由管理** - 正确配置 basename 和路由守卫
7. **样式隔离** - 使用 scoped 样式避免样式冲突

## 故障排除

### 常见问题

1. **路由不工作** - 检查 basename 配置和 history 模式
2. **样式冲突** - 使用 scoped 样式或 CSS Modules
3. **状态不同步** - 检查全局状态配置和事件监听
4. **组件不更新** - 确保响应式数据正确设置

### 调试技巧

1. 使用 Vue DevTools 调试组件状态
2. 检查 Micro-Core 开发者工具
3. 验证网络请求和资源加载
4. 使用 console.log 跟踪生命周期
