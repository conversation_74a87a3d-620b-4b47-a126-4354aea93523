# Svelte 微应用示例

本示例展示如何使用 Svelte 框架开发微前端应用，包括组件开发、状态管理、生命周期处理等关键特性。

## 🎯 示例概述

### 技术栈

- **框架**: Svelte 4.x
- **构建工具**: Vite 7.0.6
- **状态管理**: Svelte Store
- **路由**: Svelte SPA Router
- **样式**: CSS Modules + Sass

### 项目结构

```
svelte-micro-app/
├── src/
│   ├── components/           # 组件目录
│   │   ├── Header.svelte
│   │   ├── ProductCard.svelte
│   │   └── SearchBox.svelte
│   ├── stores/              # 状态管理
│   │   ├── products.js
│   │   └── cart.js
│   ├── routes/              # 路由组件
│   │   ├── Home.svelte
│   │   ├── Products.svelte
│   │   └── Cart.svelte
│   ├── utils/               # 工具函数
│   │   └── api.js
│   ├── App.svelte           # 根组件
│   └── main.js              # 入口文件
├── public/
│   └── index.html
├── vite.config.js           # Vite 配置
├── package.json
└── README.md
```

## 🚀 快速开始

### 1. 项目初始化

```bash
# 创建 Svelte 项目
npm create svelte@latest svelte-micro-app
cd svelte-micro-app

# 安装依赖
npm install

# 安装微前端适配器
npm install @micro-core/adapter-svelte
npm install @micro-core/core

# 安装其他依赖
npm install svelte-spa-router sass
```

### 2. Vite 配置

```javascript
// vite.config.js
import { defineConfig } from 'vite';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import { microCore } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    svelte(),
    microCore({
      name: 'svelte-micro-app',
      entry: './src/main.js',
      
      // 微前端配置
      microFrontend: {
        exposes: {
          './App': './src/App.svelte',
          './ProductCard': './src/components/ProductCard.svelte'
        },
        
        // 共享依赖
        shared: {
          'svelte': {
            singleton: true,
            requiredVersion: '^4.0.0'
          }
        }
      }
    })
  ],
  
  // 构建配置
  build: {
    lib: {
      entry: './src/main.js',
      name: 'SvelteMicroApp',
      formats: ['es', 'umd']
    },
    
    rollupOptions: {
      external: ['svelte'],
      output: {
        globals: {
          'svelte': 'Svelte'
        }
      }
    }
  },
  
  // 开发服务器配置
  server: {
    port: 3001,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
});
```

### 3. 微前端入口文件

```javascript
// src/main.js
import App from './App.svelte';
import { microCoreAdapter } from '@micro-core/adapter-svelte';

// 微前端生命周期函数
let app = null;

// 导出微前端生命周期
export async function bootstrap() {
  console.log('Svelte 微应用正在初始化...');
}

export async function mount(props = {}) {
  console.log('Svelte 微应用正在挂载...', props);
  
  const container = props.container || document.getElementById('app');
  
  app = new App({
    target: container,
    props: {
      // 传递微前端上下文
      microCore: props.microCore,
      globalState: props.globalState,
      eventBus: props.eventBus,
      
      // 传递其他属性
      ...props
    }
  });
  
  return app;
}

export async function unmount() {
  console.log('Svelte 微应用正在卸载...');
  
  if (app) {
    app.$destroy();
    app = null;
  }
}

export async function update(props) {
  console.log('Svelte 微应用正在更新...', props);
  
  if (app) {
    // 更新组件属性
    app.$set(props);
  }
}

// 独立运行模式
if (!window.__MICRO_CORE__) {
  mount();
}

// 注册微前端适配器
microCoreAdapter.register('svelte-micro-app', {
  bootstrap,
  mount,
  unmount,
  update
});
```

### 4. 根组件实现

```svelte
<!-- src/App.svelte -->
<script>
  import { onMount, onDestroy } from 'svelte';
  import { Router, link, push } from 'svelte-spa-router';
  import { wrap } from 'svelte-spa-router/wrap';
  
  // 导入路由组件
  import Home from './routes/Home.svelte';
  import Products from './routes/Products.svelte';
  import Cart from './routes/Cart.svelte';
  
  // 导入组件
  import Header from './components/Header.svelte';
  
  // 导入状态管理
  import { cartItems, cartTotal } from './stores/cart.js';
  import { products } from './stores/products.js';
  
  // 微前端属性
  export let microCore = null;
  export let globalState = null;
  export let eventBus = null;
  
  // 路由配置
  const routes = {
    '/': Home,
    '/products': Products,
    '/cart': Cart,
    
    // 嵌套路由
    '/products/:category': wrap({
      component: Products,
      props: { nested: true }
    }),
    
    // 通配符路由
    '*': Home
  };
  
  // 组件状态
  let isLoading = false;
  let error = null;
  
  // 生命周期
  onMount(async () => {
    console.log('Svelte 应用已挂载');
    
    // 初始化微前端通信
    if (microCore) {
      setupMicroFrontendCommunication();
    }
    
    // 加载初始数据
    await loadInitialData();
  });
  
  onDestroy(() => {
    console.log('Svelte 应用正在销毁');
    
    // 清理微前端通信
    if (eventBus) {
      eventBus.off('cart:update', handleCartUpdate);
      eventBus.off('user:login', handleUserLogin);
    }
  });
  
  // 设置微前端通信
  function setupMicroFrontendCommunication() {
    if (eventBus) {
      // 监听购物车更新事件
      eventBus.on('cart:update', handleCartUpdate);
      
      // 监听用户登录事件
      eventBus.on('user:login', handleUserLogin);
      
      // 监听全局状态变化
      if (globalState) {
        globalState.watch('theme', handleThemeChange);
        globalState.watch('user', handleUserChange);
      }
    }
  }
  
  // 加载初始数据
  async function loadInitialData() {
    try {
      isLoading = true;
      error = null;
      
      // 从全局状态获取数据
      if (globalState) {
        const userData = globalState.get('user');
        const themeData = globalState.get('theme');
        
        if (userData) {
          console.log('用户数据:', userData);
        }
        
        if (themeData) {
          applyTheme(themeData);
        }
      }
      
      // 加载产品数据
      await products.load();
      
    } catch (err) {
      error = err.message;
      console.error('数据加载失败:', err);
    } finally {
      isLoading = false;
    }
  }
  
  // 事件处理函数
  function handleCartUpdate(data) {
    console.log('收到购物车更新事件:', data);
    cartItems.update(items => {
      // 更新购物车数据
      return [...items, data.item];
    });
  }
  
  function handleUserLogin(userData) {
    console.log('用户登录:', userData);
    
    // 发送欢迎消息
    if (eventBus) {
      eventBus.emit('notification:show', {
        type: 'success',
        message: `欢迎回来，${userData.name}！`,
        duration: 3000
      });
    }
  }
  
  function handleThemeChange(theme) {
    applyTheme(theme);
  }
  
  function handleUserChange(user) {
    console.log('用户状态变化:', user);
  }
  
  // 应用主题
  function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
  }
  
  // 导航处理
  function handleNavigation(path) {
    push(path);
    
    // 通知其他微应用路由变化
    if (eventBus) {
      eventBus.emit('route:change', {
        from: 'svelte-micro-app',
        path: path,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // 添加到购物车
  function handleAddToCart(product) {
    cartItems.update(items => [...items, product]);
    
    // 通知其他微应用
    if (eventBus) {
      eventBus.emit('cart:add', {
        product,
        total: $cartTotal,
        count: $cartItems.length
      });
    }
  }
</script>

<div class="app" class:loading={isLoading}>
  <!-- 头部导航 -->
  <Header 
    {microCore}
    cartCount={$cartItems.length}
    cartTotal={$cartTotal}
    on:navigate={e => handleNavigation(e.detail.path)}
  />
  
  <!-- 错误提示 -->
  {#if error}
    <div class="error-banner">
      <span class="error-icon">⚠️</span>
      <span class="error-message">{error}</span>
      <button class="error-retry" on:click={loadInitialData}>
        重试
      </button>
    </div>
  {/if}
  
  <!-- 加载状态 -->
  {#if isLoading}
    <div class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载...</p>
    </div>
  {/if}
  
  <!-- 路由内容 -->
  <main class="main-content">
    <Router {routes} />
  </main>
  
  <!-- 全局通知 -->
  <div class="notifications" id="notifications">
    <!-- 通知消息将通过 JavaScript 动态添加 -->
  </div>
</div>

<style lang="scss">
  .app {
    min-height: 100vh;
    background: var(--bg-color, #f5f5f5);
    color: var(--text-color, #333);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    &.loading {
      pointer-events: none;
    }
  }
  
  .error-banner {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: 1rem 2rem;
    margin: 1rem;
    border-radius: 8px;
    
    .error-icon {
      font-size: 1.2rem;
    }
    
    .error-message {
      flex: 1;
    }
    
    .error-retry {
      background: #c33;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        background: #a22;
      }
    }
  }
  
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    p {
      margin-top: 1rem;
      color: #666;
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .main-content {
    min-height: calc(100vh - 80px);
    padding: 2rem;
  }
  
  .notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    pointer-events: none;
    
    :global(.notification) {
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      padding: 1rem 1.5rem;
      margin-bottom: 0.5rem;
      pointer-events: auto;
      animation: slideIn 0.3s ease;
      
      &.success {
        border-left: 4px solid #27ae60;
      }
      
      &.error {
        border-left: 4px solid #e74c3c;
      }
      
      &.info {
        border-left: 4px solid #3498db;
      }
    }
  }
  
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  // 主题样式
  :global([data-theme="dark"]) {
    --bg-color: #1a1a1a;
    --text-color: #e0e0e0;
    --card-bg: #2d2d2d;
    --border-color: #404040;
  }
  
  :global([data-theme="light"]) {
    --bg-color: #f5f5f5;
    --text-color: #333;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
  }
</style>
```

### 5. 状态管理

```javascript
// src/stores/products.js
import { writable, derived } from 'svelte/store';
import { api } from '../utils/api.js';

// 产品列表状态
export const products = writable([]);
export const loading = writable(false);
export const error = writable(null);

// 搜索和过滤状态
export const searchQuery = writable('');
export const selectedCategory = writable('all');

// 派生状态：过滤后的产品
export const filteredProducts = derived(
  [products, searchQuery, selectedCategory],
  ([$products, $searchQuery, $selectedCategory]) => {
    let filtered = $products;
    
    // 按分类过滤
    if ($selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === $selectedCategory);
    }
    
    // 按搜索关键词过滤
    if ($searchQuery) {
      const query = $searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query)
      );
    }
    
    return filtered;
  }
);

// 派生状态：产品分类
export const categories = derived(products, ($products) => {
  const categorySet = new Set($products.map(product => product.category));
  return ['all', ...Array.from(categorySet)];
});

// 操作函数
export const productActions = {
  // 加载产品数据
  async load() {
    loading.set(true);
    error.set(null);
    
    try {
      const data = await api.get('/products');
      products.set(data);
    } catch (err) {
      error.set(err.message);
      console.error('加载产品失败:', err);
    } finally {
      loading.set(false);
    }
  },
  
  // 搜索产品
  search(query) {
    searchQuery.set(query);
  },
  
  // 选择分类
  selectCategory(category) {
    selectedCategory.set(category);
  },
  
  // 获取单个产品
  async getById(id) {
    try {
      return await api.get(`/products/${id}`);
    } catch (err) {
      console.error('获取产品详情失败:', err);
      throw err;
    }
  },
  
  // 重置过滤条件
  resetFilters() {
    searchQuery.set('');
    selectedCategory.set('all');
  }
};
```

```javascript
// src/stores/cart.js
import { writable, derived } from 'svelte/store';

// 购物车状态
export const cartItems = writable([]);
export const isCartOpen = writable(false);

// 派生状态：购物车总价
export const cartTotal = derived(cartItems, ($cartItems) => {
  return $cartItems.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);
});

// 派生状态：商品总数
export const cartItemCount = derived(cartItems, ($cartItems) => {
  return $cartItems.reduce((count, item) => count + item.quantity, 0);
});

// 购物车操作
export const cartActions = {
  // 添加商品到购物车
  addItem(product) {
    cartItems.update(items => {
      const existingItem = items.find(item => item.id === product.id);
      
      if (existingItem) {
        // 如果商品已存在，增加数量
        return items.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        // 如果是新商品，添加到购物车
        return [...items, { ...product, quantity: 1 }];
      }
    });
  },
  
  // 移除商品
  removeItem(productId) {
    cartItems.update(items => items.filter(item => item.id !== productId));
  },
  
  // 更新商品数量
  updateQuantity(productId, quantity) {
    if (quantity <= 0) {
      this.removeItem(productId);
      return;
    }
    
    cartItems.update(items =>
      items.map(item =>
        item.id === productId
          ? { ...item, quantity }
          : item
      )
    );
  },
  
  // 清空购物车
  clear() {
    cartItems.set([]);
  },
  
  // 切换购物车显示状态
  toggleCart() {
    isCartOpen.update(open => !open);
  },
  
  // 打开购物车
  openCart() {
    isCartOpen.set(true);
  },
  
  // 关闭购物车
  closeCart() {
    isCartOpen.set(false);
  }
};
```

### 6. 组件示例

```svelte
<!-- src/components/ProductCard.svelte -->
<script>
  import { createEventDispatcher } from 'svelte';
  import { cartActions } from '../stores/cart.js';
  
  // 组件属性
  export let product;
  export let showAddButton = true;
  export let compact = false;
  
  // 事件分发器
  const dispatch = createEventDispatcher();
  
  // 组件状态
  let isAdding = false;
  let imageLoaded = false;
  
  // 添加到购物车
  async function handleAddToCart() {
    if (isAdding) return;
    
    isAdding = true;
    
    try {
      // 添加到购物车
      cartActions.addItem(product);
      
      // 分发事件
      dispatch('add-to-cart', { product });
      
      // 显示成功反馈
      showSuccessFeedback();
      
    } catch (error) {
      console.error('添加到购物车失败:', error);
      dispatch('error', { error: error.message });
    } finally {
      isAdding = false;
    }
  }
  
  // 显示成功反馈
  function showSuccessFeedback() {
    // 创建临时反馈元素
    const feedback = document.createElement('div');
    feedback.className = 'add-success-feedback';
    feedback.textContent = '已添加到购物车';
    
    // 添加到卡片中
    const card = document.querySelector(`[data-product-id="${product.id}"]`);
    if (card) {
      card.appendChild(feedback);
      
      // 2秒后移除
      setTimeout(() => {
        feedback.remove();
      }, 2000);
    }
  }
  
  // 处理图片加载
  function handleImageLoad() {
    imageLoaded = true;
  }
  
  // 处理图片错误
  function handleImageError() {
    console.warn(`产品图片加载失败: ${product.image}`);
  }
  
  // 格式化价格
  function formatPrice(price) {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(price);
  }
</script>

<div 
  class="product-card" 
  class:compact
  data-product-id={product.id}
>
  <!-- 产品图片 -->
  <div class="product-image">
    {#if !imageLoaded}
      <div class="image-placeholder">
        <div class="loading-spinner"></div>
      </div>
    {/if}
    
    <img
      src={product.image}
      alt={product.name}
      class="product-img"
      class:loaded={imageLoaded}
      on:load={handleImageLoad}
      on:error={handleImageError}
    />
    
    <!-- 折扣标签 -->
    {#if product.discount}
      <div class="discount-badge">
        -{product.discount}%
      </div>
    {/if}
  </div>
  
  <!-- 产品信息 -->
  <div class="product-info">
    <h3 class="product-name" title={product.name}>
      {product.name}
    </h3>
    
    {#if !compact}
      <p class="product-description">
        {product.description}
      </p>
    {/if}
    
    <!-- 价格信息 -->
    <div class="price-info">
      <span class="current-price">
        {formatPrice(product.price)}
      </span>
      
      {#if product.originalPrice && product.originalPrice > product.price}
        <span class="original-price">
          {formatPrice(product.originalPrice)}
        </span>
      {/if}
    </div>
    
    <!-- 评分 -->
    {#if product.rating}
      <div class="rating">
        {#each Array(5) as _, i}
          <span 
            class="star" 
            class:filled={i < Math.floor(product.rating)}
            class:half={i === Math.floor(product.rating) && product.rating % 1 >= 0.5}
          >
            ★
          </span>
        {/each}
        <span class="rating-text">
          ({product.reviewCount || 0})
        </span>
      </div>
    {/if}
    
    <!-- 操作按钮 -->
    {#if showAddButton}
      <div class="product-actions">
        <button
          class="add-to-cart-btn"
          class:loading={isAdding}
          disabled={isAdding || !product.inStock}
          on:click={handleAddToCart}
        >
          {#if isAdding}
            <span class="btn-spinner"></span>
            添加中...
          {:else if !product.inStock}
            缺货
          {:else}
            加入购物车
          {/if}
        </button>
        
        <button
          class="quick-view-btn"
          on:click={() => dispatch('quick-view', { product })}
        >
          快速查看
        </button>
      </div>
    {/if}
  </div>
</div>

<style lang="scss">
  .product-card {
    background: var(--card-bg, white);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    &.compact {
      .product-info {
        padding: 1rem;
      }
      
      .product-name {
        font-size: 1rem;
        margin-bottom: 0.5rem;
      }
    }
  }
  
  .product-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
    
    .image-placeholder {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
    }
    
    .product-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      &.loaded {
        opacity: 1;
      }
    }
    
    .discount-badge {
      position: absolute;
      top: 10px;
      right: 10px;
      background: #e74c3c;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
      font-weight: 600;
    }
  }
  
  .product-info {
    padding: 1.5rem;
  }
  
  .product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color, #333);
    margin-bottom: 0.5rem;
    line-height: 1.4;
    
    // 文本截断
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .product-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    
    // 文本截断
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .price-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    
    .current-price {
      font-size: 1.2rem;
      font-weight: 700;
      color: #e74c3c;
    }
    
    .original-price {
      font-size: 0.9rem;
      color: #999;
      text-decoration: line-through;
    }
  }
  
  .rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-bottom: 1rem;
    
    .star {
      color: #ddd;
      font-size: 1rem;
      
      &.filled {
        color: #ffc107;
      }
      
      &.half {
        background: linear-gradient(90deg, #ffc107 50%, #ddd 50%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    
    .rating-text {
      font-size: 0.8rem;
      color: #666;
      margin-left: 0.25rem;
    }
  }
  
  .product-actions {
    display: flex;
    gap: 0.5rem;
    
    button {
      flex: 1;
      padding: 0.75rem 1rem;
      border: none;
      border-radius: 6px;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    .add-to-cart-btn {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      
      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
      }
      
      &.loading {
        pointer-events: none;
      }
      
      .btn-spinner {
        display: inline-block;
        width: 12px;
        height: 12px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
      }
    }
    
    .quick-view-btn {
      background: transparent;
      color: var(--text-color, #333);
      border: 1px solid var(--border-color, #ddd);
      
      &:hover {
        background: var(--border-color, #ddd);
      }
    }
  }
  
  // 成功反馈样式
  :global(.add-success-feedback) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #27ae60;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 10;
    animation: fadeInOut 2s ease;
  }
  
  @keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    20%, 80% { opacity: 1; }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
</style>
```

这个 Svelte 微应用示例展示了：

- ✅ **完整的微前端生命周期管理**
- ✅ **Svelte Store 状态管理**
- ✅ **组件化开发**
- ✅ **微前端通信机制**
- ✅ **响应式设计**
- ✅ **错误处理和加载状态**
- ✅ **主题切换支持**
- ✅ **性能优化**

通过这个示例，开发者可以快速理解如何使用 Svelte 开发高质量的微前端应用。
