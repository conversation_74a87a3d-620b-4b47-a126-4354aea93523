# 多应用协作示例

本示例展示如何在 Micro-Core 架构中实现多个微前端应用的协作，包括数据共享、状态同步、事件通信和协调工作流程。

## 📋 目录

- [架构概述](#架构概述)
- [应用规划](#应用规划)
- [数据共享](#数据共享)
- [状态同步](#状态同步)
- [事件协调](#事件协调)
- [工作流程](#工作流程)
- [性能优化](#性能优化)
- [完整示例](#完整示例)

## 架构概述

多应用协作架构展示了一个完整的企业级应用场景，包含用户管理、订单处理、库存管理和数据分析等多个业务模块。

### 🏗️ 系统架构图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    多应用协作架构                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    主应用 (Shell)                           │ │
│  │  • 应用路由管理    • 全局状态管理    • 用户认证             │ │
│  │  • 主题配置        • 错误处理        • 性能监控             │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    通信层                                   │ │
│  │  • EventBus        • GlobalState     • DirectChannel       │ │
│  │  • 消息队列        • 状态同步        • 事件协调             │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐│
│  │ 用户管理应用 │  │ 订单管理应用 │  │ 库存管理应用 │  │ 数据分析应用 ││
│  │             │  │             │  │             │  │             ││
│  │ • 用户CRUD  │  │ • 订单处理  │  │ • 库存监控  │  │ • 报表生成  ││
│  │ • 权限管理  │  │ • 支付集成  │  │ • 采购管理  │  │ • 数据可视化││
│  │ • 个人资料  │  │ • 物流跟踪  │  │ • 预警系统  │  │ • 趋势分析  ││
│  │             │  │             │  │             │  │             ││
│  │ React 18    │  │ Vue 3       │  │ Angular 16  │  │ Solid.js    ││
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘│
│           │               │               │               │       │
│           └───────────────┼───────────────┼───────────────┘       │
│                           │               │                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    数据层                                   │ │
│  │  • 用户数据        • 订单数据        • 库存数据             │ │
│  │  • 权限数据        • 支付数据        • 分析数据             │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 应用规划

### 应用职责划分

```typescript
// 应用配置
const appConfig = {
  // 主应用 - Shell
  shell: {
    name: 'main-shell',
    responsibilities: [
      '应用路由管理',
      '全局状态管理',
      '用户认证授权',
      '主题和国际化',
      '错误处理',
      '性能监控'
    ],
    framework: 'React',
    port: 3000
  },

  // 用户管理应用
  userApp: {
    name: 'user-management',
    responsibilities: [
      '用户信息管理',
      '权限角色管理',
      '个人资料设置',
      '用户行为分析'
    ],
    framework: 'React',
    port: 3001,
    routes: ['/users', '/profile', '/permissions']
  },

  // 订单管理应用
  orderApp: {
    name: 'order-management',
    responsibilities: [
      '订单创建处理',
      '支付流程管理',
      '物流状态跟踪',
      '订单统计分析'
    ],
    framework: 'Vue',
    port: 3002,
    routes: ['/orders', '/payments', '/logistics']
  },

  // 库存管理应用
  inventoryApp: {
    name: 'inventory-management',
    responsibilities: [
      '库存实时监控',
      '采购需求管理',
      '库存预警系统',
      '供应商管理'
    ],
    framework: 'Angular',
    port: 3003,
    routes: ['/inventory', '/procurement', '/suppliers']
  },

  // 数据分析应用
  analyticsApp: {
    name: 'data-analytics',
    responsibilities: [
      '业务数据分析',
      '报表生成导出',
      '趋势预测分析',
      '可视化图表'
    ],
    framework: 'Solid.js',
    port: 3004,
    routes: ['/analytics', '/reports', '/charts']
  }
}
```

### 主应用配置

```typescript
// main-shell/src/micro-apps.ts
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  // 注册所有微应用
  apps: [
    {
      name: 'user-management',
      entry: 'http://localhost:3001',
      container: '#user-app',
      activeWhen: ['/users', '/profile', '/permissions']
    },
    {
      name: 'order-management',
      entry: 'http://localhost:3002',
      container: '#order-app',
      activeWhen: ['/orders', '/payments', '/logistics']
    },
    {
      name: 'inventory-management',
      entry: 'http://localhost:3003',
      container: '#inventory-app',
      activeWhen: ['/inventory', '/procurement', '/suppliers']
    },
    {
      name: 'data-analytics',
      entry: 'http://localhost:3004',
      container: '#analytics-app',
      activeWhen: ['/analytics', '/reports', '/charts']
    }
  ],

  // 全局配置
  globalConfig: {
    theme: 'light',
    language: 'zh-CN',
    apiBaseUrl: 'https://api.example.com'
  }
})

export default microCore
```

## 数据共享

### 全局状态管理

```typescript
// main-shell/src/store/globalStore.ts
import { createStore } from '@micro-core/core'

// 全局状态定义
interface GlobalState {
  // 用户信息
  user: {
    id: string
    name: string
    email: string
    role: string
    permissions: string[]
  } | null

  // 应用配置
  config: {
    theme: 'light' | 'dark'
    language: 'zh-CN' | 'en-US'
    currency: string
    timezone: string
  }

  // 业务数据
  business: {
    selectedCustomer: any
    currentOrder: any
    inventoryAlerts: any[]
    dashboardData: any
  }

  // 应用状态
  apps: {
    [appName: string]: {
      loaded: boolean
      active: boolean
      error: string | null
    }
  }
}

// 创建全局状态
export const globalStore = createStore<GlobalState>({
  user: null,
  config: {
    theme: 'light',
    language: 'zh-CN',
    currency: 'CNY',
    timezone: 'Asia/Shanghai'
  },
  business: {
    selectedCustomer: null,
    currentOrder: null,
    inventoryAlerts: [],
    dashboardData: null
  },
  apps: {}
})

// 状态操作方法
export const globalActions = {
  // 用户相关
  setUser(user: GlobalState['user']) {
    globalStore.setState('user', user)
  },

  updateUserPermissions(permissions: string[]) {
    globalStore.setState('user.permissions', permissions)
  },

  // 配置相关
  setTheme(theme: 'light' | 'dark') {
    globalStore.setState('config.theme', theme)
    // 通知所有应用主题变更
    microCore.eventBus.emit('theme:changed', theme)
  },

  setLanguage(language: 'zh-CN' | 'en-US') {
    globalStore.setState('config.language', language)
    microCore.eventBus.emit('language:changed', language)
  },

  // 业务数据相关
  setSelectedCustomer(customer: any) {
    globalStore.setState('business.selectedCustomer', customer)
    microCore.eventBus.emit('customer:selected', customer)
  },

  setCurrentOrder(order: any) {
    globalStore.setState('business.currentOrder', order)
    microCore.eventBus.emit('order:current', order)
  },

  addInventoryAlert(alert: any) {
    const alerts = globalStore.getState('business.inventoryAlerts')
    globalStore.setState('business.inventoryAlerts', [...alerts, alert])
    microCore.eventBus.emit('inventory:alert', alert)
  }
}
```

### 数据服务层

```typescript
// shared/services/dataService.ts
class DataService {
  private microCore: any

  constructor(microCore: any) {
    this.microCore = microCore
  }

  // 用户数据服务
  async getUsers(params?: any) {
    return await this.microCore.request('user-management', 'getUsers', params)
  }

  async createUser(userData: any) {
    const user = await this.microCore.request('user-management', 'createUser', userData)
    // 通知其他应用用户创建
    this.microCore.eventBus.emit('user:created', user)
    return user
  }

  // 订单数据服务
  async getOrders(params?: any) {
    return await this.microCore.request('order-management', 'getOrders', params)
  }

  async createOrder(orderData: any) {
    const order = await this.microCore.request('order-management', 'createOrder', orderData)
    
    // 通知库存应用更新库存
    this.microCore.eventBus.emit('order:created', {
      orderId: order.id,
      items: order.items
    })
    
    // 通知分析应用更新统计
    this.microCore.eventBus.emit('analytics:update', {
      type: 'order',
      data: order
    })
    
    return order
  }

  // 库存数据服务
  async getInventory(params?: any) {
    return await this.microCore.request('inventory-management', 'getInventory', params)
  }

  async updateInventory(inventoryData: any) {
    const result = await this.microCore.request('inventory-management', 'updateInventory', inventoryData)
    
    // 检查库存预警
    if (result.lowStock) {
      this.microCore.eventBus.emit('inventory:low-stock', result.lowStock)
    }
    
    return result
  }

  // 分析数据服务
  async getAnalytics(type: string, params?: any) {
    return await this.microCore.request('data-analytics', 'getAnalytics', { type, ...params })
  }
}

export default DataService
```

## 状态同步

### 状态同步机制

```typescript
// shared/sync/stateSyncManager.ts
class StateSyncManager {
  private microCore: any
  private syncRules: Map<string, SyncRule[]> = new Map()

  constructor(microCore: any) {
    this.microCore = microCore
    this.setupSyncListeners()
  }

  // 添加同步规则
  addSyncRule(sourceApp: string, rule: SyncRule) {
    if (!this.syncRules.has(sourceApp)) {
      this.syncRules.set(sourceApp, [])
    }
    this.syncRules.get(sourceApp)!.push(rule)
  }

  // 设置同步监听器
  private setupSyncListeners() {
    // 监听状态变更事件
    this.microCore.eventBus.on('state:changed', (event: StateChangeEvent) => {
      this.handleStateChange(event)
    })

    // 监听应用间数据请求
    this.microCore.eventBus.on('data:request', (event: DataRequestEvent) => {
      this.handleDataRequest(event)
    })
  }

  // 处理状态变更
  private handleStateChange(event: StateChangeEvent) {
    const rules = this.syncRules.get(event.sourceApp)
    if (!rules) return

    rules.forEach(rule => {
      if (rule.condition(event)) {
        rule.targetApps.forEach(targetApp => {
          this.microCore.sendMessage(targetApp, 'state:sync', {
            path: rule.targetPath,
            value: rule.transform ? rule.transform(event.value) : event.value
          })
        })
      }
    })
  }

  // 处理数据请求
  private handleDataRequest(event: DataRequestEvent) {
    const { sourceApp, dataType, params } = event
    
    // 根据数据类型路由到相应的应用
    const targetApp = this.getDataProvider(dataType)
    if (targetApp) {
      this.microCore.request(targetApp, `get${dataType}`, params)
        .then(data => {
          this.microCore.sendMessage(sourceApp, 'data:response', {
            requestId: event.requestId,
            data
          })
        })
    }
  }

  // 获取数据提供者
  private getDataProvider(dataType: string): string | null {
    const providers: Record<string, string> = {
      'users': 'user-management',
      'orders': 'order-management',
      'inventory': 'inventory-management',
      'analytics': 'data-analytics'
    }
    return providers[dataType] || null
  }
}

interface SyncRule {
  condition: (event: StateChangeEvent) => boolean
  targetApps: string[]
  targetPath: string
  transform?: (value: any) => any
}

interface StateChangeEvent {
  sourceApp: string
  path: string
  value: any
  timestamp: number
}

interface DataRequestEvent {
  sourceApp: string
  requestId: string
  dataType: string
  params: any
}

export default StateSyncManager
```

### 同步规则配置

```typescript
// main-shell/src/sync/syncRules.ts
import StateSyncManager from '../../../shared/sync/stateSyncManager'

export const setupSyncRules = (syncManager: StateSyncManager) => {
  // 用户信息同步规则
  syncManager.addSyncRule('user-management', {
    condition: (event) => event.path === 'currentUser',
    targetApps: ['order-management', 'inventory-management', 'data-analytics'],
    targetPath: 'user',
    transform: (user) => ({
      id: user.id,
      name: user.name,
      role: user.role,
      permissions: user.permissions
    })
  })

  // 订单状态同步规则
  syncManager.addSyncRule('order-management', {
    condition: (event) => event.path.startsWith('orders.') && event.path.endsWith('.status'),
    targetApps: ['inventory-management', 'data-analytics'],
    targetPath: 'orderStatus',
    transform: (status) => ({
      orderId: status.orderId,
      status: status.status,
      timestamp: Date.now()
    })
  })

  // 库存预警同步规则
  syncManager.addSyncRule('inventory-management', {
    condition: (event) => event.path === 'alerts',
    targetApps: ['order-management', 'data-analytics'],
    targetPath: 'inventoryAlerts'
  })

  // 主题配置同步规则
  syncManager.addSyncRule('main-shell', {
    condition: (event) => event.path === 'config.theme',
    targetApps: ['user-management', 'order-management', 'inventory-management', 'data-analytics'],
    targetPath: 'theme'
  })
}
```

## 事件协调

### 事件协调器

```typescript
// shared/coordination/eventCoordinator.ts
class EventCoordinator {
  private microCore: any
  private workflows: Map<string, Workflow> = new Map()
  private activeProcesses: Map<string, ProcessInstance> = new Map()

  constructor(microCore: any) {
    this.microCore = microCore
    this.setupEventListeners()
  }

  // 注册工作流
  registerWorkflow(name: string, workflow: Workflow) {
    this.workflows.set(name, workflow)
  }

  // 启动工作流
  async startWorkflow(workflowName: string, context: any): Promise<string> {
    const workflow = this.workflows.get(workflowName)
    if (!workflow) {
      throw new Error(`工作流 ${workflowName} 不存在`)
    }

    const processId = this.generateProcessId()
    const instance: ProcessInstance = {
      id: processId,
      workflowName,
      context,
      currentStep: 0,
      status: 'running',
      startTime: Date.now(),
      steps: []
    }

    this.activeProcesses.set(processId, instance)
    await this.executeNextStep(processId)
    
    return processId
  }

  // 执行下一步
  private async executeNextStep(processId: string) {
    const instance = this.activeProcesses.get(processId)
    if (!instance) return

    const workflow = this.workflows.get(instance.workflowName)!
    const step = workflow.steps[instance.currentStep]

    if (!step) {
      // 工作流完成
      instance.status = 'completed'
      instance.endTime = Date.now()
      this.microCore.eventBus.emit('workflow:completed', {
        processId,
        workflowName: instance.workflowName,
        context: instance.context
      })
      return
    }

    try {
      // 执行步骤
      const result = await this.executeStep(step, instance.context)
      
      instance.steps.push({
        stepName: step.name,
        result,
        timestamp: Date.now()
      })

      // 更新上下文
      if (step.updateContext) {
        instance.context = step.updateContext(instance.context, result)
      }

      // 检查条件
      if (step.condition && !step.condition(instance.context)) {
        instance.status = 'failed'
        instance.error = '步骤条件不满足'
        return
      }

      // 继续下一步
      instance.currentStep++
      await this.executeNextStep(processId)

    } catch (error) {
      instance.status = 'failed'
      instance.error = error.message
      this.microCore.eventBus.emit('workflow:failed', {
        processId,
        workflowName: instance.workflowName,
        error: error.message
      })
    }
  }

  // 执行单个步骤
  private async executeStep(step: WorkflowStep, context: any): Promise<any> {
    switch (step.type) {
      case 'request':
        return await this.microCore.request(step.targetApp, step.action, step.params(context))
      
      case 'event':
        this.microCore.eventBus.emit(step.eventName, step.eventData(context))
        return { success: true }
      
      case 'condition':
        return step.condition!(context)
      
      case 'parallel':
        const promises = step.parallelSteps!.map(s => this.executeStep(s, context))
        return await Promise.all(promises)
      
      default:
        throw new Error(`未知步骤类型: ${step.type}`)
    }
  }

  // 设置事件监听器
  private setupEventListeners() {
    // 监听工作流触发事件
    this.microCore.eventBus.on('workflow:trigger', async (event: WorkflowTriggerEvent) => {
      await this.startWorkflow(event.workflowName, event.context)
    })

    // 监听步骤完成事件
    this.microCore.eventBus.on('step:completed', (event: StepCompletedEvent) => {
      // 处理步骤完成逻辑
    })
  }

  private generateProcessId(): string {
    return `process_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

interface Workflow {
  name: string
  description: string
  steps: WorkflowStep[]
}

interface WorkflowStep {
  name: string
  type: 'request' | 'event' | 'condition' | 'parallel'
  targetApp?: string
  action?: string
  params?: (context: any) => any
  eventName?: string
  eventData?: (context: any) => any
  condition?: (context: any) => boolean
  updateContext?: (context: any, result: any) => any
  parallelSteps?: WorkflowStep[]
}

interface ProcessInstance {
  id: string
  workflowName: string
  context: any
  currentStep: number
  status: 'running' | 'completed' | 'failed'
  startTime: number
  endTime?: number
  error?: string
  steps: Array<{
    stepName: string
    result: any
    timestamp: number
  }>
}

export default EventCoordinator
```

## 工作流程

### 订单处理工作流

```typescript
// workflows/orderProcessingWorkflow.ts
import { Workflow } from '../shared/coordination/eventCoordinator'

export const orderProcessingWorkflow: Workflow = {
  name: 'order-processing',
  description: '订单处理工作流',
  steps: [
    {
      name: '验证用户权限',
      type: 'request',
      targetApp: 'user-management',
      action: 'validatePermission',
      params: (context) => ({
        userId: context.userId,
        permission: 'order:create'
      }),
      condition: (context) => context.hasPermission
    },
    
    {
      name: '检查库存',
      type: 'request',
      targetApp: 'inventory-management',
      action: 'checkStock',
      params: (context) => ({
        items: context.orderItems
      }),
      updateContext: (context, result) => ({
        ...context,
        stockAvailable: result.available,
        reservationId: result.reservationId
      })
    },
    
    {
      name: '创建订单',
      type: 'request',
      targetApp: 'order-management',
      action: 'createOrder',
      params: (context) => ({
        userId: context.userId,
        items: context.orderItems,
        reservationId: context.reservationId
      }),
      updateContext: (context, result) => ({
        ...context,
        orderId: result.orderId,
        orderNumber: result.orderNumber
      })
    },
    
    {
      name: '并行处理',
      type: 'parallel',
      parallelSteps: [
        {
          name: '更新库存',
          type: 'request',
          targetApp: 'inventory-management',
          action: 'updateStock',
          params: (context) => ({
            orderId: context.orderId,
            items: context.orderItems
          })
        },
        {
          name: '发送通知',
          type: 'event',
          eventName: 'order:created',
          eventData: (context) => ({
            orderId: context.orderId,
            userId: context.userId,
            orderNumber: context.orderNumber
          })
        },
        {
          name: '更新分析数据',
          type: 'request',
          targetApp: 'data-analytics',
          action: 'recordOrderEvent',
          params: (context) => ({
            eventType: 'order_created',
            orderId: context.orderId,
            value: context.orderTotal
          })
        }
      ]
    }
  ]
}
```

### 库存预警工作流

```typescript
// workflows/inventoryAlertWorkflow.ts
export const inventoryAlertWorkflow: Workflow = {
  name: 'inventory-alert',
  description: '库存预警工作流',
  steps: [
    {
      name: '检查库存水平',
      type: 'request',
      targetApp: 'inventory-management',
      action: 'checkLowStock',
      params: (context) => ({}),
      updateContext: (context, result) => ({
        ...context,
        lowStockItems: result.items
      })
    },
    
    {
      name: '生成预警报告',
      type: 'request',
      targetApp: 'data-analytics',
      action: 'generateAlertReport',
      params: (context) => ({
        items: context.lowStockItems
      }),
      updateContext: (context, result) => ({
        ...context,
        reportId: result.reportId
      })
    },
    
    {
      name: '通知相关人员',
      type: 'event',
      eventName: 'inventory:alert',
      eventData: (context) => ({
        items: context.lowStockItems,
        reportId: context.reportId,
        severity: 'high'
      })
    },
    
    {
      name: '自动采购建议',
      type: 'request',
      targetApp: 'inventory-management',
      action: 'generatePurchaseRecommendation',
      params: (context) => ({
        items: context.lowStockItems
      })
    }
  ]
}
```

## 性能优化

### 应用预加载

```typescript
// main-shell/src/preload/preloadManager.ts
class PreloadManager {
  private microCore: any
  private preloadRules: PreloadRule[] = []

  constructor(microCore: any) {
    this.microCore = microCore
    this.setupPreloadListeners()
  }

  // 添加预加载规则
  addPreloadRule(rule: PreloadRule) {
    this.preloadRules.push(rule)
  }

  // 检查并执行预加载
  async checkPreload(currentRoute: string, userRole: string) {
    for (const rule of this.preloadRules) {
      if (rule.condition(currentRoute, userRole)) {
        await this.preloadApp(rule.appName, rule.priority)
      }
    }
  }

  // 预加载应用
  private async preloadApp(appName: string, priority: number = 0) {
    try {
      await this.microCore.preloadApp(appName, { priority })
      console.log(`应用 ${appName} 预加载完成`)
    } catch (error) {
      console.error(`应用 ${appName} 预加载失败:`, error)
    }
  }

  // 设置预加载监听器
  private setupPreloadListeners() {
    // 监听路由变化
    this.microCore.eventBus.on('route:changed', (route: string) => {
      const user = this.microCore.getCurrentUser()
      this.checkPreload(route, user?.role || 'guest')
    })

    // 监听用户登录
    this.microCore.eventBus.on('user:login', (user: any) => {
      this.checkPreload(window.location.pathname, user.role)
    })
  }
}

interface PreloadRule {
  appName: string
  condition: (route: string, userRole: string) => boolean
  priority: number
}

// 预加载规则配置
export const preloadRules: PreloadRule[] = [
  {
    appName: 'order-management',
    condition: (route, role) => route.startsWith('/users') && ['admin', 'manager'].includes(role),
    priority: 1
  },
  {
    appName: 'data-analytics',
    condition: (route, role) => route.startsWith('/orders') && role === 'admin',
    priority: 2
  },
  {
    appName: 'inventory-management',
    condition: (route, role) => route.startsWith('/orders') && ['admin', 'manager'].includes(role),
    priority: 1
  }
]
```

### 资源共享优化

```typescript
// shared/optimization/resourceManager.ts
class ResourceManager {
  private sharedResources: Map<string, any> = new Map()
  private resourceCache: Map<string, any> = new Map()

  // 注册共享资源
  registerSharedResource(name: string, resource: any) {
    this.sharedResources.set(name, resource)
  }

  // 获取共享资源
  getSharedResource(name: string): any {
    return this.sharedResources.get(name)
  }

  // 缓存资源
  cacheResource(key: string, resource: any, ttl: number = 300000) {
    this.resourceCache.set(key, {
      data: resource,
      expiry: Date.now() + ttl
    })
  }

  // 获取缓存资源
  getCachedResource(key: string): any {
    const cached = this.resourceCache.get(key)
    if (!cached) return null
    
    if (Date.now() > cached.expiry) {
      this.resourceCache.delete(key)
      return null
    }
    
    return cached.data
  }

  // 清理过期缓存
  cleanExpiredCache() {
    const now = Date.now()
    for (const [key, cached] of this.resourceCache.entries()) {
      if (now > cached.expiry) {
        this.resourceCache.delete(key)
      }
    }
  }
}

export default ResourceManager
```

## 完整示例

### 主应用入口

```typescript
// main-shell/src/App.tsx
import React, { useEffect, useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import microCore from './micro-apps'
import { globalStore, globalActions } from './store/globalStore'
import StateSyncManager from '../shared/sync/stateSyncManager'
import EventCoordinator from '../shared/coordination/eventCoordinator'
import { setupSyncRules } from './sync/syncRules'
import { orderProcessingWorkflow, inventoryAlertWorkflow } from './workflows'
import Layout from './components/Layout'
import Login from './components/Login'

const App: React.FC = () => {
  const [initialized, setInitialized] = useState(false)
  const [user, setUser] = useState(null)

  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    try {
      // 初始化状态同步管理器
      const syncManager = new StateSyncManager(microCore)
      setupSyncRules(syncManager)

      // 初始化事件协调器
      const eventCoordinator = new EventCoordinator(microCore)
      eventCoordinator.registerWorkflow('order-processing', orderProcessingWorkflow)
      eventCoordinator.registerWorkflow('inventory-alert', inventoryAlertWorkflow)

      // 启动微前端系统
      await microCore.start()

      // 检查用户登录状态
      const currentUser = await microCore.getCurrentUser()
      if (currentUser) {
        setUser(currentUser)
        globalActions.setUser(currentUser)
      }

      setInitialized(true)
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }

  const handleLogin = async (credentials: any) => {
    try {
      const user = await microCore.login(credentials)
      setUser(user)
      globalActions.setUser(user)
    } catch (error) {
      console.error('登录失败:', error)
    }
  }

  const handleLogout = async () => {
    await microCore.logout()
    setUser(null)
    globalActions.setUser(null)
  }

  if (!initialized) {
    return <div className="loading">系统初始化中...</div>
  }

  if (!user) {
    return <Login onLogin={handleLogin} />
  }

  return (
    <Router>
      <Layout user={user} onLogout={handleLogout}>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<div id="dashboard-app" />} />
          <Route path="/users/*" element={<div id="user-app" />} />
          <Route path="/orders/*" element={<div id="order-app" />} />
          <Route path="/inventory/*" element={<div id="inventory-app" />} />
          <Route path="/analytics/*" element={<div id="analytics-app" />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default App
```

### 布局组件

```typescript
// main-shell/src/components/Layout.tsx
import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { globalStore } from '../store/globalStore'

interface LayoutProps {
  user: any
  onLogout: () => void
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ user, onLogout, children }) => {
  const location = useLocation()
  const [sidebarOpen, setSidebarOpen] = useState(true)

  const navigation = [
    { name: '仪表板', path: '/dashboard', icon: '📊' },
    { name: '用户管理', path: '/users', icon: '👥' },
    { name: '订单管理', path: '/orders', icon: '📦' },
    { name: '库存管理', path: '/inventory', icon: '📋' },
    { name: '数据分析', path: '/analytics', icon: '📈' }
  ]

  return (
    <div className="app-layout">
      {/* 顶部导航栏 */}
      <header className="app-header">
        <div className="header-left">
          <button 
            className="sidebar-toggle"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            ☰
          </button>
          <h1>企业管理系统</h1>
        </div>
        
        <div className="header-right">
          <span>欢迎，{user.name}</span>
          <button onClick={onLogout} className="logout-btn">
            退出登录
          </button>
        </div>
      </header>

      <div className="app-body">
        {/* 侧边栏 */}
        <aside className={`sidebar ${sidebarOpen ? 'open' : 'closed'}`}>
          <nav className="sidebar-nav">
            {navigation.map(item => (
              <Link
                key={item.path}
                to={item.path}
                className={`nav-item ${location.pathname.startsWith(item.path) ? 'active' : ''}`}
              >
                <span className="nav-icon">{item.icon}</span>
                <span className="nav-text">{item.name}</span>
              </Link>
            ))}
          </nav>
        </aside>

        {/* 主内容区 */}
        <main className="main-content">
          {children}
        </main>
      </div>
    </div>
  )
}

export default Layout
```

### 用户管理应用示例

```typescript
// user-management/src/App.tsx
import React, { useEffect, useState } from 'react'
import { useMicroCore } from '@micro-core/adapter-react'
import UserList from './components/UserList'
import UserForm from './components/UserForm'

const UserApp: React.FC = () => {
  const microCore = useMicroCore()
  const [users, setUsers] = useState([])
  const [selectedUser, setSelectedUser] = useState(null)
  const [showForm, setShowForm] = useState(false)

  useEffect(() => {
    setupEventListeners()
    loadUsers()
  }, [])

  const setupEventListeners = () => {
    // 监听主题变化
    microCore.eventBus.on('theme:changed', (theme) => {
      document.body.className = `theme-${theme}`
    })

    // 监听用户选择事件
    microCore.eventBus.on('user:select', (user) => {
      setSelectedUser(user)
    })
  }

  const loadUsers = async () => {
    try {
      const userData = await microCore.request('user-service', 'getUsers')
      setUsers(userData)
    } catch (error) {
      console.error('加载用户失败:', error)
    }
  }

  const handleUserCreate = async (userData) => {
    try {
      const newUser = await microCore.request('user-service', 'createUser', userData)
      setUsers(prev => [...prev, newUser])
      
      // 通知其他应用
      microCore.eventBus.emit('user:created', newUser)
      
      setShowForm(false)
    } catch (error) {
      console.error('创建用户失败:', error)
    }
  }

  const handleUserUpdate = async (userId, userData) => {
    try {
      const updatedUser = await microCore.request('user-service', 'updateUser', { id: userId, ...userData })
      setUsers(prev => prev.map(u => u.id === userId ? updatedUser : u))
      
      // 通知其他应用
      microCore.eventBus.emit('user:updated', updatedUser)
      
      setSelectedUser(null)
      setShowForm(false)
    } catch (error) {
      console.error('更新用户失败:', error)
    }
  }

  return (
    <div className="user-app">
      <div className="app-header">
        <h2>用户管理</h2>
        <button 
          className="btn-primary"
          onClick={() => setShowForm(true)}
        >
          添加用户
        </button>
      </div>

      <div className="app-content">
        {showForm ? (
          <UserForm
            user={selectedUser}
            onSave={selectedUser ? handleUserUpdate : handleUserCreate}
            onCancel={() => {
              setShowForm(false)
              setSelectedUser(null)
            }}
          />
        ) : (
          <UserList
            users={users}
            onUserSelect={(user) => {
              setSelectedUser(user)
              setShowForm(true)
            }}
          />
        )}
      </div>
    </div>
  )
}

export default UserApp
```

### 样式文件

```css
/* main-shell/src/styles/app.css */
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logout-btn {
  padding: 0.5rem 1rem;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.app-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background: #f8f9fa;
  border-right: 1px solid #e5e5e5;
  transition: width 0.3s ease;
}

.sidebar.closed {
  width: 60px;
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s;
}

.nav-item:hover {
  background-color: #e9ecef;
}

.nav-item.active {
  background-color: #007bff;
  color: white;
}

.nav-icon {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.sidebar.closed .nav-text {
  display: none;
}

.main-content {
  flex: 1;
  overflow: auto;
  padding: 2rem;
}

/* 微应用容器样式 */
#user-app,
#order-app,
#inventory-app,
#analytics-app {
  min-height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: absolute;
    left: -250px;
    height: 100%;
    z-index: 1000;
  }
  
  .sidebar.open {
    left: 0;
  }
  
  .main-content {
    padding: 1rem;
  }
}

/* 主题支持 */
.theme-dark {
  background-color: #1a1a1a;
  color: #fff;
}

.theme-dark .app-header {
  background: #2d2d2d;
  border-bottom-color: #404040;
}

.theme-dark .sidebar {
  background: #2d2d2d;
  border-right-color: #404040;
}

.theme-dark .nav-item {
  color: #fff;
}

.theme-dark .nav-item:hover {
  background-color: #404040;
}
```

## 运行示例

### 启动所有应用

```bash
# 启动主应用
cd main-shell
npm install && npm start

# 启动用户管理应用
cd user-management
npm install && npm start

# 启动订单管理应用
cd order-management
npm install && npm start

# 启动库存管理应用
cd inventory-management
npm install && npm start

# 启动数据分析应用
cd data-analytics
npm install && npm start
```

### Docker 部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  main-shell:
    build: ./main-shell
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
  
  user-management:
    build: ./user-management
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
  
  order-management:
    build: ./order-management
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
  
  inventory-management:
    build: ./inventory-management
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
  
  data-analytics:
    build: ./data-analytics
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=production
```

## 相关链接

- [应用间通信示例](/examples/advanced/communication)
- [共享状态示例](/examples/advanced/shared-state)
- [动态路由示例](/examples/advanced/dynamic-routing)
- [性能优化指南](/guide/best-practices/performance)
- [架构设计指南](/guide/best-practices/architecture)

---

*最后更新: 2024-07-27*
