# qiankun API 对照表

本文档提供了从 qiankun 迁移到 Micro-Core 的详细 API 对照表，帮助开发者快速完成迁移。

## 主应用 API 对照

### 应用注册

#### qiankun

```javascript
import { registerMicroApps, start } from 'qiankun';

registerMicroApps([
  {
    name: 'reactApp',
    entry: '//localhost:3000',
    container: '#subapp-viewport',
    activeRule: '/react',
    props: {
      msg: 'hello'
    }
  }
]);

start();
```

#### Micro-Core

```typescript
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel();

kernel.registerApplication({
  name: 'reactApp',
  entry: 'http://localhost:3000',
  container: '#subapp-viewport',
  activeWhen: '/react',
  props: {
    msg: 'hello'
  }
});

kernel.start();
```

### 应用生命周期

#### qiankun

```javascript
import { registerMicroApps } from 'qiankun';

registerMicroApps([
  {
    name: 'app1',
    entry: '//localhost:3000',
    container: '#container',
    activeRule: '/app1',
    loader: (loading) => {
      console.log('loading', loading);
    }
  }
]);
```

#### Micro-Core

```typescript
kernel.registerApplication({
  name: 'app1',
  entry: 'http://localhost:3000',
  container: '#container',
  activeWhen: '/app1',
  lifecycle: {
    beforeLoad: async (app) => {
      console.log('loading', true);
    },
    afterLoad: async (app) => {
      console.log('loading', false);
    },
    beforeMount: async (app) => {
      console.log('mounting', app.name);
    },
    afterMount: async (app) => {
      console.log('mounted', app.name);
    }
  }
});
```

### 手动加载应用

#### qiankun

```javascript
import { loadMicroApp } from 'qiankun';

const microApp = loadMicroApp({
  name: 'manualApp',
  entry: '//localhost:3000',
  container: '#manual-container'
});

// 卸载
microApp.unmount();
```

#### Micro-Core

```typescript
// 注册应用
kernel.registerApplication({
  name: 'manualApp',
  entry: 'http://localhost:3000',
  container: '#manual-container',
  activeWhen: () => false // 不自动激活
});

// 手动加载和挂载
await kernel.loadApplication('manualApp');
await kernel.mountApplication('manualApp');

// 卸载
await kernel.unmountApplication('manualApp');
```

### 预加载

#### qiankun

```javascript
import { start } from 'qiankun';

start({
  prefetch: 'all' // 或 true, false, 'popstate', 或自定义函数
});
```

#### Micro-Core

```typescript
// 全局预加载配置
kernel.start({
  prefetchAll: true
});

// 或者单独配置应用预加载
kernel.registerApplication({
  name: 'app1',
  entry: 'http://localhost:3000',
  container: '#container',
  activeWhen: '/app1',
  prefetch: {
    strategy: 'idle', // 'immediate' | 'idle' | 'visible'
    priority: 'high'  // 'low' | 'normal' | 'high'
  }
});

// 手动预加载
await kernel.prefetchApplication('app1');
```

### 沙箱配置

#### qiankun

```javascript
import { start } from 'qiankun';

start({
  sandbox: {
    strictStyleIsolation: true,
    experimentalStyleIsolation: true
  }
});
```

#### Micro-Core

```typescript
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';

kernel.use(ProxySandboxPlugin, {
  strict: true,
  styleIsolation: true
});

// 或者为特定应用配置沙箱
kernel.registerApplication({
  name: 'app1',
  entry: 'http://localhost:3000',
  container: '#container',
  activeWhen: '/app1',
  sandbox: {
    type: 'proxy',
    strict: true,
    styleIsolation: true
  }
});
```

## 微应用 API 对照

### 生命周期函数

#### qiankun

```javascript
// 微应用入口文件
let instance = null;

export async function bootstrap() {
  console.log('react app bootstraped');
}

export async function mount(props) {
  console.log('props from main framework', props);
  instance = ReactDOM.render(<App />, props.container ? props.container.querySelector('#root') : document.querySelector('#root'));
}

export async function unmount(props) {
  ReactDOM.unmountComponentAtNode(props.container ? props.container.querySelector('#root') : document.querySelector('#root'));
  instance = null;
}

// 可选的 update 钩子
export async function update(props) {
  console.log('update props', props);
}
```

#### Micro-Core

```typescript
// 微应用入口文件
import { ReactAdapter } from '@micro-core/adapter-react';

let instance = null;

export const bootstrap = ReactAdapter.bootstrap(async () => {
  console.log('react app bootstraped');
});

export const mount = ReactAdapter.mount(async (props) => {
  console.log('props from main framework', props);
  const { container } = props;
  instance = ReactDOM.render(<App />, container);
  return instance;
});

export const unmount = ReactAdapter.unmount(async () => {
  if (instance) {
    ReactDOM.unmountComponentAtNode(instance.container);
    instance = null;
  }
});

export const update = ReactAdapter.update(async (props) => {
  console.log('update props', props);
});
```

### 获取主应用传递的 props

#### qiankun

```javascript
// 在 mount 函数中获取
export async function mount(props) {
  const { msg, globalState, onGlobalStateChange, setGlobalState } = props;
  console.log('msg from main app:', msg);
}
```

#### Micro-Core

```typescript
// 在 mount 函数中获取
export const mount = ReactAdapter.mount(async (props) => {
  const { msg, ...otherProps } = props;
  console.log('msg from main app:', msg);
  
  // 或者在组件中获取
  const App = () => {
    const microProps = useMicroProps(); // 自定义 hook
    return <div>{microProps.msg}</div>;
  };
});
```

## 通信 API 对照

### 全局状态管理

#### qiankun

```javascript
// 主应用
import { initGlobalState } from 'qiankun';

const actions = initGlobalState({
  user: 'qiankun',
  num: 0
});

actions.onGlobalStateChange((state, prev) => {
  console.log(state, prev);
});

actions.setGlobalState({
  user: 'new user'
});

// 微应用
export async function mount(props) {
  const { onGlobalStateChange, setGlobalState } = props;
  
  onGlobalStateChange((state, prev) => {
    console.log(state, prev);
  });
  
  setGlobalState({
    num: 1
  });
}
```

#### Micro-Core

```typescript
// 主应用
import { GlobalState } from '@micro-core/core';

// 设置初始状态
GlobalState.set('user', 'micro-core');
GlobalState.set('num', 0);

// 监听状态变化
GlobalState.watch('user', (newUser, oldUser) => {
  console.log(newUser, oldUser);
});

// 更新状态
GlobalState.set('user', 'new user');

// 微应用
import { GlobalState } from '@micro-core/core';

export const mount = ReactAdapter.mount(async (props) => {
  // 监听状态变化
  GlobalState.watch('user', (newUser, oldUser) => {
    console.log(newUser, oldUser);
  });
  
  // 更新状态
  GlobalState.set('num', 1);
});
```

### 事件通信

#### qiankun

```javascript
// qiankun 没有内置事件系统，需要自己实现
// 通常通过全局状态或自定义事件总线实现

// 自定义事件总线
class EventBus {
  constructor() {
    this.events = {};
  }
  
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }
  
  emit(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  }
}

const eventBus = new EventBus();
window.__GLOBAL_EVENT_BUS__ = eventBus;
```

#### Micro-Core

```typescript
// Micro-Core 内置事件系统
import { EventBus } from '@micro-core/core';

// 主应用
EventBus.emit('user-login', { userId: '123' });

EventBus.on('user-logout', (userData) => {
  console.log('用户登出:', userData);
});

// 微应用
import { EventBus } from '@micro-core/core';

EventBus.on('user-login', (userData) => {
  console.log('用户登录:', userData);
});

EventBus.emit('user-logout', { userId: '123' });
```

## 路由 API 对照

### 路由配置

#### qiankun

```javascript
// qiankun 使用 activeRule 进行路由匹配
registerMicroApps([
  {
    name: 'app1',
    entry: '//localhost:3000',
    container: '#container',
    activeRule: '/app1'
  },
  {
    name: 'app2',
    entry: '//localhost:3001',
    container: '#container',
    activeRule: (location) => location.pathname.startsWith('/app2')
  }
]);
```

#### Micro-Core

```typescript
// Micro-Core 使用 activeWhen 进行路由匹配
kernel.registerApplication({
  name: 'app1',
  entry: 'http://localhost:3000',
  container: '#container',
  activeWhen: '/app1'
});

kernel.registerApplication({
  name: 'app2',
  entry: 'http://localhost:3001',
  container: '#container',
  activeWhen: (location) => location.pathname.startsWith('/app2')
});
```

### 路由导航

#### qiankun

```javascript
// qiankun 没有内置路由导航，需要使用浏览器原生 API
history.pushState(null, '', '/app1/page1');

// 或者使用主应用的路由库
import { history } from 'umi'; // 如果使用 umi
history.push('/app1/page1');
```

#### Micro-Core

```typescript
// Micro-Core 提供统一的路由 API
import { Router } from '@micro-core/plugin-router';

const router = Router.getInstance();

// 导航到指定路由
await router.push('/app1/page1');

// 替换当前路由
await router.replace('/app1/page2');

// 历史记录操作
router.back();
router.forward();
router.go(-2);
```

## 构建配置对照

### Webpack 配置

#### qiankun

```javascript
// webpack.config.js
const { name } = require('./package');

module.exports = {
  output: {
    library: `${name}-[name]`,
    libraryTarget: 'umd',
    jsonpFunction: `webpackJsonp_${name}`,
  },
  externals: {
    react: 'React',
    'react-dom': 'ReactDOM'
  }
};
```

#### Micro-Core

```typescript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/builder-webpack');

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'my-app',
      entry: './src/index.js',
      exposes: {
        './App': './src/App.jsx'
      }
    })
  ],
  externals: {
    react: 'React',
    'react-dom': 'ReactDOM'
  }
};
```

### Vite 配置

#### qiankun

```javascript
// vite.config.js - qiankun 需要额外配置
import { defineConfig } from 'vite';
import { qiankun, qiankunWindow } from 'vite-plugin-qiankun';

export default defineConfig({
  plugins: [
    qiankun('my-app', {
      useDevMode: true
    })
  ],
  define: {
    __QIANKUN_DEVELOPMENT__: true,
    __QIANKUN_PUBLIC_PATH__: '/'
  }
});
```

#### Micro-Core

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { microCoreVitePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    microCoreVitePlugin({
      name: 'my-app',
      entry: './src/main.ts',
      exposes: {
        './App': './src/App.tsx'
      }
    })
  ]
});
```

## 迁移检查清单

### 主应用迁移

- [ ] 替换 `registerMicroApps` 为 `kernel.registerApplication`
- [ ] 替换 `start()` 为 `kernel.start()`
- [ ] 替换 `loadMicroApp` 为手动加载 API
- [ ] 迁移全局状态管理到 `GlobalState`
- [ ] 迁移事件通信到 `EventBus`
- [ ] 更新沙箱配置
- [ ] 更新预加载配置

### 微应用迁移

- [ ] 使用适配器包装生命周期函数
- [ ] 更新 props 获取方式
- [ ] 迁移全局状态使用
- [ ] 迁移事件通信
- [ ] 更新构建配置
- [ ] 测试独立运行模式

### 构建配置迁移

- [ ] 更新 Webpack 配置
- [ ] 更新 Vite 配置
- [ ] 更新 package.json 依赖
- [ ] 更新部署脚本

## 常见迁移问题

### 1. 生命周期函数不执行

**qiankun 问题:**
```javascript
// 忘记导出生命周期函数
function bootstrap() {} // 没有 export
```

**Micro-Core 解决:**
```typescript
// 确保正确导出
export const bootstrap = ReactAdapter.bootstrap(async () => {});
export const mount = ReactAdapter.mount(async (props) => {});
export const unmount = ReactAdapter.unmount(async () => {});
```

### 2. 样式隔离问题

**qiankun 问题:**
```javascript
// 样式冲突
start({
  sandbox: {
    strictStyleIsolation: true // 可能导致样式丢失
  }
});
```

**Micro-Core 解决:**
```typescript
// 更灵活的样式隔离
kernel.use(ProxySandboxPlugin, {
  styleIsolation: 'scoped', // 'none' | 'scoped' | 'shadow'
  cssPrefix: 'micro-app-'
});
```

### 3. 路由冲突

**qiankun 问题:**
```javascript
// 路由基础路径配置复杂
const router = new VueRouter({
  base: window.__QIANKUN_DEVELOPMENT__ ? '/' : '/app1/',
  mode: 'history',
  routes
});
```

**Micro-Core 解决:**
```typescript
// 自动处理路由基础路径
export const mount = VueAdapter.mount(async (props) => {
  const { basename } = props;
  
  const router = new VueRouter({
    base: basename || '/',
    mode: 'history',
    routes
  });
});
```

## 性能对比

| 特性 | qiankun | Micro-Core | 优势 |
|------|---------|------------|------|
| 应用加载速度 | 中等 | 快 | 优化的加载策略 |
| 内存占用 | 中等 | 低 | 更好的资源管理 |
| 沙箱性能 | 中等 | 高 | 多种沙箱策略 |
| 通信性能 | 低 | 高 | 内置高效通信系统 |
| 开发体验 | 中等 | 优秀 | 更好的 TypeScript 支持 |
| 生态系统 | 成熟 | 新兴 | 现代化架构设计 |

## 总结

Micro-Core 相比 qiankun 提供了：

1. **更现代的 API 设计** - 基于 TypeScript，类型安全
2. **更强大的通信系统** - 内置事件总线和状态管理
3. **更灵活的沙箱策略** - 多种沙箱类型可选
4. **更好的开发体验** - 完善的开发工具和调试支持
5. **更高的性能** - 优化的加载和运行时性能

迁移过程中建议：
1. 先迁移一个简单的微应用进行测试
2. 逐步迁移其他微应用
3. 充分测试各种场景
4. 利用 Micro-Core 的新特性优化应用架构
