# qiankun 通信迁移指南

本指南详细说明如何将 qiankun 的应用间通信机制迁移到 Micro-Core。

## 📋 目录

- [通信机制对比](#通信机制对比)
- [Actions 通信迁移](#actions-通信迁移)
- [全局状态迁移](#全局状态迁移)
- [事件通信迁移](#事件通信迁移)
- [Props 传递迁移](#props-传递迁移)
- [完整迁移示例](#完整迁移示例)
- [最佳实践](#最佳实践)

## 通信机制对比

### qiankun 通信方式

```javascript
// qiankun 主要通信方式
const qiankunCommunication = {
  // 1. Actions 通信
  actions: {
    onGlobalStateChange: (callback) => {},
    setGlobalState: (state) => {},
    offGlobalStateChange: () => {}
  },
  
  // 2. Props 传递
  props: {
    customProps: {},
    container: HTMLElement
  },
  
  // 3. 自定义事件
  events: {
    window: 'postMessage',
    custom: 'CustomEvent'
  }
}
```

### Micro-Core 通信方式

```typescript
// Micro-Core 统一通信系统
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  communication: {
    // 1. EventBus - 高性能事件总线
    eventBus: {
      namespace: true,
      middleware: true,
      async: true
    },
    
    // 2. GlobalState - 响应式全局状态
    globalState: {
      reactive: true,
      persistent: true,
      computed: true
    },
    
    // 3. 直接通信
    direct: {
      appToApp: true,
      broadcast: true,
      unicast: true
    }
  }
})
```

## Actions 通信迁移

### qiankun Actions 模式

```javascript
// qiankun 主应用
import { initGlobalState } from 'qiankun'

// 初始化全局状态
const actions = initGlobalState({
  user: { name: 'admin', role: 'admin' },
  theme: 'light'
})

// 监听状态变化
actions.onGlobalStateChange((state, prev) => {
  console.log('状态变化:', state, prev)
})

// 设置状态
actions.setGlobalState({
  user: { name: 'user1', role: 'user' }
})

// qiankun 子应用
export async function mount(props) {
  const { onGlobalStateChange, setGlobalState } = props
  
  // 监听全局状态
  onGlobalStateChange((state, prev) => {
    console.log('子应用接收状态:', state)
  })
  
  // 修改全局状态
  setGlobalState({
    theme: 'dark'
  })
}
```

### Micro-Core GlobalState 迁移

```typescript
// Micro-Core 主应用
import { MicroCore, GlobalState } from '@micro-core/core'

// 创建全局状态
const globalState = new GlobalState({
  user: { name: 'admin', role: 'admin' },
  theme: 'light'
})

const microCore = new MicroCore({
  globalState,
  apps: [
    {
      name: 'sub-app',
      entry: 'http://localhost:3001',
      activeWhen: '/sub-app'
    }
  ]
})

// 监听状态变化
globalState.subscribe((state, prev) => {
  console.log('状态变化:', state, prev)
})

// 设置状态
globalState.setState({
  user: { name: 'user1', role: 'user' }
})

// Micro-Core 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  const globalState = microCore.getGlobalState()
  
  // 监听全局状态
  globalState.subscribe((state, prev) => {
    console.log('子应用接收状态:', state)
  })
  
  // 修改全局状态
  globalState.setState({
    theme: 'dark'
  })
}
```

## 全局状态迁移

### 迁移步骤

#### 1. 状态结构迁移

```typescript
// qiankun 状态结构
const qiankunState = {
  user: { name: '', role: '' },
  theme: 'light',
  language: 'zh-CN'
}

// Micro-Core 状态结构（增强版）
import { GlobalState } from '@micro-core/core'

const globalState = new GlobalState({
  // 基础状态
  user: { name: '', role: '' },
  theme: 'light',
  language: 'zh-CN',
  
  // 计算属性
  computed: {
    isAdmin: (state) => state.user.role === 'admin',
    isDarkTheme: (state) => state.theme === 'dark'
  },
  
  // 状态持久化
  persist: {
    key: 'micro-core-state',
    storage: localStorage,
    include: ['theme', 'language']
  }
})
```

#### 2. 状态监听迁移

```typescript
// qiankun 状态监听
actions.onGlobalStateChange((state, prev) => {
  // 处理状态变化
  if (state.theme !== prev.theme) {
    updateTheme(state.theme)
  }
})

// Micro-Core 状态监听（更强大）
globalState.subscribe((state, prev) => {
  // 处理状态变化
  if (state.theme !== prev.theme) {
    updateTheme(state.theme)
  }
})

// 监听特定属性
globalState.subscribe('theme', (newTheme, oldTheme) => {
  updateTheme(newTheme)
})

// 监听多个属性
globalState.subscribe(['user', 'theme'], (state, prev) => {
  updateUserTheme(state.user, state.theme)
})
```

#### 3. 状态更新迁移

```typescript
// qiankun 状态更新
actions.setGlobalState({
  user: { name: 'newUser', role: 'user' }
})

// Micro-Core 状态更新（更灵活）
// 1. 直接设置
globalState.setState({
  user: { name: 'newUser', role: 'user' }
})

// 2. 函数式更新
globalState.setState(prevState => ({
  ...prevState,
  user: { ...prevState.user, name: 'newUser' }
}))

// 3. 批量更新
globalState.batchUpdate(() => {
  globalState.setState({ theme: 'dark' })
  globalState.setState({ language: 'en-US' })
})

// 4. 异步更新
await globalState.setStateAsync({
  user: await fetchUserInfo()
})
```

## 事件通信迁移

### qiankun 自定义事件

```javascript
// qiankun 主应用
window.addEventListener('custom-event', (event) => {
  console.log('接收事件:', event.detail)
})

// 发送事件
window.dispatchEvent(new CustomEvent('custom-event', {
  detail: { message: 'Hello from main app' }
}))

// qiankun 子应用
window.addEventListener('sub-app-event', (event) => {
  console.log('子应用接收:', event.detail)
})

window.dispatchEvent(new CustomEvent('sub-app-event', {
  detail: { data: 'from sub app' }
}))
```

### Micro-Core EventBus 迁移

```typescript
// Micro-Core 主应用
import { MicroCore, EventBus } from '@micro-core/core'

const eventBus = new EventBus({
  namespace: true,
  async: true,
  middleware: [
    // 日志中间件
    (event, next) => {
      console.log('事件发送:', event)
      next()
    }
  ]
})

const microCore = new MicroCore({
  eventBus,
  apps: [...]
})

// 监听事件
eventBus.on('custom-event', (data) => {
  console.log('接收事件:', data)
})

// 发送事件
eventBus.emit('custom-event', {
  message: 'Hello from main app'
})

// Micro-Core 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  const eventBus = microCore.getEventBus()
  
  // 监听事件（带命名空间）
  eventBus.on('sub-app:event', (data) => {
    console.log('子应用接收:', data)
  })
  
  // 发送事件
  eventBus.emit('sub-app:event', {
    data: 'from sub app'
  })
}
```

## Props 传递迁移

### qiankun Props 传递

```javascript
// qiankun 主应用
registerMicroApps([
  {
    name: 'sub-app',
    entry: 'http://localhost:3001',
    container: '#subapp-container',
    activeWhen: '/sub-app',
    props: {
      customData: { id: 1, name: 'test' },
      onMessage: (msg) => console.log(msg)
    }
  }
])

// qiankun 子应用
export async function mount(props) {
  const { customData, onMessage } = props
  console.log('接收 props:', customData)
  
  // 调用主应用方法
  onMessage('Hello from sub app')
}
```

### Micro-Core Props 迁移

```typescript
// Micro-Core 主应用
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  apps: [
    {
      name: 'sub-app',
      entry: 'http://localhost:3001',
      activeWhen: '/sub-app',
      props: {
        customData: { id: 1, name: 'test' },
        onMessage: (msg: string) => console.log(msg),
        // 响应式 props
        reactive: {
          theme: () => globalState.getState().theme,
          user: () => globalState.getState().user
        }
      }
    }
  ]
})

// Micro-Core 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  const props = microCore.getProps()
  
  console.log('接收 props:', props.customData)
  
  // 调用主应用方法
  props.onMessage('Hello from sub app')
  
  // 监听响应式 props
  props.watch('theme', (newTheme) => {
    console.log('主题变化:', newTheme)
  })
}
```

## 完整迁移示例

### qiankun 完整示例

```javascript
// qiankun 主应用
import { registerMicroApps, start, initGlobalState } from 'qiankun'

// 初始化全局状态
const actions = initGlobalState({
  user: null,
  theme: 'light'
})

// 注册微应用
registerMicroApps([
  {
    name: 'user-center',
    entry: 'http://localhost:3001',
    container: '#user-center',
    activeWhen: '/user',
    props: {
      actions,
      data: { appId: 'user-center' }
    }
  }
])

// 启动 qiankun
start()

// qiankun 子应用
export async function mount(props) {
  const { actions, data } = props
  
  // 监听全局状态
  actions.onGlobalStateChange((state) => {
    updateUI(state)
  })
  
  // 设置用户信息
  actions.setGlobalState({
    user: { name: 'John', id: 1 }
  })
}
```

### Micro-Core 迁移后

```typescript
// Micro-Core 主应用
import { MicroCore, GlobalState, EventBus } from '@micro-core/core'

// 创建全局状态
const globalState = new GlobalState({
  user: null,
  theme: 'light'
})

// 创建事件总线
const eventBus = new EventBus()

// 创建微前端实例
const microCore = new MicroCore({
  globalState,
  eventBus,
  apps: [
    {
      name: 'user-center',
      entry: 'http://localhost:3001',
      container: '#user-center',
      activeWhen: '/user',
      props: {
        appId: 'user-center'
      }
    }
  ]
})

// 启动应用
microCore.start()

// Micro-Core 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  const globalState = microCore.getGlobalState()
  const eventBus = microCore.getEventBus()
  const props = microCore.getProps()
  
  // 监听全局状态
  globalState.subscribe((state) => {
    updateUI(state)
  })
  
  // 设置用户信息
  globalState.setState({
    user: { name: 'John', id: 1 }
  })
  
  // 发送事件
  eventBus.emit('user:login', { userId: 1 })
}
```

## 最佳实践

### 1. 通信方式选择

```typescript
// 根据场景选择通信方式
const communicationGuide = {
  // 全局状态 - 适用于需要持久化的共享数据
  globalState: {
    use: ['用户信息', '主题设置', '语言配置'],
    example: 'globalState.setState({ user: userInfo })'
  },
  
  // 事件总线 - 适用于临时通信和业务事件
  eventBus: {
    use: ['业务事件', '临时通知', '组件通信'],
    example: 'eventBus.emit("order:created", orderData)'
  },
  
  // 直接通信 - 适用于特定应用间通信
  direct: {
    use: ['应用间调用', '数据传递', '方法调用'],
    example: 'microCore.callApp("app-name", "method", data)'
  }
}
```

### 2. 性能优化

```typescript
// 批量状态更新
globalState.batchUpdate(() => {
  globalState.setState({ loading: true })
  globalState.setState({ data: newData })
  globalState.setState({ loading: false })
})

// 事件防抖
const debouncedEmit = debounce((data) => {
  eventBus.emit('search:query', data)
}, 300)

// 选择性监听
globalState.subscribe(['user.name', 'theme'], (state) => {
  // 只在 user.name 或 theme 变化时触发
})
```

### 3. 错误处理

```typescript
// 全局状态错误处理
globalState.onError((error, action) => {
  console.error('状态更新错误:', error, action)
  // 错误上报
  reportError(error)
})

// 事件总线错误处理
eventBus.onError((error, event) => {
  console.error('事件处理错误:', error, event)
  // 降级处理
  handleEventError(error, event)
})
```

### 4. 调试支持

```typescript
// 开发环境调试
if (process.env.NODE_ENV === 'development') {
  // 状态变化日志
  globalState.subscribe((state, prev) => {
    console.log('State Change:', { prev, current: state })
  })
  
  // 事件日志
  eventBus.use((event, next) => {
    console.log('Event:', event)
    next()
  })
}
```

## 迁移检查清单

- [ ] **状态结构迁移**
  - [ ] 将 qiankun actions 替换为 GlobalState
  - [ ] 添加计算属性和持久化配置
  - [ ] 更新状态监听逻辑

- [ ] **事件通信迁移**
  - [ ] 将 CustomEvent 替换为 EventBus
  - [ ] 添加命名空间和中间件
  - [ ] 更新事件监听和发送逻辑

- [ ] **Props 传递迁移**
  - [ ] 更新 props 传递方式
  - [ ] 添加响应式 props 支持
  - [ ] 更新子应用接收逻辑

- [ ] **错误处理**
  - [ ] 添加通信错误处理
  - [ ] 实现降级策略
  - [ ] 添加错误监控

- [ ] **性能优化**
  - [ ] 实现批量更新
  - [ ] 添加防抖节流
  - [ ] 优化监听器

- [ ] **测试验证**
  - [ ] 单元测试
  - [ ] 集成测试
  - [ ] 端到端测试

通过以上迁移指南，您可以将 qiankun 的通信机制平滑迁移到 Micro-Core，并享受更强大的通信能力和更好的开发体验。