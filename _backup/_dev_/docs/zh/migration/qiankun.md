# qiankun 迁移指南

本指南将帮助你从 qiankun 微前端框架平滑迁移到 Micro-Core，享受更强大的功能和更好的性能。

## 迁移概述

Micro-Core 提供了专门的 qiankun 兼容插件 `@micro-core/plugin-qiankun-compat`，让你能够以最小的代码变更完成迁移。

### 迁移优势

- **零学习成本** - 保持原有 API 和使用习惯
- **渐进式迁移** - 可以逐步迁移到 Micro-Core 原生 API
- **性能提升** - 享受 Micro-Core 的性能优化
- **功能增强** - 获得更多插件和扩展能力
- **生态兼容** - 与 Micro-Core 生态系统无缝集成

## 快速迁移

### 1. 安装兼容插件

```bash
# 使用 pnpm (推荐)
pnpm add @micro-core/plugin-qiankun-compat

# 使用 npm
npm install @micro-core/plugin-qiankun-compat
```

### 2. 替换导入

将原有的 qiankun 导入替换为兼容插件：

```typescript
// 原来的 qiankun 导入
// import { registerMicroApps, start, initGlobalState } from 'qiankun';

// 替换为 Micro-Core 兼容插件
import { registerMicroApps, start, initGlobalState } from '@micro-core/plugin-qiankun-compat';
```

### 3. 保持原有配置

你的现有配置可以完全保持不变：

```typescript
// 原有的 qiankun 配置完全兼容
registerMicroApps([
  {
    name: 'react-app',
    entry: '//localhost:3000',
    container: '#subapp-viewport',
    activeRule: '/react',
    props: {
      routerBase: '/react',
      getGlobalState: () => globalState
    }
  },
  {
    name: 'vue-app',
    entry: '//localhost:8080',
    container: '#subapp-viewport',
    activeRule: '/vue',
    props: {
      routerBase: '/vue'
    }
  }
], {
  beforeLoad: [
    app => {
      console.log('[LifeCycle] before load %c%s', 'color: green;', app.name);
    }
  ],
  beforeMount: [
    app => {
      console.log('[LifeCycle] before mount %c%s', 'color: green;', app.name);
    }
  ],
  afterUnmount: [
    app => {
      console.log('[LifeCycle] after unmount %c%s', 'color: green;', app.name);
    }
  ]
});

// 启动 qiankun
start({
  prefetch: true,
  jsSandbox: true,
  singular: false
});
```

## API 对照表

| qiankun API | Micro-Core 兼容插件 | 说明 |
|-------------|---------------------|------|
| `registerMicroApps` | `registerMicroApps` | ✅ 完全兼容 |
| `start` | `start` | ✅ 完全兼容 |
| `loadMicroApp` | `loadMicroApp` | ✅ 完全兼容 |
| `initGlobalState` | `initGlobalState` | ✅ 完全兼容 |
| `addGlobalUncaughtErrorHandler` | `addGlobalUncaughtErrorHandler` | ✅ 完全兼容 |
| `removeGlobalUncaughtErrorHandler` | `removeGlobalUncaughtErrorHandler` | ✅ 完全兼容 |

## 配置迁移

### 应用注册配置

```typescript
// qiankun 配置
const apps = [
  {
    name: 'react-app',
    entry: '//localhost:3000',
    container: '#subapp-viewport',
    activeRule: '/react',
    props: {
      routerBase: '/react'
    }
  }
];

// Micro-Core 兼容插件 - 完全相同
const apps = [
  {
    name: 'react-app',
    entry: '//localhost:3000',
    container: '#subapp-viewport', 
    activeRule: '/react',
    props: {
      routerBase: '/react'
    }
  }
];
```

### 启动配置

```typescript
// qiankun 启动配置
start({
  prefetch: true,
  jsSandbox: true,
  singular: false,
  fetch: window.fetch
});

// Micro-Core 兼容插件 - 完全相同
start({
  prefetch: true,
  jsSandbox: true,
  singular: false,
  fetch: window.fetch
});
```

### 生命周期钩子

```typescript
// qiankun 生命周期钩子
registerMicroApps(apps, {
  beforeLoad: [
    app => console.log('before load', app.name)
  ],
  beforeMount: [
    app => console.log('before mount', app.name)
  ],
  afterMount: [
    app => console.log('after mount', app.name)
  ],
  beforeUnmount: [
    app => console.log('before unmount', app.name)
  ],
  afterUnmount: [
    app => console.log('after unmount', app.name)
  ]
});

// Micro-Core 兼容插件 - 完全相同
registerMicroApps(apps, {
  beforeLoad: [
    app => console.log('before load', app.name)
  ],
  beforeMount: [
    app => console.log('before mount', app.name)
  ],
  afterMount: [
    app => console.log('after mount', app.name)
  ],
  beforeUnmount: [
    app => console.log('before unmount', app.name)
  ],
  afterUnmount: [
    app => console.log('after unmount', app.name)
  ]
});
```

## 通信迁移

### 全局状态管理

```typescript
// qiankun 全局状态
const { onGlobalStateChange, setGlobalState } = initGlobalState({
  user: 'qiankun',
  logged: true
});

onGlobalStateChange((value, prev) => {
  console.log('[onGlobalStateChange - master]:', value, prev);
});

setGlobalState({
  ignore: 'master',
  user: {
    name: 'master'
  }
});

// Micro-Core 兼容插件 - 完全相同
const { onGlobalStateChange, setGlobalState } = initGlobalState({
  user: 'qiankun',
  logged: true
});

onGlobalStateChange((value, prev) => {
  console.log('[onGlobalStateChange - master]:', value, prev);
});

setGlobalState({
  ignore: 'master',
  user: {
    name: 'master'
  }
});
```

### 子应用通信

```typescript
// qiankun 子应用通信
export async function mount(props) {
  const { onGlobalStateChange, setGlobalState } = props;
  
  onGlobalStateChange((state, prev) => {
    // 监听全局状态变化
    console.log(state, prev);
  });
  
  // 设置全局状态
  setGlobalState({
    data: 'hello world'
  });
}

// Micro-Core 兼容插件 - 完全相同
export async function mount(props) {
  const { onGlobalStateChange, setGlobalState } = props;
  
  onGlobalStateChange((state, prev) => {
    // 监听全局状态变化
    console.log(state, prev);
  });
  
  // 设置全局状态
  setGlobalState({
    data: 'hello world'
  });
}
```

## 子应用改造

### React 子应用

```typescript
// qiankun React 子应用
import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';

function render(props = {}) {
  const { container } = props;
  ReactDOM.render(<App />, container ? container.querySelector('#root') : document.querySelector('#root'));
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

export async function bootstrap() {
  console.log('[react16] react app bootstraped');
}

export async function mount(props) {
  console.log('[react16] props from main framework', props);
  render(props);
}

export async function unmount(props) {
  const { container } = props;
  ReactDOM.unmountComponentAtNode(container ? container.querySelector('#root') : document.querySelector('#root'));
}

// Micro-Core 兼容插件 - 完全相同
import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';

function render(props = {}) {
  const { container } = props;
  ReactDOM.render(<App />, container ? container.querySelector('#root') : document.querySelector('#root'));
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

export async function bootstrap() {
  console.log('[react16] react app bootstraped');
}

export async function mount(props) {
  console.log('[react16] props from main framework', props);
  render(props);
}

export async function unmount(props) {
  const { container } = props;
  ReactDOM.unmountComponentAtNode(container ? container.querySelector('#root') : document.querySelector('#root'));
}
```

### Vue 子应用

```typescript
// qiankun Vue 子应用
import Vue from 'vue';
import App from './App.vue';

let instance = null;

function render(props = {}) {
  const { container } = props;
  instance = new Vue({
    render: h => h(App),
  }).$mount(container ? container.querySelector('#app') : '#app');
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

export async function bootstrap() {
  console.log('[vue] vue app bootstraped');
}

export async function mount(props) {
  console.log('[vue] props from main framework', props);
  render(props);
}

export async function unmount() {
  instance.$destroy();
  instance.$el.innerHTML = '';
  instance = null;
}

// Micro-Core 兼容插件 - 完全相同
import Vue from 'vue';
import App from './App.vue';

let instance = null;

function render(props = {}) {
  const { container } = props;
  instance = new Vue({
    render: h => h(App),
  }).$mount(container ? container.querySelector('#app') : '#app');
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

export async function bootstrap() {
  console.log('[vue] vue app bootstraped');
}

export async function mount(props) {
  console.log('[vue] props from main framework', props);
  render(props);
}

export async function unmount() {
  instance.$destroy();
  instance.$el.innerHTML = '';
  instance = null;
}
```

## 构建配置迁移

### Webpack 配置

```javascript
// qiankun Webpack 配置
const { name } = require('./package');

module.exports = {
  output: {
    library: `${name}-[name]`,
    libraryTarget: 'umd',
    jsonpFunction: `webpackJsonp_${name}`,
  },
};

// Micro-Core 兼容插件 - 完全相同
const { name } = require('./package');

module.exports = {
  output: {
    library: `${name}-[name]`,
    libraryTarget: 'umd',
    jsonpFunction: `webpackJsonp_${name}`,
  },
};
```

## 渐进式迁移策略

### 第一步：安装兼容插件

```bash
pnpm add @micro-core/plugin-qiankun-compat
```

### 第二步：替换导入

```typescript
// 只需要修改导入语句
import { registerMicroApps, start } from '@micro-core/plugin-qiankun-compat';
```

### 第三步：验证功能

确保所有功能正常工作后，可以考虑逐步迁移到 Micro-Core 原生 API。

### 第四步：逐步迁移

```typescript
// 可以逐步引入 Micro-Core 原生功能
import { MicroCoreKernel } from '@micro-core/core';
import { registerMicroApps, start } from '@micro-core/plugin-qiankun-compat';

// 创建 Micro-Core 内核实例
const kernel = new MicroCoreKernel();

// 使用兼容插件注册应用
registerMicroApps([...]);
start();

// 同时可以使用 Micro-Core 原生插件
kernel.use(SomeAdvancedPlugin);
```

## 常见问题

### Q: 迁移后性能有提升吗？

A: 是的，Micro-Core 提供了多项性能优化：
- 更小的核心库体积 (< 15KB)
- 智能预加载机制
- 更高效的沙箱实现
- Worker/WASM 支持

### Q: 原有的子应用需要修改吗？

A: 不需要，子应用代码可以完全保持不变。

### Q: 可以混用 qiankun 和 Micro-Core 原生 API 吗？

A: 可以，兼容插件支持渐进式迁移，你可以逐步引入 Micro-Core 的原生功能。

### Q: 迁移有风险吗？

A: 风险很低，因为兼容插件提供了完整的 API 兼容性，你可以先在测试环境验证。

## 下一步

- 查看 [完整示例](./qiankun/complete-example.md)
- 了解 [API 对照表](./qiankun/api-mapping.md)
- 学习 [配置迁移](./qiankun/config-migration.md)
- 探索 [Micro-Core 原生功能](../guide/advanced/)
