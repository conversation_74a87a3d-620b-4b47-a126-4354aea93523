# 迁移指南

本指南帮助您将现有的单体应用或其他微前端解决方案迁移到 Micro-Core 架构。我们提供了详细的迁移策略、最佳实践和常见问题解决方案。

## 📋 目录

- [迁移概述](#迁移概述)
- [迁移策略](#迁移策略)
- [从单体应用迁移](#从单体应用迁移)
- [从其他微前端方案迁移](#从其他微前端方案迁移)
- [迁移检查清单](#迁移检查清单)
- [常见问题](#常见问题)
- [最佳实践](#最佳实践)

## 迁移概述

### 迁移架构图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 迁移架构                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    迁移过程    ┌─────────────────────────┐  │
│  │   现有应用       │    ────────►   │   Micro-Core 架构       │  │
│  │                 │                │                         │  │
│  │ ┌─────────────┐ │                │ ┌─────────────────────┐ │  │
│  │ │ 单体应用    │ │                │ │ 主应用 (Shell)      │ │  │
│  │ │             │ │                │ │                     │ │  │
│  │ │ - 路由      │ │   渐进式迁移    │ │ - 路由管理          │ │  │
│  │ │ - 状态管理  │ │   ────────►    │ │ - 应用加载          │ │  │
│  │ │ - 业务模块  │ │                │ │ - 全局状态          │ │  │
│  │ │ - UI 组件   │ │                │ └─────────────────────┘ │  │
│  │ └─────────────┘ │                │                         │  │
│  │                 │                │ ┌─────────────────────┐ │  │
│  │ ┌─────────────┐ │                │ │ 微应用 A            │ │  │
│  │ │ 依赖管理    │ │                │ │ - 独立部署          │ │  │
│  │ │ - 共享库    │ │   模块拆分      │ │ - 技术栈自由        │ │  │
│  │ │ - 工具链    │ │   ────────►    │ │ - 生命周期管理      │ │  │
│  │ └─────────────┘ │                │ └─────────────────────┘ │  │
│  └─────────────────┘                │                         │  │
│                                     │ ┌─────────────────────┐ │  │
│  ┌─────────────────┐                │ │ 微应用 B            │ │  │
│  │ 其他微前端方案   │   适配器模式    │ │ - 沙箱隔离          │ │  │
│  │                 │   ────────►    │ │ - 通信机制          │ │  │
│  │ - qiankun       │                │ │ - 错误边界          │ │  │
│  │ - single-spa    │                │ └─────────────────────┘ │  │
│  │ - Module Fed    │                └─────────────────────────┘  │
│  └─────────────────┘                                            │
└─────────────────────────────────────────────────────────────────┘
```

### 迁移收益

| 迁移前 | 迁移后 | 收益 |
|--------|--------|------|
| 单体应用，部署困难 | 独立部署，快速发布 | 🚀 部署效率提升 80% |
| 技术栈固化 | 技术栈自由选择 | 🔧 技术创新能力提升 |
| 团队协作困难 | 独立开发，并行协作 | 👥 开发效率提升 60% |
| 扩展性差 | 模块化架构，易扩展 | 📈 系统扩展性提升 |
| 维护成本高 | 模块独立维护 | 💰 维护成本降低 40% |

## 迁移策略

### 1. 渐进式迁移 (推荐)

适合大型单体应用，风险最低的迁移方式。

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    渐进式迁移时间线                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  阶段 1: 基础设施搭建 (2-4 周)                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ • 搭建 Micro-Core 主应用                                    │ │
│  │ • 配置构建和部署流水线                                       │ │
│  │ • 建立开发和测试环境                                         │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              ↓                                  │
│  阶段 2: 首个微应用迁移 (3-6 周)                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ • 选择相对独立的业务模块                                     │ │
│  │ • 拆分为独立的微应用                                         │ │
│  │ • 集成到主应用中                                             │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              ↓                                  │
│  阶段 3: 批量迁移 (8-16 周)                                      │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ • 并行迁移多个业务模块                                       │ │
│  │ • 优化应用间通信和状态管理                                   │ │
│  │ • 完善监控和错误处理                                         │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              ↓                                  │
│  阶段 4: 优化和完善 (4-8 周)                                     │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ • 性能优化和资源共享                                         │ │
│  │ • 完善文档和培训                                             │ │
│  │ • 下线原有单体应用                                           │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 2. 大爆炸式迁移

适合中小型应用，快速完成迁移。

```typescript
// 迁移计划示例
const migrationPlan = {
  phase1: {
    name: '准备阶段',
    duration: '1-2 周',
    tasks: [
      '分析现有应用架构',
      '制定拆分策略',
      '搭建 Micro-Core 环境'
    ]
  },
  phase2: {
    name: '拆分阶段',
    duration: '2-4 周',
    tasks: [
      '拆分业务模块',
      '创建微应用',
      '配置路由和状态管理'
    ]
  },
  phase3: {
    name: '集成阶段',
    duration: '1-2 周',
    tasks: [
      '集成所有微应用',
      '测试和调试',
      '部署上线'
    ]
  }
}
```

### 3. 混合式迁移

结合渐进式和大爆炸式的优点。

```typescript
// 混合迁移策略
const hybridMigration = {
  coreModules: {
    strategy: 'big-bang',
    modules: ['用户管理', '权限系统', '基础配置'],
    reason: '核心模块，影响面大，一次性迁移'
  },
  businessModules: {
    strategy: 'incremental',
    modules: ['订单管理', '商品管理', '客户服务'],
    reason: '业务模块，可独立运行，渐进式迁移'
  },
  legacyModules: {
    strategy: 'wrapper',
    modules: ['报表系统', '数据导入'],
    reason: '遗留系统，通过适配器包装'
  }
}
```

## 从单体应用迁移

### 1. 应用分析

#### 依赖关系分析

```typescript
// 使用工具分析应用依赖关系
import { DependencyAnalyzer } from '@micro-core/migration-tools'

const analyzer = new DependencyAnalyzer({
  sourceDir: './src',
  excludes: ['node_modules', 'dist']
})

const analysis = await analyzer.analyze()

console.log('模块依赖关系:', analysis.dependencies)
console.log('循环依赖:', analysis.circularDependencies)
console.log('建议拆分方案:', analysis.splitSuggestions)
```

#### 业务边界识别

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    业务边界识别图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   用户模块       │    │   订单模块       │    │   商品模块       ││
│  │                 │    │                 │    │                 ││
│  │ • 用户注册      │    │ • 订单创建      │    │ • 商品管理      ││
│  │ • 用户登录      │    │ • 订单支付      │    │ • 库存管理      ││
│  │ • 个人信息      │    │ • 订单查询      │    │ • 价格管理      ││
│  │ • 权限管理      │    │ • 订单退款      │    │ • 分类管理      ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│           │                       │                       │       │
│           └───────────────────────┼───────────────────────┘       │
│                                   │                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    共享服务层                               │ │
│  │  • 数据库访问 • 缓存服务 • 消息队列 • 文件存储              │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 2. 模块拆分

#### 拆分原则

```typescript
// 模块拆分配置
const moduleConfig = {
  // 高内聚：相关功能聚合在一起
  cohesion: {
    userModule: [
      'user-registration',
      'user-authentication',
      'user-profile',
      'user-permissions'
    ]
  },
  
  // 低耦合：减少模块间依赖
  coupling: {
    // 避免直接依赖，通过事件通信
    orderModule: {
      dependencies: [],
      communications: [
        { event: 'user.authenticated', handler: 'onUserAuth' },
        { event: 'product.updated', handler: 'onProductUpdate' }
      ]
    }
  },
  
  // 业务完整性：保持业务逻辑完整
  businessIntegrity: {
    paymentModule: [
      'payment-gateway',
      'payment-validation',
      'payment-notification',
      'payment-refund'
    ]
  }
}
```

#### 数据迁移

```typescript
// 数据库拆分策略
const databaseMigration = {
  // 按业务域拆分数据库
  databases: {
    userDB: {
      tables: ['users', 'roles', 'permissions', 'user_sessions'],
      connectionString: 'postgresql://user:pass@localhost/user_db'
    },
    orderDB: {
      tables: ['orders', 'order_items', 'payments', 'refunds'],
      connectionString: 'postgresql://user:pass@localhost/order_db'
    },
    productDB: {
      tables: ['products', 'categories', 'inventory', 'prices'],
      connectionString: 'postgresql://user:pass@localhost/product_db'
    }
  },
  
  // 跨库查询解决方案
  crossDatabaseQueries: {
    strategy: 'event-driven',
    implementation: 'saga-pattern',
    tools: ['@micro-core/saga', '@micro-core/event-bus']
  }
}
```

### 3. 路由迁移

#### 路由映射

```typescript
// 原有路由配置
const legacyRoutes = {
  '/user/*': 'UserController',
  '/order/*': 'OrderController',
  '/product/*': 'ProductController',
  '/admin/*': 'AdminController'
}

// 迁移后的路由配置
const microFrontendRoutes = {
  '/user/*': {
    app: 'user-app',
    entry: 'http://localhost:3001',
    activeWhen: (location) => location.pathname.startsWith('/user')
  },
  '/order/*': {
    app: 'order-app',
    entry: 'http://localhost:3002',
    activeWhen: (location) => location.pathname.startsWith('/order')
  },
  '/product/*': {
    app: 'product-app',
    entry: 'http://localhost:3003',
    activeWhen: (location) => location.pathname.startsWith('/product')
  }
}

// 路由迁移工具
import { RouteMigrator } from '@micro-core/migration-tools'

const migrator = new RouteMigrator({
  legacyRoutes,
  targetRoutes: microFrontendRoutes
})

await migrator.migrate()
```

### 4. 状态管理迁移

#### 全局状态拆分

```typescript
// 原有全局状态
const legacyGlobalState = {
  user: { id: 1, name: 'John', role: 'admin' },
  cart: { items: [], total: 0 },
  ui: { theme: 'dark', language: 'zh-CN' },
  cache: { products: [], orders: [] }
}

// 迁移后的状态分布
const migratedState = {
  // 全局共享状态
  global: {
    user: { id: 1, name: 'John', role: 'admin' },
    ui: { theme: 'dark', language: 'zh-CN' }
  },
  
  // 应用本地状态
  apps: {
    'order-app': {
      cart: { items: [], total: 0 },
      orderHistory: []
    },
    'product-app': {
      categories: [],
      searchFilters: {}
    }
  }
}

// 状态迁移配置
const stateMigration = {
  globalState: ['user', 'ui', 'permissions'],
  localState: {
    'user-app': ['userProfile', 'userSettings'],
    'order-app': ['cart', 'orderHistory'],
    'product-app': ['productCache', 'searchState']
  }
}
```

## 从其他微前端方案迁移

### 1. 从 qiankun 迁移

#### 配置对比

```typescript
// qiankun 配置
import { registerMicroApps, start } from 'qiankun'

registerMicroApps([
  {
    name: 'react-app',
    entry: '//localhost:7100',
    container: '#subapp-viewport',
    activeRule: '/react'
  }
])

start()

// Micro-Core 配置
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  apps: [
    {
      name: 'react-app',
      entry: 'http://localhost:7100',
      container: '#subapp-viewport',
      activeWhen: '/react'
    }
  ]
})

await microCore.start()
```

#### 生命周期迁移

```typescript
// qiankun 生命周期
export async function bootstrap() {
  console.log('react app bootstraped')
}

export async function mount(props) {
  console.log('props from main framework', props)
  ReactDOM.render(<App />, props.container)
}

export async function unmount(props) {
  ReactDOM.unmountComponentAtNode(props.container)
}

// Micro-Core 生命周期 (兼容)
export async function bootstrap(props) {
  console.log('react app bootstraped')
  // Micro-Core 扩展功能
  await props.microCore.registerApp('react-app')
}

export async function mount(props) {
  console.log('props from main framework', props)
  ReactDOM.render(<App microCore={props.microCore} />, props.container)
}

export async function unmount(props) {
  ReactDOM.unmountComponentAtNode(props.container)
  // Micro-Core 清理
  await props.microCore.unregisterApp('react-app')
}
```

### 2. 从 single-spa 迁移

#### 应用注册迁移

```typescript
// single-spa 注册
import { registerApplication, start } from 'single-spa'

registerApplication({
  name: 'vue-app',
  app: () => System.import('vue-app'),
  activeWhen: '/vue',
  customProps: {
    domElement: document.getElementById('vue-app')
  }
})

start()

// Micro-Core 注册
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore()

await microCore.registerApp({
  name: 'vue-app',
  entry: () => import('vue-app'),
  activeWhen: '/vue',
  container: '#vue-app',
  props: {
    // 自定义属性
  }
})

await microCore.start()
```

### 3. 从 Module Federation 迁移

#### 配置迁移

```typescript
// Module Federation 配置 (webpack.config.js)
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      name: 'shell',
      remotes: {
        mf1: 'mf1@http://localhost:3001/remoteEntry.js',
        mf2: 'mf2@http://localhost:3002/remoteEntry.js'
      }
    })
  ]
}

// Micro-Core 配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'mf1',
      entry: 'http://localhost:3001',
      activeWhen: '/mf1'
    },
    {
      name: 'mf2',
      entry: 'http://localhost:3002',
      activeWhen: '/mf2'
    }
  ],
  
  // 模块联邦兼容模式
  compatibility: {
    moduleFederation: true,
    remoteEntries: {
      mf1: 'http://localhost:3001/remoteEntry.js',
      mf2: 'http://localhost:3002/remoteEntry.js'
    }
  }
})
```

## 迁移检查清单

### 迁移前准备

- [ ] **应用分析完成**
  - [ ] 业务模块边界清晰
  - [ ] 依赖关系梳理完成
  - [ ] 数据流向分析完成
  - [ ] 技术债务评估完成

- [ ] **团队准备就绪**
  - [ ] 团队成员培训完成
  - [ ] 开发规范制定完成
  - [ ] 代码审查流程建立
  - [ ] 测试策略制定完成

- [ ] **基础设施准备**
  - [ ] CI/CD 流水线搭建
  - [ ] 监控系统配置
  - [ ] 日志收集系统
  - [ ] 错误追踪系统

### 迁移过程检查

- [ ] **主应用搭建**
  - [ ] Micro-Core 环境配置
  - [ ] 路由系统配置
  - [ ] 全局状态管理
  - [ ] 错误边界设置

- [ ] **微应用开发**
  - [ ] 应用独立性验证
  - [ ] 生命周期函数实现
  - [ ] 通信机制测试
  - [ ] 样式隔离验证

- [ ] **集成测试**
  - [ ] 应用加载测试
  - [ ] 路由跳转测试
  - [ ] 状态同步测试
  - [ ] 错误处理测试

### 迁移后验证

- [ ] **功能验证**
  - [ ] 所有功能正常运行
  - [ ] 用户体验无明显差异
  - [ ] 性能指标达标
  - [ ] 兼容性测试通过

- [ ] **运维验证**
  - [ ] 部署流程正常
  - [ ] 监控数据正常
  - [ ] 日志收集正常
  - [ ] 告警机制有效

## 常见问题

### 1. 样式冲突问题

**问题**：多个微应用的样式相互影响

**解决方案**：

```typescript
// 使用 CSS Modules
const microCore = new MicroCore({
  sandbox: {
    css: {
      isolation: 'scoped',
      prefix: true,
      strictStyleIsolation: true
    }
  }
})

// 或使用 Shadow DOM
const microCore = new MicroCore({
  sandbox: {
    css: {
      isolation: 'shadow-dom'
    }
  }
})
```

### 2. 全局变量冲突

**问题**：不同应用的全局变量相互覆盖

**解决方案**：

```typescript
// 启用 JavaScript 沙箱
const microCore = new MicroCore({
  sandbox: {
    js: {
      isolation: 'proxy',
      strictGlobalIsolation: true
    }
  }
})
```

### 3. 路由冲突

**问题**：应用间路由相互干扰

**解决方案**：

```typescript
// 配置路由命名空间
const microCore = new MicroCore({
  router: {
    mode: 'hash', // 或 'history'
    base: '/app',
    strict: true
  },
  apps: [
    {
      name: 'user-app',
      activeWhen: '/app/user',
      routeNamespace: 'user'
    }
  ]
})
```

### 4. 依赖重复加载

**问题**：多个应用加载相同的依赖库

**解决方案**：

```typescript
// 配置共享依赖
const microCore = new MicroCore({
  shared: {
    react: {
      singleton: true,
      version: '^18.0.0'
    },
    'react-dom': {
      singleton: true,
      version: '^18.0.0'
    },
    lodash: {
      singleton: true,
      version: '^4.17.21'
    }
  }
})
```

## 最佳实践

### 1. 渐进式迁移

```typescript
// 迁移阶段配置
const migrationPhases = [
  {
    phase: 1,
    name: '基础设施',
    apps: ['shell-app'],
    duration: '2 weeks'
  },
  {
    phase: 2,
    name: '核心业务',
    apps: ['user-app', 'auth-app'],
    duration: '4 weeks'
  },
  {
    phase: 3,
    name: '业务模块',
    apps: ['order-app', 'product-app'],
    duration: '6 weeks'
  }
]
```

### 2. 监控和回滚

```typescript
// 监控配置
const monitoring = {
  performance: {
    loadTime: { threshold: 3000 },
    renderTime: { threshold: 1000 }
  },
  errors: {
    errorRate: { threshold: 0.01 },
    crashRate: { threshold: 0.001 }
  },
  rollback: {
    automatic: true,
    conditions: ['errorRate > 0.05', 'loadTime > 5000']
  }
}
```

### 3. 团队协作

```typescript
// 团队责任分工
const teamStructure = {
  platform: {
    responsibility: ['主应用', '基础设施', '共享服务'],
    members: ['架构师', '平台工程师']
  },
  business: {
    responsibility: ['业务微应用', '业务逻辑'],
    members: ['业务开发工程师', '产品经理']
  },
  qa: {
    responsibility: ['集成测试', '性能测试'],
    members: ['测试工程师', 'DevOps 工程师']
  }
}
```

---

更多迁移相关信息请参考：
- [快速开始指南](/guide/getting-started)
- [架构设计文档](/guide/architecture)
- [API 参考文档](/api/)
- [示例项目](/examples/)

如需专业的迁移咨询服务，请联系我们的技术支持团队。
