# 兼容性处理指南

本指南详细介绍了在微前端迁移过程中如何处理各种兼容性问题，确保新旧系统的平滑过渡。

## 📋 目录

- [浏览器兼容性](#浏览器兼容性)
- [框架版本兼容性](#框架版本兼容性)
- [API兼容性](#api兼容性)
- [构建工具兼容性](#构建工具兼容性)
- [运行时兼容性](#运行时兼容性)
- [样式兼容性](#样式兼容性)
- [兼容性检测](#兼容性检测)
- [降级策略](#降级策略)

## 浏览器兼容性

### 支持的浏览器版本

```typescript
// 浏览器兼容性配置
const browserSupport = {
  // 现代浏览器
  modern: {
    chrome: '>=88',
    firefox: '>=85',
    safari: '>=14',
    edge: '>=88'
  },
  
  // 兼容模式
  legacy: {
    chrome: '>=70',
    firefox: '>=65',
    safari: '>=12',
    edge: '>=79',
    ie: '>=11' // 有限支持
  }
}
```

### 浏览器特性检测

```typescript
// 特性检测工具
class FeatureDetector {
  // 检测 ES6+ 支持
  static supportsES6(): boolean {
    try {
      new Function('(a = 0) => a')
      return true
    } catch (e) {
      return false
    }
  }
  
  // 检测 Proxy 支持
  static supportsProxy(): boolean {
    return typeof Proxy !== 'undefined'
  }
  
  // 检测 Web Components 支持
  static supportsWebComponents(): boolean {
    return 'customElements' in window &&
           'attachShadow' in Element.prototype
  }
  
  // 检测 Module 支持
  static supportsModules(): boolean {
    const script = document.createElement('script')
    return 'noModule' in script
  }
  
  // 综合兼容性检查
  static checkCompatibility(): CompatibilityReport {
    return {
      es6: this.supportsES6(),
      proxy: this.supportsProxy(),
      webComponents: this.supportsWebComponents(),
      modules: this.supportsModules(),
      overall: this.getOverallCompatibility()
    }
  }
  
  private static getOverallCompatibility(): 'modern' | 'legacy' | 'unsupported' {
    const features = this.checkCompatibility()
    
    if (features.es6 && features.proxy && features.modules) {
      return 'modern'
    } else if (features.es6) {
      return 'legacy'
    } else {
      return 'unsupported'
    }
  }
}

interface CompatibilityReport {
  es6: boolean
  proxy: boolean
  webComponents: boolean
  modules: boolean
  overall: 'modern' | 'legacy' | 'unsupported'
}
```

### Polyfill 策略

```typescript
// Polyfill 管理器
class PolyfillManager {
  private static polyfills: Map<string, () => Promise<void>> = new Map()
  
  // 注册 polyfill
  static register(name: string, loader: () => Promise<void>) {
    this.polyfills.set(name, loader)
  }
  
  // 加载必要的 polyfills
  static async loadRequired(): Promise<void> {
    const required = this.getRequiredPolyfills()
    
    await Promise.all(
      required.map(name => this.loadPolyfill(name))
    )
  }
  
  private static getRequiredPolyfills(): string[] {
    const required: string[] = []
    
    // 检查 Promise 支持
    if (!window.Promise) {
      required.push('promise')
    }
    
    // 检查 fetch 支持
    if (!window.fetch) {
      required.push('fetch')
    }
    
    // 检查 Proxy 支持
    if (!window.Proxy) {
      required.push('proxy')
    }
    
    // 检查 Web Components 支持
    if (!FeatureDetector.supportsWebComponents()) {
      required.push('webcomponents')
    }
    
    return required
  }
  
  private static async loadPolyfill(name: string): Promise<void> {
    const loader = this.polyfills.get(name)
    if (loader) {
      await loader()
    }
  }
}

// 注册常用 polyfills
PolyfillManager.register('promise', async () => {
  await import('es6-promise/auto')
})

PolyfillManager.register('fetch', async () => {
  await import('whatwg-fetch')
})

PolyfillManager.register('proxy', async () => {
  await import('proxy-polyfill')
})

PolyfillManager.register('webcomponents', async () => {
  await import('@webcomponents/webcomponentsjs')
})
```

## 框架版本兼容性

### React 版本兼容性

```typescript
// React 版本适配器
class ReactCompatibilityAdapter {
  private static version: string = React.version
  
  // 检测 React 版本
  static getVersion(): { major: number; minor: number; patch: number } {
    const [major, minor, patch] = this.version.split('.').map(Number)
    return { major, minor, patch }
  }
  
  // 创建兼容的 Root
  static createRoot(container: Element): ReactRoot {
    const version = this.getVersion()
    
    if (version.major >= 18) {
      // React 18+
      const { createRoot } = require('react-dom/client')
      return createRoot(container)
    } else {
      // React 16/17
      const ReactDOM = require('react-dom')
      return {
        render: (element: React.ReactElement) => {
          ReactDOM.render(element, container)
        },
        unmount: () => {
          ReactDOM.unmountComponentAtNode(container)
        }
      }
    }
  }
  
  // 兼容的事件处理
  static createEventHandler(handler: Function): Function {
    const version = this.getVersion()
    
    if (version.major >= 17) {
      // React 17+ 自动事件委托
      return handler
    } else {
      // React 16 手动事件委托
      return (event: Event) => {
        // 添加兼容性处理
        handler(event)
      }
    }
  }
}

interface ReactRoot {
  render(element: React.ReactElement): void
  unmount(): void
}
```

### Vue 版本兼容性

```typescript
// Vue 版本适配器
class VueCompatibilityAdapter {
  private static version: string = Vue.version || '2.x'
  
  // 检测 Vue 版本
  static isVue3(): boolean {
    return this.version.startsWith('3.')
  }
  
  // 创建兼容的应用实例
  static createApp(options: any): VueApp {
    if (this.isVue3()) {
      // Vue 3
      const { createApp } = Vue
      return createApp(options)
    } else {
      // Vue 2
      return new Vue(options) as any
    }
  }
  
  // 兼容的响应式数据
  static reactive<T extends object>(obj: T): T {
    if (this.isVue3()) {
      // Vue 3 Composition API
      const { reactive } = Vue
      return reactive(obj)
    } else {
      // Vue 2 使用 Vue.observable
      return Vue.observable(obj)
    }
  }
  
  // 兼容的生命周期
  static onMounted(callback: () => void): void {
    if (this.isVue3()) {
      // Vue 3 Composition API
      const { onMounted } = Vue
      onMounted(callback)
    } else {
      // Vue 2 Options API
      // 需要在组件内部使用
      console.warn('Vue 2 需要在组件的 mounted 钩子中调用')
    }
  }
}

interface VueApp {
  mount(selector: string | Element): any
  unmount?(): void
  use?(plugin: any): VueApp
}
```

## API兼容性

### 兼容性包装器

```typescript
// API 兼容性包装器
class APICompatibilityWrapper {
  private static adapters: Map<string, APIAdapter> = new Map()
  
  // 注册 API 适配器
  static registerAdapter(name: string, adapter: APIAdapter) {
    this.adapters.set(name, adapter)
  }
  
  // 包装 API 调用
  static wrapAPI<T = any>(name: string, originalAPI: T): T {
    const adapter = this.adapters.get(name)
    
    if (adapter) {
      return adapter.wrap(originalAPI)
    }
    
    return originalAPI
  }
  
  // 创建兼容的 API 代理
  static createProxy<T extends object>(target: T): T {
    return new Proxy(target, {
      get(obj, prop) {
        const value = obj[prop as keyof T]
        
        if (typeof value === 'function') {
          return function(...args: any[]) {
            try {
              return value.apply(obj, args)
            } catch (error) {
              console.warn(`API compatibility warning:`, error)
              return undefined
            }
          }
        }
        
        return value
      }
    })
  }
}

interface APIAdapter {
  wrap<T>(api: T): T
  isCompatible(): boolean
}

// qiankun API 适配器
class QiankunAPIAdapter implements APIAdapter {
  wrap<T>(api: T): T {
    // 包装 qiankun API 使其兼容 Micro-Core
    return this.createCompatibilityLayer(api)
  }
  
  isCompatible(): boolean {
    return typeof window !== 'undefined' && 'qiankun' in window
  }
  
  private createCompatibilityLayer<T>(api: T): T {
    // 实现 qiankun 到 Micro-Core 的 API 转换
    return api // 简化示例
  }
}

// 注册适配器
APICompatibilityWrapper.registerAdapter('qiankun', new QiankunAPIAdapter())
```

## 构建工具兼容性

### Webpack 兼容性

```javascript
// webpack.compat.js - Webpack 兼容性配置
const path = require('path')

class WebpackCompatibilityPlugin {
  constructor(options = {}) {
    this.options = {
      target: 'es5', // 目标环境
      polyfills: true, // 是否添加 polyfills
      ...options
    }
  }
  
  apply(compiler) {
    compiler.hooks.compilation.tap('WebpackCompatibilityPlugin', (compilation) => {
      // 添加兼容性处理
      this.addCompatibilityTransforms(compilation)
    })
  }
  
  addCompatibilityTransforms(compilation) {
    // 添加 ES5 转换
    if (this.options.target === 'es5') {
      this.addES5Transforms(compilation)
    }
    
    // 添加 polyfills
    if (this.options.polyfills) {
      this.addPolyfills(compilation)
    }
  }
  
  addES5Transforms(compilation) {
    // 配置 Babel 转换
    const babelOptions = {
      presets: [
        ['@babel/preset-env', {
          targets: {
            browsers: ['> 1%', 'last 2 versions', 'ie >= 11']
          },
          useBuiltIns: 'usage',
          corejs: 3
        }]
      ]
    }
    
    // 应用转换
    compilation.hooks.optimizeChunkAssets.tap('ES5Transform', (chunks) => {
      // 实现 ES5 转换逻辑
    })
  }
  
  addPolyfills(compilation) {
    // 添加必要的 polyfills
    const polyfills = [
      'core-js/stable',
      'regenerator-runtime/runtime'
    ]
    
    compilation.hooks.beforeChunkAssets.tap('AddPolyfills', () => {
      // 注入 polyfills
    })
  }
}

// 兼容性配置
function createCompatibilityConfig(options = {}) {
  const isLegacy = options.legacy || false
  
  return {
    mode: 'production',
    
    entry: {
      main: './src/index.js',
      ...(isLegacy && {
        polyfills: './src/polyfills.js'
      })
    },
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: isLegacy ? '[name].legacy.js' : '[name].js',
      chunkFilename: isLegacy ? '[name].legacy.chunk.js' : '[name].chunk.js'
    },
    
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: [
                ['@babel/preset-env', {
                  targets: isLegacy ? {
                    browsers: ['ie >= 11']
                  } : {
                    browsers: ['> 1%', 'last 2 versions']
                  }
                }]
              ]
            }
          }
        }
      ]
    },
    
    plugins: [
      new WebpackCompatibilityPlugin({
        target: isLegacy ? 'es5' : 'es2015',
        polyfills: isLegacy
      })
    ]
  }
}

module.exports = {
  WebpackCompatibilityPlugin,
  createCompatibilityConfig
}
```

### Vite 兼容性

```typescript
// vite.compat.ts - Vite 兼容性配置
import { defineConfig, Plugin } from 'vite'
import legacy from '@vitejs/plugin-legacy'

// Vite 兼容性插件
function viteCompatibilityPlugin(options: CompatibilityOptions = {}): Plugin {
  return {
    name: 'vite-compatibility',
    
    configResolved(config) {
      // 根据兼容性需求调整配置
      if (options.legacy) {
        this.addLegacySupport(config)
      }
    },
    
    generateBundle(options, bundle) {
      // 生成兼容性包
      if (options.generateLegacyBundle) {
        this.generateLegacyBundle(bundle)
      }
    }
  }
}

interface CompatibilityOptions {
  legacy?: boolean
  targets?: string[]
  polyfills?: boolean
  generateLegacyBundle?: boolean
}

// 兼容性配置
export function createCompatibilityConfig(options: CompatibilityOptions = {}) {
  return defineConfig({
    build: {
      target: options.legacy ? 'es2015' : 'esnext',
      
      rollupOptions: {
        output: {
          // 为不同环境生成不同的包
          manualChunks: {
            'vendor': ['vue', 'react'],
            'polyfills': ['core-js', 'regenerator-runtime']
          }
        }
      }
    },
    
    plugins: [
      // 传统浏览器支持
      ...(options.legacy ? [
        legacy({
          targets: options.targets || ['defaults', 'not IE 11'],
          additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
          renderLegacyChunks: true,
          polyfills: options.polyfills !== false
        })
      ] : []),
      
      // 自定义兼容性插件
      viteCompatibilityPlugin(options)
    ]
  })
}
```

## 运行时兼容性

### 运行时检测和适配

```typescript
// 运行时兼容性管理器
class RuntimeCompatibilityManager {
  private static instance: RuntimeCompatibilityManager
  private compatibilityMode: 'modern' | 'legacy' | 'fallback' = 'modern'
  private features: Map<string, boolean> = new Map()
  
  static getInstance(): RuntimeCompatibilityManager {
    if (!this.instance) {
      this.instance = new RuntimeCompatibilityManager()
    }
    return this.instance
  }
  
  // 初始化兼容性检测
  async initialize(): Promise<void> {
    await this.detectFeatures()
    this.determineCompatibilityMode()
    await this.loadCompatibilityLayer()
  }
  
  // 检测浏览器特性
  private async detectFeatures(): Promise<void> {
    const features = {
      'es6-modules': this.supportsES6Modules(),
      'dynamic-import': this.supportsDynamicImport(),
      'web-components': this.supportsWebComponents(),
      'proxy': this.supportsProxy(),
      'async-await': this.supportsAsyncAwait(),
      'fetch': this.supportsFetch(),
      'intersection-observer': this.supportsIntersectionObserver()
    }
    
    for (const [name, supported] of Object.entries(features)) {
      this.features.set(name, supported)
    }
  }
  
  // 确定兼容性模式
  private determineCompatibilityMode(): void {
    const criticalFeatures = ['es6-modules', 'proxy', 'fetch']
    const supportedCritical = criticalFeatures.filter(
      feature => this.features.get(feature)
    )
    
    if (supportedCritical.length === criticalFeatures.length) {
      this.compatibilityMode = 'modern'
    } else if (supportedCritical.length > 0) {
      this.compatibilityMode = 'legacy'
    } else {
      this.compatibilityMode = 'fallback'
    }
  }
  
  // 加载兼容性层
  private async loadCompatibilityLayer(): Promise<void> {
    switch (this.compatibilityMode) {
      case 'legacy':
        await this.loadLegacyPolyfills()
        break
      case 'fallback':
        await this.loadFallbackMode()
        break
    }
  }
  
  // 特性检测方法
  private supportsES6Modules(): boolean {
    const script = document.createElement('script')
    return 'noModule' in script
  }
  
  private supportsDynamicImport(): boolean {
    try {
      new Function('import("")')
      return true
    } catch {
      return false
    }
  }
  
  private supportsWebComponents(): boolean {
    return 'customElements' in window &&
           'attachShadow' in Element.prototype
  }
  
  private supportsProxy(): boolean {
    return typeof Proxy !== 'undefined'
  }
  
  private supportsAsyncAwait(): boolean {
    try {
      new Function('async () => {}')
      return true
    } catch {
      return false
    }
  }
  
  private supportsFetch(): boolean {
    return 'fetch' in window
  }
  
  private supportsIntersectionObserver(): boolean {
    return 'IntersectionObserver' in window
  }
  
  // 加载传统浏览器 polyfills
  private async loadLegacyPolyfills(): Promise<void> {
    const polyfills = []
    
    if (!this.features.get('fetch')) {
      polyfills.push(import('whatwg-fetch'))
    }
    
    if (!this.features.get('intersection-observer')) {
      polyfills.push(import('intersection-observer'))
    }
    
    if (!this.features.get('web-components')) {
      polyfills.push(import('@webcomponents/webcomponentsjs'))
    }
    
    await Promise.all(polyfills)
  }
  
  // 加载降级模式
  private async loadFallbackMode(): Promise<void> {
    // 加载完整的 polyfill 包
    await import('core-js/stable')
    await import('regenerator-runtime/runtime')
    
    // 设置降级标志
    window.__MICRO_CORE_FALLBACK__ = true
  }
  
  // 获取兼容性信息
  getCompatibilityInfo(): CompatibilityInfo {
    return {
      mode: this.compatibilityMode,
      features: Object.fromEntries(this.features),
      recommendations: this.getRecommendations()
    }
  }
  
  private getRecommendations(): string[] {
    const recommendations: string[] = []
    
    if (this.compatibilityMode === 'fallback') {
      recommendations.push('建议升级浏览器以获得更好的性能')
    }
    
    if (!this.features.get('es6-modules')) {
      recommendations.push('浏览器不支持 ES6 模块，将使用 UMD 格式')
    }
    
    return recommendations
  }
}

interface CompatibilityInfo {
  mode: 'modern' | 'legacy' | 'fallback'
  features: Record<string, boolean>
  recommendations: string[]
}
```

## 样式兼容性

### CSS 兼容性处理

```typescript
// CSS 兼容性管理器
class CSSCompatibilityManager {
  private static prefixes = ['-webkit-', '-moz-', '-ms-', '-o-', '']
  
  // 添加浏览器前缀
  static addVendorPrefixes(styles: CSSStyleDeclaration, property: string, value: string): void {
    this.prefixes.forEach(prefix => {
      const prefixedProperty = prefix + property
      if (prefixedProperty in styles) {
        styles.setProperty(prefixedProperty, value)
      }
    })
  }
  
  // 检测 CSS 特性支持
  static supportsCSSFeature(property: string, value?: string): boolean {
    const element = document.createElement('div')
    const style = element.style
    
    try {
      if (value) {
        style.setProperty(property, value)
        return style.getPropertyValue(property) === value
      } else {
        return property in style
      }
    } catch {
      return false
    }
  }
  
  // 创建兼容的样式规则
  static createCompatibleRule(selector: string, rules: Record<string, string>): string {
    const compatibleRules: string[] = []
    
    for (const [property, value] of Object.entries(rules)) {
      if (this.needsPrefix(property)) {
        this.prefixes.forEach(prefix => {
          compatibleRules.push(`  ${prefix}${property}: ${value};`)
        })
      } else {
        compatibleRules.push(`  ${property}: ${value};`)
      }
    }
    
    return `${selector} {\n${compatibleRules.join('\n')}\n}`
  }
  
  private static needsPrefix(property: string): boolean {
    const prefixedProperties = [
      'transform',
      'transition',
      'animation',
      'box-shadow',
      'border-radius',
      'user-select',
      'appearance'
    ]
    
    return prefixedProperties.includes(property)
  }
  
  // CSS Grid 降级
  static createGridFallback(gridStyles: GridStyles): string {
    if (this.supportsCSSFeature('display', 'grid')) {
      return this.createGridStyles(gridStyles)
    } else {
      return this.createFlexboxFallback(gridStyles)
    }
  }
  
  private static createGridStyles(styles: GridStyles): string {
    return `
      display: grid;
      grid-template-columns: ${styles.columns};
      grid-template-rows: ${styles.rows};
      gap: ${styles.gap};
    `
  }
  
  private static createFlexboxFallback(styles: GridStyles): string {
    return `
      display: flex;
      flex-wrap: wrap;
      margin: -${styles.gap};
    `
  }
}

interface GridStyles {
  columns: string
  rows: string
  gap: string
}
```

## 兼容性检测

### 自动化兼容性测试

```typescript
// 兼容性测试套件
class CompatibilityTestSuite {
  private tests: Map<string, CompatibilityTest> = new Map()
  private results: Map<string, TestResult> = new Map()
  
  // 注册测试
  registerTest(name: string, test: CompatibilityTest): void {
    this.tests.set(name, test)
  }
  
  // 运行所有测试
  async runAllTests(): Promise<CompatibilityReport> {
    const results: TestResult[] = []
    
    for (const [name, test] of this.tests) {
      try {
        const result = await this.runTest(name, test)
        results.push(result)
        this.results.set(name, result)
      } catch (error) {
        const errorResult: TestResult = {
          name,
          passed: false,
          error: error.message,
          duration: 0
        }
        results.push(errorResult)
        this.results.set(name, errorResult)
      }
    }
    
    return this.generateReport(results)
  }
  
  // 运行单个测试
  private async runTest(name: string, test: CompatibilityTest): Promise<TestResult> {
    const startTime = performance.now()
    
    const passed = await test.run()
    
    const endTime = performance.now()
    const duration = endTime - startTime
    
    return {
      name,
      passed,
      duration,
      details: test.getDetails?.()
    }
  }
  
  // 生成报告
  private generateReport(results: TestResult[]): CompatibilityReport {
    const passed = results.filter(r => r.passed).length
    const failed = results.length - passed
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0)
    
    return {
      summary: {
        total: results.length,
        passed,
        failed,
        duration: totalDuration
      },
      results,
      recommendations: this.generateRecommendations(results)
    }
  }
  
  private generateRecommendations(results: TestResult[]): string[] {
    const recommendations: string[] = []
    const failedTests = results.filter(r => !r.passed)
    
    if (failedTests.length > 0) {
      recommendations.push('发现兼容性问题，建议：')
      
      failedTests.forEach(test => {
        recommendations.push(`- ${test.name}: ${test.error || '测试失败'}`)
      })
    }
    
    return recommendations
  }
}

interface CompatibilityTest {
  run(): Promise<boolean>
  getDetails?(): any
}

interface TestResult {
  name: string
  passed: boolean
  duration: number
  error?: string
  details?: any
}

interface CompatibilityReport {
  summary: {
    total: number
    passed: number
    failed: number
    duration: number
  }
  results: TestResult[]
  recommendations: string[]
}

// 预定义测试
const compatibilityTests = new CompatibilityTestSuite()

// ES6 特性测试
compatibilityTests.registerTest('es6-features', {
  async run(): Promise<boolean> {
    try {
      // 测试箭头函数
      const arrow = () => true
      
      // 测试解构
      const [a, b] = [1, 2]
      
      // 测试模板字符串
      const template = `test ${a}`
      
      // 测试 Promise
      await new Promise(resolve => resolve(true))
      
      return true
    } catch {
      return false
    }
  }
})

// Web Components 测试
compatibilityTests.registerTest('web-components', {
  async run(): Promise<boolean> {
    return 'customElements' in window &&
           'attachShadow' in Element.prototype
  }
})

// 模块加载测试
compatibilityTests.registerTest('module-loading', {
  async run(): Promise<boolean> {
    try {
      // 测试动态导入
      const module = await import('data:text/javascript,export default true')
      return module.default === true
    } catch {
      return false
    }
  }
})
```

## 降级策略

### 渐进式降级

```typescript
// 渐进式降级管理器
class ProgressiveDegradationManager {
  private degradationLevels: DegradationLevel[] = [
    {
      name: 'modern',
      features: ['es6-modules', 'dynamic-import', 'web-components'],
      fallback: 'enhanced'
    },
    {
      name: 'enhanced',
      features: ['es6-syntax', 'fetch', 'promise'],
      fallback: 'basic'
    },
    {
      name: 'basic',
      features: ['es5-syntax'],
      fallback: 'minimal'
    },
    {
      name: 'minimal',
      features: [],
      fallback: null
    }
  ]
  
  // 确定当前降级级别
  determineLevel(features: Record<string, boolean>): string {
    for (const level of this.degradationLevels) {
      if (this.supportsLevel(level, features)) {
        return level.name
      }
    }
    
    return 'minimal'
  }
  
  private supportsLevel(level: DegradationLevel, features: Record<string, boolean>): boolean {
    return level.features.every(feature => features[feature])
  }
  
  // 应用降级策略
  async applyDegradation(level: string): Promise<void> {
    switch (level) {
      case 'enhanced':
        await this.loadEnhancedPolyfills()
        break
      case 'basic':
        await this.loadBasicPolyfills()
        break
      case 'minimal':
        await this.loadMinimalFallback()
        break
    }
  }
  
  private async loadEnhancedPolyfills(): Promise<void> {
    // 加载增强功能的 polyfills
    await Promise.all([
      import('whatwg-fetch'),
      import('es6-promise/auto')
    ])
  }
  
  private async loadBasicPolyfills(): Promise<void> {
    // 加载基础 polyfills
    await import('core-js/es6')
  }
  
  private async loadMinimalFallback(): Promise<void> {
    // 最小化降级，显示不支持提示
    this.showUnsupportedBrowserMessage()
  }
  
  private showUnsupportedBrowserMessage(): void {
    const message = document.createElement('div')
    message.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #f44336;
        color: white;
        padding: 16px;
        text-align: center;
        z-index: 9999;
      ">
        您的浏览器版本过低，建议升级到最新版本以获得最佳体验
      </div>
    `
    document.body.appendChild(message)
  }
}

interface DegradationLevel {
  name: string
  features: string[]
  fallback: string | null
}
```

## 最佳实践

### 兼容性开发建议

```typescript
// 兼容性开发最佳实践
class CompatibilityBestPractices {
  // 1. 特性检测优于浏览器检测
  static useFeatureDetection(): void {
    // ✅ 推荐：特性检测
    if ('serviceWorker' in navigator) {
      // 使用 Service Worker
    }
    
    // ❌ 避免：浏览器检测
    // if (navigator.userAgent.includes('Chrome')) {
    //   // 假设支持某个特性
    // }
  }
  
  // 2. 渐进式增强
  static useProgressiveEnhancement(): void {
    // 基础功能
    const basicFeature = () => {
      console.log('基础功能')
    }
    
    // 增强功能
    if (window.IntersectionObserver) {
      const enhancedFeature = () => {
        console.log('增强功能')
      }
      enhancedFeature()
    } else {
      basicFeature()
    }
  }
  
  // 3. 优雅降级
  static useGracefulDegradation(): void {
    try {
      // 尝试使用现代 API
      const modernAPI = () => {
        return fetch('/api/data')
      }
      modernAPI()
    } catch (error) {
      // 降级到传统方法
      const legacyAPI = () => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', '/api/data')
        xhr.send()
      }
      legacyAPI()
    }
  }
  
  // 4. 条件加载
  static useConditionalLoading(): void {
    // 根据支持情况加载不同的包
    if (FeatureDetector.supportsES6()) {
      import('./modern-bundle.js')
    } else {
      import('./legacy-bundle.js')
    }
  }
}
```

### 性能优化建议

```typescript
// 兼容性性能优化
class CompatibilityPerformanceOptimizer {
  // 延迟加载 polyfills
  static async lazyLoadPolyfills(): Promise<void> {
    const polyfillsNeeded = this.getNeededPolyfills()
    
    if (polyfillsNeeded.length > 0) {
      // 使用 requestIdleCallback 延迟加载
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          this.loadPolyfills(polyfillsNeeded)
        })
      } else {
        // 降级到 setTimeout
        setTimeout(() => {
          this.loadPolyfills(polyfillsNeeded)
        }, 100)
      }
    }
  }
  
  private static getNeededPolyfills(): string[] {
    const needed: string[] = []
    
    if (!window.fetch) needed.push('fetch')
    if (!window.Promise) needed.push('promise')
    if (!window.IntersectionObserver) needed.push('intersection-observer')
    
    return needed
  }
  
  private static async loadPolyfills(polyfills: string[]): Promise<void> {
    const loadPromises = polyfills.map(polyfill => {
      switch (polyfill) {
        case 'fetch':
          return import('whatwg-fetch')
        case 'promise':
          return import('es6-promise/auto')
        case 'intersection-observer':
          return import('intersection-observer')
        default:
          return Promise.resolve()
      }
    })
    
    await Promise.all(loadPromises)
  }
  
  // 缓存兼容性检测结果
  static cacheCompatibilityResults(): void {
    const cacheKey = 'micro-core-compatibility'
    const cached = localStorage.getItem(cacheKey)
    
    if (!cached) {
      const results = FeatureDetector.checkCompatibility()
      localStorage.setItem(cacheKey, JSON.stringify(results))
    }
  }
}
```

## 测试和验证

### 兼容性测试矩阵

```typescript
// 兼容性测试矩阵
const compatibilityMatrix = {
  browsers: [
    { name: 'Chrome', versions: ['88+', '70-87', '<70'] },
    { name: 'Firefox', versions: ['85+', '65-84', '<65'] },
    { name: 'Safari', versions: ['14+', '12-13', '<12'] },
    { name: 'Edge', versions: ['88+', '79-87', '<79'] },
    { name: 'IE', versions: ['11', '<11'] }
  ],
  
  features: [
    'ES6 Modules',
    'Dynamic Import',
    'Web Components',
    'Proxy',
    'Fetch API',
    'Intersection Observer',
    'Service Worker'
  ],
  
  frameworks: [
    { name: 'React', versions: ['18.x', '17.x', '16.8+'] },
    { name: 'Vue', versions: ['3.x', '2.7+', '2.6'] },
    { name: 'Angular', versions: ['16+', '12-15', '<12'] }
  ]
}

// 自动化兼容性测试
class AutomatedCompatibilityTesting {
  async runBrowserTests(): Promise<TestResults> {
    const results: TestResults = {
      passed: 0,
      failed: 0,
      details: []
    }
    
    for (const browser of compatibilityMatrix.browsers) {
      for (const version of browser.versions) {
        const testResult = await this.testBrowserVersion(browser.name, version)
        results.details.push(testResult)
        
        if (testResult.success) {
          results.passed++
        } else {
          results.failed++
        }
      }
    }
    
    return results
  }
  
  private async testBrowserVersion(browser: string, version: string): Promise<BrowserTestResult> {
    // 模拟浏览器环境测试
    return {
      browser,
      version,
      success: true,
      features: compatibilityMatrix.features.map(feature => ({
        name: feature,
        supported: Math.random() > 0.2 // 模拟测试结果
      }))
    }
  }
}

interface TestResults {
  passed: number
  failed: number
  details: BrowserTestResult[]
}

interface BrowserTestResult {
  browser: string
  version: string
  success: boolean
  features: FeatureTestResult[]
}

interface FeatureTestResult {
  name: string
  supported: boolean
}
```

## 故障排除

### 常见兼容性问题

```typescript
// 兼容性问题诊断器
class CompatibilityDiagnostics {
  static diagnoseIssues(): DiagnosticReport {
    const issues: CompatibilityIssue[] = []
    
    // 检查常见问题
    if (!window.Promise) {
      issues.push({
        type: 'missing-feature',
        severity: 'high',
        description: 'Promise 不支持',
        solution: '加载 es6-promise polyfill'
      })
    }
    
    if (!window.fetch) {
      issues.push({
        type: 'missing-feature',
        severity: 'medium',
        description: 'Fetch API 不支持',
        solution: '加载 whatwg-fetch polyfill'
      })
    }
    
    if (typeof Proxy === 'undefined') {
      issues.push({
        type: 'missing-feature',
        severity: 'high',
        description: 'Proxy 不支持',
        solution: '使用 proxy-polyfill 或降级到 Object.defineProperty'
      })
    }
    
    // 检查框架兼容性
    this.checkFrameworkCompatibility(issues)
    
    return {
      issues,
      recommendations: this.generateRecommendations(issues)
    }
  }
  
  private static checkFrameworkCompatibility(issues: CompatibilityIssue[]): void {
    // React 兼容性检查
    if (typeof React !== 'undefined') {
      const version = React.version
      if (version && version.startsWith('16.')) {
        issues.push({
          type: 'version-compatibility',
          severity: 'low',
          description: `React ${version} 可能需要额外配置`,
          solution: '考虑升级到 React 17+ 或添加兼容性配置'
        })
      }
    }
    
    // Vue 兼容性检查
    if (typeof Vue !== 'undefined') {
      const version = Vue.version
      if (version && version.startsWith('2.')) {
        issues.push({
          type: 'version-compatibility',
          severity: 'low',
          description: `Vue ${version} 需要兼容性适配`,
          solution: '使用 Vue 2 兼容性适配器'
        })
      }
    }
  }
  
  private static generateRecommendations(issues: CompatibilityIssue[]): string[] {
    const recommendations: string[] = []
    
    const highSeverityIssues = issues.filter(issue => issue.severity === 'high')
    if (highSeverityIssues.length > 0) {
      recommendations.push('发现高优先级兼容性问题，建议立即处理')
    }
    
    const missingFeatures = issues.filter(issue => issue.type === 'missing-feature')
    if (missingFeatures.length > 2) {
      recommendations.push('建议升级浏览器或加载完整的 polyfill 包')
    }
    
    return recommendations
  }
}

interface CompatibilityIssue {
  type: 'missing-feature' | 'version-compatibility' | 'configuration'
  severity: 'low' | 'medium' | 'high'
  description: string
  solution: string
}

interface DiagnosticReport {
  issues: CompatibilityIssue[]
  recommendations: string[]
}
```

## 总结

兼容性处理是微前端迁移中的关键环节，需要：

1. **全面的特性检测** - 准确识别浏览器能力
2. **渐进式降级策略** - 确保在各种环境下都能正常工作
3. **性能优化** - 避免不必要的 polyfill 加载
4. **自动化测试** - 确保兼容性方案的有效性
5. **持续监控** - 及时发现和解决兼容性问题

通过遵循这些最佳实践，可以确保微前端应用在各种环境下都能提供良好的用户体验。

## 相关链接

- [测试策略](/migration/general/testing) - 兼容性测试方法
- [渐进式迁移](/migration/general/progressive) - 渐进式迁移策略
- [qiankun 迁移](/migration/qiankun/) - qiankun 兼容性处理
- [wujie 迁移](/migration/wujie/) - wujie 兼容性处理
