# wujie 迁移指南

本指南将帮助你从 wujie (无界) 微前端框架平滑迁移到 Micro-Core，获得更强大的功能和更好的开发体验。

## 迁移概述

Micro-Core 提供了专门的 wujie 兼容插件 `@micro-core/plugin-wujie-compat`，让你能够以最小的代码变更完成迁移。

### 迁移优势

- **API 兼容** - 保持原有 API 和使用习惯
- **性能提升** - 更高效的沙箱实现和资源加载
- **功能增强** - 获得更多插件和扩展能力
- **更好的 TypeScript 支持** - 完整的类型定义
- **生态兼容** - 与 Micro-Core 生态系统无缝集成

## 快速迁移

### 1. 安装兼容插件

```bash
# 使用 pnpm (推荐)
pnpm add @micro-core/plugin-wujie-compat

# 使用 npm
npm install @micro-core/plugin-wujie-compat
```

### 2. 替换导入

将原有的 wujie 导入替换为兼容插件：

```typescript
// 原来的 wujie 导入
// import { setupApp, preloadApp, startApp, destroyApp, bus } from 'wujie';

// 替换为 Micro-Core 兼容插件
import { setupApp, preloadApp, startApp, destroyApp, bus } from '@micro-core/plugin-wujie-compat';
```

### 3. 保持原有配置

你的现有配置可以完全保持不变：

```typescript
// 原有的 wujie 配置完全兼容
setupApp({
  name: 'react-app',
  url: 'http://localhost:3000/',
  exec: true,
  fetch: (url, options) => window.fetch(url, options),
  props: {
    jump: (name) => {
      startApp({ name });
    }
  },
  attrs: {
    src: 'http://localhost:3000/'
  },
  replace: (code) => {
    if (name === 'react-app') {
      // 替换子应用的接口域名
      code = code.replace('localhost:8080', 'localhost:3000');
    }
    return code;
  }
});

// 启动应用
startApp({
  name: 'react-app',
  url: 'http://localhost:3000/',
  el: '#container'
});
```

## API 对照表

| wujie API | Micro-Core 兼容插件 | 说明 |
|-----------|---------------------|------|
| `setupApp` | `setupApp` | ✅ 完全兼容 |
| `preloadApp` | `preloadApp` | ✅ 完全兼容 |
| `startApp` | `startApp` | ✅ 完全兼容 |
| `destroyApp` | `destroyApp` | ✅ 完全兼容 |
| `bus` | `bus` | ✅ 完全兼容 |
| `WujieVue` | `WujieVue` | ✅ 完全兼容 |
| `WujieReact` | `WujieReact` | ✅ 完全兼容 |

## 配置迁移

### 应用设置配置

```typescript
// wujie 应用设置
setupApp({
  name: 'vue-app',
  url: 'http://localhost:8080/',
  exec: true,
  alive: true,
  fetch: (url, options) => window.fetch(url, options),
  props: {
    jump: (name) => startApp({ name }),
    userInfo: { name: 'wujie' }
  },
  attrs: {
    src: 'http://localhost:8080/'
  },
  replace: (code) => {
    return code.replace('old-api', 'new-api');
  },
  beforeLoad: (appWindow) => {
    console.log('beforeLoad', appWindow);
  },
  beforeMount: (appWindow) => {
    console.log('beforeMount', appWindow);
  },
  afterMount: (appWindow) => {
    console.log('afterMount', appWindow);
  },
  beforeUnmount: (appWindow) => {
    console.log('beforeUnmount', appWindow);
  },
  afterUnmount: (appWindow) => {
    console.log('afterUnmount', appWindow);
  }
});

// Micro-Core 兼容插件 - 完全相同
setupApp({
  name: 'vue-app',
  url: 'http://localhost:8080/',
  exec: true,
  alive: true,
  fetch: (url, options) => window.fetch(url, options),
  props: {
    jump: (name) => startApp({ name }),
    userInfo: { name: 'wujie' }
  },
  attrs: {
    src: 'http://localhost:8080/'
  },
  replace: (code) => {
    return code.replace('old-api', 'new-api');
  },
  beforeLoad: (appWindow) => {
    console.log('beforeLoad', appWindow);
  },
  beforeMount: (appWindow) => {
    console.log('beforeMount', appWindow);
  },
  afterMount: (appWindow) => {
    console.log('afterMount', appWindow);
  },
  beforeUnmount: (appWindow) => {
    console.log('beforeUnmount', appWindow);
  },
  afterUnmount: (appWindow) => {
    console.log('afterUnmount', appWindow);
  }
});
```

### 应用启动配置

```typescript
// wujie 应用启动
startApp({
  name: 'vue-app',
  url: 'http://localhost:8080/',
  el: '#vue-container',
  props: {
    data: 'from main app'
  },
  attrs: {
    style: 'width: 100%; height: 100%'
  }
});

// Micro-Core 兼容插件 - 完全相同
startApp({
  name: 'vue-app',
  url: 'http://localhost:8080/',
  el: '#vue-container',
  props: {
    data: 'from main app'
  },
  attrs: {
    style: 'width: 100%; height: 100%'
  }
});
```

## 通信迁移

### 事件总线通信

```typescript
// wujie 事件总线
import { bus } from 'wujie';

// 主应用监听事件
bus.$on('event-from-child', (data) => {
  console.log('收到子应用消息:', data);
});

// 主应用发送事件
bus.$emit('event-to-child', { message: 'hello from main' });

// Micro-Core 兼容插件 - 完全相同
import { bus } from '@micro-core/plugin-wujie-compat';

// 主应用监听事件
bus.$on('event-from-child', (data) => {
  console.log('收到子应用消息:', data);
});

// 主应用发送事件
bus.$emit('event-to-child', { message: 'hello from main' });
```

### 子应用通信

```typescript
// wujie 子应用通信
// 在子应用中
window.$wujie?.bus.$emit('event-from-child', { data: 'hello' });

window.$wujie?.bus.$on('event-to-child', (data) => {
  console.log('收到主应用消息:', data);
});

// Micro-Core 兼容插件 - 完全相同
// 在子应用中
window.$wujie?.bus.$emit('event-from-child', { data: 'hello' });

window.$wujie?.bus.$on('event-to-child', (data) => {
  console.log('收到主应用消息:', data);
});
```

## 框架组件迁移

### Vue 组件

```vue
<!-- wujie Vue 组件 -->
<template>
  <WujieVue
    width="100%"
    height="100%"
    name="vue-app"
    :url="url"
    :sync="true"
    :props="props"
    @beforeLoad="beforeLoad"
    @beforeMount="beforeMount"
    @afterMount="afterMount"
    @beforeUnmount="beforeUnmount"
    @afterUnmount="afterUnmount"
  />
</template>

<script>
import { WujieVue } from 'wujie-vue3';

export default {
  components: { WujieVue },
  data() {
    return {
      url: 'http://localhost:8080/',
      props: {
        jump: this.jump
      }
    };
  },
  methods: {
    jump(name) {
      console.log('jump to', name);
    },
    beforeLoad() {
      console.log('beforeLoad');
    },
    beforeMount() {
      console.log('beforeMount');
    },
    afterMount() {
      console.log('afterMount');
    },
    beforeUnmount() {
      console.log('beforeUnmount');
    },
    afterUnmount() {
      console.log('afterUnmount');
    }
  }
};
</script>

<!-- Micro-Core 兼容插件 - 完全相同 -->
<template>
  <WujieVue
    width="100%"
    height="100%"
    name="vue-app"
    :url="url"
    :sync="true"
    :props="props"
    @beforeLoad="beforeLoad"
    @beforeMount="beforeMount"
    @afterMount="afterMount"
    @beforeUnmount="beforeUnmount"
    @afterUnmount="afterUnmount"
  />
</template>

<script>
import { WujieVue } from '@micro-core/plugin-wujie-compat/vue3';

export default {
  components: { WujieVue },
  data() {
    return {
      url: 'http://localhost:8080/',
      props: {
        jump: this.jump
      }
    };
  },
  methods: {
    jump(name) {
      console.log('jump to', name);
    },
    beforeLoad() {
      console.log('beforeLoad');
    },
    beforeMount() {
      console.log('beforeMount');
    },
    afterMount() {
      console.log('afterMount');
    },
    beforeUnmount() {
      console.log('beforeUnmount');
    },
    afterUnmount() {
      console.log('afterUnmount');
    }
  }
};
</script>
```

### React 组件

```tsx
// wujie React 组件
import React, { useCallback } from 'react';
import WujieReact from 'wujie-react';

function App() {
  const jump = useCallback((name: string) => {
    console.log('jump to', name);
  }, []);

  return (
    <WujieReact
      width="100%"
      height="100%"
      name="react-app"
      url="http://localhost:3000/"
      sync={true}
      props={{
        jump
      }}
      beforeLoad={() => console.log('beforeLoad')}
      beforeMount={() => console.log('beforeMount')}
      afterMount={() => console.log('afterMount')}
      beforeUnmount={() => console.log('beforeUnmount')}
      afterUnmount={() => console.log('afterUnmount')}
    />
  );
}

// Micro-Core 兼容插件 - 完全相同
import React, { useCallback } from 'react';
import WujieReact from '@micro-core/plugin-wujie-compat/react';

function App() {
  const jump = useCallback((name: string) => {
    console.log('jump to', name);
  }, []);

  return (
    <WujieReact
      width="100%"
      height="100%"
      name="react-app"
      url="http://localhost:3000/"
      sync={true}
      props={{
        jump
      }}
      beforeLoad={() => console.log('beforeLoad')}
      beforeMount={() => console.log('beforeMount')}
      afterMount={() => console.log('afterMount')}
      beforeUnmount={() => console.log('beforeUnmount')}
      afterUnmount={() => console.log('afterUnmount')}
    />
  );
}
```

## 沙箱迁移

### iframe 沙箱

```typescript
// wujie iframe 沙箱配置
setupApp({
  name: 'iframe-app',
  url: 'http://localhost:3000/',
  exec: false, // 使用 iframe 沙箱
  alive: true
});

// Micro-Core 兼容插件 - 完全相同
setupApp({
  name: 'iframe-app',
  url: 'http://localhost:3000/',
  exec: false, // 使用 iframe 沙箱
  alive: true
});
```

### WebComponent 沙箱

```typescript
// wujie WebComponent 沙箱配置
setupApp({
  name: 'webcomponent-app',
  url: 'http://localhost:3000/',
  exec: true,
  degrade: false // 使用 WebComponent 沙箱
});

// Micro-Core 兼容插件 - 完全相同
setupApp({
  name: 'webcomponent-app',
  url: 'http://localhost:3000/',
  exec: true,
  degrade: false // 使用 WebComponent 沙箱
});
```

## 渐进式迁移策略

### 第一步：安装兼容插件

```bash
pnpm add @micro-core/plugin-wujie-compat
```

### 第二步：替换导入

```typescript
// 只需要修改导入语句
import { setupApp, startApp, bus } from '@micro-core/plugin-wujie-compat';
```

### 第三步：验证功能

确保所有功能正常工作后，可以考虑逐步迁移到 Micro-Core 原生 API。

### 第四步：逐步迁移

```typescript
// 可以逐步引入 Micro-Core 原生功能
import { MicroCoreKernel } from '@micro-core/core';
import { setupApp, startApp } from '@micro-core/plugin-wujie-compat';

// 创建 Micro-Core 内核实例
const kernel = new MicroCoreKernel();

// 使用兼容插件
setupApp({ ... });
startApp({ ... });

// 同时可以使用 Micro-Core 原生插件
kernel.use(SomeAdvancedPlugin);
```

## 性能优化

### 预加载优化

```typescript
// wujie 预加载
preloadApp({
  name: 'vue-app',
  url: 'http://localhost:8080/'
});

// Micro-Core 兼容插件 - 支持更多预加载策略
preloadApp({
  name: 'vue-app',
  url: 'http://localhost:8080/',
  // 新增：智能预加载策略
  strategy: 'intelligent', // 'immediate' | 'idle' | 'intelligent'
  // 新增：预加载优先级
  priority: 'high' // 'low' | 'normal' | 'high'
});
```

## 常见问题

### Q: 迁移后性能有提升吗？

A: 是的，Micro-Core 提供了多项性能优化：
- 更高效的沙箱实现
- 智能预加载机制
- 更好的资源缓存策略
- Worker/WASM 支持

### Q: iframe 沙箱还支持吗？

A: 完全支持，并且提供了更好的性能和兼容性。

### Q: 事件总线通信会有变化吗？

A: 不会，API 完全兼容，但底层实现更高效。

### Q: 可以混用 wujie 和 Micro-Core 原生 API 吗？

A: 可以，兼容插件支持渐进式迁移。

## 下一步

- 查看 [完整示例](./wujie/complete-example.md)
- 了解 [API 对照表](./wujie/api-mapping.md)
- 学习 [沙箱迁移](./wujie/sandbox-migration.md)
- 探索 [Micro-Core 原生功能](../guide/advanced/)
