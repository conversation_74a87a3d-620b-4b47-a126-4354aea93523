# 交互式教程演练场

Micro-Core 交互式教程演练场提供了循序渐进的学习体验，通过实际操作帮助您掌握微前端开发的核心概念和最佳实践。

## 📋 目录

- [教程概述](#教程概述)
- [新手入门教程](#新手入门教程)
- [进阶开发教程](#进阶开发教程)
- [高级特性教程](#高级特性教程)
- [实战项目教程](#实战项目教程)
- [故障排除教程](#故障排除教程)

## 教程概述

### 🎯 学习路径

```typescript
// 学习路径配置
const learningPath = {
  beginner: {
    name: '新手入门',
    duration: '2-3 小时',
    prerequisites: ['HTML/CSS 基础', 'JavaScript 基础'],
    topics: [
      '微前端概念',
      '环境搭建',
      '第一个微应用',
      '应用注册和路由',
      '基础通信'
    ]
  },
  
  intermediate: {
    name: '进阶开发',
    duration: '4-6 小时',
    prerequisites: ['完成新手教程', '熟悉一种前端框架'],
    topics: [
      '多框架集成',
      '状态管理',
      '沙箱配置',
      '性能优化',
      '错误处理'
    ]
  },
  
  advanced: {
    name: '高级特性',
    duration: '6-8 小时',
    prerequisites: ['完成进阶教程', '项目开发经验'],
    topics: [
      '插件开发',
      '自定义适配器',
      '构建集成',
      '监控和调试',
      '部署策略'
    ]
  },
  
  project: {
    name: '实战项目',
    duration: '8-12 小时',
    prerequisites: ['完成所有基础教程'],
    topics: [
      '电商平台搭建',
      '管理后台开发',
      '多团队协作',
      '生产部署',
      '运维监控'
    ]
  }
}
```

### 🚀 教程界面

```vue
<template>
  <div class="tutorial-playground">
    <div class="playground-header">
      <h1>Micro-Core 交互式教程</h1>
      <div class="progress-indicator">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: overallProgress + '%' }"></div>
        </div>
        <span>总进度: {{ overallProgress }}%</span>
      </div>
    </div>
    
    <div class="playground-content">
      <div class="tutorial-sidebar">
        <div class="learning-paths">
          <div 
            v-for="(path, key) in learningPaths" 
            :key="key"
            :class="['path-card', { active: currentPath === key, completed: path.completed }]"
            @click="selectPath(key)"
          >
            <div class="path-header">
              <h3>{{ path.name }}</h3>
              <span class="path-duration">{{ path.duration }}</span>
            </div>
            <div class="path-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: path.progress + '%' }"></div>
              </div>
              <span>{{ path.progress }}%</span>
            </div>
            <div class="path-topics">
              <span v-for="topic in path.topics.slice(0, 3)" :key="topic" class="topic-tag">
                {{ topic }}
              </span>
              <span v-if="path.topics.length > 3" class="more-topics">
                +{{ path.topics.length - 3 }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="chapter-list" v-if="currentPath">
          <h3>章节列表</h3>
          <div 
            v-for="(chapter, index) in currentPathData.chapters" 
            :key="index"
            :class="['chapter-item', { 
              active: currentChapter === index, 
              completed: chapter.completed,
              locked: chapter.locked 
            }]"
            @click="selectChapter(index)"
          >
            <div class="chapter-number">{{ index + 1 }}</div>
            <div class="chapter-info">
              <h4>{{ chapter.title }}</h4>
              <p>{{ chapter.description }}</p>
              <div class="chapter-meta">
                <span class="duration">{{ chapter.duration }}</span>
                <span class="difficulty">{{ chapter.difficulty }}</span>
              </div>
            </div>
            <div class="chapter-status">
              <i v-if="chapter.completed" class="icon-check"></i>
              <i v-else-if="chapter.locked" class="icon-lock"></i>
              <i v-else class="icon-play"></i>
            </div>
          </div>
        </div>
      </div>
      
      <div class="tutorial-main">
        <div class="tutorial-header">
          <h2>{{ currentChapterData?.title }}</h2>
          <div class="tutorial-controls">
            <button @click="prevStep" :disabled="currentStep === 0">
              上一步
            </button>
            <span class="step-indicator">
              {{ currentStep + 1 }} / {{ currentChapterData?.steps.length }}
            </span>
            <button @click="nextStep" :disabled="currentStep >= currentChapterData?.steps.length - 1">
              下一步
            </button>
          </div>
        </div>
        
        <div class="tutorial-content">
          <div class="step-content">
            <component 
              :is="currentStepComponent" 
              :step-data="currentStepData"
              @step-completed="onStepCompleted"
              @code-executed="onCodeExecuted"
            />
          </div>
          
          <div class="interactive-area">
            <div class="code-editor">
              <div class="editor-header">
                <span>代码编辑器</span>
                <div class="editor-controls">
                  <button @click="runCode" :disabled="!canRunCode">运行</button>
                  <button @click="resetCode">重置</button>
                  <button @click="getHint" v-if="showHintButton">提示</button>
                </div>
              </div>
              <textarea 
                v-model="currentCode" 
                class="code-textarea"
                :placeholder="codePlaceholder"
              ></textarea>
            </div>
            
            <div class="preview-area">
              <div class="preview-header">
                <span>预览结果</span>
                <div class="preview-controls">
                  <button @click="clearPreview">清除</button>
                  <button @click="fullscreenPreview">全屏</button>
                </div>
              </div>
              <iframe 
                ref="previewFrame" 
                class="preview-iframe"
                src="about:blank"
              ></iframe>
            </div>
          </div>
        </div>
        
        <div class="tutorial-footer">
          <div class="step-navigation">
            <button 
              v-if="currentStep > 0" 
              @click="prevStep" 
              class="nav-btn prev"
            >
              ← 上一步
            </button>
            
            <button 
              v-if="currentStep < currentChapterData?.steps.length - 1" 
              @click="nextStep" 
              class="nav-btn next"
              :disabled="!currentStepCompleted"
            >
              下一步 →
            </button>
            
            <button 
              v-if="currentStep === currentChapterData?.steps.length - 1" 
              @click="completeChapter" 
              class="nav-btn complete"
              :disabled="!currentStepCompleted"
            >
              完成章节 ✓
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ConceptStep from './components/ConceptStep.vue'
import CodeStep from './components/CodeStep.vue'
import InteractiveStep from './components/InteractiveStep.vue'
import QuizStep from './components/QuizStep.vue'

const currentPath = ref('beginner')
const currentChapter = ref(0)
const currentStep = ref(0)
const currentCode = ref('')
const currentStepCompleted = ref(false)

const learningPaths = ref({
  beginner: {
    name: '新手入门',
    duration: '2-3 小时',
    progress: 0,
    completed: false,
    topics: ['微前端概念', '环境搭建', '第一个微应用', '应用注册', '基础通信'],
    chapters: []
  },
  intermediate: {
    name: '进阶开发',
    duration: '4-6 小时',
    progress: 0,
    completed: false,
    topics: ['多框架集成', '状态管理', '沙箱配置', '性能优化'],
    chapters: []
  },
  advanced: {
    name: '高级特性',
    duration: '6-8 小时',
    progress: 0,
    completed: false,
    topics: ['插件开发', '自定义适配器', '构建集成', '监控调试'],
    chapters: []
  },
  project: {
    name: '实战项目',
    duration: '8-12 小时',
    progress: 0,
    completed: false,
    topics: ['电商平台', '管理后台', '多团队协作', '生产部署'],
    chapters: []
  }
})

const previewFrame = ref<HTMLIFrameElement>()

const overallProgress = computed(() => {
  const paths = Object.values(learningPaths.value)
  const totalProgress = paths.reduce((sum, path) => sum + path.progress, 0)
  return Math.round(totalProgress / paths.length)
})

const currentPathData = computed(() => {
  return learningPaths.value[currentPath.value]
})

const currentChapterData = computed(() => {
  return currentPathData.value?.chapters[currentChapter.value]
})

const currentStepData = computed(() => {
  return currentChapterData.value?.steps[currentStep.value]
})

const currentStepComponent = computed(() => {
  const stepType = currentStepData.value?.type
  const components = {
    concept: ConceptStep,
    code: CodeStep,
    interactive: InteractiveStep,
    quiz: QuizStep
  }
  return components[stepType] || ConceptStep
})

const canRunCode = computed(() => {
  return currentStepData.value?.type === 'code' && currentCode.value.trim()
})

const codePlaceholder = computed(() => {
  return currentStepData.value?.codePlaceholder || '在这里编写代码...'
})

const showHintButton = computed(() => {
  return currentStepData.value?.hasHint && !currentStepCompleted.value
})

onMounted(() => {
  initializeTutorials()
})

const initializeTutorials = () => {
  // 初始化新手教程
  learningPaths.value.beginner.chapters = [
    {
      title: '微前端基础概念',
      description: '了解微前端的核心概念和优势',
      duration: '30分钟',
      difficulty: '入门',
      completed: false,
      locked: false,
      steps: [
        {
          type: 'concept',
          title: '什么是微前端',
          content: `微前端是一种架构模式，将大型前端应用拆分为多个独立的、可部署的小应用。

## 核心概念

1. **应用拆分**: 按业务域拆分应用
2. **独立开发**: 各团队独立开发和部署
3. **技术栈自由**: 每个应用可选择不同技术栈
4. **运行时集成**: 在浏览器中动态组合应用`,
          hasQuiz: true
        },
        {
          type: 'interactive',
          title: '微前端架构演示',
          content: '通过交互式图表了解微前端架构',
          interactiveType: 'architecture-diagram'
        },
        {
          type: 'quiz',
          title: '概念检测',
          questions: [
            {
              question: '微前端的主要优势是什么？',
              options: [
                '技术栈统一',
                '团队独立开发',
                '代码共享',
                '性能提升'
              ],
              correct: 1
            }
          ]
        }
      ]
    },
    {
      title: '环境搭建',
      description: '搭建 Micro-Core 开发环境',
      duration: '20分钟',
      difficulty: '入门',
      completed: false,
      locked: false,
      steps: [
        {
          type: 'code',
          title: '安装 Micro-Core',
          content: '让我们开始安装 Micro-Core 和相关依赖',
          codeTemplate: `# 创建新项目
mkdir my-micro-frontend
cd my-micro-frontend

# 初始化项目
npm init -y

# 安装 Micro-Core
npm install @micro-core/core

# 安装开发依赖
npm install -D vite @vitejs/plugin-vue`,
          expectedOutput: 'Package installed successfully',
          hasHint: true,
          hint: '确保 Node.js 版本 >= 16'
        }
      ]
    }
  ]
  
  // 初始化其他教程路径...
  initializeIntermediateTutorials()
  initializeAdvancedTutorials()
  initializeProjectTutorials()
}

const initializeIntermediateTutorials = () => {
  learningPaths.value.intermediate.chapters = [
    {
      title: '多框架集成',
      description: '学习如何集成 React、Vue、Angular 应用',
      duration: '45分钟',
      difficulty: '中级',
      completed: false,
      locked: true,
      steps: [
        {
          type: 'concept',
          title: '框架适配器概念',
          content: '了解 Micro-Core 如何支持多种前端框架'
        },
        {
          type: 'code',
          title: '创建 React 微应用',
          content: '使用 React 适配器创建微应用',
          codeTemplate: `import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'

const microCore = new MicroCore({
  adapters: [new ReactAdapter()]
})

// 注册 React 应用
await microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react-app',
  framework: 'react'
})`
        }
      ]
    }
  ]
}

const initializeAdvancedTutorials = () => {
  learningPaths.value.advanced.chapters = [
    {
      title: '插件开发',
      description: '学习如何开发自定义插件',
      duration: '60分钟',
      difficulty: '高级',
      completed: false,
      locked: true,
      steps: [
        {
          type: 'concept',
          title: '插件系统架构',
          content: '深入了解 Micro-Core 插件系统的设计原理'
        },
        {
          type: 'code',
          title: '创建自定义插件',
          content: '开发一个简单的日志插件',
          codeTemplate: `import { Plugin } from '@micro-core/core'

export class LoggerPlugin extends Plugin {
  name = 'logger'
  
  install(microCore) {
    // 监听应用生命周期事件
    microCore.on('app:load:start', (appName) => {
      console.log(\`[Logger] 开始加载应用: \${appName}\`)
    })
    
    microCore.on('app:load:end', (appName) => {
      console.log(\`[Logger] 应用加载完成: \${appName}\`)
    })
  }
}`
        }
      ]
    }
  ]
}

const initializeProjectTutorials = () => {
  learningPaths.value.project.chapters = [
    {
      title: '电商平台项目',
      description: '构建一个完整的电商微前端平台',
      duration: '3小时',
      difficulty: '实战',
      completed: false,
      locked: true,
      steps: [
        {
          type: 'concept',
          title: '项目架构设计',
          content: '设计电商平台的微前端架构'
        },
        {
          type: 'code',
          title: '主应用搭建',
          content: '创建电商平台的主应用框架'
        }
      ]
    }
  ]
}

const selectPath = (pathKey: string) => {
  currentPath.value = pathKey
  currentChapter.value = 0
  currentStep.value = 0
  currentStepCompleted.value = false
}

const selectChapter = (chapterIndex: number) => {
  const chapter = currentPathData.value.chapters[chapterIndex]
  if (!chapter.locked) {
    currentChapter.value = chapterIndex
    currentStep.value = 0
    currentStepCompleted.value = false
  }
}

const nextStep = () => {
  if (currentStep.value < currentChapterData.value.steps.length - 1) {
    currentStep.value++
    currentStepCompleted.value = false
    loadStepCode()
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
    currentStepCompleted.value = true
    loadStepCode()
  }
}

const loadStepCode = () => {
  const stepData = currentStepData.value
  if (stepData?.type === 'code' && stepData.codeTemplate) {
    currentCode.value = stepData.codeTemplate
  }
}

const runCode = () => {
  if (canRunCode.value) {
    executeCode(currentCode.value)
  }
}

const executeCode = (code: string) => {
  // 在预览框架中执行代码
  const previewHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>代码预览</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      padding: 20px;
      margin: 0;
    }
    .output { 
      background: #f5f5f5; 
      padding: 15px; 
      border-radius: 5px; 
      margin-top: 10px;
    }
    .error { 
      color: red; 
      background: #fee; 
      border: 1px solid #fcc; 
    }
  </style>
</head>
<body>
  <h3>代码执行结果</h3>
  <div id="output" class="output"></div>
  <script>
    try {
      ${code}
      document.getElementById('output').innerHTML = '<p>✅ 代码执行成功</p>'