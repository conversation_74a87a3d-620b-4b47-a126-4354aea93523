# 性能测试演练场

Micro-Core 性能测试演练场提供了全面的性能测试工具和基准测试，帮助您评估和优化微前端应用的性能表现。

## 📋 目录

- [测试概述](#测试概述)
- [加载性能测试](#加载性能测试)
- [运行时性能测试](#运行时性能测试)
- [内存使用测试](#内存使用测试)
- [网络性能测试](#网络性能测试)
- [用户体验测试](#用户体验测试)
- [基准对比测试](#基准对比测试)
- [性能优化建议](#性能优化建议)

## 测试概述

### 🎯 性能测试指标

```typescript
// 性能测试指标体系
const performanceMetrics = {
  // 加载性能
  loading: {
    firstContentfulPaint: 'FCP - 首次内容绘制',
    largestContentfulPaint: 'LCP - 最大内容绘制',
    firstInputDelay: 'FID - 首次输入延迟',
    cumulativeLayoutShift: 'CLS - 累积布局偏移',
    timeToInteractive: 'TTI - 可交互时间'
  },
  
  // 运行时性能
  runtime: {
    frameRate: '帧率 (FPS)',
    memoryUsage: '内存使用量',
    cpuUsage: 'CPU 使用率',
    networkLatency: '网络延迟',
    cacheHitRate: '缓存命中率'
  },
  
  // 微前端特定指标
  microfrontend: {
    appLoadTime: '应用加载时间',
    appSwitchTime: '应用切换时间',
    sandboxOverhead: '沙箱开销',
    communicationLatency: '通信延迟',
    routeChangeTime: '路由切换时间'
  }
}
```

### 🚀 性能测试界面

```vue
<template>
  <div class="performance-test-playground">
    <div class="playground-header">
      <h1>Micro-Core 性能测试演练场</h1>
      <div class="test-controls">
        <button @click="startAllTests" :disabled="testing">
          {{ testing ? '测试中...' : '开始全面测试' }}
        </button>
        <button @click="clearResults">清除结果</button>
        <button @click="exportResults">导出报告</button>
      </div>
    </div>
    
    <div class="playground-content">
      <div class="test-categories">
        <div class="category-card">
          <h3>加载性能测试</h3>
          <div class="test-metrics">
            <div class="metric-item">
              <span>FCP</span>
              <span :class="getScoreClass(metrics.fcp)">
                {{ metrics.fcp || '--' }}ms
              </span>
            </div>
            <div class="metric-item">
              <span>LCP</span>
              <span :class="getScoreClass(metrics.lcp)">
                {{ metrics.lcp || '--' }}ms
              </span>
            </div>
            <div class="metric-item">
              <span>TTI</span>
              <span :class="getScoreClass(metrics.tti)">
                {{ metrics.tti || '--' }}ms
              </span>
            </div>
          </div>
          <button @click="runLoadingTests" :disabled="testing">
            运行加载测试
          </button>
        </div>
        
        <div class="category-card">
          <h3>运行时性能测试</h3>
          <div class="test-metrics">
            <div class="metric-item">
              <span>FPS</span>
              <span :class="getScoreClass(metrics.fps, 'fps')">
                {{ metrics.fps || '--' }}
              </span>
            </div>
            <div class="metric-item">
              <span>内存</span>
              <span :class="getScoreClass(metrics.memory, 'memory')">
                {{ metrics.memory || '--' }}MB
              </span>
            </div>
            <div class="metric-item">
              <span>CPU</span>
              <span :class="getScoreClass(metrics.cpu, 'cpu')">
                {{ metrics.cpu || '--' }}%
              </span>
            </div>
          </div>
          <button @click="runRuntimeTests" :disabled="testing">
            运行运行时测试
          </button>
        </div>
        
        <div class="category-card">
          <h3>微前端性能测试</h3>
          <div class="test-metrics">
            <div class="metric-item">
              <span>应用加载</span>
              <span :class="getScoreClass(metrics.appLoad)">
                {{ metrics.appLoad || '--' }}ms
              </span>
            </div>
            <div class="metric-item">
              <span>应用切换</span>
              <span :class="getScoreClass(metrics.appSwitch)">
                {{ metrics.appSwitch || '--' }}ms
              </span>
            </div>
            <div class="metric-item">
              <span>通信延迟</span>
              <span :class="getScoreClass(metrics.communication)">
                {{ metrics.communication || '--' }}ms
              </span>
            </div>
          </div>
          <button @click="runMicrofrontendTests" :disabled="testing">
            运行微前端测试
          </button>
        </div>
      </div>
      
      <div class="test-results">
        <div class="results-header">
          <h3>测试结果</h3>
          <div class="overall-score">
            <span>综合评分</span>
            <span :class="getOverallScoreClass(overallScore)">
              {{ overallScore || '--' }}
            </span>
          </div>
        </div>
        
        <div class="results-charts">
          <canvas ref="performanceChart" width="400" height="200"></canvas>
          <canvas ref="comparisonChart" width="400" height="200"></canvas>
        </div>
        
        <div class="results-details">
          <div class="detail-section">
            <h4>详细指标</h4>
            <table class="metrics-table">
              <thead>
                <tr>
                  <th>指标</th>
                  <th>当前值</th>
                  <th>基准值</th>
                  <th>评分</th>
                  <th>建议</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="metric in detailedMetrics" :key="metric.name">
                  <td>{{ metric.name }}</td>
                  <td>{{ metric.current }}</td>
                  <td>{{ metric.baseline }}</td>
                  <td :class="getScoreClass(metric.score)">
                    {{ metric.score }}
                  </td>
                  <td>{{ metric.suggestion }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="detail-section">
            <h4>性能时间线</h4>
            <div class="timeline">
              <div 
                v-for="event in performanceTimeline" 
                :key="event.id"
                class="timeline-event"
                :style="{ left: (event.time / maxTime * 100) + '%' }"
              >
                <div class="event-marker"></div>
                <div class="event-label">{{ event.name }}</div>
                <div class="event-time">{{ event.time }}ms</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

const testing = ref(false)
const metrics = ref({})
const overallScore = ref(0)
const detailedMetrics = ref([])
const performanceTimeline = ref([])
const performanceChart = ref<HTMLCanvasElement>()
const comparisonChart = ref<HTMLCanvasElement>()

const maxTime = computed(() => {
  return Math.max(...performanceTimeline.value.map(e => e.time), 1000)
})

onMounted(() => {
  initializeCharts()
})

const startAllTests = async () => {
  testing.value = true
  
  try {
    await runLoadingTests()
    await runRuntimeTests()
    await runMicrofrontendTests()
    
    calculateOverallScore()
    updateCharts()
  } finally {
    testing.value = false
  }
}

const runLoadingTests = async () => {
  console.log('运行加载性能测试...')
  
  // 模拟 FCP 测试
  const fcpStart = performance.now()
  await simulatePageLoad()
  const fcpTime = performance.now() - fcpStart
  
  // 模拟 LCP 测试
  const lcpTime = fcpTime + Math.random() * 500
  
  // 模拟 TTI 测试
  const ttiTime = lcpTime + Math.random() * 1000
  
  metrics.value = {
    ...metrics.value,
    fcp: Math.round(fcpTime),
    lcp: Math.round(lcpTime),
    tti: Math.round(ttiTime)
  }
  
  // 添加到时间线
  performanceTimeline.value.push(
    { id: 'fcp', name: 'FCP', time: Math.round(fcpTime) },
    { id: 'lcp', name: 'LCP', time: Math.round(lcpTime) },
    { id: 'tti', name: 'TTI', time: Math.round(ttiTime) }
  )
}

const runRuntimeTests = async () => {
  console.log('运行运行时性能测试...')
  
  // 模拟 FPS 测试
  const fps = await measureFPS()
  
  // 模拟内存使用测试
  const memory = measureMemoryUsage()
  
  // 模拟 CPU 使用测试
  const cpu = await measureCPUUsage()
  
  metrics.value = {
    ...metrics.value,
    fps: Math.round(fps),
    memory: Math.round(memory),
    cpu: Math.round(cpu)
  }
}

const runMicrofrontendTests = async () => {
  console.log('运行微前端性能测试...')
  
  // 模拟应用加载测试
  const appLoadTime = await measureAppLoadTime()
  
  // 模拟应用切换测试
  const appSwitchTime = await measureAppSwitchTime()
  
  // 模拟通信延迟测试
  const communicationLatency = await measureCommunicationLatency()
  
  metrics.value = {
    ...metrics.value,
    appLoad: Math.round(appLoadTime),
    appSwitch: Math.round(appSwitchTime),
    communication: Math.round(communicationLatency)
  }
}

const simulatePageLoad = () => {
  return new Promise(resolve => {
    setTimeout(resolve, Math.random() * 1000 + 500)
  })
}

const measureFPS = async () => {
  return new Promise(resolve => {
    let frames = 0
    const startTime = performance.now()
    
    const countFrame = () => {
      frames++
      if (performance.now() - startTime < 1000) {
        requestAnimationFrame(countFrame)
      } else {
        resolve(frames)
      }
    }
    
    requestAnimationFrame(countFrame)
  })
}

const measureMemoryUsage = () => {
  if ('memory' in performance) {
    return (performance as any).memory.usedJSHeapSize / 1024 / 1024
  }
  return Math.random() * 50 + 20
}

const measureCPUUsage = async () => {
  // 模拟 CPU 使用率测试
  const start = performance.now()
  let iterations = 0
  
  while (performance.now() - start < 100) {
    iterations++
  }
  
  return Math.min(iterations / 10000 * 100, 100)
}

const measureAppLoadTime = async () => {
  const start = performance.now()
  
  // 模拟应用加载
  await new Promise(resolve => setTimeout(resolve, Math.random() * 800 + 200))
  
  return performance.now() - start
}

const measureAppSwitchTime = async () => {
  const start = performance.now()
  
  // 模拟应用切换
  await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100))
  
  return performance.now() - start
}

const measureCommunicationLatency = async () => {
  const start = performance.now()
  
  // 模拟通信延迟
  await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10))
  
  return performance.now() - start
}

const calculateOverallScore = () => {
  const scores = []
  
  // FCP 评分 (< 1800ms 为优秀)
  if (metrics.value.fcp) {
    scores.push(Math.max(0, 100 - (metrics.value.fcp - 1000) / 10))
  }
  
  // LCP 评分 (< 2500ms 为优秀)
  if (metrics.value.lcp) {
    scores.push(Math.max(0, 100 - (metrics.value.lcp - 1500) / 15))
  }
  
  // FPS 评分 (> 55 为优秀)
  if (metrics.value.fps) {
    scores.push(Math.min(100, metrics.value.fps / 60 * 100))
  }
  
  // 应用加载评分 (< 500ms 为优秀)
  if (metrics.value.appLoad) {
    scores.push(Math.max(0, 100 - (metrics.value.appLoad - 300) / 5))
  }
  
  overallScore.value = Math.round(
    scores.reduce((a, b) => a + b, 0) / scores.length
  )
  
  // 更新详细指标
  updateDetailedMetrics()
}

const updateDetailedMetrics = () => {
  detailedMetrics.value = [
    {
      name: 'First Contentful Paint',
      current: `${metrics.value.fcp}ms`,
      baseline: '1800ms',
      score: getMetricScore(metrics.value.fcp, 1800, 'lower'),
      suggestion: metrics.value.fcp > 1800 ? '优化资源加载' : '表现良好'
    },
    {
      name: 'Largest Contentful Paint',
      current: `${metrics.value.lcp}ms`,
      baseline: '2500ms',
      score: getMetricScore(metrics.value.lcp, 2500, 'lower'),
      suggestion: metrics.value.lcp > 2500 ? '优化图片和字体' : '表现良好'
    },
    {
      name: 'Frame Rate',
      current: `${metrics.value.fps}`,
      baseline: '60',
      score: getMetricScore(metrics.value.fps, 60, 'higher'),
      suggestion: metrics.value.fps < 55 ? '优化渲染性能' : '表现良好'
    },
    {
      name: '应用加载时间',
      current: `${metrics.value.appLoad}ms`,
      baseline: '500ms',
      score: getMetricScore(metrics.value.appLoad, 500, 'lower'),
      suggestion: metrics.value.appLoad > 500 ? '启用预加载' : '表现良好'
    }
  ]
}

const getMetricScore = (value, baseline, type) => {
  if (!value) return '--'
  
  if (type === 'lower') {
    return Math.max(0, Math.round(100 - (value - baseline) / baseline * 100))
  } else {
    return Math.min(100, Math.round(value / baseline * 100))
  }
}

const getScoreClass = (score, type = 'time') => {
  if (!score || score === '--') return 'score-unknown'
  
  if (type === 'fps') {
    if (score >= 55) return 'score-excellent'
    if (score >= 45) return 'score-good'
    if (score >= 30) return 'score-fair'
    return 'score-poor'
  }
  
  if (type === 'memory') {
    if (score <= 30) return 'score-excellent'
    if (score <= 50) return 'score-good'
    if (score <= 80) return 'score-fair'
    return 'score-poor'
  }
  
  if (type === 'cpu') {
    if (score <= 20) return 'score-excellent'
    if (score <= 40) return 'score-good'
    if (score <= 60) return 'score-fair'
    return 'score-poor'
  }
  
  // 默认时间类型评分
  if (score >= 90) return 'score-excellent'
  if (score >= 70) return 'score-good'
  if (score >= 50) return 'score-fair'
  return 'score-poor'
}

const getOverallScoreClass = (score) => {
  if (!score) return 'score-unknown'
  if (score >= 90) return 'score-excellent'
  if (score >= 70) return 'score-good'
  if (score >= 50) return 'score-fair'
  return 'score-poor'
}

const clearResults = () => {
  metrics.value = {}
  overallScore.value = 0
  detailedMetrics.value = []
  performanceTimeline.value = []
  
  // 清除图表
  if (performanceChart.value) {
    const ctx = performanceChart.value.getContext('2d')
    ctx.clearRect(0, 0, performanceChart.value.width, performanceChart.value.height)
  }
  
  if (comparisonChart.value) {
    const ctx = comparisonChart.value.getContext('2d')
    ctx.clearRect(0, 0, comparisonChart.value.width, comparisonChart.value.height)
  }
}

const exportResults = () => {
  const report = {
    timestamp: new Date().toISOString(),
    overallScore: overallScore.value,
    metrics: metrics.value,
    detailedMetrics: detailedMetrics.value,
    timeline: performanceTimeline.value
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-report-${Date.now()}.json`
  a.click()
  
  URL.revokeObjectURL(url)
}

const initializeCharts = () => {
  // 初始化性能图表
  if (performanceChart.value) {
    drawPerformanceChart()
  }
  
  // 初始化对比图表
  if (comparisonChart.value) {
    drawComparisonChart()
  }
}

const updateCharts = () => {
  drawPerformanceChart()
  drawComparisonChart()
}

const drawPerformanceChart = () => {
  if (!performanceChart.value) return
  
  const ctx = performanceChart.value.getContext('2d')
  const canvas = performanceChart.value
  
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制性能趋势图
  const data = [
    metrics.value.fcp || 0,
    metrics.value.lcp || 0,
    metrics.value.tti || 0,
    metrics.value.appLoad || 0,
    metrics.value.appSwitch || 0
  ]
  
  const labels = ['FCP', 'LCP', 'TTI', 'App Load', 'App Switch']
  const maxValue = Math.max(...data, 1000)
  
  // 绘制标题
  ctx.fillStyle = '#333'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('性能指标趋势', canvas.width / 2, 20)
  
  // 绘制柱状图
  const barWidth = 60
  const barSpacing = 20
  const startX = (canvas.width - (data.length * barWidth + (data.length - 1) * barSpacing)) / 2
  
  data.forEach((value, index) => {
    const x = startX + index * (barWidth + barSpacing)
    const barHeight = (value / maxValue) * (canvas.height - 80)
    const y = canvas.height - 40 - barHeight
    
    // 绘制柱子
    ctx.fillStyle = getBarColor(value, labels[index])
    ctx.fillRect(x, y, barWidth, barHeight)
    
    // 绘制标签
    ctx.fillStyle = '#333'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(labels[index], x + barWidth / 2, canvas.height - 20)
    
    // 绘制数值
    ctx.fillText(`${value}ms`, x + barWidth / 2, y - 5)
  })
}

const drawComparisonChart = () => {
  if (!comparisonChart.value) return
  
  const ctx = comparisonChart.value.getContext('2d')
  const canvas = comparisonChart.value
  
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制对比图表
  const currentData = [
    metrics.value.fcp || 0,
    metrics.value.lcp || 0,
    metrics.value.appLoad || 0
  ]
  
  const baselineData = [1800, 2500, 500] // 基准值
  const labels = ['FCP', 'LCP', 'App Load']
  
  // 绘制标题
  ctx.fillStyle = '#333'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('性能对比 (当前 vs 基准)', canvas.width / 2, 20)
  
  // 绘制对比柱状图
  const barWidth = 25
  const groupSpacing = 60
  const startX = (canvas.width - (labels.length * groupSpacing)) / 2
  
  labels.forEach((label, index) => {
    const groupX = startX + index * groupSpacing
    const maxValue = Math.max(currentData[index], baselineData[index])
    
    // 当前值柱子
    const currentHeight = (currentData[index] / maxValue) * (canvas.height - 80)
    const currentY = canvas.height - 40 - currentHeight
    ctx.fillStyle = '#4ecdc4'
    ctx.fillRect(groupX, currentY, barWidth, currentHeight)
    
    // 基准值柱子
    const baselineHeight = (baselineData[index] / maxValue) * (canvas.height - 80)
    const baselineY = canvas.height - 40 - baselineHeight
    ctx.fillStyle = '#ff6b6b'
    ctx.fillRect(groupX + barWidth + 5, baselineY, barWidth, baselineHeight)
    
    // 标签
    ctx.fillStyle = '#333'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(label, groupX + barWidth, canvas.height - 20)
  })
  
  // 图例
  ctx.fillStyle = '#4ecdc4'
  ctx.fillRect(canvas.width - 120, 30, 15, 15)
  ctx.fillStyle = '#333'
  ctx.font = '12px Arial'
  ctx.textAlign = 'left'
  ctx.fillText('当前值', canvas.width - 100, 42)
  
  ctx.fillStyle = '#ff6b6b'
  ctx.fillRect(canvas.width - 120, 50, 15, 15)
  ctx.fillText('基准值', canvas.width - 100, 62)
}

const getBarColor = (value, label) => {
  const thresholds = {
    'FCP': 1800,
    'LCP': 2500,
    'TTI': 3000,
    'App Load': 500,
    'App Switch': 200
  }
  
  const threshold = thresholds[label] || 1000
  
  if (value <= threshold * 0.7) return '#4ecdc4' // 优秀
  if (value <= threshold) return '#45b7d1'        // 良好
  if (value <= threshold * 1.5) return '#f9ca24' // 一般
  return '#f0932b'                                // 需要优化
}
</script>
```

## 加载性能测试

### Core Web Vitals 测试

```typescript
// Core Web Vitals 性能测试
class CoreWebVitalsTest {
  private observer: PerformanceObserver
  private metrics: Map<string, number> = new Map()
  
  constructor() {
    this.setupObservers()
  }
  
  private setupObservers() {
    // FCP (First Contentful Paint) 观察器
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.set('FCP', entry.startTime)
          }
        })
      })
      
      this.observer.observe({ entryTypes: ['paint'] })
    }
    
    // LCP (Largest Contentful Paint) 观察器
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        this.metrics.set('LCP', lastEntry.startTime)
      })
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    }
    
    // FID (First Input Delay) 观察器
    if ('PerformanceObserver' in window) {
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.metrics.set('FID', entry.processingStart - entry.startTime)
        })
      })
      
      fidObserver.observe({ entryTypes: ['first-input'] })
    }
    
    // CLS (Cumulative Layout Shift) 观察器
    if ('PerformanceObserver' in window) {
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
            this.metrics.set('CLS', clsValue)
          }
        })
      })
      
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    }
  }
  
  // 获取所有指标
  getMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics)
  }
  
  // 评估性能等级
  evaluatePerformance(): Record<string, string> {
    const evaluation = {}
    
    // FCP 评估
    const fcp = this.metrics.get('FCP')
    if (fcp) {
      if (fcp <= 1800) evaluation.FCP = 'Good'
      else if (fcp <= 3000) evaluation.FCP = 'Needs Improvement'
      else evaluation.FCP = 'Poor'
    }
    
    // LCP 评估
    const lcp = this.metrics.get('LCP')
    if (lcp) {
      if (lcp <= 2500) evaluation.LCP = 'Good'
      else if (lcp <= 4000) evaluation.LCP = 'Needs Improvement'
      else evaluation.LCP = 'Poor'
    }
    
    // FID 评估
    const fid = this.metrics.get('FID')
    if (fid) {
      if (fid <= 100) evaluation.FID = 'Good'
      else if (fid <= 300) evaluation.FID = 'Needs Improvement'
      else evaluation.FID = 'Poor'
    }
    
    // CLS 评估
    const cls = this.metrics.get('CLS')
    if (cls) {
      if (cls <= 0.1) evaluation.CLS = 'Good'
      else if (cls <= 0.25) evaluation.CLS = 'Needs Improvement'
      else evaluation.CLS = 'Poor'
    }
    
    return evaluation
  }
}
```

### 微前端加载性能测试

```typescript
// 微前端特定的加载性能测试
class MicrofrontendLoadingTest {
  private microCore: MicroCore
  private loadingMetrics: Map<string, any> = new Map()
  
  constructor(microCore: MicroCore) {
    this.microCore = microCore
    this.setupMicrofrontendObservers()
  }
  
  private setupMicrofrontendObservers() {
    // 应用加载开始
    this.microCore.on('app:load:start', (appName) => {
      this.loadingMetrics.set(`${appName}:start`, performance.now())
    })
    
    // 应用加载完成
    this.microCore.on('app:load:end', (appName) => {
      const startTime = this.loadingMetrics.get(`${appName}:start`)
      if (startTime) {
        const loadTime = performance.now() - startTime
        this.loadingMetrics.set(`${appName}:loadTime`, loadTime)
      }
    })
    
    // 应用挂载开始
    this.microCore.on('app:mount:start', (appName) => {
      this.loadingMetrics.set(`${appName}:mountStart`, performance.now())
    })
    
    // 应用挂载完成
    this.microCore.on('app:mount:end', (appName) => {
      const mountStart = this.loadingMetrics.get(`${appName}:mountStart`)
      if (mountStart) {
        const mountTime = performance.now() - mountStart
        this.loadingMetrics.set(`${appName}:mountTime`, mountTime)
      }
    })
  }
  
  // 测试应用切换性能
  async testAppSwitching(fromApp: string, toApp: string): Promise<number> {
    const startTime = performance.now()
    
    // 卸载当前应用
    if (fromApp) {
      await this.microCore.unmountApp(fromApp)
    }
    
    // 加载新应用
    await this.microCore.loadApp(toApp)
    
    const switchTime = performance.now() - startTime
    this.loadingMetrics.set(`switch:${fromApp}->${toApp}`, switchTime)
    
    return switchTime
  }
  
  // 测试并发加载性能
  async testConcurrentLoading(appNames: string[]): Promise<Record<string, number>> {
    const startTime = performance.now()
    const results = {}
    
    // 并发加载所有应用
    const loadPromises = appNames.map(async (appName) => {
      const appStartTime = performance.now()
      await this.microCore.loadApp(appName)
      const appLoadTime = performance.now() - appStartTime
      results[appName] = appLoadTime
      return appLoadTime
    })
    
    await Promise.all(loadPromises)
    
    const totalTime = performance.now() - startTime
    results['total'] = totalTime
    
    return results
  }
  
  // 获取加载性能报告
  getLoadingReport(): any {
    const report = {
      individualApps: {},
      switching: {},
      averages: {}
    }
    
    // 整理单个应用的加载数据
    for (const [key, value] of this.loadingMetrics.entries()) {
      if (key.includes(':loadTime')) {
        const appName = key.split(':')[0]
        report.individualApps[appName] = {
          loadTime: value,
          mountTime: this.loadingMetrics.get(`${appName}:mountTime`) || 0
        }
      } else if (key.includes('switch:')) {
        report.switching[key.replace('switch:', '')] = value
      }
    }
    
    // 计算平均值
    const loadTimes = Object.values(report.individualApps).map((app: any) => app.loadTime)
    const switchTimes = Object.values(report.switching)
    
    report.averages = {
      loadTime: loadTimes.length > 0 ? loadTimes.reduce((a, b) => a + b) / loadTimes.length : 0,
      switchTime: switchTimes.length > 0 ? switchTimes.reduce((a, b) => a + b) / switchTimes.length : 0
    }
    
    return report
  }
}
```

## 运行时性能测试

### 帧率和渲染性能测试

```typescript
// 帧率和渲染性能测试
class RenderingPerformanceTest {
  private frameCount = 0
  private lastTime = 0
  private fps = 0
  private isRunning = false
  
  // 开始 FPS 监控
  startFPSMonitoring(): void {
    this.isRunning = true
    this.frameCount = 0
    this.lastTime = performance.now()
    this.measureFPS()
  }
  
  // 停止 FPS 监控
  stopFPSMonitoring(): number {
    this.isRunning = false
    return this.fps
  }
  
  private measureFPS(): void {
    if (!this.isRunning) return
    
    this.frameCount++
    const currentTime = performance.now()
    
    if (currentTime - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime))
      this.frameCount = 0
      this.lastTime = currentTime
    }
    
    requestAnimationFrame(() => this.measureFPS())
  }
  
  // 测试渲染压力
  async testRenderingStress(duration: number = 5000): Promise<any> {
    const results = {
      minFPS: Infinity,
      maxFPS: 0,
      avgFPS: 0,
      frameDrops: 0,
      samples: []
    }
    
    const startTime = performance.now()
    let sampleCount = 0
    let totalFPS = 0
    
    const sampleFPS = () => {
      if (performance.now() - startTime >= duration) {
        results.avgFPS = Math.round(totalFPS / sampleCount)
        return results
      }
      
      const currentFPS = this.fps
      results.samples.push(currentFPS)
      
      if (currentFPS < results.minFPS) results.minFPS = currentFPS
      if (currentFPS > results.maxFPS) results.maxFPS = currentFPS
      if (currentFPS < 30) results.frameDrops++
      
      totalFPS += currentFPS
      sampleCount++
      
      setTimeout(sampleFPS, 100) // 每100ms采样一次
    }
    
    this.startFPSMonitoring()
    sampleFPS()
    
    return new Promise(resolve => {
      setTimeout(() => {
        this.stopFPSMonitoring()
        resolve(results)
      }, duration)
    })
  }
  
  // 测试长列表渲染性能
  testLongListRendering(itemCount: number): Promise<number> {
    return new Promise(resolve => {
      const startTime = performance.now()
      
      // 创建长列表
      const container = document.createElement('div')
      container.style.height = '400px'
      container.style.overflow = 'auto'
      document.body.appendChild(container)
      
      for (let i = 0; i < itemCount; i++) {
        const item = document.createElement('div')
        item.textContent = `Item ${i}`
        item.style.height = '50px'
        item.style.borderBottom = '1px solid #ccc'
        container.appendChild(item)
      }
      
      // 测量渲染时间
      requestAnimationFrame(() => {
        const renderTime = performance.now() - startTime
        document.body.removeChild(container)
        resolve(renderTime)
      })
    })
  }
}
```

## 内存使用测试

### 内存泄漏检测

```typescript
// 内存使用和泄漏检测
class MemoryPerformanceTest {
  private initialMemory: number = 0
  private memorySnapshots: Array<{
    timestamp: number
    used: number
    total: number
    limit: number
  }> = []
  
  // 开始内存监控
  startMemoryMonitoring(): void {
    if ('memory' in performance) {
      this.initialMemory = (performance as any).memory.usedJSHeapSize
      this.takeMemorySnapshot()
      
      // 每5秒采样一次
      setInterval(() => {
        this.takeMemorySnapshot()
      }, 5000)
    }
  }
  
  private takeMemorySnapshot(): void {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory
      this.memorySnapshots.push({
        timestamp: Date.now(),
        used: memInfo.usedJSHeapSize,
        total: memInfo.totalJSHeapSize,
        limit: memInfo.jsHeapSizeLimit
      })
      
      // 保持最近50个快照
      if (this.memorySnapshots.length > 50) {
        this.memorySnapshots.shift()
      }
    }
  }
  
  // 检测内存泄漏
  detectMemoryLeaks(): any {
    if (this.memorySnapshots.length < 10) {
      return { status: 'insufficient_data' }
    }
    
    const recent = this.memorySnapshots.slice(-10)
    const early = this.memorySnapshots.slice(0, 10)
    
    const recentAvg = recent.reduce((sum, snap) => sum + snap.used, 0) / recent.length
    const earlyAvg = early.reduce((sum, snap) => sum + snap.used, 0) / early.length
    
    const growthRate = (recentAvg - earlyAvg) / earlyAvg * 100
    
    return {
      status: growthRate > 20 ? 'potential_leak' : 'normal',
      growthRate: Math.round(growthRate * 100) / 100,
      currentUsage: Math.round(recentAvg / 1024 / 1024 * 100) / 100, // MB
      recommendation: growthRate > 20 ? 
        '检测到潜在内存泄漏，建议检查事件监听器和定时器' : 
        '内存使用正常'
    }
  }
  
  // 测试应用内存占用
  async testAppMemoryUsage(appName: string): Promise<any> {
    const beforeLoad = this.getCurrentMemoryUsage()
    
    // 加载应用
    await this.loadApp(appName)
    const afterLoad = this.getCurrentMemoryUsage()
    
    // 卸载应用
    await this.unloadApp(appName)
    const afterUnload = this.getCurrentMemoryUsage()
    
    return {
      appName,
      loadMemoryIncrease: afterLoad - beforeLoad,
      memoryReleased: afterLoad - afterUnload,
      potentialLeak: (afterUnload - beforeLoad) > (afterLoad - beforeLoad) * 0.1
    }
  }
  
  private getCurrentMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize
    }
    return 0
  }
  
  private async loadApp(appName: string): Promise<void> {
    // 模拟应用加载
    return new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  private async unloadApp(appName: string): Promise<void> {
    // 模拟应用卸载
    return new Promise(resolve => setTimeout(resolve, 500))
  }
  
  // 获取内存使用报告
  getMemoryReport(): any {
    const latest = this.memorySnapshots[this.memorySnapshots.length - 1]
    const leakDetection = this.detectMemoryLeaks()
    
    return {
      current: {
        used: Math.round(latest.used / 1024 / 1024 * 100) / 100,
        total: Math.round(latest.total / 1024 / 1024 * 100) / 100,
        limit: Math.round(latest.limit / 1024 / 1024 * 100) / 100,
        usage: Math.round(latest.used / latest.limit * 100 * 100) / 100
      },
      leakDetection,
      trend: this.calculateMemoryTrend()
    }
  }
  
  private calculateMemoryTrend(): string {
    if (this.memorySnapshots.length < 5) return 'stable'
    
    const recent5 = this.memorySnapshots.slice(-5)
    const trend = recent5.map((snap, index) => {
      if (index === 0) return 0
      return snap.used - recent5[index - 1].used
    }).slice(1)
    
    const avgTrend = trend.reduce((sum, val) => sum + val, 0) / trend.length
    
    if (avgTrend > 1024 * 1024) return 'increasing' // 1MB增长
    if (avgTrend < -1024 * 1024) return 'decreasing'
    return 'stable'
  }
}
```

## 网络性能测试

### 资源加载性能测试

```typescript
// 网络和资源加载性能测试
class NetworkPerformanceTest {
  private resourceTimings: PerformanceResourceTiming[] = []
  
  // 开始网络性能监控
  startNetworkMonitoring(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            this.resourceTimings.push(entry as PerformanceResourceTiming)
          }
        })
      })
      
      observer.observe({ entryTypes: ['resource'] })
    }
  }
  
  // 分析资源加载性能
  analyzeResourcePerformance(): any {
    const analysis = {
      totalResources: this.resourceTimings.length,
      byType: {},
      slowResources: [],
      cacheHitRate: 0,
      averageLoadTime: 0
    }
    
    let totalLoadTime = 0
    let cacheHits = 0
    
    this.resourceTimings.forEach((timing) => {
      const loadTime = timing.responseEnd - timing.requestStart
      totalLoadTime += loadTime
      
      // 按类型分组
      const type = this.getResourceType(timing.name)
      if (!analysis.byType[type]) {
        analysis.byType[type] = { count: 0, totalTime: 0, avgTime: 0 }
      }
      analysis.byType[type].count++
      analysis.byType[type].totalTime += loadTime
      
      // 检测缓存命中
      if (timing.transferSize === 0 && timing.decodedBodySize > 0) {
        cacheHits++
      }
      
      // 识别慢资源 (>1秒)
      if (loadTime > 1000) {
        analysis.slowResources.push({
          url: timing.name,
          loadTime: Math.round(loadTime),
          size: timing.transferSize
        })
      }
    })
    
    // 计算平均值
    analysis.averageLoadTime = Math.round(totalLoadTime / this.resourceTimings.length)
    analysis.cacheHitRate = Math.round((cacheHits / this.resourceTimings.length) * 100)
    
    // 计算各类型平均时间
    Object.keys(analysis.byType).forEach(type => {
      analysis.byType[type].avgTime = Math.round(
        analysis.byType[type].totalTime / analysis.byType[type].count
      )
    })
    
    return analysis
  }
  
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'JavaScript'
    if (url.includes('.css')) return 'CSS'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'Image'
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'Font'
    if (url.includes('.json')) return 'JSON'
    return 'Other'
  }
  
  // 测试 CDN 性能
  async testCDNPerformance(urls: string[]): Promise<any> {
    const results = []
    
    for (const url of urls) {
      const startTime = performance.now()
      
      try {
        const response = await fetch(url, { method: 'HEAD' })
        const endTime = performance.now()
        
        results.push({
          url,
          responseTime: Math.round(endTime - startTime),
          status: response.status,
          success: response.ok
        })
      } catch (error) {
        results.push({
          url,
          responseTime: -1,
          status: 0,
          success: false,
          error: error.message
        })
      }
    }
    
    return {
      results,
      averageResponseTime: Math.round(
        results.filter(r => r.success)
          .reduce((sum, r) => sum + r.responseTime, 0) / 
        results.filter(r => r.success).length
      ),
      successRate: Math.round(
        (results.filter(r => r.success).length / results.length) * 100
      )
    }
  }
}
```

## 基准对比测试

### 与其他微前端框架对比

```typescript
// 基准对比测试
class BenchmarkTest {
  private frameworks = ['micro-core', 'qiankun', 'wujie', 'single-spa']
  private testResults: Map<string, any> = new Map()
  
  // 运行完整基准测试
  async runFullBenchmark(): Promise<any> {
    const results = {
      loadingPerformance: {},
      runtimePerformance: {},
      memoryUsage: {},
      bundleSize: {},
      overall: {}
    }
    
    for (const framework of this.frameworks) {
      console.log(`测试 ${framework}...`)
      
      results.loadingPerformance[framework] = await this.testLoadingPerformance(framework)
      results.runtimePerformance[framework] = await this.testRuntimePerformance(framework)
      results.memoryUsage[framework] = await this.testMemoryUsage(framework)
      results.bundleSize[framework] = await this.testBundleSize(framework)
    }
    
    // 计算综合评分
    results.overall = this.calculateOverallScores(results)
    
    return results
  }
  
  private async testLoadingPerformance(framework: string): Promise<any> {
    // 模拟不同框架的加载性能测试
    const baseTime = 1000
    const variations = {
      'micro-core': 0.8,  // 20% 更快
      'qiankun': 1.0,     // 基准
      'wujie': 0.9,       // 10% 更快
      'single-spa': 1.1   // 10% 更慢
    }
    
    const multiplier = variations[framework] || 1.0
    
    return {
      appLoadTime: Math.round(baseTime * multiplier + Math.random() * 200),
      appSwitchTime: Math.round(300 * multiplier + Math.random() * 100),
      firstContentfulPaint: Math.round(1500 * multiplier + Math.random() * 300),
      timeToInteractive: Math.round(2500 * multiplier + Math.random() * 500)
    }
  }
  
  private async testRuntimePerformance(framework: string): Promise<any> {
    const baseFPS = 60
    const baseMemory = 40
    
    const variations = {
      'micro-core': { fps: 1.1, memory: 0.8 },
      'qiankun': { fps: 1.0, memory: 1.0 },
      'wujie': { fps: 1.05, memory: 0.9 },
      'single-spa': { fps: 0.95, memory: 1.1 }
    }
    
    const variation = variations[framework] || { fps: 1.0, memory: 1.0 }
    
    return {
      averageFPS: Math.round(baseFPS * variation.fps),
      memoryUsage: Math.round(baseMemory * variation.memory),
      cpuUsage: Math.round(25 / variation.fps),
      communicationLatency: Math.round(20 / variation.fps)
    }
  }
  
  private async testMemoryUsage(framework: string): Promise<any> {
    const baseMemory = 35 // MB
    
    const variations = {
      'micro-core': 0.75,
      'qiankun': 1.0,
      'wujie': 0.85,
      'single-spa': 0.9
    }
    
    const multiplier = variations[framework] || 1.0
    
    return {
      initialMemory: Math.round(baseMemory * multiplier),
      peakMemory: Math.round(baseMemory * multiplier * 1.5),
      memoryLeakRate: Math.round(5 / multiplier), // MB/hour
      garbageCollectionFrequency: Math.round(10 * multiplier) // times/minute
    }
  }
  
  private async testBundleSize(framework: string): Promise<any> {
    const baseSizes = {
      'micro-core': { core: 15, runtime: 25, total: 40 },
      'qiankun': { core: 45, runtime: 35, total: 80 },
      'wujie': { core: 35, runtime: 30, total: 65 },
      'single-spa': { core: 25, runtime: 20, total: 45 }
    }
    
    return baseSizes[framework] || baseSizes['micro-core']
  }
  
  private calculateOverallScores(results: any): any {
    const scores = {}
    
    this.frameworks.forEach(framework => {
      const loading = results.loadingPerformance[framework]
      const runtime = results.runtimePerformance[framework]
      const memory = results.memoryUsage[framework]
      const bundle = results.bundleSize[framework]
      
      // 计算各项评分 (0-100)
      const loadingScore = Math.max(0, 100 - (loading.appLoadTime - 500) / 10)
      const runtimeScore = Math.min(100, runtime.averageFPS / 60 * 100)
      const memoryScore = Math.max(0, 100 - (memory.initialMemory - 20) * 2)
      const bundleScore = Math.max(0, 100 - (bundle.total - 30) * 2)
      
      scores[framework] = {
        loading: Math.round(loadingScore),
        runtime: Math.round(runtimeScore),
        memory: Math.round(memoryScore),
        bundle: Math.round(bundleScore),
        overall: Math.round((loadingScore + runtimeScore + memoryScore + bundleScore) / 4)
      }
    })
    
    return scores
  }
  
  // 生成对比报告
  generateComparisonReport(results: any): string {
    let report = '# 微前端框架性能对比报告\n\n'
    
    // 综合评分表
    report += '## 综合评分\n\n'
    report += '| 框架 | 加载性能 | 运行时性能 | 内存使用 | 包大小 | 综合评分 |\n'
    report += '|------|----------|------------|----------|--------|----------|\n'
    
    Object.entries(results.overall).forEach(([framework, scores]: [string, any]) => {
      report += `| ${framework} | ${scores.loading} | ${scores.runtime} | ${scores.memory} | ${scores.bundle} | **${scores.overall}** |\n`
    })
    
    // 详细对比
    report += '\n## 详细性能对比\n\n'
    
    // 加载性能对比
    report += '### 加载性能\n\n'
    report += '| 框架 | 应用加载时间 | 应用切换时间 | FCP | TTI |\n'
    report += '|------|--------------|--------------|-----|-----|\n'
    
    Object.entries(results.loadingPerformance).forEach(([framework, perf]: [string, any]) => {
      report += `| ${framework} | ${perf.appLoadTime}ms | ${perf.appSwitchTime}ms | ${perf.firstContentfulPaint}ms | ${perf.timeToInteractive}ms |\n`
    })
    
    // 运行时性能对比
    report += '\n### 运行时性能\n\n'
    report += '| 框架 | 平均FPS | 内存使用 | CPU使用率 | 通信延迟 |\n'
    report += '|------|---------|----------|-----------|----------|\n'
    
    Object.entries(results.runtimePerformance).forEach(([framework, perf]: [string, any]) => {
      report += `| ${framework} | ${perf.averageFPS} | ${perf.memoryUsage}MB | ${perf.cpuUsage}% | ${perf.communicationLatency}ms |\n`
    })
    
    return report
  }
}
```

## 性能优化建议

### 自动化性能优化建议

```typescript
// 性能优化建议生成器
class PerformanceOptimizationAdvisor {
  private metrics: any
  private suggestions: Array<{
    category: string
    priority: 'high' | 'medium' | 'low'
    issue: string
    solution: string
    impact: string
  }> = []
  
  constructor(metrics: any) {
    this.metrics = metrics
    this.analyzeAndGenerateSuggestions()
  }
  
  private analyzeAndGenerateSuggestions(): void {
    this.checkLoadingPerformance()
    this.checkRuntimePerformance()
    this.checkMemoryUsage()
    this.checkNetworkPerformance()
    this.checkMicrofrontendSpecific()
  }
  
  private checkLoadingPerformance(): void {
    if (this.metrics.fcp > 1800) {
      this.suggestions.push({
        category: '加载性能',
        priority: 'high',
        issue: `首次内容绘制时间过长 (${this.metrics.fcp}ms)`,
        solution: '启用资源预加载、优化关键渲染路径、使用 CDN',
        impact: '可提升 20-30% 的加载速度'
      })
    }
    
    if (this.metrics.lcp > 2500) {
      this.suggestions.push({
        category: '加载性能',
        priority: 'high',
        issue: `最大内容绘制时间过长 (${this.metrics.lcp}ms)`,
        solution: '优化图片加载、使用现代图片格式、实施懒加载',
        impact: '可提升 15-25% 的感知性能'
      })
    }
    
    if (this.metrics.appLoad > 500) {
      this.suggestions.push({
        category: '微前端',
        priority: 'medium',
        issue: `微应用加载时间过长 (${this.metrics.appLoad}ms)`,
        solution: '启用智能预加载、使用应用缓存、优化包大小',
        impact: '可提升 30-40% 的应用切换速度'
      })
    }
  }
  
  private checkRuntimePerformance(): void {
    if (this.metrics.fps < 55) {
      this.suggestions.push({
        category: '运行时性能',
        priority: 'high',
        issue: `帧率过低 (${this.metrics.fps} FPS)`,
        solution: '优化渲染逻辑、使用虚拟滚动、减少DOM操作',
        impact: '可提升 20-30% 的交互流畅度'
      })
    }
    
    if (this.metrics.communication > 50) {
      this.suggestions.push({
        category: '微前端',
        priority: 'medium',
        issue: `应用间通信延迟过高 (${this.metrics.communication}ms)`,
        solution: '使用高效的通信机制、减少通信频率、批量处理消息',
        impact: '可提升 40-50% 的通信效率'
      })
    }
  }
  
  private checkMemoryUsage(): void {
    if (this.metrics.memory > 80) {
      this.suggestions.push({
        category: '内存优化',
        priority: 'high',
        issue: `内存使用过高 (${this.metrics.memory}MB)`,
        solution: '清理未使用的事件监听器、优化缓存策略、使用内存池',
        impact: '可减少 20-30% 的内存占用'
      })
    }
  }
  
  private checkNetworkPerformance(): void {
    // 基于网络性能指标生成建议
    if (this.metrics.cacheHitRate < 70) {
      this.suggestions.push({
        category: '网络优化',
        priority: 'medium',
        issue: `缓存命中率过低 (${this.metrics.cacheHitRate}%)`,
        solution: '优化缓存策略、设置合适的缓存头、使用 Service Worker',
        impact: '可提升 25-35% 的资源加载速度'
      })
    }
  }
  
  private checkMicrofrontendSpecific(): void {
    if (this.metrics.appSwitch > 200) {
      this.suggestions.push({
        category: '微前端',
        priority: 'medium',
        issue: `应用切换时间过长 (${this.metrics.appSwitch}ms)`,
        solution: '使用应用保活、优化沙箱切换、预加载相关应用',
        impact: '可提升 50-60% 的应用切换体验'
      })
    }
  }
  
  // 获取优化建议
  getSuggestions(): any {
    return {
      summary: {
        total: this.suggestions.length,
        high: this.suggestions.filter(s => s.priority === 'high').length,
        medium: this.suggestions.filter(s => s.priority === 'medium').length,
        low: this.suggestions.filter(s => s.priority === 'low').length
      },
      suggestions: this.suggestions.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      })
    }
  }
  
  // 生成优化计划
  generateOptimizationPlan(): string {
    const suggestions = this.getSuggestions()
    let plan = '# 性能优化计划\n\n'
    
    plan += `## 概述\n\n`
    plan += `总共发现 ${suggestions.total} 个优化点：\n`
    plan += `- 🔴 高优先级：${suggestions.summary.high} 个\n`
    plan += `- 🟡 中优先级：${suggestions.summary.medium} 个\n`
    plan += `- 🟢 低优先级：${suggestions.summary.low} 个\n\n`
    
    const categories = [...new Set(suggestions.suggestions.map(s => s.category))]
    
    categories.forEach(category => {
      plan += `## ${category}\n\n`
      
      const categorySuggestions = suggestions.suggestions.filter(s => s.category === category)
      
      categorySuggestions.forEach((suggestion, index) => {
        const priorityIcon = {
          high: '🔴',
          medium: '🟡',
          low: '🟢'
        }[suggestion.priority]
        
        plan += `### ${priorityIcon} ${suggestion.issue}\n\n`
        plan += `**解决方案：** ${suggestion.solution}\n\n`
        plan += `**预期效果：** ${suggestion.impact}\n\n`
      })
    })
    
    return plan
  }
}
```

## 相关链接

- [配置生成器](/playground/config-generator)
- [框架示例](/playground/framework-example)
- [高级特性](/playground/advanced-features)
- [性能优化指南](/guide/best-practices/performance)
- [API 文档](/api/)

---

通过性能测试演练场，您可以全面评估 Micro-Core 应用的性能表现，识别性能瓶颈，并获得针对性的优化建议，确保您的微前端应用达到最佳性能。
# 性能测试演练场

Micro-Core 性能测试演练场提供了全面的性能测试工具和基准测试，帮助您评估和优化微前端应用的性能表现。

## 📋 目录

- [测试概述](#测试概述)
- [加载性能测试](#加载性能测试)
- [运行时性能测试](#运行时性能测试)
- [内存使用测试](#内存使用测试)
- [网络性能测试](#网络性能测试)
- [用户体验测试](#用户体验测试)
- [基准对比测试](#基准对比测试)
- [性能优化建议](#性能优化建议)

## 测试概述

### 🎯 性能测试指标

```typescript
// 性能测试指标体系
const performanceMetrics = {
  // 加载性能
  loading: {
    firstContentfulPaint: 'FCP - 首次内容绘制',
    largestContentfulPaint: 'LCP - 最大内容绘制',
    firstInputDelay: 'FID - 首次输入延迟',
    cumulativeLayoutShift: 'CLS - 累积布局偏移',
    timeToInteractive: 'TTI - 可交互时间'
  },
  
  // 运行时性能
  runtime: {
    frameRate: '帧率 (FPS)',
    memoryUsage: '内存使用量',
    cpuUsage: 'CPU 使用率',
    networkLatency: '网络延迟',
    cacheHitRate: '缓存命中率'
  },
  
  // 微前端特定指标
  microfrontend: {
    appLoadTime: '应用加载时间',
    appSwitchTime: '应用切换时间',
    sandboxOverhead: '沙箱开销',
    communicationLatency: '通信延迟',
    routeChangeTime: '路由切换时间'
  }
}
```

### 🚀 性能测试界面

```vue
<template>
  <div class="performance-test-playground">
    <div class="playground-header">
      <h1>Micro-Core 性能测试演练场</h1>
      <div class="test-controls">
        <button @click="startAllTests" :disabled="testing">
          {{ testing ? '测试中...' : '开始全面测试' }}
        </button>
        <button @click="clearResults">清除结果</button>
        <button @click="exportResults">导出报告</button>
      </div>
    </div>
    
    <div class="playground-content">
      <div class="test-categories">
        <div class="category-card">
          <h3>加载性能测试</h3>
          <div class="test-metrics">
            <div class="metric-item">
              <span>FCP</span>
              <span :class="getScoreClass(metrics.fcp)">
                {{ metrics.fcp || '--' }}ms
              </span>
            </div>
            <div class="metric-item">
              <span>LCP</span>
              <span :class="getScoreClass(metrics.lcp)">
                {{ metrics.lcp || '--' }}ms
              </span>
            </div>
            <div class="metric-item">
              <span>TTI</span>
              <span :class="getScoreClass(metrics.tti)">
                {{ metrics.tti || '--' }}ms
              </span>
            </div>
          </div>
          <button @click="runLoadingTests" :disabled="testing">
            运行加载测试
          </button>
        </div>
        
        <div class="category-card">
          <h3>运行时性能测试</h3>
          <div class="test-metrics">
            <div class="metric-item">
              <span>FPS</span>
              <span :class="getScoreClass(metrics.fps, 'fps')">
                {{ metrics.fps || '--' }}
              </span>
            </div>
            <div class="metric-item">
              <span>内存</span>
              <span :class="getScoreClass(metrics.memory, 'memory')">
                {{ metrics.memory || '--' }}MB
              </span>
            </div>
            <div class="metric-item">
              <span>CPU</span>
              <span :class="getScoreClass(metrics.cpu, 'cpu')">
                {{ metrics.cpu || '--' }}%
              </span>
            </div>
          </div>
          <button @click="runRuntimeTests" :disabled="testing">
            运行运行时测试
          </button>
        </div>
        
        <div class="category-card">
          <h3>微前端性能测试</h3>
          <div class="test-metrics">
            <div class="metric-item">
              <span>应用加载</span>
              <span :class="getScoreClass(metrics.appLoad)">
                {{ metrics.appLoad || '--' }}ms
              </span>
            </div>
            <div class="metric-item">
              <span>应用切换</span>
              <span :class="getScoreClass(metrics.appSwitch)">
                {{ metrics.appSwitch || '--' }}ms
              </span>
            </div>
            <div class="metric-item">
              <span>通信延迟</span>
              <span :class="getScoreClass(metrics.communication)">
                {{ metrics.communication || '--' }}ms
              </span>
            </div>
          </div>
          <button @click="runMicrofrontendTests" :disabled="testing">
            运行微前端测试
          </button>
        </div>
      </div>
      
      <div class="test-results">
        <div class="results-header">
          <h3>测试结果</h3>
          <div class="overall-score">
            <span>综合评分</span>
            <span :class="getOverallScoreClass(overallScore)">
              {{ overallScore || '--' }}
            </span>
          </div>
        </div>
        
        <div class="results-charts">
          <canvas ref="performanceChart" width="400" height="200"></canvas>
          <canvas ref="comparisonChart" width="400" height="200"></canvas>
        </div>
        
        <div class="results-details">
          <div class="detail-section">
            <h4>详细指标</h4>
            <table class="metrics-table">
              <thead>
                <tr>
                  <th>指标</th>
                  <th>当前值</th>
                  <th>基准值</th>
                  <th>评分</th>
                  <th>建议</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="metric in detailedMetrics" :key="metric.name">
                  <td>{{ metric.name }}</td>
                  <td>{{ metric.current }}</td>
                  <td>{{ metric.baseline }}</td>
                  <td :class="getScoreClass(metric.score)">
                    {{ metric.score }}
                  </td>
                  <td>{{ metric.suggestion }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="detail-section">
            <h4>性能时间线</h4>
            <div class="timeline">
              <div 
                v-for="event in performanceTimeline" 
                :key="event.id"
                class="timeline-event"
                :style="{ left: (event.time / maxTime * 100) + '%' }"
              >
                <div class="event-marker"></div>
                <div class="event-label">{{ event.name }}</div>
                <div class="event-time">{{ event.time }}ms</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

const testing = ref(false)
const metrics = ref({})
const overallScore = ref(0)
const detailedMetrics = ref([])
const performanceTimeline = ref([])
const performanceChart = ref<HTMLCanvasElement>()
const comparisonChart = ref<HTMLCanvasElement>()

const maxTime = computed(() => {
  return Math.max(...performanceTimeline.value.map(e => e.time), 1000)
})

onMounted(() => {
  initializeCharts()
})

const startAllTests = async () => {
  testing.value = true
  
  try {
    await runLoadingTests()
    await runRuntimeTests()
    await runMicrofrontendTests()
    
    calculateOverallScore()
    updateCharts()
  } finally {
    testing.value = false
  }
}

const runLoadingTests = async () => {
  console.log('运行加载性能测试...')
  
  // 模拟 FCP 测试
  const fcpStart = performance.now()
  await simulatePageLoad()
  const fcpTime = performance.now() - fcpStart
  
  // 模拟 LCP 测试
  const lcpTime = fcpTime + Math.random() * 500
  
  // 模拟 TTI 测试
  const ttiTime = lcpTime + Math.random() * 1000
  
  metrics.value = {
    ...metrics.value,
    fcp: Math.round(fcpTime),
    lcp: Math.round(lcpTime),
    tti: Math.round(ttiTime)
  }
  
  // 添加到时间线
  performanceTimeline.value.push(
    { id: 'fcp', name: 'FCP', time: Math.round(fcpTime) },
    { id: 'lcp', name: 'LCP', time: Math.round(lcpTime) },
    { id: 'tti', name: 'TTI', time: Math.round(ttiTime) }
  )
}

const runRuntimeTests = async () => {
  console.log('运行运行时性能测试...')
  
  // 模拟 FPS 测试
  const fps = await measureFPS()
  
  // 模拟内存使用测试
  const memory = measureMemoryUsage()
  
  // 模拟 CPU 使用测试
  const cpu = await measureCPUUsage()
  
  metrics.value = {
    ...metrics.value,
    fps: Math.round(fps),
    memory: Math.round(memory),
    cpu: Math.round(cpu)
  }
}

const runMicrofrontendTests = async () => {
  console.log('运行微前端性能测试...')
  
  // 模拟应用加载测试
  const appLoadTime = await measureAppLoadTime()
  
  // 模拟应用切换测试
  const appSwitchTime = await measureAppSwitchTime()
  
  // 模拟通信延迟测试
  const communicationLatency = await measureCommunicationLatency()
  
  metrics.value = {
    ...metrics.value,
    appLoad: Math.round(appLoadTime),
    appSwitch: Math.round(appSwitchTime),
    communication: Math.round(communicationLatency)
  }
}

const simulatePageLoad = () => {
  return new Promise(resolve => {
    setTimeout(resolve, Math.random() * 1000 + 500)
  })
}

const measureFPS = async () => {
  return new Promise(resolve => {
    let frames = 0
    const startTime = performance.now()
    
    const countFrame = () => {
      frames++
      if (performance.now() - startTime < 1000) {
        requestAnimationFrame(countFrame)
      } else {
        resolve(frames)
      }
    }
    
    requestAnimationFrame(countFrame)
  })
}

const measureMemoryUsage = () => {
  if ('memory' in performance) {
    return (performance as any).memory.usedJSHeapSize / 1024 / 1024
  }
  return Math.random() * 50 + 20
}

const measureCPUUsage = async () => {
  // 模拟 CPU 使用率测试
  const start = performance.now()
  let iterations = 0
  
  while (performance.now() - start < 100) {
    iterations++
  }
  
  return Math.min(iterations / 10000 * 100, 100)
}

const measureAppLoadTime = async () => {
  const start = performance.now()
  
  // 模拟应用加载
  await new Promise(resolve => setTimeout(resolve, Math.random() * 800 + 200))
  
  return performance.now() - start
}

const measureAppSwitchTime = async () => {
  const start = performance.now()
  
  // 模拟应用切换
  await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100))
  
  return performance.now() - start
}

const measureCommunicationLatency = async () => {
  const start = performance.now()
  
  // 模拟通信延迟
  await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10))
  
  return performance.now() - start
}

const calculateOverallScore = () => {
  const scores = []
  
  // FCP 评分 (< 1800ms 为优秀)
  if (metrics.value.fcp) {
    scores.push(Math.max(0, 100 - (metrics.value.fcp - 1000) / 10))
  }
  
  // LCP 评分 (< 2500ms 为优秀)
  if (metrics.value.lcp) {
    scores.push(Math.max(0, 100 - (metrics.value.lcp - 1500) / 15))
  }
  
  // FPS 评分 (> 55 为优秀)
  if (metrics.value.fps) {
    scores.push(Math.min(100, metrics.value.fps / 60 * 100))
  }
  
  // 应用加载评分 (< 500ms 为优秀)
  if (metrics.value.appLoad) {
    scores.push(Math.max(0, 100 - (metrics.value.appLoad - 300) / 5))
  }
  
  overallScore.value = Math.round(
    scores.reduce((a, b) => a + b, 0) / scores.length
  )
  
  // 更新详细指标
  updateDetailedMetrics()
}

const updateDetailedMetrics = () => {
  detailedMetrics.value = [
    {
      name: 'First Contentful Paint',
      current: `${metrics.value.fcp}ms