# 演练场

欢迎来到 Micro-Core 演练场！这里提供了丰富的交互式示例和工具，让你可以快速体验和学习 Micro-Core 的各种功能。

## 🎯 快速导航

<div class="playground-nav">
  <div class="nav-section">
    <h3>🚀 快速体验</h3>
    <ul>
      <li><a href="./basic-example">基础示例</a> - 最简单的微前端应用</li>
      <li><a href="./framework-example">框架示例</a> - React、Vue、Angular 集成</li>
      <li><a href="./advanced-features">高级特性</a> - 插件、沙箱、通信等</li>
    </ul>
  </div>
  
  <div class="nav-section">
    <h3>🔄 迁移演练</h3>
    <ul>
      <li><a href="./qiankun-migration">qiankun 迁移示例</a> - 从 qiankun 平滑迁移</li>
      <li><a href="./wujie-migration">Wujie 迁移示例</a> - 从 wujie 平滑迁移</li>
    </ul>
  </div>
  
  <div class="nav-section">
    <h3>🛠️ 自定义演练</h3>
    <ul>
      <li><a href="./config-generator">配置生成器</a> - 可视化生成配置</li>
      <li><a href="./performance-test">性能测试</a> - 实时性能基准测试</li>
      <li><a href="./benchmark">基准测试</a> - 与其他框架对比</li>
    </ul>
  </div>
  
  <div class="nav-section">
    <h3>📚 交互式教程</h3>
    <ul>
      <li><a href="./step-by-step">步骤式学习</a> - 从零开始学习微前端</li>
    </ul>
  </div>
  
  <div class="nav-section">
    <h3>🔧 开发工具</h3>
    <ul>
      <li><a href="./debug-panel">调试面板</a> - 可视化调试工具</li>
      <li><a href="./performance-analysis">性能分析</a> - 性能监控和分析</li>
      <li><a href="./dev-tools">开发者工具</a> - 完整的开发工具集</li>
    </ul>
  </div>
</div>

## 🎮 在线演练场

### 基础示例

<div class="playground-card">
  <h4>🎯 Hello World</h4>
  <p>最简单的微前端应用，展示基本的应用注册和挂载。</p>
  <div class="playground-actions">
    <a href="./basic-example" class="btn btn-primary">立即体验</a>
    <a href="https://github.com/micro-core/examples/tree/main/basic" class="btn btn-secondary">查看源码</a>
  </div>
</div>

<div class="playground-card">
  <h4>🔄 应用切换</h4>
  <p>演示多个微应用之间的路由切换和生命周期管理。</p>
  <div class="playground-actions">
    <a href="./app-switching" class="btn btn-primary">立即体验</a>
    <a href="https://github.com/micro-core/examples/tree/main/app-switching" class="btn btn-secondary">查看源码</a>
  </div>
</div>

### 框架集成

<div class="playground-card">
  <h4>⚛️ React + Vue 混合应用</h4>
  <p>展示 React 和 Vue 应用在同一个微前端系统中的协作。</p>
  <div class="playground-actions">
    <a href="./framework-example" class="btn btn-primary">立即体验</a>
    <a href="https://github.com/micro-core/examples/tree/main/react-vue" class="btn btn-secondary">查看源码</a>
  </div>
</div>

<div class="playground-card">
  <h4>🅰️ Angular 集成</h4>
  <p>演示 Angular 应用的微前端集成和依赖注入。</p>
  <div class="playground-actions">
    <a href="./angular-example" class="btn btn-primary">立即体验</a>
    <a href="https://github.com/micro-core/examples/tree/main/angular" class="btn btn-secondary">查看源码</a>
  </div>
</div>

### 高级特性

<div class="playground-card">
  <h4>💬 应用间通信</h4>
  <p>展示不同微应用之间的事件通信和状态共享。</p>
  <div class="playground-actions">
    <a href="./communication-example" class="btn btn-primary">立即体验</a>
    <a href="https://github.com/micro-core/examples/tree/main/communication" class="btn btn-secondary">查看源码</a>
  </div>
</div>

<div class="playground-card">
  <h4>🛡️ 沙箱隔离</h4>
  <p>演示不同沙箱策略的效果和性能对比。</p>
  <div class="playground-actions">
    <a href="./sandbox-example" class="btn btn-primary">立即体验</a>
    <a href="https://github.com/micro-core/examples/tree/main/sandbox" class="btn btn-secondary">查看源码</a>
  </div>
</div>

## 🔧 工具和实用程序

### 配置生成器

<div class="tool-card">
  <h4>⚙️ 可视化配置生成器</h4>
  <p>通过可视化界面生成 Micro-Core 配置文件，支持所有配置选项。</p>
  <div class="tool-features">
    <ul>
      <li>✅ 拖拽式应用配置</li>
      <li>✅ 实时配置预览</li>
      <li>✅ 配置验证和提示</li>
      <li>✅ 一键导出配置文件</li>
    </ul>
  </div>
  <div class="playground-actions">
    <a href="./config-generator" class="btn btn-primary">打开工具</a>
  </div>
</div>

### 性能测试

<div class="tool-card">
  <h4>📊 性能基准测试</h4>
  <p>实时测试和对比不同配置下的性能表现。</p>
  <div class="tool-features">
    <ul>
      <li>✅ 应用加载时间测试</li>
      <li>✅ 内存使用监控</li>
      <li>✅ 路由切换性能</li>
      <li>✅ 与其他框架对比</li>
    </ul>
  </div>
  <div class="playground-actions">
    <a href="./performance-test" class="btn btn-primary">开始测试</a>
  </div>
</div>

### 调试工具

<div class="tool-card">
  <h4>🐛 可视化调试面板</h4>
  <p>实时监控微前端应用的状态和性能指标。</p>
  <div class="tool-features">
    <ul>
      <li>✅ 应用状态监控</li>
      <li>✅ 事件流追踪</li>
      <li>✅ 性能指标展示</li>
      <li>✅ 错误日志收集</li>
    </ul>
  </div>
  <div class="playground-actions">
    <a href="./debug-panel" class="btn btn-primary">打开面板</a>
  </div>
</div>

## 📚 学习路径

### 初学者路径

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    初学者学习路径                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ 1. 基础示例      │───▶│ 2. 框架集成      │───▶│ 3. 应用通信      ││
│  │ • Hello World   │    │ • React + Vue   │    │ • 事件总线      ││
│  │ • 应用切换      │    │ • Angular 集成  │    │ • 状态共享      ││
│  │ 预计时间: 30分钟 │    │ 预计时间: 45分钟 │    │ 预计时间: 30分钟 ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│                                   │                               │
│                                   ▼                               │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ 6. 项目实战      │◀───│ 5. 最佳实践      │◀───│ 4. 高级特性      ││
│  │ • 完整项目      │    │ • 性能优化      │    │ • 沙箱隔离      ││
│  │ • 部署上线      │    │ • 错误处理      │    │ • 插件系统      ││
│  │ 预计时间: 2小时  │    │ 预计时间: 1小时  │    │ 预计时间: 1小时  ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### 进阶开发者路径

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    进阶开发者路径                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ 1. 架构深入      │───▶│ 2. 插件开发      │───▶│ 3. 性能优化      ││
│  │ • 微内核原理    │    │ • 自定义插件    │    │ • 加载优化      ││
│  │ • 沙箱机制      │    │ • 插件生态      │    │ • 内存管理      ││
│  │ 预计时间: 1小时  │    │ 预计时间: 2小时  │    │ 预计时间: 1小时  ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│                                   │                               │
│                                   ▼                               │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ 6. 开源贡献      │◀───│ 5. 企业实践      │◀───│ 4. 迁移方案      ││
│  │ • 提交 PR       │    │ • 大型项目      │    │ • qiankun 迁移  ││
│  │ • 社区参与      │    │ • 团队协作      │    │ • wujie 迁移    ││
│  │ 预计时间: 持续   │    │ 预计时间: 3小时  │    │ 预计时间: 2小时  ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## 🎯 交互式教程

### 步骤式学习

我们提供了完整的步骤式交互教程，让你可以按照自己的节奏学习：

1. **🎯 第一步：理解微前端**
   - 什么是微前端？
   - 微前端的优势和挑战
   - Micro-Core 的解决方案

2. **🏗️ 第二步：搭建第一个应用**
   - 创建主应用
   - 注册微应用
   - 配置路由

3. **🔄 第三步：应用间通信**
   - 事件总线使用
   - 全局状态管理
   - 数据共享策略

4. **🛡️ 第四步：沙箱和隔离**
   - 选择合适的沙箱
   - 样式隔离配置
   - 性能优化技巧

5. **🚀 第五步：生产部署**
   - 构建优化
   - 部署策略
   - 监控和维护

<div class="playground-actions">
  <a href="./step-by-step" class="btn btn-primary btn-large">开始学习</a>
</div>

## 🔗 相关资源

- **📚 [完整文档](/guide/)** - 详细的使用指南和 API 文档
- **💻 [GitHub 仓库](https://github.com/micro-core/micro-core)** - 源码和问题反馈
- **🌟 [示例项目](https://github.com/micro-core/examples)** - 更多完整示例
- **💬 [社区讨论](https://github.com/micro-core/micro-core/discussions)** - 与其他开发者交流
- **📖 [博客文章](/blog/)** - 深度技术文章和最佳实践

## 💡 使用提示

### 浏览器要求

- **Chrome** 80+ (推荐)
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

### 性能建议

- 使用现代浏览器以获得最佳性能
- 建议在桌面环境下体验完整功能
- 移动端支持基础功能演示

### 问题反馈

如果在使用演练场过程中遇到问题，请：

1. 检查浏览器控制台是否有错误信息
2. 尝试刷新页面或清除缓存
3. 在 [GitHub Issues](https://github.com/micro-core/micro-core/issues) 中反馈问题

---

<style>
.playground-nav {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.nav-section {
  background: var(--vp-c-bg-soft);
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 20px;
}

.nav-section h3 {
  margin: 0 0 15px 0;
  color: var(--vp-c-brand-1);
}

.nav-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-section li {
  margin: 8px 0;
}

.nav-section a {
  color: var(--vp-c-text-1);
  text-decoration: none;
  padding: 4px 0;
  display: block;
  border-bottom: 1px solid transparent;
  transition: border-color 0.3s ease;
}

.nav-section a:hover {
  border-bottom-color: var(--vp-c-brand-1);
}

.playground-card, .tool-card {
  background: var(--vp-c-bg-soft);
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  padding: 24px;
  margin: 20px 0;
  transition: all 0.3s ease;
}

.playground-card:hover, .tool-card:hover {
  border-color: var(--vp-c-brand-1);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
}

.playground-card h4, .tool-card h4 {
  margin: 0 0 12px 0;
  color: var(--vp-c-text-1);
}

.playground-card p, .tool-card p {
  color: var(--vp-c-text-2);
  margin: 0 0 16px 0;
}

.tool-features ul {
  margin: 16px 0;
  padding-left: 0;
  list-style: none;
}

.tool-features li {
  margin: 8px 0;
  color: var(--vp-c-text-2);
}

.playground-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid;
}

.btn-primary {
  background: var(--vp-c-brand-1);
  color: white;
  border-color: var(--vp-c-brand-1);
}

.btn-primary:hover {
  background: var(--vp-c-brand-2);
  border-color: var(--vp-c-brand-2);
}

.btn-secondary {
  background: transparent;
  color: var(--vp-c-text-1);
  border-color: var(--vp-c-border);
}

.btn-secondary:hover {
  background: var(--vp-c-bg-soft);
  border-color: var(--vp-c-brand-1);
}

.btn-large {
  padding: 12px 24px;
  font-size: 16px;
}
</style>
