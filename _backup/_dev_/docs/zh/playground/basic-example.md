# 基础示例

这是一个最简单的 Micro-Core 微前端示例，展示了如何快速创建和运行一个微前端应用。

## 🎯 学习目标

通过这个示例，你将学会：

- 如何创建一个基础的微前端主应用
- 如何注册和管理微应用
- 如何实现应用间的路由切换
- 如何处理应用的生命周期

## 📋 示例概述

这个示例包含：
- 1 个主应用（负责应用管理和路由）
- 2 个微应用（React 和 Vue）
- 基础的路由切换功能

## 🚀 在线体验

<div class="playground-container">
  <div class="playground-header">
    <h3>🎮 交互式演示</h3>
    <p>点击下方按钮体验不同的微应用切换</p>
  </div>
  
  <div class="playground-demo">
    <div class="demo-nav">
      <button class="nav-btn active" data-app="home">🏠 主页</button>
      <button class="nav-btn" data-app="react">⚛️ React 应用</button>
      <button class="nav-btn" data-app="vue">💚 Vue 应用</button>
    </div>
    
    <div class="demo-content">
      <div class="app-container" id="home-app">
        <div class="welcome-card">
          <h2>🎉 欢迎使用 Micro-Core</h2>
          <p>这是一个基础的微前端示例，展示了主应用和微应用的协作。</p>
          <div class="features">
            <div class="feature">
              <span class="icon">🏗️</span>
              <span>微内核架构</span>
            </div>
            <div class="feature">
              <span class="icon">🔄</span>
              <span>应用切换</span>
            </div>
            <div class="feature">
              <span class="icon">💬</span>
              <span>应用通信</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="app-container hidden" id="react-app">
        <div class="react-demo">
          <h2>⚛️ React 微应用</h2>
          <p>这是一个 React 18 微应用示例</p>
          <div class="counter-demo">
            <p>计数器: <span id="react-counter">0</span></p>
            <button onclick="updateReactCounter()">增加计数</button>
          </div>
          <div class="communication-demo">
            <button onclick="sendMessageFromReact()">发送消息到 Vue</button>
            <div id="react-messages"></div>
          </div>
        </div>
      </div>
      
      <div class="app-container hidden" id="vue-app">
        <div class="vue-demo">
          <h2>💚 Vue 微应用</h2>
          <p>这是一个 Vue 3 微应用示例</p>
          <div class="counter-demo">
            <p>计数器: <span id="vue-counter">0</span></p>
            <button onclick="updateVueCounter()">增加计数</button>
          </div>
          <div class="communication-demo">
            <button onclick="sendMessageFromVue()">发送消息到 React</button>
            <div id="vue-messages"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="playground-info">
    <div class="info-section">
      <h4>📊 应用状态</h4>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">当前应用:</span>
          <span class="value" id="current-app">主页</span>
        </div>
        <div class="status-item">
          <span class="label">已加载应用:</span>
          <span class="value" id="loaded-apps">1</span>
        </div>
        <div class="status-item">
          <span class="label">消息数量:</span>
          <span class="value" id="message-count">0</span>
        </div>
      </div>
    </div>
  </div>
</div>

## 💻 源码解析

### 1. 主应用配置

```typescript
// main.ts - 主应用入口
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';
import { VueAdapter } from '@micro-core/adapter-vue';

// 创建微前端实例
const microCore = new MicroCore({
  container: '#app',
  
  // 注册适配器
  adapters: [
    new ReactAdapter(),
    new VueAdapter()
  ]
});

// 注册 React 微应用
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeRule: '/react',
  
  // 生命周期钩子
  beforeMount: () => {
    console.log('React 应用即将挂载');
  },
  
  afterMount: () => {
    console.log('React 应用挂载完成');
  }
});

// 注册 Vue 微应用
microCore.registerApp({
  name: 'vue-app',
  entry: 'http://localhost:3002',
  container: '#vue-container',
  activeRule: '/vue',
  
  beforeMount: () => {
    console.log('Vue 应用即将挂载');
  },
  
  afterMount: () => {
    console.log('Vue 应用挂载完成');
  }
});

// 启动微前端系统
microCore.start().then(() => {
  console.log('Micro-Core 启动成功！');
});
```

### 2. React 微应用

```tsx
// react-app/src/index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { createReactAdapter } from '@micro-core/adapter-react';
import App from './App';

// 创建 React 适配器
const adapter = createReactAdapter({
  concurrent: true,
  strictMode: true
});

// 导出生命周期函数
export const { bootstrap, mount, unmount } = adapter({
  rootComponent: App,
  container: '#root'
});

// 独立运行模式
if (!window.__POWERED_BY_MICRO_CORE__) {
  const root = ReactDOM.createRoot(document.getElementById('root')!);
  root.render(<App />);
}
```

```tsx
// react-app/src/App.tsx
import React, { useState, useEffect } from 'react';
import { useMicroCore } from '@micro-core/adapter-react';

function App() {
  const [count, setCount] = useState(0);
  const { eventBus } = useMicroCore();
  const [messages, setMessages] = useState<string[]>([]);
  
  useEffect(() => {
    // 监听来自 Vue 应用的消息
    const unsubscribe = eventBus.on('message-from-vue', (data) => {
      setMessages(prev => [...prev, `Vue: ${data.message}`]);
    });
    
    return unsubscribe;
  }, [eventBus]);
  
  const sendMessage = () => {
    eventBus.emit('message-from-react', {
      message: `Hello from React! Count: ${count}`,
      timestamp: Date.now()
    });
  };
  
  return (
    <div className="react-app">
      <h2>⚛️ React 微应用</h2>
      
      <div className="counter">
        <p>计数器: {count}</p>
        <button onClick={() => setCount(count + 1)}>
          增加计数
        </button>
      </div>
      
      <div className="communication">
        <button onClick={sendMessage}>
          发送消息到 Vue
        </button>
        
        <div className="messages">
          <h3>收到的消息:</h3>
          {messages.map((msg, index) => (
            <div key={index} className="message">
              {msg}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default App;
```

### 3. Vue 微应用

```typescript
// vue-app/src/main.ts
import { createApp } from 'vue';
import { createVueAdapter } from '@micro-core/adapter-vue';
import App from './App.vue';

// 创建 Vue 适配器
const adapter = createVueAdapter({
  version: '3.x'
});

// 导出生命周期函数
export const { bootstrap, mount, unmount } = adapter({
  rootComponent: App,
  container: '#app'
});

// 独立运行模式
if (!window.__POWERED_BY_MICRO_CORE__) {
  createApp(App).mount('#app');
}
```

```vue
<!-- vue-app/src/App.vue -->
<template>
  <div class="vue-app">
    <h2>💚 Vue 微应用</h2>
    
    <div class="counter">
      <p>计数器: {{ count }}</p>
      <button @click="increment">增加计数</button>
    </div>
    
    <div class="communication">
      <button @click="sendMessage">发送消息到 React</button>
      
      <div class="messages">
        <h3>收到的消息:</h3>
        <div
          v-for="(msg, index) in messages"
          :key="index"
          class="message"
        >
          {{ msg }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useMicroCore } from '@micro-core/adapter-vue';

const count = ref(0);
const messages = ref<string[]>([]);
const { eventBus } = useMicroCore();

let unsubscribe: (() => void) | null = null;

const increment = () => {
  count.value++;
};

const sendMessage = () => {
  eventBus.emit('message-from-vue', {
    message: `Hello from Vue! Count: ${count.value}`,
    timestamp: Date.now()
  });
};

onMounted(() => {
  // 监听来自 React 应用的消息
  unsubscribe = eventBus.on('message-from-react', (data) => {
    messages.value.push(`React: ${data.message}`);
  });
});

onUnmounted(() => {
  unsubscribe?.();
});
</script>

<style scoped>
.vue-app {
  padding: 20px;
  border: 2px solid #4fc08d;
  border-radius: 8px;
}

.counter, .communication {
  margin: 20px 0;
}

.message {
  padding: 8px;
  margin: 4px 0;
  background: #f0f0f0;
  border-radius: 4px;
}

button {
  padding: 8px 16px;
  margin: 4px;
  border: none;
  border-radius: 4px;
  background: #4fc08d;
  color: white;
  cursor: pointer;
}

button:hover {
  background: #369870;
}
</style>
```

## 🔧 本地运行

### 1. 克隆项目

```bash
git clone https://github.com/micro-core/examples.git
cd examples/basic-example
```

### 2. 安装依赖

```bash
# 安装主应用依赖
npm install

# 安装 React 应用依赖
cd react-app
npm install
cd ..

# 安装 Vue 应用依赖
cd vue-app
npm install
cd ..
```

### 3. 启动应用

```bash
# 启动所有应用
npm run dev

# 或分别启动
npm run dev:main    # 主应用 (端口 3000)
npm run dev:react   # React 应用 (端口 3001)
npm run dev:vue     # Vue 应用 (端口 3002)
```

### 4. 访问应用

打开浏览器访问 http://localhost:3000

## 📚 关键概念

### 1. 应用注册

```typescript
// 应用注册是微前端的核心概念
microCore.registerApp({
  name: 'app-name',        // 应用唯一标识
  entry: 'http://...',     // 应用入口地址
  container: '#container', // 挂载容器
  activeRule: '/path'      // 激活规则
});
```

### 2. 生命周期

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    微应用生命周期                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ 1. bootstrap    │───▶│ 2. mount        │───▶│ 3. unmount      ││
│  │ 应用初始化       │    │ 应用挂载         │    │ 应用卸载         ││
│  │ • 加载资源      │    │ • 渲染组件      │    │ • 清理资源      ││
│  │ • 初始化配置    │    │ • 绑定事件      │    │ • 移除监听器    ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### 3. 应用间通信

```typescript
// 发送消息
eventBus.emit('event-name', data);

// 监听消息
const unsubscribe = eventBus.on('event-name', (data) => {
  console.log('收到消息:', data);
});

// 取消监听
unsubscribe();
```

### 4. 路由管理

```typescript
// 路由配置
const routes = [
  { path: '/', component: 'main-app' },
  { path: '/react', component: 'react-app' },
  { path: '/vue', component: 'vue-app' }
];

// 程序化导航
microCore.navigateTo('/react');
```

## 🎯 实践练习

### 练习 1: 添加新的微应用

尝试添加一个 Angular 微应用到这个示例中：

```typescript
// 1. 注册 Angular 应用
microCore.registerApp({
  name: 'angular-app',
  entry: 'http://localhost:3003',
  container: '#angular-container',
  activeRule: '/angular'
});

// 2. 创建 Angular 应用
ng new angular-app
cd angular-app
npm install @micro-core/adapter-angular
```

### 练习 2: 实现共享状态

在 React 和 Vue 应用之间共享一个用户状态：

```typescript
// 在主应用中初始化全局状态
microCore.setGlobalState('user', {
  name: '张三',
  role: 'admin'
});

// 在 React 应用中使用
const [user, setUser] = useGlobalState('user');

// 在 Vue 应用中使用
const { user, setUser } = useGlobalState('user');
```

### 练习 3: 添加路由守卫

为应用添加权限控制：

```typescript
microCore.addRouteGuard('/admin', (to, from, next) => {
  const user = microCore.getGlobalState('user');
  if (user.role === 'admin') {
    next();
  } else {
    next('/login');
  }
});
```

## 🐛 常见问题

### Q1: 应用无法加载？

**A:** 检查以下几点：
- 确保微应用服务器正在运行
- 检查 CORS 配置
- 验证应用入口地址是否正确

```typescript
// 解决 CORS 问题
const microCore = new MicroCore({
  fetch: {
    credentials: 'include',
    mode: 'cors'
  }
});
```

### Q2: 应用间通信不生效？

**A:** 确保正确使用事件总线：

```typescript
// ❌ 错误用法
eventBus.emit('message', data);
eventBus.on('message', handler); // 可能在 emit 之后执行

// ✅ 正确用法
// 先注册监听器
const unsubscribe = eventBus.on('message', handler);
// 再发送消息
eventBus.emit('message', data);
```

### Q3: 样式冲突怎么办？

**A:** 启用样式隔离：

```typescript
microCore.registerApp({
  name: 'my-app',
  entry: 'http://localhost:3001',
  container: '#container',
  activeRule: '/my-app',
  
  // 启用样式隔离
  sandbox: {
    css: true
  }
});
```

## 📈 性能优化

### 1. 预加载策略

```typescript
// 预加载下一个可能访问的应用
microCore.preloadApp('react-app');

// 智能预加载
microCore.enableSmartPreload({
  strategy: 'hover', // 鼠标悬停时预加载
  delay: 200
});
```

### 2. 缓存配置

```typescript
microCore.configure({
  cache: {
    enabled: true,
    maxAge: 300000, // 5分钟
    maxSize: 10     // 最多缓存10个应用
  }
});
```

### 3. 资源优化

```typescript
// 共享依赖
microCore.shareLibrary('react', React);
microCore.shareLibrary('vue', Vue);

// 代码分割
const LazyComponent = React.lazy(() => import('./LazyComponent'));
```

## 🔗 相关资源

- **📚 [完整文档](/guide/)** - 详细的使用指南
- **💻 [GitHub 源码](https://github.com/micro-core/examples/tree/main/basic-example)** - 完整示例代码
- **🎥 [视频教程](https://www.youtube.com/watch?v=xxx)** - 视频讲解
- **💬 [社区讨论](https://github.com/micro-core/micro-core/discussions)** - 问题交流

## 🎉 下一步

恭喜！你已经完成了基础示例的学习。接下来你可以：

1. **[框架示例](./framework-example)** - 学习更多框架集成
2. **[高级特性](./advanced-features)** - 探索高级功能
3. **[迁移演练](./qiankun-migration)** - 学习从其他框架迁移
4. **[自定义演练](./config-generator)** - 使用配置生成器

---

<script>
// 演示页面交互逻辑
let reactCounter = 0;
let vueCounter = 0;
let messageCount = 0;

// 应用切换
document.querySelectorAll('.nav-btn').forEach(btn => {
  btn.addEventListener('click', (e) => {
    const app = e.target.dataset.app;
    switchApp(app);
  });
});

function switchApp(appName) {
  // 更新导航状态
  document.querySelectorAll('.nav-btn').forEach(btn => {
    btn.classList.remove('active');
  });
  document.querySelector(`[data-app="${appName}"]`).classList.add('active');
  
  // 切换应用显示
  document.querySelectorAll('.app-container').forEach(container => {
    container.classList.add('hidden');
  });
  document.getElementById(`${appName}-app`).classList.remove('hidden');
  
  // 更新状态显示
  document.getElementById('current-app').textContent = getAppDisplayName(appName);
  updateLoadedApps();
}

function getAppDisplayName(appName) {
  const names = {
    'home': '主页',
    'react': 'React 应用',
    'vue': 'Vue 应用'
  };
  return names[appName] || appName;
}

function updateReactCounter() {
  reactCounter++;
  document.getElementById('react-counter').textContent = reactCounter;
}

function updateVueCounter() {
  vueCounter++;
  document.getElementById('vue-counter').textContent = vueCounter;
}

function sendMessageFromReact() {
  const message = `Hello from React! Count: ${reactCounter}`;
  const vueMessages = document.getElementById('vue-messages');
  const messageDiv = document.createElement('div');
  messageDiv.className = 'message';
  messageDiv.textContent = `React: ${message}`;
  vueMessages.appendChild(messageDiv);
  
  messageCount++;
  updateMessageCount();
}

function sendMessageFromVue() {
  const message = `Hello from Vue! Count: ${vueCounter}`;
  const reactMessages = document.getElementById('react-messages');
  const messageDiv = document.createElement('div');
  messageDiv.className = 'message';
  messageDiv.textContent = `Vue: ${message}`;
  reactMessages.appendChild(messageDiv);
  
  messageCount++;
  updateMessageCount();
}

function updateLoadedApps() {
  const activeApps = document.querySelectorAll('.app-container:not(.hidden)').length;
  document.getElementById('loaded-apps').textContent = activeApps;
}

function updateMessageCount() {
  document.getElementById('message-count').textContent = messageCount;
}
</script>

<style>
.playground-container {
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
  background: var(--vp-c-bg-soft);
}

.playground-header {
  text-align: center;
  margin-bottom: 24px;
}

.playground-header h3 {
  margin: 0 0 8px 0;
  color: var(--vp-c-brand-1);
}

.playground-demo {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-nav {
  display: flex;
  background: var(--vp-c-bg);
  border-bottom: 1px solid var(--vp-c-border);
}

.nav-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.nav-btn:hover {
  background: var(--vp-c-bg-soft);
}

.nav-btn.active {
  background: var(--vp-c-brand-1);
  color: white;
}

.demo-content {
  min-height: 300px;
  padding: 24px;
}

.app-container {
  transition: opacity 0.3s ease;
}

.app-container.hidden {
  display: none;
}

.welcome-card {
  text-align: center;
  padding: 40px 20px;
}

.welcome-card h2 {
  margin: 0 0 16px 0;
  color: var(--vp-c-text-1);
}

.features {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 32px;
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.feature .icon {
  font-size: 24px;
}

.react-demo, .vue-demo {
  padding: 20px;
  border-radius: 8px;
}

.react-demo {
  border: 2px solid #61dafb;
  background: rgba(97, 218, 251, 0.1);
}

.vue-demo {
  border: 2px solid #4fc08d;
  background: rgba(79, 192, 141, 0.1);
}

.counter-demo, .communication-demo {
  margin: 16px 0;
  padding: 16px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
}

.playground-info {
  margin-top: 24px;
  padding: 16px;
  background: var(--vp-c-bg);
  border-radius: 8px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--vp-c-bg-soft);
  border-radius: 4px;
}

.status-item .label {
  color: var(--vp-c-text-2);
}

.status-item .value {
  font-weight: 600;
  color: var(--vp-c-brand-1);
}

.message {
  padding: 8px 12px;
  margin: 4px 0;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-size: 14px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: var(--vp-c-brand-1);
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

button:hover {
  background: var(--vp-c-brand-2);
}

@media (max-width: 768px) {
  .features {
    flex-direction: column;
    gap: 16px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
