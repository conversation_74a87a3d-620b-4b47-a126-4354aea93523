# 性能分析

Micro-Core 提供了全面的性能分析工具，帮助开发者深入了解微前端应用的性能表现，识别性能瓶颈并进行针对性优化。

## 📋 目录

- [性能分析概述](#性能分析概述)
- [性能指标监控](#性能指标监控)
- [应用加载性能](#应用加载性能)
- [运行时性能](#运行时性能)
- [内存分析](#内存分析)
- [网络性能](#网络性能)
- [渲染性能](#渲染性能)
- [性能优化建议](#性能优化建议)
- [性能基准测试](#性能基准测试)

## 性能分析概述

### 🎯 性能分析目标

```typescript
// 性能分析配置
const performanceConfig = {
  // 监控指标
  metrics: {
    loadTime: true,        // 加载时间
    renderTime: true,      // 渲染时间
    memoryUsage: true,     // 内存使用
    networkRequests: true, // 网络请求
    userInteraction: true  // 用户交互响应
  },
  
  // 采样设置
  sampling: {
    interval: 1000,        // 采样间隔(ms)
    duration: 300000,      // 监控时长(ms)
    bufferSize: 1000       // 数据缓冲区大小
  },
  
  // 阈值设置
  thresholds: {
    loadTime: 3000,        // 加载时间阈值(ms)
    memoryUsage: 50,       // 内存使用阈值(MB)
    renderTime: 16,        // 渲染时间阈值(ms)
    networkLatency: 500    // 网络延迟阈值(ms)
  }
}
```

### 📊 性能分析架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 性能分析架构                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    数据收集层                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 性能API     │  │ 自定义埋点   │  │ 浏览器API           │ │ │
│  │  │ • Navigation│  │ • 应用生命周期│  │ • Memory API       │ │ │
│  │  │ • Resource  │  │ • 用户交互   │  │ • Observer API     │ │ │
│  │  │ • Paint     │  │ • 业务指标   │  │ • DevTools API     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    数据处理层                               │ │
│  │  • 数据聚合        • 统计分析      • 异常检测              │ │
│  │  • 趋势分析        • 对比分析      • 预警机制              │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    可视化展示层                             │ │
│  │  • 实时图表        • 性能报告      • 优化建议              │ │
│  │  • 对比分析        • 历史趋势      • 导出功能              │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 性能指标监控

### 📈 核心性能指标

```typescript
// 性能指标收集器
class PerformanceCollector {
  private metrics: PerformanceMetrics = {
    // Web Vitals 指标
    webVitals: {
      FCP: 0,    // First Contentful Paint
      LCP: 0,    // Largest Contentful Paint
      FID: 0,    // First Input Delay
      CLS: 0,    // Cumulative Layout Shift
      TTFB: 0    // Time to First Byte
    },
    
    // 应用加载指标
    appLoading: {
      loadStart: 0,
      loadEnd: 0,
      loadDuration: 0,
      resourceCount: 0,
      resourceSize: 0
    },
    
    // 运行时指标
    runtime: {
      memoryUsage: 0,
      cpuUsage: 0,
      renderTime: 0,
      interactionTime: 0
    },
    
    // 网络指标
    network: {
      requestCount: 0,
      totalSize: 0,
      averageLatency: 0,
      errorRate: 0
    }
  }
  
  // 开始性能监控
  startMonitoring() {
    this.collectWebVitals()
    this.collectAppMetrics()
    this.collectRuntimeMetrics()
    this.collectNetworkMetrics()
  }
  
  // 收集 Web Vitals
  private collectWebVitals() {
    // FCP - First Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const fcp = entries.find(entry => entry.name === 'first-contentful-paint')
      if (fcp) {
        this.metrics.webVitals.FCP = fcp.startTime
      }
    }).observe({ entryTypes: ['paint'] })
    
    // LCP - Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      this.metrics.webVitals.LCP = lastEntry.startTime
    }).observe({ entryTypes: ['largest-contentful-paint'] })
    
    // FID - First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.processingStart && entry.startTime) {
          this.metrics.webVitals.FID = entry.processingStart - entry.startTime
        }
      })
    }).observe({ entryTypes: ['first-input'] })
    
    // CLS - Cumulative Layout Shift
    let clsValue = 0
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      })
      this.metrics.webVitals.CLS = clsValue
    }).observe({ entryTypes: ['layout-shift'] })
  }
  
  // 收集应用指标
  private collectAppMetrics() {
    // 监听应用加载事件
    this.microCore.on('app:load:start', (appName) => {
      this.metrics.appLoading.loadStart = performance.now()
    })
    
    this.microCore.on('app:load:end', (appName) => {
      this.metrics.appLoading.loadEnd = performance.now()
      this.metrics.appLoading.loadDuration = 
        this.metrics.appLoading.loadEnd - this.metrics.appLoading.loadStart
    })
  }
  
  // 获取性能报告
  getPerformanceReport(): PerformanceReport {
    return {
      timestamp: new Date(),
      metrics: this.metrics,
      score: this.calculatePerformanceScore(),
      recommendations: this.generateRecommendations()
    }
  }
}
```

### 🎯 性能评分系统

```typescript
// 性能评分计算
class PerformanceScorer {
  // 计算综合性能分数
  calculateScore(metrics: PerformanceMetrics): PerformanceScore {
    const scores = {
      loading: this.calculateLoadingScore(metrics.webVitals),
      interactivity: this.calculateInteractivityScore(metrics.webVitals),
      visualStability: this.calculateVisualStabilityScore(metrics.webVitals),
      resource: this.calculateResourceScore(metrics.network)
    }
    
    // 加权平均计算总分
    const totalScore = (
      scores.loading * 0.3 +
      scores.interactivity * 0.3 +
      scores.visualStability * 0.2 +
      scores.resource * 0.2
    )
    
    return {
      total: Math.round(totalScore),
      breakdown: scores,
      grade: this.getGrade(totalScore)
    }
  }
  
  // 计算加载性能分数
  private calculateLoadingScore(webVitals: WebVitals): number {
    const fcpScore = this.scoreMetric(webVitals.FCP, [1800, 3000])
    const lcpScore = this.scoreMetric(webVitals.LCP, [2500, 4000])
    return (fcpScore + lcpScore) / 2
  }
  
  // 计算交互性能分数
  private calculateInteractivityScore(webVitals: WebVitals): number {
    return this.scoreMetric(webVitals.FID, [100, 300])
  }
  
  // 计算视觉稳定性分数
  private calculateVisualStabilityScore(webVitals: WebVitals): number {
    return this.scoreMetric(webVitals.CLS, [0.1, 0.25], true)
  }
  
  // 指标评分
  private scoreMetric(value: number, thresholds: [number, number], reverse = false): number {
    const [good, poor] = thresholds
    let score: number
    
    if (reverse) {
      // 数值越小越好（如 CLS）
      if (value <= good) score = 100
      else if (value <= poor) score = 50 + (poor - value) / (poor - good) * 50
      else score = 0
    } else {
      // 数值越小越好（如 FCP, LCP, FID）
      if (value <= good) score = 100
      else if (value <= poor) score = 50 + (poor - value) / (poor - good) * 50
      else score = 0
    }
    
    return Math.max(0, Math.min(100, score))
  }
  
  // 获取性能等级
  private getGrade(score: number): string {
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }
}
```

## 应用加载性能

### ⚡ 加载时间分析

```typescript
// 应用加载性能分析
class LoadPerformanceAnalyzer {
  private loadMetrics: Map<string, LoadMetrics> = new Map()
  
  // 分析应用加载性能
  analyzeLoadPerformance(appName: string): LoadAnalysis {
    const metrics = this.loadMetrics.get(appName)
    if (!metrics) return null
    
    return {
      appName,
      loadTime: metrics.loadTime,
      breakdown: {
        dns: metrics.dnsTime,
        tcp: metrics.tcpTime,
        request: metrics.requestTime,
        response: metrics.responseTime,
        processing: metrics.processingTime
      },
      resources: this.analyzeResources(metrics.resources),
      bottlenecks: this.identifyBottlenecks(metrics),
      recommendations: this.generateLoadRecommendations(metrics)
    }
  }
  
  // 资源分析
  private analyzeResources(resources: ResourceMetrics[]): ResourceAnalysis {
    const totalSize = resources.reduce((sum, r) => sum + r.size, 0)
    const totalTime = Math.max(...resources.map(r => r.loadTime))
    
    return {
      totalCount: resources.length,
      totalSize,
      totalTime,
      breakdown: {
        js: resources.filter(r => r.type === 'script'),
        css: resources.filter(r => r.type === 'stylesheet'),
        images: resources.filter(r => r.type === 'image'),
        fonts: resources.filter(r => r.type === 'font'),
        other: resources.filter(r => !['script', 'stylesheet', 'image', 'font'].includes(r.type))
      },
      largestResources: resources
        .sort((a, b) => b.size - a.size)
        .slice(0, 10),
      slowestResources: resources
        .sort((a, b) => b.loadTime - a.loadTime)
        .slice(0, 10)
    }
  }
  
  // 识别性能瓶颈
  private identifyBottlenecks(metrics: LoadMetrics): Bottleneck[] {
    const bottlenecks: Bottleneck[] = []
    
    // DNS 解析慢
    if (metrics.dnsTime > 200) {
      bottlenecks.push({
        type: 'dns',
        severity: 'high',
        description: 'DNS 解析时间过长',
        impact: `DNS 解析耗时 ${metrics.dnsTime}ms`,
        suggestion: '考虑使用 DNS 预解析或 CDN'
      })
    }
    
    // 资源过大
    const largeResources = metrics.resources.filter(r => r.size > 1024 * 1024) // > 1MB
    if (largeResources.length > 0) {
      bottlenecks.push({
        type: 'resource-size',
        severity: 'medium',
        description: '存在大体积资源',
        impact: `${largeResources.length} 个资源超过 1MB`,
        suggestion: '压缩资源或使用懒加载'
      })
    }
    
    // 请求数量过多
    if (metrics.resources.length > 50) {
      bottlenecks.push({
        type: 'request-count',
        severity: 'medium',
        description: '网络请求数量过多',
        impact: `总共 ${metrics.resources.length} 个请求`,
        suggestion: '合并资源或使用 HTTP/2'
      })
    }
    
    return bottlenecks
  }
}
```

### 📊 加载瀑布图

```typescript
    // 加载瀑布图数据
const waterfallData = {
  timeline: [
    {
      name: 'HTML Document',
      start: 0,
      duration: 150,
      type: 'document',
      size: '12KB'
    },
    {
      name: 'app.js',
      start: 50,
      duration: 300,
      type: 'script',
      size: '245KB'
    },
    {
      name: 'styles.css',
      start: 80,
      duration: 120,
      type: 'stylesheet',
      size: '45KB'
    },
    {
      name: 'vendor.js',
      start: 100,
      duration: 450,
      type: 'script',
      size: '680KB'
    }
  ]
}
```

## 运行时性能

### 🔄 实时性能监控

```typescript
// 运行时性能监控器
class RuntimePerformanceMonitor {
  private frameMetrics: FrameMetric[] = []
  private interactionMetrics: InteractionMetric[] = []
  
  constructor() {
    this.startFrameMonitoring()
    this.startInteractionMonitoring()
  }
  
  // 监控帧率性能
  private startFrameMonitoring() {
    let lastFrameTime = performance.now()
    let frameCount = 0
    
    const measureFrame = () => {
      const currentTime = performance.now()
      const frameDuration = currentTime - lastFrameTime
      
      this.frameMetrics.push({
        timestamp: currentTime,
        duration: frameDuration,
        fps: 1000 / frameDuration
      })
      
      // 保持最近1000帧的数据
      if (this.frameMetrics.length > 1000) {
        this.frameMetrics.shift()
      }
      
      lastFrameTime = currentTime
      frameCount++
      
      requestAnimationFrame(measureFrame)
    }
    
    requestAnimationFrame(measureFrame)
  }
  
  // 监控用户交互性能
  private startInteractionMonitoring() {
    // 监控点击事件
    document.addEventListener('click', (event) => {
      const startTime = performance.now()
      
      // 使用 setTimeout 测量响应时间
      setTimeout(() => {
        const responseTime = performance.now() - startTime
        
        this.interactionMetrics.push({
          type: 'click',
          timestamp: startTime,
          responseTime,
          target: event.target?.tagName || 'unknown'
        })
      }, 0)
    })
    
    // 监控输入事件
    document.addEventListener('input', (event) => {
      const startTime = performance.now()
      
      requestAnimationFrame(() => {
        const responseTime = performance.now() - startTime
        
        this.interactionMetrics.push({
          type: 'input',
          timestamp: startTime,
          responseTime,
          target: event.target?.tagName || 'unknown'
        })
      })
    })
  }
  
  // 获取性能报告
  getPerformanceReport(): RuntimePerformanceReport {
    const recentFrames = this.frameMetrics.slice(-100) // 最近100帧
    const recentInteractions = this.interactionMetrics.slice(-50) // 最近50次交互
    
    return {
      frameRate: {
        average: recentFrames.reduce((sum, f) => sum + f.fps, 0) / recentFrames.length,
        min: Math.min(...recentFrames.map(f => f.fps)),
        max: Math.max(...recentFrames.map(f => f.fps)),
        p95: this.calculatePercentile(recentFrames.map(f => f.fps), 95)
      },
      
      interactionResponse: {
        average: recentInteractions.reduce((sum, i) => sum + i.responseTime, 0) / recentInteractions.length,
        p95: this.calculatePercentile(recentInteractions.map(i => i.responseTime), 95),
        slowInteractions: recentInteractions.filter(i => i.responseTime > 100)
      },
      
      recommendations: this.generateRuntimeRecommendations(recentFrames, recentInteractions)
    }
  }
}

// 性能指标接口
interface FrameMetric {
  timestamp: number
  duration: number
  fps: number
}

interface InteractionMetric {
  type: string
  timestamp: number
  responseTime: number
  target: string
}
```

## 内存分析

### 💾 内存使用监控

```typescript
// 内存分析器
class MemoryAnalyzer {
  private memorySnapshots: MemorySnapshot[] = []
  private leakDetector: LeakDetector
  
  constructor() {
    this.leakDetector = new LeakDetector()
    this.startMemoryMonitoring()
  }
  
  // 开始内存监控
  private startMemoryMonitoring() {
    if ('memory' in performance) {
      setInterval(() => {
        this.takeMemorySnapshot()
      }, 10000) // 每10秒采样一次
    }
  }
  
  // 获取内存快照
  private takeMemorySnapshot() {
    const memory = (performance as any).memory
    
    const snapshot: MemorySnapshot = {
      timestamp: Date.now(),
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit,
      apps: this.getAppMemoryUsage()
    }
    
    this.memorySnapshots.push(snapshot)
    
    // 保持最近100个快照
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots.shift()
    }
    
    // 检测内存泄漏
    this.leakDetector.analyze(snapshot)
  }
  
  // 获取应用内存使用情况
  private getAppMemoryUsage(): Record<string, number> {
    const appMemory: Record<string, number> = {}
    
    // 这里需要与微前端框架集成来获取每个应用的内存使用
    // 实际实现会根据具体的框架API来获取
    
    return appMemory
  }
  
  // 分析内存趋势
  analyzeMemoryTrend(): MemoryTrendAnalysis {
    if (this.memorySnapshots.length < 10) {
      return { trend: 'insufficient-data' }
    }
    
    const recent = this.memorySnapshots.slice(-10)
    const older = this.memorySnapshots.slice(-20, -10)
    
    const recentAvg = recent.reduce((sum, s) => sum + s.used, 0) / recent.length
    const olderAvg = older.reduce((sum, s) => sum + s.used, 0) / older.length
    
    const growthRate = (recentAvg - olderAvg) / olderAvg
    
    return {
      trend: growthRate > 0.1 ? 'increasing' : growthRate < -0.1 ? 'decreasing' : 'stable',
      growthRate,
      currentUsage: recentAvg,
      peakUsage: Math.max(...this.memorySnapshots.map(s => s.used)),
      recommendations: this.generateMemoryRecommendations(growthRate, recentAvg)
    }
  }
}

// 内存泄漏检测器
class LeakDetector {
  private suspiciousPatterns: SuspiciousPattern[] = []
  
  analyze(snapshot: MemorySnapshot) {
    // 检测持续增长的内存使用
    this.detectContinuousGrowth(snapshot)
    
    // 检测异常的内存峰值
    this.detectMemorySpikes(snapshot)
    
    // 检测应用卸载后的内存残留
    this.detectMemoryRetention(snapshot)
  }
  
  private detectContinuousGrowth(snapshot: MemorySnapshot) {
    // 实现持续增长检测逻辑
  }
  
  private detectMemorySpikes(snapshot: MemorySnapshot) {
    // 实现内存峰值检测逻辑
  }
  
  private detectMemoryRetention(snapshot: MemorySnapshot) {
    // 实现内存残留检测逻辑
  }
}
```

## 网络性能

### 🌐 网络请求分析

```typescript
// 网络性能分析器
class NetworkPerformanceAnalyzer {
  private requests: NetworkRequest[] = []
  private maxRequests = 1000
  
  constructor() {
    this.interceptNetworkRequests()
  }
  
  // 拦截网络请求
  private interceptNetworkRequests() {
    // 拦截 fetch 请求
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const startTime = performance.now()
      const url = args[0] as string
      const options = args[1] as RequestInit
      
      try {
        const response = await originalFetch(...args)
        const endTime = performance.now()
        
        this.recordRequest({
          url,
          method: options?.method || 'GET',
          startTime,
          endTime,
          duration: endTime - startTime,
          status: response.status,
          size: parseInt(response.headers.get('content-length') || '0'),
          type: this.getRequestType(url),
          success: response.ok
        })
        
        return response
      } catch (error) {
        const endTime = performance.now()
        
        this.recordRequest({
          url,
          method: options?.method || 'GET',
          startTime,
          endTime,
          duration: endTime - startTime,
          status: 0,
          size: 0,
          type: this.getRequestType(url),
          success: false,
          error: error.message
        })
        
        throw error
      }
    }
    
    // 拦截 XMLHttpRequest
    const originalXHR = window.XMLHttpRequest
    window.XMLHttpRequest = class extends originalXHR {
      private _startTime: number
      private _url: string
      private _method: string
      
      open(method: string, url: string, ...args: any[]) {
        this._method = method
        this._url = url
        this._startTime = performance.now()
        return super.open(method, url, ...args)
      }
      
      send(...args: any[]) {
        this.addEventListener('loadend', () => {
          const endTime = performance.now()
          
          this.recordRequest({
            url: this._url,
            method: this._method,
            startTime: this._startTime,
            endTime,
            duration: endTime - this._startTime,
            status: this.status,
            size: parseInt(this.getResponseHeader('content-length') || '0'),
            type: this.getRequestType(this._url),
            success: this.status >= 200 && this.status < 300
          })
        })
        
        return super.send(...args)
      }
    }
  }
  
  private recordRequest(request: NetworkRequest) {
    this.requests.unshift(request)
    
    if (this.requests.length > this.maxRequests) {
      this.requests = this.requests.slice(0, this.maxRequests)
    }
  }
  
  private getRequestType(url: string): string {
    if (url.includes('.js')) return 'script'
    if (url.includes('.css')) return 'stylesheet'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image'
    if (url.includes('/api/')) return 'api'
    return 'other'
  }
  
  // 分析网络性能
  analyzeNetworkPerformance(): NetworkPerformanceReport {
    const requests = this.requests
    const successfulRequests = requests.filter(r => r.success)
    const failedRequests = requests.filter(r => !r.success)
    
    return {
      totalRequests: requests.length,
      successRate: successfulRequests.length / requests.length,
      averageLatency: successfulRequests.reduce((sum, r) => sum + r.duration, 0) / successfulRequests.length,
      totalDataTransferred: requests.reduce((sum, r) => sum + r.size, 0),
      
      requestsByType: this.groupRequestsByType(requests),
      slowestRequests: requests.sort((a, b) => b.duration - a.duration).slice(0, 10),
      failedRequests: failedRequests,
      
      recommendations: this.generateNetworkRecommendations(requests)
    }
  }
  
  private generateNetworkRecommendations(requests: NetworkRequest[]): string[] {
    const recommendations: string[] = []
    
    // 检查慢请求
    const slowRequests = requests.filter(r => r.duration > 1000)
    if (slowRequests.length > 0) {
      recommendations.push(`发现 ${slowRequests.length} 个慢请求，建议优化服务器响应时间`)
    }
    
    // 检查大文件
    const largeRequests = requests.filter(r => r.size > 1024 * 1024) // > 1MB
    if (largeRequests.length > 0) {
      recommendations.push(`发现 ${largeRequests.length} 个大文件，建议启用压缩或分块加载`)
    }
    
    // 检查请求数量
    if (requests.length > 50) {
      recommendations.push('请求数量较多，建议合并资源或使用 HTTP/2')
    }
    
    return recommendations
  }
}

// 网络请求接口
interface NetworkRequest {
  url: string
  method: string
  startTime: number
  endTime: number
  duration: number
  status: number
  size: number
  type: string
  success: boolean
  error?: string
}
```

## 渲染性能

### 🎨 渲染性能监控

```typescript
// 渲染性能分析器
class RenderPerformanceAnalyzer {
  private paintMetrics: PaintMetric[] = []
  private layoutMetrics: LayoutMetric[] = []
  
  constructor() {
    this.setupRenderMonitoring()
  }
  
  private setupRenderMonitoring() {
    // 监控绘制性能
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.entryType === 'paint') {
          this.paintMetrics.push({
            name: entry.name,
            startTime: entry.startTime,
            duration: entry.duration || 0
          })
        }
      })
    }).observe({ entryTypes: ['paint'] })
    
    // 监控布局偏移
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
          this.layoutMetrics.push({
            value: entry.value,
            startTime: entry.startTime,
            sources: entry.sources?.map(source => ({
              node: source.node?.tagName || 'unknown',
              previousRect: source.previousRect,
              currentRect: source.currentRect
            })) || []
          })
        }
      })
    }).observe({ entryTypes: ['layout-shift'] })
  }
  
  // 分析渲染性能
  analyzeRenderPerformance(): RenderPerformanceReport {
    const totalCLS = this.layoutMetrics.reduce((sum, metric) => sum + metric.value, 0)
    const paintTimes = this.paintMetrics.reduce((acc, metric) => {
      acc[metric.name] = metric.startTime
      return acc
    }, {} as Record<string, number>)
    
    return {
      cumulativeLayoutShift: totalCLS,
      firstContentfulPaint: paintTimes['first-contentful-paint'] || 0,
      firstPaint: paintTimes['first-paint'] || 0,
      layoutShifts: this.layoutMetrics.length,
      
      recommendations: this.generateRenderRecommendations(totalCLS, this.layoutMetrics)
    }
  }
  
  private generateRenderRecommendations(cls: number, shifts: LayoutMetric[]): string[] {
    const recommendations: string[] = []
    
    if (cls > 0.25) {
      recommendations.push('累积布局偏移过高，建议为图片和广告预留空间')
    }
    
    if (shifts.length > 10) {
      recommendations.push('布局偏移次数过多，检查动态内容加载')
    }
    
    return recommendations
  }
}

// 渲染指标接口
interface PaintMetric {
  name: string
  startTime: number
  duration: number
}

interface LayoutMetric {
  value: number
  startTime: number
  sources: Array<{
    node: string
    previousRect: DOMRect
    currentRect: DOMRect
  }>
}
```

## 性能优化建议

### 🎯 智能优化建议

```typescript
// 性能优化建议生成器
class PerformanceOptimizer {
  generateRecommendations(report: PerformanceReport): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = []
    
    // 加载性能优化
    if (report.loadTime > 3000) {
      recommendations.push({
        category: 'loading',
        priority: 'high',
        title: '优化应用加载时间',
        description: `当前加载时间为 ${report.loadTime}ms，超过推荐值 3000ms`,
        suggestions: [
          '启用代码分割和懒加载',
          '压缩和优化静态资源',
          '使用 CDN 加速资源加载',
          '启用浏览器缓存策略'
        ],
        impact: 'high',
        effort: 'medium'
      })
    }
    
    // 内存优化
    if (report.memoryUsage > 50 * 1024 * 1024) { // > 50MB
      recommendations.push({
        category: 'memory',
        priority: 'medium',
        title: '优化内存使用',
        description: `当前内存使用 ${this.formatBytes(report.memoryUsage)}，建议优化`,
        suggestions: [
          '及时清理未使用的事件监听器',
          '避免创建不必要的对象引用',
          '使用对象池复用大对象',
          '定期检查内存泄漏'
        ],
        impact: 'medium',
        effort: 'high'
      })
    }
    
    // 网络优化
    if (report.networkRequests > 50) {
      recommendations.push({
        category: 'network',
        priority: 'medium',
        title: '减少网络请求数量',
        description: `当前有 ${report.networkRequests} 个网络请求`,
        suggestions: [
          '合并 CSS 和 JavaScript 文件',
          '使用雪碧图合并小图标',
          '启用 HTTP/2 多路复用',
          '使用内联关键资源'
        ],
        impact: 'medium',
        effort: 'medium'
      })
    }
    
    // 渲染优化
    if (report.cls > 0.25) {
      recommendations.push({
        category: 'rendering',
        priority: 'high',
        title: '减少累积布局偏移',
        description: `CLS 值为 ${report.cls.toFixed(3)}，超过推荐值 0.25`,
        suggestions: [
          '为图片和视频设置尺寸属性',
          '为动态内容预留空间',
          '避免在现有内容上方插入内容',
          '使用 transform 动画替代改变布局的动画'
        ],
        impact: 'high',
        effort: 'low'
      })
    }
    
    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }
  
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// 优化建议接口
interface OptimizationRecommendation {
  category: 'loading' | 'memory' | 'network' | 'rendering'
  priority: 'high' | 'medium' | 'low'
  title: string
  description: string
  suggestions: string[]
  impact: 'high' | 'medium' | 'low'
  effort: 'high' | 'medium' | 'low'
}
```

## 性能基准测试

### 📊 基准测试套件

```typescript
// 性能基准测试
class PerformanceBenchmark {
  private benchmarks: BenchmarkSuite[] = []
  
  constructor(private microCore: MicroCore) {
    this.setupBenchmarks()
  }
  
  private setupBenchmarks() {
    // 应用加载基准测试
    this.benchmarks.push({
      name: 'App Loading',
      tests: [
        {
          name: 'Cold Start',
          run: () => this.testColdStart(),
          threshold: 3000
        },
        {
          name: 'Warm Start',
          run: () => this.testWarmStart(),
          threshold: 1000
        },
        {
          name: 'App Switch',
          run: () => this.testAppSwitch(),
          threshold: 500
        }
      ]
    })
    
    // 内存使用基准测试
    this.benchmarks.push({
      name: 'Memory Usage',
      tests: [
        {
          name: 'Initial Memory',
          run: () => this.testInitialMemory(),
          threshold: 20 * 1024 * 1024 // 20MB
        },
        {
          name: 'Memory After Load',
          run: () => this.testMemoryAfterLoad(),
          threshold: 50 * 1024 * 1024 // 50MB
        }
      ]
    })
  }
  
  // 运行所有基准测试
  async runAllBenchmarks(): Promise<BenchmarkResult[]> {
    const results: BenchmarkResult[] = []
    
    for (const suite of this.benchmarks) {
      const suiteResults = await this.runBenchmarkSuite(suite)
      results.push(...suiteResults)
    }
    
    return results
  }
  
  private async runBenchmarkSuite(suite: BenchmarkSuite): Promise<BenchmarkResult[]> {
    const results: BenchmarkResult[] = []
    
    for (const test of suite.tests) {
      const result = await this.runBenchmarkTest(suite.name, test)
      results.push(result)
    }
    
    return results
  }
  
  private async runBenchmarkTest(suiteName: string, test: BenchmarkTest): Promise<BenchmarkResult> {
    const iterations = 5
    const measurements: number[] = []
    
    for (let i = 0; i < iterations; i++) {
      const measurement = await test.run()
      measurements.push(measurement)
      
      // 等待一段时间再进行下一次测试
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    const average = measurements.reduce((sum, m) => sum + m, 0) / measurements.length
    const min = Math.min(...measurements)
    const max = Math.max(...measurements)
    
    return {
      suite: suiteName,
      test: test.name,
      measurements,
      average,
      min,
      max,
      threshold: test.threshold,
      passed: average <= test.threshold,
      score: this.calculateScore(average, test.threshold)
    }
  }
  
  private calculateScore(value: number, threshold: number): number {
    if (value <= threshold * 0.5) return 100
    if (value <= threshold) return 80 - (value / threshold - 0.5) * 60
    return Math.max(0, 20 - (value / threshold - 1) * 20)
  }
  
  // 冷启动测试
  private async testColdStart(): Promise<number> {
    // 清除缓存
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map(name => caches.delete(name)))
    }
    
    const startTime = performance.now()
    
    // 重新加载应用
    await this.microCore.reloadApp('test-app')
    
    return performance.now() - startTime
  }
  
  // 热启动测试
  private async testWarmStart(): Promise<number> {
    const startTime = performance.now()
    
    // 加载已缓存的应用
    await this.microCore.loadApp('test-app')
    
    return performance.now() - startTime
  }
  
  // 应用切换测试
  private async testAppSwitch(): Promise<number> {
    const startTime = performance.now()
    
    // 从应用A切换到应用B
    await this.microCore.navigateToApp('app-b')
    
    return performance.now() - startTime
  }
  
  // 初始内存测试
  private async testInitialMemory(): Promise<number> {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize
    }
    return 0
  }
  
  // 加载后内存测试
  private async testMemoryAfterLoad(): Promise<number> {
    await this.microCore.loadApp('test-app')
    
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize
    }
    return 0
  }
}

// 基准测试接口
interface BenchmarkSuite {
  name: string
  tests: BenchmarkTest[]
}

interface BenchmarkTest {
  name: string
  run: () => Promise<number>
  threshold: number
}

interface BenchmarkResult {
  suite: string
  test: string
  measurements: number[]
  average: number
  min: number
  max: number
  threshold: number
  passed: boolean
  score: number
}
```

## 性能报告

### 📋 综合性能报告

```typescript
// 性能报告生成器
class PerformanceReporter {
  generateReport(data: PerformanceData): PerformanceReport {
    return {
      timestamp: new Date(),
      summary: this.generateSummary(data),
      webVitals: this.analyzeWebVitals(data.webVitals),
      loading: this.analyzeLoading(data.loading),
      runtime: this.analyzeRuntime(data.runtime),
      memory: this.analyzeMemory(data.memory),
      network: this.analyzeNetwork(data.network),
      rendering: this.analyzeRendering(data.rendering),
      recommendations: this.generateRecommendations(data),
      score: this.calculateOverallScore(data)
    }
  }
  
  private generateSummary(data: PerformanceData): PerformanceSummary {
    return {
      overallScore: this.calculateOverallScore(data),
      grade: this.getPerformanceGrade(this.calculateOverallScore(data)),
      keyMetrics: {
        loadTime: data.loading.totalTime,
        fcp: data.webVitals.FCP,
        lcp: data.webVitals.LCP,
        fid: data.webVitals.FID,
        cls: data.webVitals.CLS
      },
      status: this.getOverallStatus(data)
    }
  }
  
  private getPerformanceGrade(score: number): string {
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }
  
  // 导出报告
  exportReport(report: PerformanceReport, format: 'json' | 'html' | 'pdf' = 'json'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(report, null, 2)
      
      case 'html':
        return this.generateHTMLReport(report)
      
      case 'pdf':
        // 这里需要集成PDF生成库
        return this.generatePDFReport(report)
      
      default:
        return JSON.stringify(report, null, 2)
    }
  }
  
  private generateHTMLReport(report: PerformanceReport): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>性能分析报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .score { font-size: 48px; font-weight: bold; color: ${this.getScoreColor(report.score)}; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .metric { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>性能分析报告</h1>
        <div class="score">${report.score}</div>
        <p>生成时间: ${report.timestamp}</p>
    </div>
    
    <div class="metrics">
        <div class="metric">
            <h3>加载时间</h3>
            <p>${report.summary.keyMetrics.loadTime}ms</p>
        </div>
        <div class="metric">
            <h3>首次内容绘制</h3>
            <p>${report.summary.keyMetrics.fcp}ms</p>
        </div>
        <div class="metric">
            <h3>最大内容绘制</h3>
            <p>${report.summary.keyMetrics.lcp}ms</p>
        </div>
    </div>
    
    <h2>优化建议</h2>
    <ul>
        ${report.recommendations.map(rec => `<li>${rec.title}: ${rec.description}</li>`).join('')}
    </ul>
</body>
</html>`
  }
  
  private getScoreColor(score: number): string {
    if (score >= 90) return '#4CAF50'
    if (score >= 70) return '#FF9800'
    return '#F44336'
  }
}
```

## 使用示例

### 🚀 完整使用示例

```typescript
import { MicroCore, PerformanceAnalyzer } from '@micro-core/core'

// 创建微前端实例
const microCore = new MicroCore({
  apps: [
    {
      name: 'app-a',
      entry: 'http://localhost:3001',
      activeWhen: '/app-a'
    }
  ]
})

// 创建性能分析器
const performanceAnalyzer = new PerformanceAnalyzer(microCore, {
  // 启用所有监控功能
  monitoring: {
    webVitals: true,
    loading: true,
    runtime: true,
    memory: true,
    network: true,
    rendering: true
  },
  
  // 采样配置
  sampling: {
    interval: 1000,
    duration: 300000,
    bufferSize: 1000
  },
  
  // 性能阈值
  thresholds: {
    loadTime: 3000,
    fcp: 1800,
    lcp: 2500,
    fid: 100,
    cls: 0.1
  }
})

// 开始性能监控
performanceAnalyzer.startMonitoring()

// 获取性能报告
const report = await performanceAnalyzer.getPerformanceReport()
console.log('性能报告:', report)

// 导出报告
const htmlReport = performanceAnalyzer.exportReport(report, 'html')
console.log('HTML报告:', htmlReport)

// 运行基准测试
const benchmarkResults = await performanceAnalyzer.runBenchmarks()
console.log('基准测试结果:', benchmarkResults)
```

## 最佳实践

### 🎯 性能监控最佳实践

1. **合理设置监控频率**
   ```typescript
   // 开发环境：高频监控
   const devConfig = {
     sampling: { interval: 500 },
     monitoring: { all: true }
   }
   
   // 生产环境：低频监控
   const prodConfig = {
     sampling: { interval: 5000 },
     monitoring: { essential: true }
   }
   ```

2. **设置合理的性能阈值**
   ```typescript
   const thresholds = {
     // 移动端阈值更严格
     mobile: {
       loadTime: 2000,
       fcp: 1200,
       lcp: 2000
     },
     
     // 桌面端阈值相对宽松
     desktop: {
       loadTime: 3000,
       fcp: 1800,
       lcp: 2500
     }
   }
   ```

3. **定期生成性能报告**
   ```typescript
   // 每日性能报告
   setInterval(() => {
     const report = performanceAnalyzer.getPerformanceReport()
     sendReportToServer(report)
   }, 24 * 60 * 60 * 1000)
   ```

### ⚠️ 注意事项

- 性能监控会消耗一定的系统资源，需要平衡监控精度和性能开销
- 在生产环境中应该限制监控功能，避免影响用户体验
- 定期清理历史数据，防止内存泄漏
- 敏感的性能数据应该加密传输和存储

## 总结

Micro-Core 性能分析工具提供了全面的性能监控和分析功能，帮助开发者：

- 🔍 **深入了解** 应用的性能表现
- 📊 **量化分析** 各项性能指标
- 🎯 **精准定位** 性能瓶颈
- 💡 **智能建议** 优化方案
- 📈 **持续监控** 性能趋势

通过合理使用性能分析工具，可以显著提升微前端应用的用户体验和运行效率。

---

## 相关链接

- [调试面板](/playground/debug-panel) - 可视化调试工具
- [开发工具](/playground/dev-tools) - 完整开发工具套件
- [基准测试](/playground/benchmark) - 性能基准测试
- [API 文档](/api/core) - 核心 API 参考
# 性能分析

Micro-Core 提供了全面的性能分析工具，帮助开发者深入了解微前端应用的性能表现，识别性能瓶颈并进行针对性优化。

## 📋 目录

- [性能分析概述](#性能分析概述)
- [性能指标监控](#性能指标监控)
- [应用加载性能](#应用加载性能)
- [运行时性能](#运行时性能)
- [内存分析](#内存分析)
- [网络性能](#网络性能)
- [渲染性能](#渲染性能)
- [性能优化建议](#性能优化建议)
- [性能基准测试](#性能基准测试)

## 性能分析概述

### 🎯 性能分析目标

```typescript
// 性能分析配置
const performanceConfig = {
  // 监控指标
  metrics: {
    loadTime: true,        // 加载时间
    renderTime: true,      // 渲染时间
    memoryUsage: true,     // 内存使用
    networkRequests: true, // 网络请求
    userInteraction: true  // 用户交互响应
  },
  
  // 采样设置
  sampling: {
    interval: 1000,        // 采样间隔(ms)
    duration: 300000,      // 监控时长(ms)
    bufferSize: 1000       // 数据缓冲区大小
  },
  
  // 阈值设置
  thresholds: {
    loadTime: 3000,        // 加载时间阈值(ms)
    memoryUsage: 50,       // 内存使用阈值(MB)
    renderTime: 16,        // 渲染时间阈值(ms)
    networkLatency: 500    // 网络延迟阈值(ms)
  }
}
```

### 📊 性能分析架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 性能分析架构                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    数据收集层                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 性能API     │  │ 自定义埋点   │  │ 浏览器API           │ │ │
│  │  │ • Navigation│  │ • 应用生命周期│  │ • Memory API       │ │ │
│  │  │ • Resource  │  │ • 用户交互   │  │ • Observer API     │ │ │
│  │  │ • Paint     │  │ • 业务指标   │  │ • DevTools API     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    数据处理层                               │ │
│  │  • 数据聚合        • 统计分析      • 异常检测              │ │
│  │  • 趋势分析        • 对比分析      • 预警机制              │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    可视化展示层                             │ │
│  │  • 实时图表        • 性能报告      • 优化建议              │ │
│  │  • 对比分析        • 历史趋势      • 导出功能              │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 性能指标监控

### 📈 核心性能指标

```typescript
// 性能指标收集器
class PerformanceCollector {
  private metrics: PerformanceMetrics = {
    // Web Vitals 指标
    webVitals: {
      FCP: 0,    // First Contentful Paint
      LCP: 0,    // Largest Contentful Paint
      FID: 0,    // First Input Delay
      CLS: 0,    // Cumulative Layout Shift
      TTFB: 0    // Time to First Byte
    },
    
    // 应用加载指标
    appLoading: {
      loadStart: 0,
      loadEnd: 0,
      loadDuration: 0,
      resourceCount: 0,
      resourceSize: 0
    },
    
    // 运行时指标
    runtime: {
      memoryUsage: 0,
      cpuUsage: 0,
      renderTime: 0,
      interactionTime: 0
    },
    
    // 网络指标
    network: {
      requestCount: 0,
      totalSize: 0,
      averageLatency: 0,
      errorRate: 0
    }
  }
  
  // 开始性能监控
  startMonitoring() {
    this.collectWebVitals()
    this.collectAppMetrics()
    this.collectRuntimeMetrics()
    this.collectNetworkMetrics()
  }
  
  // 收集 Web Vitals
  private collectWebVitals() {
    // FCP - First Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const fcp = entries.find(entry => entry.name === 'first-contentful-paint')
      if (fcp) {
        this.metrics.webVitals.FCP = fcp.startTime
      }
    }).observe({ entryTypes: ['paint'] })
    
    // LCP - Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      this.metrics.webVitals.LCP = lastEntry.startTime
    }).observe({ entryTypes: ['largest-contentful-paint'] })
    
    // FID - First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.processingStart && entry.startTime) {
          this.metrics.webVitals.FID = entry.processingStart - entry.startTime
        }
      })
    }).observe({ entryTypes: ['first-input'] })
    
    // CLS - Cumulative Layout Shift
    let clsValue = 0
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      })
      this.metrics.webVitals.CLS = clsValue
    }).observe({ entryTypes: ['layout-shift'] })
  }
  
  // 收集应用指标
  private collectAppMetrics() {
    // 监听应用加载事件
    this.microCore.on('app:load:start', (appName) => {
      this.metrics.appLoading.loadStart = performance.now()
    })
    
    this.microCore.on('app:load:end', (appName) => {
      this.metrics.appLoading.loadEnd = performance.now()
      this.metrics.appLoading.loadDuration = 
        this.metrics.appLoading.loadEnd - this.metrics.appLoading.loadStart
    })
  }
  
  // 获取性能报告
  getPerformanceReport(): PerformanceReport {
    return {
      timestamp: new Date(),
      metrics: this.metrics,
      score: this.calculatePerformanceScore(),
      recommendations: this.generateRecommendations()
    }
  }
}
```

### 🎯 性能评分系统

```typescript
// 性能评分计算
class PerformanceScorer {
  // 计算综合性能分数
  calculateScore(metrics: PerformanceMetrics): PerformanceScore {
    const scores = {
      loading: this.calculateLoadingScore(metrics.webVitals),
      interactivity: this.calculateInteractivityScore(metrics.webVitals),
      visualStability: this.calculateVisualStabilityScore(metrics.webVitals),
      resource: this.calculateResourceScore(metrics.network)
    }
    
    // 加权平均计算总分
    const totalScore = (
      scores.loading * 0.3 +
      scores.interactivity * 0.3 +
      scores.visualStability * 0.2 +
      scores.resource * 0.2
    )
    
    return {
      total: Math.round(totalScore),
      breakdown: scores,
      grade: this.getGrade(totalScore)
    }
  }
  
  // 计算加载性能分数
  private calculateLoadingScore(webVitals: WebVitals): number {
    const fcpScore = this.scoreMetric(webVitals.FCP, [1800, 3000])
    const lcpScore = this.scoreMetric(webVitals.LCP, [2500, 4000])
    return (fcpScore + lcpScore) / 2
  }
  
  // 计算交互性能分数
  private calculateInteractivityScore(webVitals: WebVitals): number {
    return this.scoreMetric(webVitals.FID, [100, 300])
  }
  
  // 计算视觉稳定性分数
  private calculateVisualStabilityScore(webVitals: WebVitals): number {
    return this.scoreMetric(webVitals.CLS, [0.1, 0.25], true)
  }
  
  // 指标评分
  private scoreMetric(value: number, thresholds: [number, number], reverse = false): number {
    const [good, poor] = thresholds
    let score: number
    
    if (reverse) {
      // 数值越小越好（如 CLS）
      if (value <= good) score = 100
      else if (value <= poor) score = 50 + (poor - value) / (poor - good) * 50
      else score = 0
    } else {
      // 数值越小越好（如 FCP, LCP, FID）
      if (value <= good) score = 100
      else if (value <= poor) score = 50 + (poor - value) / (poor - good) * 50
      else score = 0
    }
    
    return Math.max(0, Math.min(100, score))
  }
  
  // 获取性能等级
  private getGrade(score: number): string {
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }
}
```

## 应用加载性能

### ⚡ 加载时间分析

```typescript
// 应用加载性能分析
class LoadPerformanceAnalyzer {
  private loadMetrics: Map<string, LoadMetrics> = new Map()
  
  // 分析应用加载性能
  analyzeLoadPerformance(appName: string): LoadAnalysis {
    const metrics = this.loadMetrics.get(appName)
    if (!metrics) return null
    
    return {
      appName,
      loadTime: metrics.loadTime,
      breakdown: {
        dns: metrics.dnsTime,
        tcp: metrics.tcpTime,
        request: metrics.requestTime,
        response: metrics.responseTime,
        processing: metrics.processingTime
      },
      resources: this.analyzeResources(metrics.resources),
      bottlenecks: this.identifyBottlenecks(metrics),
      recommendations: this.generateLoadRecommendations(metrics)
    }
  }
  
  // 资源分析
  private analyzeResources(resources: ResourceMetrics[]): ResourceAnalysis {
    const totalSize = resources.reduce((sum, r) => sum + r.size, 0)
    const totalTime = Math.max(...resources.map(r => r.loadTime))
    
    return {
      totalCount: resources.length,
      totalSize,
      totalTime,
      breakdown: {
        js: resources.filter(r => r.type === 'script'),
        css: resources.filter(r => r.type === 'stylesheet'),
        images: resources.filter(r => r.type === 'image'),
        fonts: resources.filter(r => r.type === 'font'),
        other: resources.filter(r => !['script', 'stylesheet', 'image', 'font'].includes(r.type))
      },
      largestResources: resources
        .sort((a, b) => b.size - a.size)
        .slice(0, 10),
      slowestResources: resources
        .sort((a, b) => b.loadTime - a.loadTime)
        .slice(0, 10)
    }
  }
  
  // 识别性能瓶颈
  private identifyBottlenecks(metrics: LoadMetrics): Bottleneck[] {
    const bottlenecks: Bottleneck[] = []
    
    // DNS 解析慢
    if (metrics.dnsTime > 200) {
      bottlenecks.push({
        type: 'dns',
        severity: 'high',
        description: 'DNS 解析时间过长',
        impact: `DNS 解析耗时 ${metrics.dnsTime}ms`,
        suggestion: '考虑使用 DNS 预解析或 CDN'
      })
    }
    
    // 资源过大
    const largeResources = metrics.resources.filter(r => r.size > 1024 * 1024) // > 1MB
    if (largeResources.length > 0) {
      bottlenecks.push({
        type: 'resource-size',
        severity: 'medium',
        description: '存在大体积资源',
        impact: `${largeResources.length} 个资源超过 1MB`,
        suggestion: '压缩资源或使用懒加载'
      })
    }
    
    // 请求数量过多
    if (metrics.resources.length > 50) {
      bottlenecks.push({
        type: 'request-count',
        severity: 'medium',
        description: '网络请求数量过多',
        impact: `总共 ${metrics.resources.length} 个请求`,
        suggestion: '合并资源或使用 HTTP/2'
      })
    }
    
    return bottlenecks
  }
}
```

### 📊 加载瀑布图

```typescript
// 加载瀑布图数据
const waterfallData = {
  timeline: [
    {
      name: 'HTML Document',
      start: 0,
      duration: 150,
      type: 'document',
      size: '12KB'
    },
    {
      name: 'app.js',
      start: 50,
      duration: 300,
      type: 'script',
      size: '245KB'
    },
    {
      name: 'styles