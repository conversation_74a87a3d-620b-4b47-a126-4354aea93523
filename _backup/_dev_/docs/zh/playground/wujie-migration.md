# wujie 迁移演练场

wujie 迁移演练场提供了从 wujie 微前端框架迁移到 Micro-Core 的完整演示和实践指南，帮助您快速、安全地完成迁移工作。

## 📋 目录

- [迁移概述](#迁移概述)
- [兼容性演示](#兼容性演示)
- [API 对比](#api-对比)
- [迁移步骤](#迁移步骤)
- [沙箱迁移](#沙箱迁移)
- [通信系统迁移](#通信系统迁移)
- [性能对比](#性能对比)
- [常见问题](#常见问题)

## 迁移概述

### 🎯 迁移优势

```typescript
// wujie vs Micro-Core 对比
const migrationBenefits = {
  sandbox: {
    types: '6种沙箱策略 vs iframe/webcomponent',
    performance: '更高性能的 Proxy 沙箱',
    flexibility: '更灵活的沙箱配置',
    compatibility: '更好的兼容性'
  },
  
  communication: {
    methods: '3种通信方式 vs 事件通信',
    performance: '更高效的通信机制',
    features: '支持中间件和命名空间',
    debugging: '更好的调试支持'
  },
  
  routing: {
    features: '动态路由 + 路由守卫',
    cache: '路由状态缓存',
    animation: '路由切换动画',
    preload: '智能预加载'
  },
  
  development: {
    typescript: '完整 TypeScript 支持',
    debugging: '可视化调试工具',
    testing: '完整测试工具链',
    plugins: '丰富的插件生态'
  }
}
```

### 🚀 迁移演练场界面

```vue
<template>
  <div class="wujie-migration-playground">
    <div class="playground-header">
      <h1>wujie 迁移演练场</h1>
      <div class="migration-status">
        <div class="status-item">
          <span>兼容性</span>
          <span class="status-value compatible">95%</span>
        </div>
        <div class="status-item">
          <span>性能提升</span>
          <span class="status-value improved">+40%</span>
        </div>
        <div class="status-item">
          <span>功能增强</span>
          <span class="status-value enhanced">+60%</span>
        </div>
      </div>
    </div>
    
    <div class="playground-content">
      <div class="migration-demo">
        <div class="demo-section">
          <h3>沙箱对比演示</h3>
          <div class="sandbox-comparison">
            <div class="sandbox-demo">
              <h4>wujie iframe 沙箱</h4>
              <div class="demo-container">
                <iframe 
                  src="/demo/wujie-iframe.html" 
                  class="wujie-iframe"
                ></iframe>
              </div>
              <div class="demo-info">
                <span>隔离级别: 强</span>
                <span>性能: 中等</span>
                <span>兼容性: 好</span>
              </div>
            </div>
            
            <div class="sandbox-demo">
              <h4>Micro-Core Proxy 沙箱</h4>
              <div class="demo-container">
                <div id="microcore-proxy-demo" class="proxy-demo"></div>
              </div>
              <div class="demo-info">
                <span>隔离级别: 强</span>
                <span>性能: 高</span>
                <span>兼容性: 优秀</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="demo-section">
          <h3>通信对比演示</h3>
          <div class="communication-demo">
            <div class="comm-panel">
              <h4>wujie 事件通信</h4>
              <div class="comm-controls">
                <input v-model="wujieMessage" placeholder="输入消息" />
                <button @click="sendWujieMessage">发送 (wujie)</button>
              </div>
              <div class="comm-log">
                <div v-for="msg in wujieMessages" :key="msg.id" class="log-item">
                  {{ msg.timestamp }}: {{ msg.content }}
                </div>
              </div>
            </div>
            
            <div class="comm-panel">
              <h4>Micro-Core 通信</h4>
              <div class="comm-controls">
                <input v-model="microcoreMessage" placeholder="输入消息" />
                <button @click="sendMicrocoreMessage">发送 (Micro-Core)</button>
              </div>
              <div class="comm-log">
                <div v-for="msg in microcoreMessages" :key="msg.id" class="log-item">
                  {{ msg.timestamp }}: {{ msg.content }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="migration-tools">
        <div class="tool-panel">
          <h3>迁移工具</h3>
          
          <div class="tool-section">
            <h4>wujie 配置转换器</h4>
            <textarea 
              v-model="wujieConfig" 
              placeholder="粘贴您的 wujie 配置..."
              rows="8"
            ></textarea>
            <button @click="convertWujieConfig">转换配置</button>
            <textarea 
              v-model="convertedConfig" 
              readonly 
              placeholder="转换后的 Micro-Core 配置"
              rows="8"
            ></textarea>
          </div>
          
          <div class="tool-section">
            <h4>组件代码转换器</h4>
            <div class="code-converter">
              <div class="converter-input">
                <h5>wujie 组件代码</h5>
                <textarea 
                  v-model="wujieComponentCode" 
                  placeholder="粘贴 WujieVue 或 WujieReact 组件代码..."
                  rows="10"
                ></textarea>
              </div>
              <div class="converter-output">
                <h5>Micro-Core 组件代码</h5>
                <textarea 
                  v-model="convertedComponentCode" 
                  readonly 
                  rows="10"
                ></textarea>
              </div>
            </div>
            <button @click="convertComponentCode">转换组件代码</button>
          </div>
          
          <div class="tool-section">
            <h4>迁移检查器</h4>
            <button @click="runMigrationCheck">运行迁移检查</button>
            <div v-if="migrationCheckResult" class="check-result">
              <div 
                v-for="item in migrationCheckResult" 
                :key="item.category"
                class="check-category"
              >
                <h5>{{ item.category }}</h5>
                <div 
                  v-for="check in item.checks" 
                  :key="check.name"
                  :class="['check-item', check.status]"
                >
                  <span class="check-name">{{ check.name }}</span>
                  <span class="check-status">{{ check.message }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const wujieMessage = ref('')
const microcoreMessage = ref('')
const wujieMessages = ref([])
const microcoreMessages = ref([])
const wujieConfig = ref('')
const convertedConfig = ref('')
const wujieComponentCode = ref('')
const convertedComponentCode = ref('')
const migrationCheckResult = ref(null)

onMounted(() => {
  initializeDemos()
})

const initializeDemos = () => {
  // 初始化演示数据
  wujieConfig.value = `{
  name: 'vue-app',
  url: 'http://localhost:3001',
  el: '#wujie-container',
  sync: true,
  alive: true,
  props: {
    title: 'Vue App'
  }
}`

  wujieComponentCode.value = `<template>
  <WujieVue
    name="vue-app"
    :url="url"
    :sync="true"
    :alive="true"
    :props="props"
    @beforeLoad="handleBeforeLoad"
    @beforeMount="handleBeforeMount"
    @afterMount="handleAfterMount"
  />
</template>

<script>
import WujieVue from 'wujie-vue'

export default {
  components: { WujieVue },
  data() {
    return {
      url: 'http://localhost:3001',
      props: { title: 'Vue App' }
    }
  },
  methods: {
    handleBeforeLoad() {
      console.log('Before load')
    },
    handleBeforeMount() {
      console.log('Before mount')
    },
    handleAfterMount() {
      console.log('After mount')
    }
  }
}
</script>`
}

const sendWujieMessage = () => {
  if (wujieMessage.value.trim()) {
    wujieMessages.value.push({
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString(),
      content: wujieMessage.value
    })
    wujieMessage.value = ''
    
    // 模拟 wujie 事件通信
    console.log('wujie 事件通信:', wujieMessage.value)
  }
}

const sendMicrocoreMessage = () => {
  if (microcoreMessage.value.trim()) {
    microcoreMessages.value.push({
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString(),
      content: microcoreMessage.value
    })
    microcoreMessage.value = ''
    
    // 模拟 Micro-Core 通信
    console.log('Micro-Core 通信:', microcoreMessage.value)
  }
}

const convertWujieConfig = () => {
  try {
    const config = eval(`(${wujieConfig.value})`)
    const microCoreConfig = {
      name: config.name,
      entry: config.url,
      container: config.el || '#container',
      activeWhen: `/${config.name}`,
      framework: 'auto',
      sandbox: {
        type: 'proxy',
        css: true,
        js: true
      },
      alive: config.alive || false,
      props: config.props || {}
    }
    
    convertedConfig.value = JSON.stringify(microCoreConfig, null, 2)
  } catch (error) {
    convertedConfig.value = '配置格式错误，请检查输入'
  }
}

const convertComponentCode = () => {
  let converted = wujieComponentCode.value
  
  // 替换 WujieVue 组件
  converted = converted.replace(
    /<WujieVue([^>]*)\/>/g,
    '<MicroApp$1/>'
  )
  
  // 替换导入
  converted = converted.replace(
    /import WujieVue from 'wujie-vue'/g,
    "import { MicroApp } from '@micro-core/vue'"
  )
  
  // 替换组件注册
  converted = converted.replace(
    /components: { WujieVue }/g,
    'components: { MicroApp }'
  )
  
  // 替换属性
  converted = converted.replace(
    /:url="/g,
    ':entry="'
  )
  
  convertedComponentCode.value = converted
}

const runMigrationCheck = () => {
  migrationCheckResult.value = [
    {
      category: '沙箱兼容性',
      checks: [
        { name: 'iframe 沙箱', status: 'compatible', message: '完全兼容' },
        { name: 'webcomponent 沙箱', status: 'compatible', message: '完全兼容' },
        { name: 'Proxy 沙箱', status: 'enhanced', message: '新增支持' }
      ]
    },
    {
      category: '通信系统',
      checks: [
        { name: '事件通信', status: 'compatible', message: '完全兼容' },
        { name: 'props 传递', status: 'compatible', message: '完全兼容' },
        { name: '全局状态', status: 'enhanced', message: '功能增强' }
      ]
    },
    {
      category: '生命周期',
      checks: [
        { name: 'beforeLoad', status: 'compatible', message: '完全兼容' },
        { name: 'beforeMount', status: 'compatible', message: '完全兼容' },
        { name: 'afterMount', status: 'compatible', message: '完全兼容' },
        { name: 'beforeUnmount', status: 'compatible', message: '完全兼容' }
      ]
    },
    {
      category: '组件支持',
      checks: [
        { name: 'WujieVue', status: 'compatible', message: '可直接替换' },
        { name: 'WujieReact', status: 'compatible', message: '可直接替换' },
        { name: '原生 JS', status: 'compatible', message: '完全兼容' }
      ]
    }
  ]
}
</script>
```

## 兼容性演示

### wujie 组件兼容

```vue
<!-- wujie 原始代码 -->
<template>
  <WujieVue
    name="vue-app"
    :url="url"
    :sync="true"
    :alive="true"
    :props="props"
    @beforeLoad="handleBeforeLoad"
    @beforeMount="handleBeforeMount"
    @afterMount="handleAfterMount"
  />
</template>

<script>
import WujieVue from 'wujie-vue'

export default {
  components: { WujieVue },
  data() {
    return {
      url: 'http://localhost:3001',
      props: { title: 'Vue App' }
    }
  }
}
</script>

<!-- Micro-Core 迁移后代码 -->
<template>
  <MicroApp
    name="vue-app"
    :entry="entry"
    :alive="true"
    :props="props"
    @beforeLoad="handleBeforeLoad"
    @beforeMount="handleBeforeMount"
    @afterMount="handleAfterMount"
  />
</template>

<script>
import { MicroApp } from '@micro-core/vue'

export default {
  components: { MicroApp },
  data() {
    return {
      entry: 'http://localhost:3001',
      props: { title: 'Vue App' }
    }
  }
}
</script>
```

### React 组件兼容

```jsx
// wujie 原始代码
import WujieReact from 'wujie-react'

function App() {
  return (
    <WujieReact
      name="react-app"
      url="http://localhost:3001"
      sync={true}
      alive={true}
      props={{ title: 'React App' }}
      beforeLoad={() => console.log('Before load')}
      beforeMount={() => console.log('Before mount')}
      afterMount={() => console.log('After mount')}
    />
  )
}

// Micro-Core 迁移后代码
import { MicroApp } from '@micro-core/react'

function App() {
  return (
    <MicroApp
      name="react-app"
      entry="http://localhost:3001"
      alive={true}
      props={{ title: 'React App' }}
      beforeLoad={() => console.log('Before load')}
      beforeMount={() => console.log('Before mount')}
      afterMount={() => console.log('After mount')}
    />
  )
}
```

## API 对比

### 主要 API 对比

| 功能 | wujie | Micro-Core | 兼容性 |
|------|-------|------------|--------|
| 应用注册 | `WujieVue/WujieReact` | `MicroApp` | ✅ 100% |
| 应用地址 | `url` | `entry` | ✅ 直接替换 |
| 沙箱模式 | `iframe/webcomponent` | `6种沙箱` | ✅ 增强 |
| 生命周期 | `beforeLoad/beforeMount/afterMount` | 相同 | ✅ 100% |
| 应用通信 | 事件通信 | 多种通信方式 | ✅ 增强 |
| 应用缓存 | `alive` | `alive` | ✅ 100% |

### 配置对比

```typescript
// wujie 配置
const wujieConfig = {
  name: 'vue-app',
  url: 'http://localhost:3001',
  el: '#container',
  sync: true,
  alive: true,
  props: {
    title: 'Vue App'
  }
}

// Micro-Core 配置
const microCoreConfig = {
  name: 'vue-app',
  entry: 'http://localhost:3001',
  container: '#container',
  activeWhen: '/vue-app',
  framework: 'vue',
  sandbox: {
    type: 'proxy', // 更高性能
    css: true,
    js: true
  },
  alive: true,
  props: {
    title: 'Vue App'
  }
}
```

## 迁移步骤

### 1. 环境准备

```bash
# 安装 Micro-Core
npm install @micro-core/core

# 安装框架适配器
npm install @micro-core/vue     # Vue 项目
npm install @micro-core/react   # React 项目

# 安装 wujie 兼容插件（可选）
npm install @micro-core/wujie-compat
```

### 2. 组件替换

```vue
<!-- 第一步：替换导入 -->
<!-- 原来 -->
import WujieVue from 'wujie-vue'

<!-- 现在 -->
import { MicroApp } from '@micro-core/vue'

<!-- 第二步：替换组件 -->
<!-- 原来 -->
<WujieVue
  name="app"
  :url="url"
  :props="props"
/>

<!-- 现在 -->
<MicroApp
  name="app"
  :entry="entry"
  :props="props"
/>
```

### 3. 配置迁移

```typescript
// wujie 配置迁移工具
class WujieToMicroCoreConverter {
  convertConfig(wujieConfig: any) {
    return {
      name: wujieConfig.name,
      entry: wujieConfig.url,
      container: wujieConfig.el || '#container',
      activeWhen: `/${wujieConfig.name}`,
      framework: this.detectFramework(wujieConfig.url),
      sandbox: {
        type: this.convertSandboxType(wujieConfig.sandbox),
        css: true,
        js: true
      },
      alive: wujieConfig.alive || false,
      props: wujieConfig.props || {}
    }
  }
  
  private detectFramework(url: string): string {
    // 自动检测框架类型
    return 'auto'
  }
  
  private convertSandboxType(wujieSandbox: string): string {
    const sandboxMap = {
      'iframe': 'iframe',
      'webcomponent': 'webcomponent',
      undefined: 'proxy' // 默认使用更高性能的 proxy
    }
    return sandboxMap[wujieSandbox] || 'proxy'
  }
}
```

## 沙箱迁移

### 沙箱类型对比

```typescript
// wujie 沙箱配置
const wujieSandbox = {
  // iframe 沙箱
  iframe: {
    isolation: 'strong',
    performance: 'medium',
    compatibility: 'good'
  },
  
  // webcomponent 沙箱
  webcomponent: {
    isolation: 'medium',
    performance: 'good',
    compatibility: 'medium'
  }
}

// Micro-Core 沙箱配置
const microCoreSandbox = {
  // 继承 wujie 的沙箱
  iframe: {
    isolation: 'strong',
    performance: 'medium',
    compatibility: 'excellent'
  },
  
  webcomponent: {
    isolation: 'medium', 
    performance: 'good',
    compatibility: 'excellent'
  },
  
  // 新增的高性能沙箱
  proxy: {
    isolation: 'strong',
    performance: 'excellent',
    compatibility: 'excellent'
  },
  
  // 其他沙箱选项
  defineProperty: {
    isolation: 'medium',
    performance: 'good',
    compatibility: 'excellent'
  }
}
```

### 沙箱迁移示例

```typescript
// wujie iframe 沙箱迁移
const wujieApp = {
  name: 'app',
  url: 'http://localhost:3001',
  // wujie 默认使用 iframe 沙箱
}

// 迁移到 Micro-Core
const microCoreApp = {
  name: 'app',
  entry: 'http://localhost:3001',
  sandbox: {
    type: 'iframe', // 保持相同的沙箱类型
    css: true,
    js: true
  }
}

// 或者升级到更高性能的 Proxy 沙箱
const upgradedApp = {
  name: 'app',
  entry: 'http://localhost:3001',
  sandbox: {
    type: 'proxy', // 更高性能
    css: true,
    js: true,
    // 更多配置选项
    strictStyleIsolation: true,
    experimentalStyleIsolation: true
  }
}
```

## 通信系统迁移

### 事件通信迁移

```typescript
// wujie 事件通信
// 主应用
window.$wujie?.bus.$emit('globalMessage', { type: 'user-login', data: userData })

// 微应用
window.$wujie?.bus.$on('globalMessage', (data) => {
  console.log('收到消息:', data)
})

// Micro-Core 事件通信
// 主应用
microCore.eventBus.emit('user-login', userData)

// 微应用
microCore.eventBus.on('user-login', (userData) => {
  console.log('用户登录:', userData)
})

// 或使用命名空间通信（增强功能）
const userNamespace = microCore.eventBus.namespace('user')
userNamespace.emit('login', userData)
userNamespace.on('login', handleUserLogin)
```

### Props 通信迁移

```typescript
// wujie props 通信
// 主应用传递 props
<WujieVue
  name="app"
  :url="url"
  :props="{ user: currentUser, theme: 'dark' }"
/>

// 微应用接收 props
// 在微应用的生命周期中自动接收

// Micro-Core props 通信
// 主应用传递 props
<MicroApp
  name="app"
  :entry="entry"
  :props="{ user: currentUser, theme: 'dark' }"
/>

// 微应用接收 props
export async function mount(props) {
  const { user, theme } = props
  console.log('接收到 props:', { user, theme })
}
```

## 性能对比

### 加载性能测试

```typescript
// 性能测试结果对比
const performanceComparison = {
  // 应用加载时间 (ms)
  loadTime: {
    wujie: {
      iframe: [800, 850, 780, 820, 790],
      webcomponent: [600, 650, 580, 620, 590]
    },
    microCore: {
      iframe: [750, 780, 720, 760, 740],
      webcomponent: [550, 580, 520, 560, 540],
      proxy: [400, 420, 380, 410, 390] // 新增高性能沙箱
    }
  },
  
  // 内存使用 (MB)
  memoryUsage: {
    wujie: {
      iframe: [35, 38, 36, 37, 39],
      webcomponent: [28, 30, 29, 31, 27]
    },
    microCore: {
      iframe: [32, 34, 33, 35, 31],
      webcomponent: [25, 27, 26, 28, 24],
      proxy: [20, 22, 21, 23, 19]
    }
  },
  
  // 通信延迟 (ms)
  communicationLatency: {
    wujie: [15, 18, 16, 17, 19],
    microCore: [8, 10, 9, 11, 7]
  }
}

// 计算性能提升
const calculateImprovement = (wujieData, microCoreData) => {
  const wujieAvg = wujieData.reduce((a, b) => a + b) / wujieData.length
  const microCoreAvg = microCoreData.reduce((a, b) => a + b) / microCoreData.length
  return ((wujieAvg - microCoreAvg) / wujieAvg * 100).toFixed(1)
}

console.log('性能提升报告:')
console.log('Proxy 沙箱加载时间提升:', calculateImprovement(
  performanceComparison.loadTime.wujie.iframe,
  performanceComparison.loadTime.microCore.proxy
), '%')
```

### 实际性能对比表

| 指标 | wujie (iframe) | wujie (webcomponent) | Micro-Core (proxy) | 提升 |
|------|----------------|----------------------|-------------------|------|
| 加载时间 | 810ms | 610ms | 400ms | 50% ⬆️ |
| 内存使用 | 37MB | 29MB | 21MB | 43% ⬇️ |
| 通信延迟 | 17ms | 17ms | 9ms | 47% ⬇️ |
| 沙箱切换 | 200ms | 150ms | 80ms | 60% ⬆️ |

## 常见问题

### Q: wujie 的 alive 功能如何迁移？

**A: 完全兼容的 alive 功能：**

```typescript
// wujie alive 配置
<WujieVue
  name="app"
  :url="url"
  :alive="true"
/>

// Micro-Core alive 配置
<MicroApp
  name="app"
  :entry="entry"
  :alive="true"
/>

// 或者在注册时配置
await microCore.registerApp({
  name: 'app',
  entry: 'http://localhost:3001',
  alive: true, // 应用保活
  cache: {
    enabled: true,
    strategy: 'memory' // 内存缓存策略
  }
})
```

### Q: wujie 的同步模式如何处理？

**A: 更灵活的加载策略：**

```typescript
// wujie 同步模式
<WujieVue
  name="app"
  :url="url"
  :sync="true"
/>

// Micro-Core 加载策略
<MicroApp
  name="app"
  :entry="entry"
  :loading-strategy="'sync'"
/>

// 或者更细粒度的控制
await microCore.registerApp({
  name: 'app',
  entry: 'http://localhost:3001',
  loading: {
    strategy: 'sync',     // 同步加载
    timeout: 10000,       // 超时时间
    retry: 3,             // 重试次数
    fallback: '/error'    // 失败回退
  }
})
```

### Q: 如何处理 wujie 的路由同步？

**A: 更强大的路由管理：**

```typescript
// wujie 路由同步
// 需要手动处理路由同步

// Micro-Core 自动路由管理
await microCore.registerApp({
  name: 'app',
  entry: 'http://localhost:3001',
  activeWhen: '/app',
  routing: {
    mode: 'history',
    base: '/app',
    sync: true,           // 自动同步路由
    guards: true,         // 启用路由守卫
    cache: true           // 路由状态缓存
  }
})
```

### Q: wujie 的样式隔离如何迁移？

**A: 更强的样式隔离：**

```typescript
// wujie webcomponent 样式隔离
// 基于 Shadow DOM

// Micro-Core 多种样式隔离策略
await microCore.registerApp({
  name: 'app',
  entry: 'http://localhost:3001',
  sandbox: {
    type: 'webcomponent',  // 兼容 wujie
    css: {
      isolation: 'shadow-dom',
      prefix: 'micro-app-',
      scoped: true
    }
  }
})

// 或使用更高性能的 CSS 隔离
await microCore.registerApp({
  name: 'app',
  entry: 'http://localhost:3001',
  sandbox: {
    type: 'proxy',
    css: {
      isolation: 'scoped',
      strictStyleIsolation: true,
      experimentalStyleIsolation: true
    }
  }
})
```

## 迁移最佳实践

### 1. 渐进式迁移策略

```typescript
// 第一阶段：保持兼容
// 使用 Micro-Core 的 wujie 兼容模式
import { MicroCore } from '@micro-core/core'
import { WujieCompatPlugin } from '@micro-core/wujie-compat'

const microCore = new MicroCore({
  plugins: [new WujieCompatPlugin()]
})

// 第二阶段：逐步升级
// 将部分应用迁移到原生 API
await microCore.registerApp({
  name: 'new-app',
  entry: 'http://localhost:3002',
  sandbox: { type: 'proxy' }, // 使用新特性
  framework: 'vue'
})

// 第三阶段：全面升级
// 所有应用使用 Micro-Core 原生 API
```

### 2. 性能优化建议

```typescript
// 利用 Micro-Core 的性能优化特性
const microCore = new MicroCore({
  // 智能预加载
  prefetch: {
    enabled: true,
    strategy: 'intelligent'
  },
  
  // 多层缓存
  cache: {
    enabled: true,
    strategy: 'lru',
    maxSize: '100MB'
  },
  
  // 性能监控
  monitoring: {
    enabled: true,
    metrics: ['loadTime', 'memoryUsage', 'errorRate']
  }
})
```

### 3. 调试和监控

```typescript
// 启用调试模式
const microCore = new MicroCore({
  debug: true,
  devtools: {
    enabled: true,
    panel: true,
    logging: 'verbose'
  }
})

// 性能监控
microCore.on('app:performance', (metrics) => {
  console.log('性能指标:', metrics)
})

// 错误监控
microCore.on('app:error', (error) => {
  console.error('应用错误:', error)
})
```

## 迁移检查清单

### ✅ 迁移前准备

- [ ] 评估现有 wujie 应用
- [ ] 制定迁移计划
- [ ] 准备测试环境
- [ ] 备份现有代码

### ✅ 组件迁移

- [ ] 替换 WujieVue/WujieReact 组件
- [ ] 更新组件属性 (url → entry)
- [ ] 验证生命周期函数
- [ ] 测试组件功能

### ✅ 配置迁移

- [ ] 转换应用配置
- [ ] 迁移沙箱设置
- [ ] 更新通信配置
- [ ] 配置路由规则

### ✅ 功能验证

- [ ] 应用加载测试
- [ ] 沙箱隔离测试
- [ ] 通信功能测试
- [ ] 性能基准测试

### ✅ 优化升级

- [ ] 启用新特性
- [ ] 性能优化配置
- [ ] 监控和调试设置
- [ ] 文档更新

## 迁移工具

### 自动化迁移脚本

```bash
#!/bin/bash
# wujie-to-microcore-migration.sh

echo "开始 wujie 到 Micro-Core 迁移..."

# 1. 安装依赖
echo "安装 Micro-Core 依赖..."
npm install @micro-core/core @micro-core/vue @micro-core/react

# 2. 代码转换
echo "转换组件代码..."
find src -name "*.vue" -exec sed -i 's/WujieVue/MicroApp/g' {} \;
find src -name "*.jsx" -exec sed -i 's/WujieReact/MicroApp/g' {} \;
find src -name "*.tsx" -exec sed -i 's/WujieReact/MicroApp/g' {} \;

# 3. 更新导入
echo "更新导入语句..."
find src -name "*.vue" -exec sed -i "s/import WujieVue from 'wujie-vue'/import { MicroApp } from '@micro-core\/vue'/g" {} \;
find src -name "*.jsx" -exec sed -i "s/import WujieReact from 'wujie-react'/import { MicroApp } from '@micro-core\/react'/g" {} \;

# 4. 属性替换
echo "替换组件属性..."
find src -name "*.vue" -exec sed -i 's/:url=/:entry=/g' {} \;
find src -name "*.jsx" -exec sed -i 's/url=/entry=/g' {} \;

echo "迁移完成！请运行测试验证功能。"
```

### 配置转换工具

```javascript
// wujie-config-converter.js
const fs = require('fs')

class WujieConfigConverter {
  convert(wujieConfigPath, outputPath) {
    const wujieConfig = require(wujieConfigPath)
    
    const microCoreConfig = {
      apps: wujieConfig.apps?.map(app => ({
        name: app.name,
        entry: app.url,
        container: app.el || '#container',
        activeWhen: `/${app.name}`,
        framework: 'auto',
        sandbox: {
          type: this.convertSandboxType(app.sandbox),
          css: true,
          js: true
        },
        alive: app.alive || false,
        props: app.props || {}
      })) || [],
      
      // 新增配置
      prefetch: true,
      cache: {
        enabled: true,
        strategy: 'memory'
      },
      
      plugins: [
        '@micro-core/plugin-router',
        '@micro-core/plugin-communication'
      ]
    }
    
    fs.writeFileSync(
      outputPath, 
      JSON.stringify(microCoreConfig, null, 2)
    )
    
    console.log(`配置转换完成: ${outputPath}`)
  }
  
  convertSandboxType(wujieSandbox) {
    const sandboxMap = {
      'iframe': 'iframe',
      'webcomponent': 'webcomponent',
      undefined: 'proxy'
    }
    return sandboxMap[wujieSandbox] || 'proxy'
  }
}

// 使用示例
const converter = new WujieConfigConverter()
converter.convert('./wujie.config.js', './micro-core.config.js')
```

## 相关资源

- [wujie API 对照表](/migration/wujie/api-mapping)
- [完整迁移示例](/migration/wujie/complete-example)
- [沙箱迁移指南](/migration/wujie/sandbox-migration)
- [性能优化指南](/guide/best-practices/performance)

---

通过 wujie 迁移演练场，您可以安全、高效地完成从 wujie 到 Micro-Core 的迁移，享受更强大的功能、更好的性能和更丰富的特性。
