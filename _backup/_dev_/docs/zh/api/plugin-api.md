# 插件 API

Micro-Core 的插件系统提供了丰富的 API 接口，支持开发各种类型的插件来扩展系统功能。

## 核心接口

### Plugin 接口

所有插件必须实现 `Plugin` 接口：

```typescript
interface Plugin {
  // 基本信息
  readonly name: string
  readonly version: string
  readonly type: PluginType
  
  // 依赖关系
  readonly dependencies?: string[]
  readonly optionalDependencies?: string[]
  
  // 生命周期方法
  initialize?(context: PluginContext): Promise<void> | void
  start?(context: PluginContext): Promise<void> | void
  stop?(context: PluginContext): Promise<void> | void
  destroy?(): Promise<void> | void
  
  // 配置方法
  configure?(config: PluginConfig): void
  
  // 应用生命周期钩子
  beforeAppLoad?(app: MicroApp): Promise<void> | void
  afterAppLoad?(app: MicroApp): Promise<void> | void
  beforeAppUnload?(app: MicroApp): Promise<void> | void
  afterAppUnload?(app: MicroApp): Promise<void> | void
}
```

### PluginContext 接口

插件上下文提供了访问系统功能的接口：

```typescript
interface PluginContext {
  // 核心服务
  readonly kernel: MicroKernel
  readonly logger: Logger
  readonly eventBus: EventBus
  readonly configManager: ConfigManager
  
  // 应用管理
  readonly appManager: AppManager
  readonly routerManager: RouterManager
  readonly sandboxManager: SandboxManager
  
  // 工具方法
  getPlugin<T extends Plugin>(name: string): T | null
  getService<T>(name: string): T | null
  registerService<T>(name: string, service: T): void
}
```

## 插件类型

### PluginType 枚举

```typescript
enum PluginType {
  CORE = 'core',
  SANDBOX = 'sandbox', 
  ROUTER = 'router',
  COMMUNICATION = 'communication',
  ADAPTER = 'adapter',
  MIDDLEWARE = 'middleware',
  EXTENSION = 'extension'
}
```

## 生命周期管理

### 插件生命周期

```typescript
class ExamplePlugin implements Plugin {
  name = 'example-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  // 初始化阶段
  async initialize(context: PluginContext) {
    context.logger.info('插件初始化开始')
    
    // 注册服务
    context.registerService('example-service', new ExampleService())
    
    // 订阅事件
    context.eventBus.on('app:loaded', this.handleAppLoaded.bind(this))
  }
  
  // 启动阶段
  async start(context: PluginContext) {
    context.logger.info('插件启动')
    
    // 启动后台任务
    this.startBackgroundTask()
  }
  
  // 停止阶段
  async stop(context: PluginContext) {
    context.logger.info('插件停止')
    
    // 清理资源
    this.cleanup()
  }
  
  // 销毁阶段
  async destroy() {
    // 最终清理
    this.finalCleanup()
  }
}
```

### 应用生命周期钩子

```typescript
class AppLifecyclePlugin implements Plugin {
  name = 'app-lifecycle-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  // 应用加载前
  async beforeAppLoad(app: MicroApp) {
    console.log(`应用 ${app.name} 即将加载`)
    
    // 预处理应用配置
    this.preprocessAppConfig(app)
    
    // 准备沙箱环境
    await this.prepareSandbox(app)
  }
  
  // 应用加载后
  async afterAppLoad(app: MicroApp) {
    console.log(`应用 ${app.name} 加载完成`)
    
    // 注入全局服务
    this.injectGlobalServices(app)
    
    // 启动监控
    this.startMonitoring(app)
  }
  
  // 应用卸载前
  async beforeAppUnload(app: MicroApp) {
    console.log(`应用 ${app.name} 即将卸载`)
    
    // 保存应用状态
    await this.saveAppState(app)
    
    // 清理事件监听器
    this.cleanupEventListeners(app)
  }
  
  // 应用卸载后
  async afterAppUnload(app: MicroApp) {
    console.log(`应用 ${app.name} 卸载完成`)
    
    // 释放资源
    this.releaseResources(app)
    
    // 更新统计信息
    this.updateStats(app)
  }
}
```

## 事件系统

### EventBus API

```typescript
interface EventBus {
  // 事件监听
  on<T = any>(event: string, listener: EventListener<T>): void
  once<T = any>(event: string, listener: EventListener<T>): void
  off(event: string, listener?: EventListener): void
  
  // 事件发射
  emit<T = any>(event: string, data?: T): void
  emitAsync<T = any>(event: string, data?: T): Promise<void>
  
  // 事件管理
  listenerCount(event: string): number
  eventNames(): string[]
  removeAllListeners(event?: string): void
}

type EventListener<T = any> = (data: T) => void | Promise<void>
```

### 事件使用示例

```typescript
class EventPlugin implements Plugin {
  name = 'event-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  initialize(context: PluginContext) {
    const { eventBus } = context
    
    // 监听系统事件
    eventBus.on('system:ready', this.onSystemReady.bind(this))
    eventBus.on('app:error', this.onAppError.bind(this))
    
    // 监听自定义事件
    eventBus.on('user:login', this.onUserLogin.bind(this))
    eventBus.on('user:logout', this.onUserLogout.bind(this))
  }
  
  private onSystemReady() {
    console.log('系统就绪')
    // 发射自定义事件
    this.context.eventBus.emit('plugin:ready', { plugin: this.name })
  }
  
  private onAppError(error: AppError) {
    console.error('应用错误:', error)
    // 错误处理逻辑
  }
  
  private onUserLogin(userData: UserData) {
    console.log('用户登录:', userData)
    // 用户登录处理逻辑
  }
}
```

## 服务注册

### 服务接口

```typescript
interface Service {
  readonly name: string
  readonly version: string
  
  initialize?(): Promise<void> | void
  destroy?(): Promise<void> | void
}
```

### 服务注册示例

```typescript
class DatabaseService implements Service {
  name = 'database-service'
  version = '1.0.0'
  
  private connection: any
  
  async initialize() {
    this.connection = await this.createConnection()
  }
  
  async query(sql: string, params?: any[]) {
    return this.connection.query(sql, params)
  }
  
  async destroy() {
    await this.connection.close()
  }
}

class DatabasePlugin implements Plugin {
  name = 'database-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  async initialize(context: PluginContext) {
    const dbService = new DatabaseService()
    await dbService.initialize()
    
    // 注册服务
    context.registerService('database', dbService)
  }
}
```

## 配置管理

### PluginConfig 接口

```typescript
interface PluginConfig {
  [key: string]: any
}

interface ConfigurablePlugin extends Plugin {
  configure(config: PluginConfig): void
  getConfig(): PluginConfig
  updateConfig(config: Partial<PluginConfig>): void
}
```

### 配置示例

```typescript
class ConfigurablePlugin implements ConfigurablePlugin {
  name = 'configurable-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  private config: PluginConfig = {
    enabled: true,
    timeout: 5000,
    retries: 3
  }
  
  configure(config: PluginConfig) {
    this.config = { ...this.config, ...config }
  }
  
  getConfig(): PluginConfig {
    return { ...this.config }
  }
  
  updateConfig(config: Partial<PluginConfig>) {
    this.config = { ...this.config, ...config }
  }
}

// 使用配置
const plugin = new ConfigurablePlugin()
plugin.configure({
  timeout: 10000,
  retries: 5,
  customOption: 'value'
})
```

## 错误处理

### PluginError 类

```typescript
class PluginError extends Error {
  constructor(
    public readonly code: string,
    message: string,
    public readonly plugin: string,
    public readonly cause?: Error
  ) {
    super(message)
    this.name = 'PluginError'
  }
}

// 错误代码常量
export const PluginErrorCodes = {
  INITIALIZATION_FAILED: 'PLUGIN_INITIALIZATION_FAILED',
  START_FAILED: 'PLUGIN_START_FAILED',
  DEPENDENCY_NOT_FOUND: 'PLUGIN_DEPENDENCY_NOT_FOUND',
  CONFIGURATION_INVALID: 'PLUGIN_CONFIGURATION_INVALID'
} as const
```

### 错误处理示例

```typescript
class RobustPlugin implements Plugin {
  name = 'robust-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  async initialize(context: PluginContext) {
    try {
      await this.doInitialization()
    } catch (error) {
      throw new PluginError(
        PluginErrorCodes.INITIALIZATION_FAILED,
        '插件初始化失败',
        this.name,
        error
      )
    }
  }
  
  async start(context: PluginContext) {
    try {
      // 检查依赖
      const dependency = context.getPlugin('required-plugin')
      if (!dependency) {
        throw new PluginError(
          PluginErrorCodes.DEPENDENCY_NOT_FOUND,
          '找不到必需的依赖插件',
          this.name
        )
      }
      
      await this.doStart()
    } catch (error) {
      context.logger.error('插件启动失败', error)
      throw error
    }
  }
}
```

## 插件通信

### 插件间通信

```typescript
class ProducerPlugin implements Plugin {
  name = 'producer-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  private context: PluginContext
  
  initialize(context: PluginContext) {
    this.context = context
  }
  
  async produceData() {
    const data = await this.generateData()
    
    // 通过事件总线发送数据
    this.context.eventBus.emit('data:produced', data)
    
    // 直接调用其他插件
    const consumer = this.context.getPlugin<ConsumerPlugin>('consumer-plugin')
    if (consumer) {
      await consumer.consumeData(data)
    }
  }
}

class ConsumerPlugin implements Plugin {
  name = 'consumer-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  initialize(context: PluginContext) {
    // 监听数据事件
    context.eventBus.on('data:produced', this.consumeData.bind(this))
  }
  
  async consumeData(data: any) {
    console.log('接收到数据:', data)
    // 处理数据
    await this.processData(data)
  }
}
```

## 异步插件

### 异步初始化

```typescript
class AsyncPlugin implements Plugin {
  name = 'async-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  async initialize(context: PluginContext) {
    // 异步初始化资源
    await this.loadRemoteConfig()
    await this.connectToDatabase()
    await this.setupCache()
    
    context.logger.info('异步插件初始化完成')
  }
  
  private async loadRemoteConfig() {
    const response = await fetch('/api/plugin-config')
    this.config = await response.json()
  }
  
  private async connectToDatabase() {
    this.db = await createDatabaseConnection(this.config.database)
  }
  
  private async setupCache() {
    this.cache = new Cache(this.config.cache)
    await this.cache.initialize()
  }
}
```

## 插件测试

### 测试工具

```typescript
import { createPluginTestContext } from '@micro-core/test-utils'

describe('ExamplePlugin', () => {
  let plugin: ExamplePlugin
  let context: PluginContext
  
  beforeEach(() => {
    plugin = new ExamplePlugin()
    context = createPluginTestContext({
      plugins: ['dependency-plugin'],
      services: {
        'test-service': new TestService()
      }
    })
  })
  
  it('should initialize correctly', async () => {
    await plugin.initialize(context)
    
    expect(plugin.isInitialized).toBe(true)
    expect(context.getService('example-service')).toBeDefined()
  })
  
  it('should handle events correctly', async () => {
    await plugin.initialize(context)
    
    const spy = jest.spyOn(plugin, 'handleEvent')
    context.eventBus.emit('test-event', { data: 'test' })
    
    expect(spy).toHaveBeenCalledWith({ data: 'test' })
  })
})
```

## 插件调试

### 调试工具

```typescript
class DebuggablePlugin implements Plugin {
  name = 'debuggable-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  private debug: boolean = false
  
  configure(config: PluginConfig) {
    this.debug = config.debug || false
  }
  
  private log(...args: any[]) {
    if (this.debug) {
      console.log(`[${this.name}]`, ...args)
    }
  }
  
  async initialize(context: PluginContext) {
    this.log('开始初始化')
    
    // 初始化逻辑
    await this.doInitialization()
    
    this.log('初始化完成')
  }
}
```

## 最佳实践

### 1. 插件设计原则

- **单一职责**：每个插件只负责一个特定功能
- **松耦合**：通过事件和接口通信，避免直接依赖
- **可配置**：提供丰富的配置选项
- **可测试**：编写完整的单元测试

### 2. 性能优化

```typescript
class OptimizedPlugin implements Plugin {
  name = 'optimized-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  private cache = new Map()
  private debounceMap = new Map()
  
  // 使用缓存
  async getData(key: string) {
    if (this.cache.has(key)) {
      return this.cache.get(key)
    }
    
    const data = await this.fetchData(key)
    this.cache.set(key, data)
    return data
  }
  
  // 防抖处理
  debounce(key: string, fn: Function, delay: number = 300) {
    if (this.debounceMap.has(key)) {
      clearTimeout(this.debounceMap.get(key))
    }
    
    const timeoutId = setTimeout(fn, delay)
    this.debounceMap.set(key, timeoutId)
  }
}
```

### 3. 错误恢复

```typescript
class ResilientPlugin implements Plugin {
  name = 'resilient-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  private retryCount = 0
  private maxRetries = 3
  
  async initialize(context: PluginContext) {
    try {
      await this.doInitialization()
    } catch (error) {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        context.logger.warn(`插件初始化失败，重试 ${this.retryCount}/${this.maxRetries}`)
        await this.initialize(context)
      } else {
        throw error
      }
    }
  }
}
```

## 参考资料

- [插件开发指南](/guide/plugin-system)
- [内置插件文档](/ecosystem/plugins)
- [插件示例](/examples/plugin-development)
- [API 参考](/api/)
