# 插件API参考

## 概述

Micro-Core 采用100%插件化架构设计，所有功能都通过插件实现。插件系统基于钩子机制，提供丰富的扩展点，支持按需加载和组合使用。本文档提供完整的插件API技术规范，包括接口定义、参数说明、使用示例和错误处理机制。

## 核心插件接口

### Plugin 基础接口

#### 接口定义

```typescript
interface Plugin {
  /** 插件名称 - 必须唯一 */
  name: string;
  /** 插件版本 - 遵循语义化版本规范 */
  version: string;
  /** 插件描述 - 可选，用于文档和调试 */
  description?: string;
  /** 插件依赖 - 可选，声明依赖的其他插件 */
  dependencies?: string[];
  /** 插件安装函数 - 可选，插件初始化逻辑 */
  install?: (pluginSystem: PluginSystem, options?: any) => void;
  /** 插件卸载函数 - 可选，插件清理逻辑 */
  uninstall?: (pluginSystem: PluginSystem) => void;
}
```

#### 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `name` | `string` | ✅ | 插件唯一标识符，建议使用 kebab-case 命名 |
| `version` | `string` | ✅ | 插件版本号，遵循 semver 规范 |
| `description` | `string` | ❌ | 插件功能描述，用于调试和文档生成 |
| `dependencies` | `string[]` | ❌ | 依赖的插件名称列表，系统会确保依赖顺序 |
| `install` | `Function` | ❌ | 插件安装时的回调函数 |
| `uninstall` | `Function` | ❌ | 插件卸载时的回调函数 |

#### 返回值类型

插件接口本身不返回值，但 `install` 和 `uninstall` 方法可以执行副作用操作。

### 插件系统核心API

#### PluginSystem.install()

**功能说明**: 安装插件到系统中

**方法签名**:
```typescript
install(plugin: Plugin, options?: any): void
```

**参数列表**:
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `plugin` | `Plugin` | ✅ | 插件实例对象 |
| `options` | `any` | ❌ | 插件配置选项，传递给 install 方法 |

**返回值**: `void`

**异常处理**:
- `PLUGIN_ALREADY_EXISTS`: 插件已存在时抛出
- `PLUGIN_INSTALL_FAILED`: 插件安装失败时抛出

**使用示例**:
```typescript
// 基础安装
kernel.use(RouterPlugin);

// 带配置安装
kernel.use(RouterPlugin, {
  mode: 'history',
  base: '/app',
  routes: [
    { path: '/home', app: 'home-app' },
    { path: '/profile', app: 'profile-app' }
  ]
});
```

#### PluginSystem.uninstall()

**功能说明**: 从系统中卸载指定插件

**方法签名**:
```typescript
uninstall(name: string): void
```

**参数列表**:
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `name` | `string` | ✅ | 要卸载的插件名称 |

**返回值**: `void`

**异常处理**:
- `PLUGIN_NOT_FOUND`: 插件不存在时抛出

**使用示例**:
```typescript
// 卸载指定插件
kernel.uninstall('router-plugin');
```

#### PluginSystem.get()

**功能说明**: 获取已安装的插件实例

**方法签名**:
```typescript
get(name: string): Plugin | null
```

**参数列表**:
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `name` | `string` | ✅ | 插件名称 |

**返回值**: `Plugin | null` - 插件实例或 null（如果不存在）

**使用示例**:
```typescript
const routerPlugin = kernel.getPlugin('router-plugin');
if (routerPlugin) {
  console.log('路由插件版本:', routerPlugin.version);
}
```

#### PluginSystem.has()

**功能说明**: 检查插件是否已安装

**方法签名**:
```typescript
has(name: string): boolean
```

**参数列表**:
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `name` | `string` | ✅ | 插件名称 |

**返回值**: `boolean` - 插件是否存在

**使用示例**:
```typescript
if (kernel.hasPlugin('router-plugin')) {
  console.log('路由插件已安装');
}
```

## 钩子系统API

### 钩子注册

#### addHook()

**功能说明**: 注册钩子回调函数

**方法签名**:
```typescript
addHook(hookName: string, callback: HookCallback): void
```

**参数列表**:
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `hookName` | `string` | ✅ | 钩子名称 |
| `callback` | `HookCallback` | ✅ | 回调函数 |

**HookCallback 类型定义**:
```typescript
type HookCallback = (context: any, ...args: any[]) => Promise<any> | any;
```

**使用示例**:
```typescript
// 注册应用加载前钩子
pluginSystem.addHook('beforeAppLoad', async (context, appConfig) => {
  console.log('准备加载应用:', appConfig.name);
  // 执行预处理逻辑
  return { processed: true };
});
```

### 钩子执行

#### executeHook()

**功能说明**: 执行指定钩子的所有回调函数

**方法签名**:
```typescript
executeHook(hookName: string, context: any, ...args: any[]): Promise<any[]>
```

**参数列表**:
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `hookName` | `string` | ✅ | 钩子名称 |
| `context` | `any` | ✅ | 执行上下文 |
| `...args` | `any[]` | ❌ | 传递给回调函数的参数 |

**返回值**: `Promise<any[]>` - 所有回调函数的返回值数组

## 官方插件API详细说明

### 1. 路由插件 (@micro-core/plugin-router)

#### 功能说明
提供统一的路由管理能力，支持多种路由模式和应用间路由协调。

#### 接口定义
```typescript
interface RouterPluginOptions {
  mode?: 'hash' | 'history' | 'memory';
  base?: string;
  routes?: RouteConfig[];
  fallback?: string;
  beforeEach?: (to: Route, from: Route) => boolean | Promise<boolean>;
  afterEach?: (to: Route, from: Route) => void;
}

interface RouteConfig {
  path: string;
  app: string;
  exact?: boolean;
  meta?: Record<string, any>;
}
```

#### 参数说明
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `mode` | `'hash' \| 'history' \| 'memory'` | `'hash'` | 路由模式 |
| `base` | `string` | `'/'` | 基础路径 |
| `routes` | `RouteConfig[]` | `[]` | 路由配置数组 |
| `fallback` | `string` | `undefined` | 404 回退路由 |
| `beforeEach` | `Function` | `undefined` | 路由守卫 |
| `afterEach` | `Function` | `undefined` | 路由后置钩子 |

#### 使用示例
```typescript
import { RouterPlugin } from '@micro-core/plugin-router';

kernel.use(RouterPlugin, {
  mode: 'history',
  base: '/micro-app',
  routes: [
    { path: '/home', app: 'home-app', exact: true },
    { path: '/user/:id', app: 'user-app', meta: { requiresAuth: true } }
  ],
  beforeEach: async (to, from) => {
    if (to.meta?.requiresAuth && !isAuthenticated()) {
      return false;
    }
    return true;
  }
});
```

### 2. 通信插件 (@micro-core/plugin-communication)

#### 功能说明
提供应用间通信能力，包括事件总线、全局状态管理和消息传递。

#### 接口定义
```typescript
interface CommunicationPluginOptions {
  enableEventBus?: boolean;
  enableGlobalState?: boolean;
  enableMessageBridge?: boolean;
  eventBusOptions?: EventBusOptions;
  globalStateOptions?: GlobalStateOptions;
}

interface EventBusOptions {
  maxListeners?: number;
  enableWildcard?: boolean;
  enableNamespace?: boolean;
}
```

#### API方法

##### EventBus API
```typescript
// 事件监听
on(event: string, listener: Function): () => void
once(event: string, listener: Function): () => void
off(event: string, listener?: Function): void

// 事件发射
emit(event: string, data?: any): boolean
emitAsync(event: string, data?: any): Promise<any[]>

// 事件管理
hasListeners(event: string): boolean
listenerCount(event: string): number
```

##### GlobalState API
```typescript
// 状态管理
set(key: string, value: any): void
get(key: string): any
has(key: string): boolean
delete(key: string): boolean
clear(): void

// 状态订阅
subscribe(key: string, callback: Function): () => void
unsubscribe(key: string, callback?: Function): void
```

#### 使用示例
```typescript
import { CommunicationPlugin } from '@micro-core/plugin-communication';

kernel.use(CommunicationPlugin, {
  enableEventBus: true,
  enableGlobalState: true,
  eventBusOptions: {
    maxListeners: 100,
    enableNamespace: true
  }
});

// 使用事件总线
const comm = kernel.getPlugin('communication');
comm.eventBus.on('user:login', (user) => {
  console.log('用户登录:', user);
});

// 使用全局状态
comm.globalState.set('currentUser', { id: 1, name: 'John' });
const user = comm.globalState.get('currentUser');
```

### 3. 沙箱插件系列

#### Proxy沙箱 (@micro-core/plugin-sandbox-proxy)

**功能说明**: 基于 Proxy 实现的 JavaScript 沙箱，提供完整的全局变量隔离。

**接口定义**:
```typescript
interface ProxySandboxOptions {
  strictIsolation?: boolean;
  allowList?: string[];
  denyList?: string[];
  enablePerformanceMonitor?: boolean;
}
```

#### Iframe沙箱 (@micro-core/plugin-sandbox-iframe)

**功能说明**: 基于 iframe 实现的沙箱，提供最强的隔离能力。

**接口定义**:
```typescript
interface IframeSandboxOptions {
  src?: string;
  sandbox?: string;
  allowScripts?: boolean;
  allowSameOrigin?: boolean;
  communicationProtocol?: 'postMessage' | 'proxy';
}
```

### 4. 预加载插件 (@micro-core/plugin-prefetch)

#### 功能说明
智能预加载微应用资源，基于路由预测和视口检测提升应用切换性能。

#### 接口定义
```typescript
interface PrefetchPluginOptions {
  enableRoutePrediction?: boolean;
  enableViewportDetection?: boolean;
  enableIdlePrefetch?: boolean;
  maxConcurrentRequests?: number;
  prefetchTimeout?: number;
  strategies?: PrefetchStrategy[];
}

interface PrefetchStrategy {
  name: string;
  condition: (context: PrefetchContext) => boolean;
  execute: (context: PrefetchContext) => Promise<void>;
}
```

#### 使用示例
```typescript
import { PrefetchPlugin } from '@micro-core/plugin-prefetch';

kernel.use(PrefetchPlugin, {
  enableRoutePrediction: true,
  enableViewportDetection: true,
  maxConcurrentRequests: 3,
  prefetchTimeout: 10000,
  strategies: [
    {
      name: 'priority-based',
      condition: (ctx) => ctx.route.meta?.priority === 'high',
      execute: async (ctx) => {
        await ctx.prefetchWithWorker(ctx.targetApp);
      }
    }
  ]
});
```

### 5. 认证插件 (@micro-core/plugin-auth)

#### 功能说明
提供统一的认证和授权管理，支持多种认证方式和权限控制。

#### 接口定义
```typescript
interface AuthPluginOptions {
  authType?: 'jwt' | 'session' | 'oauth' | 'custom';
  loginUrl?: string;
  logoutUrl?: string;
  tokenKey?: string;
  refreshTokenKey?: string;
  autoRefresh?: boolean;
  permissions?: PermissionConfig[];
}

interface PermissionConfig {
  resource: string;
  actions: string[];
  condition?: (user: User, context: any) => boolean;
}
```

#### API方法
```typescript
// 认证管理
login(credentials: any): Promise<AuthResult>
logout(): Promise<void>
isAuthenticated(): boolean
getCurrentUser(): User | null

// 权限检查
hasPermission(resource: string, action: string): boolean
checkPermissions(permissions: string[]): boolean
```

### 6. 高性能加载器插件

#### Worker加载器 (@micro-core/plugin-loader-worker)

**功能说明**: 使用 Web Worker 进行资源加载，避免阻塞主线程。

**接口定义**:
```typescript
interface WorkerLoaderOptions {
  maxWorkers?: number;
  workerScript?: string;
  enableCache?: boolean;
  cacheStrategy?: 'memory' | 'indexeddb' | 'localstorage';
  timeout?: number;
}
```

#### WASM加载器 (@micro-core/plugin-loader-wasm)

**功能说明**: 使用 WebAssembly 进行高性能资源处理和解析。

**接口定义**:
```typescript
interface WasmLoaderOptions {
  wasmModule?: string;
  enableStreaming?: boolean;
  memoryPages?: number;
  enableOptimization?: boolean;
}
```

## 典型使用场景

### 场景1: 多框架应用集成

```typescript
import { 
  RouterPlugin, 
  CommunicationPlugin, 
  ProxySandboxPlugin,
  ReactAdapterPlugin,
  VueAdapterPlugin 
} from '@micro-core/plugins';

// 配置路由
kernel.use(RouterPlugin, {
  mode: 'history',
  routes: [
    { path: '/react-app', app: 'react-app' },
    { path: '/vue-app', app: 'vue-app' }
  ]
});

// 配置通信
kernel.use(CommunicationPlugin, {
  enableEventBus: true,
  enableGlobalState: true
});

// 配置沙箱
kernel.use(ProxySandboxPlugin, {
  strictIsolation: true
});

// 配置框架适配器
kernel.use(ReactAdapterPlugin);
kernel.use(VueAdapterPlugin);
```

### 场景2: 高性能预加载配置

```typescript
import { 
  PrefetchPlugin, 
  WorkerLoaderPlugin,
  CachePlugin 
} from '@micro-core/plugins';

// 配置预加载策略
kernel.use(PrefetchPlugin, {
  enableRoutePrediction: true,
  enableViewportDetection: true,
  strategies: [
    {
      name: 'priority-based',
      condition: (ctx) => ctx.route.meta?.priority === 'high',
      execute: async (ctx) => {
        await ctx.prefetchWithWorker(ctx.targetApp);
      }
    }
  ]
});

// 配置Worker加载器
kernel.use(WorkerLoaderPlugin, {
  maxWorkers: 4,
  enableCache: true,
  cacheStrategy: 'indexeddb'
});
```

### 场景3: 企业级权限控制

```typescript
import { 
  AuthPlugin, 
  RouterPlugin,
  CommunicationPlugin 
} from '@micro-core/plugins';

// 配置认证
kernel.use(AuthPlugin, {
  authType: 'jwt',
  autoRefresh: true,
  permissions: [
    {
      resource: 'admin-panel',
      actions: ['read', 'write'],
      condition: (user) => user.role === 'admin'
    }
  ]
});

// 配置路由守卫
kernel.use(RouterPlugin, {
  beforeEach: async (to, from) => {
    const auth = kernel.getPlugin('auth');
    if (to.meta?.requiresAuth && !auth.isAuthenticated()) {
      await auth.redirectToLogin();
      return false;
    }
    return true;
  }
});
```

## 版本兼容性说明

### 支持的版本范围

| 组件 | 最低版本 | 推荐版本 | 最新版本 |
|------|----------|----------|----------|
| @micro-core/core | 0.1.0 | 0.1.0 | 0.1.0 |
| Node.js | 18.0.0 | 20.0.0 | 21.x |
| TypeScript | 5.0.0 | 5.3.0 | 5.3.x |
| 浏览器支持 | ES2020 | ES2022 | Latest |

### 插件版本兼容性矩阵

| 插件名称 | Core 0.1.0 | 向后兼容 | 破坏性变更 |
|----------|------------|----------|------------|
| plugin-router | ✅ | v0.1.x | 无 |
| plugin-communication | ✅ | v0.1.x | 无 |
| plugin-sandbox-proxy | ✅ | v0.1.x | 无 |
| plugin-sandbox-iframe | ✅ | v0.1.x | 无 |
| plugin-prefetch | ✅ | v0.1.x | 无 |
| plugin-auth | ✅ | v0.1.x | 无 |

### 升级指南

#### 从 0.0.x 升级到 0.1.0
```typescript
// 旧版本 (0.0.x)
kernel.installPlugin(plugin, options);

// 新版本 (0.1.0)
kernel.use(plugin, options);
```

## 错误代码对照表

### 插件系统错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `E001` | `PLUGIN_NOT_FOUND` | 插件不存在 | 检查插件名称是否正确 |
| `E002` | `PLUGIN_ALREADY_EXISTS` | 插件已存在 | 使用不同的插件名称或先卸载现有插件 |
| `E003` | `PLUGIN_INSTALL_FAILED` | 插件安装失败 | 检查插件代码和依赖项 |
| `E004` | `PLUGIN_UNINSTALL_FAILED` | 插件卸载失败 | 检查插件的 uninstall 方法实现 |
| `E005` | `PLUGIN_DEPENDENCY_MISSING` | 插件依赖缺失 | 先安装依赖的插件 |
| `E006` | `PLUGIN_DEPENDENCY_CIRCULAR` | 插件循环依赖 | 重新设计插件依赖关系 |
| `E007` | `HOOK_EXECUTION_FAILED` | 钩子执行失败 | 检查钩子回调函数的实现 |
| `E008` | `INVALID_PLUGIN_CONFIG` | 插件配置无效 | 检查插件配置参数 |

### 钩子系统错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `H001` | `HOOK_NOT_FOUND` | 钩子不存在 | 检查钩子名称是否正确 |
| `H002` | `HOOK_CALLBACK_INVALID` | 钩子回调无效 | 确保回调是有效的函数 |
| `H003` | `HOOK_EXECUTION_TIMEOUT` | 钩子执行超时 | 优化钩子回调的执行时间 |
| `H004` | `HOOK_CALLBACK_ERROR` | 钩子回调执行错误 | 检查回调函数的实现逻辑 |

### 路由插件错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `R001` | `ROUTE_NOT_FOUND` | 路由不存在 | 检查路由配置 |
| `R002` | `ROUTE_GUARD_REJECTED` | 路由守卫拒绝 | 检查路由守卫逻辑 |
| `R003` | `INVALID_ROUTE_CONFIG` | 路由配置无效 | 检查路由配置格式 |
| `R004` | `NAVIGATION_CANCELLED` | 导航被取消 | 检查导航逻辑 |

### 沙箱插件错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `S001` | `SANDBOX_CREATION_FAILED` | 沙箱创建失败 | 检查浏览器兼容性 |
| `S002` | `SANDBOX_ISOLATION_BREACH` | 沙箱隔离被破坏 | 检查沙箱配置 |
| `S003` | `SANDBOX_RESOURCE_LEAK` | 沙箱资源泄漏 | 确保正确清理沙箱资源 |
| `S004` | `SANDBOX_COMMUNICATION_ERROR` | 沙箱通信错误 | 检查沙箱通信机制 |

### 通信插件错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `C001` | `EVENT_BUS_ERROR` | 事件总线错误 | 检查事件监听器实现 |
| `C002` | `GLOBAL_STATE_ERROR` | 全局状态错误 | 检查状态管理逻辑 |
| `C003` | `MESSAGE_BRIDGE_ERROR` | 消息桥接错误 | 检查消息传递机制 |
| `C004` | `SERIALIZATION_ERROR` | 序列化错误 | 确保数据可序列化 |

## 性能优化建议

### 插件加载优化

1. **按需加载**: 使用动态导入延迟加载非关键插件
```typescript
const loadRouterPlugin = async () => {
  const { RouterPlugin } = await import('@micro-core/plugin-router');
  return RouterPlugin;
};
```

2. **插件预加载**: 在空闲时间预加载可能需要的插件
```typescript
requestIdleCallback(async () => {
  await import('@micro-core/plugin-prefetch');
});
```

3. **插件缓存**: 缓存插件实例避免重复创建
```typescript
const pluginCache = new Map();
const getPlugin = (name) => {
  if (!pluginCache.has(name)) {
    pluginCache.set(name, createPlugin(name));
  }
  return pluginCache.get(name);
};
```

### 钩子执行优化

1. **异步钩子**: 使用异步钩子避免阻塞
```typescript
pluginSystem.addHook('beforeAppLoad', async (context, appConfig) => {
  // 异步处理逻辑
  await processAppConfig(appConfig);
});
```

2. **钩子去重**: 避免重复注册相同的钩子
```typescript
if (!pluginSystem.hasHook('beforeAppLoad', myCallback)) {
  pluginSystem.addHook('beforeAppLoad', myCallback);
}
```

## 调试和诊断

### 插件调试工具

```typescript
// 获取插件系统状态
const status = pluginSystem.getStatus();
console.log('插件数量:', status.pluginCount);
console.log('钩子数量:', status.hookCount);
console.log('每个插件的钩子数:', status.hooksPerPlugin);

// 启用调试模式
kernel.enableDebugMode({
  logLevel: 'debug',
  enablePerformanceMonitor: true,
  enablePluginTracing: true
});
```

### 性能监控

```typescript
// 监控插件加载时间
const startTime = performance.now();
await kernel.use(plugin, options);
const loadTime = performance.now() - startTime;
console.log(`插件 ${plugin.name} 加载耗时: ${loadTime}ms`);

// 监控钩子执行时间
pluginSystem.addHook('beforeAppLoad', async (context) => {
  const start = performance.now();
  await processApp(context);
  const duration = performance.now() - start;
  console.log(`钩子执行耗时: ${duration}ms`);
});
```

---

**文档版本**: v1.0.0  
**最后更新**: 2025年7月  
**维护者**: Echo (<EMAIL>)  
**许可证**: MIT License