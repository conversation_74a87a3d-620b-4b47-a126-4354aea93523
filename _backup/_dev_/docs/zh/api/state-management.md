# 状态管理 API

Micro-Core 提供了强大的状态管理系统，支持全局状态共享、应用级状态隔离、状态持久化等功能，确保微前端应用间的数据一致性和状态同步。

## StateManager 核心类

### 类定义

```typescript
import { StateManager, StateConfig, StateSubscriber } from '@micro-core/core';

export class StateManager {
  private globalState: Map<string, any> = new Map();
  private appStates: Map<string, Map<string, any>> = new Map();
  private subscribers: Map<string, Set<StateSubscriber>> = new Map();
  private config: StateConfig;
  private eventBus: EventBus;
  private persistenceAdapter?: PersistenceAdapter;
  
  constructor(config: StateConfig, eventBus: EventBus) {
    this.config = config;
    this.eventBus = eventBus;
    this.initializePersistence();
  }
  
  // 全局状态管理
  getGlobalState<T = any>(key: string, defaultValue?: T): T;
  setGlobalState<T = any>(key: string, value: T): void;
  removeGlobalState(key: string): void;
  
  // 应用级状态管理
  getAppState<T = any>(appName: string, key: string, defaultValue?: T): T;
  setAppState<T = any>(appName: string, key: string, value: T): void;
  removeAppState(appName: string, key: string): void;
  clearAppState(appName: string): void;
  
  // 状态订阅
  subscribe(key: string, subscriber: StateSubscriber): () => void;
  subscribeApp(appName: string, key: string, subscriber: StateSubscriber): () => void;
  unsubscribe(key: string, subscriber: StateSubscriber): void;
  
  // 状态持久化
  persist(key: string, options?: PersistOptions): void;
  restore(key: string): Promise<any>;
  clearPersisted(key: string): void;
  
  // 状态快照
  createSnapshot(appName?: string): StateSnapshot;
  restoreSnapshot(snapshot: StateSnapshot): void;
  
  // 状态同步
  syncState(key: string, targetApps?: string[]): void;
  enableAutoSync(key: string, options?: SyncOptions): void;
  disableAutoSync(key: string): void;
  
  // 状态验证
  validateState(key: string, schema: StateSchema): boolean;
  setStateSchema(key: string, schema: StateSchema): void;
  
  // 状态计算
  computed<T>(key: string, computeFn: ComputeFunction<T>, dependencies: string[]): T;
  
  // 批量操作
  batch(operations: StateOperation[]): void;
  
  // 状态重置
  reset(keys?: string[]): void;
  resetApp(appName: string, keys?: string[]): void;
}
```

## 状态配置接口

### StateConfig 接口

```typescript
interface StateConfig {
  // 持久化配置
  persistence?: {
    enabled: boolean;
    adapter: 'localStorage' | 'sessionStorage' | 'indexedDB' | 'custom';
    prefix?: string;
    exclude?: string[];
    include?: string[];
  };
  
  // 同步配置
  sync?: {
    enabled: boolean;
    debounce?: number;
    throttle?: number;
    conflictResolution?: 'merge' | 'override' | 'custom';
  };
  
  // 验证配置
  validation?: {
    enabled: boolean;
    strict?: boolean;
    schemas?: Record<string, StateSchema>;
  };
  
  // 性能配置
  performance?: {
    enableBatching: boolean;
    batchSize?: number;
    enableMemoization: boolean;
    maxCacheSize?: number;
  };
  
  // 调试配置
  debug?: {
    enabled: boolean;
    logLevel?: 'info' | 'warn' | 'error';
    enableDevtools?: boolean;
  };
}

// 示例配置
const stateConfig: StateConfig = {
  persistence: {
    enabled: true,
    adapter: 'localStorage',
    prefix: 'micro-core-state',
    exclude: ['temp', 'cache']
  },
  sync: {
    enabled: true,
    debounce: 100,
    conflictResolution: 'merge'
  },
  validation: {
    enabled: true,
    strict: false
  },
  performance: {
    enableBatching: true,
    batchSize: 10,
    enableMemoization: true,
    maxCacheSize: 100
  },
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    logLevel: 'info',
    enableDevtools: true
  }
};
```

### 状态订阅接口

```typescript
interface StateSubscriber {
  (newValue: any, oldValue: any, key: string): void;
}

interface StateSubscription {
  key: string;
  subscriber: StateSubscriber;
  appName?: string;
  options?: SubscriptionOptions;
}

interface SubscriptionOptions {
  immediate?: boolean;
  deep?: boolean;
  filter?: (newValue: any, oldValue: any) => boolean;
  transform?: (value: any) => any;
}
```

## 核心方法详解

### 全局状态管理

#### getGlobalState()

获取全局状态值。

```typescript
getGlobalState<T = any>(key: string, defaultValue?: T): T;
```

**参数:**
- `key` - 状态键名
- `defaultValue` - 默认值（可选）

**返回值:**
- 返回状态值，如果不存在则返回默认值

**示例:**

```typescript
const stateManager = new StateManager(config, eventBus);

// 获取用户信息
const userInfo = stateManager.getGlobalState('user.info', {
  id: null,
  name: '',
  email: ''
});

// 获取应用配置
const appConfig = stateManager.getGlobalState('app.config');

// 获取主题设置
const theme = stateManager.getGlobalState('ui.theme', 'light');

console.log('当前用户:', userInfo);
console.log('当前主题:', theme);
```

#### setGlobalState()

设置全局状态值。

```typescript
setGlobalState<T = any>(key: string, value: T): void;
```

**参数:**
- `key` - 状态键名
- `value` - 状态值

**示例:**

```typescript
// 设置用户信息
stateManager.setGlobalState('user.info', {
  id: 123,
  name: 'John Doe',
  email: '<EMAIL>',
  avatar: '/avatars/john.jpg'
});

// 设置应用配置
stateManager.setGlobalState('app.config', {
  apiUrl: 'https://api.example.com',
  timeout: 5000,
  retries: 3
});

// 设置UI状态
stateManager.setGlobalState('ui.theme', 'dark');
stateManager.setGlobalState('ui.sidebar.collapsed', true);
stateManager.setGlobalState('ui.notifications.enabled', true);

// 设置复杂对象
stateManager.setGlobalState('shopping.cart', {
  items: [
    { id: 1, name: '商品A', price: 100, quantity: 2 },
    { id: 2, name: '商品B', price: 200, quantity: 1 }
  ],
  total: 400,
  discount: 0.1
});
```

### 应用级状态管理

#### getAppState()

获取特定应用的状态值。

```typescript
getAppState<T = any>(appName: string, key: string, defaultValue?: T): T;
```

**示例:**

```typescript
// 获取用户应用的状态
const userAppState = stateManager.getAppState('user-app', 'currentUser');
const userPreferences = stateManager.getAppState('user-app', 'preferences', {
  language: 'zh-CN',
  timezone: 'Asia/Shanghai'
});

// 获取订单应用的状态
const orderFilters = stateManager.getAppState('order-app', 'filters', {
  status: 'all',
  dateRange: 'last30days'
});

console.log('用户应用状态:', userAppState);
console.log('用户偏好设置:', userPreferences);
console.log('订单筛选条件:', orderFilters);
```

#### setAppState()

设置特定应用的状态值。

```typescript
setAppState<T = any>(appName: string, key: string, value: T): void;
```

**示例:**

```typescript
// 设置用户应用状态
stateManager.setAppState('user-app', 'currentUser', {
  id: 123,
  name: 'John Doe',
  role: 'admin'
});

stateManager.setAppState('user-app', 'preferences', {
  language: 'en-US',
  timezone: 'America/New_York',
  theme: 'dark',
  notifications: {
    email: true,
    push: false,
    sms: true
  }
});

// 设置订单应用状态
stateManager.setAppState('order-app', 'filters', {
  status: 'pending',
  dateRange: 'today',
  customer: 'VIP'
});

stateManager.setAppState('order-app', 'pagination', {
  page: 1,
  pageSize: 20,
  total: 150
});

// 设置产品应用状态
stateManager.setAppState('product-app', 'selectedCategory', 'electronics');
stateManager.setAppState('product-app', 'searchQuery', 'iPhone');
```

### 状态订阅

#### subscribe()

订阅全局状态变化。

```typescript
subscribe(key: string, subscriber: StateSubscriber): () => void;
```

**参数:**
- `key` - 状态键名
- `subscriber` - 订阅回调函数

**返回值:**
- 返回取消订阅的函数

**示例:**

```typescript
// 订阅用户信息变化
const unsubscribeUser = stateManager.subscribe('user.info', (newUser, oldUser) => {
  console.log('用户信息已更新:', newUser);
  console.log('之前的用户信息:', oldUser);
  
  // 更新UI显示
  updateUserDisplay(newUser);
  
  // 记录用户活动
  logUserActivity('profile_updated', newUser.id);
});

// 订阅主题变化
const unsubscribeTheme = stateManager.subscribe('ui.theme', (newTheme, oldTheme) => {
  console.log(`主题从 ${oldTheme} 切换到 ${newTheme}`);
  
  // 应用新主题
  document.body.className = `theme-${newTheme}`;
  
  // 保存用户偏好
  saveUserPreference('theme', newTheme);
});

// 订阅购物车变化
const unsubscribeCart = stateManager.subscribe('shopping.cart', (newCart, oldCart) => {
  console.log('购物车已更新:', newCart);
  
  // 更新购物车图标
  updateCartIcon(newCart.items.length);
  
  // 计算总价
  const total = newCart.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  updateCartTotal(total);
  
  // 检查库存
  checkInventory(newCart.items);
});

// 带选项的订阅
const unsubscribeWithOptions = stateManager.subscribe('app.config', (newConfig, oldConfig) => {
  console.log('应用配置已更新');
  
  // 重新初始化API客户端
  if (newConfig.apiUrl !== oldConfig.apiUrl) {
    initializeApiClient(newConfig.apiUrl);
  }
}, {
  immediate: true, // 立即执行一次
  deep: true, // 深度监听对象变化
  filter: (newVal, oldVal) => newVal.apiUrl !== oldVal.apiUrl // 只在API URL变化时触发
});

// 清理订阅
setTimeout(() => {
  unsubscribeUser();
  unsubscribeTheme();
  unsubscribeCart();
  unsubscribeWithOptions();
}, 60000);
```

#### subscribeApp()

订阅特定应用的状态变化。

```typescript
subscribeApp(appName: string, key: string, subscriber: StateSubscriber): () => void;
```

**示例:**

```typescript
// 订阅用户应用的状态变化
const unsubscribeUserApp = stateManager.subscribeApp('user-app', 'currentUser', (newUser, oldUser) => {
  console.log('用户应用的当前用户已更新:', newUser);
  
  // 同步到其他应用
  stateManager.setGlobalState('user.current', newUser);
  
  // 更新权限
  updateUserPermissions(newUser.role);
});

// 订阅订单应用的筛选条件变化
const unsubscribeOrderFilters = stateManager.subscribeApp('order-app', 'filters', (newFilters, oldFilters) => {
  console.log('订单筛选条件已更新:', newFilters);
  
  // 重新加载订单列表
  loadOrderList(newFilters);
  
  // 更新URL参数
  updateUrlParams(newFilters);
});

// 订阅产品应用的搜索状态
const unsubscribeProductSearch = stateManager.subscribeApp('product-app', 'searchQuery', (newQuery, oldQuery) => {
  console.log(`搜索查询从 "${oldQuery}" 变更为 "${newQuery}"`);
  
  // 执行搜索
  if (newQuery.length >= 3) {
    performSearch(newQuery);
  } else {
    clearSearchResults();
  }
  
  // 记录搜索历史
  addToSearchHistory(newQuery);
});
```

### 状态持久化

#### persist()

将状态持久化到存储中。

```typescript
persist(key: string, options?: PersistOptions): void;
```

**示例:**

```typescript
// 基本持久化
stateManager.persist('user.preferences');
stateManager.persist('ui.theme');
stateManager.persist('shopping.cart');

// 带选项的持久化
stateManager.persist('app.settings', {
  storage: 'localStorage', // 存储类型
  ttl: 7 * 24 * 60 * 60 * 1000, // 7天过期
  encrypt: true, // 加密存储
  compress: true // 压缩存储
});

// 持久化应用状态
stateManager.persist('user-app.preferences', {
  storage: 'sessionStorage', // 会话存储
  key: 'user_app_prefs' // 自定义存储键名
});

// 条件持久化
stateManager.persist('temp.data', {
  condition: (value) => value && Object.keys(value).length > 0, // 只在有数据时持久化
  transform: (value) => ({ ...value, timestamp: Date.now() }) // 添加时间戳
});
```

#### restore()

从存储中恢复状态。

```typescript
restore(key: string): Promise<any>;
```

**示例:**

```typescript
// 应用启动时恢复状态
const initializeAppState = async () => {
  try {
    // 恢复用户偏好
    const userPreferences = await stateManager.restore('user.preferences');
    if (userPreferences) {
      stateManager.setGlobalState('user.preferences', userPreferences);
      console.log('用户偏好已恢复:', userPreferences);
    }
    
    // 恢复UI主题
    const theme = await stateManager.restore('ui.theme');
    if (theme) {
      stateManager.setGlobalState('ui.theme', theme);
      document.body.className = `theme-${theme}`;
      console.log('主题已恢复:', theme);
    }
    
    // 恢复购物车
    const cart = await stateManager.restore('shopping.cart');
    if (cart) {
      stateManager.setGlobalState('shopping.cart', cart);
      console.log('购物车已恢复:', cart);
    }
    
    // 恢复应用特定状态
    const userAppPrefs = await stateManager.restore('user-app.preferences');
    if (userAppPrefs) {
      stateManager.setAppState('user-app', 'preferences', userAppPrefs);
      console.log('用户应用偏好已恢复:', userAppPrefs);
    }
    
  } catch (error) {
    console.error('状态恢复失败:', error);
  }
};

// 在应用启动时调用
initializeAppState();

// 批量恢复
const restoreMultipleStates = async () => {
  const keys = ['user.preferences', 'ui.theme', 'shopping.cart'];
  const results = await Promise.allSettled(
    keys.map(key => stateManager.restore(key))
  );
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled' && result.value) {
      stateManager.setGlobalState(keys[index], result.value);
      console.log(`${keys[index]} 恢复成功`);
    } else {
      console.warn(`${keys[index]} 恢复失败:`, result.reason);
    }
  });
};
```

### 状态快照

#### createSnapshot()

创建状态快照。

```typescript
createSnapshot(appName?: string): StateSnapshot;
```

**示例:**

```typescript
// 创建全局状态快照
const globalSnapshot = stateManager.createSnapshot();
console.log('全局状态快照:', globalSnapshot);

// 创建特定应用的状态快照
const userAppSnapshot = stateManager.createSnapshot('user-app');
console.log('用户应用状态快照:', userAppSnapshot);

// 在关键操作前创建快照
const performCriticalOperation = async () => {
  // 创建操作前快照
  const beforeSnapshot = stateManager.createSnapshot();
  
  try {
    // 执行关键操作
    await criticalOperation();
    
    console.log('关键操作执行成功');
  } catch (error) {
    console.error('关键操作失败，恢复状态:', error);
    
    // 恢复到操作前状态
    stateManager.restoreSnapshot(beforeSnapshot);
  }
};

// 定期创建快照用于调试
const createPeriodicSnapshots = () => {
  setInterval(() => {
    const snapshot = stateManager.createSnapshot();
    
    // 保存到调试存储
    localStorage.setItem(`debug-snapshot-${Date.now()}`, JSON.stringify(snapshot));
    
    // 只保留最近10个快照
    const keys = Object.keys(localStorage).filter(key => key.startsWith('debug-snapshot-'));
    if (keys.length > 10) {
      keys.sort().slice(0, keys.length - 10).forEach(key => {
        localStorage.removeItem(key);
      });
    }
  }, 60000); // 每分钟创建一次快照
};

if (process.env.NODE_ENV === 'development') {
  createPeriodicSnapshots();
}
```

#### restoreSnapshot()

恢复状态快照。

```typescript
restoreSnapshot(snapshot: StateSnapshot): void;
```

**示例:**

```typescript
// 恢复快照
const restoreFromSnapshot = (snapshot: StateSnapshot) => {
  try {
    stateManager.restoreSnapshot(snapshot);
    console.log('状态快照恢复成功');
    
    // 通知所有应用状态已恢复
    eventBus.emit('state:restored', { snapshot });
  } catch (error) {
    console.error('状态快照恢复失败:', error);
  }
};

// 从调试存储恢复快照
const restoreDebugSnapshot = (timestamp: string) => {
  const snapshotData = localStorage.getItem(`debug-snapshot-${timestamp}`);
  if (snapshotData) {
    const snapshot = JSON.parse(snapshotData);
    restoreFromSnapshot(snapshot);
  }
};
```

### 状态同步

#### syncState()

同步状态到指定应用。

```typescript
syncState(key: string, targetApps?: string[]): void;
```

**示例:**

```typescript
// 同步用户信息到所有应用
stateManager.syncState('user.info');

// 同步主题设置到指定应用
stateManager.syncState('ui.theme', ['user-app', 'product-app', 'order-app']);

// 同步购物车状态
stateManager.syncState('shopping.cart', ['product-app', 'checkout-app']);

// 实时同步示例
const setupRealTimeSync = () => {
  // 监听用户信息变化并同步
  stateManager.subscribe('user.info', (newUser) => {
    stateManager.syncState('user.info');
    
    // 同步到后端
    syncUserToBackend(newUser);
  });
  
  // 监听主题变化并同步
  stateManager.subscribe('ui.theme', (newTheme) => {
    stateManager.syncState('ui.theme');
    
    // 保存到用户偏好
    saveUserPreference('theme', newTheme);
  });
};

setupRealTimeSync();
```

#### enableAutoSync()

启用自动状态同步。

```typescript
enableAutoSync(key: string, options?: SyncOptions): void;
```

**示例:**

```typescript
// 启用基本自动同步
stateManager.enableAutoSync('user.info');
stateManager.enableAutoSync('ui.theme');

// 启用带选项的自动同步
stateManager.enableAutoSync('shopping.cart', {
  debounce: 500, // 防抖延迟
  throttle: 1000, // 节流间隔
  targetApps: ['product-app', 'checkout-app'], // 目标应用
  condition: (value) => value && value.items.length > 0, // 同步条件
  transform: (value) => ({ ...value, syncTime: Date.now() }) // 数据转换
});

// 启用应用间数据同步
stateManager.enableAutoSync('user-app.preferences', {
  bidirectional: true, // 双向同步
  conflictResolution: 'merge', // 冲突解决策略
  syncToGlobal: true // 同步到全局状态
});

// 启用实时协作同步
stateManager.enableAutoSync('collaboration.cursor', {
  realtime: true, // 实时同步
  broadcast: true, // 广播到所有应用
  excludeSelf: true // 排除自身
});
```

### 状态验证

#### validateState()

验证状态值是否符合模式。

```typescript
validateState(key: string, schema: StateSchema): boolean;
```

**示例:**

```typescript
// 定义状态模式
const userSchema: StateSchema = {
  type: 'object',
  properties: {
    id: { type: 'number', minimum: 1 },
    name: { type: 'string', minLength: 1, maxLength: 100 },
    email: { type: 'string', format: 'email' },
    role: { type: 'string', enum: ['admin', 'user', 'guest'] },
    preferences: {
      type: 'object',
      properties: {
        theme: { type: 'string', enum: ['light', 'dark'] },
        language: { type: 'string', pattern: '^[a-z]{2}-[A-Z]{2}$' }
      }
    }
  },
  required: ['id', 'name', 'email', 'role']
};

// 设置状态模式
stateManager.setStateSchema('user.info', userSchema);

// 验证状态
const isValid = stateManager.validateState('user.info', userSchema);
console.log('用户信息验证结果:', isValid);

// 在设置状态时自动验证
const setUserWithValidation = (userInfo: any) => {
  if (stateManager.validateState('user.info', userSchema)) {
    stateManager.setGlobalState('user.info', userInfo);
    console.log('用户信息设置成功');
  } else {
    console.error('用户信息格式不正确');
    throw new Error('Invalid user information');
  }
};

// 使用示例
try {
  setUserWithValidation({
    id: 123,
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'admin',
    preferences: {
      theme: 'dark',
      language: 'zh-CN'
    }
  });
} catch (error) {
  console.error('设置用户信息失败:', error);
}
```

### 计算状态

#### computed()

创建计算状态。

```typescript
computed<T>(key: string, computeFn: ComputeFunction<T>, dependencies: string[]): T;
```

**示例:**

```typescript
// 计算购物车总价
const cartTotal = stateManager.computed(
  'shopping.cartTotal',
  () => {
    const cart = stateManager.getGlobalState('shopping.cart', { items: [] });
    return cart.items.reduce((total, item) => total + item.price * item.quantity, 0);
  },
  ['shopping.cart']
);

console.log('购物车总价:', cartTotal);

// 计算用户权限
const userPermissions = stateManager.computed(
  'user.permissions',
  () => {
    const user = stateManager.getGlobalState('user.info');
    const rolePermissions = stateManager.getGlobalState('roles.permissions', {});
    
    if (!user || !user.role) return [];
    
    return rolePermissions[user.role] || [];
  },
  ['user.info', 'roles.permissions']
);

console.log('用户权限:', userPermissions);

// 计算应用状态摘要
const appStateSummary = stateManager.computed(
  'app.summary',
  () => {
    const userCount = stateManager.getAppState('user-app', 'userCount', 0);
    const orderCount = stateManager.getAppState('order-app', 'orderCount', 0);
    const productCount = stateManager.getAppState('product-app', 'productCount', 0);
    
    return {
      totalUsers: userCount,
      totalOrders: orderCount,
      totalProducts: productCount,
      lastUpdated: new Date().toISOString()
    };
  },
  ['user-app.userCount', 'order-app.orderCount', 'product-app.productCount']
);

console.log('应用状态摘要:', appStateSummary);

// 复杂计算状态
const dashboardData = stateManager.computed(
  'dashboard.data',
  () => {
    const users = stateManager.getGlobalState('users', []);
    const orders = stateManager.getGlobalState('orders', []);
    const products = stateManager.getGlobalState('products', []);
    
    // 计算统计数据
    const activeUsers = users.filter(user => user.status === 'active').length;
    const pendingOrders = orders.filter(order => order.status === 'pending').length;
    const lowStockProducts = products.filter(product => product.stock < 10).length;
    
    // 计算趋势数据
    const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentOrders = orders.filter(order => new Date(order.createdAt) > last30Days);
    const monthlyRevenue = recentOrders.reduce((sum, order) => sum + order.total, 0);
    
    return {
      stats: {
        activeUsers,
        pendingOrders,
        lowStockProducts,
        monthlyRevenue
      },
      trends: {
        ordersThisMonth: recentOrders.length,
        averageOrderValue: recentOrders.length > 0 ? monthlyRevenue / recentOrders.length : 0
      },
      alerts: [
        ...(lowStockProducts > 0 ? [`${lowStockProducts} 个产品库存不足`] : []),
        ...(pendingOrders > 10 ? [`有 ${pendingOrders} 个待处理订单`] : [])
      ]
    };
  },
  ['users', 'orders', 'products']
);

console.log('仪表板数据:', dashboardData);
```

### 批量操作

#### batch()

批量执行状态操作。

```typescript
batch(operations: StateOperation[]): void;
```

**示例:**

```typescript
// 批量设置状态
stateManager.batch([
  { type: 'set', key: 'user.info', value: { id: 123, name: 'John' } },
  { type: 'set', key: 'ui.theme', value: 'dark' },
  { type: 'set', key: 'app.initialized', value: true },
  { type: 'remove', key: 'temp.data' }
]);

// 批量应用状态操作
stateManager.batch([
  { type: 'setApp', appName: 'user-app', key: 'currentUser', value: { id: 123 } },
  { type: 'setApp', appName: 'order-app', key: 'filters', value: { status: 'all' } },
  { type: 'removeApp', appName: 'temp-app', key: 'cache' }
]);

// 复杂批量