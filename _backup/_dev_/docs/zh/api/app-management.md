# 应用管理 API

Micro-Core 的应用管理系统提供了完整的微前端应用生命周期管理，包括应用注册、加载、挂载、卸载、状态管理等核心功能。

## AppManager 核心类

### 类定义

```typescript
import { AppManager, MicroApp, AppConfig, AppStatus } from '@micro-core/core';

export class AppManager {
  private apps: Map<string, MicroApp> = new Map();
  private config: AppManagerConfig;
  private eventBus: EventBus;
  private stateManager: StateManager;
  private loadingQueue: Map<string, Promise<void>> = new Map();
  
  constructor(config: AppManagerConfig, eventBus: EventBus, stateManager: StateManager) {
    this.config = config;
    this.eventBus = eventBus;
    this.stateManager = stateManager;
  }
  
  // 注册应用
  registerApp(config: AppConfig): void;
  
  // 注销应用
  unregisterApp(name: string): void;
  
  // 加载应用
  loadApp(name: string, props?: any): Promise<MicroApp>;
  
  // 卸载应用
  unloadApp(name: string): Promise<void>;
  
  // 挂载应用
  mountApp(name: string, container?: HTMLElement, props?: any): Promise<void>;
  
  // 卸载应用
  unmountApp(name: string): Promise<void>;
  
  // 获取应用实例
  getApp(name: string): MicroApp | null;
  
  // 获取所有应用
  getApps(): MicroApp[];
  
  // 获取应用状态
  getAppStatus(name: string): AppStatus;
  
  // 预加载应用
  preloadApp(name: string): Promise<void>;
  
  // 批量操作
  batchLoad(names: string[]): Promise<MicroApp[]>;
  batchUnload(names: string[]): Promise<void>;
  
  // 应用通信
  sendMessage(fromApp: string, toApp: string, message: any): Promise<void>;
  broadcast(message: any, excludeApps?: string[]): Promise<void>;
}
```

## 应用配置接口

### AppConfig 接口

```typescript
interface AppConfig {
  // 应用名称（必需）
  name: string;
  
  // 应用入口（必需）
  entry: string | AppEntry;
  
  // 挂载容器（必需）
  container: string | HTMLElement;
  
  // 激活规则（必需）
  activeRule: string | ActiveRule | ActiveRule[];
  
  // 应用框架类型
  framework?: FrameworkType;
  
  // 应用属性
  props?: Record<string, any>;
  
  // 加载配置
  loader?: LoaderConfig;
  
  // 沙箱配置
  sandbox?: SandboxConfig;
  
  // 样式隔离配置
  styles?: StyleConfig;
  
  // 生命周期钩子
  lifecycle?: LifecycleHooks;
  
  // 自定义元数据
  metadata?: Record<string, any>;
}
```

### 应用入口配置

```typescript
interface AppEntry {
  // 脚本入口
  scripts?: string[];
  
  // 样式入口
  styles?: string[];
  
  // HTML入口
  html?: string;
  
  // 模块入口
  module?: string;
  
  // 入口类型
  type?: 'script' | 'module' | 'html';
}

// 示例配置
const appConfig: AppConfig = {
  name: 'user-app',
  entry: {
    scripts: [
      'https://cdn.example.com/user-app/main.js',
      'https://cdn.example.com/user-app/vendor.js'
    ],
    styles: [
      'https://cdn.example.com/user-app/main.css'
    ],
    type: 'script'
  },
  container: '#user-app-container',
  activeRule: '/user'
};
```

### 激活规则配置

```typescript
type ActiveRule = string | RegExp | ((location: Location) => boolean);

// 字符串路径匹配
const stringRule: ActiveRule = '/user';

// 正则表达式匹配
const regexRule: ActiveRule = /^\/user/;

// 自定义函数匹配
const functionRule: ActiveRule = (location) => {
  return location.pathname.startsWith('/user') && 
         location.search.includes('module=user');
};

// 多规则组合
const multipleRules: ActiveRule[] = [
  '/user',
  '/profile',
  (location) => location.hash.includes('#user')
];
```

## 核心方法详解

### registerApp()

注册微前端应用到系统中。

```typescript
registerApp(config: AppConfig): void;
```

**参数:**
- `config` - 应用配置对象

**示例:**

```typescript
const appManager = new AppManager(config, eventBus, stateManager);

// 注册React应用
appManager.registerApp({
  name: 'react-app',
  entry: 'https://cdn.example.com/react-app/index.js',
  container: '#react-container',
  activeRule: '/react',
  framework: FrameworkType.REACT,
  props: {
    theme: 'dark',
    apiUrl: '/api/v1'
  },
  loader: {
    timeout: 10000,
    retries: 3,
    cache: true
  },
  sandbox: {
    enabled: true,
    strictStyleIsolation: true,
    experimentalStyleIsolation: false
  },
  styles: {
    isolation: true,
    prefix: 'react-app'
  },
  lifecycle: {
    beforeLoad: async (app) => {
      console.log(`准备加载应用: ${app.name}`);
    },
    afterLoad: async (app) => {
      console.log(`应用加载完成: ${app.name}`);
    }
  },
  metadata: {
    version: '1.0.0',
    author: 'React Team',
    description: 'React微前端应用'
  }
});

// 注册Vue应用
appManager.registerApp({
  name: 'vue-app',
  entry: {
    scripts: ['https://cdn.example.com/vue-app/main.js'],
    styles: ['https://cdn.example.com/vue-app/main.css'],
    type: 'script'
  },
  container: document.getElementById('vue-container'),
  activeRule: ['/vue', /^\/vue-/],
  framework: FrameworkType.VUE3,
  props: {
    locale: 'zh-CN',
    features: ['user-management', 'data-analysis']
  }
});
```

### loadApp()

加载指定的微前端应用。

```typescript
loadApp(name: string, props?: any): Promise<MicroApp>;
```

**参数:**
- `name` - 应用名称
- `props` - 传递给应用的属性（可选）

**返回值:**
- 返回加载完成的应用实例

**示例:**

```typescript
// 基本加载
try {
  const app = await appManager.loadApp('react-app');
  console.log('应用加载成功:', app.name);
} catch (error) {
  console.error('应用加载失败:', error);
}

// 带属性加载
const app = await appManager.loadApp('vue-app', {
  userId: 123,
  permissions: ['read', 'write'],
  theme: 'light'
});

// 检查加载状态
const status = appManager.getAppStatus('react-app');
if (status === AppStatus.LOADING) {
  console.log('应用正在加载中...');
} else if (status === AppStatus.LOADED) {
  console.log('应用已加载完成');
}

// 并发加载多个应用
const [reactApp, vueApp] = await Promise.all([
  appManager.loadApp('react-app'),
  appManager.loadApp('vue-app')
]);

console.log('所有应用加载完成');
```

### mountApp()

挂载应用到指定容器。

```typescript
mountApp(name: string, container?: HTMLElement, props?: any): Promise<void>;
```

**参数:**
- `name` - 应用名称
- `container` - 挂载容器（可选，默认使用注册时的容器）
- `props` - 传递给应用的属性（可选）

**示例:**

```typescript
// 基本挂载
await appManager.mountApp('react-app');

// 挂载到指定容器
const customContainer = document.getElementById('custom-container');
await appManager.mountApp('vue-app', customContainer);

// 带属性挂载
await appManager.mountApp('angular-app', null, {
  mode: 'production',
  features: {
    analytics: true,
    debugging: false
  }
});

// 动态挂载示例
const mountUserApp = async (userId: number) => {
  try {
    // 先加载应用
    await appManager.loadApp('user-app');
    
    // 然后挂载应用
    await appManager.mountApp('user-app', null, {
      userId,
      timestamp: Date.now()
    });
    
    console.log(`用户应用已为用户 ${userId} 挂载`);
  } catch (error) {
    console.error('挂载用户应用失败:', error);
  }
};

await mountUserApp(123);
```

### preloadApp()

预加载应用资源，提升用户体验。

```typescript
preloadApp(name: string): Promise<void>;
```

**示例:**

```typescript
// 预加载单个应用
await appManager.preloadApp('user-app');

// 预加载多个应用
const appsToPreload = ['dashboard-app', 'settings-app', 'profile-app'];
await Promise.all(
  appsToPreload.map(name => appManager.preloadApp(name))
);

// 智能预加载
const preloadStrategy = async () => {
  const currentRoute = window.location.pathname;
  
  // 根据当前路由预加载相关应用
  if (currentRoute.startsWith('/user')) {
    await appManager.preloadApp('user-profile-app');
    await appManager.preloadApp('user-settings-app');
  } else if (currentRoute.startsWith('/admin')) {
    await appManager.preloadApp('admin-dashboard-app');
    await appManager.preloadApp('admin-users-app');
  }
};

// 在路由变化时执行预加载
router.afterEach(preloadStrategy);

// 空闲时间预加载
if ('requestIdleCallback' in window) {
  requestIdleCallback(() => {
    appManager.preloadApp('rarely-used-app');
  });
}
```

## 应用状态管理

### AppStatus 枚举

```typescript
enum AppStatus {
  NOT_LOADED = 'not_loaded',     // 未加载
  LOADING = 'loading',           // 加载中
  LOADED = 'loaded',             // 已加载
  MOUNTING = 'mounting',         // 挂载中
  MOUNTED = 'mounted',           // 已挂载
  UNMOUNTING = 'unmounting',     // 卸载中
  UNMOUNTED = 'unmounted',       // 已卸载
  ERROR = 'error',               // 错误状态
  SKIP_BECAUSE_BROKEN = 'skip_because_broken' // 因错误跳过
}
```

### MicroApp 接口

```typescript
interface MicroApp {
  // 应用名称
  name: string;
  
  // 应用状态
  status: AppStatus;
  
  // 应用配置
  config: AppConfig;
  
  // 应用实例
  instance?: any;
  
  // 挂载容器
  container?: HTMLElement;
  
  // 应用属性
  props: Record<string, any>;
  
  // 生命周期方法
  load?(): Promise<void>;
  mount?(container: HTMLElement, props?: any): Promise<any>;
  unmount?(): Promise<void>;
  update?(props: any): Promise<void>;
  
  // 状态检查
  isActive(): boolean;
  isLoaded(): boolean;
  isMounted(): boolean;
  
  // 获取应用信息
  getInfo(): AppInfo;
  
  // 发送消息
  sendMessage(message: any): Promise<void>;
  
  // 接收消息
  onMessage(handler: MessageHandler): () => void;
}

interface AppInfo {
  name: string;
  status: AppStatus;
  framework?: FrameworkType;
  version?: string;
  loadTime?: number;
  mountTime?: number;
  memoryUsage?: number;
  errorCount?: number;
}
```

### 状态监听和事件

```typescript
// 监听应用状态变化
appManager.on('app:statusChange', (app: MicroApp, oldStatus: AppStatus, newStatus: AppStatus) => {
  console.log(`应用 ${app.name} 状态从 ${oldStatus} 变更为 ${newStatus}`);
  
  // 状态变化处理
  switch (newStatus) {
    case AppStatus.LOADED:
      console.log(`应用 ${app.name} 加载完成`);
      break;
    case AppStatus.MOUNTED:
      console.log(`应用 ${app.name} 挂载完成`);
      // 发送挂载完成事件
      eventBus.emit('app:mounted', { appName: app.name });
      break;
    case AppStatus.ERROR:
      console.error(`应用 ${app.name} 出现错误`);
      // 错误处理和恢复
      handleAppError(app);
      break;
  }
});

// 监听应用加载事件
appManager.on('app:beforeLoad', (app: MicroApp) => {
  console.log(`准备加载应用: ${app.name}`);
  // 显示加载指示器
  showLoadingIndicator(app.name);
});

appManager.on('app:afterLoad', (app: MicroApp) => {
  console.log(`应用加载完成: ${app.name}`);
  // 隐藏加载指示器
  hideLoadingIndicator(app.name);
});

// 监听应用挂载事件
appManager.on('app:beforeMount', (app: MicroApp) => {
  console.log(`准备挂载应用: ${app.name}`);
  // 准备挂载环境
  prepareContainer(app.container);
});

appManager.on('app:afterMount', (app: MicroApp) => {
  console.log(`应用挂载完成: ${app.name}`);
  // 应用挂载后处理
  postMountProcessing(app);
});

// 监听应用错误
appManager.on('app:error', (app: MicroApp, error: Error) => {
  console.error(`应用 ${app.name} 发生错误:`, error);
  
  // 错误上报
  errorReporting.captureException(error, {
    app: app.name,
    status: app.status,
    timestamp: Date.now()
  });
  
  // 错误恢复策略
  if (error.name === 'LoadError') {
    // 重新加载应用
    setTimeout(() => {
      appManager.loadApp(app.name);
    }, 5000);
  }
});
```

## 应用通信

### 应用间消息传递

```typescript
// 发送消息到指定应用
await appManager.sendMessage('user-app', 'profile-app', {
  type: 'USER_SELECTED',
  payload: {
    userId: 123,
    userName: 'John Doe'
  }
});

// 广播消息到所有应用
await appManager.broadcast({
  type: 'THEME_CHANGED',
  payload: {
    theme: 'dark',
    timestamp: Date.now()
  }
}, ['system-app']); // 排除系统应用

// 应用内消息处理
const userApp = appManager.getApp('user-app');
if (userApp) {
  const unsubscribe = userApp.onMessage((message) => {
    console.log('用户应用收到消息:', message);
    
    switch (message.type) {
      case 'USER_SELECTED':
        // 处理用户选择消息
        handleUserSelection(message.payload);
        break;
      case 'THEME_CHANGED':
        // 处理主题变更消息
        updateTheme(message.payload.theme);
        break;
    }
  });
  
  // 清理监听器
  setTimeout(unsubscribe, 60000);
}
```

### 请求-响应模式

```typescript
// 实现请求-响应通信
class AppCommunicator {
  private pendingRequests: Map<string, PendingRequest> = new Map();
  
  async request<T = any>(
    fromApp: string, 
    toApp: string, 
    data: any, 
    timeout: number = 5000
  ): Promise<T> {
    const requestId = generateRequestId();
    
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error(`请求超时: ${timeout}ms`));
      }, timeout);
      
      // 存储待处理请求
      this.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeoutId
      });
      
      // 发送请求消息
      appManager.sendMessage(fromApp, toApp, {
        type: 'REQUEST',
        requestId,
        data
      });
    });
  }
  
  respond(appName: string, handler: RequestHandler): () => void {
    const app = appManager.getApp(appName);
    if (!app) return () => {};
    
    return app.onMessage(async (message) => {
      if (message.type === 'REQUEST') {
        try {
          const result = await handler(message.data);
          
          // 发送响应
          await appManager.sendMessage(appName, message.sender, {
            type: 'RESPONSE',
            requestId: message.requestId,
            data: result
          });
        } catch (error) {
          // 发送错误响应
          await appManager.sendMessage(appName, message.sender, {
            type: 'RESPONSE',
            requestId: message.requestId,
            error: error.message
          });
        }
      } else if (message.type === 'RESPONSE') {
        const pendingRequest = this.pendingRequests.get(message.requestId);
        if (pendingRequest) {
          clearTimeout(pendingRequest.timeoutId);
          this.pendingRequests.delete(message.requestId);
          
          if (message.error) {
            pendingRequest.reject(new Error(message.error));
          } else {
            pendingRequest.resolve(message.data);
          }
        }
      }
    });
  }
}

// 使用请求-响应通信
const communicator = new AppCommunicator();

// 设置响应处理器
const unsubscribe = communicator.respond('user-app', async (data) => {
  if (data.action === 'GET_USER') {
    const user = await getUserById(data.userId);
    return { user };
  }
  throw new Error('未知操作');
});

// 发送请求
try {
  const response = await communicator.request(
    'profile-app',
    'user-app',
    { action: 'GET_USER', userId: 123 },
    3000
  );
  
  console.log('获取到用户信息:', response.user);
} catch (error) {
  console.error('请求失败:', error);
}
```

## 应用生命周期管理

### 生命周期钩子

```typescript
interface LifecycleHooks {
  // 加载前钩子
  beforeLoad?(app: MicroApp): Promise<void> | void;
  
  // 加载后钩子
  afterLoad?(app: MicroApp): Promise<void> | void;
  
  // 挂载前钩子
  beforeMount?(app: MicroApp): Promise<void> | void;
  
  // 挂载后钩子
  afterMount?(app: MicroApp): Promise<void> | void;
  
  // 卸载前钩子
  beforeUnmount?(app: MicroApp): Promise<void> | void;
  
  // 卸载后钩子
  afterUnmount?(app: MicroApp): Promise<void> | void;
  
  // 更新前钩子
  beforeUpdate?(app: MicroApp, props: any): Promise<void> | void;
  
  // 更新后钩子
  afterUpdate?(app: MicroApp, props: any): Promise<void> | void;
  
  // 错误处理钩子
  onError?(app: MicroApp, error: Error, phase: LifecyclePhase): Promise<void> | void;
}

enum LifecyclePhase {
  LOAD = 'load',
  MOUNT = 'mount',
  UNMOUNT = 'unmount',
  UPDATE = 'update'
}
```

### 生命周期使用示例

```typescript
// 注册带生命周期钩子的应用
appManager.registerApp({
  name: 'analytics-app',
  entry: 'https://cdn.example.com/analytics/main.js',
  container: '#analytics-container',
  activeRule: '/analytics',
  lifecycle: {
    beforeLoad: async (app) => {
      console.log(`[${app.name}] 准备加载应用`);
      
      // 预检查依赖
      await checkDependencies(['chart.js', 'd3']);
      
      // 初始化分析配置
      await initializeAnalyticsConfig();
    },
    
    afterLoad: async (app) => {
      console.log(`[${app.name}] 应用加载完成`);
      
      // 注册全局分析函数
      window.analytics = app.instance.analytics;
      
      // 发送加载完成事件
      eventBus.emit('analytics:loaded', { appName: app.name });
    },
    
    beforeMount: async (app) => {
      console.log(`[${app.name}] 准备挂载应用`);
      
      // 准备挂载容器
      const container = app.container;
      if (container) {
        container.innerHTML = '';
        container.className = 'analytics-app-container';
      }
      
      // 设置应用主题
      const theme = stateManager.getState('app.theme', 'light');
      app.props.theme = theme;
    },
    
    afterMount: async (app) => {
      console.log(`[${app.name}] 应用挂载完成`);
      
      // 启动数据收集
      app.instance.startDataCollection();
      
      // 注册快捷键
      registerShortcuts(app.instance);
      
      // 发送挂载完成事件
      eventBus.emit('analytics:mounted', { 
        appName: app.name,
        mountTime: Date.now()
      });
    },
    
    beforeUnmount: async (app) => {
      console.log(`[${app.name}] 准备卸载应用`);
      
      // 停止数据收集
      app.instance.stopDataCollection();
      
      // 保存当前状态
      const currentState = app.instance.getState();
      stateManager.setState(`apps.${app.name}.lastState`, currentState);
      
      // 注销快捷键
      unregisterShortcuts();
    },
    
    afterUnmount: async (app) => {
      console.log(`[${app.name}] 应用卸载完成`);
      
      // 清理全局变量
      delete window.analytics;
      
      // 发送卸载完成事件
      eventBus.emit('analytics:unmounted', { appName: app.name });
    },
    
    onError: async (app, error, phase) => {
      console.error(`[${app.name}] 在 ${phase} 阶段发生错误:`, error);
      
      // 错误上报
      errorReporting.captureException(error, {
        app: app.name,
        phase,
        timestamp: Date.now()
      });
      
      // 错误恢复策略
      switch (phase) {
        case LifecyclePhase.LOAD:
          // 加载错误：尝试重新加载
          setTimeout(() => {
            appManager.loadApp(app.name);
          }, 5000);
          break;
          
        case LifecyclePhase.MOUNT:
          // 挂载错误：清理容器并重试
          if (app.container) {
            app.container.innerHTML = '';
          }
          setTimeout(() => {
            appManager.mountApp(app.name);
          }, 3000);
          break;
      }
    }
  }
});
```

## 应用测试

### 单元测试

```typescript
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AppManager } from '@micro-core/core';

describe('AppManager', () => {
  let appManager: AppManager;
  let mockEventBus: any;
  let mockStateManager: any;
  
  beforeEach(() => {
    mockEventBus = {
      emit: vi.fn(),
      on: vi.fn(),
      off: vi.fn()
    };
    
    mockStateManager = {
      getState: vi.fn(),
      setState: vi.fn()
    };
    
    appManager = new AppManager({}, mockEventBus, mockStateManager);
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  it('should register app correctly', () => {
    const appConfig = {
      name: 'test-app',
      entry: '/test-app.js',
      container: '#test-container',
      activeRule: '/test'
    };
    
    appManager.registerApp(appConfig);
    
    const app = appManager.getApp('test-app');
    expect(app).toBeDefined();
    expect(app?.name).toBe('test-app');
    expect(app?.status).toBe(AppStatus.NOT_LOADED);
  });
  
  it('should load app successfully', async () => {
    const appConfig = {
      name: 'test-app',
      entry: '/test-app.js',
      container: '#test-container',
      activeRule: '/test'
    };
    
    appManager.registerApp(appConfig);
    
    // 模拟应用加载
    vi.spyOn(appManager as any, 'loadAppAssets').mockResolvedValue({});
    
    const app = await appManager.loadApp('test-app');
    
    expect(app).toBeDefined();
    expect(app.status).toBe(AppStatus.LOADED);
  });
  
  it('should mount app successfully', async () => {
    const container = document.createElement('div');
    container.id = 'test-container';
    document.body.appendChild(container);
    
    const appConfig = {
      name: 'test-app',
      entry: '/test-app.js',
      container: '#test-container',
      activeRule: '/test'
    };
    
    appManager.registerApp(appConfig);
    
    // 模拟应用加载和挂载
    vi.spyOn(appManager as any, 'loadAppAssets').mockResolvedValue({
      mount: vi.fn().mockResolvedValue({}),
      unmount: vi.fn().mockResolvedValue(undefined)
    });
    
    await appManager.loadApp('test-app');
    await appManager.mountApp('test-app');
    
    const app = appManager.getApp('test-app');
    expect(app?.status).toBe(AppStatus.MOUNTED);
    
    document.body.removeChild(container);
  });
  
  it('should handle app errors gracefully', async () => {
    const appConfig = {
      name: 'error-app',
      entry: '/error-app.js',
      container: '#error-container',
      activeRule: '/error'
    };
    
    appManager.registerApp(appConfig);
    
    // 模拟加载错误
    vi.spyOn(appManager as any, 'loadAppAssets').mockRejectedValue(
      new Error('Load failed')
    );
    
    await expect(appManager.loadApp('error-app')).rejects.toThrow('Load failed');
    
    const app = appManager.getApp('error-app');
    expect(app?.status).toBe(AppStatus.ERROR);
  });
  
  it('should support app communication', async () => {
    const app1Config = {
      name: 'app1',
      entry: '/app1.js',
      container: '#app1-container',
      activeRule: '/app1'
    };
    
    const app2Config = {
      name: 'app2',
      entry: '/app2.js',
      container: '#app2-container',
      activeRule: '/app2'
    };
    
    appManager.registerApp(app1Config);
    appManager.registerApp(app2Config);
    
    const message = { type: 'TEST_MESSAGE', data: 'hello' };
    
    await appManager.sendMessage('app1', 'app2', message);
    
    // 验证消息发送
    expect(mockEventBus.emit).toHaveBeenCalledWith(
      'app:message',
      expect.objectContaining({
        from: 'app1',
        to: 'app2',
        message
      })
    );
  });
});
```

### 集成测试

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { MicroCore } from '@micro-core/core';

describe('AppManager Integration', () => {
  let microCore: MicroCore;
  let appManager: AppManager;
  
  beforeEach(() => {
    microCore = new MicroCore();
    appManager = microCore.getAppManager();
  });
  
  it('should manage multiple apps lifecycle', async () => {
    // 注册多个应用
    appManager.registerApp({
      name: 'app1',
      entry: '/app1.js',
      container: '#app1',
      activeRule: '/app1'
    });
    
    appManager.registerApp({
      name: 'app2',
      entry: '/app2.js',
      container: '#app2',
      activeRule: '/app2'
    });
    
    await microCore.start();
    
    // 验证应用已注册
    const apps = appManager.getApps();
    expect(apps).toHaveLength(2);
    expect(apps.map(app => app.name)).toEqual(['app1', 'app2']);
  });
  
  it('should handle app routing correctly', async () => {
    appManager.registerApp({
      name: 'user-app',
      entry: '/user-app.js',
      container: '#user-app',
      activeRule: '/user'
    });
    
    await microCore.start();
    
    // 模拟路由变化
    history.pushState({}, '', '/user/profile');
    
    // 验证应用激活
    const userApp = appManager.getApp('user-app');
    expect(userApp?.isActive()).toBe(true);
  });
});
```

## 最佳实践

### 应用设计原则

1. **单一职责原则**
   ```typescript
   // ✅ 好的应用设计
   appManager.registerApp({
     name: 'user-management',
     entry: '/user-management.js',
     container: '#user-container',
     activeRule: '/users',
     // 专注于用户管理功能
   });
   
   appManager.registerApp({
     name: 'order-management',
     entry: '/order-management.js',
     container: '#order-container',
     activeRule: '/orders',
     // 专注于订单管理功能
   });
   
   // ❌ 避免的设计
   appManager.registerApp({
     name: 'mega-app',
     entry: '/mega-app.js',
     container: '#mega-container',
     activeRule: ['/', '/users', '/orders', '/products'],
     // 功能过于庞大，违反单一职责
   });
   ```

2. **合理的应用拆分**
   ```typescript
   // ✅ 按业务领域拆分
   const businessApps = [
     { name: 'user-center', activeRule: '/user' },
     { name: 'product-catalog', activeRule: '/products' },
     { name: 'order-system', activeRule: '/orders' },
     { name: 'payment-gateway', activeRule: '/payment' }
   ];
   
   // ✅ 按功能模块拆分
   const functionalApps = [
     { name: 'dashboard', activeRule: '/dashboard' },
     { name: 'analytics', activeRule: '/analytics' },
     { name: 'settings', activeRule: '/settings' },
     { name: 'notifications', activeRule: '/notifications' }
   ];
   ```

3. **性能优化策略**
   ```typescript
   // ✅ 使用预加载提升性能
   const performanceOptimizedConfig = {
     name: 'heavy-app',
     entry: '/heavy-app.js',
     container: '#heavy-container',
     activeRule: '/heavy',
     loader: {
       preload: true,
       cache: true,
       timeout: 15000,
       retries: 3
     },
     // 启用代码分割
     codeSpitting: {
       enabled: true,
       chunks: ['vendor', 'common', 'main']
     }
   };
   
   // ✅ 懒加载非关键应用
   const lazyLoadConfig = {
     name: 'admin-tools',
     entry: '/admin-tools.js',
     container: '#admin-container',
     activeRule: '/admin',
     loader: {
       lazy: true,
       loadOnDemand: true
     }
   };
   ```

### 错误处理和恢复

```typescript
// ✅ 完善的错误处理策略
class AppErrorHandler {
  private retryAttempts: Map<string, number> = new Map();
  private maxRetries = 3;
  
  async handleAppError(app: MicroApp, error: Error, phase: string): Promise<void> {
    console.error(`应用 ${app.name} 在 ${phase} 阶段发生错误:`, error);
    
    // 错误分类处理
    switch (error.name) {
      case 'NetworkError':
        await this.handleNetworkError(app, error);
        break;
      case 'ScriptError':
        await this.handleScriptError(app, error);
        break;
      case 'TimeoutError':
        await this.handleTimeoutError(app, error);
        break;
      default:
        await this.handleGenericError(app, error);
    }
  }
  
  private async handleNetworkError(app: MicroApp, error: Error): Promise<void> {
    const attempts = this.retryAttempts.get(app.name) || 0;
    
    if (attempts < this.maxRetries) {
      this.retryAttempts.set(app.name, attempts + 1);
      
      // 延迟重试
      const delay = Math.pow(2, attempts) * 1000;
      setTimeout(() => {
        appManager.loadApp(app.name);
      }, delay);
      
      console.log(`网络错误，${delay}ms后重试 (${attempts + 1}/${this.maxRetries})`);
    } else {
      // 重试次数用尽，显示错误页面
      this.showErrorPage(app, '网络连接失败，请检查网络设置');
    }
  }
  
  private async handleScriptError(app: MicroApp, error: Error): Promise<void> {
    // 脚本错误通常不可恢复，显示错误信息
    this.showErrorPage(app, '应用加载失败，请刷新页面重试');
    
    // 上报错误
    errorReporting.captureException(error, {
      app: app.name,
      type: 'script_error',
      userAgent: navigator.userAgent
    });
  }
  
  private showErrorPage(app: MicroApp, message: string): void {
    if (app.container) {
      app.container.innerHTML = `
        <div class="app-error-page">
          <h3>应用加载失败</h3>
          <p>${message}</p>
          <button onclick="location.reload()">刷新页面</button>
        </div>
      `;
    }
  }
}

// 使用错误处理器
const errorHandler = new AppErrorHandler();

appManager.on('app:error', (app, error, phase) => {
  errorHandler.handleAppError(app, error, phase);
});
```

### 内存管理和清理

```typescript
// ✅ 内存管理最佳实践
class AppMemoryManager {
  private memoryThreshold = 100 * 1024 * 1024; // 100MB
  private checkInterval = 30000; // 30秒
  private intervalId?: NodeJS.Timeout;
  
  startMonitoring(): void {
    this.intervalId = setInterval(() => {
      this.checkMemoryUsage();
    }, this.checkInterval);
  }
  
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }
  
  private checkMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMemory = memory.usedJSHeapSize;
      
      if (usedMemory > this.memoryThreshold) {
        console.warn(`内存使用过高: ${(usedMemory / 1024 / 1024).toFixed(2)}MB`);
        this.performCleanup();
      }
    }
  }
  
  private async performCleanup(): Promise<void> {
    const apps = appManager.getApps();
    
    // 卸载非活跃应用
    const inactiveApps = apps.filter(app => 
      app.status === AppStatus.MOUNTED && !app.isActive()
    );
    
    for (const app of inactiveApps) {
      console.log(`清理非活跃应用: ${app.name}`);
      await appManager.unmountApp(app.name);
    }
    
    // 清理缓存
    this.clearCache();
    
    // 强制垃圾回收（如果支持）
    if ('gc' in window) {
      (window as any).gc();
    }
  }
  
  private clearCache(): void {
    // 清理应用缓存
    const cacheKeys = Object.keys(localStorage).filter(key => 
      key.startsWith('micro-core-cache-')
    );
    
    cacheKeys.forEach(key => {
      localStorage.removeItem(key);
    });
  }
}

// 启用内存管理
const memoryManager = new AppMemoryManager();
memoryManager.startMonitoring();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  memoryManager.stopMonitoring();
});
```

## 总结

Micro-Core 的应用管理系统提供了：

1. **完整的生命周期管理**: 从注册到卸载的全流程控制
2. **灵活的配置系统**: 支持多种入口类型和激活规则
3. **强大的通信机制**: 应用间消息传递和请求响应
4. **智能的状态管理**: 实时状态监控和事件通知
5. **性能优化支持**: 预加载、缓存、懒加载等策略
6. **完善的错误处理**: 错误恢复和降级策略
7. **内存管理**: 自动清理和资源优化
8. **测试友好**: 完整的单元测试和集成测试支持

通过合理使用应用管理API，可以构建高效、稳定、可维护的微前端应用架构。
