# 沙箱 API (第三部分)

## 常见问题与解决方案 (续)

### 事件冲突 (续)

```typescript
// 在卸载时调用清理函数
function unmountApp() {
  sandbox.execScript(`
    if (typeof window.__cleanupEventListeners === 'function') {
      window.__cleanupEventListeners();
    }
  `);
  
  // 停用沙箱
  sandbox.deactivate();
}
```

### 内存泄漏

**问题**：微应用卸载后，沙箱中的资源没有被正确释放，导致内存泄漏。

**解决方案**：

1. 在卸载时停用沙箱
2. 清理定时器和事件监听器
3. 使用弱引用存储对象

```typescript
// 在卸载时停用沙箱
function unmountApp() {
  // 清理资源
  sandbox.execScript(`
    // 清理定时器
    if (window.__timers) {
      window.__timers.forEach(id => {
        clearTimeout(id);
        clearInterval(id);
      });
    }
    
    // 清理事件监听器
    if (typeof window.__cleanupEventListeners === 'function') {
      window.__cleanupEventListeners();
    }
    
    // 清理 DOM 引用
    if (window.__domReferences) {
      window.__domReferences.forEach(ref => {
        ref.element = null;
      });
    }
  `);
  
  // 停用沙箱
  sandbox.deactivate();
  
  // 手动触发垃圾回收（仅在开发环境）
  if (process.env.NODE_ENV === 'development') {
    if (global.gc) {
      global.gc();
    }
  }
}

// 使用弱引用存储对象
sandbox.execScript(`
  // 创建弱引用映射
  const weakMap = new WeakMap();
  
  // 存储对象引用
  function storeReference(key, object) {
    if (!window.__referenceKeys) {
      window.__referenceKeys = [];
    }
    
    window.__referenceKeys.push(key);
    weakMap.set(key, object);
    
    return object;
  }
  
  // 获取对象引用
  function getReference(key) {
    return weakMap.get(key);
  }
  
  // 清理所有引用
  function cleanupReferences() {
    if (window.__referenceKeys) {
      window.__referenceKeys.length = 0;
    }
  }
`);
```

### 性能问题

**问题**：沙箱隔离导致性能下降。

**解决方案**：

1. 优化沙箱配置
2. 使用共享依赖
3. 实现懒加载

```typescript
// 优化沙箱配置
const sandbox = new Sandbox({
  name: 'app1',
  // 只隔离必要的功能
  isolateGlobalVariables: true,
  isolateWindowEvents: false, // 不隔离事件可以提高性能
  strictIsolation: false, // 非严格模式性能更好
  
  // 使用允许列表而不是拒绝列表
  allowList: [
    'console',
    'setTimeout',
    'clearTimeout',
    'setInterval',
    'clearInterval',
    'requestAnimationFrame',
    'cancelAnimationFrame',
    'fetch',
    'localStorage',
    'sessionStorage'
  ]
});

// 使用共享依赖
const sandbox = new Sandbox({
  name: 'app1',
  customGlobals: {
    // 共享常用库
    React: window.React,
    ReactDOM: window.ReactDOM,
    Vue: window.Vue,
    moment: window.moment,
    lodash: window._
  }
});

// 实现懒加载
function createSandboxOnDemand() {
  let sandboxInstance = null;
  
  return {
    getSandbox() {
      if (!sandboxInstance) {
        sandboxInstance = new Sandbox({
          name: 'app1'
        });
      }
      
      return sandboxInstance;
    },
    
    activate() {
      const sandbox = this.getSandbox();
      sandbox.activate();
      return sandbox;
    },
    
    deactivate() {
      if (sandboxInstance) {
        sandboxInstance.deactivate();
      }
    },
    
    destroy() {
      if (sandboxInstance) {
        sandboxInstance.deactivate();
        sandboxInstance = null;
      }
    }
  };
}

const sandboxManager = createSandboxOnDemand();
```

### 第三方库兼容性

**问题**：某些第三方库在沙箱环境中无法正常工作。

**解决方案**：

1. 使用兼容模式
2. 添加特定库的补丁
3. 将库排除在沙箱之外

```typescript
// 使用兼容模式
const sandbox = new Sandbox({
  name: 'app1',
  compatMode: true
});

// 添加特定库的补丁
sandbox.execScript(`
  // jQuery 补丁
  if (window.jQuery) {
    const originalOn = window.jQuery.fn.on;
    
    window.jQuery.fn.on = function(...args) {
      // 保存事件监听器引用
      if (!window.__jQueryEvents) {
        window.__jQueryEvents = [];
      }
      
      const element = this;
      const [event, selector, data, handler] = args;
      
      window.__jQueryEvents.push({
        element,
        event,
        selector,
        handler
      });
      
      return originalOn.apply(this, args);
    };
    
    // 添加清理方法
    window.__cleanupJQueryEvents = function() {
      if (window.__jQueryEvents) {
        window.__jQueryEvents.forEach(({ element, event, selector, handler }) => {
          element.off(event, selector, handler);
        });
        window.__jQueryEvents.length = 0;
      }
    };
  }
`);

// 将库排除在沙箱之外
const sandbox = new Sandbox({
  name: 'app1',
  excludeLibs: ['jquery', 'lodash', 'moment']
});

// 在沙箱外加载库
function loadExternalLib(url) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = url;
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

// 加载 jQuery
loadExternalLib('https://code.jquery.com/jquery-3.6.0.min.js')
  .then(() => {
    // 将 jQuery 注入沙箱
    sandbox.setGlobalValue('jQuery', window.jQuery);
    sandbox.setGlobalValue('$', window.jQuery);
    
    // 激活沙箱
    sandbox.activate();
  });
```

## 最佳实践

### 沙箱设计模式

#### 工厂模式

```typescript
// 沙箱工厂
class SandboxFactory {
  static createSandbox(type, options) {
    switch (type) {
      case 'iframe':
        return new IframeSandbox(options);
      case 'proxy':
        return new ProxySandbox(options);
      case 'snapshot':
        return new SnapshotSandbox(options);
      case 'legacy':
        return new LegacySandbox(options);
      default:
        return new Sandbox(options);
    }
  }
}

// 创建不同类型的沙箱
const proxySandbox = SandboxFactory.createSandbox('proxy', {
  name: 'app1'
});

const iframeSandbox = SandboxFactory.createSandbox('iframe', {
  name: 'app2',
  url: 'about:blank'
});
```

#### 装饰器模式

```typescript
// 基础沙箱
const baseSandbox = new Sandbox({
  name: 'app1'
});

// 日志装饰器
function withLogging(sandbox) {
  const originalExecScript = sandbox.execScript.bind(sandbox);
  
  sandbox.execScript = function(code, options) {
    console.log(`执行代码，长度: ${code.length}`);
    
    const startTime = Date.now();
    const result = originalExecScript(code, options);
    const endTime = Date.now();
    
    console.log(`代码执行完成，耗时: ${endTime - startTime}ms`);
    
    return result;
  };
  
  return sandbox;
}

// 性能监控装饰器
function withPerformanceMonitoring(sandbox) {
  const metrics = {
    execCount: 0,
    totalExecTime: 0,
    maxExecTime: 0,
    minExecTime: Infinity
  };
  
  const originalExecScript = sandbox.execScript.bind(sandbox);
  
  sandbox.execScript = function(code, options) {
    const startTime = performance.now();
    const result = originalExecScript(code, options);
    const endTime = performance.now();
    
    const execTime = endTime - startTime;
    
    metrics.execCount++;
    metrics.totalExecTime += execTime;
    metrics.maxExecTime = Math.max(metrics.maxExecTime, execTime);
    metrics.minExecTime = Math.min(metrics.minExecTime, execTime);
    
    return result;
  };
  
  sandbox.getMetrics = function() {
    return {
      ...metrics,
      avgExecTime: metrics.execCount > 0 ? metrics.totalExecTime / metrics.execCount : 0
    };
  };
  
  return sandbox;
}

// 应用装饰器
const enhancedSandbox = withPerformanceMonitoring(withLogging(baseSandbox));

// 使用增强的沙箱
enhancedSandbox.activate();
enhancedSandbox.execScript('console.log("Hello from sandbox!");');

// 获取性能指标
const metrics = enhancedSandbox.getMetrics();
console.log('性能指标:', metrics);
```

#### 策略模式

```typescript
// 沙箱策略接口
class SandboxStrategy {
  activate() {}
  deactivate() {}
  execScript(code, options) {}
}

// 代理沙箱策略
class ProxySandboxStrategy extends SandboxStrategy {
  constructor(options) {
    super();
    this.options = options;
    this.proxyWindow = null;
    this.originalWindow = window;
  }
  
  activate() {
    const { name } = this.options;
    
    const fakeWindow = {};
    const proxy = new Proxy(fakeWindow, {
      get(target, prop) {
        if (prop in target) {
          return target[prop];
        }
        
        return window[prop];
      },
      
      set(target, prop, value) {
        target[prop] = value;
        return true;
      }
    });
    
    this.proxyWindow = proxy;
  }
  
  deactivate() {
    this.proxyWindow = null;
  }
  
  execScript(code, options) {
    if (!this.proxyWindow) {
      throw new Error('沙箱未激活');
    }
    
    const fn = new Function('window', 'self', 'globalThis', `
      with (window) {
        ${code}
      }
    `);
    
    return fn(this.proxyWindow, this.proxyWindow, this.proxyWindow);
  }
}

// 快照沙箱策略
class SnapshotSandboxStrategy extends SandboxStrategy {
  constructor(options) {
    super();
    this.options = options;
    this.snapshot = {};
    this.modifiedKeys = new Set();
  }
  
  activate() {
    this.snapshot = {};
    
    // 保存当前 window 状态
    for (const key in window) {
      if (Object.prototype.hasOwnProperty.call(window, key)) {
        this.snapshot[key] = window[key];
      }
    }
  }
  
  deactivate() {
    // 恢复 window 状态
    this.modifiedKeys.forEach(key => {
      if (key in this.snapshot) {
        window[key] = this.snapshot[key];
      } else {
        delete window[key];
      }
    });
    
    this.modifiedKeys.clear();
  }
  
  execScript(code, options) {
    const fn = new Function('window', 'self', 'globalThis', code);
    
    // 记录修改的键
    const rawWindow = window;
    const proxy = new Proxy(window, {
      set: (target, prop, value) => {
        this.modifiedKeys.add(prop);
        target[prop] = value;
        return true;
      }
    });
    
    return fn(proxy, proxy, proxy);
  }
}

// 沙箱上下文
class SandboxContext {
  constructor(strategy) {
    this.strategy = strategy;
  }
  
  setStrategy(strategy) {
    this.strategy = strategy;
  }
  
  activate() {
    return this.strategy.activate();
  }
  
  deactivate() {
    return this.strategy.deactivate();
  }
  
  execScript(code, options) {
    return this.strategy.execScript(code, options);
  }
}

// 使用策略模式
const proxySandboxStrategy = new ProxySandboxStrategy({
  name: 'app1'
});

const snapshotSandboxStrategy = new SnapshotSandboxStrategy({
  name: 'app1'
});

// 创建沙箱上下文
const sandboxContext = new SandboxContext(proxySandboxStrategy);

// 激活沙箱
sandboxContext.activate();

// 执行代码
sandboxContext.execScript('console.log("Hello from proxy sandbox!");');

// 切换策略
sandboxContext.setStrategy(snapshotSandboxStrategy);

// 激活新策略
sandboxContext.activate();

// 执行代码
sandboxContext.execScript('console.log("Hello from snapshot sandbox!");');

// 停用沙箱
sandboxContext.deactivate();
```

### 沙箱安全

#### 安全配置

```typescript
// 安全配置
const sandbox = new Sandbox({
  name: 'app1',
  
  // 严格隔离
  strictIsolation: true,
  
  // 禁用危险 API
  denyList: [
    'eval',
    'Function',
    'setTimeout',
    'setInterval',
    'requestAnimationFrame',
    'fetch',
    'XMLHttpRequest',
    'WebSocket',
    'Worker',
    'parent',
    'top',
    'frames'
  ],
  
  // 禁用 with 语句
  disableWith: true,
  
  // 启用内容安全策略
  csp: "default-src 'self'; script-src 'self'; object-src 'none'; style-src 'self';",
  
  // 错误处理
  errorHandler: (error, code) => {
    console.error('沙箱执行错误:', error);
    reportSecurityViolation(error, code);
  }
});

// 安全执行代码
function safeExecScript(code) {
  try {
    // 静态分析代码
    const dangerousPatterns = [
      /eval\s*\(/g,
      /new\s+Function/g,
      /document\.write/g,
      /\<script/g,
      /javascript:/g
    ];
    
    const hasDangerousPattern = dangerousPatterns.some(pattern => pattern.test(code));
    
    if (hasDangerousPattern) {
      throw new Error('代码包含潜在危险模式');
    }
    
    // 执行代码
    return sandbox.execScript(code);
  } catch (error) {
    console.error('安全执行失败:', error);
    return null;
  }
}
```

#### 权限控制

```typescript
// 权限控制
const sandbox = new Sandbox({
  name: 'app1',
  
  // 自定义全局变量
  customGlobals: {
    // 安全的 fetch 实现
    fetch: (url, options) => {
      // 检查 URL 是否在允许列表中
      const allowedDomains = [
        'api.example.com',
        'cdn.example.com'
      ];
      
      const urlObj = new URL(url, window.location.origin);
      const isDomainAllowed = allowedDomains.some(domain => urlObj.hostname === domain);
      
      if (!isDomainAllowed) {
        return Promise.reject(new Error(`不允许访问域名: ${urlObj.hostname}`));
      }
      
      // 添加安全头
      const secureOptions = {
        ...options,
        headers: {
          ...options?.headers,
          'X-Requested-By': 'sandbox'
        }
      };
      
      // 调用原始 fetch
      return window.fetch(url, secureOptions);
    },
    
    // 安全的 localStorage 实现
    localStorage: {
      getItem: (key) => {
        // 添加前缀
        const prefixedKey = `app1:${key}`;
        return window.localStorage.getItem(prefixedKey);
      },
      
      setItem: (key, value) => {
        // 检查配额
        if (value.length > 1024 * 10) { // 10KB 限制
          throw new Error('存储项太大');
        }
        
        // 添加前缀
        const prefixedKey = `app1:${key}`;
        return window.localStorage.setItem(prefixedKey, value);
      },
      
      removeItem: (key) => {
        const prefixedKey = `app1:${key}`;
        return window.localStorage.removeItem(prefixedKey);
      },
      
      clear: () => {
        // 只清除应用自己的项
        for (let i = 0; i < window.localStorage.length; i++) {
          const key = window.localStorage.key(i);
          if (key.startsWith('app1:')) {
            window.localStorage.removeItem(key);
          }
        }
      }
    }
  }
});
```

#### 内容安全策略

```typescript
// 设置 CSP
function setupCSP(sandbox) {
  // 创建 CSP meta 标签
  const cspMeta = document.createElement('meta');
  cspMeta.httpEquiv = 'Content-Security-Policy';
  cspMeta.content = "default-src 'self'; script-src 'self' 'unsafe-eval'; object-src 'none'; style-src 'self' 'unsafe-inline';";
  
  // 获取沙箱容器
  const container = sandbox.getContainer();
  
  // 添加 CSP meta 标签
  if (container) {
    const head = container.querySelector('head');
    if (head) {
      head.appendChild(cspMeta);
    } else {
      const head = document.createElement('head');
      head.appendChild(cspMeta);
      container.insertBefore(head, container.firstChild);
    }
  }
}

// 创建沙箱
const sandbox = new Sandbox({
  name: 'app1',
  el: '#app1-container'
});

// 激活沙箱
sandbox.activate();

// 设置 CSP
setupCSP(sandbox);
```

### 沙箱测试

```typescript
// 沙箱测试
function testSandbox() {
  // 创建沙箱
  const sandbox = new Sandbox({
    name: 'test-sandbox'
  });
  
  // 激活沙箱
  sandbox.activate();
  
  // 测试全局变量隔离
  sandbox.setGlobalValue('testVar', 'sandbox value');
  
  const sandboxValue = sandbox.getGlobalValue('testVar');
  const globalValue = window.testVar;
  
  console.assert(sandboxValue === 'sandbox value', '沙箱值应该正确设置');
  console.assert(globalValue === undefined, '全局值应该不受影响');
  
  // 测试代码执行
  const result = sandbox.execScript(`
    const a = 1;
    const b = 2;
    return a + b;
  `);
  
  console.assert(result === 3, '代码执行结果应该正确');
  
  // 测试事件隔离
  let eventTriggered = false;
  
  window.addEventListener('test-event', () => {
    eventTriggered = true;
  });
  
  sandbox.execScript(`
    const event = new CustomEvent('test-event');
    window.dispatchEvent(event);
  `);
  
  console.assert(!eventTriggered, '沙箱事件不应该触发全局监听器');
  
  // 测试快照和恢复
  sandbox.setGlobalValue('counter', 0);
  const snapshot = sandbox.snapshot();
  
  sandbox.setGlobalValue('counter', 10);
  console.assert(sandbox.getGlobalValue('counter') === 10, '修改后的值应该为 10');
  
  sandbox.restore(snapshot);
  console.assert(sandbox.getGlobalValue('counter') === 0, '恢复后的值应该为 0');
  
  // 停用沙箱
  sandbox.deactivate();
  
  console.log('沙箱测试完成');
}

// 运行测试
testSandbox();
```

## 总结

Micro-Core 沙箱 API 提供了强大而灵活的沙箱功能，适用于微前端架构中的各种场景。通过本文档，你可以了解沙箱 API 的基本用法、高级特性以及最佳实践，帮助你构建安全、可靠的微前端应用。

主要特性包括：

1. **全局变量隔离**：防止微应用污染全局环境
2. **DOM 隔离**：限制微应用对 DOM 的访问范围
3. **事件隔离**：防止事件冲突和泄漏
4. **样式隔离**：防止 CSS 样式冲突
5. **存储隔离**：隔离 localStorage、sessionStorage 等存储
6. **安全机制**：提供多种安全配置和权限控制选项
7. **性能优化**：支持多种优化策略，提高沙箱性能

通过合理使用这些功能，你可以构建出安全、可靠的微前端应用。