# 路由插件 API

路由插件是 Micro-Core 的核心插件之一，负责管理微前端应用的路由切换、导航控制和路由守卫等功能。

## 📋 目录

- [基础概念](#基础概念)
- [API 参考](#api-参考)
- [配置选项](#配置选项)
- [路由守卫](#路由守卫)
- [动态路由](#动态路由)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 基础概念

### 路由系统架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由系统架构图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   Browser       │    │   Router        │    │   Application   ││
│  │   History       │◄──►│   Plugin        │◄──►│   Manager       ││
│  │                 │    │                 │    │                 ││
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ ││
│  │ │   pushState │ │    │ │   Route     │ │    │ │   App A     │ ││
│  │ │   popState  │ │    │ │   Matcher   │ │    │ │  (React)    │ ││
│  │ │   hashChange│ │    │ │             │ │    │ └─────────────┘ ││
│  │ └─────────────┘ │    │ └─────────────┘ │    │                 ││
│  │                 │    │                 │    │ ┌─────────────┐ ││
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ │   App B     │ ││
│  │ │   Location  │ │    │ │   Guards    │ │    │ │   (Vue)     │ ││
│  │ │   State     │ │    │ │   System    │ │    │ └─────────────┘ ││
│  │ └─────────────┘ │    │ └─────────────┘ │    │                 ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│           │                       │                       │       │
│           └───────────────────────┼───────────────────────┘       │
│                                   │                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Event Bus                                │ │
│  │  route:before-change → route:change → route:after-change   │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## API 参考

### RouterPlugin 类

#### 构造函数

```typescript
constructor(options: RouterPluginOptions)
```

#### 核心方法

##### `push(location: RouteLocation): Promise<void>`

编程式导航到指定路由。

```typescript
// 字符串路径
router.push('/user/123')

// 对象形式
router.push({
  path: '/user',
  params: { id: '123' },
  query: { tab: 'profile' }
})
```

##### `replace(location: RouteLocation): Promise<void>`

替换当前路由，不会在历史记录中留下记录。

##### `beforeEach(guard: NavigationGuard): () => void`

添加全局前置守卫。

```typescript
const removeGuard = router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login')
  } else {
    next()
  }
})
```

## 配置选项

### RouterPluginOptions

```typescript
interface RouterPluginOptions {
  mode?: 'hash' | 'history' | 'memory'
  base?: string
  routes?: RouteRecord[]
  linkActiveClass?: string
  scrollBehavior?: ScrollBehavior
  fallback?: boolean
}
```

## 使用示例

### 基础使用

```typescript
import { RouterPlugin } from '@micro-core/plugin-router'

const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/app/',
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeComponent
    },
    {
      path: '/user/:id',
      name: 'user',
      component: UserComponent,
      props: true
    }
  ]
})

// 注册插件
microCore.use(routerPlugin)
```

### 路由守卫示例

```typescript
// 权限检查
router.beforeEach(async (to, from, next) => {
  if (to.meta.requiresAuth) {
    const user = await getCurrentUser()
    if (!user) {
      next('/login')
      return
    }
  }
  next()
})

// 页面统计
router.afterEach((to, from) => {
  analytics.track('page_view', {
    path: to.path,
    title: to.meta.title
  })
})
```

## 最佳实践

### 1. 路由设计原则

- **层次清晰** - 使用嵌套路由体现页面层次关系
- **语义明确** - 路径应该直观反映页面内容
- **参数合理** - 合理使用路径参数和查询参数
- **向后兼容** - 路由变更时考虑向后兼容性

### 2. 性能优化

```typescript
// 路由懒加载
const routes = [
  {
    path: '/heavy-page',
    component: () => import('./HeavyPage.vue')
  }
]

// 预加载关键路由
router.beforeEach((to, from, next) => {
  if (to.name === 'critical-page') {
    // 预加载相关资源
    preloadCriticalResources()
  }
  next()
})
```

### 3. 错误处理

```typescript
// 全局错误处理
router.onError((error) => {
  console.error('Router error:', error)
  // 上报错误
  reportError(error)
  // 跳转到错误页面
  router.push('/error')
})

// 404 处理
const routes = [
  // ... 其他路由
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: NotFoundComponent
  }
]
```

## 故障排除

### 常见问题

#### 1. 路由不匹配

**问题**：路由配置正确但页面不显示

**解决方案**：
- 检查路由路径是否正确
- 确认组件是否正确导入
- 检查路由模式配置

#### 2. 守卫不执行

**问题**：路由守卫没有按预期执行

**解决方案**：
- 确认守卫注册顺序
- 检查 `next()` 函数调用
- 验证守卫返回值

#### 3. 历史记录问题

**问题**：浏览器前进后退按钮异常

**解决方案**：
- 检查 `mode` 配置
- 确认服务器配置支持 History API
- 验证 `base` 路径设置

### 调试技巧

```typescript
// 开启路由调试
const router = new RouterPlugin({
  // 其他配置...
  debug: process.env.NODE_ENV === 'development'
})

// 监听路由变化
router.afterEach((to, from) => {
  console.log(`Route changed from ${from.path} to ${to.path}`)
})
```

---

更多详细信息请参考 [完整 API 文档](/api/) 或访问 [GitHub 仓库](https://github.com/echo008/micro-core)。
