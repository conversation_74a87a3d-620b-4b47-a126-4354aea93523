# 通信插件 API

通信插件提供了应用间通信的核心功能，支持事件总线、状态共享和直接消息传递等多种通信方式。

## 📋 目录

- [插件概述](#插件概述)
- [安装配置](#安装配置)
- [核心API](#核心api)
- [事件总线](#事件总线)
- [状态管理](#状态管理)
- [直接通信](#直接通信)
- [中间件系统](#中间件系统)
- [调试工具](#调试工具)
- [最佳实践](#最佳实践)

## 插件概述

通信插件是 Micro-Core 生态系统中的核心插件，提供了完整的应用间通信解决方案。

### 🎯 主要特性

- **事件总线**：高性能的发布订阅模式
- **状态共享**：响应式的全局状态管理
- **直接通信**：点对点消息传递
- **中间件支持**：可扩展的消息处理管道
- **类型安全**：完整的 TypeScript 支持
- **调试友好**：丰富的调试和监控工具

## 安装配置

### 安装插件

```bash
npm install @micro-core/plugin-communication
```

### 基础配置

```typescript
import { MicroCore } from '@micro-core/core'
import { CommunicationPlugin } from '@micro-core/plugin-communication'

const microCore = new MicroCore({
  plugins: [
    new CommunicationPlugin({
      // 事件总线配置
      eventBus: {
        maxListeners: 100,
        enableWildcard: true,
        namespace: 'micro-core'
      },
      
      // 状态管理配置
      state: {
        persist: true,
        storage: 'localStorage',
        serializer: 'json'
      },
      
      // 调试配置
      debug: {
        enabled: process.env.NODE_ENV === 'development',
        logLevel: 'info'
      }
    })
  ]
})
```

## 核心API

### CommunicationPlugin

通信插件的主类，提供所有通信功能的入口。

```typescript
class CommunicationPlugin {
  constructor(options?: CommunicationOptions)
  
  // 插件生命周期
  install(microCore: MicroCore): void
  uninstall(): void
  
  // 获取通信实例
  getEventBus(): EventBus
  getStateManager(): StateManager
  getDirectChannel(): DirectChannel
}
```

### CommunicationOptions

```typescript
interface CommunicationOptions {
  eventBus?: EventBusOptions
  state?: StateOptions
  direct?: DirectOptions
  middleware?: MiddlewareOptions
  debug?: DebugOptions
}

interface EventBusOptions {
  maxListeners?: number
  enableWildcard?: boolean
  namespace?: string
  delimiter?: string
}

interface StateOptions {
  persist?: boolean
  storage?: 'localStorage' | 'sessionStorage' | 'memory'
  serializer?: 'json' | 'msgpack'
  prefix?: string
}
```

## 事件总线

事件总线提供了发布订阅模式的通信机制。

### 基础用法

```typescript
// 获取事件总线实例
const eventBus = microCore.getPlugin('communication').getEventBus()

// 订阅事件
eventBus.on('user:login', (data) => {
  console.log('用户登录:', data)
})

// 发布事件
eventBus.emit('user:login', {
  userId: '123',
  username: 'john'
})

// 一次性订阅
eventBus.once('app:ready', () => {
  console.log('应用就绪')
})

// 取消订阅
const unsubscribe = eventBus.on('data:update', handler)
unsubscribe()
```

### 通配符支持

```typescript
// 订阅所有用户相关事件
eventBus.on('user:*', (eventName, data) => {
  console.log(`用户事件: ${eventName}`, data)
})

// 订阅所有事件
eventBus.on('*', (eventName, data) => {
  console.log(`全局事件: ${eventName}`, data)
})
```

### 命名空间

```typescript
// 创建命名空间
const userEvents = eventBus.namespace('user')
const orderEvents = eventBus.namespace('order')

// 在命名空间中发布/订阅
userEvents.on('login', handler)
userEvents.emit('login', data)

orderEvents.on('created', handler)
orderEvents.emit('created', data)
```

### EventBus API

```typescript
interface EventBus {
  // 事件订阅
  on(event: string, listener: Function): () => void
  once(event: string, listener: Function): () => void
  off(event: string, listener?: Function): void
  
  // 事件发布
  emit(event: string, ...args: any[]): boolean
  emitAsync(event: string, ...args: any[]): Promise<any[]>
  
  // 命名空间
  namespace(name: string): EventBus
  
  // 工具方法
  listenerCount(event: string): number
  eventNames(): string[]
  removeAllListeners(event?: string): void
  
  // 中间件
  use(middleware: EventMiddleware): void
}
```

## 状态管理

状态管理器提供了响应式的全局状态共享功能。

### 基础用法

```typescript
// 获取状态管理器
const stateManager = microCore.getPlugin('communication').getStateManager()

// 设置状态
stateManager.set('user', {
  id: '123',
  name: 'John Doe',
  role: 'admin'
})

// 获取状态
const user = stateManager.get('user')

// 监听状态变化
stateManager.watch('user', (newValue, oldValue) => {
  console.log('用户状态变化:', newValue, oldValue)
})

// 批量更新
stateManager.batch(() => {
  stateManager.set('user.name', 'Jane Doe')
  stateManager.set('user.role', 'user')
})
```

### 计算属性

```typescript
// 定义计算属性
stateManager.computed('userDisplayName', () => {
  const user = stateManager.get('user')
  return user ? `${user.name} (${user.role})` : '未登录'
})

// 使用计算属性
const displayName = stateManager.get('userDisplayName')
```

### 状态持久化

```typescript
// 配置持久化
const stateManager = new StateManager({
  persist: {
    enabled: true,
    storage: 'localStorage',
    key: 'micro-core-state',
    include: ['user', 'settings'],
    exclude: ['temp']
  }
})

// 手动保存/恢复
stateManager.save()
stateManager.restore()
```

### StateManager API

```typescript
interface StateManager {
  // 状态操作
  get<T>(path: string): T
  set<T>(path: string, value: T): void
  delete(path: string): void
  has(path: string): boolean
  
  // 监听器
  watch<T>(path: string, callback: WatchCallback<T>): () => void
  unwatch(path: string, callback?: WatchCallback): void
  
  // 计算属性
  computed<T>(name: string, getter: () => T): void
  
  // 批量操作
  batch(fn: () => void): void
  
  // 持久化
  save(): void
  restore(): void
  clear(): void
  
  // 工具方法
  getState(): Record<string, any>
  setState(state: Record<string, any>): void
  reset(): void
}
```

## 直接通信

直接通信提供了应用间点对点的消息传递功能。

### 基础用法

```typescript
// 获取直接通信通道
const directChannel = microCore.getPlugin('communication').getDirectChannel()

// 发送消息到指定应用
directChannel.send('app-b', 'user:data', {
  userId: '123',
  action: 'update'
})

// 接收来自其他应用的消息
directChannel.receive('user:data', (data, fromApp) => {
  console.log(`收到来自 ${fromApp} 的消息:`, data)
})

// 请求-响应模式
const response = await directChannel.request('app-b', 'get:user', {
  userId: '123'
})
```

### 消息路由

```typescript
// 配置消息路由
directChannel.route('user:*', (message, fromApp) => {
  // 路由到用户服务
  return userService.handle(message)
})

// 广播消息
directChannel.broadcast('system:notification', {
  type: 'info',
  message: '系统维护通知'
})
```

### DirectChannel API

```typescript
interface DirectChannel {
  // 消息发送
  send(toApp: string, type: string, data: any): void
  broadcast(type: string, data: any): void
  
  // 消息接收
  receive(type: string, handler: MessageHandler): () => void
  
  // 请求-响应
  request<T>(toApp: string, type: string, data: any): Promise<T>
  respond(type: string, handler: RequestHandler): () => void
  
  // 消息路由
  route(pattern: string, handler: RouteHandler): void
  
  // 连接管理
  connect(appName: string): void
  disconnect(appName: string): void
  isConnected(appName: string): boolean
}
```

## 中间件系统

中间件系统允许你在消息传递过程中插入自定义逻辑。

### 事件中间件

```typescript
// 日志中间件
const loggerMiddleware: EventMiddleware = (context, next) => {
  console.log(`事件: ${context.event}`, context.args)
  return next()
}

// 权限检查中间件
const authMiddleware: EventMiddleware = (context, next) => {
  if (context.event.startsWith('admin:') && !isAdmin()) {
    throw new Error('权限不足')
  }
  return next()
}

// 使用中间件
eventBus.use(loggerMiddleware)
eventBus.use(authMiddleware)
```

### 状态中间件

```typescript
// 状态变更日志
const stateLoggerMiddleware: StateMiddleware = (context, next) => {
  console.log(`状态变更: ${context.path}`, {
    oldValue: context.oldValue,
    newValue: context.newValue
  })
  return next()
}

// 状态验证
const stateValidatorMiddleware: StateMiddleware = (context, next) => {
  if (context.path === 'user' && !validateUser(context.newValue)) {
    throw new Error('用户数据格式错误')
  }
  return next()
}

stateManager.use(stateLoggerMiddleware)
stateManager.use(stateValidatorMiddleware)
```

### 中间件接口

```typescript
interface EventMiddleware {
  (context: EventContext, next: () => any): any
}

interface StateMiddleware {
  (context: StateContext, next: () => any): any
}

interface EventContext {
  event: string
  args: any[]
  timestamp: number
  source: string
}

interface StateContext {
  path: string
  oldValue: any
  newValue: any
  timestamp: number
  source: string
}
```

## 调试工具

通信插件提供了丰富的调试和监控工具。

### 调试面板

```typescript
// 启用调试面板
const communicationPlugin = new CommunicationPlugin({
  debug: {
    enabled: true,
    panel: true,
    position: 'bottom-right'
  }
})
```

### 事件监控

```typescript
// 监控所有事件
eventBus.monitor((event, args, timestamp) => {
  console.log(`[${timestamp}] 事件: ${event}`, args)
})

// 性能监控
eventBus.performance((event, duration) => {
  if (duration > 100) {
    console.warn(`慢事件: ${event} 耗时 ${duration}ms`)
  }
})
```

### 状态快照

```typescript
// 创建状态快照
const snapshot = stateManager.snapshot()

// 恢复状态快照
stateManager.restore(snapshot)

// 状态历史记录
const history = stateManager.getHistory()
```

## 最佳实践

### 1. 事件命名规范

```typescript
// ✅ 推荐的事件命名
'user:login'          // 用户登录
'user:logout'         // 用户登出
'order:created'       // 订单创建
'product:updated'     // 产品更新
'system:error'        // 系统错误

// ❌ 不推荐的命名
'userLogin'           // 缺少命名空间
'user_login'          // 使用下划线
'USER:LOGIN'          // 全大写
```

### 2. 状态结构设计

```typescript
// ✅ 推荐的状态结构
const state = {
  user: {
    profile: { id: '123', name: 'John' },
    preferences: { theme: 'dark', language: 'zh' },
    permissions: ['read', 'write']
  },
  app: {
    loading: false,
    error: null,
    version: '1.0.0'
  }
}

// ❌ 扁平化结构（不推荐）
const state = {
  userId: '123',
  userName: 'John',
  userTheme: 'dark',
  appLoading: false
}
```

### 3. 错误处理

```typescript
// 事件错误处理
eventBus.on('error', (error, event, args) => {
  console.error(`事件 ${event} 处理失败:`, error)
  // 发送错误报告
  errorReporter.report(error, { event, args })
})

// 状态错误处理
stateManager.onError((error, path, value) => {
  console.error(`状态 ${path} 设置失败:`, error)
  // 回滚状态
  stateManager.rollback()
})
```

### 4. 性能优化

```typescript
// 批量状态更新
stateManager.batch(() => {
  stateManager.set('user.name', 'John')
  stateManager.set('user.age', 30)
  stateManager.set('user.email', '<EMAIL>')
})

// 事件防抖
const debouncedEmit = debounce((data) => {
  eventBus.emit('search:query', data)
}, 300)

// 内存清理
const unsubscribe = eventBus.on('data:update', handler)
// 在组件卸载时清理
onUnmount(() => {
  unsubscribe()
})
```

### 5. 类型安全

```typescript
// 定义事件类型
interface EventMap {
  'user:login': { userId: string; timestamp: number }
  'user:logout': { userId: string }
  'order:created': { orderId: string; amount: number }
}

// 类型安全的事件总线
const typedEventBus = eventBus as TypedEventBus<EventMap>

// 类型检查
typedEventBus.emit('user:login', {
  userId: '123',
  timestamp: Date.now()
})
```

## 相关链接

- [核心 API 文档](/api/core)
- [应用间通信指南](/guide/features/communication)
- [状态管理指南](/guide/features/state-management)
- [插件开发指南](/guide/advanced/plugins)
- [调试工具使用](/guide/debugging)

---

*最后更新: 2024-07-27*