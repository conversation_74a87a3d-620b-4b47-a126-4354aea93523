# 路由插件 API

路由插件提供统一的路由管理和协调功能，支持多个微应用的路由切换和状态管理。

## 📋 目录

- [插件概述](#插件概述)
- [安装和配置](#安装和配置)
- [API 参考](#api-参考)
- [路由配置](#路由配置)
- [路由守卫](#路由守卫)
- [使用示例](#使用示例)

## 插件概述

### 🎯 主要功能

- **🧭 统一路由管理**: 协调主应用和微应用的路由
- **🔄 动态路由**: 支持运行时动态添加和删除路由
- **🛡️ 路由守卫**: 提供全局和局部路由守卫
- **💾 路由缓存**: 智能缓存路由状态和组件
- **🎨 切换动画**: 支持路由切换动画效果

### 🏗️ 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由插件架构                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    路由协调器                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 路由匹配器   │  │ 路由守卫     │  │ 路由缓存管理器       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    应用路由管理                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 主应用路由   │  │ 微应用A路由  │  │ 微应用B路由          │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    浏览器路由                               │ │
│  │  • History API    • Hash 路由    • Memory 路由            │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装和配置

### 基础安装

```typescript
import { RouterPlugin } from '@micro-core/plugin-router';
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore();

// 创建路由插件实例
const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/',
  
  // 路由配置
  routes: [
    {
      path: '/home',
      name: 'home',
      component: 'main-app'
    },
    {
      path: '/user/*',
      name: 'user',
      component: 'user-app'
    }
  ]
});

// 注册插件
microCore.use(routerPlugin);
```

### 完整配置

```typescript
const routerPlugin = new RouterPlugin({
  // 路由模式
  mode: 'history', // 'history' | 'hash' | 'memory'
  
  // 基础路径
  base: '/',
  
  // 路由配置
  routes: [
    {
      path: '/dashboard',
      name: 'dashboard',
      component: 'dashboard-app',
      meta: {
        requiresAuth: true,
        title: '仪表板'
      }
    }
  ],
  
  // 路由守卫
  beforeEach: (to, from, next) => {
    console.log(`导航到: ${to.path}`);
    next();
  },
  
  afterEach: (to, from) => {
    console.log(`导航完成: ${to.path}`);
  },
  
  // 路由缓存
  cache: {
    enabled: true,
    maxSize: 10,
    ttl: 300000 // 5分钟
  },
  
  // 切换动画
  transition: {
    name: 'fade',
    duration: 300
  },
  
  // 错误处理
  onError: (error) => {
    console.error('路由错误:', error);
  }
});
```

## API 参考

### RouterPlugin 类

#### 构造函数

```typescript
constructor(config: RouterPluginConfig)
```

创建路由插件实例。

**参数:**
- `config: RouterPluginConfig` - 路由插件配置

#### 方法

##### navigate()

```typescript
navigate(path: string, options?: NavigateOptions): Promise<void>
```

导航到指定路径。

**参数:**
- `path: string` - 目标路径
- `options?: NavigateOptions` - 导航选项

**示例:**
```typescript
// 基础导航
await router.navigate('/dashboard');

// 带参数导航
await router.navigate('/user/123', {
  replace: true,
  state: { from: 'home' }
});
```

##### push()

```typescript
push(path: string, state?: any): Promise<void>
```

推入新的路由记录。

**参数:**
- `path: string` - 路径
- `state?: any` - 状态数据

##### replace()

```typescript
replace(path: string, state?: any): Promise<void>
```

替换当前路由记录。

##### go()

```typescript
go(delta: number): void
```

在历史记录中前进或后退。

**参数:**
- `delta: number` - 步数，正数前进，负数后退

##### back()

```typescript
back(): void
```

后退一步。

##### forward()

```typescript
forward(): void
```

前进一步。

##### getCurrentRoute()

```typescript
getCurrentRoute(): RouteInfo
```

获取当前路由信息。

**返回值:**
```typescript
interface RouteInfo {
  path: string;
  name?: string;
  params: Record<string, string>;
  query: Record<string, string>;
  hash: string;
  meta?: Record<string, any>;
}
```

##### addRoute()

```typescript
addRoute(route: RouteConfig): void
```

动态添加路由。

**参数:**
- `route: RouteConfig` - 路由配置

##### removeRoute()

```typescript
removeRoute(name: string): void
```

移除指定路由。

**参数:**
- `name: string` - 路由名称

##### hasRoute()

```typescript
hasRoute(name: string): boolean
```

检查路由是否存在。

##### getRoutes()

```typescript
getRoutes(): RouteConfig[]
```

获取所有路由配置。

##### beforeEach()

```typescript
beforeEach(guard: NavigationGuard): () => void
```

添加全局前置守卫。

**参数:**
- `guard: NavigationGuard` - 守卫函数

**返回值:**
- `() => void` - 取消守卫的函数

##### afterEach()

```typescript
afterEach(hook: NavigationHook): () => void
```

添加全局后置钩子。

##### onError()

```typescript
onError(handler: (error: Error) => void): () => void
```

添加错误处理器。

## 路由配置

### RouteConfig 接口

```typescript
interface RouteConfig {
  /** 路由路径 */
  path: string;
  
  /** 路由名称 */
  name?: string;
  
  /** 关联的微应用 */
  component: string;
  
  /** 路由元信息 */
  meta?: Record<string, any>;
  
  /** 子路由 */
  children?: RouteConfig[];
  
  /** 路由别名 */
  alias?: string | string[];
  
  /** 重定向 */
  redirect?: string | ((to: RouteInfo) => string);
  
  /** 路由参数 */
  props?: boolean | Record<string, any> | ((route: RouteInfo) => Record<string, any>);
  
  /** 路由守卫 */
  beforeEnter?: NavigationGuard;
}
```

### 路径匹配

```typescript
// 静态路径
{
  path: '/home',
  component: 'home-app'
}

// 动态参数
{
  path: '/user/:id',
  component: 'user-app'
}

// 通配符匹配
{
  path: '/admin/*',
  component: 'admin-app'
}

// 可选参数
{
  path: '/product/:id?',
  component: 'product-app'
}

// 正则匹配
{
  path: '/order/:id(\\d+)',
  component: 'order-app'
}
```

### 嵌套路由

```typescript
{
  path: '/dashboard',
  component: 'dashboard-app',
  children: [
    {
      path: 'analytics',
      component: 'analytics-app'
    },
    {
      path: 'settings',
      component: 'settings-app'
    }
  ]
}
```

## 路由守卫

### 守卫类型

#### 全局前置守卫

```typescript
router.beforeEach((to, from, next) => {
  // 权限检查
  if (to.meta?.requiresAuth && !isAuthenticated()) {
    next('/login');
    return;
  }
  
  // 加载检查
  if (to.meta?.requiresData) {
    loadData().then(() => next());
    return;
  }
  
  next();
});
```

#### 全局后置钩子

```typescript
router.afterEach((to, from) => {
  // 页面标题
  if (to.meta?.title) {
    document.title = to.meta.title;
  }
  
  // 统计上报
  analytics.track('page_view', {
    path: to.path,
    name: to.name
  });
});
```

#### 路由独享守卫

```typescript
{
  path: '/admin',
  component: 'admin-app',
  beforeEnter: (to, from, next) => {
    if (!hasAdminPermission()) {
      next('/403');
      return;
    }
    next();
  }
}
```

### NavigationGuard 接口

```typescript
type NavigationGuard = (
  to: RouteInfo,
  from: RouteInfo,
  next: NavigationGuardNext
) => void | Promise<void>;

type NavigationGuardNext = (
  to?: string | false | Error | RouteInfo
) => void;
```

### 守卫执行顺序

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由守卫执行流程                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  1. 导航被触发                                                   │
│  │                                                              │
│  ▼                                                              │
│  2. 在失活的组件里调用 beforeRouteLeave 守卫                     │
│  │                                                              │
│  ▼                                                              │
│  3. 调用全局的 beforeEach 守卫                                   │
│  │                                                              │
│  ▼                                                              │
│  4. 在重用的组件里调用 beforeRouteUpdate 守卫                    │
│  │                                                              │
│  ▼                                                              │
│  5. 在路由配置里调用 beforeEnter                                 │
│  │                                                              │
│  ▼                                                              │
│  6. 解析异步路由组件                                             │
│  │                                                              │
│  ▼                                                              │
│  7. 在被激活的组件里调用 beforeRouteEnter                        │
│  │                                                              │
│  ▼                                                              │
│  8. 调用全局的 resolveGuards                                     │
│  │                                                              │
│  ▼                                                              │
│  9. 导航被确认                                                   │
│  │                                                              │
│  ▼                                                              │
│  10. 调用全局的 afterEach 钩子                                   │
│  │                                                              │
│  ▼                                                              │
│  11. 触发 DOM 更新                                               │
│  │                                                              │
│  ▼                                                              │
│  12. 调用 beforeRouteEnter 守卫中传给 next 的回调函数            │
└─────────────────────────────────────────────────────────────────┘
```

## 使用示例

### 基础路由配置

```typescript
import { RouterPlugin } from '@micro-core/plugin-router';
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore();

const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/',
  routes: [
    {
      path: '/',
      name: 'home',
      component: 'main-app',
      meta: { title: '首页' }
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: 'dashboard-app',
      meta: { 
        title: '仪表板',
        requiresAuth: true 
      }
    },
    {
      path: '/user/:id',
      name: 'user',
      component: 'user-app',
      meta: { title: '用户详情' }
    }
  ]
});

microCore.use(routerPlugin);
```

### 动态路由管理

```typescript
// 获取路由插件实例
const router = microCore.getPlugin('router-plugin');

// 动态添加路由
router.addRoute({
  path: '/admin',
  name: 'admin',
  component: 'admin-app',
  meta: { requiresAdmin: true }
});

// 动态移除路由
router.removeRoute('admin');

// 检查路由是否存在
if (router.hasRoute('dashboard')) {
  console.log('仪表板路由存在');
}

// 获取所有路由
const routes = router.getRoutes();
console.log('所有路由:', routes);
```

### 编程式导航

```typescript
// 基础导航
await router.navigate('/dashboard');

// 带参数导航
await router.navigate('/user/123');

// 带查询参数导航
await router.navigate('/search?q=micro-core&type=docs');

// 替换当前路由
await router.replace('/login');

// 历史记录操作
router.go(-1);  // 后退一步
router.back();  // 后退
router.forward(); // 前进
```

### 路由信息获取

```typescript
// 获取当前路由信息
const currentRoute = router.getCurrentRoute();
console.log('当前路由:', currentRoute);

// 监听路由变化
router.beforeEach((to, from, next) => {
  console.log(`从 ${from.path} 导航到 ${to.path}`);
  next();
});

router.afterEach((to, from) => {
  console.log(`导航完成: ${to.path}`);
});
```

### 权限控制示例

```typescript
const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/',
  
  // 全局前置守卫
  beforeEach: async (to, from, next) => {
    // 检查是否需要认证
    if (to.meta?.requiresAuth) {
      const isAuthenticated = await checkAuth();
      if (!isAuthenticated) {
        next('/login');
        return;
      }
    }
    
    // 检查管理员权限
    if (to.meta?.requiresAdmin) {
      const isAdmin = await checkAdminPermission();
      if (!isAdmin) {
        next('/403');
        return;
      }
    }
    
    // 加载必要数据
    if (to.meta?.preload) {
      try {
        await loadRouteData(to.meta.preload);
      } catch (error) {
        console.error('预加载数据失败:', error);
        next('/error');
        return;
      }
    }
    
    next();
  },
  
  // 全局后置钩子
  afterEach: (to, from) => {
    // 更新页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - Micro-Core`;
    }
    
    // 统计页面访问
    analytics.track('page_view', {
      path: to.path,
      name: to.name,
      referrer: from.path
    });
    
    // 滚动到顶部
    if (to.meta?.scrollToTop !== false) {
      window.scrollTo(0, 0);
    }
  }
});
```

### 路由缓存配置

```typescript
const routerPlugin = new RouterPlugin({
  // 路由缓存配置
  cache: {
    enabled: true,
    maxSize: 20,
    ttl: 600000, // 10分钟
    
    // 缓存策略
    strategy: 'lru', // 'lru' | 'fifo' | 'custom'
    
    // 自定义缓存键
    keyGenerator: (route) => {
      return `${route.name}-${JSON.stringify(route.params)}`;
    },
    
    // 缓存过滤器
    filter: (route) => {
      // 不缓存需要实时数据的页面
      return !route.meta?.noCache;
    }
  }
});
```

### 路由切换动画

```typescript
const routerPlugin = new RouterPlugin({
  // 切换动画配置
  transition: {
    name: 'slide',
    duration: 300,
    
    // 自定义动画
    enter: (element, done) => {
      element.style.opacity = '0';
      element.style.transform = 'translateX(100px)';
      
      element.animate([
        { opacity: 0, transform: 'translateX(100px)' },
        { opacity: 1, transform: 'translateX(0)' }
      ], {
        duration: 300,
        easing: 'ease-out'
      }).onfinish = done;
    },
    
    leave: (element, done) => {
      element.animate([
        { opacity: 1, transform: 'translateX(0)' },
        { opacity: 0, transform: 'translateX(-100px)' }
      ], {
        duration: 300,
        easing: 'ease-in'
      }).onfinish = done;
    }
  }
});
```

### 错误处理

```typescript
const routerPlugin = new RouterPlugin({
  // 错误处理
  onError: (error) => {
    console.error('路由错误:', error);
    
    // 根据错误类型处理
    if (error.name === 'NavigationDuplicated') {
      // 重复导航错误，可以忽略
      return;
    }
    
    if (error.name === 'NavigationAborted') {
      // 导航被中止
      console.warn('导航被中止:', error.message);
      return;
    }
    
    if (error.name === 'NavigationCancelled') {
      // 导航被取消
      console.warn('导航被取消:', error.message);
      return;
    }
    
    // 其他错误，显示错误页面
    router.replace('/error');
  }
});

// 监听路由错误
router.onError((error) => {
  // 发送错误报告
  errorReporting.captureException(error);
  
  // 显示用户友好的错误信息
  showErrorMessage('页面加载失败，请稍后重试');
});
```

## 类型定义

### RouterPluginConfig

```typescript
interface RouterPluginConfig {
  /** 路由模式 */
  mode?: 'history' | 'hash' | 'memory';
  
  /** 基础路径 */
  base?: string;
  
  /** 路由配置 */
  routes?: RouteConfig[];
  
  /** 全局前置守卫 */
  beforeEach?: NavigationGuard;
  
  /** 全局后置钩子 */
  afterEach?: NavigationHook;
  
  /** 路由缓存配置 */
  cache?: CacheConfig;
  
  /** 切换动画配置 */
  transition?: TransitionConfig;
  
  /** 错误处理器 */
  onError?: (error: Error) => void;
}
```

### NavigateOptions

```typescript
interface NavigateOptions {
  /** 是否替换当前记录 */
  replace?: boolean;
  
  /** 状态数据 */
  state?: any;
  
  /** 是否静默导航 */
  silent?: boolean;
  
  /** 导航完成回调 */
  onComplete?: () => void;
  
  /** 导航失败回调 */
  onAbort?: (error: Error) => void;
}
```

### CacheConfig

```typescript
interface CacheConfig {
  /** 是否启用缓存 */
  enabled: boolean;
  
  /** 最大缓存数量 */
  maxSize?: number;
  
  /** 缓存过期时间 */
  ttl?: number;
  
  /** 缓存策略 */
  strategy?: 'lru' | 'fifo' | 'custom';
  
  /** 缓存键生成器 */
  keyGenerator?: (route: RouteInfo) => string;
  
  /** 缓存过滤器 */
  filter?: (route: RouteInfo) => boolean;
}
```

### TransitionConfig

```typescript
interface TransitionConfig {
  /** 动画名称 */
  name?: string;
  
  /** 动画持续时间 */
  duration?: number;
  
  /** 进入动画 */
  enter?: (element: HTMLElement, done: () => void) => void;
  
  /** 离开动画 */
  leave?: (element: HTMLElement, done: () => void) => void;
}
```

## 相关链接

- [路由系统指南](/guide/features/routing)
- [路由系统 API](/api/routing)
- [插件开发指南](/guide/advanced/plugins)
- [路由示例](/examples/routing)

---

路由插件是 Micro-Core 的核心插件之一，提供了强大而灵活的路由管理能力，让你可以轻松构建复杂的微前端应用。
