# 认证插件 API

认证插件是 Micro-Core 的核心插件之一，提供了完整的身份认证和授权解决方案，支持多种认证方式和权限管理策略。

## 📋 目录

- [基础概念](#基础概念)
- [API 参考](#api-参考)
- [认证方式](#认证方式)
- [权限管理](#权限管理)
- [会话管理](#会话管理)
- [配置选项](#配置选项)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 基础概念

### 认证系统架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    认证系统架构图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   Client App    │    │   Auth Plugin   │    │   Auth Server   ││
│  │                 │◄──►│                 │◄──►│                 ││
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ ││
│  │ │   Login     │ │    │ │   Token     │ │    │ │   User      │ ││
│  │ │   Form      │ │    │ │   Manager   │ │    │ │   Store     │ ││
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ ││
│  │                 │    │                 │    │                 ││
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ ││
│  │ │   Protected │ │    │ │   Permission│ │    │ │   OAuth     │ ││
│  │ │   Routes    │ │    │ │   Manager   │ │    │ │   Provider  │ ││
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│           │                       │                       │       │
│           └───────────────────────┼───────────────────────┘       │
│                                   │                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                  Security Layer                             │ │
│  │  JWT Tokens ←→ Session Store ←→ CSRF Protection ←→ HTTPS   │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 认证流程

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    认证流程时序图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  User    Client    Auth Plugin    Auth Server    Protected App  │
│   │        │           │              │              │          │
│   │ Login  │           │              │              │          │
│   ├────────►           │              │              │          │
│   │        │ authenticate              │              │          │
│   │        ├───────────►              │              │          │
│   │        │           │ validate     │              │          │
│   │        │           ├──────────────►              │          │
│   │        │           │              │ user info    │          │
│   │        │           │◄─────────────┤              │          │
│   │        │ token     │              │              │          │
│   │        │◄──────────┤              │              │          │
│   │ success│           │              │              │          │
│   │◄───────┤           │              │              │          │
│   │        │           │              │              │          │
│   │ Access │           │              │              │          │
│   ├────────►           │              │              │          │
│   │        │ authorize │              │              │          │
│   │        ├───────────►              │              │          │
│   │        │           │ verify token │              │          │
│   │        │           ├──────────────►              │          │
│   │        │           │              │ valid        │          │
│   │        │           │◄─────────────┤              │          │
│   │        │           │              │              │ access   │
│   │        │           │              │              ├──────────►
│   │        │           │              │              │          │
│   │        │           │              │              │ response │
│   │        │           │              │              │◄─────────┤
│   │        │ allowed   │              │              │          │
│   │        │◄──────────┤              │              │          │
│   │ content│           │              │              │          │
│   │◄───────┤           │              │              │          │
└─────────────────────────────────────────────────────────────────┘
```

## API 参考

### AuthPlugin 类

#### 构造函数

```typescript
constructor(options: AuthPluginOptions)
```

#### 核心方法

##### `login(credentials: LoginCredentials): Promise<AuthResult>`

用户登录认证。

```typescript
// 用户名密码登录
const result = await auth.login({
  username: '<EMAIL>',
  password: 'password123'
})

// 第三方登录
const result = await auth.login({
  provider: 'google',
  token: 'google-oauth-token'
})
```

##### `logout(): Promise<void>`

用户登出。

```typescript
await auth.logout()
```

##### `getCurrentUser(): Promise<User | null>`

获取当前登录用户信息。

```typescript
const user = await auth.getCurrentUser()
if (user) {
  console.log('Current user:', user.name)
}
```

##### `isAuthenticated(): boolean`

检查用户是否已认证。

```typescript
if (auth.isAuthenticated()) {
  // 用户已登录
  showUserDashboard()
} else {
  // 用户未登录
  showLoginForm()
}
```

##### `hasPermission(permission: string): boolean`

检查用户是否具有指定权限。

```typescript
if (auth.hasPermission('admin:users:read')) {
  // 用户有权限查看用户列表
  showUserList()
}
```

##### `hasRole(role: string): boolean`

检查用户是否具有指定角色。

```typescript
if (auth.hasRole('admin')) {
  // 用户是管理员
  showAdminPanel()
}
```

##### `refreshToken(): Promise<string>`

刷新访问令牌。

```typescript
try {
  const newToken = await auth.refreshToken()
  console.log('Token refreshed successfully')
} catch (error) {
  console.error('Token refresh failed:', error)
  // 重定向到登录页面
  redirectToLogin()
}
```

##### `onAuthStateChange(callback: AuthStateChangeCallback): () => void`

监听认证状态变化。

```typescript
const unsubscribe = auth.onAuthStateChange((user, isAuthenticated) => {
  if (isAuthenticated) {
    console.log('User logged in:', user)
    updateUIForLoggedInUser(user)
  } else {
    console.log('User logged out')
    updateUIForLoggedOutUser()
  }
})

// 取消监听
unsubscribe()
```

### 类型定义

#### LoginCredentials

```typescript
interface LoginCredentials {
  // 用户名密码登录
  username?: string
  password?: string
  
  // 第三方登录
  provider?: 'google' | 'github' | 'facebook' | 'wechat'
  token?: string
  code?: string
  
  // 其他选项
  rememberMe?: boolean
  captcha?: string
}
```

#### AuthResult

```typescript
interface AuthResult {
  success: boolean
  user?: User
  token?: string
  refreshToken?: string
  expiresIn?: number
  error?: string
}
```

#### User

```typescript
interface User {
  id: string
  username: string
  email: string
  name: string
  avatar?: string
  roles: string[]
  permissions: string[]
  profile?: Record<string, any>
  createdAt: Date
  lastLoginAt?: Date
}
```

#### AuthStateChangeCallback

```typescript
type AuthStateChangeCallback = (
  user: User | null,
  isAuthenticated: boolean
) => void
```

## 认证方式

### 1. 用户名密码认证

```typescript
const auth = new AuthPlugin({
  providers: {
    local: {
      enabled: true,
      loginUrl: '/api/auth/login',
      logoutUrl: '/api/auth/logout',
      userInfoUrl: '/api/auth/me',
      validation: {
        username: {
          required: true,
          minLength: 3,
          pattern: /^[a-zA-Z0-9_]+$/
        },
        password: {
          required: true,
          minLength: 8,
          pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/
        }
      }
    }
  }
})

// 登录
const result = await auth.login({
  username: '<EMAIL>',
  password: 'SecurePassword123!'
})
```

### 2. JWT Token 认证

```typescript
const auth = new AuthPlugin({
  token: {
    type: 'jwt',
    storage: 'localStorage', // 'localStorage' | 'sessionStorage' | 'cookie'
    key: 'auth_token',
    autoRefresh: true,
    refreshThreshold: 300, // 5分钟前自动刷新
    header: 'Authorization',
    prefix: 'Bearer '
  }
})

// 自动在请求头中添加 token
fetch('/api/protected', {
  headers: auth.getAuthHeaders()
})
```

### 3. OAuth 第三方认证

```typescript
const auth = new AuthPlugin({
  providers: {
    google: {
      enabled: true,
      clientId: 'your-google-client-id',
      redirectUri: 'http://localhost:3000/auth/callback',
      scope: 'openid profile email'
    },
    github: {
      enabled: true,
      clientId: 'your-github-client-id',
      redirectUri: 'http://localhost:3000/auth/callback'
    }
  }
})

// Google 登录
const result = await auth.login({
  provider: 'google'
})

// GitHub 登录
const result = await auth.login({
  provider: 'github'
})
```

### 4. 单点登录 (SSO)

```typescript
const auth = new AuthPlugin({
  sso: {
    enabled: true,
    provider: 'saml',
    entityId: 'your-app-entity-id',
    ssoUrl: 'https://sso.company.com/saml/login',
    sloUrl: 'https://sso.company.com/saml/logout',
    certificate: 'your-sso-certificate'
  }
})

// SSO 登录
const result = await auth.login({
  provider: 'sso'
})
```

## 权限管理

### RBAC (基于角色的访问控制)

```typescript
const auth = new AuthPlugin({
  rbac: {
    enabled: true,
    roles: {
      admin: {
        name: '管理员',
        permissions: ['*'] // 所有权限
      },
      editor: {
        name: '编辑者',
        permissions: [
          'content:read',
          'content:write',
          'content:update'
        ]
      },
      viewer: {
        name: '查看者',
        permissions: ['content:read']
      }
    },
    permissions: {
      'content:read': '查看内容',
      'content:write': '创建内容',
      'content:update': '更新内容',
      'content:delete': '删除内容',
      'user:manage': '管理用户'
    }
  }
})

// 权限检查
if (auth.hasPermission('content:write')) {
  showCreateButton()
}

if (auth.hasRole('admin')) {
  showAdminMenu()
}
```

### 动态权限

```typescript
// 基于资源的权限检查
const canEditPost = await auth.hasPermission('post:edit', {
  resourceId: 'post-123',
  ownerId: 'user-456'
})

// 自定义权限验证器
auth.addPermissionValidator('post:edit', async (user, resource) => {
  // 作者可以编辑自己的文章
  if (resource.ownerId === user.id) {
    return true
  }
  
  // 管理员可以编辑所有文章
  if (user.roles.includes('admin')) {
    return true
  }
  
  // 编辑者可以编辑已发布的文章
  if (user.roles.includes('editor') && resource.status === 'published') {
    return true
  }
  
  return false
})
```

### 权限守卫

```typescript
// 路由级权限守卫
router.beforeEach(async (to, from, next) => {
  const requiredPermissions = to.meta.permissions || []
  const requiredRoles = to.meta.roles || []
  
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission =>
      auth.hasPermission(permission)
    )
    
    if (!hasAllPermissions) {
      next('/403')
      return
    }
  }
  
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role =>
      auth.hasRole(role)
    )
    
    if (!hasRequiredRole) {
      next('/403')
      return
    }
  }
  
  next()
})

// 组件级权限守卫
const ProtectedComponent = {
  beforeCreate() {
    if (!auth.hasPermission('admin:access')) {
      this.$router.push('/403')
    }
  }
}
```

## 会话管理

### 会话配置

```typescript
const auth = new AuthPlugin({
  session: {
    timeout: 30 * 60 * 1000, // 30分钟
    warningTime: 5 * 60 * 1000, // 5分钟前警告
    autoExtend: true, // 用户活动时自动延长会话
    storage: 'sessionStorage',
    key: 'user_session'
  }
})
```

### 会话监控

```typescript
// 监听会话状态
auth.onSessionStateChange((state) => {
  switch (state) {
    case 'active':
      console.log('Session is active')
      break
    case 'warning':
      console.log('Session will expire soon')
      showSessionWarning()
      break
    case 'expired':
      console.log('Session has expired')
      redirectToLogin()
      break
  }
})

// 手动延长会话
auth.extendSession()

// 获取会话信息
const sessionInfo = auth.getSessionInfo()
console.log('Session expires at:', sessionInfo.expiresAt)
```

### 多标签页同步

```typescript
const auth = new AuthPlugin({
  session: {
    syncAcrossTabs: true, // 多标签页会话同步
    broadcastChannel: 'auth_sync'
  }
})

// 监听其他标签页的登录/登出事件
auth.onCrossTabAuthChange((event) => {
  if (event.type === 'login') {
    console.log('User logged in from another tab')
    updateUIForLoggedInUser(event.user)
  } else if (event.type === 'logout') {
    console.log('User logged out from another tab')
    updateUIForLoggedOutUser()
  }
})
```

## 配置选项

### AuthPluginOptions

```typescript
interface AuthPluginOptions {
  // 认证提供者配置
  providers?: {
    local?: LocalAuthConfig
    google?: OAuthConfig
    github?: OAuthConfig
    facebook?: OAuthConfig
    wechat?: OAuthConfig
    sso?: SSOConfig
  }
  
  // Token 配置
  token?: {
    type: 'jwt' | 'opaque'
    storage: 'localStorage' | 'sessionStorage' | 'cookie'
    key: string
    autoRefresh: boolean
    refreshThreshold: number
    header: string
    prefix: string
  }
  
  // RBAC 配置
  rbac?: {
    enabled: boolean
    roles: Record<string, Role>
    permissions: Record<string, string>
  }
  
  // 会话配置
  session?: {
    timeout: number
    warningTime: number
    autoExtend: boolean
    storage: 'localStorage' | 'sessionStorage'
    key: string
    syncAcrossTabs: boolean
    broadcastChannel: string
  }
  
  // 安全配置
  security?: {
    csrfProtection: boolean
    xssProtection: boolean
    httpsOnly: boolean
    sameSite: 'strict' | 'lax' | 'none'
  }
  
  // 调试配置
  debug?: boolean
  logger?: Logger
}
```

### 完整配置示例

```typescript
const auth = new AuthPlugin({
  providers: {
    local: {
      enabled: true,
      loginUrl: '/api/auth/login',
      logoutUrl: '/api/auth/logout',
      userInfoUrl: '/api/auth/me',
      validation: {
        username: { required: true, minLength: 3 },
        password: { required: true, minLength: 8 }
      }
    },
    google: {
      enabled: true,
      clientId: process.env.GOOGLE_CLIENT_ID,
      redirectUri: `${window.location.origin}/auth/callback`
    }
  },
  
  token: {
    type: 'jwt',
    storage: 'localStorage',
    key: 'auth_token',
    autoRefresh: true,
    refreshThreshold: 300,
    header: 'Authorization',
    prefix: 'Bearer '
  },
  
  rbac: {
    enabled: true,
    roles: {
      admin: { name: '管理员', permissions: ['*'] },
      editor: { name: '编辑者', permissions: ['content:*'] },
      viewer: { name: '查看者', permissions: ['content:read'] }
    }
  },
  
  session: {
    timeout: 30 * 60 * 1000,
    warningTime: 5 * 60 * 1000,
    autoExtend: true,
    syncAcrossTabs: true
  },
  
  security: {
    csrfProtection: true,
    xssProtection: true,
    httpsOnly: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  },
  
  debug: process.env.NODE_ENV === 'development'
})
```

## 使用示例

### 完整登录流程

```typescript
// 登录组件
class LoginComponent {
  constructor() {
    this.auth = new AuthPlugin(authConfig)
    this.setupEventListeners()
  }
  
  setupEventListeners() {
    // 监听认证状态变化
    this.auth.onAuthStateChange((user, isAuthenticated) => {
      if (isAuthenticated) {
        this.onLoginSuccess(user)
      } else {
        this.onLogout()
      }
    })
    
    // 监听会话警告
    this.auth.onSessionStateChange((state) => {
      if (state === 'warning') {
        this.showSessionWarning()
      }
    })
  }
  
  async handleLogin(formData) {
    try {
      this.showLoading(true)
      
      const result = await this.auth.login({
        username: formData.username,
        password: formData.password,
        rememberMe: formData.rememberMe
      })
      
      if (result.success) {
        this.showMessage('登录成功', 'success')
        this.redirectToDashboard()
      } else {
        this.showMessage(result.error || '登录失败', 'error')
      }
    } catch (error) {
      console.error('Login error:', error)
      this.showMessage('登录过程中发生错误', 'error')
    } finally {
      this.showLoading(false)
    }
  }
  
  async handleOAuthLogin(provider) {
    try {
      const result = await this.auth.login({ provider })
      if (result.success) {
        this.redirectToDashboard()
      }
    } catch (error) {
      console.error('OAuth login error:', error)
      this.showMessage('第三方登录失败', 'error')
    }
  }
  
  onLoginSuccess(user) {
    console.log('User logged in:', user)
    // 更新用户界面
    this.updateUserProfile(user)
    // 发送登录事件
    this.eventBus.emit('user:login', user)
  }
  
  onLogout() {
    console.log('User logged out')
    // 清理用户界面
    this.clearUserProfile()
    // 重定向到登录页
    this.redirectToLogin()
  }
}
```

### 权限控制组件

```typescript
// 权限控制高阶组件
function withPermission(WrappedComponent, requiredPermissions) {
  return class PermissionWrapper extends Component {
    constructor(props) {
      super(props)
      this.auth = getAuthInstance()
      this.state = {
        hasPermission: false,
        loading: true
      }
    }
    
    async componentDidMount() {
      await this.checkPermissions()
    }
    
    async checkPermissions() {
      const hasPermission = requiredPermissions.every(permission =>
        this.auth.hasPermission(permission)
      )
      
      this.setState({
        hasPermission,
        loading: false
      })
    }
    
    render() {
      const { loading, hasPermission } = this.state
      
      if (loading) {
        return <LoadingSpinner />
      }
      
      if (!hasPermission) {
        return <AccessDenied />
      }
      
      return <WrappedComponent {...this.props} />
    }
  }
}

// 使用权限控制
const AdminPanel = withPermission(AdminPanelComponent, ['admin:access'])
const UserManagement = withPermission(UserManagementComponent, ['user:manage'])
```

## 最佳实践

### 1. 安全性最佳实践

```typescript
// 1. 使用 HTTPS
const auth = new AuthPlugin({
  security: {
    httpsOnly: true,
    sameSite: 'strict'
  }
})

// 2. Token 安全存储
const auth = new AuthPlugin({
  token: {
    storage: 'httpOnly-cookie', // 推荐使用 httpOnly cookie
    secure: true,
    sameSite: 'strict'
  }
})

// 3. 定期刷新 Token
const auth = new AuthPlugin({
  token: {
    autoRefresh: true,
    refreshThreshold: 300 // 5分钟前自动刷新
  }
})

// 4. 输入验证
const auth = new AuthPlugin({
  providers: {
    local: {
      validation: {
        username: {
          required: true,
          pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        },
        password: {
          required: true,
          minLength: 8,
          pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/
        }
      }
    }
  }
})
```

### 2. 用户体验优化

```typescript
// 1. 自动登录
if (auth.hasValidToken()) {
  try {
    await auth.validateToken()
    // 自动登录成功
  } catch (error) {
    // Token 无效，清理并重定向到登录页
    auth.clearToken()
    redirectToLogin()
  }
}

// 2. 记住登录状态
const result = await auth.login({
  username,
  password,
  rememberMe: true // 使用持久化存储
})

// 3. 会话延长提醒
auth.onSessionStateChange((state) => {
  if (state === 'warning') {
    showConfirmDialog({
      title: '会话即将过期',
      message: '您的会话将在5分钟后过期，是否继续？',
      onConfirm: () => auth.extendSession(),
      onCancel: () => auth.logout()
    })
  }
})
```

### 3. 错误处理

```typescript
// 统一错误处理
auth.onError((error) => {
  switch (error.code) {
    case 'INVALID_CREDENTIALS':
      showMessage('用户名或密码错误', 'error')
      break
    case 'ACCOUNT_LOCKED':
      showMessage('账户已被锁定，请联系管理员', 'error')
      break
    case 'TOKEN_EXPIRED':
      showMessage('登录已过期，请重新登录', 'warning')
      redirectToLogin()
      break
    case 'PERMISSION_DENIED':
      showMessage('权限不足', 'error')
      redirectToHome()
      break
    default:
      showMessage('认证服务异常，请稍后重试', 'error')
  }
})

// 网络错误处理
auth.onNetworkError((error) => {
  if (error.code === 'NETWORK_ERROR') {
    showMessage('网络连接异常，请检查网络设置', 'error')
  }
})
```

## 故障排除

### 常见问题

#### 1. Token 失效问题

**问题**：用户频繁被要求重新登录

**解决方案**：
- 检查 token 过期时间设置
- 确认自动刷新机制正常工作
- 验证服务器时间同步

```typescript
// 调试 token 状态
console.log('Token info:', auth.getTokenInfo())
console.log('Token valid:', auth.isTokenValid())
console.log('Token expires at:', new Date(auth.getTokenExpirationTime()))
```

#### 2. 权限检查失败

**问题**：用户有权限但系统显示无权限

**解决方案**：
- 检查权限配置是否正确
- 确认用户角色分配
- 验证权限继承关系

```typescript
// 调试权限信息
console.log('User roles:', auth.getCurrentUser()?.roles)
console.log('User permissions:', auth.getCurrentUser()?.permissions)
console.log('Has permission:', auth.hasPermission('specific:permission'))
```

#### 3. 跨域认证问题

**问题**：第三方登录或 SSO 认证失败

**解决方案**：
- 检查 CORS 配置
- 确认回调 URL 设置
- 验证客户端 ID 和密钥

```typescript
// 调试 OAuth 配置
console.log('OAuth config:', auth.getOAuthConfig('google'))
console.log('Redirect URI:', auth.getRedirectUri('google'))
```

### 调试工具

```typescript
// 开启调试模式
const auth = new AuthPlugin({
  debug: true,
  logger: {
    log: (message, data) => console.log(`[Auth] ${message}`, data),
    warn: (message, data) => console.warn(`[Auth] ${message}`, data),
    error: (message, data) => console.error(`[Auth] ${message}`, data)
  }
})

// 获取调试信息
console.log('Auth state:', auth.getDebugInfo())
console.log('Active sessions:', auth.getActiveSessions())
console.log('Permission cache:', auth.getPermissionCache())
```

---

更多详细信息请参考 [完整 API 文档](/api/) 或访问 [GitHub 仓库](https://github.com/echo008/micro-core)。
