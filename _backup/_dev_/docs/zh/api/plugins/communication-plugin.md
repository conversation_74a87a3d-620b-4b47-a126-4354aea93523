# 通信插件 API

通信插件是 Micro-Core 的核心插件之一，提供了完整的应用间通信解决方案，包括事件总线、全局状态管理、消息通道等功能。

## 📋 目录

- [基础概念](#基础概念)
- [API 参考](#api-参考)
- [事件总线](#事件总线)
- [全局状态](#全局状态)
- [消息通道](#消息通道)
- [配置选项](#配置选项)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 基础概念

### 通信系统架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    通信系统架构图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   App A         │    │ Communication   │    │   App B         ││
│  │  (React)        │◄──►│   Plugin        │◄──►│  (Vue)          ││
│  │                 │    │                 │    │                 ││
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ ││
│  │ │   Event     │ │    │ │   Event     │ │    │ │   Event     │ ││
│  │ │   Emitter   │ │    │ │   Bus       │ │    │ │   Listener  │ ││
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ ││
│  │                 │    │                 │    │                 ││
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ ││
│  │ │   State     │ │    │ │   Global    │ │    │ │   State     │ ││
│  │ │   Updater   │ │    │ │   Store     │ │    │ │   Consumer  │ ││
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│           │                       │                       │       │
│           └───────────────────────┼───────────────────────┘       │
│                                   │                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                  Message Channel                            │ │
│  │  postMessage ←→ MessagePort ←→ BroadcastChannel ←→ Worker  │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 通信模式

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    通信模式对比                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  1. 事件总线模式 (Event Bus)                                     │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  App A ──emit──► EventBus ──notify──► App B               │ │
│  │         ◄──────────────────────────────────                │ │
│  │                    ▲                                       │ │
│  │                    │                                       │ │
│  │                    ▼                                       │ │
│  │                  App C                                     │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  2. 全局状态模式 (Global State)                                  │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  App A ──update──► GlobalStore ◄──subscribe── App B       │ │
│  │         ◄─notify─────────┬─────────notify──►               │ │
│  │                          │                                 │ │
│  │                          ▼                                 │ │
│  │                        App C                               │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  3. 消息通道模式 (Message Channel)                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  App A ──message──► Channel ──message──► App B             │ │
│  │         ◄─────────────────────────────────                 │ │
│  │                                                             │ │
│  │  特点：点对点通信，支持二进制数据                              │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## API 参考

### CommunicationPlugin 类

#### 构造函数

```typescript
constructor(options: CommunicationPluginOptions)
```

#### 事件总线方法

##### `emit(event: string, data?: any): void`

发送事件到事件总线。

```typescript
// 发送简单事件
communication.emit('user-login', { userId: '123' })

// 发送复杂数据
communication.emit('data-update', {
  type: 'user',
  action: 'create',
  payload: { name: 'John', email: '<EMAIL>' }
})
```

##### `on(event: string, handler: EventHandler): () => void`

监听事件。

```typescript
const unsubscribe = communication.on('user-login', (data) => {
  console.log('User logged in:', data.userId)
  updateUserState(data)
})

// 取消监听
unsubscribe()
```

##### `once(event: string, handler: EventHandler): void`

监听事件一次。

```typescript
communication.once('app-ready', () => {
  console.log('App is ready!')
  initializeFeatures()
})
```

##### `off(event: string, handler?: EventHandler): void`

取消事件监听。

```typescript
// 取消特定处理器
communication.off('user-login', userLoginHandler)

// 取消所有处理器
communication.off('user-login')
```

#### 全局状态方法

##### `setState(key: string, value: any): void`

设置全局状态。

```typescript
// 设置用户信息
communication.setState('user', {
  id: '123',
  name: 'John Doe',
  role: 'admin'
})

// 设置应用配置
communication.setState('config', {
  theme: 'dark',
  language: 'zh-CN'
})
```

##### `getState(key: string): any`

获取全局状态。

```typescript
const user = communication.getState('user')
const config = communication.getState('config')
```

##### `subscribe(key: string, handler: StateHandler): () => void`

订阅状态变化。

```typescript
const unsubscribe = communication.subscribe('user', (newUser, oldUser) => {
  console.log('User changed:', { newUser, oldUser })
  updateUI(newUser)
})

// 取消订阅
unsubscribe()
```

##### `unsubscribe(key: string, handler?: StateHandler): void`

取消状态订阅。

#### 消息通道方法

##### `createChannel(name: string): MessageChannel`

创建消息通道。

```typescript
const channel = communication.createChannel('user-data')
```

##### `sendMessage(channel: string, message: any): void`

发送消息到指定通道。

```typescript
communication.sendMessage('user-data', {
  type: 'update',
  payload: userData
})
```

##### `onMessage(channel: string, handler: MessageHandler): () => void`

监听通道消息。

```typescript
const unsubscribe = communication.onMessage('user-data', (message) => {
  console.log('Received message:', message)
  handleUserDataUpdate(message.payload)
})
```

### 类型定义

#### EventHandler

```typescript
type EventHandler = (data?: any) => void
```

#### StateHandler

```typescript
type StateHandler = (newValue: any, oldValue: any) => void
```

#### MessageHandler

```typescript
type MessageHandler = (message: {
  type: string
  payload: any
  timestamp: number
  source: string
}) => void
```

## 事件总线

### 基础使用

```typescript
import { CommunicationPlugin } from '@micro-core/plugin-communication'

const communication = new CommunicationPlugin({
  eventBus: {
    maxListeners: 100,
    enableWildcard: true
  }
})

// 在应用 A 中发送事件
communication.emit('user:login', {
  userId: '123',
  timestamp: Date.now()
})

// 在应用 B 中监听事件
communication.on('user:login', (data) => {
  console.log(`User ${data.userId} logged in at ${data.timestamp}`)
  updateUserStatus(data.userId, 'online')
})
```

### 通配符事件

```typescript
// 监听所有用户相关事件
communication.on('user:*', (data, event) => {
  console.log(`User event: ${event}`, data)
})

// 监听所有事件
communication.on('*', (data, event) => {
  console.log(`Event: ${event}`, data)
})
```

### 事件命名空间

```typescript
// 推荐的事件命名规范
communication.emit('user:login', userData)        // 用户登录
communication.emit('user:logout', userData)       // 用户登出
communication.emit('order:create', orderData)     // 订单创建
communication.emit('order:update', orderData)     // 订单更新
communication.emit('system:error', errorData)     // 系统错误
communication.emit('system:warning', warningData) // 系统警告
```

## 全局状态

### 状态管理

```typescript
// 初始化全局状态
communication.setState('app', {
  user: null,
  theme: 'light',
  language: 'zh-CN',
  permissions: []
})

// 更新用户信息
communication.setState('app.user', {
  id: '123',
  name: 'John Doe',
  avatar: '/avatars/john.jpg'
})

// 更新主题
communication.setState('app.theme', 'dark')
```

### 状态订阅

```typescript
// 订阅用户状态变化
const unsubscribeUser = communication.subscribe('app.user', (newUser, oldUser) => {
  if (newUser && !oldUser) {
    console.log('User logged in:', newUser)
    showWelcomeMessage(newUser.name)
  } else if (!newUser && oldUser) {
    console.log('User logged out:', oldUser)
    redirectToLogin()
  }
})

// 订阅主题变化
const unsubscribeTheme = communication.subscribe('app.theme', (newTheme) => {
  document.body.className = `theme-${newTheme}`
  updateComponentTheme(newTheme)
})
```

### 状态持久化

```typescript
const communication = new CommunicationPlugin({
  globalState: {
    persistence: {
      enabled: true,
      storage: 'localStorage', // 'localStorage' | 'sessionStorage' | 'indexedDB'
      key: 'micro-core-state',
      include: ['app.user', 'app.theme'], // 需要持久化的状态
      exclude: ['app.temp'] // 不需要持久化的状态
    }
  }
})
```

## 消息通道

### 基础使用

```typescript
// 创建专用通道
const userChannel = communication.createChannel('user-updates')
const orderChannel = communication.createChannel('order-updates')

// 发送消息
communication.sendMessage('user-updates', {
  type: 'profile-update',
  payload: {
    userId: '123',
    changes: { name: 'New Name' }
  }
})

// 监听消息
communication.onMessage('user-updates', (message) => {
  switch (message.type) {
    case 'profile-update':
      handleProfileUpdate(message.payload)
      break
    case 'avatar-change':
      handleAvatarChange(message.payload)
      break
  }
})
```

### 二进制数据传输

```typescript
// 发送文件数据
const fileData = new ArrayBuffer(1024)
communication.sendMessage('file-channel', {
  type: 'file-upload',
  payload: fileData,
  metadata: {
    filename: 'document.pdf',
    size: fileData.byteLength
  }
})

// 接收文件数据
communication.onMessage('file-channel', (message) => {
  if (message.type === 'file-upload') {
    const { payload, metadata } = message
    processFile(payload, metadata)
  }
})
```

## 配置选项

### CommunicationPluginOptions

```typescript
interface CommunicationPluginOptions {
  // 事件总线配置
  eventBus?: {
    maxListeners?: number
    enableWildcard?: boolean
    delimiter?: string
    verboseMemoryLeak?: boolean
  }
  
  // 全局状态配置
  globalState?: {
    initialState?: Record<string, any>
    persistence?: {
      enabled: boolean
      storage: 'localStorage' | 'sessionStorage' | 'indexedDB'
      key: string
      include?: string[]
      exclude?: string[]
    }
    middleware?: StateMiddleware[]
  }
  
  // 消息通道配置
  messageChannel?: {
    maxChannels?: number
    messageTimeout?: number
    enableBroadcast?: boolean
  }
  
  // 调试配置
  debug?: boolean
  logger?: Logger
}
```

### 完整配置示例

```typescript
const communication = new CommunicationPlugin({
  eventBus: {
    maxListeners: 50,
    enableWildcard: true,
    delimiter: ':',
    verboseMemoryLeak: true
  },
  
  globalState: {
    initialState: {
      app: {
        user: null,
        theme: 'light',
        language: navigator.language
      }
    },
    persistence: {
      enabled: true,
      storage: 'localStorage',
      key: 'micro-core-global-state',
      include: ['app.user', 'app.theme'],
      exclude: ['app.temp', 'app.cache']
    },
    middleware: [
      // 状态变化日志
      (action, state) => {
        console.log('State change:', action, state)
      },
      // 状态验证
      (action, state) => {
        if (action.key === 'app.user' && action.value) {
          validateUser(action.value)
        }
      }
    ]
  },
  
  messageChannel: {
    maxChannels: 20,
    messageTimeout: 5000,
    enableBroadcast: true
  },
  
  debug: process.env.NODE_ENV === 'development',
  logger: console
})
```

## 使用示例

### 用户登录状态同步

```typescript
// 应用 A：登录组件
class LoginComponent {
  async login(credentials) {
    try {
      const user = await authService.login(credentials)
      
      // 更新全局用户状态
      communication.setState('user', user)
      
      // 发送登录成功事件
      communication.emit('user:login:success', {
        user,
        timestamp: Date.now()
      })
      
    } catch (error) {
      communication.emit('user:login:error', { error })
    }
  }
}

// 应用 B：用户信息显示组件
class UserInfoComponent {
  constructor() {
    // 订阅用户状态变化
    this.unsubscribeUser = communication.subscribe('user', (user) => {
      this.updateUserDisplay(user)
    })
    
    // 监听登录事件
    this.unsubscribeLogin = communication.on('user:login:success', (data) => {
      this.showWelcomeMessage(data.user.name)
    })
  }
  
  destroy() {
    this.unsubscribeUser()
    this.unsubscribeLogin()
  }
}
```

### 购物车状态管理

```typescript
// 购物车服务
class CartService {
  constructor() {
    // 初始化购物车状态
    communication.setState('cart', {
      items: [],
      total: 0,
      count: 0
    })
  }
  
  addItem(product) {
    const cart = communication.getState('cart')
    const existingItem = cart.items.find(item => item.id === product.id)
    
    if (existingItem) {
      existingItem.quantity += 1
    } else {
      cart.items.push({ ...product, quantity: 1 })
    }
    
    this.updateCart(cart)
  }
  
  removeItem(productId) {
    const cart = communication.getState('cart')
    cart.items = cart.items.filter(item => item.id !== productId)
    this.updateCart(cart)
  }
  
  updateCart(cart) {
    cart.count = cart.items.reduce((sum, item) => sum + item.quantity, 0)
    cart.total = cart.items.reduce((sum, item) => sum + item.price * item.quantity, 0)
    
    communication.setState('cart', cart)
    communication.emit('cart:updated', cart)
  }
}

// 购物车图标组件
class CartIconComponent {
  constructor() {
    this.unsubscribe = communication.subscribe('cart', (cart) => {
      this.updateBadge(cart.count)
    })
  }
  
  updateBadge(count) {
    this.badgeElement.textContent = count
    this.badgeElement.style.display = count > 0 ? 'block' : 'none'
  }
}
```

## 最佳实践

### 1. 事件命名规范

```typescript
// 推荐的命名模式
'domain:action:result'

// 示例
'user:login:success'    // 用户登录成功
'user:login:error'      // 用户登录失败
'order:create:pending'  // 订单创建中
'order:create:success'  // 订单创建成功
'system:error:network'  // 网络错误
```

### 2. 状态设计原则

```typescript
// 扁平化状态结构
const state = {
  'user.profile': { id: '123', name: 'John' },
  'user.preferences': { theme: 'dark', lang: 'zh' },
  'cart.items': [],
  'cart.total': 0
}

// 避免深层嵌套
// ❌ 不推荐
const badState = {
  user: {
    profile: {
      personal: {
        info: {
          name: 'John'
        }
      }
    }
  }
}
```

### 3. 内存管理

```typescript
class ComponentWithCommunication {
  constructor() {
    // 保存取消订阅函数
    this.unsubscribers = []
    
    this.unsubscribers.push(
      communication.on('user:update', this.handleUserUpdate),
      communication.subscribe('theme', this.handleThemeChange)
    )
  }
  
  destroy() {
    // 清理所有订阅
    this.unsubscribers.forEach(unsubscribe => unsubscribe())
    this.unsubscribers = []
  }
}
```

### 4. 错误处理

```typescript
// 事件处理错误
communication.on('user:login', (data) => {
  try {
    updateUserInterface(data)
  } catch (error) {
    console.error('Error handling user login:', error)
    communication.emit('system:error', {
      source: 'user-login-handler',
      error: error.message
    })
  }
})

// 状态更新错误
try {
  communication.setState('user', invalidUserData)
} catch (error) {
  console.error('Failed to update user state:', error)
  // 回滚到之前的状态
  communication.setState('user', previousUserState)
}
```

## 故障排除

### 常见问题

#### 1. 事件未触发

**问题**：发送事件后监听器没有响应

**解决方案**：
- 检查事件名称是否一致
- 确认监听器注册时机
- 验证事件数据格式

```typescript
// 调试事件
communication.on('*', (data, event) => {
  console.log('Event fired:', event, data)
})
```

#### 2. 状态不同步

**问题**：状态更新后组件没有响应

**解决方案**：
- 检查状态键名是否正确
- 确认订阅函数正确注册
- 验证状态更新是否成功

```typescript
// 调试状态变化
communication.subscribe('*', (newValue, oldValue, key) => {
  console.log('State changed:', key, { newValue, oldValue })
})
```

#### 3. 内存泄漏

**问题**：应用运行时间长后内存占用过高

**解决方案**：
- 确保正确取消事件监听
- 检查状态订阅是否及时清理
- 使用开发工具监控内存使用

```typescript
// 内存使用监控
setInterval(() => {
  if (performance.memory) {
    console.log('Memory usage:', {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
    })
  }
}, 10000)
```

### 调试工具

```typescript
// 开启调试模式
const communication = new CommunicationPlugin({
  debug: true,
  logger: {
    log: (message, ...args) => console.log(`[Communication] ${message}`, ...args),
    warn: (message, ...args) => console.warn(`[Communication] ${message}`, ...args),
    error: (message, ...args) => console.error(`[Communication] ${message}`, ...args)
  }
})

// 获取调试信息
console.log('Active listeners:', communication.getListeners())
console.log('Current state:', communication.getAllState())
console.log('Active channels:', communication.getChannels())
```

---

更多详细信息请参考 [完整 API 文档](/api/) 或访问 [GitHub 仓库](https://github.com/echo008/micro-core)。
