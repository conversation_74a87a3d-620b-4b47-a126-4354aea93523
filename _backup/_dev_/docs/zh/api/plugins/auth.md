# 认证插件 API

认证插件提供了统一的身份认证和授权管理功能，支持多种认证方式和权限控制策略。

## 📋 目录

- [插件概述](#插件概述)
- [安装配置](#安装配置)
- [核心API](#核心api)
- [认证管理](#认证管理)
- [权限控制](#权限控制)
- [会话管理](#会话管理)
- [多租户支持](#多租户支持)
- [安全特性](#安全特性)
- [最佳实践](#最佳实践)

## 插件概述

认证插件是 Micro-Core 生态系统中的安全核心插件，提供了完整的身份认证和授权解决方案。

### 🎯 主要特性

- **多种认证方式**：JWT、OAuth2、SAML、LDAP 等
- **细粒度权限控制**：基于角色和资源的访问控制
- **会话管理**：安全的会话创建、维护和销毁
- **多租户支持**：支持多租户架构的权限隔离
- **安全防护**：防止常见的安全攻击
- **SSO 支持**：单点登录集成

## 安装配置

### 安装插件

```bash
npm install @micro-core/plugin-auth
```

### 基础配置

```typescript
import { MicroCore } from '@micro-core/core'
import { AuthPlugin } from '@micro-core/plugin-auth'

const microCore = new MicroCore({
  plugins: [
    new AuthPlugin({
      // 认证配置
      auth: {
        provider: 'jwt',
        secret: process.env.JWT_SECRET,
        expiresIn: '24h',
        refreshToken: true
      },
      
      // 权限配置
      authorization: {
        strategy: 'rbac',
        defaultRole: 'user',
        superAdmin: 'admin'
      },
      
      // 会话配置
      session: {
        storage: 'memory',
        timeout: 30 * 60 * 1000, // 30分钟
        sliding: true
      },
      
      // 安全配置
      security: {
        rateLimiting: true,
        bruteForceProtection: true,
        csrfProtection: true
      }
    })
  ]
})
```

## 核心API

### AuthPlugin

认证插件的主类，提供所有认证和授权功能。

```typescript
class AuthPlugin {
  constructor(options?: AuthOptions)
  
  // 插件生命周期
  install(microCore: MicroCore): void
  uninstall(): void
  
  // 获取认证实例
  getAuthManager(): AuthManager
  getPermissionManager(): PermissionManager
  getSessionManager(): SessionManager
}
```

### AuthOptions

```typescript
interface AuthOptions {
  auth?: AuthConfig
  authorization?: AuthorizationConfig
  session?: SessionConfig
  security?: SecurityConfig
  providers?: ProviderConfig[]
}

interface AuthConfig {
  provider: 'jwt' | 'oauth2' | 'saml' | 'ldap'
  secret?: string
  expiresIn?: string | number
  refreshToken?: boolean
  algorithm?: string
}

interface AuthorizationConfig {
  strategy: 'rbac' | 'abac' | 'acl'
  defaultRole?: string
  superAdmin?: string
  permissions?: PermissionDefinition[]
}
```

## 认证管理

认证管理器负责用户身份验证和令牌管理。

### 基础用法

```typescript
// 获取认证管理器
const authManager = microCore.getPlugin('auth').getAuthManager()

// 用户登录
const loginResult = await authManager.login({
  username: '<EMAIL>',
  password: 'password123'
})

console.log('登录成功:', loginResult)
// {
//   success: true,
//   user: { id: '123', username: 'john', role: 'user' },
//   token: 'eyJhbGciOiJIUzI1NiIs...',
//   refreshToken: 'eyJhbGciOiJIUzI1NiIs...',
//   expiresAt: **********
// }

// 用户登出
await authManager.logout()

// 刷新令牌
const newToken = await authManager.refreshToken(refreshToken)
```

### 多种认证方式

#### JWT 认证

```typescript
const jwtAuth = new JWTAuthProvider({
  secret: 'your-secret-key',
  expiresIn: '24h',
  algorithm: 'HS256',
  issuer: 'micro-core',
  audience: 'micro-apps'
})

// 生成令牌
const token = await jwtAuth.generateToken({
  userId: '123',
  username: 'john',
  role: 'user'
})

// 验证令牌
const payload = await jwtAuth.verifyToken(token)
```

#### OAuth2 认证

```typescript
const oauth2Auth = new OAuth2AuthProvider({
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret',
  redirectUri: 'http://localhost:3000/callback',
  scope: ['read', 'write'],
  provider: 'google' // 或 'github', 'facebook' 等
})

// 获取授权URL
const authUrl = oauth2Auth.getAuthorizationUrl()

// 处理回调
const tokens = await oauth2Auth.handleCallback(code)
```

### AuthManager API

```typescript
interface AuthManager {
  // 认证操作
  login(credentials: LoginCredentials): Promise<LoginResult>
  logout(): Promise<void>
  register(userData: RegisterData): Promise<RegisterResult>
  
  // 令牌管理
  generateToken(payload: TokenPayload): Promise<string>
  verifyToken(token: string): Promise<TokenPayload>
  refreshToken(refreshToken: string): Promise<string>
  revokeToken(token: string): Promise<void>
  
  // 用户管理
  getCurrentUser(): Promise<User | null>
  updateUser(userId: string, data: Partial<User>): Promise<User>
  deleteUser(userId: string): Promise<void>
  
  // 密码管理
  changePassword(oldPassword: string, newPassword: string): Promise<void>
  resetPassword(email: string): Promise<void>
  
  // 认证状态
  isAuthenticated(): boolean
  getAuthStatus(): AuthStatus
}
```

## 权限控制

权限管理器提供了细粒度的权限控制功能。

### RBAC (基于角色的访问控制)

```typescript
// 获取权限管理器
const permissionManager = microCore.getPlugin('auth').getPermissionManager()

// 定义角色
await permissionManager.createRole({
  name: 'editor',
  description: '编辑者',
  permissions: [
    'article:read',
    'article:write',
    'article:update'
  ]
})

// 分配角色
await permissionManager.assignRole('user123', 'editor')

// 检查权限
const canEdit = await permissionManager.hasPermission('user123', 'article:update')
const canDelete = await permissionManager.hasPermission('user123', 'article:delete')

console.log('可以编辑:', canEdit)    // true
console.log('可以删除:', canDelete)  // false
```

### 会话管理

会话管理器负责用户会话的创建、维护和销毁。

```typescript
// 获取会话管理器
const sessionManager = microCore.getPlugin('auth').getSessionManager()

// 创建会话
const session = await sessionManager.create({
  userId: 'user123',
  data: { role: 'user', preferences: {} },
  timeout: 30 * 60 * 1000 // 30分钟
})

// 获取会话
const currentSession = await sessionManager.get(session.id)

// 更新会话
await sessionManager.update(session.id, {
  lastActivity: Date.now()
})

// 销毁会话
await sessionManager.destroy(session.id)
```

## 安全特性

### 防暴力破解

```typescript
const authPlugin = new AuthPlugin({
  security: {
    bruteForceProtection: {
      enabled: true,
      maxAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15分钟
      resetTime: 60 * 60 * 1000 // 1小时后重置
    }
  }
})
```

### CSRF 防护

```typescript
const authPlugin = new AuthPlugin({
  security: {
    csrfProtection: {
      enabled: true,
      tokenName: '_csrf',
      cookieName: 'csrf-token',
      headerName: 'X-CSRF-Token'
    }
  }
})
```

## 最佳实践

### 1. 安全配置

```typescript
// ✅ 推荐的安全配置
const authPlugin = new AuthPlugin({
  auth: {
    secret: process.env.JWT_SECRET, // 使用环境变量
    expiresIn: '1h', // 短期令牌
    refreshToken: true // 启用刷新令牌
  },
  security: {
    rateLimiting: true,
    bruteForceProtection: true,
    csrfProtection: true
  }
})
```

### 2. 权限设计

```typescript
// ✅ 清晰的权限命名
const permissions = [
  'user:read',
  'user:write',
  'user:delete',
  'article:read',
  'article:write',
  'article:publish'
]

// ✅ 合理的角色设计
const roles = {
  viewer: ['user:read', 'article:read'],
  editor: ['user:read', 'article:read', 'article:write'],
  admin: ['user:*', 'article:*']
}
```

## 相关链接

- [核心 API 文档](/api/core)
- [权限控制指南](/guide/features/auth)
- [安全最佳实践](/guide/security)
- [插件开发指南](/guide/advanced/plugins)

---

*最后更新: 2024-07-27*