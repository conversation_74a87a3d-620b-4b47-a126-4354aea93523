# 插件基类 API

Micro-Core 的插件系统基于统一的基类设计，所有插件都继承自 `BasePlugin` 类，提供标准化的插件开发接口和生命周期管理。

## BasePlugin 基类

### 类定义

```typescript
import { BasePlugin, PluginContext, PluginType } from '@micro-core/core';

export abstract class BasePlugin {
  // 插件名称（必须实现）
  abstract readonly name: string;
  
  // 插件版本（必须实现）
  abstract readonly version: string;
  
  // 插件类型（必须实现）
  abstract readonly type: PluginType;
  
  // 插件依赖（可选）
  readonly dependencies?: string[];
  
  // 插件配置（可选）
  protected options?: Record<string, any>;
  
  // 插件上下文
  protected context?: PluginContext;
  
  // 插件状态
  private _status: PluginStatus = PluginStatus.NOT_INSTALLED;
  
  constructor(options?: Record<string, any>) {
    this.options = options || {};
  }
  
  // 插件安装（必须实现）
  abstract install(context: PluginContext): Promise<void> | void;
  
  // 插件卸载（必须实现）
  abstract uninstall(): Promise<void> | void;
  
  // 获取插件状态
  get status(): PluginStatus {
    return this._status;
  }
  
  // 获取插件信息
  getInfo(): PluginInfo {
    return {
      name: this.name,
      version: this.version,
      type: this.type,
      dependencies: this.dependencies || [],
      status: this.status
    };
  }
}
```

## 核心属性

### name

插件的唯一标识名称。

```typescript
abstract readonly name: string;
```

**示例:**

```typescript
export class MyPlugin extends BasePlugin {
  readonly name = 'my-plugin';
  // ...
}
```

### version

插件版本号，遵循语义化版本规范。

```typescript
abstract readonly version: string;
```

**示例:**

```typescript
export class MyPlugin extends BasePlugin {
  readonly version = '1.2.3';
  // ...
}
```

### type

插件类型，用于分类和管理。

```typescript
abstract readonly type: PluginType;

enum PluginType {
  CORE = 'core',
  SANDBOX = 'sandbox',
  ROUTER = 'router',
  COMMUNICATION = 'communication',
  LOADER = 'loader',
  AUTH = 'auth',
  MONITOR = 'monitor',
  CUSTOM = 'custom'
}
```

**示例:**

```typescript
export class MyPlugin extends BasePlugin {
  readonly type = PluginType.CUSTOM;
  // ...
}
```

### dependencies

插件依赖列表，声明该插件需要的其他插件。

```typescript
readonly dependencies?: string[];
```

**示例:**

```typescript
export class MyPlugin extends BasePlugin {
  readonly dependencies = ['router', 'communication'];
  // ...
}
```

## 核心方法

### install()

插件安装方法，在插件被注册到系统时调用。

```typescript
abstract install(context: PluginContext): Promise<void> | void;
```

**参数:**
- `context` - 插件上下文，提供系统接口

**示例:**

```typescript
export class MyPlugin extends BasePlugin {
  async install(context: PluginContext) {
    this.context = context;
    
    // 初始化插件
    await this.initialize();
    
    // 注册钩子
    this.registerHooks();
    
    // 设置事件监听
    this.setupEventListeners();
    
    console.log(`插件 ${this.name} 安装完成`);
  }
  
  private async initialize() {
    // 插件初始化逻辑
  }
  
  private registerHooks() {
    // 注册生命周期钩子
    this.context.registerHook('app:beforeMount', this.handleBeforeMount.bind(this));
    this.context.registerHook('app:afterMount', this.handleAfterMount.bind(this));
  }
  
  private setupEventListeners() {
    // 设置事件监听器
    this.context.eventBus.on('custom:event', this.handleCustomEvent.bind(this));
  }
}
```

### uninstall()

插件卸载方法，在插件被移除时调用。

```typescript
abstract uninstall(): Promise<void> | void;
```

**示例:**

```typescript
export class MyPlugin extends BasePlugin {
  async uninstall() {
    // 清理资源
    await this.cleanup();
    
    // 移除事件监听
    this.removeEventListeners();
    
    // 注销钩子
    this.unregisterHooks();
    
    console.log(`插件 ${this.name} 卸载完成`);
  }
  
  private async cleanup() {
    // 清理插件资源
  }
  
  private removeEventListeners() {
    // 移除事件监听器
    this.context?.eventBus.off('custom:event', this.handleCustomEvent);
  }
  
  private unregisterHooks() {
    // 注销生命周期钩子
    this.context?.unregisterHook('app:beforeMount', this.handleBeforeMount);
    this.context?.unregisterHook('app:afterMount', this.handleAfterMount);
  }
}
```

## 插件上下文 (PluginContext)

插件上下文提供了插件与核心系统交互的接口。

### 接口定义

```typescript
interface PluginContext {
  // 应用管理器
  readonly appManager: AppManager;
  
  // 事件总线
  readonly eventBus: EventBus;
  
  // 配置管理器
  readonly config: ConfigManager;
  
  // 日志系统
  readonly logger: Logger;
  
  // 状态管理器
  readonly stateManager: StateManager;
  
  // 工具函数
  readonly utils: PluginUtils;
  
  // 注册钩子
  registerHook(name: string, handler: HookHandler): void;
  
  // 注销钩子
  unregisterHook(name: string, handler: HookHandler): void;
  
  // 触发钩子
  triggerHook(name: string, ...args: any[]): Promise<any>;
  
  // 获取其他插件实例
  getPlugin<T = BasePlugin>(name: string): T | null;
  
  // 获取所有插件
  getPlugins(): Map<string, BasePlugin>;
  
  // 检查插件是否存在
  hasPlugin(name: string): boolean;
}
```

### 使用示例

```typescript
export class MyPlugin extends BasePlugin {
  async install(context: PluginContext) {
    this.context = context;
    
    // 使用应用管理器
    const apps = context.appManager.getApps();
    console.log('当前应用列表:', apps);
    
    // 使用事件总线
    context.eventBus.on('app:mounted', (app) => {
      console.log(`应用 ${app.name} 已挂载`);
    });
    
    // 使用配置管理器
    const config = context.config.get('myPlugin');
    console.log('插件配置:', config);
    
    // 使用日志系统
    context.logger.info('插件初始化完成');
    
    // 使用状态管理器
    await context.stateManager.setState('plugin.myPlugin.status', 'active');
    
    // 获取其他插件
    const routerPlugin = context.getPlugin('router');
    if (routerPlugin) {
      console.log('路由插件已加载');
    }
  }
}
```

## 生命周期钩子

### 系统钩子

插件可以注册系统级别的生命周期钩子：

```typescript
// 应用生命周期钩子
'app:beforeLoad'    // 应用加载前
'app:afterLoad'     // 应用加载后
'app:beforeMount'   // 应用挂载前
'app:afterMount'    // 应用挂载后
'app:beforeUnmount' // 应用卸载前
'app:afterUnmount'  // 应用卸载后
'app:error'         // 应用错误

// 路由生命周期钩子
'route:beforeChange' // 路由变化前
'route:afterChange'  // 路由变化后
'route:error'        // 路由错误

// 系统生命周期钩子
'system:start'       // 系统启动
'system:stop'        // 系统停止
'system:error'       // 系统错误

// 插件生命周期钩子
'plugin:beforeInstall' // 插件安装前
'plugin:afterInstall'  // 插件安装后
'plugin:beforeUninstall' // 插件卸载前
'plugin:afterUninstall'  // 插件卸载后
```

### 钩子注册示例

```typescript
export class MyPlugin extends BasePlugin {
  async install(context: PluginContext) {
    this.context = context;
    
    // 注册应用生命周期钩子
    context.registerHook('app:beforeMount', async (app) => {
      console.log(`准备挂载应用: ${app.name}`);
      
      // 可以修改应用配置
      app.config.customProperty = 'value';
      
      // 可以阻止应用挂载
      if (app.name === 'blocked-app') {
        throw new Error('应用被阻止挂载');
      }
    });
    
    // 注册路由钩子
    context.registerHook('route:beforeChange', async (to, from) => {
      console.log(`路由从 ${from.path} 变更到 ${to.path}`);
      
      // 路由守卫逻辑
      if (to.meta?.requiresAuth && !this.isAuthenticated()) {
        throw new Error('需要登录');
      }
    });
    
    // 注册系统钩子
    context.registerHook('system:error', async (error) => {
      console.error('系统错误:', error);
      
      // 错误处理逻辑
      await this.handleSystemError(error);
    });
  }
  
  private isAuthenticated(): boolean {
    return this.context?.stateManager.getState('auth.isLoggedIn') || false;
  }
  
  private async handleSystemError(error: Error): Promise<void> {
    // 错误处理逻辑
    await this.context?.logger.error('系统错误', error);
  }
}
```

## 插件工具类

### PluginUtils 接口

```typescript
interface PluginUtils {
  // 深度合并对象
  deepMerge<T>(target: T, ...sources: Partial<T>[]): T;
  
  // 防抖函数
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate?: boolean
  ): T;
  
  // 节流函数
  throttle<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): T;
  
  // 生成唯一ID
  generateId(prefix?: string): string;
  
  // 类型检查
  isFunction(value: any): value is Function;
  isObject(value: any): value is object;
  isArray(value: any): value is any[];
  isString(value: any): value is string;
  isNumber(value: any): value is number;
  isBoolean(value: any): value is boolean;
  
  // 路径操作
  getNestedValue(obj: any, path: string): any;
  setNestedValue(obj: any, path: string, value: any): void;
  
  // 事件相关
  createEventEmitter(): EventEmitter;
  
  // 异步工具
  sleep(ms: number): Promise<void>;
  timeout<T>(promise: Promise<T>, ms: number): Promise<T>;
  retry<T>(
    fn: () => Promise<T>,
    options?: RetryOptions
  ): Promise<T>;
}
```

### 使用示例

```typescript
export class MyPlugin extends BasePlugin {
  async install(context: PluginContext) {
    this.context = context;
    const { utils } = context;
    
    // 使用深度合并
    const config = utils.deepMerge(
      this.getDefaultConfig(),
      this.options,
      context.config.get('myPlugin')
    );
    
    // 使用防抖
    const debouncedHandler = utils.debounce(this.handleEvent.bind(this), 300);
    context.eventBus.on('frequent:event', debouncedHandler);
    
    // 使用节流
    const throttledHandler = utils.throttle(this.handleScroll.bind(this), 100);
    window.addEventListener('scroll', throttledHandler);
    
    // 生成唯一ID
    const instanceId = utils.generateId('plugin');
    console.log('插件实例ID:', instanceId);
    
    // 类型检查
    if (utils.isFunction(config.onInit)) {
      await config.onInit();
    }
    
    // 路径操作
    const nestedValue = utils.getNestedValue(config, 'advanced.settings.enabled');
    utils.setNestedValue(config, 'runtime.instanceId', instanceId);
    
    // 异步工具
    await utils.sleep(100); // 等待100ms
    
    const result = await utils.timeout(
      this.loadRemoteConfig(),
      5000 // 5秒超时
    );
    
    // 重试机制
    const data = await utils.retry(
      () => this.fetchData(),
      { maxAttempts: 3, delay: 1000 }
    );
  }
}
```

## 插件配置

### 配置模式

```typescript
// 插件配置接口
interface PluginConfig {
  // 基础配置
  enabled?: boolean;
  debug?: boolean;
  
  // 性能配置
  performance?: {
    enableCache?: boolean;
    cacheSize?: number;
    timeout?: number;
  };
  
  // 功能配置
  features?: {
    [key: string]: boolean | object;
  };
  
  // 自定义配置
  [key: string]: any;
}

export class MyPlugin extends BasePlugin {
  private config: PluginConfig;
  
  constructor(options?: PluginConfig) {
    super(options);
    
    // 合并默认配置
    this.config = {
      enabled: true,
      debug: false,
      performance: {
        enableCache: true,
        cacheSize: 100,
        timeout: 5000
      },
      features: {
        featureA: true,
        featureB: false
      },
      ...options
    };
  }
  
  async install(context: PluginContext) {
    // 检查插件是否启用
    if (!this.config.enabled) {
      console.log(`插件 ${this.name} 已禁用`);
      return;
    }
    
    // 根据配置初始化功能
    if (this.config.features?.featureA) {
      await this.initializeFeatureA();
    }
    
    if (this.config.features?.featureB) {
      await this.initializeFeatureB();
    }
  }
}
```

## 错误处理

### 插件错误类型

```typescript
// 插件错误基类
export class PluginError extends Error {
  constructor(
    message: string,
    public readonly pluginName: string,
    public readonly code?: string
  ) {
    super(message);
    this.name = 'PluginError';
  }
}

// 插件安装错误
export class PluginInstallError extends PluginError {
  constructor(pluginName: string, message: string, public readonly cause?: Error) {
    super(message, pluginName, 'INSTALL_ERROR');
    this.name = 'PluginInstallError';
  }
}

// 插件依赖错误
export class PluginDependencyError extends PluginError {
  constructor(
    pluginName: string,
    public readonly missingDependencies: string[]
  ) {
    super(
      `插件 ${pluginName} 缺少依赖: ${missingDependencies.join(', ')}`,
      pluginName,
      'DEPENDENCY_ERROR'
    );
    this.name = 'PluginDependencyError';
  }
}
```

### 错误处理示例

```typescript
export class MyPlugin extends BasePlugin {
  async install(context: PluginContext) {
    try {
      this.context = context;
      
      // 检查依赖
      await this.checkDependencies();
      
      // 初始化插件
      await this.initialize();
      
    } catch (error) {
      // 包装错误
      if (error instanceof PluginError) {
        throw error;
      }
      
      throw new PluginInstallError(
        this.name,
        `插件安装失败: ${error.message}`,
        error
      );
    }
  }
  
  private async checkDependencies(): Promise<void> {
    if (!this.dependencies) {
      return;
    }
    
    const missingDeps: string[] = [];
    
    for (const dep of this.dependencies) {
      if (!this.context?.hasPlugin(dep)) {
        missingDeps.push(dep);
      }
    }
    
    if (missingDeps.length > 0) {
      throw new PluginDependencyError(this.name, missingDeps);
    }
  }
  
  private async initialize(): Promise<void> {
    // 初始化逻辑
    try {
      await this.setupResources();
      await this.registerHandlers();
    } catch (error) {
      // 清理已创建的资源
      await this.cleanup();
      throw error;
    }
  }
  
  private async cleanup(): Promise<void> {
    // 清理资源
  }
}
```

## 插件测试

### 单元测试

```typescript
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { MyPlugin } from './my-plugin';
import { createMockPluginContext } from '@micro-core/test-utils';

describe('MyPlugin', () => {
  let plugin: MyPlugin;
  let mockContext: PluginContext;
  
  beforeEach(() => {
    plugin = new MyPlugin({
      enabled: true,
      debug: true
    });
    
    mockContext = createMockPluginContext();
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  it('should install successfully', async () => {
    await plugin.install(mockContext);
    
    expect(plugin.status).toBe(PluginStatus.INSTALLED);
    expect(mockContext.registerHook).toHaveBeenCalled();
  });
  
  it('should handle dependencies correctly', async () => {
    // 模拟缺少依赖
    mockContext.hasPlugin = vi.fn().mockReturnValue(false);
    
    await expect(plugin.install(mockContext)).rejects.toThrow(
      PluginDependencyError
    );
  });
  
  it('should uninstall cleanly', async () => {
    await plugin.install(mockContext);
    await plugin.uninstall();
    
    expect(plugin.status).toBe(PluginStatus.NOT_INSTALLED);
    expect(mockContext.unregisterHook).toHaveBeenCalled();
  });
  
  it('should handle configuration correctly', () => {
    const customPlugin = new MyPlugin({
      features: {
        featureA: false,
        featureB: true
      }
    });
    
    expect(customPlugin.getConfig().features.featureA).toBe(false);
    expect(customPlugin.getConfig().features.featureB).toBe(true);
  });
});
```

### 集成测试

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { MicroCore } from '@micro-core/core';
import { MyPlugin } from './my-plugin';

describe('MyPlugin Integration', () => {
  let microCore: MicroCore;
  let plugin: MyPlugin;
  
  beforeEach(() => {
    microCore = new MicroCore();
    plugin = new MyPlugin();
  });
  
  it('should integrate with MicroCore correctly', async () => {
    // 注册插件
    microCore.use(plugin);
    
    // 启动系统
    await microCore.start();
    
    // 验证插件已安装
    const installedPlugin = microCore.getPlugin('my-plugin');
    expect(installedPlugin).toBeDefined();
    expect(installedPlugin.status).toBe(PluginStatus.INSTALLED);
  });
  
  it('should handle app lifecycle events', async () => {
    const handleBeforeMount = vi.fn();
    plugin.handleBeforeMount = handleBeforeMount;
    
    microCore.use(plugin);
    await microCore.start();
    
    // 注册应用
    microCore.registerApp({
      name: 'test-app',
      entry: '/test-app.js',
      container: '#test-container',
      activeRule: '/test'
    });
    
    // 触发应用挂载
    await microCore.loadApp('test-app');
    
    expect(handleBeforeMount).toHaveBeenCalled();
  });
});
```

## 最佳实践

### 插件开发指南

1. **命名规范**
   - 使用描述性的插件名称
   - 遵循 kebab-case 命名约定
   - 避免与系统保留名称冲突

2. **版本管理**
   - 遵循语义化版本规范
   - 在重大变更时更新主版本号
   - 提供版本兼容性说明

3. **依赖管理**
   - 明确声明插件依赖
   - 避免循环依赖
   - 提供依赖缺失时的降级方案

4. **错误处理**
   - 使用专门的错误类型
   - 提供详细的错误信息
   - 实现优雅的错误恢复

5. **性能考虑**
   - 避免阻塞主线程
   - 使用异步操作
   - 实现资源清理

6. **测试覆盖**
   - 编写单元测试
   - 进行集成测试
   - 测试错误场景

### 插件模板

```typescript
import { BasePlugin, PluginType, PluginContext } from '@micro-core/core';

export interface MyPluginOptions {
  enabled?: boolean;
  debug?: boolean;
  // 其他配置选项
}

export class MyPlugin extends BasePlugin {
  readonly name = 'my-plugin';
  readonly version = '1.0.0';
  readonly type = PluginType.CUSTOM;
  readonly dependencies = ['router']; // 可选
  
  private config: Required<MyPluginOptions>;
  
  constructor(options: MyPluginOptions = {}) {
    super(options);
    
    this.config = {
      enabled: true,
      debug: false,
      ...options
    };
  }
  
  async install(context: PluginContext): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    this.context = context;
    
    try {
      // 初始化插件
      await this.initialize();
      
      // 注册钩子
      this.registerHooks();
      
      // 设置事件监听
      this.setupEventListeners();
      
      this.log('插件安装完成');
      
    } catch (error) {
      this.log('插件安装失败', error);
      throw error;
    }
  }
  
  async uninstall(): Promise<void> {
    try {
      // 清理资源
      await this.cleanup();
      
      // 移除事件监听
      this.removeEventListeners();
      
      // 注销钩子
      this.unregisterHooks();
      
      this.log('插件卸载完成');
      
    } catch (error) {
      this.log('插件卸载失败', error);
      throw error;
    }
  }
  
  private async initialize(): Promise<void> {
    // 插件初始化逻辑
  }
  
  private registerHooks(): void {
    // 注册生命周期钩子
  }
  
  private setupEventListeners(): void {
    // 设置事件监听器
  }
  
  private async cleanup(): Promise<void> {
    // 清理资源
  }
  
  private removeEventListeners(): void {
    // 移除事件监听器
  }
  
  private unregisterHooks(): void {
    // 注销生命周期钩子
  }
  
  private log(message: string, ...args: any[]): void {
    if (this.config.debug) {
      console.log(`[${this.name}] ${message}`, ...args);
    }
  }
}
```

## 总结

Micro-Core 的插件基类提供了：

1. **标准化接口**: 统一的插件开发规范
2. **生命周期管理**: 完整的安装和卸载流程
3. **上下文访问**: 丰富的系统接口
4. **钩子机制**: 灵活的事件处理
5. **工具支持**: 实用的开发工具
6. **错误处理**: 完善的错误管理
7. **测试支持**: 便于测试的设计

通过继承 `BasePlugin` 基类，开发者可以快速创建功能强大、稳定可靠的插件。
