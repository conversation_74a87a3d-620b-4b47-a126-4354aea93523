# 适配器 API 参考

## 概述

Micro-Core 适配器系统提供了框架无关的微前端集成能力，通过协议转换接口、数据格式处理和异常处理机制，实现不同前端框架与微前端架构的无缝集成。本文档提供完整的适配器API技术规范，包括接口定义、参数说明、使用示例和错误处理机制。

### 功能定位

- **框架抽象**：为不同前端框架提供统一的微前端接入层
- **生命周期映射**：将微前端生命周期映射到框架特定的生命周期
- **资源管理**：处理框架特定的资源加载和依赖管理
- **协议转换**：在微前端协议和框架协议之间进行转换

## 核心接口定义

### BaseAdapter 接口

#### 接口定义
```typescript
interface BaseAdapter {
  /** 适配器名称 - 必须唯一 */
  readonly name: string;
  /** 适配器版本 - 遵循语义化版本规范 */
  readonly version: string;
  /** 支持的框架版本 - 兼容的框架版本范围 */
  readonly supportedVersions: string[];
  
  /**
   * 检查是否可以处理指定的应用配置
   */
  canHandle(config: BaseAppConfig): Promise<boolean> | boolean;
  
  /**
   * 加载应用
   */
  load(config: BaseAppConfig): Promise<BaseAppInstance>;
  
  /**
   * 挂载应用
   */
  mount(appId: string, container: Element): Promise<void>;
  
  /**
   * 卸载应用
   */
  unmount(appId: string): Promise<void>;
  
  /**
   * 更新应用
   */
  update(appId: string, config: Partial<BaseAppConfig>): Promise<void>;
  
  /**
   * 销毁应用
   */
  destroy(appId: string): Promise<void>;
  
  /**
   * 获取应用实例
   */
  getAppInstance(appId: string): BaseAppInstance | undefined;
  
  /**
   * 获取所有应用实例
   */
  getAllAppInstances(): BaseAppInstance[];
}
```

#### 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `name` | `string` | ✅ | 适配器唯一标识符，建议使用框架名称 |
| `version` | `string` | ✅ | 适配器版本号，遵循 semver 规范 |
| `supportedVersions` | `string[]` | ✅ | 支持的框架版本范围数组 |

### BaseAppConfig 接口

```typescript
interface BaseAppConfig {
  name: string;
  entry: string | { scripts?: string[]; styles?: string[]; html?: string };
  container?: string | HTMLElement;
  activeRule?: string | ((location: Location) => boolean);
  props?: Record<string, any>;
  framework?: string;
  version?: string;
  sandbox?: boolean | SandboxConfig;
  loader?: LoaderConfig;
}
```

### BaseAppInstance 接口

```typescript
interface BaseAppInstance {
  id: string;
  name: string;
  config: BaseAppConfig;
  status: 'loading' | 'loaded' | 'mounting' | 'mounted' | 'unmounting' | 'unmounted' | 'error';
  instance?: any;
  container?: Element;
  sandbox?: any;
  createdAt: number;
  updatedAt: number;
  error?: Error;
}
```

## 核心API方法

### canHandle()

**功能说明**: 检查适配器是否能够处理指定的应用配置

**方法签名**:
```typescript
canHandle(config: BaseAppConfig): Promise<boolean> | boolean
```

**参数列表**:
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `config` | `BaseAppConfig` | ✅ | 应用配置对象 |

**返回值**: `Promise<boolean> | boolean` - 是否能够处理该配置

**使用示例**:
```typescript
const reactAdapter = new ReactAdapter();
const canHandle = await reactAdapter.canHandle({
  name: 'react-app',
  entry: 'https://example.com/react-app.js',
  framework: 'react'
});
```

### load()

**功能说明**: 加载微应用并创建应用实例

**方法签名**:
```typescript
load(config: BaseAppConfig): Promise<BaseAppInstance>
```

**异常处理**:
- `ADAPTER_LOAD_FAILED`: 应用加载失败
- `INVALID_APP_CONFIG`: 应用配置无效
- `FRAMEWORK_NOT_SUPPORTED`: 框架不支持

**使用示例**:
```typescript
const appInstance = await reactAdapter.load({
  name: 'react-dashboard',
  entry: 'https://cdn.example.com/dashboard.js',
  framework: 'react',
  props: { user: { id: 1, name: 'John' } }
});
```

### mount()

**功能说明**: 将应用挂载到指定的DOM容器中

**方法签名**:
```typescript
mount(appId: string, container: Element): Promise<void>
```

**异常处理**:
- `APP_INSTANCE_NOT_FOUND`: 应用实例不存在
- `MOUNT_FAILED`: 挂载失败
- `INVALID_CONTAINER`: 容器无效

### unmount()

**功能说明**: 从DOM容器中卸载应用

**方法签名**:
```typescript
unmount(appId: string): Promise<void>
```

### update()

**功能说明**: 更新应用配置和属性

**方法签名**:
```typescript
update(appId: string, config: Partial<BaseAppConfig>): Promise<void>
```

### destroy()

**功能说明**: 销毁应用实例并清理所有资源

**方法签名**:
```typescript
destroy(appId: string): Promise<void>
```

## 框架特定适配器

### React 适配器 (@micro-core/adapter-react)

#### 接口定义
```typescript
interface ReactAdapterConfig extends BaseAdapterConfig {
  reactVersion?: '16' | '17' | '18';
  enableDevTools?: boolean;
  strictMode?: boolean;
  errorBoundary?: boolean | ReactErrorBoundaryConfig;
}

interface ReactErrorBoundaryConfig {
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}
```

#### 使用示例
```typescript
import { ReactAdapter } from '@micro-core/adapter-react';

const reactAdapter = new ReactAdapter({
  reactVersion: '18',
  enableDevTools: true,
  errorBoundary: {
    fallback: ({ error, retry }) => (
      <div>
        <h2>应用加载失败</h2>
        <p>{error.message}</p>
        <button onClick={retry}>重试</button>
      </div>
    )
  }
});
```

### Vue 3 适配器 (@micro-core/adapter-vue3)

#### 接口定义
```typescript
interface Vue3AdapterConfig extends BaseAdapterConfig {
  vueVersion?: string;
  enableDevTools?: boolean;
  globalProperties?: Record<string, any>;
  errorHandler?: (error: Error, instance: any, info: string) => void;
}
```

#### 使用示例
```typescript
import { Vue3Adapter } from '@micro-core/adapter-vue3';

const vue3Adapter = new Vue3Adapter({
  vueVersion: '3.3.0',
  enableDevTools: true,
  globalProperties: {
    $http: axios,
    $router: router
  }
});
```

### Vue 2 适配器 (@micro-core/adapter-vue2)

#### 接口定义
```typescript
interface Vue2AdapterConfig extends BaseAdapterConfig {
  vueVersion?: string;
  enableDevTools?: boolean;
  globalConfig?: Partial<VueConfiguration>;
  errorHandler?: (error: Error, vm: any, info: string) => void;
}
```

### Angular 适配器 (@micro-core/adapter-angular)

#### 接口定义
```typescript
interface AngularAdapterConfig extends BaseAdapterConfig {
  angularVersion?: string;
  enableProdMode?: boolean;
  platformOptions?: any;
  zoneConfig?: ZoneConfig;
}
```

### HTML 适配器 (@micro-core/adapter-html)

#### 接口定义
```typescript
interface HtmlAdapterConfig extends BaseAdapterConfig {
  autoCleanup?: boolean;
  enableScriptExecution?: boolean;
  enableStyleIsolation?: boolean;
  scriptLoadTimeout?: number;
}
```

## 适配器工厂模式

### AdapterFactory

```typescript
class AdapterFactory {
  private adapters = new Map<string, BaseAdapter>();
  
  register(framework: string, adapter: BaseAdapter): void;
  create(config: BaseAppConfig): BaseAdapter | null;
  detectFramework(config: BaseAppConfig): string | null;
  getRegisteredAdapters(): Map<string, BaseAdapter>;
}
```

**使用示例**:
```typescript
const factory = new AdapterFactory();
factory.register('react', new ReactAdapter());
factory.register('vue3', new Vue3Adapter());

const adapter = factory.create({
  name: 'my-app',
  entry: 'https://example.com/app.js',
  framework: 'react'
});
```

## 典型使用场景

### 场景1: 多框架应用集成

```typescript
import { AdapterFactory } from '@micro-core/core';
import { ReactAdapter, Vue3Adapter } from '@micro-core/adapters';

const factory = new AdapterFactory();
factory.register('react', new ReactAdapter());
factory.register('vue3', new Vue3Adapter());

const apps = [
  { name: 'header', framework: 'react', entry: '/header.js' },
  { name: 'sidebar', framework: 'vue3', entry: '/sidebar.js' }
];

for (const appConfig of apps) {
  const adapter = factory.create(appConfig);
  const instance = await adapter.load(appConfig);
  await adapter.mount(instance.id, document.querySelector(`#${appConfig.name}`));
}
```

### 场景2: 动态应用管理

```typescript
class DynamicAppManager {
  private adapter: ReactAdapter;
  private loadedApps = new Map<string, string>();

  async loadApp(config: BaseAppConfig): Promise<void> {
    const instance = await this.adapter.load(config);
    this.loadedApps.set(config.name, instance.id);
    
    const container = document.querySelector(config.container as string);
    await this.adapter.mount(instance.id, container);
  }

  async unloadApp(appName: string): Promise<void> {
    const appId = this.loadedApps.get(appName);
    if (appId) {
      await this.adapter.unmount(appId);
      await this.adapter.destroy(appId);
      this.loadedApps.delete(appName);
    }
  }
}
```

## 版本兼容性说明

### 支持的版本范围

| 组件 | 最低版本 | 推荐版本 | 最新版本 |
|------|----------|----------|----------|
| @micro-core/core | 0.1.0 | 0.1.0 | 0.1.0 |
| React | 16.8.0 | 18.2.0 | 18.x |
| Vue 3 | 3.0.0 | 3.3.0 | 3.x |
| Vue 2 | 2.7.0 | 2.7.14 | 2.7.x |
| Angular | 12.0.0 | 15.0.0 | 16.x |

### 适配器兼容性矩阵

| 适配器 | Core 0.1.0 | 向后兼容 | 破坏性变更 |
|--------|------------|----------|------------|
| adapter-react | ✅ | v0.1.x | 无 |
| adapter-vue3 | ✅ | v0.1.x | 无 |
| adapter-vue2 | ✅ | v0.1.x | 无 |
| adapter-angular | ✅ | v0.1.x | 无 |
| adapter-html | ✅ | v0.1.x | 无 |

## 错误代码对照表

### 适配器系统错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `A001` | `ADAPTER_NOT_FOUND` | 适配器不存在 | 检查适配器是否已注册 |
| `A002` | `ADAPTER_LOAD_FAILED` | 适配器加载失败 | 检查应用配置和网络连接 |
| `A003` | `INVALID_APP_CONFIG` | 应用配置无效 | 检查应用配置格式 |
| `A004` | `FRAMEWORK_NOT_SUPPORTED` | 框架不支持 | 使用支持的框架版本 |
| `A005` | `APP_INSTANCE_NOT_FOUND` | 应用实例不存在 | 确保应用已正确加载 |
| `A006` | `MOUNT_FAILED` | 挂载失败 | 检查容器元素和应用状态 |
| `A007` | `UNMOUNT_FAILED` | 卸载失败 | 检查应用生命周期实现 |
| `A008` | `INVALID_CONTAINER` | 容器无效 | 确保容器元素存在且有效 |

### React适配器错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `R001` | `REACT_VERSION_MISMATCH` | React版本不匹配 | 使用兼容的React版本 |
| `R002` | `COMPONENT_RENDER_ERROR` | 组件渲染错误 | 检查组件代码和props |
| `R003` | `HOOK_ERROR` | Hook使用错误 | 确保Hook在函数组件中使用 |
| `R004` | `CONTEXT_ERROR` | Context错误 | 检查Context Provider配置 |

### Vue适配器错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `V001` | `VUE_VERSION_MISMATCH` | Vue版本不匹配 | 使用兼容的Vue版本 |
| `V002` | `COMPONENT_MOUNT_ERROR` | 组件挂载错误 | 检查组件定义和模板 |
| `V003` | `REACTIVE_ERROR` | 响应式数据错误 | 检查数据响应式设置 |
| `V004` | `DIRECTIVE_ERROR` | 指令错误 | 检查自定义指令实现 |

### Angular适配器错误码

| 错误码 | 错误名称 | 说明 | 解决方案 |
|--------|----------|------|----------|
| `N001` | `ANGULAR_VERSION_MISMATCH` | Angular版本不匹配 | 使用兼容的Angular版本 |
| `N002` | `MODULE_BOOTSTRAP_ERROR` | 模块启动错误 | 检查模块配置和依赖 |
| `N003` | `ZONE_ERROR` | Zone.js错误 | 检查Zone.js配置 |
| `N004` | `INJECTION_ERROR` | 依赖注入错误 | 检查服务注入配置 |

## 性能优化建议

### 适配器优化

1. **延迟加载**: 按需加载适配器
```typescript
const loadReactAdapter = async () => {
  const { ReactAdapter } = await import('@micro-core/adapter-react');
  return new ReactAdapter();
};
```

2. **实例复用**: 复用适配器实例
```typescript
const adapterCache = new Map();
const getAdapter = (framework) => {
  if (!adapterCache.has(framework)) {
    adapterCache.set(framework, createAdapter(framework));
  }
  return adapterCache.get(framework);
};
```

3. **预加载策略**: 预加载关键应用
```typescript
const preloadApps = async (configs) => {
  const promises = configs.map(config => adapter.load(config));
  await Promise.all(promises);
};
```

## 调试和诊断

### 适配器调试

```typescript
// 启用调试模式
const adapter = new ReactAdapter({
  enableDebugMode: true,
  logLevel: 'debug'
});

// 监听适配器事件
adapter.on('app:loading', (app) => console.log('应用加载中:', app.name));
adapter.on('app:mounted', (app) => console.log('应用已挂载:', app.name));
adapter.on('app:error', (error) => console.error('适配器错误:', error));
```

### 性能监控

```typescript
// 监控适配器性能
const startTime = performance.now();
await adapter.mount(appId, container);
const mountTime = performance.now() - startTime;
console.log(`挂载耗时: ${mountTime}ms`);
```

---

**文档版本**: v1.0.0  
**最后更新**: 2025年7月  
**维护者**: Echo (<EMAIL>)  
**许可证**: MIT License
