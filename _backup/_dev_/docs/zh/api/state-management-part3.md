# 状态管理 API (第三部分)

## 常见问题与解决方案 (续)

### 性能问题 (续)

```typescript
// 使用节流
const setThrottledTheme = throttleState(store, 'theme', 500);

// 使用节流函数
setThrottledTheme('dark'); // 不会立即更新
setThrottledTheme('light'); // 取消之前的更新，设置新值
setThrottledTheme('dark'); // 取消之前的更新，设置新值

// 优化订阅
// 1. 使用精确的路径
store.subscribe('user.name', (newValue) => {
  console.log('用户名变化:', newValue);
});

// 2. 使用条件订阅
store.subscribe('notifications', (newValue, oldValue) => {
  // 只有当数组长度变化时才更新
  if (newValue.length !== oldValue.length) {
    updateNotificationBadge(newValue.length);
  }
});

// 3. 使用批量处理
store.subscribe('items', (newValue) => {
  // 使用 requestAnimationFrame 批量更新 UI
  cancelAnimationFrame(updateFrame);
  updateFrame = requestAnimationFrame(() => {
    updateItemsList(newValue);
  });
});
```

### 内存泄漏

**问题**：订阅没有正确清理，导致内存泄漏。

**解决方案**：

1. 使用取消订阅函数
2. 实现自动清理
3. 使用弱引用

```typescript
// 使用取消订阅函数
const unsubscribe = store.subscribe('user', (newValue) => {
  console.log('用户变化:', newValue);
});

// 在组件卸载时取消订阅
function unmountComponent() {
  unsubscribe();
}

// 实现自动清理
class Component {
  constructor(store) {
    this.store = store;
    this.subscriptions = [];
  }
  
  subscribe(path, callback) {
    const unsubscribe = this.store.subscribe(path, callback);
    this.subscriptions.push(unsubscribe);
    return unsubscribe;
  }
  
  destroy() {
    // 清理所有订阅
    this.subscriptions.forEach(unsubscribe => unsubscribe());
    this.subscriptions = [];
  }
}

// 使用组件
const component = new Component(store);

component.subscribe('user', (newValue) => {
  console.log('用户变化:', newValue);
});

component.subscribe('theme', (newValue) => {
  console.log('主题变化:', newValue);
});

// 销毁组件
component.destroy();

// 使用弱引用
const subscriptions = new WeakMap();

function subscribeComponent(component, store, path, callback) {
  const unsubscribe = store.subscribe(path, callback);
  
  // 保存取消订阅函数
  if (!subscriptions.has(component)) {
    subscriptions.set(component, []);
  }
  
  subscriptions.get(component).push(unsubscribe);
  
  return unsubscribe;
}

function unsubscribeComponent(component) {
  const subs = subscriptions.get(component);
  
  if (subs) {
    subs.forEach(unsubscribe => unsubscribe());
    subscriptions.delete(component);
  }
}

// 使用
const myComponent = {};

subscribeComponent(myComponent, store, 'user', (newValue) => {
  console.log('用户变化:', newValue);
});

// 当不再需要组件时
unsubscribeComponent(myComponent);
```

### 状态持久化问题

**问题**：状态持久化导致存储空间不足或性能问题。

**解决方案**：

1. 选择性持久化
2. 压缩状态
3. 使用过期时间

```typescript
// 选择性持久化
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: [],
    temporaryData: {}
  },
  
  // 选择性持久化
  persist: {
    enabled: true,
    storage: localStorage,
    key: 'my-app-state',
    include: ['user', 'theme'], // 只持久化这些路径
    exclude: ['user.token'] // 排除这些路径
  }
});

// 压缩状态
const compressPlugin = {
  name: 'compress',
  
  init(store, options) {
    const {
      storage = localStorage,
      key = 'micro-core-store',
      compressionLevel = 9
    } = options || {};
    
    // 从存储中恢复状态
    const compressedState = storage.getItem(key);
    
    if (compressedState) {
      try {
        // 解压缩状态
        const stateStr = LZString.decompressFromUTF16(compressedState);
        const state = JSON.parse(stateStr);
        
        // 恢复状态
        store.restore(state);
      } catch (error) {
        console.error('恢复状态失败:', error);
      }
    }
    
    // 监听状态变化
    store.subscribe('*', () => {
      const state = store.snapshot();
      
      try {
        // 压缩状态
        const stateStr = JSON.stringify(state);
        const compressedState = LZString.compressToUTF16(stateStr);
        
        // 保存状态
        storage.setItem(key, compressedState);
      } catch (error) {
        console.error('保存状态失败:', error);
      }
    });
  }
};

// 使用过期时间
const expirePlugin = {
  name: 'expire',
  
  init(store, options) {
    const {
      storage = localStorage,
      key = 'micro-core-store',
      expires = 24 * 60 * 60 * 1000 // 1天
    } = options || {};
    
    // 从存储中恢复状态
    const savedStateStr = storage.getItem(key);
    
    if (savedStateStr) {
      try {
        const savedState = JSON.parse(savedStateStr);
        
        // 检查过期时间
        if (savedState._expires && Date.now() > savedState._expires) {
          storage.removeItem(key);
        } else {
          // 删除元数据
          delete savedState._expires;
          
          // 恢复状态
          store.restore(savedState);
        }
      } catch (error) {
        console.error('恢复状态失败:', error);
      }
    }
    
    // 监听状态变化
    store.subscribe('*', () => {
      const state = store.snapshot();
      
      // 添加过期时间
      state._expires = Date.now() + expires;
      
      try {
        // 保存状态
        storage.setItem(key, JSON.stringify(state));
      } catch (error) {
        console.error('保存状态失败:', error);
      }
    });
  }
};
```

## 最佳实践

### 状态设计模式

#### 模块化状态

```typescript
// 创建存储
const store = new Store();

// 用户模块
const userModule = {
  state: {
    id: null,
    name: '',
    email: '',
    isLoggedIn: false,
    roles: []
  },
  
  getters: {
    isAdmin: (state) => state.roles.includes('admin'),
    fullName: (state) => `${state.firstName} ${state.lastName}`
  },
  
  actions: {
    login: async ({ setState }, credentials) => {
      try {
        const user = await api.login(credentials);
        
        setState('id', user.id);
        setState('name', user.name);
        setState('email', user.email);
        setState('isLoggedIn', true);
        setState('roles', user.roles);
        
        return user;
      } catch (error) {
        console.error('登录失败:', error);
        throw error;
      }
    },
    
    logout: ({ setState }) => {
      setState('id', null);
      setState('name', '');
      setState('email', '');
      setState('isLoggedIn', false);
      setState('roles', []);
    }
  }
};

// 设置模块
const settingsModule = {
  state: {
    theme: 'light',
    language: 'zh-CN',
    notifications: {
      email: true,
      push: false
    }
  },
  
  getters: {
    isDarkTheme: (state) => state.theme === 'dark'
  },
  
  actions: {
    toggleTheme: ({ getState, setState }) => {
      const currentTheme = getState('theme');
      const newTheme = currentTheme === 'light' ? 'dark' : 'light';
      setState('theme', newTheme);
      return newTheme;
    },
    
    setLanguage: ({ setState }, language) => {
      setState('language', language);
    }
  }
};

// 注册模块
store.registerModule('user', userModule);
store.registerModule('settings', settingsModule);
```

#### 命令模式

```typescript
// 命令接口
interface Command {
  execute(): Promise<any>;
  undo(): Promise<void>;
}

// 登录命令
class LoginCommand implements Command {
  constructor(private store: Store, private credentials: any) {}
  
  async execute() {
    // 保存当前状态用于撤销
    this.previousState = this.store.getState('user');
    
    // 执行登录
    const user = await api.login(this.credentials);
    
    this.store.setState('user', {
      id: user.id,
      name: user.name,
      email: user.email,
      isLoggedIn: true,
      roles: user.roles
    });
    
    return user;
  }
  
  async undo() {
    // 恢复之前的状态
    this.store.setState('user', this.previousState);
  }
}

// 更改主题命令
class ChangeThemeCommand implements Command {
  constructor(private store: Store, private theme: string) {}
  
  async execute() {
    // 保存当前主题用于撤销
    this.previousTheme = this.store.getState('settings.theme');
    
    // 设置新主题
    this.store.setState('settings.theme', this.theme);
    
    return this.theme;
  }
  
  async undo() {
    // 恢复之前的主题
    this.store.setState('settings.theme', this.previousTheme);
  }
}

// 命令管理器
class CommandManager {
  private history: Command[] = [];
  private redoStack: Command[] = [];
  
  async executeCommand(command: Command) {
    try {
      const result = await command.execute();
      this.history.push(command);
      this.redoStack = [];
      return result;
    } catch (error) {
      console.error('命令执行失败:', error);
      throw error;
    }
  }
  
  async undo() {
    if (this.history.length === 0) {
      return false;
    }
    
    const command = this.history.pop();
    await command.undo();
    this.redoStack.push(command);
    
    return true;
  }
  
  async redo() {
    if (this.redoStack.length === 0) {
      return false;
    }
    
    const command = this.redoStack.pop();
    await command.execute();
    this.history.push(command);
    
    return true;
  }
}

// 使用命令
const commandManager = new CommandManager();

// 执行登录命令
const loginCommand = new LoginCommand(store, {
  username: 'admin',
  password: 'password'
});

commandManager.executeCommand(loginCommand)
  .then(user => {
    console.log('登录成功:', user);
  })
  .catch(error => {
    console.error('登录失败:', error);
  });

// 执行更改主题命令
const changeThemeCommand = new ChangeThemeCommand(store, 'dark');

commandManager.executeCommand(changeThemeCommand)
  .then(theme => {
    console.log('主题已更改为:', theme);
  });

// 撤销最后一个命令
commandManager.undo()
  .then(success => {
    if (success) {
      console.log('已撤销更改主题命令');
    }
  });

// 重做命令
commandManager.redo()
  .then(success => {
    if (success) {
      console.log('已重做更改主题命令');
    }
  });
```

#### 观察者模式

```typescript
// 创建存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  }
});

// 观察者接口
interface Observer {
  update(path: string, newValue: any, oldValue: any): void;
}

// 用户观察者
class UserObserver implements Observer {
  update(path: string, newValue: any, oldValue: any) {
    if (path === 'user' || path.startsWith('user.')) {
      console.log('用户状态变化:', path, oldValue, '->', newValue);
      
      // 更新 UI
      this.updateUserUI(newValue);
    }
  }
  
  private updateUserUI(user: any) {
    // 更新用户界面
    if (user.isLoggedIn) {
      showUserProfile(user);
    } else {
      showLoginForm();
    }
  }
}

// 主题观察者
class ThemeObserver implements Observer {
  update(path: string, newValue: any, oldValue: any) {
    if (path === 'theme') {
      console.log('主题变化:', oldValue, '->', newValue);
      
      // 更新主题
      document.body.className = newValue;
    }
  }
}

// 通知观察者
class NotificationObserver implements Observer {
  update(path: string, newValue: any, oldValue: any) {
    if (path === 'notifications' || path.startsWith('notifications.')) {
      console.log('通知变化:', path, oldValue, '->', newValue);
      
      // 更新通知
      if (path === 'notifications' && Array.isArray(newValue)) {
        this.updateNotificationBadge(newValue.length);
        this.showLatestNotification(newValue);
      }
    }
  }
  
  private updateNotificationBadge(count: number) {
    // 更新通知徽章
    const badge = document.getElementById('notification-badge');
    if (badge) {
      badge.textContent = count > 0 ? count.toString() : '';
      badge.style.display = count > 0 ? 'block' : 'none';
    }
  }
  
  private showLatestNotification(notifications: any[]) {
    // 显示最新通知
    if (notifications.length > 0) {
      const latest = notifications[notifications.length - 1];
      if (!latest.read) {
        showToast(latest.text);
      }
    }
  }
}

// 注册观察者
const userObserver = new UserObserver();
const themeObserver = new ThemeObserver();
const notificationObserver = new NotificationObserver();

// 订阅状态变化
store.subscribe('*', (newValue, oldValue, path) => {
  userObserver.update(path, newValue, oldValue);
  themeObserver.update(path, newValue, oldValue);
  notificationObserver.update(path, newValue, oldValue);
});
```

### 状态测试

```typescript
// 测试存储
function testStore() {
  // 创建存储
  const store = new Store({
    state: {
      user: {
        id: null,
        name: '',
        isLoggedIn: false
      },
      theme: 'light',
      counter: 0
    }
  });
  
  // 测试初始状态
  console.assert(store.getState('user.id') === null, '用户 ID 应该为 null');
  console.assert(store.getState('user.name') === '', '用户名应该为空字符串');
  console.assert(store.getState('user.isLoggedIn') === false, '用户应该未登录');
  console.assert(store.getState('theme') === 'light', '主题应该为 light');
  console.assert(store.getState('counter') === 0, '计数器应该为 0');
  
  // 测试设置状态
  store.setState('user.name', 'Admin');
  console.assert(store.getState('user.name') === 'Admin', '用户名应该更新为 Admin');
  
  store.setState('theme', 'dark');
  console.assert(store.getState('theme') === 'dark', '主题应该更新为 dark');
  
  // 测试批量更新
  store.batch(() => {
    store.setState('user.id', 1);
    store.setState('user.isLoggedIn', true);
    store.setState('counter', 10);
  });
  
  console.assert(store.getState('user.id') === 1, '用户 ID 应该更新为 1');
  console.assert(store.getState('user.isLoggedIn') === true, '用户应该已登录');
  console.assert(store.getState('counter') === 10, '计数器应该更新为 10');
  
  // 测试订阅
  let callCount = 0;
  let lastValue = null;
  
  const unsubscribe = store.subscribe('counter', (newValue) => {
    callCount++;
    lastValue = newValue;
  });
  
  store.setState('counter', 20);
  console.assert(callCount === 1, '回调应该被调用一次');
  console.assert(lastValue === 20, '最后的值应该为 20');
  
  store.setState('counter', 30);
  console.assert(callCount === 2, '回调应该被调用两次');
  console.assert(lastValue === 30, '最后的值应该为 30');
  
  // 测试取消订阅
  unsubscribe();
  store.setState('counter', 40);
  console.assert(callCount === 2, '取消订阅后回调不应该被调用');
  console.assert(lastValue === 30, '最后的值应该仍为 30');
  
  // 测试重置状态
  store.resetState('user');
  console.assert(store.getState('user.id') === null, '重置后用户 ID 应该为 null');
  console.assert(store.getState('user.name') === '', '重置后用户名应该为空字符串');
  console.assert(store.getState('user.isLoggedIn') === false, '重置后用户应该未登录');
  
  // 测试快照和恢复
  store.setState('theme', 'light');
  store.setState('counter', 50);
  
  const snapshot = store.snapshot();
  
  store.setState('theme', 'dark');
  store.setState('counter', 60);
  
  console.assert(store.getState('theme') === 'dark', '主题应该为 dark');
  console.assert(store.getState('counter') === 60, '计数器应该为 60');
  
  store.restore(snapshot);
  
  console.assert(store.getState('theme') === 'light', '恢复后主题应该为 light');
  console.assert(store.getState('counter') === 50, '恢复后计数器应该为 50');
  
  console.log('存储测试完成');
}

// 运行测试
testStore();
```

### 状态可视化

```typescript
// 状态可视化
function visualizeState(store) {
  // 创建可视化容器
  const container = document.createElement('div');
  container.className = 'state-visualizer';
  container.style.position = 'fixed';
  container.style.bottom = '10px';
  container.style.right = '10px';
  container.style.width = '300px';
  container.style.maxHeight = '500px';
  container.style.overflow = 'auto';
  container.style.backgroundColor = '#f0f0f0';
  container.style.border = '1px solid #ccc';
  container.style.borderRadius = '4px';
  container.style.padding = '10px';
  container.style.zIndex = '9999';
  
  // 创建标题
  const title = document.createElement('h3');
  title.textContent = 'State Visualizer';
  title.style.margin = '0 0 10px 0';
  container.appendChild(title);
  
  // 创建状态显示区域
  const stateDisplay = document.createElement('pre');
  stateDisplay.style.margin = '0';
  stateDisplay.style.padding = '10px';
  stateDisplay.style.backgroundColor = '#fff';
  stateDisplay.style.border = '1px solid #ddd';
  stateDisplay.style.borderRadius = '4px';
  stateDisplay.style.overflow = 'auto';
  container.appendChild(stateDisplay);
  
  // 创建操作按钮
  const buttonContainer = document.createElement('div');
  buttonContainer.style.marginTop = '10px';
  buttonContainer.style.display = 'flex';
  buttonContainer.style.justifyContent = 'space-between';
  container.appendChild(buttonContainer);
  
  // 创建刷新按钮
  const refreshButton = document.createElement('button');
  refreshButton.textContent = '刷新';
  refreshButton.onclick = updateDisplay;
  buttonContainer.appendChild(refreshButton);
  
  // 创建关闭按钮
  const closeButton = document.createElement('button');
  closeButton.textContent = '关闭';
  closeButton.onclick = () => {
    document.body.removeChild(container);
    unsubscribe();
  };
  buttonContainer.appendChild(closeButton);
  
  // 添加到文档
  document.body.appendChild(container);
  
  // 更新显示
  function updateDisplay() {
    const state = store.getState();
    stateDisplay.textContent = JSON.stringify(state, null, 2);
  }
  
  // 初始更新
  updateDisplay();
  
  // 订阅状态变化
  const unsubscribe = store.subscribe('*', updateDisplay);
  
  // 返回清理函数
  return () => {
    if (container.parentNode) {
      document.body.removeChild(container);
    }
    unsubscribe();
  };
}

// 使用可视化
const cleanup = visualizeState(store);

// 清理可视化
// cleanup();
```

## 总结

Micro-Core 状态管理 API 提供了强大而灵活的状态管理功能，适用于微前端架构中的各种场景。通过本文档，你可以了解状态管理 API 的基本用法、高级特性以及最佳实践，帮助你构建高效、可靠的微前端应用。

主要特性包括：

1. **全局状态管理**：在所有微应用之间共享全局状态
2. **状态同步**：保持不同微应用之间的状态同步
3. **状态持久化**：将状态持久化到存储中
4. **状态变更通知**：通知状态变更的订阅者
5. **模块化状态**：通过模块组织和管理相关的状态
6. **中间件和插件**：通过中间件和插件扩展状态管理功能
7. **与框架集成**：与 React、Vue、Angular 等框架集成

通过合理使用这些功能，你可以构建出状态管理高效、可靠的微前端应用。