# 事件总线 API

Micro-Core 提供了强大的事件总线系统，用于微应用之间的通信和消息传递。

## 基本概念

事件总线是一种发布-订阅模式的实现，允许不同的微应用之间进行松耦合的通信。

## EventBus 类

### 导入和创建

```typescript
import { EventBus } from '@micro-core/core';

// 创建事件总线实例
const eventBus = new EventBus();

// 使用全局事件总线
import { globalEventBus } from '@micro-core/core';
```

### 构造函数

```typescript
new EventBus(options?: EventBusOptions)
```

**参数：**
- `options.scope` (string): 事件作用域
- `options.maxListeners` (number): 最大监听器数量，默认 10

**示例：**

```typescript
const eventBus = new EventBus({
  scope: 'app1',
  maxListeners: 20
});
```

## 核心方法

### on(event, listener)

订阅事件。

```typescript
on<T = any>(event: string, listener: (data: T) => void): () => void
```

**参数：**
- `event` (string): 事件名称
- `listener` (function): 事件监听器函数

**返回值：** 取消订阅的函数

**示例：**

```typescript
// 基本订阅
const unsubscribe = eventBus.on('user:login', (data) => {
  console.log('用户登录:', data);
});

// 带类型的订阅
interface UserData {
  id: number;
  name: string;
}

const unsubscribe = eventBus.on<UserData>('user:login', (data) => {
  console.log(`用户 ${data.name} 登录`);
});

// 取消订阅
unsubscribe();
```

### once(event, listener)

订阅一次性事件。

```typescript
once<T = any>(event: string, listener: (data: T) => void): () => void
```

**示例：**

```typescript
// 只监听一次
eventBus.once('app:ready', () => {
  console.log('应用已就绪');
});
```

### off(event, listener?)

取消事件订阅。

```typescript
off(event: string, listener?: Function): void
```

**示例：**

```typescript
// 取消特定监听器
eventBus.off('user:login', handleUserLogin);

// 取消事件的所有监听器
eventBus.off('user:login');
```

### emit(event, data?)

发布事件。

```typescript
emit<T = any>(event: string, data?: T): void
```

**示例：**

```typescript
// 发布不带数据的事件
eventBus.emit('app:initialized');

// 发布带数据的事件
eventBus.emit('user:login', { 
  id: 1, 
  name: '张三' 
});
```

### clear()

清空所有事件的订阅者。

```typescript
clear(): void
```

## 实用方法

### hasListeners(event)

检查事件是否有监听器。

```typescript
hasListeners(event: string): boolean
```

### getListeners(event)

获取事件的所有监听器。

```typescript
getListeners(event: string): Function[]
```

### setMaxListeners(n)

设置最大监听器数量。

```typescript
setMaxListeners(n: number): void
```

## 事件命名规范

推荐使用以下命名规范：

```typescript
// 系统事件
eventBus.emit('system:app-mounted', { appName: 'user-center' });

// 业务事件 - 使用 域:动作 格式
eventBus.emit('user:login', { userId: 123 });
eventBus.emit('order:created', { orderId: 'ORD001' });

// UI事件
eventBus.emit('ui:modal-opened', { modalId: 'confirm' });

// 错误事件
eventBus.emit('error:api-failed', { endpoint: '/api/users' });
```

## 微应用间通信

### 主应用中的使用

```typescript
import { globalEventBus } from '@micro-core/core';

// 监听微应用事件
globalEventBus.on('app1:data-updated', (data) => {
  console.log('应用1数据更新:', data);
});

// 向微应用发送事件
globalEventBus.emit('main:theme-changed', { theme: 'dark' });
```

### 微应用中的使用

```typescript
// 在微应用的生命周期函数中
export async function mount(props) {
  const { eventBus } = props;
  
  // 监听主应用事件
  eventBus.on('main:theme-changed', (data) => {
    updateTheme(data.theme);
  });
  
  // 向主应用发送事件
  eventBus.emit('app1:ready', { status: 'mounted' });
}
```

## 框架集成

### React Hook

```typescript
import { useEffect, useCallback } from 'react';
import { globalEventBus } from '@micro-core/core';

export function useEventBus<T = any>(
  event: string, 
  handler: (data: T) => void, 
  deps: any[] = []
) {
  useEffect(() => {
    const unsubscribe = globalEventBus.on<T>(event, handler);
    return unsubscribe;
  }, [event, ...deps]);
  
  const emit = useCallback(<D = any>(eventName: string, data?: D) => {
    globalEventBus.emit(eventName, data);
  }, []);
  
  return { emit };
}

// 使用示例
const MyComponent = () => {
  const { emit } = useEventBus('user:updated', (user) => {
    console.log('用户更新:', user);
  });
  
  const handleClick = () => {
    emit('button:clicked', { id: 'btn1' });
  };
  
  return <button onClick={handleClick}>点击</button>;
};
```

### Vue Composable

```typescript
import { onMounted, onUnmounted } from 'vue';
import { globalEventBus } from '@micro-core/core';

export function useEventBus<T = any>(
  event: string, 
  handler?: (data: T) => void
) {
  let unsubscribe: (() => void) | null = null;
  
  onMounted(() => {
    if (handler) {
      unsubscribe = globalEventBus.on<T>(event, handler);
    }
  });
  
  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe();
    }
  });
  
  const emit = <D = any>(eventName: string, data?: D) => {
    globalEventBus.emit(eventName, data);
  };
  
  return { emit };
}
```

## 最佳实践

### 1. 事件命名

- 使用命名空间避免冲突
- 使用动词:名词格式
- 保持命名一致性

### 2. 错误处理

```typescript
eventBus.on('user:login', (data) => {
  try {
    processUserLogin(data);
  } catch (error) {
    console.error('处理用户登录失败:', error);
    eventBus.emit('user:login:error', { error, data });
  }
});
```

### 3. 取消订阅

```typescript
class MyComponent {
  private unsubscribers: Array<() => void> = [];
  
  constructor() {
    this.unsubscribers.push(
      eventBus.on('event1', this.handleEvent1),
      eventBus.on('event2', this.handleEvent2)
    );
  }
  
  destroy() {
    this.unsubscribers.forEach(fn => fn());
    this.unsubscribers = [];
  }
}
```

### 4. 避免事件循环

```typescript
// 不推荐 - 可能导致循环
eventBus.on('data:update', (data) => {
  updateData(data);
  eventBus.emit('data:update', newData); // 危险！
});

// 推荐
eventBus.on('data:update:requested', (data) => {
  const result = updateData(data);
  eventBus.emit('data:update:completed', result);
});
```

## 调试

启用调试模式查看事件活动：

```typescript
const eventBus = new EventBus({
  debug: true
});

// 或者监听所有事件
eventBus.on('*', (data, eventName) => {
  console.log(`事件: ${eventName}`, data);
});
```

## 类型定义

```typescript
interface EventBusOptions {
  scope?: string;
  maxListeners?: number;
  debug?: boolean;
}

type EventListener<T = any> = (data: T) => void;
type Unsubscribe = () => void;
```

## 参考

- [核心 API](./core.md)
- [应用通信指南](../guide/communication.md)
- [最佳实践](../guide/best-practices.md)