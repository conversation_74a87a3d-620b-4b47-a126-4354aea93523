# 路由系统 API

Micro-Core 提供了统一的路由管理系统，支持主应用和子应用的路由协调，实现无缝的页面导航体验。

## RouterPlugin

### 安装和配置

```typescript
import { RouterPlugin } from '@micro-core/plugin-router';

kernel.use(RouterPlugin, {
  mode: 'history',              // 路由模式: 'hash' | 'history'
  base: '/',                    // 基础路径
  linkActiveClass: 'active',    // 激活链接的CSS类
  scrollBehavior: 'smooth',     // 滚动行为
  caseSensitive: false,         // 是否区分大小写
  strict: false,                // 是否严格匹配
  fallback: true                // 是否启用回退模式
});
```

## 路由配置

### 应用路由配置

```typescript
kernel.registerApplication({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user',           // 简单字符串匹配
  routing: {
    basename: '/user',           // 子应用路由基础路径
    mode: 'history',            // 子应用路由模式
    routes: [                   // 子应用路由定义
      { path: '/', component: 'UserHome' },
      { path: '/profile', component: 'UserProfile' },
      { path: '/settings', component: 'UserSettings' }
    ]
  }
});

// 使用正则表达式匹配
kernel.registerApplication({
  name: 'admin-app',
  entry: 'http://localhost:3002',
  container: '#admin-app',
  activeWhen: /^\/admin/,        // 正则表达式匹配
  routing: {
    basename: '/admin'
  }
});

// 使用函数匹配
kernel.registerApplication({
  name: 'dashboard-app',
  entry: 'http://localhost:3003',
  container: '#dashboard-app',
  activeWhen: (location) => {    // 自定义匹配函数
    return location.pathname.startsWith('/dashboard') && 
           location.search.includes('module=analytics');
  },
  routing: {
    basename: '/dashboard'
  }
});
```

## Router 实例

### 获取路由实例

```typescript
import { Router } from '@micro-core/plugin-router';

// 获取全局路由实例
const router = Router.getInstance();

// 或者从内核获取
const router = kernel.getPlugin('router').getRouter();
```

### 路由导航

#### push()

```typescript
push(to: string | RouteLocation, state?: any): Promise<void>
```

导航到新路由。

**参数:**

```typescript
interface RouteLocation {
  path?: string;
  name?: string;
  params?: Record<string, any>;
  query?: Record<string, any>;
  hash?: string;
  state?: any;
}
```

**示例:**

```typescript
// 字符串路径
await router.push('/user/profile');

// 路由对象
await router.push({
  path: '/user/profile',
  query: { tab: 'settings' },
  hash: '#personal'
});

// 命名路由
await router.push({
  name: 'user-profile',
  params: { userId: '123' }
});

// 带状态的导航
await router.push('/dashboard', { from: 'menu' });
```

#### replace()

```typescript
replace(to: string | RouteLocation, state?: any): Promise<void>
```

替换当前路由（不会在历史记录中留下记录）。

**示例:**

```typescript
await router.replace('/login');

await router.replace({
  path: '/error',
  query: { code: '404' }
});
```

#### go()

```typescript
go(delta: number): void
```

在历史记录中前进或后退。

**示例:**

```typescript
router.go(-1);  // 后退一步
router.go(1);   // 前进一步
router.go(-2);  // 后退两步
```

#### back()

```typescript
back(): void
```

后退一步。

**示例:**

```typescript
router.back();
```

#### forward()

```typescript
forward(): void
```

前进一步。

**示例:**

```typescript
router.forward();
```

### 路由信息

#### getCurrentRoute()

```typescript
getCurrentRoute(): RouteInfo
```

获取当前路由信息。

**返回值:**

```typescript
interface RouteInfo {
  path: string;
  name?: string;
  params: Record<string, any>;
  query: Record<string, any>;
  hash: string;
  fullPath: string;
  matched: RouteRecord[];
  meta: Record<string, any>;
  state?: any;
}
```

**示例:**

```typescript
const currentRoute = router.getCurrentRoute();
console.log('当前路径:', currentRoute.path);
console.log('查询参数:', currentRoute.query);
console.log('路由参数:', currentRoute.params);
```

#### getRoutes()

```typescript
getRoutes(): RouteRecord[]
```

获取所有注册的路由记录。

**示例:**

```typescript
const routes = router.getRoutes();
routes.forEach(route => {
  console.log(`路由: ${route.path} -> 应用: ${route.appName}`);
});
```

#### hasRoute()

```typescript
hasRoute(name: string): boolean
```

检查是否存在指定名称的路由。

**示例:**

```typescript
if (router.hasRoute('user-profile')) {
  console.log('用户资料路由存在');
}
```

### 路由匹配

#### resolve()

```typescript
resolve(to: string | RouteLocation): ResolvedRoute
```

解析路由位置。

**返回值:**

```typescript
interface ResolvedRoute {
  path: string;
  name?: string;
  params: Record<string, any>;
  query: Record<string, any>;
  hash: string;
  fullPath: string;
  matched: RouteRecord[];
  appName?: string;
}
```

**示例:**

```typescript
const resolved = router.resolve('/user/profile?tab=settings');
console.log('解析结果:', resolved);

const resolvedNamed = router.resolve({
  name: 'user-profile',
  params: { userId: '123' }
});
```

#### match()

```typescript
match(path: string): RouteMatch | null
```

匹配路径到路由记录。

**示例:**

```typescript
const match = router.match('/user/profile/123');
if (match) {
  console.log('匹配的应用:', match.appName);
  console.log('路由参数:', match.params);
}
```

## 路由守卫

### 全局前置守卫

```typescript
beforeEach(guard: NavigationGuard): () => void
```

注册全局前置守卫。

**参数:**

```typescript
type NavigationGuard = (
  to: RouteInfo,
  from: RouteInfo,
  next: NavigationGuardNext
) => void | Promise<void>;

type NavigationGuardNext = (
  to?: string | RouteLocation | boolean | Error
) => void;
```

**示例:**

```typescript
// 身份验证守卫
const removeGuard = router.beforeEach(async (to, from, next) => {
  console.log(`导航从 ${from.path} 到 ${to.path}`);
  
  // 检查是否需要身份验证
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
    return;
  }
  
  // 检查权限
  if (to.meta.roles && !hasPermission(to.meta.roles)) {
    next('/403');
    return;
  }
  
  next();
});

// 移除守卫
removeGuard();
```

### 全局后置钩子

```typescript
afterEach(hook: NavigationHook): () => void
```

注册全局后置钩子。

**参数:**

```typescript
type NavigationHook = (to: RouteInfo, from: RouteInfo) => void;
```

**示例:**

```typescript
const removeHook = router.afterEach((to, from) => {
  console.log(`导航完成: ${from.path} -> ${to.path}`);
  
  // 发送页面浏览统计
  analytics.track('page_view', {
    path: to.path,
    appName: to.matched[0]?.appName,
    timestamp: Date.now()
  });
  
  // 更新页面标题
  document.title = to.meta.title || 'Micro-Core App';
});
```

### 应用级路由守卫

```typescript
// 为特定应用注册路由守卫
router.beforeEach((to, from, next) => {
  // 只对用户应用的路由生效
  if (to.path.startsWith('/user')) {
    // 检查用户权限
    if (!hasUserPermission()) {
      next('/login');
      return;
    }
  }
  
  next();
});
```

## 路由事件

### 监听路由变化

```typescript
// 监听路由变化事件
router.on('route:change', (to, from) => {
  console.log('路由变化:', { to: to.path, from: from.path });
});

// 监听路由错误
router.on('route:error', (error, to, from) => {
  console.error('路由错误:', error);
});

// 监听应用激活
router.on('app:activate', (appName, route) => {
  console.log(`应用 ${appName} 被激活，路由: ${route.path}`);
});

// 监听应用停用
router.on('app:deactivate', (appName, route) => {
  console.log(`应用 ${appName} 被停用，路由: ${route.path}`);
});
```

## 路由工具函数

### createRouterLink()

```typescript
createRouterLink(to: string | RouteLocation, options?: LinkOptions): HTMLAnchorElement
```

创建路由链接元素。

**参数:**

```typescript
interface LinkOptions {
  text?: string;
  className?: string;
  activeClass?: string;
  exactActiveClass?: string;
  target?: string;
  replace?: boolean;
}
```

**示例:**

```typescript
const link = router.createRouterLink('/user/profile', {
  text: '用户资料',
  className: 'nav-link',
  activeClass: 'active'
});

document.body.appendChild(link);
```

### isRouteActive()

```typescript
isRouteActive(route: string | RouteLocation, exact?: boolean): boolean
```

检查路由是否处于激活状态。

**示例:**

```typescript
if (router.isRouteActive('/user')) {
  console.log('用户模块处于激活状态');
}

// 精确匹配
if (router.isRouteActive('/user/profile', true)) {
  console.log('当前正在用户资料页面');
}
```

### generateUrl()

```typescript
generateUrl(to: string | RouteLocation): string
```

生成完整的URL。

**示例:**

```typescript
const url = router.generateUrl({
  path: '/user/profile',
  query: { tab: 'settings' }
});
console.log('生成的URL:', url); // /user/profile?tab=settings
```

## 路由缓存

### 启用路由缓存

```typescript
kernel.use(RouterPlugin, {
  cache: {
    enabled: true,
    maxSize: 10,              // 最大缓存数量
    ttl: 5 * 60 * 1000,      // 缓存时间 (5分钟)
    strategy: 'lru'           // 缓存策略: 'lru' | 'fifo'
  }
});
```

### 缓存控制

```typescript
// 清除特定路由的缓存
router.clearCache('/user/profile');

// 清除所有缓存
router.clearAllCache();

// 预加载路由
await router.preloadRoute('/dashboard');

// 检查路由是否已缓存
if (router.isCached('/user/profile')) {
  console.log('路由已缓存');
}
```

## 路由动画

### 配置路由动画

```typescript
kernel.use(RouterPlugin, {
  transition: {
    name: 'fade',             // 动画名称
    mode: 'out-in',           // 动画模式
    duration: 300,            // 动画时长
    enterActiveClass: 'fade-enter-active',
    leaveActiveClass: 'fade-leave-active',
    enterFromClass: 'fade-enter-from',
    leaveToClass: 'fade-leave-to'
  }
});
```

### 自定义路由动画

```typescript
router.setTransition((to, from) => {
  // 根据路由方向选择不同的动画
  if (to.meta.level > from.meta.level) {
    return 'slide-left';
  } else if (to.meta.level < from.meta.level) {
    return 'slide-right';
  } else {
    return 'fade';
  }
});
```

## 路由懒加载

### 配置懒加载

```typescript
kernel.registerApplication({
  name: 'lazy-app',
  entry: () => import('http://localhost:3001/app.js'), // 懒加载入口
  container: '#lazy-app',
  activeWhen: '/lazy',
  routing: {
    lazy: true,
    preload: false,           // 是否预加载
    timeout: 10000           // 加载超时时间
  }
});
```

### 懒加载控制

```typescript
// 预加载懒加载应用
await router.preloadLazyApp('lazy-app');

// 检查懒加载应用状态
const status = router.getLazyAppStatus('lazy-app');
console.log('懒加载应用状态:', status);
```

## 路由调试

### 开启调试模式

```typescript
kernel.use(RouterPlugin, {
  debug: process.env.NODE_ENV === 'development',
  debugLevel: 'verbose'      // 'silent' | 'error' | 'warn' | 'info' | 'verbose'
});
```

### 调试工具

```typescript
// 获取路由调试信息
const debugInfo = router.getDebugInfo();
console.log('路由调试信息:', debugInfo);

// 导出路由历史
const history = router.exportHistory();
console.log('路由历史:', history);

// 路由性能分析
const performance = router.getPerformanceMetrics();
console.log('路由性能:', performance);
```

## 最佳实践

### 1. 路由命名规范

```typescript
// 推荐的路由命名规范
const routes = [
  { path: '/user', name: 'user-home', appName: 'user-app' },
  { path: '/user/profile', name: 'user-profile', appName: 'user-app' },
  { path: '/user/settings', name: 'user-settings', appName: 'user-app' },
  { path: '/admin', name: 'admin-home', appName: 'admin-app' },
  { path: '/admin/users', name: 'admin-users', appName: 'admin-app' }
];
```

### 2. 路由元信息

```typescript
kernel.registerApplication({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user',
  routing: {
    routes: [
      {
        path: '/profile',
        name: 'user-profile',
        meta: {
          title: '用户资料',
          requiresAuth: true,
          roles: ['user', 'admin'],
          level: 2,
          keepAlive: true
        }
      }
    ]
  }
});
```

### 3. 错误处理

```typescript
router.onError((error, to, from) => {
  console.error('路由错误:', error);
  
  // 根据错误类型进行处理
  if (error.type === 'NavigationDuplicated') {
    // 重复导航，忽略
    return;
  }
  
  if (error.type === 'NavigationAborted') {
    // 导航被中止
    console.warn('导航被中止:', to.path);
    return;
  }
  
  if (error.type === 'NavigationCancelled') {
    // 导航被取消
    console.warn('导航被取消:', to.path);
    return;
  }
  
  // 其他错误，导航到错误页面
  router.replace('/error');
});
```

### 4. 性能优化

```typescript
// 路由预加载策略
router.beforeEach(async (to, from, next) => {
  // 预加载下一个可能访问的路由
  const nextRoutes = getPredictedRoutes(to.path);
  nextRoutes.forEach(route => {
    router.preloadRoute(route);
  });
  
  next();
});

// 路由缓存策略
router.beforeEach((to, from, next) => {
  // 对于频繁访问的路由启用缓存
  if (isFrequentRoute(to.path)) {
    to.meta.keepAlive = true;
  }
  
  next();
});
```
