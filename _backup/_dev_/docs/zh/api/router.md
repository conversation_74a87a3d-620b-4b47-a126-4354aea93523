# 路由 API

Micro-Core 提供了强大的路由系统，用于管理微应用的路由和导航。本文档详细介绍了路由 API 的使用方法和最佳实践。

## 基本概念

在微前端架构中，路由系统负责以下任务：

1. **路由匹配**：根据当前 URL 确定应该激活哪个微应用
2. **路由导航**：在不同微应用之间进行导航
3. **路由状态同步**：保持主应用和微应用之间的路由状态同步
4. **路由拦截**：拦截路由变化，执行权限检查等逻辑

## Router

`Router` 是 Micro-Core 提供的核心路由类，用于创建和管理路由系统。

### 基本用法

```typescript
import { Router } from '@micro-core/core';

// 创建路由实例
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    {
      path: '/',
      name: 'home',
      component: 'home-app'
    },
    {
      path: '/users/:userId',
      name: 'user-detail',
      component: 'user-app'
    },
    {
      path: '/settings',
      name: 'settings',
      component: 'settings-app',
      meta: {
        requiresAuth: true
      }
    }
  ]
});

// 监听路由变化
router.beforeEach((to, from, next) => {
  console.log(`路由从 ${from.path} 变化到 ${to.path}`);
  
  // 检查权限
  if (to.meta.requiresAuth && !isAuthenticated()) {
    // 重定向到登录页
    next('/login');
  } else {
    // 继续导航
    next();
  }
});

// 导航到指定路由
router.push('/users/123');

// 替换当前路由
router.replace('/settings');

// 获取当前路由
const currentRoute = router.currentRoute;
console.log('当前路径:', currentRoute.path);
console.log('路由参数:', currentRoute.params);
console.log('查询参数:', currentRoute.query);
```

### API 参考

#### 构造函数

创建路由实例。

```typescript
constructor(options: RouterOptions)
```

**参数：**
- `options` (RouterOptions): 路由配置选项
  - `mode` (string): 路由模式，可选值为 'hash' 或 'history'，默认为 'history'
  - `base` (string): 应用的基础路径，默认为 '/'
  - `routes` (RouteConfig[]): 路由配置数组
  - `scrollBehavior` (Function): 控制滚动行为的函数
  - `parseQuery` (Function): 自定义查询字符串解析函数
  - `stringifyQuery` (Function): 自定义查询字符串生成函数
  - `fallback` (boolean): 当浏览器不支持 history.pushState 时是否回退到 hash 模式，默认为 true

**示例：**

```typescript
// 基本用法
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    { path: '/', component: 'home-app' },
    { path: '/users/:userId', component: 'user-app' }
  ]
});

// 完整配置
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    {
      path: '/',
      name: 'home',
      component: 'home-app',
      meta: { title: '首页' }
    },
    {
      path: '/users/:userId',
      name: 'user-detail',
      component: 'user-app',
      props: true,
      meta: { title: '用户详情' }
    },
    {
      path: '/settings',
      name: 'settings',
      component: 'settings-app',
      meta: { requiresAuth: true, title: '设置' },
      children: [
        {
          path: 'profile',
          name: 'profile',
          component: 'profile-app',
          meta: { title: '个人资料' }
        },
        {
          path: 'security',
          name: 'security',
          component: 'security-app',
          meta: { title: '安全设置' }
        }
      ]
    },
    {
      path: '*',
      component: 'not-found-app'
    }
  ],
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { x: 0, y: 0 };
    }
  },
  parseQuery: (query) => {
    // 自定义查询字符串解析
    return customParseQuery(query);
  },
  stringifyQuery: (query) => {
    // 自定义查询字符串生成
    return customStringifyQuery(query);
  },
  fallback: true
});
```

#### push(location, onComplete?, onAbort?)

导航到指定路由。

```typescript
push(location: RawLocation, onComplete?: Function, onAbort?: Function): void
```

**参数：**
- `location` (RawLocation): 目标路由位置，可以是字符串路径或者路由位置对象
- `onComplete` (Function): 可选，导航成功完成时的回调函数
- `onAbort` (Function): 可选，导航中止时的回调函数

**示例：**

```typescript
// 使用字符串路径
router.push('/users/123');

// 使用路由位置对象
router.push({
  path: '/users/123',
  query: { tab: 'profile' }
});

// 使用命名路由
router.push({
  name: 'user-detail',
  params: { userId: '123' },
  query: { tab: 'profile' }
});

// 带回调函数
router.push('/settings', 
  () => {
    console.log('导航成功完成');
    showSettingsUI();
  },
  (error) => {
    console.error('导航被中止:', error);
    showErrorMessage(error.message);
  }
);
```

#### replace(location, onComplete?, onAbort?)

替换当前路由。

```typescript
replace(location: RawLocation, onComplete?: Function, onAbort?: Function): void
```

**参数：**
- `location` (RawLocation): 目标路由位置，可以是字符串路径或者路由位置对象
- `onComplete` (Function): 可选，导航成功完成时的回调函数
- `onAbort` (Function): 可选，导航中止时的回调函数

**示例：**

```typescript
// 使用字符串路径
router.replace('/users/123');

// 使用路由位置对象
router.replace({
  path: '/users/123',
  query: { tab: 'profile' }
});

// 使用命名路由
router.replace({
  name: 'user-detail',
  params: { userId: '123' },
  query: { tab: 'profile' }
});

// 带回调函数
router.replace('/settings', 
  () => {
    console.log('导航成功完成');
    showSettingsUI();
  },
  (error) => {
    console.error('导航被中止:', error);
    showErrorMessage(error.message);
  }
);
```

#### go(n)

导航到历史堆栈中的某个位置。

```typescript
go(n: number): void
```

**参数：**
- `n` (number): 相对于当前页面的历史堆栈位置，正数表示前进，负数表示后退

**示例：**

```typescript
// 后退一步
router.go(-1);

// 前进一步
router.go(1);

// 前进两步
router.go(2);

// 后退两步
router.go(-2);
```

#### back()

后退一步。

```typescript
back(): void
```

**参数：** 无

**示例：**

```typescript
// 后退一步
router.back();

// 在用户点击后退按钮时使用
backButton.addEventListener('click', () => {
  router.back();
});
```

#### forward()

前进一步。

```typescript
forward(): void
```

**参数：** 无

**示例：**

```typescript
// 前进一步
router.forward();

// 在用户点击前进按钮时使用
forwardButton.addEventListener('click', () => {
  router.forward();
});
```

#### beforeEach(guard)

添加全局前置守卫。

```typescript
beforeEach(guard: NavigationGuard): Function
```

**参数：**
- `guard` (NavigationGuard): 导航守卫函数，接收 to、from 和 next 三个参数

**返回值：** 移除已注册守卫的函数

**示例：**

```typescript
// 添加全局前置守卫
const unregister = router.beforeEach((to, from, next) => {
  console.log(`路由从 ${from.path} 变化到 ${to.path}`);
  
  // 检查权限
  if (to.meta.requiresAuth && !isAuthenticated()) {
    // 重定向到登录页
    next('/login');
  } else {
    // 继续导航
    next();
  }
});

// 移除守卫
unregister();

// 权限检查守卫
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth) {
    if (!isAuthenticated()) {
      // 保存目标路由
      saveTargetRoute(to.fullPath);
      // 重定向到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      });
    } else if (to.meta.roles && !hasRole(to.meta.roles)) {
      // 检查角色权限
      next('/403'); // 无权限页面
    } else {
      // 有权限，继续导航
      next();
    }
  } else {
    // 不需要权限，继续导航
    next();
  }
});

// 进度条守卫
router.beforeEach((to, from, next) => {
  // 启动进度条
  startProgressBar();
  next();
});
```

#### afterEach(hook)

添加全局后置钩子。

```typescript
afterEach(hook: (to: Route, from: Route) => void): Function
```

**参数：**
- `hook` ((to: Route, from: Route) => void): 后置钩子函数，接收 to 和 from 两个参数

**返回值：** 移除已注册钩子的函数

**示例：**

```typescript
// 添加全局后置钩子
const unregister = router.afterEach((to, from) => {
  console.log(`路由导航完成: ${from.path} -> ${to.path}`);
  
  // 更新页面标题
  document.title = to.meta.title || '默认标题';
});

// 移除钩子
unregister();

// 进度条钩子
router.afterEach((to, from) => {
  // 结束进度条
  stopProgressBar();
});

// 分析钩子
router.afterEach((to, from) => {
  // 发送页面浏览事件
  trackPageView({
    path: to.path,
    title: to.meta.title,
    referrer: from.path
  });
});
```

#### beforeResolve(guard)

添加全局解析守卫。

```typescript
beforeResolve(guard: NavigationGuard): Function
```

**参数：**
- `guard` (NavigationGuard): 导航守卫函数，接收 to、from 和 next 三个参数

**返回值：** 移除已注册守卫的函数

**示例：**

```typescript
// 添加全局解析守卫
const unregister = router.beforeResolve((to, from, next) => {
  // 在导航被确认之前，组件已经被解析
  console.log('路由解析中...');
  
  // 异步数据预加载
  if (to.meta.fetchData) {
    loadAsyncData(to.params.id)
      .then(data => {
        // 将数据附加到路由上
        to.meta.data = data;
        next();
      })
      .catch(error => {
        console.error('数据加载失败:', error);
        next(false); // 中止导航
      });
  } else {
    next();
  }
});

// 移除守卫
unregister();
```

#### onError(callback)

注册错误处理回调。

```typescript
onError(callback: (error: Error) => void): Function
```

**参数：**
- `callback` ((error: Error) => void): 错误处理回调函数

**返回值：** 移除已注册回调的函数

**示例：**

```typescript
// 注册错误处理回调
const unregister = router.onError((error) => {
  console.error('路由错误:', error);
  
  // 显示错误通知
  showErrorNotification(`路由导航失败: ${error.message}`);
  
  // 上报错误
  reportError({
    type: 'router_error',
    message: error.message,
    stack: error.stack
  });
});

// 移除回调
unregister();
```

#### addRoutes(routes)

动态添加路由规则。

```typescript
addRoutes(routes: RouteConfig[]): void
```

**参数：**
- `routes` (RouteConfig[]): 路由配置数组

**示例：**

```typescript
// 动态添加路由
router.addRoutes([
  {
    path: '/admin',
    name: 'admin',
    component: 'admin-app',
    meta: { requiresAuth: true, roles: ['admin'] },
    children: [
      {
        path: 'users',
        name: 'admin-users',
        component: 'admin-users-app'
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: 'admin-settings-app'
      }
    ]
  }
]);

// 基于用户角色动态添加路由
function addRoutesBasedOnUserRole(role) {
  let routes = [];
  
  if (role === 'admin') {
    routes = adminRoutes;
  } else if (role === 'manager') {
    routes = managerRoutes;
  } else {
    routes = userRoutes;
  }
  
  router.addRoutes(routes);
}

// 在用户登录后调用
function onUserLogin(user) {
  addRoutesBasedOnUserRole(user.role);
}
```

#### getMatchedComponents(location?)

返回指定路由匹配的组件数组。

```typescript
getMatchedComponents(location?: RawLocation): Array<string>
```

**参数：**
- `location` (RawLocation): 可选，路由位置，默认为当前路由

**返回值：** 匹配的组件数组

**示例：**

```typescript
// 获取当前路由匹配的组件
const components = router.getMatchedComponents();
console.log('当前路由匹配的组件:', components);

// 获取指定路由匹配的组件
const components = router.getMatchedComponents('/users/123');
console.log('指定路由匹配的组件:', components);

// 预加载组件
function preloadComponents(path) {
  const components = router.getMatchedComponents(path);
  components.forEach(component => {
    // 预加载组件
    loadComponent(component);
  });
}
```

#### resolve(location, current?, append?)

解析目标位置。

```typescript
resolve(location: RawLocation, current?: Route, append?: boolean): {
  location: Location;
  route: Route;
  href: string;
}
```

**参数：**
- `location` (RawLocation): 目标路由位置
- `current` (Route): 可选，当前路由，默认为当前路由
- `append` (boolean): 可选，是否追加路径，默认为 false

**返回值：** 包含解析后的位置、路由和 href 的对象

**示例：**

```typescript
// 解析路由
const resolved = router.resolve('/users/123');
console.log('解析后的 href:', resolved.href);
console.log('解析后的路由:', resolved.route);

// 解析命名路由
const resolved = router.resolve({
  name: 'user-detail',
  params: { userId: '123' }
});
console.log('解析后的 href:', resolved.href);

// 在生成链接时使用
function generateLink(location) {
  const resolved = router.resolve(location);
  return resolved.href;
}

const userLink = generateLink({
  name: 'user-detail',
  params: { userId: '123' }
});
console.log('用户链接:', userLink);
```

## 路由配置

### 路由模式

Micro-Core 路由支持两种模式：

1. **Hash 模式**：使用 URL 的 hash 部分（`#` 后面的部分）来模拟完整的 URL
2. **History 模式**：使用 HTML5 History API 来实现真实的 URL

```typescript
// Hash 模式
const router = new Router({
  mode: 'hash',
  routes: [...]
});
// URL 示例: https://example.com/#/users/123

// History 模式
const router = new Router({
  mode: 'history',
  routes: [...]
});
// URL 示例: https://example.com/users/123
```

### 路由匹配

路由匹配支持多种模式：

1. **静态路径**：完全匹配指定的路径
2. **动态路径参数**：匹配路径的一部分，并将其作为参数
3. **嵌套路由**：通过父子关系定义嵌套的路由结构
4. **命名视图**：同时显示多个视图
5. **通配符**：匹配任意路径

```typescript
const router = new Router({
  routes: [
    // 静态路径
    { path: '/home', component: 'home-app' },
    
    // 动态路径参数
    { path: '/users/:userId', component: 'user-app' },
    
    // 可选参数
    { path: '/posts/:postId?', component: 'post-app' },
    
    // 多参数
    { path: '/categories/:categoryId/products/:productId', component: 'product-app' },
    
    // 嵌套路由
    {
      path: '/settings',
      component: 'settings-app',
      children: [
        { path: 'profile', component: 'profile-app' },
        { path: 'security', component: 'security-app' }
      ]
    },
    
    // 命名视图
    {
      path: '/dashboard',
      components: {
        default: 'dashboard-main-app',
        sidebar: 'dashboard-sidebar-app',
        header: 'dashboard-header-app'
      }
    },
    
    // 通配符
    { path: '*', component: 'not-found-app' }
  ]
});
```

### 路由元信息

路由元信息允许你将任意信息附加到路由上：

```typescript
const router = new Router({
  routes: [
    {
      path: '/admin',
      component: 'admin-app',
      meta: {
        requiresAuth: true,
        roles: ['admin'],
        title: '管理后台',
        transition: 'fade',
        keepAlive: true
      }
    }
  ]
});

// 在导航守卫中使用元信息
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '默认标题';
  
  // 检查权限
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
  } else if (to.meta.roles && !hasRole(to.meta.roles)) {
    next('/403');
  } else {
    next();
  }
});
```

### 路由传参

路由传参有多种方式：

1. **动态路径参数**：通过路径传递参数
2. **查询参数**：通过 URL 查询字符串传递参数
3. **Props 传参**：将路由参数作为组件的 props

```typescript
const router = new Router({
  routes: [
    // 动态路径参数
    {
      path: '/users/:userId',
      component: 'user-app'
    },
    
    // Props 传参 (布尔模式)
    {
      path: '/products/:productId',
      component: 'product-app',
      props: true // 将路由参数作为组件的 props
    },
    
    // Props 传参 (对象模式)
    {
      path: '/about',
      component: 'about-app',
      props: { version: '1.0.0' } // 传递静态值
    },
    
    // Props 传参 (函数模式)
    {
      path: '/search',
      component: 'search-app',
      props: (route) => ({
        query: route.query.q,
        page: Number(route.query.page) || 1,
        filters: route.query.filters
      })
    }
  ]
});

// 导航时传递参数
router.push({
  path: '/users/123',
  query: { tab: 'profile', mode: 'edit' }
});

// 在组件中访问参数
// 路径参数: this.$route.params.userId
// 查询参数: this.$route.query.tab, this.$route.query.mode
```

## 导航守卫

导航守卫用于控制导航的行为，例如权限检查、数据预加载等。

### 全局守卫

全局守卫应用于所有路由导航：

```typescript
// 全局前置守卫
router.beforeEach((to, from, next) => {
  console.log(`导航开始: ${from.path} -> ${to.path}`);
  
  // 权限检查
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
  } else {
    next();
  }
});

// 全局解析守卫
router.beforeResolve((to, from, next) => {
  console.log('路由解析中...');
  
  // 数据预加载
  if (to.meta.fetchData) {
    loadData()
      .then(() => next())
      .catch(() => next(false));
  } else {
    next();
  }
});

// 全局后置钩子
router.afterEach((to, from) => {
  console.log(`导航完成: ${from.path} -> ${to.path}`);
  
  // 更新页面标题
  document.title = to.meta.title || '默认标题';
  
  // 发送分析事件
  trackPageView(to.path);
});
```

### 路由独享守卫

路由独享守卫只应用于特定路由：

```typescript
const router = new Router({
  routes: [
    {
      path: '/admin',
      component: 'admin-app',
      beforeEnter: (to, from, next) => {
        // 检查是否是管理员
        if (isAdmin()) {
          next();
        } else {
          next('/403');
        }
      }
    }
  ]
});
```

### 组件内守卫

组件内守卫在组件内部定义：

```typescript
const UserComponent = {
  template: '...',
  
  // 组件进入前
  beforeRouteEnter(to, from, next) {
    // 在渲染该组件的对应路由被确认前调用
    // 此时组件实例还未被创建，不能访问 this
    
    // 加载用户数据
    fetchUser(to.params.userId)
      .then(user => {
        // 通过回调访问组件实例
        next(vm => {
          vm.user = user;
        });
      })
      .catch(error => {
        next(false);
      });
  },
  
  // 组件更新前
  beforeRouteUpdate(to, from, next) {
    // 在当前路由改变，但该组件被复用时调用
    // 可以访问 this
    
    // 保存滚动位置
    const scrollPosition = this.saveScrollPosition();
    
    // 更新用户数据
    this.fetchUser(to.params.userId)
      .then(() => {
        next();
        // 恢复滚动位置
        this.$nextTick(() => {
          this.restoreScrollPosition(scrollPosition);
        });
      })
      .catch(error => {
        next(false);
      });
  },
  
  // 组件离开前
  beforeRouteLeave(to, from, next) {
    // 在导航离开该组件的对应路由时调用
    // 可以访问 this
    
    // 检查是否有未保存的更改
    if (this.hasUnsavedChanges) {
      // 显示确认对话框
      const confirmed = window.confirm('有未保存的更改，确定要离开吗？');
      if (confirmed) {
        next();
      } else {
        next(false);
      }
    } else {
      next();
    }
  }
};
```

## 路由懒加载

路由懒加载允许你按需加载路由组件，提高应用性能：

```typescript
const router = new Router({
  routes: [
    {
      path: '/',
      component: 'home-app' // 直接加载
    },
    {
      path: '/users',
      component: () => loadMicroApp('users-app') // 懒加载
    },
    {
      path: '/settings',
      component: () => {
        // 显示加载指示器
        showLoadingIndicator();
        
        // 加载组件
        return loadMicroApp('settings-app')
          .finally(() => {
            // 隐藏加载指示器
            hideLoadingIndicator();
          });
      }
    },
    {
      path: '/admin',
      component: () => {
        // 带超时的懒加载
        return Promise.race([
          loadMicroApp('admin-app'),
          new Promise((_, reject) => {
            setTimeout(() => reject(new Error('加载超时')), 5000);
          })
        ]);
      }
    }
  ]
});
```

## 滚动行为

控制页面滚动行为：

```typescript
const router = new Router({
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置，则恢复到保存的位置
    if (savedPosition) {
      return savedPosition;
    }
    
    // 如果有哈希，则滚动到锚点
    if (to.hash) {
      return { selector: to.hash };
    }
    
    // 否则滚动到顶部
    return { x: 0, y: 0 };
  }
});

// 更复杂的滚动行为
const router = new Router({
  scrollBehavior(to, from, savedPosition) {
    // 延迟滚动
    return new Promise((resolve) => {
      setTimeout(() => {
        if (savedPosition) {
          resolve(savedPosition);
        } else if (to.hash) {
          resolve({ selector: to.hash, behavior: 'smooth' });
        } else {
          resolve({ x: 0, y: 0, behavior: 'smooth' });
        }
      }, 500);
    });
  }
});
```

## 路由过渡效果

为路由切换添加过渡效果：

```typescript
// 在主应用中
function renderApp(route) {
  const app = document.getElementById('app');
  
  // 应用过渡效果
  const transition = route.meta.transition || 'fade';
  app.className = `transition-${transition}-enter`;
  
  // 加载微应用
  loadMicroApp(route.component)
    .then(microApp => {
      // 渲染微应用
      microApp.mount(app);
      
      // 完成过渡
      setTimeout(() => {
        app.className = `transition-${transition}-enter-active`;
      }, 20);
      
      setTimeout(() => {
        app.className = '';
      }, 300);
    });
}

// 监听路由变化
router.afterEach((to, from) => {
  renderApp(to);
});
```

## 路由模式

### History 模式

使用 HTML5 History API 实现真实的 URL：

```typescript
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [...]
});
```

**注意事项：**

1. 需要服务器配置支持，所有路由都应该返回同一个 HTML 文件
2. 服务器配置示例：

```nginx
# Nginx 配置
location /app/ {
  try_files $uri $uri/ /app/index.html;
}
```

```apache
# Apache 配置