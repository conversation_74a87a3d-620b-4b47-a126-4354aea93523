# 事件系统 API

Micro-Core 的事件系统提供了强大的应用间通信机制，支持发布-订阅模式、事件冒泡、事件拦截等高级功能，确保微前端应用之间的高效协作。

## 核心概念

### EventBus 事件总线

`EventBus` 是 Micro-Core 事件系统的核心，负责管理所有事件的发布、订阅和分发。

```typescript
import { EventBus } from '@micro-core/core';

// 获取全局事件总线实例
const eventBus = EventBus.getInstance();

// 或者创建独立的事件总线
const customEventBus = new EventBus({
  namespace: 'my-app',
  debug: true
});
```

### 事件类型

Micro-Core 支持多种类型的事件：

```typescript
// 系统事件 - 由框架自动触发
interface SystemEvent {
  type: 'system';
  name: 'app-mounted' | 'app-unmounted' | 'route-changed' | 'state-updated';
  source: string;
  timestamp: number;
  data?: any;
}

// 应用事件 - 由微应用主动触发
interface AppEvent {
  type: 'app';
  name: string;
  source: string;
  target?: string | string[];
  data?: any;
  bubbles?: boolean;
  cancelable?: boolean;
}

// 自定义事件 - 用户定义的事件类型
interface CustomEvent {
  type: 'custom';
  name: string;
  namespace?: string;
  data?: any;
  meta?: Record<string, any>;
}
```

## EventBus API

### 构造函数

```typescript
const eventBus = new EventBus(options?: EventBusOptions);

interface EventBusOptions {
  // 事件总线命名空间
  namespace?: string;
  
  // 调试模式
  debug?: boolean;
  
  // 最大监听器数量
  maxListeners?: number;
  
  // 事件历史记录大小
  historySize?: number;
  
  // 是否启用事件拦截
  enableInterception?: boolean;
  
  // 默认事件配置
  defaultEventConfig?: {
    bubbles?: boolean;
    cancelable?: boolean;
    timeout?: number;
  };
}
```

### 事件订阅

#### on() - 订阅事件

```typescript
// 基础订阅
eventBus.on('user-login', (event) => {
  console.log('用户登录:', event.data);
});

// 带选项的订阅
eventBus.on('data-updated', (event) => {
  // 处理数据更新
}, {
  once: false,        // 是否只执行一次
  priority: 1,        // 执行优先级 (数字越大优先级越高)
  namespace: 'app1',  // 命名空间过滤
  condition: (event) => event.data.id > 100  // 条件过滤
});

// 使用通配符订阅
eventBus.on('user.*', (event) => {
  console.log('用户相关事件:', event.name);
});

// 订阅多个事件
eventBus.on(['login', 'logout', 'register'], (event) => {
  console.log('认证事件:', event.name);
});
```

#### once() - 一次性订阅

```typescript
// 只监听一次
eventBus.once('app-ready', (event) => {
  console.log('应用已就绪');
});

// 等价于 on() 方法的 once 选项
eventBus.on('app-ready', handler, { once: true });
```

#### off() - 取消订阅

```typescript
// 取消特定处理器
const handler = (event) => console.log(event);
eventBus.on('test', handler);
eventBus.off('test', handler);

// 取消事件的所有监听器
eventBus.off('test');

// 取消多个事件
eventBus.off(['event1', 'event2']);

// 取消命名空间下的所有事件
eventBus.off('*', null, { namespace: 'app1' });
```

### 事件发布

#### emit() - 发布事件

```typescript
// 基础发布
eventBus.emit('user-action', {
  action: 'click',
  target: 'button'
});

// 带配置的发布
eventBus.emit('data-sync', {
  from: 'app1',
  to: 'app2',
  data: { id: 1, name: 'test' }
}, {
  bubbles: true,      // 是否冒泡
  cancelable: true,   // 是否可取消
  timeout: 5000,      // 超时时间
  target: ['app2'],   // 目标应用
  priority: 'high'    // 优先级
});

// 异步发布
const result = await eventBus.emitAsync('async-operation', {
  operation: 'save',
  data: userData
});

if (result.success) {
  console.log('操作成功:', result.data);
} else {
  console.error('操作失败:', result.error);
}
```

#### broadcast() - 广播事件

```typescript
// 向所有应用广播
eventBus.broadcast('global-notification', {
  type: 'info',
  message: '系统维护通知'
});

// 向特定应用广播
eventBus.broadcast('team-message', {
  message: 'Hello team!'
}, {
  targets: ['app1', 'app2'],  // 目标应用
  exclude: ['app3']           // 排除应用
});
```

### 事件拦截

#### intercept() - 拦截事件

```typescript
// 拦截特定事件
eventBus.intercept('sensitive-operation', (event, next) => {
  // 权限检查
  if (!hasPermission(event.data.operation)) {
    // 阻止事件继续传播
    return false;
  }
  
  // 修改事件数据
  event.data.timestamp = Date.now();
  event.data.user = getCurrentUser();
  
  // 继续传播
  return next(event);
});

// 全局拦截器
eventBus.intercept('*', (event, next) => {
  // 记录所有事件
  console.log(`事件: ${event.name}`, event.data);
  
  // 性能监控
  const startTime = performance.now();
  const result = next(event);
  const endTime = performance.now();
  
  console.log(`事件处理耗时: ${endTime - startTime}ms`);
  return result;
});
```

### 事件查询

#### getListeners() - 获取监听器

```typescript
// 获取特定事件的监听器
const listeners = eventBus.getListeners('user-login');
console.log(`user-login 事件有 ${listeners.length} 个监听器`);

// 获取所有监听器
const allListeners = eventBus.getListeners();
```

#### getHistory() - 获取事件历史

```typescript
// 获取最近的事件历史
const history = eventBus.getHistory(10);
history.forEach(event => {
  console.log(`${event.timestamp}: ${event.name}`);
});

// 获取特定事件的历史
const userEvents = eventBus.getHistory(5, 'user.*');
```

## 高级功能

### 事件命名空间

```typescript
// 创建命名空间事件总线
const appEventBus = new EventBus({ namespace: 'my-app' });

// 发布命名空间事件
appEventBus.emit('local-event', { data: 'test' });

// 跨命名空间通信
eventBus.emit('global-event', { data: 'test' }, {
  namespace: 'other-app'
});

// 监听跨命名空间事件
eventBus.on('other-app:remote-event', (event) => {
  console.log('收到远程事件:', event);
});
```

### 事件优先级

```typescript
// 高优先级监听器
eventBus.on('critical-event', handler1, { priority: 10 });

// 中等优先级监听器
eventBus.on('critical-event', handler2, { priority: 5 });

// 低优先级监听器
eventBus.on('critical-event', handler3, { priority: 1 });

// 发布事件时，监听器按优先级顺序执行：handler1 -> handler2 -> handler3
eventBus.emit('critical-event', { urgent: true });
```

### 条件监听

```typescript
// 基于数据条件的监听
eventBus.on('user-action', (event) => {
  console.log('VIP用户操作:', event.data);
}, {
  condition: (event) => event.data.user.type === 'vip'
});

// 基于时间条件的监听
eventBus.on('time-sensitive', (event) => {
  console.log('工作时间事件:', event.data);
}, {
  condition: (event) => {
    const hour = new Date().getHours();
    return hour >= 9 && hour <= 18;
  }
});
```

### 事件聚合

```typescript
// 聚合多个事件
const aggregator = eventBus.aggregate([
  'data-loaded',
  'ui-rendered',
  'user-ready'
], (events) => {
  console.log('所有准备工作完成:', events);
  // 触发应用就绪事件
  eventBus.emit('app-ready', {
    loadTime: events.reduce((total, e) => total + e.duration, 0)
  });
});

// 带超时的聚合
const timedAggregator = eventBus.aggregate([
  'async-task-1',
  'async-task-2'
], (events) => {
  console.log('任务完成:', events.length);
}, {
  timeout: 10000,  // 10秒超时
  onTimeout: () => {
    console.warn('部分任务超时');
  }
});
```

### 事件重放

```typescript
// 启用事件重放
const replayBus = new EventBus({
  enableReplay: true,
  replayBufferSize: 100
});

// 发布一些事件
replayBus.emit('event1', { data: 1 });
replayBus.emit('event2', { data: 2 });

// 新的监听器可以接收到历史事件
replayBus.on('event1', (event) => {
  console.log('重放事件:', event);
}, {
  replay: true  // 启用重放
});

// 手动重放特定事件
replayBus.replay('event1', newListener);
```

## 错误处理

### 事件错误捕获

```typescript
// 监听器错误处理
eventBus.on('risky-operation', (event) => {
  try {
    // 可能出错的操作
    riskyFunction(event.data);
  } catch (error) {
    // 发布错误事件
    eventBus.emit('operation-error', {
      originalEvent: event,
      error: error.message
    });
  }
});

// 全局错误处理
eventBus.onError((error, event, listener) => {
  console.error('事件处理错误:', {
    event: event.name,
    error: error.message,
    listener: listener.toString()
  });
  
  // 发送错误报告
  sendErrorReport(error, event);
});
```

### 超时处理

```typescript
// 带超时的事件发布
eventBus.emit('slow-operation', { data: 'test' }, {
  timeout: 5000,
  onTimeout: () => {
    console.warn('事件处理超时');
    // 执行超时后的清理工作
    cleanup();
  }
});

// 异步事件超时
try {
  const result = await eventBus.emitAsync('async-task', { data: 'test' }, {
    timeout: 3000
  });
  console.log('任务完成:', result);
} catch (error) {
  if (error.code === 'TIMEOUT') {
    console.warn('任务超时');
  }
}
```

## 性能优化

### 事件节流和防抖

```typescript
import { throttle, debounce } from '@micro-core/utils';

// 节流处理高频事件
const throttledHandler = throttle((event) => {
  console.log('节流处理:', event.data);
}, 1000);  // 每秒最多执行一次

eventBus.on('scroll', throttledHandler);

// 防抖处理用户输入
const debouncedHandler = debounce((event) => {
  console.log('防抖处理:', event.data);
}, 500);  // 500ms 内无新事件才执行

eventBus.on('input', debouncedHandler);
```

### 批量处理

```typescript
// 启用批量处理
const batchBus = new EventBus({
  enableBatch: true,
  batchSize: 10,
  batchTimeout: 100
});

// 批量监听器
batchBus.onBatch('user-action', (events) => {
  console.log(`批量处理 ${events.length} 个用户操作`);
  
  // 批量更新UI
  updateUI(events.map(e => e.data));
});

// 发布多个事件，会被批量处理
for (let i = 0; i < 20; i++) {
  batchBus.emit('user-action', { id: i, action: 'click' });
}
```

### 内存管理

```typescript
// 自动清理过期监听器
const managedBus = new EventBus({
  autoCleanup: true,
  maxAge: 300000,  // 5分钟后清理不活跃的监听器
  maxListeners: 1000  // 最大监听器数量
});

// 手动清理
eventBus.cleanup({
  removeInactive: true,    // 移除不活跃的监听器
  clearHistory: true,      // 清空事件历史
  resetCounters: true      // 重置计数器
});

// 监控内存使用
const stats = eventBus.getStats();
console.log('事件总线统计:', {
  listeners: stats.listenerCount,
  events: stats.eventCount,
  memory: stats.memoryUsage
});
```

## 调试和监控

### 调试模式

```typescript
// 启用调试模式
const debugBus = new EventBus({ debug: true });

// 调试信息会自动输出到控制台
debugBus.emit('test-event', { data: 'debug' });
// 输出: [EventBus] 发布事件: test-event { data: 'debug' }

// 自定义调试输出
debugBus.onDebug((type, message, data) => {
  if (type === 'emit') {
    console.log(`🚀 事件发布: ${message}`, data);
  } else if (type === 'listen') {
    console.log(`👂 事件监听: ${message}`, data);
  }
});
```

### 性能监控

```typescript
// 启用性能监控
const monitoredBus = new EventBus({
  enableMonitoring: true,
  monitoringInterval: 1000  // 每秒收集一次统计
});

// 监听性能报告
monitoredBus.on('performance-report', (event) => {
  const { stats } = event.data;
  console.log('性能报告:', {
    eventsPerSecond: stats.eventsPerSecond,
    averageProcessingTime: stats.averageProcessingTime,
    slowestEvent: stats.slowestEvent
  });
});

// 获取实时性能数据
const performance = monitoredBus.getPerformanceMetrics();
console.log('实时性能:', performance);
```

### 事件追踪

```typescript
// 启用事件追踪
const trackedBus = new EventBus({
  enableTracing: true,
  traceLevel: 'verbose'
});

// 追踪特定事件的完整生命周期
trackedBus.trace('important-event', {
  includeStack: true,      // 包含调用栈
  includeTimeline: true,   // 包含时间线
  includeData: true        // 包含事件数据
});

// 获取追踪报告
const trace = trackedBus.getTrace('important-event');
console.log('事件追踪:', trace);
```

## 最佳实践

### 事件命名规范

```typescript
// 推荐的事件命名规范

// 系统事件 - 使用 system: 前缀
eventBus.emit('system:app-mounted', { appName: 'user-center' });
eventBus.emit('system:route-changed', { from: '/home', to: '/profile' });

// 业务事件 - 使用 业务域:动作 格式
eventBus.emit('user:login', { userId: 123, timestamp: Date.now() });
eventBus.emit('order:created', { orderId: 'ORD001', amount: 99.99 });
eventBus.emit('product:updated', { productId: 'P001', changes: ['price'] });

// UI事件 - 使用 ui: 前缀
eventBus.emit('ui:modal-opened', { modalId: 'confirm-dialog' });
eventBus.emit('ui:notification-shown', { type: 'success', message: '保存成功' });

// 错误事件 - 使用 error: 前缀
eventBus.emit('error:api-failed', { endpoint: '/api/users', status: 500 });
eventBus.emit('error:validation-failed', { field: 'email', rule: 'required' });
```

### 事件数据结构

```typescript
// 标准事件数据结构
interface StandardEventData {
  // 必需字段
  timestamp: number;        // 事件时间戳
  source: string;          // 事件源应用
  version: string;         // 事件版本
  
  // 可选字段
  correlationId?: string;  // 关联ID，用于追踪相关事件
  userId?: string;         // 用户ID
  sessionId?: string;      // 会话ID
  
  // 业务数据
  payload: any;           // 实际业务数据
  metadata?: Record<string, any>;  // 元数据
}

// 使用示例
eventBus.emit('user:profile-updated', {
  timestamp: Date.now(),
  source: 'user-center',
  version: '1.0',
  correlationId: 'req-123',
  userId: 'user-456',
  payload: {
    changes: ['avatar', 'nickname'],
    newData: { avatar: 'new-avatar.jpg', nickname: 'NewName' }
  },
  metadata: {
    userAgent: navigator.userAgent,
    ip: '***********'
  }
});
```

### 错误处理策略

```typescript
// 统一错误处理
class EventErrorHandler {
  static handle(error: Error, event: Event, context: any) {
    // 记录错误
    console.error('事件处理错误:', {
      event: event.name,
      error: error.message,
      stack: error.stack,
      context
    });
    
    // 发送错误报告
    eventBus.emit('error:event-processing-failed', {
      originalEvent: event,
      error: {
        message: error.message,
        stack: error.stack
      },
      context,
      timestamp: Date.now()
    });
    
    // 根据错误类型决定是否继续传播
    if (error instanceof CriticalError) {
      // 关键错误，停止传播
      return false;
    }
    
    // 非关键错误，继续传播
    return true;
  }
}

// 应用错误处理器
eventBus.onError(EventErrorHandler.handle);
```

### 测试支持

```typescript
// 事件系统测试工具
class EventBusTestUtils {
  private eventBus: EventBus;
  private capturedEvents: Event[] = [];
  
  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }
  
  // 开始捕获事件
  startCapture(eventNames?: string[]) {
    this.capturedEvents = [];
    
    const captureHandler = (event: Event) => {
      this.capturedEvents.push(event);
    };
    
    if (eventNames) {
      eventNames.forEach(name => {
        this.eventBus.on(name, captureHandler);
      });
    } else {
      this.eventBus.on('*', captureHandler);
    }
  }
  
  // 停止捕获
  stopCapture() {
    // 清理监听器
    this.eventBus.off('*');
  }
  
  // 断言事件被触发
  expectEvent(eventName: string, expectedData?: any) {
    const event = this.capturedEvents.find(e => e.name === eventName);
    expect(event).toBeDefined();
    
    if (expectedData) {
      expect(event.data).toEqual(expectedData);
    }
    
    return event;
  }
  
  // 断言事件触发次数
  expectEventCount(eventName: string, count: number) {
    const events = this.capturedEvents.filter(e => e.name === eventName);
    expect(events.length).toBe(count);
  }
  
  // 获取捕获的事件
  getCapturedEvents() {
    return [...this.capturedEvents];
  }
}

// 测试示例
describe('EventBus', () => {
  let eventBus: EventBus;
  let testUtils: EventBusTestUtils;
  
  beforeEach(() => {
    eventBus = new EventBus();
    testUtils = new EventBusTestUtils(eventBus);
  });
  
  test('应该正确发布和接收事件', () => {
    testUtils.startCapture(['user:login']);
    
    eventBus.emit('user:login', { userId: 123 });
    
    testUtils.expectEvent('user:login', { userId: 123 });
    testUtils.expectEventCount('user:login', 1);
  });
});
```

## 参考资料

- [核心 API 文档](/api/core)
- [应用通信指南](/guide/communication)
- [插件开发指南](/guide/plugin-development)
- [最佳实践](/guide/best-practices)
- [故障排除](/guide/troubleshooting)