# 加载器 API

Micro-Core 提供了强大的加载器系统，用于加载和管理微应用资源。本文档详细介绍了加载器 API 的使用方法和最佳实践。

## 基本概念

在微前端架构中，加载器负责以下任务：

1. **资源加载**：加载微应用的 JavaScript、CSS 和其他资源
2. **依赖管理**：处理微应用之间的依赖关系
3. **生命周期管理**：控制微应用的加载、挂载、卸载等生命周期
4. **错误处理**：处理资源加载和执行过程中的错误

## Loader

`Loader` 是 Micro-Core 提供的核心加载器类，用于加载和管理微应用资源。

### 基本用法

```typescript
import { Loader } from '@micro-core/core';

// 创建加载器实例
const loader = new Loader({
  sandbox: true,
  plugins: ['css', 'html', 'js'],
  timeout: 10000
});

// 加载微应用
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container'
})
  .then(app => {
    // 挂载微应用
    return app.mount();
  })
  .then(() => {
    console.log('微应用加载并挂载成功');
  })
  .catch(error => {
    console.error('微应用加载失败:', error);
  });
```

### API 参考

#### 构造函数

创建加载器实例。

```typescript
constructor(options?: LoaderOptions)
```

**参数：**
- `options` (LoaderOptions): 可选，加载器配置选项
  - `sandbox` (boolean | SandboxOptions): 是否启用沙箱，或沙箱配置选项，默认为 true
  - `plugins` (string[] | Plugin[]): 加载器插件列表，默认包含 'js', 'css', 'html'
  - `timeout` (number): 资源加载超时时间，单位为毫秒，默认为 30000
  - `fetch` (Function): 自定义 fetch 函数，用于加载远程资源
  - `cache` (boolean | CacheOptions): 是否启用缓存，或缓存配置选项，默认为 true
  - `retryCount` (number): 加载失败时的重试次数，默认为 3
  - `retryDelay` (number): 重试间隔时间，单位为毫秒，默认为 1000
  - `prefetch` (boolean): 是否预加载资源，默认为 false
  - `esModule` (boolean): 是否使用 ES 模块，默认为 false
  - `strictMode` (boolean): 是否启用严格模式，默认为 true
  - `logger` (Logger): 自定义日志记录器

**示例：**

```typescript
// 基本用法
const loader = new Loader();

// 完整配置
const loader = new Loader({
  // 沙箱配置
  sandbox: {
    enabled: true,
    strictIsolation: true,
    disableWith: false,
    useProxy: true
  },
  
  // 插件配置
  plugins: ['js', 'css', 'html', 'json', 'assets'],
  
  // 超时配置
  timeout: 15000,
  
  // 自定义 fetch 函数
  fetch: (url, options) => {
    console.log(`加载资源: ${url}`);
    return window.fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'X-Custom-Header': 'custom-value'
      }
    });
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    maxAge: 3600000, // 1小时
    capacity: 100
  },
  
  // 重试配置
  retryCount: 3,
  retryDelay: 1000,
  
  // 其他配置
  prefetch: true,
  esModule: true,
  strictMode: true,
  
  // 自定义日志记录器
  logger: {
    info: (...args) => console.info('[Loader]', ...args),
    warn: (...args) => console.warn('[Loader]', ...args),
    error: (...args) => console.error('[Loader]', ...args),
    debug: (...args) => console.debug('[Loader]', ...args)
  }
});
```

#### load(options)

加载微应用。

```typescript
load(options: LoadOptions): Promise<MicroApp>
```

**参数：**
- `options` (LoadOptions): 加载选项
  - `name` (string): 微应用名称
  - `entry` (string | string[]): 微应用入口地址，可以是 HTML 地址、JS 地址或多个地址
  - `container` (string | HTMLElement): 微应用容器元素或选择器
  - `props` (object): 传递给微应用的属性
  - `sandbox` (boolean | SandboxOptions): 是否启用沙箱，或沙箱配置选项
  - `plugins` (string[] | Plugin[]): 加载器插件列表
  - `timeout` (number): 资源加载超时时间，单位为毫秒
  - `fetch` (Function): 自定义 fetch 函数，用于加载远程资源
  - `cache` (boolean | CacheOptions): 是否启用缓存，或缓存配置选项
  - `retryCount` (number): 加载失败时的重试次数
  - `retryDelay` (number): 重试间隔时间，单位为毫秒
  - `prefetch` (boolean): 是否预加载资源
  - `esModule` (boolean): 是否使用 ES 模块
  - `strictMode` (boolean): 是否启用严格模式
  - `lifecycle` (LifecycleHooks): 生命周期钩子
  - `customLoader` (Function): 自定义加载函数

**返回值：** 返回一个 Promise，解析为加载的微应用实例

**示例：**

```typescript
// 基本用法
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container'
})
  .then(app => {
    // 挂载微应用
    return app.mount();
  })
  .catch(error => {
    console.error('微应用加载失败:', error);
  });

// 加载多个入口
loader.load({
  name: 'app2',
  entry: [
    'https://example.com/app2/index.js',
    'https://example.com/app2/style.css'
  ],
  container: '#app2-container',
  props: {
    user: {
      id: 1,
      name: 'Admin'
    },
    theme: 'dark'
  }
})
  .then(app => app.mount())
  .catch(error => {
    console.error('微应用加载失败:', error);
  });

// 完整配置
loader.load({
  name: 'app3',
  entry: 'https://example.com/app3/index.html',
  container: document.getElementById('app3-container'),
  props: {
    user: { id: 1, name: 'Admin' },
    theme: 'dark'
  },
  
  // 沙箱配置
  sandbox: {
    enabled: true,
    strictIsolation: true,
    disableWith: false,
    useProxy: true
  },
  
  // 插件配置
  plugins: ['js', 'css', 'html', 'json'],
  
  // 超时配置
  timeout: 15000,
  
  // 自定义 fetch 函数
  fetch: (url, options) => {
    console.log(`加载资源: ${url}`);
    return window.fetch(url, options);
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    maxAge: 3600000, // 1小时
    capacity: 100
  },
  
  // 重试配置
  retryCount: 3,
  retryDelay: 1000,
  
  // 其他配置
  prefetch: true,
  esModule: true,
  strictMode: true,
  
  // 生命周期钩子
  lifecycle: {
    beforeLoad: (app) => {
      console.log(`${app.name} 开始加载`);
      return true; // 返回 false 将阻止加载
    },
    afterLoad: (app) => {
      console.log(`${app.name} 加载完成`);
    },
    beforeMount: (app) => {
      console.log(`${app.name} 开始挂载`);
      return true; // 返回 false 将阻止挂载
    },
    afterMount: (app) => {
      console.log(`${app.name} 挂载完成`);
    },
    beforeUnmount: (app) => {
      console.log(`${app.name} 开始卸载`);
      return true; // 返回 false 将阻止卸载
    },
    afterUnmount: (app) => {
      console.log(`${app.name} 卸载完成`);
    },
    onError: (error, app) => {
      console.error(`${app.name} 发生错误:`, error);
    }
  },
  
  // 自定义加载函数
  customLoader: (url, options) => {
    if (url.endsWith('.custom')) {
      return fetch(url)
        .then(response => response.text())
        .then(text => {
          // 处理自定义格式
          return processCustomFormat(text);
        });
    }
    return null; // 返回 null 表示使用默认加载器
  }
})
  .then(app => app.mount())
  .catch(error => {
    console.error('微应用加载失败:', error);
  });
```

#### loadAll(optionsList)

加载多个微应用。

```typescript
loadAll(optionsList: LoadOptions[]): Promise<MicroApp[]>
```

**参数：**
- `optionsList` (LoadOptions[]): 加载选项数组

**返回值：** 返回一个 Promise，解析为加载的微应用实例数组

**示例：**

```typescript
// 加载多个微应用
loader.loadAll([
  {
    name: 'app1',
    entry: 'https://example.com/app1/index.html',
    container: '#app1-container'
  },
  {
    name: 'app2',
    entry: 'https://example.com/app2/index.html',
    container: '#app2-container'
  },
  {
    name: 'app3',
    entry: 'https://example.com/app3/index.html',
    container: '#app3-container'
  }
])
  .then(apps => {
    // 挂载所有微应用
    return Promise.all(apps.map(app => app.mount()));
  })
  .then(() => {
    console.log('所有微应用加载并挂载成功');
  })
  .catch(error => {
    console.error('微应用加载失败:', error);
  });
```

#### loadResource(url, options)

加载单个资源。

```typescript
loadResource(url: string, options?: ResourceOptions): Promise<any>
```

**参数：**
- `url` (string): 资源 URL
- `options` (ResourceOptions): 可选，资源加载选项
  - `type` (string): 资源类型，如 'js', 'css', 'html', 'json' 等
  - `timeout` (number): 资源加载超时时间，单位为毫秒
  - `fetch` (Function): 自定义 fetch 函数，用于加载远程资源
  - `cache` (boolean | CacheOptions): 是否启用缓存，或缓存配置选项
  - `retryCount` (number): 加载失败时的重试次数
  - `retryDelay` (number): 重试间隔时间，单位为毫秒
  - `sandbox` (boolean | SandboxOptions): 是否启用沙箱，或沙箱配置选项
  - `esModule` (boolean): 是否使用 ES 模块
  - `context` (object): 执行上下文

**返回值：** 返回一个 Promise，解析为加载的资源

**示例：**

```typescript
// 加载 JavaScript 资源
loader.loadResource('https://example.com/app1/main.js', { type: 'js' })
  .then(module => {
    console.log('JavaScript 模块加载成功:', module);
  })
  .catch(error => {
    console.error('JavaScript 加载失败:', error);
  });

// 加载 CSS 资源
loader.loadResource('https://example.com/app1/style.css', { type: 'css' })
  .then(() => {
    console.log('CSS 加载成功');
  })
  .catch(error => {
    console.error('CSS 加载失败:', error);
  });

// 加载 HTML 资源
loader.loadResource('https://example.com/app1/template.html', { type: 'html' })
  .then(html => {
    console.log('HTML 加载成功:', html);
  })
  .catch(error => {
    console.error('HTML 加载失败:', error);
  });

// 加载 JSON 资源
loader.loadResource('https://example.com/app1/config.json', { type: 'json' })
  .then(config => {
    console.log('JSON 配置加载成功:', config);
  })
  .catch(error => {
    console.error('JSON 加载失败:', error);
  });

// 完整配置
loader.loadResource('https://example.com/app1/main.js', {
  type: 'js',
  timeout: 5000,
  fetch: (url, options) => {
    console.log(`加载资源: ${url}`);
    return window.fetch(url, options);
  },
  cache: {
    enabled: true,
    maxAge: 3600000 // 1小时
  },
  retryCount: 3,
  retryDelay: 1000,
  sandbox: true,
  esModule: true,
  context: {
    // 执行上下文
    globalVar: 'value'
  }
})
  .then(module => {
    console.log('资源加载成功:', module);
  })
  .catch(error => {
    console.error('资源加载失败:', error);
  });
```

#### loadResources(urls, options)

加载多个资源。

```typescript
loadResources(urls: string[], options?: ResourceOptions): Promise<any[]>
```

**参数：**
- `urls` (string[]): 资源 URL 数组
- `options` (ResourceOptions): 可选，资源加载选项

**返回值：** 返回一个 Promise，解析为加载的资源数组

**示例：**

```typescript
// 加载多个资源
loader.loadResources([
  'https://example.com/app1/main.js',
  'https://example.com/app1/style.css',
  'https://example.com/app1/config.json'
], {
  timeout: 10000,
  cache: true,
  retryCount: 3
})
  .then(resources => {
    console.log('所有资源加载成功:', resources);
  })
  .catch(error => {
    console.error('资源加载失败:', error);
  });

// 指定资源类型
loader.loadResources([
  { url: 'https://example.com/app1/main.js', type: 'js' },
  { url: 'https://example.com/app1/style.css', type: 'css' },
  { url: 'https://example.com/app1/config.json', type: 'json' }
])
  .then(resources => {
    console.log('所有资源加载成功:', resources);
  })
  .catch(error => {
    console.error('资源加载失败:', error);
  });
```

#### registerPlugin(plugin)

注册加载器插件。

```typescript
registerPlugin(plugin: Plugin | string): void
```

**参数：**
- `plugin` (Plugin | string): 插件对象或插件名称

**示例：**

```typescript
// 注册内置插件
loader.registerPlugin('js');
loader.registerPlugin('css');
loader.registerPlugin('html');
loader.registerPlugin('json');

// 注册自定义插件
const customPlugin = {
  name: 'custom',
  test: (url) => url.endsWith('.custom'),
  load: (url, options) => {
    return fetch(url)
      .then(response => response.text())
      .then(text => {
        // 处理自定义格式
        return processCustomFormat(text);
      });
  }
};

loader.registerPlugin(customPlugin);
```

#### unregisterPlugin(pluginName)

注销加载器插件。

```typescript
unregisterPlugin(pluginName: string): void
```

**参数：**
- `pluginName` (string): 插件名称

**示例：**

```typescript
// 注销插件
loader.unregisterPlugin('css');
```

#### getPlugin(pluginName)

获取加载器插件。

```typescript
getPlugin(pluginName: string): Plugin | null
```

**参数：**
- `pluginName` (string): 插件名称

**返回值：** 返回插件对象，如果插件不存在则返回 null

**示例：**

```typescript
// 获取插件
const cssPlugin = loader.getPlugin('css');
console.log('CSS 插件:', cssPlugin);
```

#### clearCache()

清除资源缓存。

```typescript
clearCache(): void
```

**示例：**

```typescript
// 清除所有缓存
loader.clearCache();
```

#### clearCache(url)

清除指定资源的缓存。

```typescript
clearCache(url: string): void
```

**参数：**
- `url` (string): 资源 URL

**示例：**

```typescript
// 清除指定资源的缓存
loader.clearCache('https://example.com/app1/main.js');
```

#### prefetch(urls, options)

预加载资源。

```typescript
prefetch(urls: string | string[], options?: ResourceOptions): Promise<any[]>
```

**参数：**
- `urls` (string | string[]): 资源 URL 或 URL 数组
- `options` (ResourceOptions): 可选，资源加载选项

**返回值：** 返回一个 Promise，解析为预加载的资源数组

**示例：**

```typescript
// 预加载单个资源
loader.prefetch('https://example.com/app1/main.js')
  .then(() => {
    console.log('资源预加载成功');
  })
  .catch(error => {
    console.error('资源预加载失败:', error);
  });

// 预加载多个资源
loader.prefetch([
  'https://example.com/app1/main.js',
  'https://example.com/app1/style.css',
  'https://example.com/app1/config.json'
])
  .then(() => {
    console.log('所有资源预加载成功');
  })
  .catch(error => {
    console.error('资源预加载失败:', error);
  });

// 带选项的预加载
loader.prefetch([
  'https://example.com/app1/main.js',
  'https://example.com/app1/style.css'
], {
  timeout: 10000,
  retryCount: 3
})
  .then(() => {
    console.log('所有资源预加载成功');
  })
  .catch(error => {
    console.error('资源预加载失败:', error);
  });
```

#### setGlobalConfig(config)

设置全局配置。

```typescript
setGlobalConfig(config: LoaderOptions): void
```

**参数：**
- `config` (LoaderOptions): 加载器配置选项

**示例：**

```typescript
// 设置全局配置
loader.setGlobalConfig({
  timeout: 15000,
  cache: true,
  retryCount: 3,
  retryDelay: 1000,
  prefetch: true,
  sandbox: true
});
```

#### getGlobalConfig()

获取全局配置。

```typescript
getGlobalConfig(): LoaderOptions
```

**返回值：** 返回当前的全局配置

**示例：**

```typescript
// 获取全局配置
const config = loader.getGlobalConfig();
console.log('全局配置:', config);
```

#### on(event, callback)

监听加载器事件。

```typescript
on(event: string, callback: Function): void
```

**参数：**
- `event` (string): 事件名称
- `callback` (Function): 事件回调函数

**示例：**

```typescript
// 监听加载事件
loader.on('load', (app) => {
  console.log(`微应用 ${app.name} 加载成功`);
});

// 监听挂载事件
loader.on('mount', (app) => {
  console.log(`微应用 ${app.name} 挂载成功`);
});

// 监听卸载事件
loader.on('unmount', (app) => {
  console.log(`微应用 ${app.name} 卸载成功`);
});

// 监听错误事件
loader.on('error', (error, app) => {
  console.error(`微应用 ${app.name} 发生错误:`, error);
});
```

#### off(event, callback)

取消监听加载器事件。

```typescript
off(event: string, callback?: Function): void
```

**参数：**
- `event` (string): 事件名称
- `callback` (Function): 可选，事件回调函数，如果不提供则移除所有该事件的监听器

**示例：**

```typescript
// 定义事件处理函数
function handleLoad(app) {
  console.log(`微应用 ${app.name} 加载成功`);
}

// 监听事件
loader.on('load', handleLoad);

// 取消监听特定事件处理函数
loader.off('load', handleLoad);

// 取消监听所有 load 事件
loader.off('load');
```

## MicroApp

`MicroApp` 是加载器加载的微应用实例，提供了管理微应用生命周期的方法。

### 属性

- `name` (string): 微应用名称
- `entry` (string | string[]): 微应用入口地址
- `container` (string | HTMLElement): 微应用容器元素或选择器
- `props` (object): 传递给微应用的属性
- `status` (string): 微应用状态，可能的值有 'loading', 'loaded', 'mounting', 'mounted', 'unmounting', 'unmounted', 'error'

### 方法

#### mount()

挂载微应用。

```typescript
mount(): Promise<void>
```

**返回值：** 返回一个 Promise，表示挂载操作的完成

**示例：**

```typescript
// 加载并挂载微应用
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container'
})
  .then(app => {
    // 挂载微应用
    return app.mount();
  })
  .then(() => {
    console.log('微应用挂载成功');
  })
  .catch(error => {
    console.error('微应用挂载失败:', error);
  });
```

#### unmount()

卸载微应用。

```typescript
unmount(): Promise<void>
```

**返回值：** 返回一个 Promise，表示卸载操作的完成

**示例：**

```typescript
// 卸载微应用
app.unmount()
  .then(() => {
    console.log('微应用卸载成功');
  })
  .catch(error => {
    console.error('微应用卸载失败:', error);
  });
```

#### update(props)

更新微应用属性。

```typescript
update(props: object): Promise<void>
```

**参数：**
- `props` (object): 新的属性对象

**返回值：** 返回一个 Promise，表示更新操作的完成

**示例：**

```typescript
// 更新微应用属性
app.update({
  user: {
    id: 2,
    name: 'User'
  },
  theme: 'light'
})
  .then(() => {
    console.log('微应用属性更新成功');
  })
  .catch(error => {
    console.error('微应用属性更新失败:', error);
  });
```

#### getStatus()

获取微应用状态。

```typescript
getStatus(): string
```

**返回值：** 返回微应用当前状态

**示例：**

```typescript
// 获取微应用状态
const status = app.getStatus();
console.log('微应用状态:', status);
```

#### getContainer()

获取微应用容器元素。

```typescript
getContainer(): HTMLElement | null
```

**返回值：** 返回微应用容器元素，如果容器不存在则返回 null

**示例：**

```typescript
// 获取微应用容器
const container = app.getContainer();
console.log('微应用容器:', container);
```

#### getProps()

获取微应用属性。

```typescript
getProps(): object
```

**返回值：** 返回微应用当前属性

**示例：**

```typescript
// 获取微应用属性
const props = app.getProps();
console.log('微应用属性:', props);
```

## 插件系统

Micro-Core 加载器提供了强大的插件系统，用于扩展加载器的功能。

### 内置插件

Micro-Core 加载器内置了以下插件：

1. **js**: 加载 JavaScript 资源
2. **css**: 加载 CSS 资源
3. **html**: 加载 HTML 资源
4. **json**: 加载 JSON 资源
5. **assets**: 加载其他静态资源

### 自定义插件

你可以创建自定义插件来扩展加载器的功能：

```typescript
// 定义自定义插件
const markdownPlugin = {
  // 插件名称
  name: 'markdown',
  
  // 测试函数，判断是否由该插件处理
  test: (url) => url.endsWith('.md'),
  
  // 加载函数，处理资源加载
  load: (url, options) => {
    return fetch(url)
      .then(response => response.text())
      .then(text => {
        // 将 Markdown 转换为 HTML
        return convertMarkdownToHtml(text);
      });
  }
};

// 注册自定义插件
loader.registerPlugin(markdownPlugin);

// 使用自定义插件加载资源
loader.loadResource('https://example.com/app1/readme.md')
  .then(html => {
    console.log('Markdown 加载并转换成功:', html);
  })
  .catch(error => {
    console.error('Markdown 加载失败:', error);
  });
```

### 插件 API

自定义插件需要实现以下接口：

```typescript
interface Plugin {
  // 插件名称
  name: string;
  
  // 测试函数，判断是否由该插件处理
  test: (url: string) => boolean;
  
  // 加载函数，处理资源加载
  load: (url: string, options?: ResourceOptions) => Promise<any>;
  
  // 可选，资源处理函数，在资源加载后执行
  process?: (resource: any, url: string, options?: ResourceOptions) => Promise<any>;
  
  // 可选，资源卸载函数，在资源卸载时执行
  unload?: (resource: any, url: string) => Promise<void>;
}
```

## 缓存系统

Micro-Core 加载器提供了缓存系统，用于缓存加载的资源，提高加载性能。

### 缓存配置

```typescript
// 创建带缓存配置的加载器
const loader = new Loader({
  cache: {
    enabled: true,      // 是否启用缓存
    maxAge: 3600000,    // 缓存最大存活时间，单位为毫秒，默认为 1 小时
    capacity: 100       // 缓存容量，默认为 50
  }
});

// 或者在加载资源时指定缓存配置
loader.loadResource('https://example.com/app1/main.js', {
  cache: {
    enabled: true,
    maxAge: 3600000,
    capacity: 100
  }
});
```

### 缓存管理

```typescript
// 清除所有缓存
loader.clearCache();

// 清除特定资源的缓存