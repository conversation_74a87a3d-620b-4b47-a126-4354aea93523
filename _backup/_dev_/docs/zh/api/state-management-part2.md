# 状态管理 API (第二部分)

## 中间件 (续)

### 持久化中间件

```typescript
// 持久化中间件
const persistMiddleware = (context, next) => {
  const result = next(); // 先更新状态
  
  // 然后持久化
  if (context.path.startsWith('user') || context.path.startsWith('settings')) {
    const state = store.getState();
    localStorage.setItem('app-state', JSON.stringify(state));
  }
  
  return result;
};

// 创建带持久化中间件的存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    settings: {
      theme: 'light',
      language: 'zh-CN'
    }
  },
  
  // 添加中间件
  middlewares: [persistMiddleware]
});
```

### 撤销/重做中间件

```typescript
// 撤销/重做中间件
function createUndoRedoMiddleware(options = {}) {
  const {
    maxHistory = 10,
    include = ['*'],
    exclude = []
  } = options;
  
  const history = [];
  let currentIndex = -1;
  let isUndoRedo = false;
  
  return (context, next) => {
    const { store, path } = context;
    
    // 检查路径是否应该记录历史
    const shouldRecord = (
      !isUndoRedo &&
      include.some(pattern => path === pattern || pattern === '*') &&
      !exclude.some(pattern => path === pattern)
    );
    
    // 执行状态更新
    const result = next();
    
    // 记录历史
    if (shouldRecord) {
      // 移除当前索引之后的历史
      if (currentIndex < history.length - 1) {
        history.splice(currentIndex + 1);
      }
      
      // 添加新的历史记录
      history.push(store.snapshot());
      
      // 限制历史记录数量
      if (history.length > maxHistory) {
        history.shift();
      }
      
      // 更新当前索引
      currentIndex = history.length - 1;
    }
    
    return result;
  };
}

// 创建撤销/重做中间件
const undoRedoMiddleware = createUndoRedoMiddleware({
  maxHistory: 20,
  exclude: ['notifications']
});

// 创建带撤销/重做中间件的存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  },
  
  // 添加中间件
  middlewares: [undoRedoMiddleware]
});

// 添加撤销/重做方法
store.undo = function() {
  if (currentIndex > 0) {
    isUndoRedo = true;
    currentIndex--;
    this.restore(history[currentIndex]);
    isUndoRedo = false;
    return true;
  }
  return false;
};

store.redo = function() {
  if (currentIndex < history.length - 1) {
    isUndoRedo = true;
    currentIndex++;
    this.restore(history[currentIndex]);
    isUndoRedo = false;
    return true;
  }
  return false;
};

// 使用撤销/重做
store.setState('theme', 'dark');
store.setState('user.name', 'Admin');

// 撤销
store.undo(); // 回到 user.name = ''
store.undo(); // 回到 theme = 'light'

// 重做
store.redo(); // 回到 theme = 'dark'
store.redo(); // 回到 user.name = 'Admin'
```

## 插件

Micro-Core 状态管理系统支持插件，可以扩展存储的功能。

### 持久化插件

```typescript
// 持久化插件
const persistPlugin = {
  name: 'persist',
  
  // 初始化插件
  init(store, options) {
    const {
      storage = localStorage,
      key = 'micro-core-store',
      expires = null,
      include = null,
      exclude = null
    } = options || {};
    
    // 从存储中恢复状态
    const savedStateStr = storage.getItem(key);
    
    if (savedStateStr) {
      try {
        const savedState = JSON.parse(savedStateStr);
        
        // 检查过期时间
        if (expires && savedState._expires && Date.now() > savedState._expires) {
          storage.removeItem(key);
        } else {
          // 删除元数据
          delete savedState._expires;
          
          // 恢复状态
          store.restore(savedState);
        }
      } catch (error) {
        console.error('恢复状态失败:', error);
      }
    }
    
    // 监听状态变化
    store.subscribe('*', () => {
      const state = store.snapshot();
      
      // 过滤状态
      let persistState = { ...state };
      
      if (include) {
        persistState = {};
        include.forEach(path => {
          const value = store.getState(path);
          setNestedValue(persistState, path, value);
        });
      }
      
      if (exclude) {
        exclude.forEach(path => {
          deleteNestedValue(persistState, path);
        });
      }
      
      // 添加过期时间
      if (expires) {
        persistState._expires = Date.now() + expires;
      }
      
      // 保存状态
      try {
        storage.setItem(key, JSON.stringify(persistState));
      } catch (error) {
        console.error('保存状态失败:', error);
      }
    });
    
    // 辅助函数
    function setNestedValue(obj, path, value) {
      const parts = path.split('.');
      let current = obj;
      
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }
      
      current[parts[parts.length - 1]] = value;
    }
    
    function deleteNestedValue(obj, path) {
      const parts = path.split('.');
      let current = obj;
      
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (!current[part]) {
          return;
        }
        current = current[part];
      }
      
      delete current[parts[parts.length - 1]];
    }
  }
};

// 创建带持久化插件的存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  },
  
  // 添加插件
  plugins: [
    {
      plugin: persistPlugin,
      options: {
        key: 'my-app-state',
        expires: 24 * 60 * 60 * 1000, // 1天
        include: ['user', 'theme'],
        exclude: ['user.password']
      }
    }
  ]
});
```

### 同步插件

```typescript
// 同步插件
const syncPlugin = {
  name: 'sync',
  
  // 初始化插件
  init(store, options) {
    const {
      channel = 'micro-core-sync',
      scope = 'global'
    } = options || {};
    
    // 创建广播通道
    const bc = new BroadcastChannel(channel);
    
    // 监听状态变化
    store.subscribe('*', (newValue, oldValue, path) => {
      // 发送同步消息
      bc.postMessage({
        type: 'sync',
        path,
        value: newValue,
        scope
      });
    });
    
    // 监听同步消息
    bc.onmessage = (event) => {
      const { type, path, value, scope: messageScope } = event.data;
      
      // 检查消息类型和作用域
      if (type === 'sync' && messageScope === scope) {
        // 更新状态，但不触发同步
        store._setState(path, value);
      }
    };
    
    // 清理函数
    return () => {
      bc.close();
    };
  }
};

// 创建带同步插件的存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  },
  
  // 添加插件
  plugins: [
    {
      plugin: syncPlugin,
      options: {
        channel: 'my-app-sync',
        scope: 'app1'
      }
    }
  ]
});
```

### 开发工具插件

```typescript
// 开发工具插件
const devtoolsPlugin = {
  name: 'devtools',
  
  // 初始化插件
  init(store, options) {
    const {
      name = 'Micro-Core Store',
      maxAge = 25
    } = options || {};
    
    // 检查开发工具是否存在
    if (typeof window === 'undefined' || !window.__REDUX_DEVTOOLS_EXTENSION__) {
      console.warn('Redux DevTools 扩展未安装');
      return;
    }
    
    // 连接开发工具
    const devtools = window.__REDUX_DEVTOOLS_EXTENSION__.connect({
      name,
      maxAge
    });
    
    // 初始化
    devtools.init(store.snapshot());
    
    // 监听状态变化
    store.subscribe('*', (newValue, oldValue, path) => {
      devtools.send({
        type: `[Store] ${path}`,
        payload: { path, newValue, oldValue }
      }, store.snapshot());
    });
    
    // 监听开发工具消息
    devtools.subscribe((message) => {
      if (message.type === 'DISPATCH' && message.payload.type === 'JUMP_TO_STATE') {
        store.restore(JSON.parse(message.state));
      }
    });
    
    // 清理函数
    return () => {
      devtools.unsubscribe();
    };
  }
};

// 创建带开发工具插件的存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  },
  
  // 添加插件
  plugins: [
    {
      plugin: devtoolsPlugin,
      options: {
        name: 'My App Store'
      }
    }
  ]
});
```

## 高级功能

### 不可变数据

Micro-Core 状态管理系统支持不可变数据，可以提高状态的可预测性和安全性。

```typescript
// 创建带不可变数据的存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  },
  
  // 启用不可变数据
  immutable: true
});

// 获取状态（返回不可变对象）
const user = store.getState('user');

// 尝试直接修改状态（会失败）
try {
  user.name = 'Admin'; // 抛出错误
} catch (error) {
  console.error('无法直接修改状态:', error);
}

// 正确的修改方式
store.setState('user.name', 'Admin');

// 使用函数更新
store.setState('user', (currentUser) => ({
  ...currentUser,
  name: 'Admin',
  isLoggedIn: true
}));
```

### 计算属性

Micro-Core 状态管理系统支持计算属性，可以根据状态派生出新的值。

```typescript
// 创建带计算属性的存储
const store = new Store({
  state: {
    user: {
      firstName: 'John',
      lastName: 'Doe',
      age: 30
    },
    items: [
      { id: 1, name: 'Item 1', price: 10, quantity: 2 },
      { id: 2, name: 'Item 2', price: 20, quantity: 1 },
      { id: 3, name: 'Item 3', price: 30, quantity: 3 }
    ]
  }
});

// 注册计算属性
store.registerComputed('user.fullName', (state) => {
  const user = state.user;
  return `${user.firstName} ${user.lastName}`;
});

store.registerComputed('totalPrice', (state) => {
  return state.items.reduce((total, item) => total + item.price * item.quantity, 0);
});

// 获取计算属性
const fullName = store.getComputed('user.fullName');
console.log('全名:', fullName); // 输出: 全名: John Doe

const totalPrice = store.getComputed('totalPrice');
console.log('总价:', totalPrice); // 输出: 总价: 130

// 监听计算属性变化
store.subscribeComputed('totalPrice', (newValue, oldValue) => {
  console.log('总价变化:', oldValue, '->', newValue);
});

// 更新状态，触发计算属性更新
store.setState('items.0.quantity', 3);
```

### 状态时间旅行

Micro-Core 状态管理系统支持状态时间旅行，可以在状态的历史记录中前进和后退。

```typescript
// 创建带时间旅行的存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  }
});

// 启用时间旅行
store.enableTimeTravel({
  maxHistory: 20,
  include: ['user', 'theme'],
  exclude: ['notifications']
});

// 修改状态
store.setState('theme', 'dark');
store.setState('user.name', 'Admin');
store.setState('user.isLoggedIn', true);

// 获取历史记录
const history = store.getHistory();
console.log('历史记录:', history);

// 回到过去
store.goToState(1); // 回到 theme = 'dark' 的状态

// 前进到未来
store.goToState(3); // 回到 user.isLoggedIn = true 的状态

// 回到特定时间点
store.goToTime(timestamp);

// 禁用时间旅行
store.disableTimeTravel();
```

### 状态分片

Micro-Core 状态管理系统支持状态分片，可以将状态分割成多个独立的部分，提高性能和可维护性。

```typescript
// 创建用户分片
const userSlice = createSlice({
  name: 'user',
  initialState: {
    id: null,
    name: '',
    email: '',
    isLoggedIn: false
  },
  reducers: {
    setUser: (state, user) => {
      state.id = user.id;
      state.name = user.name;
      state.email = user.email;
      state.isLoggedIn = true;
    },
    clearUser: (state) => {
      state.id = null;
      state.name = '';
      state.email = '';
      state.isLoggedIn = false;
    },
    updateName: (state, name) => {
      state.name = name;
    }
  }
});

// 创建设置分片
const settingsSlice = createSlice({
  name: 'settings',
  initialState: {
    theme: 'light',
    language: 'zh-CN',
    notifications: {
      email: true,
      push: false
    }
  },
  reducers: {
    setTheme: (state, theme) => {
      state.theme = theme;
    },
    setLanguage: (state, language) => {
      state.language = language;
    },
    toggleNotification: (state, type) => {
      state.notifications[type] = !state.notifications[type];
    }
  }
});

// 创建存储
const store = new Store();

// 注册分片
store.registerSlice(userSlice);
store.registerSlice(settingsSlice);

// 使用分片
store.dispatch('user/setUser', {
  id: 1,
  name: 'Admin',
  email: '<EMAIL>'
});

store.dispatch('settings/setTheme', 'dark');
store.dispatch('settings/toggleNotification', 'push');

// 获取分片状态
const user = store.getState('user');
const theme = store.getState('settings.theme');
```

## 与其他框架集成

### 与 React 集成

```typescript
// 创建存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  }
});

// 创建 React 上下文
const StoreContext = React.createContext(null);

// 创建提供者组件
function StoreProvider({ children }) {
  const [state, setState] = React.useState(store.getState());
  
  React.useEffect(() => {
    // 监听状态变化
    const unsubscribe = store.subscribe('*', () => {
      setState(store.getState());
    });
    
    return unsubscribe;
  }, []);
  
  return (
    <StoreContext.Provider value={{ store, state }}>
      {children}
    </StoreContext.Provider>
  );
}

// 创建钩子
function useStore(path) {
  const { store, state } = React.useContext(StoreContext);
  
  if (!store) {
    throw new Error('useStore 必须在 StoreProvider 内部使用');
  }
  
  // 获取特定路径的状态
  const value = path ? getNestedValue(state, path) : state;
  
  // 更新状态的函数
  const setValue = React.useCallback((newValue) => {
    if (path) {
      store.setState(path, newValue);
    } else {
      throw new Error('必须提供路径才能更新状态');
    }
  }, [store, path]);
  
  return [value, setValue];
}

// 使用钩子
function UserProfile() {
  const [user, setUser] = useStore('user');
  const [theme] = useStore('theme');
  
  const handleLogin = () => {
    setUser({
      id: 1,
      name: 'Admin',
      isLoggedIn: true
    });
  };
  
  const handleLogout = () => {
    setUser({
      id: null,
      name: '',
      isLoggedIn: false
    });
  };
  
  return (
    <div className={`profile ${theme}`}>
      {user.isLoggedIn ? (
        <>
          <h2>欢迎, {user.name}!</h2>
          <button onClick={handleLogout}>登出</button>
        </>
      ) : (
        <button onClick={handleLogin}>登录</button>
      )}
    </div>
  );
}

// 使用提供者
function App() {
  return (
    <StoreProvider>
      <UserProfile />
    </StoreProvider>
  );
}
```

### 与 Vue 集成

```typescript
// 创建存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  }
});

// 创建 Vue 插件
const StorePlugin = {
  install(app) {
    // 添加全局属性
    app.config.globalProperties.$store = store;
    
    // 添加组合式 API
    app.provide('store', store);
    
    // 添加指令
    app.directive('store', {
      beforeMount(el, binding) {
        const path = binding.value;
        const updateEl = () => {
          el.textContent = store.getState(path);
        };
        
        // 初始更新
        updateEl();
        
        // 监听变化
        const unsubscribe = store.subscribe(path, updateEl);
        
        // 保存取消订阅函数
        el._storeUnsubscribe = unsubscribe;
      },
      unmounted(el) {
        // 取消订阅
        if (el._storeUnsubscribe) {
          el._storeUnsubscribe();
        }
      }
    });
  }
};

// 创建组合式函数
function useStore(path) {
  const store = inject('store');
  
  if (!store) {
    throw new Error('useStore 必须在安装了 StorePlugin 的应用中使用');
  }
  
  // 创建响应式引用
  const value = ref(path ? store.getState(path) : store.getState());
  
  // 监听变化
  onMounted(() => {
    const unsubscribe = store.subscribe(path || '*', (newValue) => {
      value.value = newValue;
    });
    
    // 取消订阅
    onUnmounted(unsubscribe);
  });
  
  // 更新函数
  const setValue = (newValue) => {
    if (path) {
      store.setState(path, newValue);
    } else {
      throw new Error('必须提供路径才能更新状态');
    }
  };
  
  return [value, setValue];
}

// 使用插件
const app = createApp(App);
app.use(StorePlugin);
app.mount('#app');

// 在组件中使用
export default {
  setup() {
    const [user, setUser] = useStore('user');
    const [theme] = useStore('theme');
    
    const handleLogin = () => {
      setUser({
        id: 1,
        name: 'Admin',
        isLoggedIn: true
      });
    };
    
    const handleLogout = () => {
      setUser({
        id: null,
        name: '',
        isLoggedIn: false
      });
    };
    
    return {
      user,
      theme,
      handleLogin,
      handleLogout
    };
  }
};
```

### 与 Angular 集成

```typescript
// 创建存储
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  }
});

// 创建服务
@Injectable({
  providedIn: 'root'
})
class StoreService {
  // 获取状态
  getState(path?: string): any {
    return store.getState(path);
  }
  
  // 设置状态
  setState(path: string, value: any): void {
    store.setState(path, value);
  }
  
  // 订阅状态
  subscribe(path: string, callback: (newValue: any, oldValue: any, path: string) => void): () => void {
    return store.subscribe(path, callback);
  }
  
  // 观察状态
  select<T>(path: string): Observable<T> {
    return new Observable<T>(observer => {
      // 发送当前值
      observer.next(store.getState(path));
      
      // 订阅变化
      const unsubscribe = store.subscribe(path, (newValue) => {
        observer.next(newValue);
      });
      
      // 取消订阅
      return unsubscribe;
    });
  }
}

// 在组件中使用
@Component({
  selector: 'app-user-profile',
  template: `
    <div [class]="'profile ' + (theme$ | async)">
      <ng-container *ngIf="(user$ | async)?.isLoggedIn; else loginTemplate">
        <h2>欢迎, {{ (user$ | async)?.name }}!</h2>
        <button (click)="handleLogout()">登出</button>
      </ng-container>
      <ng-template #loginTemplate>
        <button (click)="handleLogin()">登录</button>
      </ng-template>
    </div>
  `
})
class UserProfileComponent implements OnInit, OnDestroy {
  user$: Observable<any>;
  theme$: Observable<string>;
  
  constructor(private storeService: StoreService) {}
  
  ngOnInit() {
    this.user$ = this.storeService.select<any>('user');
    this.theme$ = this.storeService.select<string>('theme');
  }
  
  handleLogin() {
    this.storeService.setState('user', {
      id: 1,
      name: 'Admin',
      isLoggedIn: true
    });
  }
  
  handleLogout() {
    this.storeService.setState('user', {
      id: null,
      name: '',
      isLoggedIn: false
    });
  }
  
  ngOnDestroy() {
    // 清理资源
  }
}
```

## 常见问题与解决方案

### 状态不同步

**问题**：多个微应用之间的状态不同步。

**解决方案**：

1. 使用同步插件
2. 实现状态广播
3. 使用共享存储

```typescript
// 使用同步插件
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  },
  
  // 添加同步插件
  plugins: [
    {
      plugin: syncPlugin,
      options: {
        channel: 'my-app-sync',
        scope: 'global'
      }
    }
  ]
});

// 实现状态广播
const bc = new BroadcastChannel('state-sync');

// 监听状态变化
store.subscribe('*', (newValue, oldValue, path) => {
  // 发送同步消息
  bc.postMessage({
    type: 'state-change',
    path,
    value: newValue
  });
});

// 监听同步消息
bc.onmessage = (event) => {
  const { type, path, value } = event.data;
  
  if (type === 'state-change') {
    // 更新状态，但不触发同步
    store._setState(path, value);
  }
};

// 使用共享存储
const sharedStore = window.microCore.sharedStore;

// 使用共享存储
const user = sharedStore.getState('user');
sharedStore.setState('theme', 'dark');
```

### 状态冲突

**问题**：多个微应用同时修改相同的状态，导致冲突。

**解决方案**：

1. 使用状态锁
2. 实现乐观更新
3. 使用版本控制

```typescript
// 使用状态锁
const store = new Store({
  state: {
    user: {
      id: null,
      name: '',
      isLoggedIn: false
    },
    theme: 'light',
    notifications: []
  }
});

// 添加锁中间件
store.use((context, next) => {
  const { path } = context;
  
  // 检查锁
  if (store._locks && store._locks[path]) {
    console.warn(`状态 ${path} 已被锁定，无法修改`);
    return;
  }
  
  return next();
});

// 锁定状态
store.lockState = function(path, timeout = 5000) {
  if (!this._locks) {
    this._locks = {};
  }
  
  this._locks[path] = true;
  
  // 设置超时
  setTimeout(() => {
    this.unlockState(path);
  }, timeout);
};

// 解锁状态
store.unlockState = function(path) {
  if (this._locks) {
    delete this._locks[path];
  }
};

// 使用锁
store.lockState('user');
store.setState('user.name', 'Admin'); // 警告: 状态 user 已被锁定，无法修改
store.unlockState('user');
store.setState('user.name', 'Admin'); // 正常工作
```

### 性能问题

**问题**：状态频繁变化导致性能下降。

**解决方案**：

1. 使用批量更新
2. 实现状态节流
3. 优化订阅

```typescript
// 使用批量更新
store.batch(() => {
  store.setState('user.id', 1);
  store.setState('user.name', 'Admin');
  store.setState('user.isLoggedIn', true);
  store.setState('notifications', []);
});

// 实现状态节流
function throttleState(store, path, wait = 300) {
  let lastValue = store.getState(path);
  let timeout = null;
  
  // 返回节流函数
  return (value) => {
    clearTimeout(timeout);
    
    timeout = setTimeout(() => {
      if (value !== lastValue) {
        store.setState(path, value);
        lastValue = value;
      }
    }, wait);
  };
}

// 使用节流
const setThrott