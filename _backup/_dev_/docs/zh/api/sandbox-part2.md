# 沙箱 API (第二部分)

## StyleSandbox (续)

### API 参考 (续)

#### addStyle(cssText)

添加 CSS 样式文本。

```typescript
addStyle(cssText: string): HTMLStyleElement
```

**参数：**
- `cssText` (string): CSS 样式文本

**返回值：** 创建的 style 元素

**示例：**

```typescript
// 添加样式
const styleElement = styleSandbox.addStyle(`
  .title {
    color: red;
    font-size: 20px;
  }
  
  #container {
    padding: 10px;
    margin: 20px;
  }
  
  body {
    background-color: #f0f0f0;
  }
`);

// 添加带注释的样式
styleSandbox.addStyle(`
  /* 标题样式 */
  .title {
    color: blue;
    font-weight: bold;
  }
  
  /* 容器样式 */
  .container {
    border: 1px solid #ccc;
    border-radius: 4px;
  }
`);

// 添加媒体查询
styleSandbox.addStyle(`
  @media (max-width: 768px) {
    .title {
      font-size: 16px;
    }
    
    .container {
      padding: 5px;
    }
  }
`);

// 添加关键帧动画
styleSandbox.addStyle(`
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .animated {
    animation: fadeIn 1s ease-in-out;
  }
`);
```

#### addStylesheet(url)

添加外部样式表。

```typescript
addStylesheet(url: string): Promise<HTMLLinkElement>
```

**参数：**
- `url` (string): 样式表 URL

**返回值：** 返回一个 Promise，解析为创建的 link 元素

**示例：**

```typescript
// 添加外部样式表
styleSandbox.addStylesheet('https://example.com/app1/style.css')
  .then(linkElement => {
    console.log('样式表加载成功:', linkElement);
  })
  .catch(error => {
    console.error('样式表加载失败:', error);
  });

// 添加多个样式表
Promise.all([
  styleSandbox.addStylesheet('https://example.com/app1/base.css'),
  styleSandbox.addStylesheet('https://example.com/app1/theme.css'),
  styleSandbox.addStylesheet('https://example.com/app1/components.css')
])
  .then(linkElements => {
    console.log('所有样式表加载成功');
  })
  .catch(error => {
    console.error('样式表加载失败:', error);
  });
```

#### removeStyle(styleElement)

移除样式元素。

```typescript
removeStyle(styleElement: HTMLStyleElement | HTMLLinkElement): boolean
```

**参数：**
- `styleElement` (HTMLStyleElement | HTMLLinkElement): 样式元素

**返回值：** 是否成功移除

**示例：**

```typescript
// 添加样式并保存引用
const styleElement = styleSandbox.addStyle(`
  .title {
    color: red;
  }
`);

// 移除样式
const removed = styleSandbox.removeStyle(styleElement);
console.log('是否移除成功:', removed);

// 添加样式表并保存引用
styleSandbox.addStylesheet('https://example.com/app1/style.css')
  .then(linkElement => {
    // 移除样式表
    const removed = styleSandbox.removeStyle(linkElement);
    console.log('是否移除成功:', removed);
  });
```

#### removeAllStyles()

移除所有样式。

```typescript
removeAllStyles(): void
```

**示例：**

```typescript
// 添加多个样式
styleSandbox.addStyle(`
  .title {
    color: red;
  }
`);

styleSandbox.addStyle(`
  .container {
    padding: 10px;
  }
`);

styleSandbox.addStylesheet('https://example.com/app1/style.css');

// 移除所有样式
styleSandbox.removeAllStyles();

// 在组件卸载时清理样式
function unmountComponent() {
  // 移除所有样式
  styleSandbox.removeAllStyles();
  
  // 停用样式沙箱
  styleSandbox.deactivate();
}
```

#### scopeCssText(cssText)

对 CSS 文本进行作用域隔离处理。

```typescript
scopeCssText(cssText: string): string
```

**参数：**
- `cssText` (string): CSS 文本

**返回值：** 处理后的 CSS 文本

**示例：**

```typescript
// 原始 CSS
const originalCss = `
  .title {
    color: red;
    font-size: 20px;
  }
  
  #container {
    padding: 10px;
    margin: 20px;
  }
  
  body {
    background-color: #f0f0f0;
  }
`;

// 作用域处理
const scopedCss = styleSandbox.scopeCssText(originalCss);
console.log('作用域处理后的 CSS:', scopedCss);

// 手动添加到文档
const style = document.createElement('style');
style.textContent = scopedCss;
document.head.appendChild(style);
```

#### isActive()

检查样式沙箱是否处于激活状态。

```typescript
isActive(): boolean
```

**返回值：** 样式沙箱是否激活

**示例：**

```typescript
// 检查样式沙箱状态
const isActive = styleSandbox.isActive();
console.log('样式沙箱是否激活:', isActive);

// 条件激活
if (!styleSandbox.isActive()) {
  console.log('样式沙箱未激活，正在激活...');
  styleSandbox.activate();
}

// 条件添加样式
if (styleSandbox.isActive()) {
  styleSandbox.addStyle(`
    .title {
      color: red;
    }
  `);
} else {
  console.warn('样式沙箱未激活，无法添加样式');
}
```

## StorageSandbox

`StorageSandbox` 是 Micro-Core 提供的存储沙箱类，用于隔离微应用的 localStorage 和 sessionStorage。

### 基本用法

```typescript
import { StorageSandbox } from '@micro-core/core';

// 创建存储沙箱实例
const storageSandbox = new StorageSandbox({
  name: 'app1',
  type: 'localStorage',
  prefix: 'app1:'
});

// 激活存储沙箱
storageSandbox.activate();

// 设置存储项
storageSandbox.setItem('user', JSON.stringify({ id: 1, name: 'Admin' }));

// 获取存储项
const user = JSON.parse(storageSandbox.getItem('user'));
console.log('用户:', user);

// 移除存储项
storageSandbox.removeItem('user');

// 清除所有存储项
storageSandbox.clear();

// 停用存储沙箱
storageSandbox.deactivate();
```

### API 参考

#### 构造函数

创建存储沙箱实例。

```typescript
constructor(options?: StorageSandboxOptions)
```

**参数：**
- `options` (StorageSandboxOptions): 可选，存储沙箱配置选项
  - `name` (string): 沙箱名称
  - `type` (string): 存储类型，'localStorage' 或 'sessionStorage'，默认为 'localStorage'
  - `prefix` (string): 键前缀，默认为 '{name}:'
  - `useProxy` (boolean): 是否使用代理，默认为 true
  - `initialData` (Record<string, string>): 初始数据

**示例：**

```typescript
// 基本用法
const storageSandbox = new StorageSandbox();

// localStorage 沙箱
const localStorageSandbox = new StorageSandbox({
  name: 'app1',
  type: 'localStorage',
  prefix: 'app1:'
});

// sessionStorage 沙箱
const sessionStorageSandbox = new StorageSandbox({
  name: 'app1',
  type: 'sessionStorage',
  prefix: 'app1:'
});

// 带初始数据的沙箱
const storageSandbox = new StorageSandbox({
  name: 'app1',
  type: 'localStorage',
  prefix: 'app1:',
  initialData: {
    'theme': 'light',
    'language': 'zh-CN',
    'user': JSON.stringify({ id: 1, name: 'Admin' })
  }
});
```

#### activate()

激活存储沙箱。

```typescript
activate(): void
```

**示例：**

```typescript
// 激活存储沙箱
storageSandbox.activate();

// 在组件挂载时激活存储沙箱
function mountComponent() {
  // 激活存储沙箱
  storageSandbox.activate();
  
  // 渲染组件
  renderComponent();
}
```

#### deactivate()

停用存储沙箱。

```typescript
deactivate(): void
```

**示例：**

```typescript
// 停用存储沙箱
storageSandbox.deactivate();

// 在组件卸载时停用存储沙箱
function unmountComponent() {
  // 停用存储沙箱
  storageSandbox.deactivate();
  
  // 清理资源
  cleanupResources();
}
```

#### getItem(key)

获取存储项。

```typescript
getItem(key: string): string | null
```

**参数：**
- `key` (string): 键名

**返回值：** 存储项值，如果不存在则返回 null

**示例：**

```typescript
// 获取简单值
const theme = storageSandbox.getItem('theme');
console.log('主题:', theme);

// 获取 JSON 值
const userJson = storageSandbox.getItem('user');
if (userJson) {
  const user = JSON.parse(userJson);
  console.log('用户:', user);
}

// 检查项是否存在
const language = storageSandbox.getItem('language');
if (language) {
  console.log('语言:', language);
} else {
  console.log('未设置语言');
}
```

#### setItem(key, value)

设置存储项。

```typescript
setItem(key: string, value: string): void
```

**参数：**
- `key` (string): 键名
- `value` (string): 值

**示例：**

```typescript
// 设置简单值
storageSandbox.setItem('theme', 'dark');
storageSandbox.setItem('language', 'zh-CN');

// 设置 JSON 值
const user = { id: 1, name: 'Admin', role: 'admin' };
storageSandbox.setItem('user', JSON.stringify(user));

// 设置数组
const permissions = ['read', 'write', 'delete'];
storageSandbox.setItem('permissions', JSON.stringify(permissions));

// 设置带过期时间的值
function setWithExpiry(key, value, ttl) {
  const item = {
    value: value,
    expiry: Date.now() + ttl
  };
  storageSandbox.setItem(key, JSON.stringify(item));
}

// 设置 1 小时过期的值
setWithExpiry('token', 'abc123', 3600000);

// 获取带过期时间的值
function getWithExpiry(key) {
  const itemStr = storageSandbox.getItem(key);
  if (!itemStr) {
    return null;
  }
  
  const item = JSON.parse(itemStr);
  if (Date.now() > item.expiry) {
    storageSandbox.removeItem(key);
    return null;
  }
  
  return item.value;
}

// 获取 token
const token = getWithExpiry('token');
console.log('Token:', token);
```

#### removeItem(key)

移除存储项。

```typescript
removeItem(key: string): void
```

**参数：**
- `key` (string): 键名

**示例：**

```typescript
// 移除单个项
storageSandbox.removeItem('theme');

// 条件移除
const user = JSON.parse(storageSandbox.getItem('user') || '{}');
if (user.role === 'guest') {
  storageSandbox.removeItem('user');
}

// 移除过期项
function removeExpiredItems() {
  const keys = storageSandbox.keys();
  
  keys.forEach(key => {
    if (key.endsWith('_expiry')) {
      const expiryStr = storageSandbox.getItem(key);
      if (expiryStr) {
        const expiry = parseInt(expiryStr, 10);
        if (Date.now() > expiry) {
          // 移除过期项和过期标记
          const actualKey = key.replace('_expiry', '');
          storageSandbox.removeItem(actualKey);
          storageSandbox.removeItem(key);
        }
      }
    }
  });
}
```

#### clear()

清除所有存储项。

```typescript
clear(): void
```

**示例：**

```typescript
// 清除所有项
storageSandbox.clear();

// 在用户登出时清除
function logout() {
  // 清除用户相关存储
  storageSandbox.clear();
  
  // 重定向到登录页
  window.location.href = '/login';
}
```

#### key(index)

获取指定索引的键名。

```typescript
key(index: number): string | null
```

**参数：**
- `index` (number): 索引

**返回值：** 键名，如果不存在则返回 null

**示例：**

```typescript
// 获取第一个键
const firstKey = storageSandbox.key(0);
console.log('第一个键:', firstKey);

// 遍历所有键
for (let i = 0; i < storageSandbox.length; i++) {
  const key = storageSandbox.key(i);
  if (key) {
    const value = storageSandbox.getItem(key);
    console.log(`${key}: ${value}`);
  }
}
```

#### keys()

获取所有键名。

```typescript
keys(): string[]
```

**返回值：** 键名数组

**示例：**

```typescript
// 获取所有键
const keys = storageSandbox.keys();
console.log('所有键:', keys);

// 遍历所有键值对
keys.forEach(key => {
  const value = storageSandbox.getItem(key);
  console.log(`${key}: ${value}`);
});

// 过滤键
const userKeys = keys.filter(key => key.startsWith('user.'));
console.log('用户相关键:', userKeys);
```

#### has(key)

检查是否存在指定键。

```typescript
has(key: string): boolean
```

**参数：**
- `key` (string): 键名

**返回值：** 是否存在

**示例：**

```typescript
// 检查键是否存在
const hasTheme = storageSandbox.has('theme');
console.log('是否有主题设置:', hasTheme);

// 条件操作
if (storageSandbox.has('user')) {
  const user = JSON.parse(storageSandbox.getItem('user') || '{}');
  console.log('用户:', user);
} else {
  console.log('未登录');
}
```

#### size()

获取存储项数量。

```typescript
size(): number
```

**返回值：** 存储项数量

**示例：**

```typescript
// 获取存储项数量
const count = storageSandbox.size();
console.log('存储项数量:', count);

// 检查是否为空
if (storageSandbox.size() === 0) {
  console.log('存储为空');
} else {
  console.log(`存储包含 ${storageSandbox.size()} 项`);
}
```

#### getStorage()

获取原始存储对象。

```typescript
getStorage(): Storage
```

**返回值：** 原始存储对象

**示例：**

```typescript
// 获取原始存储对象
const storage = storageSandbox.getStorage();

// 直接操作原始存储
storage.setItem('global-key', 'global-value');

// 检查原始存储中的值
console.log('原始存储中的值:', storage.getItem('global-key'));
```

#### getProxyStorage()

获取代理存储对象。

```typescript
getProxyStorage(): Storage
```

**返回值：** 代理存储对象

**示例：**

```typescript
// 获取代理存储对象
const proxyStorage = storageSandbox.getProxyStorage();

// 使用代理存储
proxyStorage.setItem('key', 'value');
const value = proxyStorage.getItem('key');
console.log('值:', value);

// 在沙箱中执行代码
sandbox.execScript(`
  // 使用沙箱存储
  localStorage.setItem('app-key', 'app-value');
  console.log('应用值:', localStorage.getItem('app-key'));
`);
```

## 高级功能

### 多沙箱管理

Micro-Core 沙箱系统支持多个沙箱实例的管理，用于隔离多个微应用。

```typescript
import { SandboxManager } from '@micro-core/core';

// 创建沙箱管理器
const sandboxManager = new SandboxManager();

// 创建沙箱
const app1Sandbox = sandboxManager.createSandbox({
  name: 'app1',
  el: '#app1-container'
});

const app2Sandbox = sandboxManager.createSandbox({
  name: 'app2',
  el: '#app2-container'
});

// 获取沙箱
const sandbox = sandboxManager.getSandbox('app1');

// 激活沙箱
sandboxManager.activateSandbox('app1');

// 停用沙箱
sandboxManager.deactivateSandbox('app1');

// 删除沙箱
sandboxManager.removeSandbox('app1');

// 清除所有沙箱
sandboxManager.clear();
```

### 沙箱快照

Micro-Core 沙箱系统支持创建和恢复沙箱状态的快照，用于保存和恢复沙箱状态。

```typescript
// 创建沙箱
const sandbox = new Sandbox({
  name: 'app1'
});

// 激活沙箱
sandbox.activate();

// 设置初始状态
sandbox.setGlobalValue('counter', 0);
sandbox.setGlobalValue('theme', 'light');

// 创建快照
const snapshot = sandbox.snapshot();

// 修改状态
sandbox.setGlobalValue('counter', 10);
sandbox.setGlobalValue('theme', 'dark');
sandbox.setGlobalValue('newValue', 'added after snapshot');

// 从快照恢复
sandbox.restore(snapshot);

// 验证恢复
console.log('恢复后 counter:', sandbox.getGlobalValue('counter')); // 输出: 恢复后 counter: 0
console.log('恢复后 theme:', sandbox.getGlobalValue('theme')); // 输出: 恢复后 theme: light
console.log('恢复后 newValue:', sandbox.getGlobalValue('newValue')); // 输出: 恢复后 newValue: undefined
```

### 沙箱插件

Micro-Core 沙箱系统支持插件机制，用于扩展沙箱功能。

```typescript
// 定义沙箱插件
const loggingPlugin = {
  name: 'logging',
  
  // 沙箱创建时调用
  onCreate(sandbox) {
    console.log(`沙箱 ${sandbox.name} 创建`);
  },
  
  // 沙箱激活时调用
  onActivate(sandbox) {
    console.log(`沙箱 ${sandbox.name} 激活`);
  },
  
  // 沙箱停用时调用
  onDeactivate(sandbox) {
    console.log(`沙箱 ${sandbox.name} 停用`);
  },
  
  // 沙箱销毁时调用
  onDestroy(sandbox) {
    console.log(`沙箱 ${sandbox.name} 销毁`);
  },
  
  // 执行代码前调用
  beforeExecScript(code, sandbox) {
    console.log(`沙箱 ${sandbox.name} 执行代码前，代码长度: ${code.length}`);
    return code; // 可以修改代码后返回
  },
  
  // 执行代码后调用
  afterExecScript(result, sandbox) {
    console.log(`沙箱 ${sandbox.name} 执行代码后`);
    return result; // 可以修改结果后返回
  }
};

// 注册插件
Sandbox.registerPlugin(loggingPlugin);

// 创建沙箱
const sandbox = new Sandbox({
  name: 'app1',
  plugins: ['logging'] // 启用插件
});
```

### 沙箱中间件

Micro-Core 沙箱系统支持中间件机制，用于拦截和处理沙箱操作。

```typescript
// 创建沙箱
const sandbox = new Sandbox({
  name: 'app1'
});

// 添加全局变量访问中间件
sandbox.use((context, next) => {
  const { type, key, value } = context;
  
  if (type === 'get') {
    console.log(`获取全局变量: ${key}`);
  } else if (type === 'set') {
    console.log(`设置全局变量: ${key} = ${value}`);
  }
  
  return next();
});

// 添加代码执行中间件
sandbox.use((context, next) => {
  if (context.type === 'execScript') {
    console.log(`执行代码，长度: ${context.code.length}`);
    
    const startTime = Date.now();
    const result = next();
    const endTime = Date.now();
    
    console.log(`代码执行完成，耗时: ${endTime - startTime}ms`);
    
    return result;
  }
  
  return next();
});

// 添加错误处理中间件
sandbox.use((context, next) => {
  try {
    return next();
  } catch (error) {
    console.error(`沙箱操作错误: ${error.message}`);
    throw error;
  }
});
```

## 与其他框架集成

### 与 qiankun 集成

```typescript
import { registerMicroApps, start } from 'qiankun';
import { Sandbox } from '@micro-core/core';

// 创建沙箱
const sandbox = new Sandbox({
  name: 'micro-app',
  el: '#micro-container'
});

// 注册微应用
registerMicroApps([
  {
    name: 'micro-app',
    entry: '//localhost:8081',
    container: '#micro-container',
    activeRule: '/micro-app',
    props: {
      sandbox // 传递沙箱给微应用
    }
  }
]);

// 启动 qiankun
start({
  sandbox: {
    // 使用自定义沙箱
    ...sandbox.getQiankunOptions()
  }
});
```

### 与 wujie (无界) 集成

```typescript
import { bus, setupApp, preloadApp, startApp } from 'wujie';
import { Sandbox } from '@micro-core/core';

// 创建沙箱
const sandbox = new Sandbox({
  name: 'micro-app',
  el: '#micro-container'
});

// 设置微应用
setupApp({
  name: 'micro-app',
  url: '//localhost:8081',
  exec: true,
  props: {
    sandbox // 传递沙箱给微应用
  },
  beforeLoad: (appWindow) => {
    // 激活沙箱
    sandbox.activate();
    
    // 将沙箱代理的 window 对象与 wujie 的 appWindow 集成
    Object.assign(appWindow, sandbox.getProxyWindow());
  },
  beforeUnmount: () => {
    // 停用沙箱
    sandbox.deactivate();
  }
});

// 启动微应用
startApp({
  name: 'micro-app',
  container: '#micro-container',
  url: '//localhost:8081'
});
```

## 常见问题与解决方案

### 全局变量冲突

**问题**：多个微应用使用相同的全局变量名，导致冲突。

**解决方案**：

1. 启用严格隔离模式
2. 使用自定义全局变量
3. 实现变量命名空间

```typescript
// 启用严格隔离模式
const sandbox = new Sandbox({
  name: 'app1',
  strictIsolation: true
});

// 使用自定义全局变量
const sandbox = new Sandbox({
  name: 'app1',
  customGlobals: {
    // 应用专属全局变量
    appName: 'App1',
    appVersion: '1.0.0',
    
    // 覆盖可能冲突的变量
    jQuery: customJQuery,
    $: customJQuery
  }
});

// 实现变量命名空间
sandbox.execScript(`
  // 创建应用命名空间
  window.APP1 = {};
  
  // 在命名空间中定义变量
  APP1.config = {
    theme: 'light',
    language: 'zh-CN'
  };
  
  // 在命名空间中定义函数
  APP1.init = function() {
    console.log('App1 初始化');
  };
`);
```

### 样式冲突

**问题**：多个微应用的 CSS 样式相互影响。

**解决方案**：

1. 启用样式隔离
2. 使用 CSS 命名空间
3. 使用 CSS Modules 或 CSS-in-JS

```typescript
// 启用样式隔离
const styleSandbox = new StyleSandbox({
  name: 'app1',
  el: '#app1-container',
  scopeSelector: '[data-app="app1"]',
  useStrictMode: true
});

// 使用 CSS 命名空间
styleSandbox.addStyle(`
  /* 使用应用前缀 */
  .app1-title {
    color: red;
  }
  
  .app1-container {
    padding: 10px;
  }
`);

// 使用 BEM 命名约定
styleSandbox.addStyle(`
  /* 使用 BEM 命名约定 */
  .app1__title {
    color: red;
  }
  
  .app1__container {
    padding: 10px;
  }
  
  .app1__button--primary {
    background-color: blue;
  }
`);
```

### 事件冲突

**问题**：多个微应用监听相同的全局事件，导致冲突。

**解决方案**：

1. 启用事件隔离
2. 使用事件命名空间
3. 在卸载时清理事件监听器

```typescript
// 启用事件隔离
const sandbox = new Sandbox({
  name: 'app1',
  isolateWindowEvents: true
});

// 使用事件命名空间
sandbox.execScript(`
  // 使用应用前缀
  window.addEventListener('app1:click', (event) => {
    console.log('App1 点击事件');
  });
  
  // 触发带命名空间的事件
  const event = new CustomEvent('app1:click');
  window.dispatchEvent(event);
`);

// 在卸载时清理事件监听器
sandbox.execScript(`
  // 保存事件监听器引用
  const listeners = [];
  
  function addListener(target, type, handler, options) {
    target.addEventListener(type, handler, options);
    listeners.push({ target, type, handler });
  }
  
  // 添加事件监听器
  addListener(window, 'resize', handleResize);
  addListener(document, 'click', handleClick);
  
  // 清理函数
  window.__cleanupEventListeners = function() {
    listeners.forEach(({ target, type, handler }) => {
      target.removeEventListener(type, handler);
    });
    listeners.length = 0;
  };
`);

// 在卸载时调用清理函数
function unmountApp() {
  sandbox.execScript(`
    if (typeof window.__cleanupEventListeners === 'function') {
      window.__cleanupEventListeners();
    }
  `