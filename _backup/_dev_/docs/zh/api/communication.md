# 通信 API

Micro-Core 提供了强大的通信系统，用于主应用和微应用之间以及微应用之间的通信。本文档详细介绍了通信 API 的使用方法和最佳实践。

## 基本概念

在微前端架构中，通信系统负责以下任务：

1. **主应用与微应用通信**：主应用向微应用传递数据和命令，微应用向主应用报告状态和事件
2. **微应用之间通信**：不同微应用之间交换数据和协调操作
3. **全局状态共享**：在所有应用之间共享全局状态
4. **事件广播**：向所有应用广播事件

## MessageChannel

`MessageChannel` 是 Micro-Core 提供的核心通信类，用于创建和管理通信通道。

### 基本用法

```typescript
import { MessageChannel } from '@micro-core/core';

// 创建通信通道
const channel = new MessageChannel({
  name: 'main-channel',
  type: 'broadcast'
});

// 发送消息
channel.send('greeting', { text: 'Hello from main app!' });

// 监听消息
channel.on('response', (data) => {
  console.log('收到响应:', data);
});

// 移除监听器
const handler = (data) => {
  console.log('收到消息:', data);
};
channel.on('message', handler);
channel.off('message', handler);
```

### API 参考

#### 构造函数

创建通信通道实例。

```typescript
constructor(options?: ChannelOptions)
```

**参数：**
- `options` (ChannelOptions): 可选，通道配置选项
  - `name` (string): 通道名称，默认为随机生成的名称
  - `type` (string): 通道类型，可选值为 'broadcast', 'point-to-point', 'request-response'，默认为 'broadcast'
  - `scope` (string): 通道作用域，默认为 'global'
  - `timeout` (number): 请求超时时间，单位为毫秒，默认为 5000
  - `bufferSize` (number): 消息缓冲区大小，默认为 100
  - `secure` (boolean): 是否启用安全模式，默认为 false
  - `serializer` (Function): 自定义序列化函数
  - `deserializer` (Function): 自定义反序列化函数

**示例：**

```typescript
// 基本用法
const channel = new MessageChannel();

// 完整配置
const channel = new MessageChannel({
  name: 'main-channel',
  type: 'broadcast',
  scope: 'global',
  timeout: 10000,
  bufferSize: 200,
  secure: true,
  serializer: (data) => JSON.stringify(data),
  deserializer: (message) => JSON.parse(message)
});

// 点对点通道
const p2pChannel = new MessageChannel({
  name: 'p2p-channel',
  type: 'point-to-point'
});

// 请求响应通道
const reqResChannel = new MessageChannel({
  name: 'req-res-channel',
  type: 'request-response',
  timeout: 3000
});
```

#### send(type, data, options)

发送消息。

```typescript
send(type: string, data?: any, options?: SendOptions): void
```

**参数：**
- `type` (string): 消息类型
- `data` (any): 可选，消息数据
- `options` (SendOptions): 可选，发送选项
  - `target` (string): 目标接收者，仅在 'point-to-point' 类型通道中有效
  - `timeout` (number): 请求超时时间，单位为毫秒
  - `priority` (number): 消息优先级，数字越大优先级越高
  - `ttl` (number): 消息生存时间，单位为毫秒
  - `retry` (boolean | number): 是否重试或重试次数
  - `retryDelay` (number): 重试间隔时间，单位为毫秒
  - `onSuccess` (Function): 发送成功回调函数
  - `onError` (Function): 发送失败回调函数

**示例：**

```typescript
// 基本用法
channel.send('greeting', { text: 'Hello!' });

// 带选项的发送
channel.send('request', { id: 1, action: 'getData' }, {
  target: 'app1',
  timeout: 5000,
  priority: 1,
  onSuccess: () => console.log('发送成功'),
  onError: (error) => console.error('发送失败:', error)
});

// 发送高优先级消息
channel.send('important', { action: 'alert', message: '系统即将维护' }, {
  priority: 10
});

// 发送带生存时间的消息
channel.send('notification', { text: '新消息' }, {
  ttl: 60000 // 1分钟
});

// 发送带重试的消息
channel.send('critical', { action: 'save', data: userData }, {
  retry: 3,
  retryDelay: 1000
});
```

#### request(type, data, options)

发送请求并等待响应。

```typescript
request(type: string, data?: any, options?: RequestOptions): Promise<any>
```

**参数：**
- `type` (string): 请求类型
- `data` (any): 可选，请求数据
- `options` (RequestOptions): 可选，请求选项
  - `target` (string): 目标接收者
  - `timeout` (number): 请求超时时间，单位为毫秒
  - `priority` (number): 请求优先级，数字越大优先级越高
  - `retry` (boolean | number): 是否重试或重试次数
  - `retryDelay` (number): 重试间隔时间，单位为毫秒

**返回值：** 返回一个 Promise，解析为响应数据

**示例：**

```typescript
// 基本用法
channel.request('getData', { id: 1 })
  .then(response => {
    console.log('收到响应:', response);
  })
  .catch(error => {
    console.error('请求失败:', error);
  });

// 带选项的请求
channel.request('getUser', { userId: 123 }, {
  target: 'user-service',
  timeout: 10000,
  retry: 3
})
  .then(user => {
    console.log('用户数据:', user);
  })
  .catch(error => {
    console.error('获取用户数据失败:', error);
  });

// 使用 async/await
async function fetchData() {
  try {
    const data = await channel.request('fetchData', { query: 'example' });
    console.log('数据:', data);
  } catch (error) {
    console.error('获取数据失败:', error);
  }
}

// 批量请求
Promise.all([
  channel.request('getData', { id: 1 }),
  channel.request('getUser', { userId: 123 }),
  channel.request('getConfig')
])
  .then(([data, user, config]) => {
    console.log('所有数据:', { data, user, config });
  })
  .catch(error => {
    console.error('批量请求失败:', error);
  });
```

#### respond(type, handler)

注册响应处理器。

```typescript
respond(type: string, handler: (data: any, request: Request) => any): void
```

**参数：**
- `type` (string): 请求类型
- `handler` ((data: any, request: Request) => any): 响应处理函数，接收请求数据和请求对象，返回响应数据

**示例：**

```typescript
// 注册响应处理器
channel.respond('getData', (data) => {
  console.log('收到数据请求:', data);
  
  // 返回响应数据
  return {
    id: data.id,
    name: 'Example',
    value: Math.random()
  };
});

// 异步响应处理器
channel.respond('getUser', async (data) => {
  console.log('收到用户请求:', data);
  
  // 异步获取用户数据
  const user = await fetchUserFromDatabase(data.userId);
  
  // 返回用户数据
  return user;
});

// 带错误处理的响应处理器
channel.respond('processData', (data, request) => {
  console.log(`收到处理数据请求，来自: ${request.sender}`);
  
  try {
    // 处理数据
    const result = processData(data);
    return result;
  } catch (error) {
    // 抛出错误，将被转换为拒绝的 Promise
    throw new Error(`处理数据失败: ${error.message}`);
  }
});
```

#### on(type, handler)

监听消息。

```typescript
on(type: string, handler: (data: any, message: Message) => void): void
```

**参数：**
- `type` (string): 消息类型
- `handler` ((data: any, message: Message) => void): 消息处理函数，接收消息数据和消息对象

**示例：**

```typescript
// 监听消息
channel.on('greeting', (data) => {
  console.log('收到问候:', data.text);
});

// 带消息对象的处理函数
channel.on('notification', (data, message) => {
  console.log(`收到通知，来自: ${message.sender}`);
  console.log('通知内容:', data);
  console.log('发送时间:', new Date(message.timestamp));
});

// 监听所有消息
channel.on('*', (data, message) => {
  console.log(`收到消息，类型: ${message.type}`);
  console.log('消息数据:', data);
});

// 监听错误
channel.on('error', (error) => {
  console.error('通道错误:', error);
});
```

#### off(type, handler)

取消监听消息。

```typescript
off(type: string, handler?: (data: any, message: Message) => void): void
```

**参数：**
- `type` (string): 消息类型
- `handler` ((data: any, message: Message) => void): 可选，消息处理函数，如果不提供则移除所有该类型的监听器

**示例：**

```typescript
// 定义消息处理函数
function handleGreeting(data) {
  console.log('收到问候:', data.text);
}

// 监听消息
channel.on('greeting', handleGreeting);

// 取消特定处理函数的监听
channel.off('greeting', handleGreeting);

// 取消所有 'greeting' 类型的监听
channel.off('greeting');

// 取消所有监听
channel.off('*');
```

#### once(type, handler)

监听一次消息，收到后自动移除监听器。

```typescript
once(type: string, handler: (data: any, message: Message) => void): void
```

**参数：**
- `type` (string): 消息类型
- `handler` ((data: any, message: Message) => void): 消息处理函数，接收消息数据和消息对象

**示例：**

```typescript
// 监听一次消息
channel.once('init', (data) => {
  console.log('收到初始化数据:', data);
  // 初始化应用
  initApp(data);
});

// 监听一次响应
channel.send('request', { id: 1 });
channel.once('response', (data) => {
  console.log('收到一次性响应:', data);
});
```

#### broadcast(type, data)

广播消息给所有监听者。

```typescript
broadcast(type: string, data?: any): void
```

**参数：**
- `type` (string): 消息类型
- `data` (any): 可选，消息数据

**示例：**

```typescript
// 广播消息
channel.broadcast('globalNotification', {
  level: 'info',
  message: '系统已更新到最新版本'
});

// 广播系统事件
channel.broadcast('systemEvent', {
  type: 'maintenance',
  startTime: new Date(Date.now() + 3600000),
  duration: '2小时'
});

// 广播状态变更
channel.broadcast('stateChange', {
  user: {
    id: 1,
    name: 'Admin',
    isLoggedIn: true
  }
});
```

#### connect(target)

连接到目标通道。

```typescript
connect(target: string | MessageChannel): boolean
```

**参数：**
- `target` (string | MessageChannel): 目标通道名称或通道实例

**返回值：** 连接是否成功

**示例：**

```typescript
// 创建两个通道
const channelA = new MessageChannel({ name: 'channel-a' });
const channelB = new MessageChannel({ name: 'channel-b' });

// 连接通道
channelA.connect(channelB);

// 通过名称连接通道
channelA.connect('channel-c');

// 检查连接结果
const isConnected = channelA.connect('channel-d');
if (isConnected) {
  console.log('通道连接成功');
} else {
  console.error('通道连接失败');
}
```

#### disconnect(target)

断开与目标通道的连接。

```typescript
disconnect(target: string | MessageChannel): boolean
```

**参数：**
- `target` (string | MessageChannel): 目标通道名称或通道实例

**返回值：** 断开连接是否成功

**示例：**

```typescript
// 断开与特定通道的连接
channelA.disconnect(channelB);

// 通过名称断开连接
channelA.disconnect('channel-c');

// 检查断开连接结果
const isDisconnected = channelA.disconnect('channel-d');
if (isDisconnected) {
  console.log('通道断开连接成功');
} else {
  console.error('通道断开连接失败');
}

// 断开所有连接
channelA.disconnect();
```

#### isConnected(target)

检查是否已连接到目标通道。

```typescript
isConnected(target: string | MessageChannel): boolean
```

**参数：**
- `target` (string | MessageChannel): 目标通道名称或通道实例

**返回值：** 是否已连接

**示例：**

```typescript
// 检查是否已连接
const connected = channelA.isConnected(channelB);
console.log('是否已连接:', connected);

// 通过名称检查连接
const connectedByName = channelA.isConnected('channel-c');
console.log('是否已连接到 channel-c:', connectedByName);
```

#### getConnections()

获取所有已连接的通道。

```typescript
getConnections(): Array<MessageChannel>
```

**返回值：** 已连接的通道数组

**示例：**

```typescript
// 获取所有连接
const connections = channelA.getConnections();
console.log('已连接的通道数量:', connections.length);

// 遍历连接
connections.forEach(channel => {
  console.log('已连接通道:', channel.name);
});
```

#### close()

关闭通道。

```typescript
close(): void
```

**示例：**

```typescript
// 关闭通道
channel.close();

// 在组件卸载时关闭通道
function unmountComponent() {
  // 清理资源
  channel.close();
}
```

## EventBus

`EventBus` 是 Micro-Core 提供的事件总线，用于在应用之间传递事件。

### 基本用法

```typescript
import { EventBus } from '@micro-core/core';

// 创建事件总线
const eventBus = new EventBus();

// 发布事件
eventBus.emit('userLoggedIn', { userId: 1, username: 'admin' });

// 订阅事件
eventBus.on('userLoggedIn', (data) => {
  console.log('用户登录:', data.username);
});

// 取消订阅
const handler = (data) => {
  console.log('收到事件:', data);
};
eventBus.on('event', handler);
eventBus.off('event', handler);
```

### API 参考

#### 构造函数

创建事件总线实例。

```typescript
constructor(options?: EventBusOptions)
```

**参数：**
- `options` (EventBusOptions): 可选，事件总线配置选项
  - `scope` (string): 事件总线作用域，默认为 'global'
  - `maxListeners` (number): 每个事件类型的最大监听器数量，默认为 10
  - `wildcard` (boolean): 是否支持通配符，默认为 true
  - `delimiter` (string): 事件名称分隔符，默认为 '.'
  - `newListener` (boolean): 是否触发 newListener 事件，默认为 true
  - `removeListener` (boolean): 是否触发 removeListener 事件，默认为 true

**示例：**

```typescript
// 基本用法
const eventBus = new EventBus();

// 完整配置
const eventBus = new EventBus({
  scope: 'app1',
  maxListeners: 20,
  wildcard: true,
  delimiter: '.',
  newListener: true,
  removeListener: true
});
```

#### emit(event, data)

发布事件。

```typescript
emit(event: string, data?: any): boolean
```

**参数：**
- `event` (string): 事件名称
- `data` (any): 可选，事件数据

**返回值：** 是否有监听器处理了该事件

**示例：**

```typescript
// 发布简单事件
eventBus.emit('click');

// 发布带数据的事件
eventBus.emit('userLoggedIn', {
  userId: 1,
  username: 'admin',
  timestamp: Date.now()
});

// 发布分层事件
eventBus.emit('user.profile.updated', {
  userId: 1,
  fields: ['name', 'avatar']
});

// 检查是否有监听器
const hasListeners = eventBus.emit('customEvent', { foo: 'bar' });
if (!hasListeners) {
  console.warn('没有监听器处理 customEvent 事件');
}
```

#### on(event, handler)

订阅事件。

```typescript
on(event: string, handler: (data: any, eventName: string) => void): this
```

**参数：**
- `event` (string): 事件名称，支持通配符
- `handler` ((data: any, eventName: string) => void): 事件处理函数，接收事件数据和事件名称

**返回值：** 事件总线实例，用于链式调用

**示例：**

```typescript
// 订阅简单事件
eventBus.on('click', () => {
  console.log('点击事件触发');
});

// 订阅带数据的事件
eventBus.on('userLoggedIn', (data) => {
  console.log(`用户 ${data.username} 登录，时间: ${new Date(data.timestamp)}`);
});

// 使用通配符订阅多个事件
eventBus.on('user.*', (data, eventName) => {
  console.log(`用户事件: ${eventName}`, data);
});

// 订阅所有事件
eventBus.on('*', (data, eventName) => {
  console.log(`事件: ${eventName}`, data);
});

// 链式调用
eventBus
  .on('event1', handler1)
  .on('event2', handler2)
  .on('event3', handler3);
```

#### once(event, handler)

订阅事件一次，触发后自动取消订阅。

```typescript
once(event: string, handler: (data: any, eventName: string) => void): this
```

**参数：**
- `event` (string): 事件名称，支持通配符
- `handler` ((data: any, eventName: string) => void): 事件处理函数，接收事件数据和事件名称

**返回值：** 事件总线实例，用于链式调用

**示例：**

```typescript
// 订阅一次事件
eventBus.once('initialization', (data) => {
  console.log('初始化数据:', data);
  // 初始化应用
  initApp(data);
});

// 使用通配符订阅一次事件
eventBus.once('user.*', (data, eventName) => {
  console.log(`首次用户事件: ${eventName}`, data);
});
```

#### off(event, handler)

取消订阅事件。

```typescript
off(event: string, handler?: (data: any, eventName: string) => void): this
```

**参数：**
- `event` (string): 事件名称
- `handler` ((data: any, eventName: string) => void): 可选，事件处理函数，如果不提供则移除所有该事件的监听器

**返回值：** 事件总线实例，用于链式调用

**示例：**

```typescript
// 定义事件处理函数
function handleUserLogin(data) {
  console.log(`用户 ${data.username} 登录`);
}

// 订阅事件
eventBus.on('userLoggedIn', handleUserLogin);

// 取消特定处理函数的订阅
eventBus.off('userLoggedIn', handleUserLogin);

// 取消所有 'userLoggedIn' 事件的订阅
eventBus.off('userLoggedIn');

// 取消所有事件的订阅
eventBus.off('*');

// 链式调用
eventBus
  .off('event1', handler1)
  .off('event2')
  .off('*');
```

#### listeners(event)

获取指定事件的所有监听器。

```typescript
listeners(event: string): Array<(data: any, eventName: string) => void>
```

**参数：**
- `event` (string): 事件名称

**返回值：** 事件监听器函数数组

**示例：**

```typescript
// 获取事件监听器
const loginListeners = eventBus.listeners('userLoggedIn');
console.log('登录事件监听器数量:', loginListeners.length);

// 检查是否有监听器
function hasListeners(event) {
  return eventBus.listeners(event).length > 0;
}

if (hasListeners('userLoggedIn')) {
  console.log('有监听器监听登录事件');
}
```

#### listenerCount(event)

获取指定事件的监听器数量。

```typescript
listenerCount(event: string): number
```

**参数：**
- `event` (string): 事件名称

**返回值：** 监听器数量

**示例：**

```typescript
// 获取监听器数量
const count = eventBus.listenerCount('userLoggedIn');
console.log('登录事件监听器数量:', count);

// 检查是否有监听器
if (eventBus.listenerCount('userLoggedIn') > 0) {
  console.log('有监听器监听登录事件');
}
```

#### eventNames()

获取所有已注册事件的名称。

```typescript
eventNames(): Array<string>
```

**返回值：** 事件名称数组

**示例：**

```typescript
// 获取所有事件名称
const events = eventBus.eventNames();
console.log('已注册的事件:', events);

// 遍历所有事件
events.forEach(event => {
  const count = eventBus.listenerCount(event);
  console.log(`事件 ${event} 有 ${count} 个监听器`);
});
```

#### removeAllListeners(event?)

移除所有监听器，或指定事件的所有监听器。

```typescript
removeAllListeners(event?: string): this
```

**参数：**
- `event` (string): 可选，事件名称，如果不提供则移除所有事件的所有监听器

**返回值：** 事件总线实例，用于链式调用

**示例：**

```typescript
// 移除特定事件的所有监听器
eventBus.removeAllListeners('userLoggedIn');

// 移除所有事件的所有监听器
eventBus.removeAllListeners();

// 在组件卸载时清理
function unmountComponent() {
  // 移除组件相关的所有事件监听器
  eventBus.removeAllListeners('component.*');
}
```

#### setMaxListeners(n)

设置每个事件类型的最大监听器数量。

```typescript
setMaxListeners(n: number): this
```

**参数：**
- `n` (number): 最大监听器数量，设置为 0 表示无限制

**返回值：** 事件总线实例，用于链式调用

**示例：**

```typescript
// 设置最大监听器数量
eventBus.setMaxListeners(20);

// 设置为无限制
eventBus.setMaxListeners(0);
```

#### getMaxListeners()

获取每个事件类型的最大监听器数量。

```typescript
getMaxListeners(): number
```

**返回值：** 最大监听器数量

**示例：**

```typescript
// 获取最大监听器数量
const maxListeners = eventBus.getMaxListeners();
console.log('每个事件的最大监听器数量:', maxListeners);
```

## GlobalState

`GlobalState` 是 Micro-Core 提供的全局状态管理器，用于在应用之间共享状态。

### 基本用法

```typescript
import { GlobalState } from '@micro-core/core';

// 创建全局状态
const state = new GlobalState({
  user: {
    id: null,
    name: '',
    isLoggedIn: false
  },
  theme: 'light',
  notifications: []
});

// 获取状态
const user = state.get('user');
console.log('当前用户:', user);

// 设置状态
state.set('theme', 'dark');

// 监听状态变化
state.subscribe('user', (newValue, oldValue) => {
  console.log('用户状态变化:', oldValue, '->', newValue);
});

// 批量更新状态
state.batch(() => {
  state.set('user.id', 1);
  state.set('user.name', 'Admin');
  state.set('user.isLoggedIn', true);
});
```

### API 参考

#### 构造函数

创建全局状态实例。

```typescript
constructor(initialState?: object)
```

**参数：**
- `initialState` (object): 可选，初始状态对象

**示例：**

```typescript
// 创建空状态
const state = new GlobalState();

// 创建带初始值的状态
const state = new GlobalState({
  user: {
    id: null,
    name: '',
    isLoggedIn: false
  },
  theme: 'light',
  notifications: [],
  settings: {
    language: 'zh-CN',
    notifications: {
      email: true,
      push: false
    }
  }
});
```

#### get(path)

获取状态值。

```typescript
get(path?: string): any
```

**参数：**
- `path` (string): 可选，状态路径，使用点号分隔，如果不提供则返回整个状态对象

**返回值：** 状态值

**示例：**

```typescript
// 获取整个状态
const fullState = state.get();
console.log('完整状态:', fullState);

// 获取顶级状态
const user = state.get('user');
console.log('用户:', user);

// 获取嵌套状态
const userName = state.get('user.name');
console.log('用户名:', userName);

const pushNotifications = state.get('settings.notifications.push');
console.log('推送通知设置:', pushNotifications);

// 获取数组元素
const firstNotification = state.get('notifications.0');
console.log('第一条通知:', firstNotification);
```

#### set(path, value)

设置状态值。

```typescript
set(path: string, value: any): void
```

**参数：**
- `path` (string): 状态路径，使用点号分隔
- `value` (any): 新的状态值

**示例：**

```typescript
// 设置顶级状态
state.set('theme', 'dark');

// 设置嵌套状态
state.set('user.name', 'Admin');
state.set('settings.notifications.push', true);

// 设置数组
state.set('notifications', [
  { id: 1, text: '新消息', read: false },
  { id: 2, text: '系统更新', read: false }
]);

// 更新数组元素
state.set('notifications.0.rea