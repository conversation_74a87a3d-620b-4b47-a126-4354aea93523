# 路由 API (第三部分)

## 常见问题与解决方案

### 路由冲突

**问题**：主应用和微应用的路由发生冲突。

**解决方案**：

1. 使用不同的路由模式：主应用使用 history 模式，微应用使用 hash 模式
2. 为每个微应用设置唯一的路由前缀
3. 使用路由同步机制，确保主应用和微应用的路由状态一致

```typescript
// 主应用使用 history 模式
const mainRouter = new Router({
  mode: 'history',
  routes: [...]
});

// 微应用使用 hash 模式
const microRouter = new Router({
  mode: 'hash',
  routes: [...]
});

// 或者为微应用设置唯一前缀
const microRouter = new Router({
  mode: 'history',
  base: '/app1', // 唯一前缀
  routes: [...]
});
```

### 路由导航失败

**问题**：路由导航失败，无法跳转到目标路由。

**解决方案**：

1. 检查路由配置是否正确
2. 检查导航守卫是否阻止了导航
3. 使用路由错误处理

```typescript
// 注册错误处理
router.onError((error) => {
  console.error('路由错误:', error);
  
  // 尝试恢复导航
  if (error.type === 'NavigationDuplicated') {
    // 忽略重复导航错误
    return;
  }
  
  // 导航到错误页面
  router.push('/error');
});

// 添加超时处理
function navigateWithTimeout(location, timeout = 5000) {
  return Promise.race([
    router.push(location),
    new Promise((_, reject) => {
      setTimeout(() => reject(new Error('导航超时')), timeout);
    })
  ]).catch(error => {
    console.error('导航失败:', error);
    // 回退到首页
    router.push('/');
  });
}
```

### 路由参数丢失

**问题**：在路由跳转后，参数丢失。

**解决方案**：

1. 使用命名路由和 params
2. 使用 query 参数
3. 使用路由元信息

```typescript
// 使用命名路由和 params
router.push({
  name: 'user-detail',
  params: { userId: '123' }
});

// 使用 query 参数
router.push({
  path: '/users',
  query: { userId: '123' }
});

// 使用路由元信息
router.beforeEach((to, from, next) => {
  // 保存参数到元信息
  if (from.params.userId && !to.params.userId) {
    to.params.userId = from.params.userId;
  }
  next();
});
```

### 刷新页面后路由状态丢失

**问题**：刷新页面后，路由状态丢失。

**解决方案**：

1. 使用 localStorage 或 sessionStorage 保存路由状态
2. 使用 URL 参数保存状态
3. 实现路由持久化

```typescript
// 使用 localStorage 保存路由状态
router.beforeEach((to, from, next) => {
  // 保存路由状态
  localStorage.setItem('lastRoute', JSON.stringify({
    path: to.path,
    query: to.query,
    params: to.params
  }));
  next();
});

// 在应用启动时恢复路由状态
function restoreRouteState() {
  const lastRoute = localStorage.getItem('lastRoute');
  if (lastRoute) {
    const { path, query, params } = JSON.parse(lastRoute);
    router.push({ path, query, params });
  }
}

// 路由持久化
const persistedState = {
  route: {
    path: window.location.pathname,
    query: parseQuery(window.location.search),
    hash: window.location.hash
  }
};

// 创建路由时使用持久化状态
const router = new Router({
  mode: 'history',
  routes: [...],
  // 初始化时使用持久化状态
  initialState: persistedState.route
});
```

### 微应用间路由跳转

**问题**：在不同微应用之间进行路由跳转。

**解决方案**：

1. 通过主应用路由进行跳转
2. 使用事件通信
3. 使用共享的路由服务

```typescript
// 通过主应用路由进行跳转
function navigateToAnotherApp(appName, path) {
  // 获取主应用路由
  const mainRouter = window.mainRouter;
  
  // 构建完整路径
  const fullPath = `/${appName}${path}`;
  
  // 使用主应用路由进行跳转
  mainRouter.push(fullPath);
}

// 使用事件通信
function navigateToAnotherApp(appName, path) {
  // 发布导航事件
  window.dispatchEvent(new CustomEvent('navigate', {
    detail: { appName, path }
  }));
}

// 在主应用中监听导航事件
window.addEventListener('navigate', (event) => {
  const { appName, path } = event.detail;
  const fullPath = `/${appName}${path}`;
  router.push(fullPath);
});

// 使用共享的路由服务
const routerService = {
  routers: new Map(),
  
  // 注册路由
  register(appName, router) {
    this.routers.set(appName, router);
  },
  
  // 导航到指定应用的路由
  navigateTo(appName, path) {
    const targetRouter = this.routers.get(appName);
    if (targetRouter) {
      targetRouter.push(path);
    } else {
      // 回退到主应用路由
      const mainRouter = this.routers.get('main');
      mainRouter.push(`/${appName}${path}`);
    }
  }
};

// 注册主应用路由
routerService.register('main', mainRouter);

// 在微应用中注册路由
routerService.register('user-app', microRouter);

// 导航到另一个微应用
routerService.navigateTo('product-app', '/detail/123');
```

### 路由权限控制

**问题**：如何实现复杂的路由权限控制。

**解决方案**：

1. 使用路由元信息和导航守卫
2. 实现动态路由生成
3. 使用路由权限中间件

```typescript
// 使用路由元信息和导航守卫
const router = new Router({
  routes: [
    {
      path: '/admin',
      component: 'admin-app',
      meta: {
        requiresAuth: true,
        roles: ['admin']
      }
    },
    {
      path: '/users',
      component: 'users-app',
      meta: {
        requiresAuth: true,
        roles: ['admin', 'manager']
      }
    }
  ]
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const { requiresAuth, roles } = to.meta;
  
  // 检查是否需要认证
  if (requiresAuth) {
    if (!isAuthenticated()) {
      // 保存目标路由
      saveTargetRoute(to.fullPath);
      // 重定向到登录页
      return next('/login');
    }
    
    // 检查角色权限
    if (roles && !hasRole(roles)) {
      return next('/403');
    }
  }
  
  next();
});

// 动态路由生成
const asyncRoutes = [
  {
    path: '/admin',
    component: 'admin-app',
    meta: { roles: ['admin'] },
    children: [
      {
        path: 'users',
        component: 'admin-users-app',
        meta: { roles: ['admin'] }
      },
      {
        path: 'settings',
        component: 'admin-settings-app',
        meta: { roles: ['admin'] }
      }
    ]
  },
  {
    path: '/users',
    component: 'users-app',
    meta: { roles: ['admin', 'manager'] }
  }
];

// 根据用户角色过滤路由
function filterAsyncRoutes(routes, roles) {
  const res = [];
  
  routes.forEach(route => {
    const tmp = { ...route };
    
    // 检查是否有权限访问
    if (hasPermission(roles, tmp)) {
      // 处理子路由
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles);
      }
      
      res.push(tmp);
    }
  });
  
  return res;
}

// 检查权限
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role));
  }
  
  return true; // 没有设置权限要求，默认允许访问
}

// 生成可访问路由
function generateAccessibleRoutes(roles) {
  const accessibleRoutes = filterAsyncRoutes(asyncRoutes, roles);
  return accessibleRoutes;
}

// 在用户登录后动态添加路由
function addRoutesAfterLogin(user) {
  const accessibleRoutes = generateAccessibleRoutes(user.roles);
  router.addRoutes(accessibleRoutes);
}

// 路由权限中间件
const authMiddleware = {
  // 检查认证
  checkAuth(to, from, next) {
    if (to.meta.requiresAuth && !isAuthenticated()) {
      saveTargetRoute(to.fullPath);
      return next('/login');
    }
    
    next();
  },
  
  // 检查角色
  checkRole(to, from, next) {
    if (to.meta.roles && !hasRole(to.meta.roles)) {
      return next('/403');
    }
    
    next();
  },
  
  // 检查权限
  checkPermission(to, from, next) {
    if (to.meta.permissions && !hasPermission(to.meta.permissions)) {
      return next('/403');
    }
    
    next();
  }
};

// 应用中间件
router.beforeEach(authMiddleware.checkAuth);
router.beforeEach(authMiddleware.checkRole);
router.beforeEach(authMiddleware.checkPermission);
```

### 路由性能优化

**问题**：路由导航性能不佳，影响用户体验。

**解决方案**：

1. 使用路由懒加载
2. 实现路由预加载
3. 优化路由配置

```typescript
// 使用路由懒加载
const router = new Router({
  routes: [
    {
      path: '/',
      component: 'home-app' // 直接加载
    },
    {
      path: '/users',
      component: () => loadMicroApp('users-app') // 懒加载
    }
  ]
});

// 实现路由预加载
const preloadedApps = new Set();

// 预加载函数
function preloadMicroApp(name) {
  if (preloadedApps.has(name)) {
    return Promise.resolve();
  }
  
  return loadMicroApp(name, { mount: false })
    .then(() => {
      preloadedApps.add(name);
    });
}

// 在空闲时预加载
if ('requestIdleCallback' in window) {
  requestIdleCallback(() => {
    preloadMicroApp('users-app');
    preloadMicroApp('settings-app');
  });
} else {
  setTimeout(() => {
    preloadMicroApp('users-app');
    preloadMicroApp('settings-app');
  }, 1000);
}

// 优化路由配置
const router = new Router({
  routes: [
    // 使用更精确的路由匹配
    { path: '/users/:userId(\\d+)', component: 'user-app' },
    
    // 减少嵌套层级
    { path: '/settings/profile', component: 'profile-app' },
    { path: '/settings/security', component: 'security-app' },
    
    // 使用路由元信息优化
    {
      path: '/dashboard',
      component: 'dashboard-app',
      meta: {
        keepAlive: true, // 缓存组件
        transition: 'fade' // 过渡效果
      }
    }
  ]
});
```

## 高级功能

### 路由元信息扩展

扩展路由元信息以支持更多功能：

```typescript
const router = new Router({
  routes: [
    {
      path: '/users',
      component: 'users-app',
      meta: {
        // 基本信息
        title: '用户管理',
        icon: 'user',
        
        // 权限控制
        requiresAuth: true,
        roles: ['admin', 'manager'],
        permissions: ['user:view', 'user:edit'],
        
        // 缓存控制
        keepAlive: true,
        cacheTimeout: 300000, // 5分钟
        
        // 过渡效果
        transition: 'fade',
        transitionDuration: 300,
        
        // 布局控制
        layout: 'admin',
        sidebar: true,
        
        // 数据预加载
        fetchData: true,
        fetchFunction: 'fetchUserList',
        
        // 面包屑
        breadcrumb: [
          { name: '首页', path: '/' },
          { name: '用户管理' }
        ],
        
        // 分析跟踪
        track: true,
        trackEvent: 'page_view',
        trackCategory: 'user_management'
      }
    }
  ]
});

// 使用元信息
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }
  
  // 权限检查
  if (to.meta.requiresAuth && !isAuthenticated()) {
    return next('/login');
  }
  
  // 角色检查
  if (to.meta.roles && !hasRole(to.meta.roles)) {
    return next('/403');
  }
  
  // 权限检查
  if (to.meta.permissions && !hasPermission(to.meta.permissions)) {
    return next('/403');
  }
  
  next();
});

router.afterEach((to, from) => {
  // 设置布局
  if (to.meta.layout) {
    setLayout(to.meta.layout);
  }
  
  // 设置侧边栏
  if (to.meta.sidebar !== undefined) {
    setSidebar(to.meta.sidebar);
  }
  
  // 设置面包屑
  if (to.meta.breadcrumb) {
    setBreadcrumb(to.meta.breadcrumb);
  }
  
  // 分析跟踪
  if (to.meta.track) {
    trackPageView({
      event: to.meta.trackEvent || 'page_view',
      category: to.meta.trackCategory,
      path: to.path,
      title: to.meta.title
    });
  }
});
```

### 路由中间件

实现路由中间件系统：

```typescript
// 中间件管理器
class RouterMiddlewareManager {
  constructor(router) {
    this.router = router;
    this.middlewares = [];
    
    // 注册全局前置守卫
    this.router.beforeEach(this.handle.bind(this));
  }
  
  // 添加中间件
  use(middleware) {
    this.middlewares.push(middleware);
    return this;
  }
  
  // 处理中间件
  handle(to, from, next) {
    const middlewares = [...this.middlewares];
    
    // 执行中间件链
    const runMiddleware = (index) => {
      // 所有中间件已执行完毕
      if (index >= middlewares.length) {
        return next();
      }
      
      // 执行当前中间件
      const middleware = middlewares[index];
      middleware(to, from, () => runMiddleware(index + 1), this.router);
    };
    
    runMiddleware(0);
  }
}

// 创建路由实例
const router = new Router({
  mode: 'history',
  routes: [...]
});

// 创建中间件管理器
const middlewareManager = new RouterMiddlewareManager(router);

// 认证中间件
const authMiddleware = (to, from, next, router) => {
  if (to.meta.requiresAuth && !isAuthenticated()) {
    return next('/login');
  }
  
  next();
};

// 角色中间件
const roleMiddleware = (to, from, next, router) => {
  if (to.meta.roles && !hasRole(to.meta.roles)) {
    return next('/403');
  }
  
  next();
};

// 日志中间件
const logMiddleware = (to, from, next, router) => {
  console.log(`路由导航: ${from.path} -> ${to.path}`);
  next();
};

// 性能监控中间件
const performanceMiddleware = (to, from, next, router) => {
  const startTime = performance.now();
  
  // 在导航完成后记录性能
  const unregister = router.afterEach(() => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`路由导航耗时: ${duration.toFixed(2)}ms`);
    
    // 移除后置钩子
    unregister();
  });
  
  next();
};

// 注册中间件
middlewareManager
  .use(logMiddleware)
  .use(performanceMiddleware)
  .use(authMiddleware)
  .use(roleMiddleware);
```

### 路由插件系统

实现路由插件系统：

```typescript
// 路由插件管理器
class RouterPluginManager {
  constructor(router) {
    this.router = router;
    this.plugins = new Map();
  }
  
  // 注册插件
  register(name, plugin) {
    if (this.plugins.has(name)) {
      console.warn(`插件 "${name}" 已经注册，将被覆盖`);
    }
    
    this.plugins.set(name, plugin);
    
    // 初始化插件
    if (typeof plugin.install === 'function') {
      plugin.install(this.router);
    }
    
    return this;
  }
  
  // 获取插件
  getPlugin(name) {
    return this.plugins.get(name);
  }
  
  // 卸载插件
  unregister(name) {
    const plugin = this.plugins.get(name);
    
    if (plugin && typeof plugin.uninstall === 'function') {
      plugin.uninstall(this.router);
    }
    
    this.plugins.delete(name);
    return this;
  }
}

// 创建路由实例
const router = new Router({
  mode: 'history',
  routes: [...]
});

// 创建插件管理器
const pluginManager = new RouterPluginManager(router);

// 面包屑插件
const breadcrumbPlugin = {
  install(router) {
    // 添加全局后置钩子
    this.unregisterHook = router.afterEach((to) => {
      // 生成面包屑
      const breadcrumbs = [];
      
      // 添加首页
      breadcrumbs.push({
        name: '首页',
        path: '/'
      });
      
      // 根据路由匹配生成面包屑
      to.matched.forEach(route => {
        if (route.meta && route.meta.title) {
          breadcrumbs.push({
            name: route.meta.title,
            path: route.path
          });
        }
      });
      
      // 更新面包屑
      updateBreadcrumbs(breadcrumbs);
    });
  },
  
  uninstall(router) {
    // 移除钩子
    if (this.unregisterHook) {
      this.unregisterHook();
    }
  }
};

// 进度条插件
const progressPlugin = {
  install(router) {
    // 创建进度条
    const progressBar = createProgressBar();
    
    // 添加全局前置守卫
    this.beforeHook = router.beforeEach((to, from, next) => {
      // 启动进度条
      progressBar.start();
      next();
    });
    
    // 添加全局后置钩子
    this.afterHook = router.afterEach(() => {
      // 完成进度条
      progressBar.finish();
    });
    
    // 添加错误处理
    this.errorHook = router.onError(() => {
      // 错误时停止进度条
      progressBar.fail();
    });
  },
  
  uninstall(router) {
    // 移除钩子
    if (this.beforeHook) this.beforeHook();
    if (this.afterHook) this.afterHook();
    if (this.errorHook) this.errorHook();
  }
};

// 分析插件
const analyticsPlugin = {
  install(router) {
    // 添加全局后置钩子
    this.unregisterHook = router.afterEach((to, from) => {
      // 发送页面浏览事件
      trackPageView({
        path: to.path,
        title: to.meta.title || document.title,
        referrer: from.path
      });
    });
  },
  
  uninstall(router) {
    // 移除钩子
    if (this.unregisterHook) {
      this.unregisterHook();
    }
  }
};

// 注册插件
pluginManager
  .register('breadcrumb', breadcrumbPlugin)
  .register('progress', progressPlugin)
  .register('analytics', analyticsPlugin);
```

### 路由历史记录管理

实现路由历史记录管理：

```typescript
// 路由历史记录管理器
class RouterHistoryManager {
  constructor(router, options = {}) {
    this.router = router;
    this.options = {
      maxSize: options.maxSize || 50,
      storageKey: options.storageKey || 'router_history',
      persist: options.persist !== false
    };
    
    // 历史记录
    this.history = [];
    
    // 从存储中恢复历史记录
    if (this.options.persist) {
      this.restoreFromStorage();
    }
    
    // 监听路由变化
    this.unregisterHook = router.afterEach((to) => {
      this.addToHistory(to);
    });
  }
  
  // 添加到历史记录
  addToHistory(route) {
    // 创建历史记录项
    const historyItem = {
      path: route.path,
      fullPath: route.fullPath,
      name: route.name,
      params: { ...route.params },
      query: { ...route.query },
      meta: route.meta ? { ...route.meta } : {},
      timestamp: Date.now()
    };
    
    // 检查是否已存在相同路由
    const existingIndex = this.history.findIndex(item => item.fullPath === historyItem.fullPath);
    if (existingIndex !== -1) {
      // 移除已存在的路由
      this.history.splice(existingIndex, 1);
    }
    
    // 添加到历史记录
    this.history.unshift(historyItem);
    
    // 限制历史记录大小
    if (this.history.length > this.options.maxSize) {
      this.history = this.history.slice(0, this.options.maxSize);
    }
    
    // 保存到存储
    if (this.options.persist) {
      this.saveToStorage();
    }
  }
  
  // 获取历史记录
  getHistory() {
    return [...this.history];
  }
  
  // 清除历史记录
  clearHistory() {
    this.history = [];
    
    // 清除存储
    if (this.options.persist) {
      localStorage.removeItem(this.options.storageKey);
    }
  }
  
  // 导航到历史记录项
  navigateTo(index) {
    if (index >= 0 && index < this.history.length) {
      const item = this.history[index];
      this.router.push({
        path: item.path,
        params: item.params,
        query: item.query
      });
      return true;
    }
    return false;
  }
  
  // 从存储中恢复历史记录
  restoreFromStorage() {
    try {
      const stored = localStorage.getItem(this.options.storageKey);
      if (stored) {
        this.history = JSON.parse(stored);
      }
    } catch (error) {
      console.error('恢复路由历史记录失败:', error);
      this.history = [];
    }
  }
  
  // 保存到存储
  saveToStorage() {
    try {
      localStorage.setItem(this.options.storageKey, JSON.stringify(this.history));
    } catch (error) {
      console.error('保存路由历史记录失败:', error);
    }
  }
  
  // 销毁
  destroy() {
    if (this.unregisterHook) {
      this.unregisterHook();
    }
  }
}

// 创建路由实例
const router = new Router({
  mode: 'history',
  routes: [...]
});

// 创建历史记录管理器
const historyManager = new RouterHistoryManager(router, {
  maxSize: 100,
  storageKey: 'app_router_history',
  persist: true
});

// 使用历史记录
function showHistory() {
  const history = historyManager.getHistory();
  
  // 显示历史记录
  const historyList = history.map((item, index) => {
    return {
      index,
      path: item.path,
      title: item.meta.title || item.path,
      timestamp: new Date(item.timestamp).toLocaleString()
    };
  });
  
  return historyList;
}

// 导航到历史记录项
function navigateToHistoryItem(index) {
  historyManager.navigateTo(index);
}

// 清除历史记录
function clearHistory() {
  historyManager.clearHistory();
}
```

## 总结

Micro-Core 路由 API 提供了强大而灵活的路由管理功能，适用于微前端架构中的各种场景。通过本文档，你可以了解路由 API 的基本用法、高级特性以及最佳实践，帮助你构建高效、可靠的微前端应用。

主要特性包括：

1. **灵活的路由模式**：支持 history 和 hash 模式
2. **强大的路由匹配**：支持静态路径、动态参数、嵌套路由和通配符
3. **完善的导航守卫**：提供全局守卫、路由独享守卫和组件内守卫
4. **路由元信息**：支持附加任意信息到路由上
5. **路由懒加载**：提高应用性能
6. **滚动行为控制**：自定义页面滚动行为
7. **路由过渡效果**：为路由切换添加动画效果
8. **微前端路由集成**：与 qiankun 和 wujie 等微前端框架集成

通过合理使用这些功能，你可以构建出用户体验良好、性能优异的微前端应用。