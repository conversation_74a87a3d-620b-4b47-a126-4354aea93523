# 沙箱 API

Micro-Core 提供了强大的沙箱系统，用于隔离微应用的运行环境，防止微应用之间的相互干扰。本文档详细介绍了沙箱 API 的使用方法和最佳实践。

## 基本概念

在微前端架构中，沙箱负责以下任务：

1. **全局变量隔离**：防止微应用污染全局环境
2. **DOM 隔离**：限制微应用对 DOM 的访问范围
3. **事件隔离**：防止事件冲突和泄漏
4. **样式隔离**：防止 CSS 样式冲突
5. **存储隔离**：隔离 localStorage、sessionStorage 等存储

## Sandbox

`Sandbox` 是 Micro-Core 提供的核心沙箱类，用于创建和管理微应用的沙箱环境。

### 基本用法

```typescript
import { Sandbox } from '@micro-core/core';

// 创建沙箱实例
const sandbox = new Sandbox({
  name: 'app1',
  el: '#app1-container',
  scopeStyle: true,
  isolateGlobalVariables: true
});

// 激活沙箱
sandbox.activate();

// 在沙箱中执行代码
sandbox.execScript(`
  window.appName = 'App1';
  console.log('Hello from sandbox!');
`);

// 获取沙箱中的变量
const appName = sandbox.getGlobalValue('appName');
console.log('应用名称:', appName); // 输出: 应用名称: App1

// 设置沙箱中的变量
sandbox.setGlobalValue('theme', 'dark');

// 清理沙箱
sandbox.deactivate();
```

### API 参考

#### 构造函数

创建沙箱实例。

```typescript
constructor(options?: SandboxOptions)
```

**参数：**
- `options` (SandboxOptions): 可选，沙箱配置选项
  - `name` (string): 沙箱名称，用于标识沙箱实例
  - `el` (string | HTMLElement): 沙箱容器元素或选择器
  - `scopeStyle` (boolean): 是否启用样式隔离，默认为 true
  - `isolateGlobalVariables` (boolean): 是否隔离全局变量，默认为 true
  - `isolateWindowEvents` (boolean): 是否隔离窗口事件，默认为 true
  - `isolateStorage` (boolean): 是否隔离存储，默认为 true
  - `disableWith` (boolean): 是否禁用 with 语句，默认为 false
  - `useProxy` (boolean): 是否使用 Proxy 实现沙箱，默认为 true
  - `strictIsolation` (boolean): 是否启用严格隔离模式，默认为 false
  - `allowList` (string[]): 允许访问的全局变量列表
  - `denyList` (string[]): 禁止访问的全局变量列表
  - `customGlobals` (Record<string, any>): 自定义全局变量
  - `beforeExecute` (Function): 代码执行前的钩子函数
  - `afterExecute` (Function): 代码执行后的钩子函数
  - `errorHandler` (Function): 错误处理函数

**示例：**

```typescript
// 基本用法
const sandbox = new Sandbox();

// 完整配置
const sandbox = new Sandbox({
  // 基本配置
  name: 'app1',
  el: document.getElementById('app1-container'),
  
  // 隔离配置
  scopeStyle: true,
  isolateGlobalVariables: true,
  isolateWindowEvents: true,
  isolateStorage: true,
  
  // 高级配置
  disableWith: false,
  useProxy: true,
  strictIsolation: false,
  
  // 访问控制
  allowList: ['console', 'fetch', 'localStorage'],
  denyList: ['parent', 'top', 'eval'],
  
  // 自定义全局变量
  customGlobals: {
    appName: 'App1',
    appVersion: '1.0.0',
    appConfig: {
      theme: 'light',
      language: 'zh-CN'
    }
  },
  
  // 钩子函数
  beforeExecute: (code, context) => {
    console.log(`即将执行代码，长度: ${code.length}`);
    return code; // 可以修改代码后返回
  },
  
  // 执行后钩子
  afterExecute: (result, context) => {
    console.log('代码执行完成');
    return result; // 可以修改结果后返回
  },
  
  // 错误处理
  errorHandler: (error, code) => {
    console.error('沙箱执行错误:', error);
    console.error('错误代码:', code);
  }
});
```

#### activate()

激活沙箱。

```typescript
activate(): void
```

**示例：**

```typescript
// 激活沙箱
sandbox.activate();

// 在组件挂载时激活沙箱
function mountComponent() {
  // 激活沙箱
  sandbox.activate();
  
  // 渲染组件
  renderComponent();
}
```

#### deactivate()

停用沙箱。

```typescript
deactivate(): void
```

**示例：**

```typescript
// 停用沙箱
sandbox.deactivate();

// 在组件卸载时停用沙箱
function unmountComponent() {
  // 停用沙箱
  sandbox.deactivate();
  
  // 清理资源
  cleanupResources();
}
```

#### execScript(code, options)

在沙箱中执行 JavaScript 代码。

```typescript
execScript(code: string, options?: ExecScriptOptions): any
```

**参数：**
- `code` (string): 要执行的 JavaScript 代码
- `options` (ExecScriptOptions): 可选，执行选项
  - `sourceUrl` (string): 源代码 URL，用于调试
  - `timeout` (number): 执行超时时间，单位为毫秒
  - `async` (boolean): 是否异步执行
  - `context` (object): 执行上下文
  - `args` (object): 传递给代码的参数

**返回值：** 代码执行的结果

**示例：**

```typescript
// 执行简单代码
const result = sandbox.execScript(`
  const a = 1;
  const b = 2;
  return a + b;
`);
console.log('执行结果:', result); // 输出: 执行结果: 3

// 带选项的执行
const asyncResult = sandbox.execScript(`
  return new Promise(resolve => {
    setTimeout(() => {
      resolve('异步结果');
    }, 1000);
  });
`, {
  async: true,
  timeout: 2000,
  sourceUrl: 'async-code.js'
});

asyncResult.then(result => {
  console.log('异步执行结果:', result); // 输出: 异步执行结果: 异步结果
});

// 传递参数
const calculateResult = sandbox.execScript(`
  return a * b + c;
`, {
  args: { a: 10, b: 20, c: 5 }
});
console.log('计算结果:', calculateResult); // 输出: 计算结果: 205

// 使用上下文
const contextResult = sandbox.execScript(`
  return this.name.toUpperCase();
`, {
  context: { name: 'app1' }
});
console.log('上下文结果:', contextResult); // 输出: 上下文结果: APP1
```

#### execFile(url, options)

加载并执行外部 JavaScript 文件。

```typescript
execFile(url: string, options?: ExecFileOptions): Promise<any>
```

**参数：**
- `url` (string): JavaScript 文件的 URL
- `options` (ExecFileOptions): 可选，执行选项
  - `timeout` (number): 加载超时时间，单位为毫秒
  - `async` (boolean): 是否异步执行
  - `context` (object): 执行上下文
  - `args` (object): 传递给代码的参数
  - `cache` (boolean): 是否缓存文件内容

**返回值：** 返回一个 Promise，解析为代码执行的结果

**示例：**

```typescript
// 执行外部文件
sandbox.execFile('https://example.com/app1/main.js')
  .then(result => {
    console.log('执行结果:', result);
  })
  .catch(error => {
    console.error('执行失败:', error);
  });

// 带选项的执行
sandbox.execFile('https://example.com/app1/utils.js', {
  timeout: 5000,
  async: true,
  cache: true,
  args: {
    config: {
      apiUrl: 'https://api.example.com'
    }
  }
})
  .then(result => {
    console.log('执行结果:', result);
  })
  .catch(error => {
    console.error('执行失败:', error);
  });
```

#### getGlobalValue(key)

获取沙箱中的全局变量值。

```typescript
getGlobalValue(key: string): any
```

**参数：**
- `key` (string): 变量名

**返回值：** 变量值

**示例：**

```typescript
// 设置全局变量
sandbox.execScript(`
  window.appName = 'App1';
  window.appVersion = '1.0.0';
  window.appConfig = {
    theme: 'light',
    language: 'zh-CN'
  };
`);

// 获取全局变量
const appName = sandbox.getGlobalValue('appName');
console.log('应用名称:', appName); // 输出: 应用名称: App1

const appVersion = sandbox.getGlobalValue('appVersion');
console.log('应用版本:', appVersion); // 输出: 应用版本: 1.0.0

const appConfig = sandbox.getGlobalValue('appConfig');
console.log('应用配置:', appConfig); // 输出: 应用配置: { theme: 'light', language: 'zh-CN' }

// 获取嵌套属性
const theme = sandbox.getGlobalValue('appConfig.theme');
console.log('主题:', theme); // 输出: 主题: light
```

#### setGlobalValue(key, value)

设置沙箱中的全局变量值。

```typescript
setGlobalValue(key: string, value: any): void
```

**参数：**
- `key` (string): 变量名
- `value` (any): 变量值

**示例：**

```typescript
// 设置全局变量
sandbox.setGlobalValue('appName', 'App1');
sandbox.setGlobalValue('appVersion', '1.0.0');
sandbox.setGlobalValue('appConfig', {
  theme: 'light',
  language: 'zh-CN'
});

// 验证设置
sandbox.execScript(`
  console.log('应用名称:', window.appName);
  console.log('应用版本:', window.appVersion);
  console.log('应用配置:', window.appConfig);
`);

// 设置嵌套属性
sandbox.setGlobalValue('appConfig.theme', 'dark');

// 验证嵌套属性
const theme = sandbox.getGlobalValue('appConfig.theme');
console.log('主题:', theme); // 输出: 主题: dark
```

#### hasGlobalValue(key)

检查沙箱中是否存在指定的全局变量。

```typescript
hasGlobalValue(key: string): boolean
```

**参数：**
- `key` (string): 变量名

**返回值：** 变量是否存在

**示例：**

```typescript
// 设置全局变量
sandbox.setGlobalValue('appName', 'App1');

// 检查变量是否存在
const hasAppName = sandbox.hasGlobalValue('appName');
console.log('是否存在 appName:', hasAppName); // 输出: 是否存在 appName: true

const hasAppVersion = sandbox.hasGlobalValue('appVersion');
console.log('是否存在 appVersion:', hasAppVersion); // 输出: 是否存在 appVersion: false

// 条件操作
if (sandbox.hasGlobalValue('appConfig')) {
  const appConfig = sandbox.getGlobalValue('appConfig');
  console.log('应用配置:', appConfig);
} else {
  console.log('应用配置不存在');
}
```

#### deleteGlobalValue(key)

删除沙箱中的全局变量。

```typescript
deleteGlobalValue(key: string): boolean
```

**参数：**
- `key` (string): 变量名

**返回值：** 是否成功删除

**示例：**

```typescript
// 设置全局变量
sandbox.setGlobalValue('appName', 'App1');
sandbox.setGlobalValue('appVersion', '1.0.0');

// 删除变量
const deleted = sandbox.deleteGlobalValue('appName');
console.log('是否删除成功:', deleted); // 输出: 是否删除成功: true

// 验证删除
const hasAppName = sandbox.hasGlobalValue('appName');
console.log('是否存在 appName:', hasAppName); // 输出: 是否存在 appName: false

const hasAppVersion = sandbox.hasGlobalValue('appVersion');
console.log('是否存在 appVersion:', hasAppVersion); // 输出: 是否存在 appVersion: true
```

#### clear()

清除沙箱中的所有全局变量。

```typescript
clear(): void
```

**示例：**

```typescript
// 设置全局变量
sandbox.setGlobalValue('appName', 'App1');
sandbox.setGlobalValue('appVersion', '1.0.0');

// 清除所有变量
sandbox.clear();

// 验证清除
const hasAppName = sandbox.hasGlobalValue('appName');
console.log('是否存在 appName:', hasAppName); // 输出: 是否存在 appName: false

const hasAppVersion = sandbox.hasGlobalValue('appVersion');
console.log('是否存在 appVersion:', hasAppVersion); // 输出: 是否存在 appVersion: false
```

#### reset()

重置沙箱到初始状态。

```typescript
reset(): void
```

**示例：**

```typescript
// 设置全局变量
sandbox.setGlobalValue('appName', 'App1');
sandbox.setGlobalValue('appVersion', '1.0.0');

// 执行代码
sandbox.execScript(`
  window.counter = 0;
  
  window.increment = function() {
    window.counter++;
  };
  
  window.increment();
  window.increment();
`);

// 重置沙箱
sandbox.reset();

// 验证重置
const hasAppName = sandbox.hasGlobalValue('appName');
console.log('是否存在 appName:', hasAppName); // 输出: 是否存在 appName: false

const hasCounter = sandbox.hasGlobalValue('counter');
console.log('是否存在 counter:', hasCounter); // 输出: 是否存在 counter: false

const hasIncrement = sandbox.hasGlobalValue('increment');
console.log('是否存在 increment:', hasIncrement); // 输出: 是否存在 increment: false
```

#### snapshot()

创建沙箱状态的快照。

```typescript
snapshot(): SandboxSnapshot
```

**返回值：** 沙箱状态快照

**示例：**

```typescript
// 设置初始状态
sandbox.setGlobalValue('appName', 'App1');
sandbox.setGlobalValue('counter', 0);

// 创建快照
const snapshot = sandbox.snapshot();
console.log('快照:', snapshot);

// 修改状态
sandbox.setGlobalValue('appName', 'Modified App1');
sandbox.setGlobalValue('counter', 10);
sandbox.setGlobalValue('newValue', 'added after snapshot');

// 验证修改
console.log('修改后 appName:', sandbox.getGlobalValue('appName')); // 输出: 修改后 appName: Modified App1
console.log('修改后 counter:', sandbox.getGlobalValue('counter')); // 输出: 修改后 counter: 10
```

#### restore(snapshot)

从快照恢复沙箱状态。

```typescript
restore(snapshot: SandboxSnapshot): void
```

**参数：**
- `snapshot` (SandboxSnapshot): 沙箱状态快照

**示例：**

```typescript
// 设置初始状态
sandbox.setGlobalValue('appName', 'App1');
sandbox.setGlobalValue('counter', 0);

// 创建快照
const snapshot = sandbox.snapshot();

// 修改状态
sandbox.setGlobalValue('appName', 'Modified App1');
sandbox.setGlobalValue('counter', 10);
sandbox.setGlobalValue('newValue', 'added after snapshot');

// 从快照恢复
sandbox.restore(snapshot);

// 验证恢复
console.log('恢复后 appName:', sandbox.getGlobalValue('appName')); // 输出: 恢复后 appName: App1
console.log('恢复后 counter:', sandbox.getGlobalValue('counter')); // 输出: 恢复后 counter: 0

// 验证新增变量是否被删除
const hasNewValue = sandbox.hasGlobalValue('newValue');
console.log('是否存在 newValue:', hasNewValue); // 输出: 是否存在 newValue: false
```

#### getProxyWindow()

获取沙箱代理的 window 对象。

```typescript
getProxyWindow(): Window
```

**返回值：** 沙箱代理的 window 对象

**示例：**

```typescript
// 获取代理 window
const proxyWindow = sandbox.getProxyWindow();

// 使用代理 window
proxyWindow.appName = 'App1';
proxyWindow.counter = 0;

proxyWindow.increment = function() {
  proxyWindow.counter++;
};

proxyWindow.increment();
console.log('计数器:', proxyWindow.counter); // 输出: 计数器: 1

// 验证沙箱隔离
console.log('全局 window.appName:', window.appName); // 输出: 全局 window.appName: undefined
console.log('沙箱 appName:', sandbox.getGlobalValue('appName')); // 输出: 沙箱 appName: App1
```

#### getOriginalWindow()

获取原始的 window 对象。

```typescript
getOriginalWindow(): Window
```

**返回值：** 原始的 window 对象

**示例：**

```typescript
// 获取原始 window
const originalWindow = sandbox.getOriginalWindow();

// 使用原始 window
originalWindow.globalVar = 'Global Value';

// 验证全局变量
console.log('全局 window.globalVar:', window.globalVar); // 输出: 全局 window.globalVar: Global Value
console.log('沙箱 globalVar:', sandbox.getGlobalValue('globalVar')); // 输出: 沙箱 globalVar: undefined
```

#### addGlobalValueMutationObserver(callback)

添加全局变量变更观察器。

```typescript
addGlobalValueMutationObserver(callback: (key: string, newValue: any, oldValue: any) => void): () => void
```

**参数：**
- `callback` ((key: string, newValue: any, oldValue: any) => void): 变更回调函数

**返回值：** 移除观察器的函数

**示例：**

```typescript
// 添加观察器
const removeObserver = sandbox.addGlobalValueMutationObserver((key, newValue, oldValue) => {
  console.log(`变量 ${key} 变更:`, oldValue, '->', newValue);
});

// 修改变量
sandbox.setGlobalValue('appName', 'App1'); // 输出: 变量 appName 变更: undefined -> App1
sandbox.setGlobalValue('counter', 0); // 输出: 变量 counter 变更: undefined -> 0
sandbox.setGlobalValue('appName', 'Modified App1'); // 输出: 变量 appName 变更: App1 -> Modified App1

// 移除观察器
removeObserver();

// 再次修改变量，不会触发回调
sandbox.setGlobalValue('counter', 10);
```

#### addEventListener(type, listener, options)

在沙箱环境中添加事件监听器。

```typescript
addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void
```

**参数：**
- `type` (string): 事件类型
- `listener` (EventListenerOrEventListenerObject): 事件监听器
- `options` (boolean | AddEventListenerOptions): 可选，事件监听选项

**示例：**

```typescript
// 添加事件监听器
sandbox.addEventListener('click', (event) => {
  console.log('沙箱中的点击事件:', event);
});

// 添加带选项的事件监听器
sandbox.addEventListener('scroll', (event) => {
  console.log('沙箱中的滚动事件:', event);
}, { passive: true });

// 在沙箱中执行代码
sandbox.execScript(`
  // 触发事件
  const clickEvent = new Event('click');
  window.dispatchEvent(clickEvent);
  
  // 添加事件监听器
  window.addEventListener('custom-event', (event) => {
    console.log('自定义事件:', event.detail);
  });
`);

// 触发自定义事件
const customEvent = new CustomEvent('custom-event', { detail: { message: 'Hello from outside!' } });
sandbox.getProxyWindow().dispatchEvent(customEvent);
```

#### removeEventListener(type, listener, options)

在沙箱环境中移除事件监听器。

```typescript
removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void
```

**参数：**
- `type` (string): 事件类型
- `listener` (EventListenerOrEventListenerObject): 事件监听器
- `options` (boolean | EventListenerOptions): 可选，事件监听选项

**示例：**

```typescript
// 定义事件处理函数
function handleClick(event) {
  console.log('沙箱中的点击事件:', event);
}

// 添加事件监听器
sandbox.addEventListener('click', handleClick);

// 移除事件监听器
sandbox.removeEventListener('click', handleClick);

// 在沙箱中执行代码
sandbox.execScript(`
  // 定义事件处理函数
  function handleCustomEvent(event) {
    console.log('自定义事件:', event.detail);
  }
  
  // 添加事件监听器
  window.addEventListener('custom-event', handleCustomEvent);
  
  // 移除事件监听器
  window.removeEventListener('custom-event', handleCustomEvent);
`);
```

#### scopeCssText(cssText)

对 CSS 文本进行作用域隔离处理。

```typescript
scopeCssText(cssText: string): string
```

**参数：**
- `cssText` (string): CSS 文本

**返回值：** 处理后的 CSS 文本

**示例：**

```typescript
// 原始 CSS
const originalCss = `
  .title {
    color: red;
    font-size: 20px;
  }
  
  #container {
    padding: 10px;
    margin: 20px;
  }
  
  body {
    background-color: #f0f0f0;
  }
`;

// 作用域处理
const scopedCss = sandbox.scopeCssText(originalCss);
console.log('作用域处理后的 CSS:', scopedCss);

// 添加到文档
const style = document.createElement('style');
style.textContent = scopedCss;
document.head.appendChild(style);
```

#### scopeCssUrl(url)

加载并对 CSS 文件进行作用域隔离处理。

```typescript
scopeCssUrl(url: string): Promise<string>
```

**参数：**
- `url` (string): CSS 文件 URL

**返回值：** 返回一个 Promise，解析为处理后的 CSS 文本

**示例：**

```typescript
// 加载并处理 CSS 文件
sandbox.scopeCssUrl('https://example.com/app1/style.css')
  .then(scopedCss => {
    console.log('作用域处理后的 CSS:', scopedCss);
    
    // 添加到文档
    const style = document.createElement('style');
    style.textContent = scopedCss;
    document.head.appendChild(style);
  })
  .catch(error => {
    console.error('CSS 处理失败:', error);
  });
```

#### isActive()

检查沙箱是否处于激活状态。

```typescript
isActive(): boolean
```

**返回值：** 沙箱是否激活

**示例：**

```typescript
// 检查沙箱状态
const isActive = sandbox.isActive();
console.log('沙箱是否激活:', isActive);

// 条件激活
if (!sandbox.isActive()) {
  console.log('沙箱未激活，正在激活...');
  sandbox.activate();
}

// 条件执行
if (sandbox.isActive()) {
  sandbox.execScript(`
    console.log('在激活的沙箱中执行');
  `);
} else {
  console.warn('沙箱未激活，无法执行代码');
}
```

## StyleSandbox

`StyleSandbox` 是 Micro-Core 提供的样式沙箱类，用于隔离微应用的 CSS 样式。

### 基本用法

```typescript
import { StyleSandbox } from '@micro-core/core';

// 创建样式沙箱实例
const styleSandbox = new StyleSandbox({
  name: 'app1',
  el: '#app1-container',
  scopeSelector: '[data-app="app1"]',
  useStrictMode: true
});

// 激活样式沙箱
styleSandbox.activate();

// 添加样式
styleSandbox.addStyle(`
  .title {
    color: red;
    font-size: 20px;
  }
  
  #container {
    padding: 10px;
    margin: 20px;
  }
`);

// 添加外部样式表
styleSandbox.addStylesheet('https://example.com/app1/style.css');

// 停用样式沙箱
styleSandbox.deactivate();
```

### API 参考

#### 构造函数

创建样式沙箱实例。

```typescript
constructor(options?: StyleSandboxOptions)
```

**参数：**
- `options` (StyleSandboxOptions): 可选，样式沙箱配置选项
  - `name` (string): 沙箱名称
  - `el` (string | HTMLElement): 沙箱容器元素或选择器
  - `scopeSelector` (string): 作用域选择器
  - `useStrictMode` (boolean): 是否使用严格模式，默认为 false
  - `useTransform` (boolean): 是否使用 CSS 转换，默认为 true
  - `preserveRootSelector` (boolean): 是否保留根选择器，默认为 false
  - `excludeSelectors` (string[]): 排除的选择器列表
  - `includeSelectors` (string[]): 包含的选择器列表

**示例：**

```typescript
// 基本用法
const styleSandbox = new StyleSandbox();

// 完整配置
const styleSandbox = new StyleSandbox({
  name: 'app1',
  el: document.getElementById('app1-container'),
  scopeSelector: '[data-app="app1"]',
  useStrictMode: true,
  useTransform: true,
  preserveRootSelector: false,
  excludeSelectors: ['body', 'html', ':root'],
  includeSelectors: ['.app1-', '#app1-']
});
```

#### activate()

激活样式沙箱。

```typescript
activate(): void
```

**示例：**

```typescript
// 激活样式沙箱
styleSandbox.activate();

// 在组件挂载时激活样式沙箱
function mountComponent() {
  // 激活样式沙箱
  styleSandbox.activate();
  
  // 渲染组件
  renderComponent();
}
```

#### deactivate()

停用样式沙箱。

```typescript
deactivate(): void
```

**示例：**

```typescript
// 停用样式沙箱
styleSandbox.deactivate();

// 在组件卸载时停用样式沙箱
function unmountComponent() {
  // 停用样式沙箱
  styleSandbox.deactivate();
  
  // 清理资源
  cleanupResources();
}
```

#### addStyle(cssText)

添加 CSS 样式文本。

```typescript
addStyle(cssText: string): HTML