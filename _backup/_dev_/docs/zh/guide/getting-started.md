# 快速开始

本指南将帮助您在 5 分钟内快速上手 Micro-Core 微前端框架。

## 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0 或 pnpm >= 6.0.0

## 安装

### 核心包安装

::: code-group

```bash [npm]
npm install @micro-core/core
```

```bash [yarn]
yarn add @micro-core/core
```

```bash [pnpm]
pnpm add @micro-core/core
```

:::

### 适配器安装（可选）

根据您的技术栈选择相应的适配器：

::: code-group

```bash [React]
npm install @micro-core/adapter-react
```

```bash [Vue]
npm install @micro-core/adapter-vue
```

```bash [Angular]
npm install @micro-core/adapter-angular
```

:::

## 基础使用

### 1. 创建主应用

在您的主应用中初始化 Micro-Core：

```typescript
// main.ts
import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'

// 创建微前端实例
const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()]
})

// 注册子应用
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react'
})

microCore.registerApp({
  name: 'vue-app', 
  entry: 'http://localhost:3002',
  container: '#vue-container',
  activeWhen: '/vue'
})

// 启动微前端
microCore.start()
```

### 2. 配置子应用

#### React 子应用配置

```typescript
// src/index.tsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'

// 微前端生命周期函数
export async function bootstrap() {
  console.log('React app bootstrapped')
}

export async function mount(props: any) {
  const container = props.container || document.getElementById('root')
  const root = ReactDOM.createRoot(container)
  root.render(<App />)
}

export async function unmount(props: any) {
  const container = props.container || document.getElementById('root')
  const root = ReactDOM.createRoot(container)
  root.unmount()
}

// 独立运行时的逻辑
if (!window.__MICRO_CORE__) {
  mount({})
}
```

#### Vue 子应用配置

```typescript
// src/main.ts
import { createApp } from 'vue'
import App from './App.vue'

let app: any = null

// 微前端生命周期函数
export async function bootstrap() {
  console.log('Vue app bootstrapped')
}

export async function mount(props: any) {
  app = createApp(App)
  const container = props.container || '#app'
  app.mount(container)
}

export async function unmount() {
  if (app) {
    app.unmount()
    app = null
  }
}

// 独立运行时的逻辑
if (!window.__MICRO_CORE__) {
  mount({})
}
```

### 3. 配置构建工具

#### Vite 配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import { microCorePlugin } from '@micro-core/builder-vite'

export default defineConfig({
  plugins: [
    microCorePlugin({
      name: 'react-app',
      entry: './src/index.tsx',
      exposes: {
        './App': './src/App.tsx'
      }
    })
  ],
  build: {
    lib: {
      entry: './src/index.tsx',
      name: 'ReactApp',
      formats: ['umd']
    }
  }
})
```

#### Webpack 配置

```javascript
// webpack.config.js
const { MicroCorePlugin } = require('@micro-core/builder-webpack')

module.exports = {
  plugins: [
    new MicroCorePlugin({
      name: 'react-app',
      entry: './src/index.tsx',
      exposes: {
        './App': './src/App.tsx'
      }
    })
  ]
}
```

## 路由配置

### 主应用路由

```typescript
import { createBrowserRouter } from 'react-router-dom'

const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        path: '/react/*',
        element: <div id="react-container" />
      },
      {
        path: '/vue/*',
        element: <div id="vue-container" />
      }
    ]
  }
])
```

### 子应用路由

子应用需要配置基础路径：

```typescript
// React 子应用
import { createBrowserRouter } from 'react-router-dom'

const router = createBrowserRouter([
  {
    path: '/react/home',
    element: <Home />
  },
  {
    path: '/react/about',
    element: <About />
  }
], {
  basename: window.__MICRO_CORE__ ? '/react' : '/'
})
```

## 应用通信

### 事件通信

```typescript
// 主应用发送事件
microCore.eventBus.emit('user-login', { userId: 123 })

// 子应用监听事件
window.__MICRO_CORE__.eventBus.on('user-login', (data) => {
  console.log('用户登录:', data)
})
```

### 状态共享

```typescript
// 设置共享状态
microCore.setState('user', { name: 'John', role: 'admin' })

// 获取共享状态
const user = microCore.getState('user')

// 监听状态变化
microCore.onStateChange('user', (newUser, oldUser) => {
  console.log('用户状态变化:', newUser, oldUser)
})
```

## 开发调试

### 启用调试模式

```typescript
const microCore = new MicroCore({
  debug: true, // 启用调试模式
  devtools: true // 启用开发者工具
})
```

### 使用调试面板

在浏览器中打开开发者工具，切换到 "Micro-Core" 面板，您可以：

- 查看已注册的应用列表
- 监控应用状态变化
- 查看事件通信日志
- 分析性能指标

## 部署上线

### 构建生产版本

```bash
# 构建主应用
npm run build

# 构建子应用
cd sub-app-react && npm run build
cd sub-app-vue && npm run build
```

### 部署配置

```nginx
# nginx.conf
server {
    listen 80;
    server_name example.com;
    
    # 主应用
    location / {
        root /var/www/main-app;
        try_files $uri $uri/ /index.html;
    }
    
    # 子应用
    location /react-app/ {
        root /var/www/sub-apps;
        try_files $uri $uri/ /react-app/index.html;
    }
    
    location /vue-app/ {
        root /var/www/sub-apps;
        try_files $uri $uri/ /vue-app/index.html;
    }
}
```

## 下一步

恭喜！您已经成功搭建了第一个 Micro-Core 微前端应用。接下来您可以：

- [了解核心概念](./core-concepts.md) - 深入理解 Micro-Core 的设计理念
- [查看完整示例](../examples/) - 学习更多实际应用场景
- [探索高级特性](./advanced/) - 使用插件系统和中间件
- [阅读最佳实践](./best-practices/) - 了解生产环境的最佳实践

## 常见问题

### Q: 如何处理样式隔离？

A: Micro-Core 提供多种样式隔离方案：

```typescript
const microCore = new MicroCore({
  sandbox: {
    css: 'scoped' // 'scoped' | 'shadow-dom' | 'none'
  }
})
```

### Q: 如何处理应用间的依赖共享？

A: 使用外部依赖配置：

```typescript
const microCore = new MicroCore({
  externals: {
    'react': 'React',
    'react-dom': 'ReactDOM',
    'vue': 'Vue'
  }
})
```

### Q: 如何实现应用的预加载？

A: 配置预加载策略：

```typescript
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react',
  preload: true // 启用预加载
})
```

如果您遇到其他问题，请查看 [故障排除指南](./troubleshooting.md) 或在 [GitHub Issues](https://github.com/echo008/micro-core/issues) 中提问。