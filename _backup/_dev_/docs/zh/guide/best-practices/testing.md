# 测试策略最佳实践

微前端架构的测试策略需要覆盖单个微应用的测试、跨应用集成测试以及整体系统的端到端测试。本文档提供了全面的测试策略和最佳实践。

## 🎯 测试策略概述

### 测试金字塔

```
        /\
       /  \
      / E2E \     端到端测试 (10%)
     /______\
    /        \
   /Integration\ 集成测试 (20%)
  /__________\
 /            \
/  Unit Tests  \   单元测试 (70%)
/______________\
```

### 测试类型

| 测试类型 | 覆盖范围 | 执行速度 | 维护成本 | 推荐比例 |
|----------|----------|----------|----------|----------|
| 单元测试 | 函数/组件 | 快 | 低 | 70% |
| 集成测试 | 模块间交互 | 中等 | 中等 | 20% |
| 端到端测试 | 完整用户流程 | 慢 | 高 | 10% |

## 🧪 单元测试

### 1. React 组件测试

```typescript
// React 组件单元测试示例
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import UserProfile from '../UserProfile';
import { MicroCoreProvider } from '@micro-core/react';

// Mock 微前端上下文
const mockMicroCoreContext = {
  eventBus: {
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn()
  },
  globalState: {
    get: jest.fn(),
    set: jest.fn(),
    watch: jest.fn()
  },
  appName: 'user-center',
  isInMicroFrontend: true
};

describe('UserProfile Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should render user information correctly', () => {
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg'
    };
    
    render(
      <MicroCoreProvider value={mockMicroCoreContext}>
        <UserProfile user={mockUser} />
      </MicroCoreProvider>
    );
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByRole('img')).toHaveAttribute('src', mockUser.avatar);
  });
  
  it('should emit user update event when profile is saved', async () => {
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>'
    };
    
    render(
      <MicroCoreProvider value={mockMicroCoreContext}>
        <UserProfile user={mockUser} />
      </MicroCoreProvider>
    );
    
    // 模拟编辑操作
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    
    const nameInput = screen.getByLabelText(/name/i);
    fireEvent.change(nameInput, { target: { value: 'Jane Doe' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);
    
    // 验证事件发送
    await waitFor(() => {
      expect(mockMicroCoreContext.eventBus.emit).toHaveBeenCalledWith(
        'user:updated',
        expect.objectContaining({
          id: '1',
          name: 'Jane Doe'
        })
      );
    });
  });
  
  it('should handle micro-frontend communication', () => {
    const mockUser = { id: '1', name: 'John Doe', email: '<EMAIL>' };
    
    render(
      <MicroCoreProvider value={mockMicroCoreContext}>
        <UserProfile user={mockUser} />
      </MicroCoreProvider>
    );
    
    // 验证组件注册了事件监听器
    expect(mockMicroCoreContext.eventBus.on).toHaveBeenCalledWith(
      'user:refresh',
      expect.any(Function)
    );
  });
  
  it('should work in standalone mode', () => {
    const mockUser = { id: '1', name: 'John Doe', email: '<EMAIL>' };
    
    // 不提供微前端上下文，测试独立运行模式
    render(<UserProfile user={mockUser} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    // 在独立模式下，某些功能可能不可用
    expect(screen.queryByRole('button', { name: /share/i })).not.toBeInTheDocument();
  });
});
```

### 2. Vue 组件测试

```typescript
// Vue 组件单元测试示例
import { mount, VueWrapper } from '@vue/test-utils';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import ProductList from '../ProductList.vue';
import { createMicroCorePlugin } from '@micro-core/vue';

// Mock API 服务
const mockProductService = {
  getProducts: vi.fn(),
  searchProducts: vi.fn()
};

// Mock 微前端插件
const mockMicroCore = createMicroCorePlugin({
  appName: 'product-catalog',
  eventBus: {
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn()
  },
  globalState: {
    get: vi.fn(),
    set: vi.fn(),
    watch: vi.fn()
  }
});

describe('ProductList Component', () => {
  let wrapper: VueWrapper<any>;
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    wrapper = mount(ProductList, {
      global: {
        plugins: [mockMicroCore],
        provide: {
          productService: mockProductService
        }
      },
      props: {
        category: 'electronics'
      }
    });
  });
  
  afterEach(() => {
    wrapper.unmount();
  });
  
  it('should load products on mount', async () => {
    const mockProducts = [
      { id: 1, name: 'iPhone', price: 999 },
      { id: 2, name: 'iPad', price: 599 }
    ];
    
    mockProductService.getProducts.mockResolvedValue(mockProducts);
    
    await wrapper.vm.$nextTick();
    
    expect(mockProductService.getProducts).toHaveBeenCalledWith('electronics');
    expect(wrapper.findAll('[data-testid="product-item"]')).toHaveLength(2);
  });
  
  it('should emit product selection event', async () => {
    const mockProducts = [
      { id: 1, name: 'iPhone', price: 999 }
    ];
    
    mockProductService.getProducts.mockResolvedValue(mockProducts);
    await wrapper.vm.$nextTick();
    
    const productItem = wrapper.find('[data-testid="product-item"]');
    await productItem.trigger('click');
    
    // 验证微前端事件发送
    expect(mockMicroCore.eventBus.emit).toHaveBeenCalledWith(
      'product:selected',
      { id: 1, name: 'iPhone', price: 999 }
    );
  });
  
  it('should handle search functionality', async () => {
    const searchInput = wrapper.find('[data-testid="search-input"]');
    const searchButton = wrapper.find('[data-testid="search-button"]');
    
    await searchInput.setValue('iPhone');
    await searchButton.trigger('click');
    
    expect(mockProductService.searchProducts).toHaveBeenCalledWith('iPhone', 'electronics');
  });
  
  it('should display loading state', async () => {
    // 模拟加载状态
    mockProductService.getProducts.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 100))
    );
    
    expect(wrapper.find('[data-testid="loading"]').exists()).toBe(true);
    
    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 150));
    
    expect(wrapper.find('[data-testid="loading"]').exists()).toBe(false);
  });
});
```

### 3. 服务层测试

```typescript
// 服务层单元测试
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { UserService } from '../services/UserService';
import { HttpClient } from '../utils/HttpClient';
import { EventBus } from '@micro-core/core';

// Mock 依赖
vi.mock('../utils/HttpClient');
vi.mock('@micro-core/core');

describe('UserService', () => {
  let userService: UserService;
  let mockHttpClient: jest.Mocked<HttpClient>;
  let mockEventBus: jest.Mocked<EventBus>;
  
  beforeEach(() => {
    mockHttpClient = new HttpClient() as jest.Mocked<HttpClient>;
    mockEventBus = new EventBus() as jest.Mocked<EventBus>;
    
    userService = new UserService(mockHttpClient, mockEventBus);
  });
  
  describe('getUserById', () => {
    it('should fetch user data successfully', async () => {
      const mockUser = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>'
      };
      
      mockHttpClient.get.mockResolvedValue({ data: mockUser });
      
      const result = await userService.getUserById('1');
      
      expect(mockHttpClient.get).toHaveBeenCalledWith('/api/users/1');
      expect(result).toEqual(mockUser);
    });
    
    it('should handle API errors gracefully', async () => {
      const mockError = new Error('User not found');
      mockHttpClient.get.mockRejectedValue(mockError);
      
      await expect(userService.getUserById('999')).rejects.toThrow('User not found');
    });
    
    it('should cache user data', async () => {
      const mockUser = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>'
      };
      
      mockHttpClient.get.mockResolvedValue({ data: mockUser });
      
      // 第一次调用
      await userService.getUserById('1');
      // 第二次调用
      await userService.getUserById('1');
      
      // 应该只调用一次 API
      expect(mockHttpClient.get).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('updateUser', () => {
    it('should update user and emit event', async () => {
      const userId = '1';
      const updateData = { name: 'Jane Doe' };
      const updatedUser = { id: userId, name: 'Jane Doe', email: '<EMAIL>' };
      
      mockHttpClient.put.mockResolvedValue({ data: updatedUser });
      
      const result = await userService.updateUser(userId, updateData);
      
      expect(mockHttpClient.put).toHaveBeenCalledWith(`/api/users/${userId}`, updateData);
      expect(mockEventBus.emit).toHaveBeenCalledWith('user:updated', updatedUser);
      expect(result).toEqual(updatedUser);
    });
  });
});
```

## 🔗 集成测试

### 1. 微应用间通信测试

```typescript
// 微应用间通信集成测试
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { MicroCore } from '@micro-core/core';
import { UserCenterApp } from '../apps/UserCenterApp';
import { ProductCatalogApp } from '../apps/ProductCatalogApp';

describe('Micro-App Communication Integration', () => {
  let microCore: MicroCore;
  let userCenterApp: UserCenterApp;
  let productCatalogApp: ProductCatalogApp;
  
  beforeEach(async () => {
    // 初始化微前端核心
    microCore = new MicroCore({
      container: document.createElement('div')
    });
    
    // 注册微应用
    userCenterApp = new UserCenterApp();
    productCatalogApp = new ProductCatalogApp();
    
    await microCore.registerApp('user-center', userCenterApp);
    await microCore.registerApp('product-catalog', productCatalogApp);
  });
  
  afterEach(async () => {
    await microCore.destroy();
  });
  
  it('should communicate user login status between apps', async () => {
    // 启动用户中心应用
    await microCore.loadApp('user-center');
    
    // 模拟用户登录
    const mockUser = { id: '1', name: 'John Doe', email: '<EMAIL>' };
    await userCenterApp.login(mockUser);
    
    // 启动商品目录应用
    await microCore.loadApp('product-catalog');
    
    // 验证商品目录应用接收到用户信息
    const userInProductApp = productCatalogApp.getCurrentUser();
    expect(userInProductApp).toEqual(mockUser);
  });
  
  it('should handle cross-app data sharing', async () => {
    await microCore.loadApp('user-center');
    await microCore.loadApp('product-catalog');
    
    // 在用户中心设置购物车数据
    const cartData = [
      { productId: '1', quantity: 2 },
      { productId: '2', quantity: 1 }
    ];
    
    userCenterApp.setCartData(cartData);
    
    // 验证商品目录应用可以访问购物车数据
    const cartInProductApp = productCatalogApp.getCartData();
    expect(cartInProductApp).toEqual(cartData);
  });
  
  it('should handle app lifecycle events', async () => {
    const lifecycleEvents: string[] = [];
    
    // 监听生命周期事件
    microCore.on('app:mounted', (appName) => {
      lifecycleEvents.push(`${appName}:mounted`);
    });
    
    microCore.on('app:unmounted', (appName) => {
      lifecycleEvents.push(`${appName}:unmounted`);
    });
    
    // 加载和卸载应用
    await microCore.loadApp('user-center');
    await microCore.loadApp('product-catalog');
    await microCore.unloadApp('user-center');
    
    expect(lifecycleEvents).toEqual([
      'user-center:mounted',
      'product-catalog:mounted',
      'user-center:unmounted'
    ]);
  });
});
```

### 2. 路由集成测试

```typescript
// 路由集成测试
import { describe, it, expect, beforeEach } from 'vitest';
import { Router } from '@micro-core/router';
import { createMemoryHistory } from 'history';

describe('Router Integration', () => {
  let router: Router;
  let history: any;
  
  beforeEach(() => {
    history = createMemoryHistory();
    router = new Router({
      history,
      routes: [
        {
          path: '/user/*',
          app: 'user-center',
          component: () => import('../apps/UserCenterApp')
        },
        {
          path: '/products/*',
          app: 'product-catalog',
          component: () => import('../apps/ProductCatalogApp')
        }
      ]
    });
  });
  
  it('should route to correct micro-app', async () => {
    // 导航到用户中心
    await router.push('/user/profile');
    
    expect(router.getCurrentApp()).toBe('user-center');
    expect(router.getCurrentRoute().path).toBe('/user/profile');
  });
  
  it('should handle nested routing', async () => {
    await router.push('/products/category/electronics');
    
    expect(router.getCurrentApp()).toBe('product-catalog');
    expect(router.getCurrentRoute().params).toEqual({
      category: 'electronics'
    });
  });
  
  it('should preserve query parameters across app switches', async () => {
    await router.push('/user/profile?tab=settings');
    await router.push('/products/search?q=phone');
    await router.back();
    
    expect(router.getCurrentRoute().query).toEqual({
      tab: 'settings'
    });
  });
});
```

## 🎭 端到端测试

### 1. Playwright E2E 测试

```typescript
// Playwright 端到端测试
import { test, expect } from '@playwright/test';

test.describe('Micro-Frontend E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000');
  });
  
  test('should complete user registration flow', async ({ page }) => {
    // 导航到注册页面
    await page.click('[data-testid="register-button"]');
    await expect(page).toHaveURL(/.*\/register/);
    
    // 填写注册表单
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    
    // 提交表单
    await page.click('[data-testid="submit-button"]');
    
    // 验证注册成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page).toHaveURL(/.*\/dashboard/);
  });
  
  test('should handle micro-app navigation', async ({ page }) => {
    // 登录
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
    
    // 导航到商品目录
    await page.click('[data-testid="products-nav"]');
    await expect(page).toHaveURL(/.*\/products/);
    
    // 验证商品列表加载
    await expect(page.locator('[data-testid="product-list"]')).toBeVisible();
    
    // 导航到用户中心
    await page.click('[data-testid="profile-nav"]');
    await expect(page).toHaveURL(/.*\/user\/profile/);
    
    // 验证用户信息显示
    await expect(page.locator('[data-testid="user-info"]')).toContainText('testuser');
  });
  
  test('should handle error scenarios gracefully', async ({ page }) => {
    // 模拟网络错误
    await page.route('**/api/products', route => route.abort());
    
    await page.goto('/products');
    
    // 验证错误处理
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
    
    // 测试重试功能
    await page.unroute('**/api/products');
    await page.click('[data-testid="retry-button"]');
    
    // 验证重试后正常加载
    await expect(page.locator('[data-testid="product-list"]')).toBeVisible();
  });
});
```

### 2. 性能测试

```typescript
// 性能测试
import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test('should meet performance benchmarks', async ({ page }) => {
    // 开始性能监控
    await page.goto('http://localhost:3000');
    
    // 测量首次内容绘制 (FCP)
    const fcp = await page.evaluate(() => {
      return new Promise(resolve => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
          if (fcpEntry) {
            resolve(fcpEntry.startTime);
          }
        }).observe({ entryTypes: ['paint'] });
      });
    });
    
    expect(fcp).toBeLessThan(1800); // FCP < 1.8s
    
    // 测量微应用切换时间
    const switchStart = Date.now();
    await page.click('[data-testid="products-nav"]');
    await page.waitForSelector('[data-testid="product-list"]');
    const switchTime = Date.now() - switchStart;
    
    expect(switchTime).toBeLessThan(200); // 切换时间 < 200ms
  });
  
  test('should handle concurrent user interactions', async ({ browser }) => {
    // 创建多个页面模拟并发用户
    const pages = await Promise.all(
      Array.from({ length: 5 }, () => browser.newPage())
    );
    
    // 并发执行用户操作
    await Promise.all(
      pages.map(async (page, index) => {
        await page.goto('http://localhost:3000');
        await page.fill('[data-testid="username-input"]', `user${index}`);
        await page.fill('[data-testid="password-input"]', 'password123');
        await page.click('[data-testid="login-button"]');
        
        // 验证登录成功
        await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
      })
    );
    
    // 清理
    await Promise.all(pages.map(page => page.close()));
  });
});
```

通过实施这些全面的测试策略，可以确保微前端应用的质量、稳定性和性能，为用户提供可靠的使用体验。
