# 错误处理最佳实践

微前端架构中的错误处理比传统单体应用更加复杂，需要考虑跨应用的错误传播、隔离和恢复。本文档提供了全面的错误处理策略和最佳实践。

## 🎯 错误处理原则

### 核心原则

1. **错误隔离** - 一个微应用的错误不应影响其他应用
2. **优雅降级** - 提供备用方案确保核心功能可用
3. **快速恢复** - 自动或手动恢复机制
4. **用户友好** - 向用户提供清晰的错误信息
5. **可观测性** - 完整的错误监控和日志记录

### 错误分类

```typescript
// 错误类型定义
enum ErrorType {
  // 应用级错误
  APPLICATION_ERROR = 'application_error',
  
  // 网络错误
  NETWORK_ERROR = 'network_error',
  
  // 资源加载错误
  RESOURCE_ERROR = 'resource_error',
  
  // 路由错误
  ROUTING_ERROR = 'routing_error',
  
  // 通信错误
  COMMUNICATION_ERROR = 'communication_error',
  
  // 沙箱错误
  SANDBOX_ERROR = 'sandbox_error',
  
  // 配置错误
  CONFIGURATION_ERROR = 'configuration_error',
  
  // 权限错误
  PERMISSION_ERROR = 'permission_error'
}

// 错误严重级别
enum ErrorSeverity {
  LOW = 'low',        // 不影响核心功能
  MEDIUM = 'medium',  // 影响部分功能
  HIGH = 'high',      // 影响核心功能
  CRITICAL = 'critical' // 系统不可用
}

// 错误信息接口
interface MicroFrontendError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  context: {
    appName: string;
    route: string;
    timestamp: number;
    userAgent: string;
    userId?: string;
  };
  metadata?: Record<string, any>;
}
```

## 🛡️ 错误边界实现

### 1. React 错误边界

```typescript
// React 错误边界组件
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  isolate?: boolean;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class MicroAppErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;
  
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error
    };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });
    
    // 记录错误
    this.logError(error, errorInfo);
    
    // 通知错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // 上报错误
    this.reportError(error, errorInfo);
    
    // 尝试恢复
    this.attemptRecovery(error);
  }
  
  private logError(error: Error, errorInfo: ErrorInfo): void {
    console.error('微应用错误:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    });
  }
  
  private reportError(error: Error, errorInfo: ErrorInfo): void {
    // 发送错误报告到监控系统
    const errorReport: MicroFrontendError = {
      id: this.generateErrorId(),
      type: ErrorType.APPLICATION_ERROR,
      severity: this.determineSeverity(error),
      message: error.message,
      stack: error.stack,
      context: {
        appName: this.getAppName(),
        route: window.location.pathname,
        timestamp: Date.now(),
        userAgent: navigator.userAgent
      },
      metadata: {
        componentStack: errorInfo.componentStack,
        retryCount: this.retryCount
      }
    };
    
    // 发送到错误监控服务
    this.sendErrorReport(errorReport);
  }
  
  private attemptRecovery(error: Error): void {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      
      // 延迟重试
      setTimeout(() => {
        this.setState({ hasError: false, error: undefined, errorInfo: undefined });
      }, 1000 * this.retryCount);
    }
  }
  
  private handleRetry = (): void => {
    this.retryCount = 0;
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };
  
  render() {
    if (this.state.hasError) {
      // 自定义错误 UI
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // 默认错误 UI
      return (
        <div className="micro-app-error-boundary">
          <div className="error-content">
            <h2>应用出现错误</h2>
            <p>抱歉，应用遇到了一个错误。我们已经记录了这个问题。</p>
            
            {process.env.NODE_ENV === 'development' && (
              <details className="error-details">
                <summary>错误详情</summary>
                <pre>{this.state.error?.stack}</pre>
                <pre>{this.state.errorInfo?.componentStack}</pre>
              </details>
            )}
            
            <div className="error-actions">
              <button onClick={this.handleRetry}>重试</button>
              <button onClick={() => window.location.reload()}>刷新页面</button>
            </div>
          </div>
        </div>
      );
    }
    
    return this.props.children;
  }
  
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private determineSeverity(error: Error): ErrorSeverity {
    // 根据错误类型和消息确定严重级别
    if (error.name === 'ChunkLoadError') {
      return ErrorSeverity.HIGH;
    }
    
    if (error.message.includes('Network Error')) {
      return ErrorSeverity.MEDIUM;
    }
    
    return ErrorSeverity.LOW;
  }
  
  private getAppName(): string {
    return window.__CURRENT_MICRO_APP__ || 'unknown';
  }
  
  private async sendErrorReport(errorReport: MicroFrontendError): Promise<void> {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorReport)
      });
    } catch (error) {
      console.warn('错误报告发送失败:', error);
    }
  }
}

export default MicroAppErrorBoundary;
```

### 2. Vue 错误处理

```typescript
// Vue 3 错误处理
import { App, createApp } from 'vue';

// 全局错误处理器
function setupGlobalErrorHandler(app: App): void {
  // 捕获组件错误
  app.config.errorHandler = (error: Error, instance, info: string) => {
    console.error('Vue 组件错误:', {
      error: error.message,
      stack: error.stack,
      component: instance?.$options.name || 'Unknown',
      info,
      timestamp: new Date().toISOString()
    });
    
    // 上报错误
    reportVueError(error, instance, info);
    
    // 尝试恢复
    attemptVueRecovery(error, instance);
  };
  
  // 捕获 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的 Promise 错误:', event.reason);
    
    const error = new Error(event.reason);
    reportVueError(error, null, 'unhandledrejection');
    
    // 阻止默认行为
    event.preventDefault();
  });
}

// Vue 错误恢复组件
const ErrorRecovery = {
  name: 'ErrorRecovery',
  
  data() {
    return {
      hasError: false,
      error: null,
      retryCount: 0,
      maxRetries: 3
    };
  },
  
  errorCaptured(error: Error, instance: any, info: string) {
    this.hasError = true;
    this.error = error;
    
    console.error('捕获到子组件错误:', {
      error: error.message,
      component: instance?.$options.name,
      info
    });
    
    // 上报错误
    reportVueError(error, instance, info);
    
    // 返回 false 阻止错误继续传播
    return false;
  },
  
  methods: {
    retry() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        this.hasError = false;
        this.error = null;
        
        // 重新渲染子组件
        this.$forceUpdate();
      }
    },
    
    reset() {
      this.hasError = false;
      this.error = null;
      this.retryCount = 0;
    }
  },
  
  render() {
    if (this.hasError) {
      return (
        <div class="vue-error-recovery">
          <div class="error-content">
            <h3>组件加载失败</h3>
            <p>组件遇到错误，请尝试重新加载。</p>
            
            {process.env.NODE_ENV === 'development' && (
              <details class="error-details">
                <summary>错误详情</summary>
                <pre>{this.error?.stack}</pre>
              </details>
            )}
            
            <div class="error-actions">
              <button onClick={this.retry}>重试 ({this.retryCount}/{this.maxRetries})</button>
              <button onClick={this.reset}>重置</button>
            </div>
          </div>
        </div>
      );
    }
    
    return this.$slots.default?.();
  }
};

function reportVueError(error: Error, instance: any, info: string): void {
  const errorReport: MicroFrontendError = {
    id: `vue_error_${Date.now()}`,
    type: ErrorType.APPLICATION_ERROR,
    severity: ErrorSeverity.MEDIUM,
    message: error.message,
    stack: error.stack,
    context: {
      appName: window.__CURRENT_MICRO_APP__ || 'vue-app',
      route: window.location.pathname,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    },
    metadata: {
      framework: 'vue',
      component: instance?.$options.name,
      info
    }
  };
  
  // 发送错误报告
  sendErrorReport(errorReport);
}

function attemptVueRecovery(error: Error, instance: any): void {
  // 尝试重新挂载组件
  if (instance && instance.$parent) {
    setTimeout(() => {
      instance.$parent.$forceUpdate();
    }, 1000);
  }
}
```

### 3. Angular 错误处理

```typescript
// Angular 全局错误处理器
import { ErrorHandler, Injectable, Injector } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
  constructor(private injector: Injector) {}
  
  handleError(error: Error | HttpErrorResponse): void {
    console.error('Angular 全局错误:', error);
    
    // 区分不同类型的错误
    if (error instanceof HttpErrorResponse) {
      this.handleHttpError(error);
    } else {
      this.handleClientError(error);
    }
    
    // 上报错误
    this.reportError(error);
  }
  
  private handleHttpError(error: HttpErrorResponse): void {
    console.error('HTTP 错误:', {
      status: error.status,
      statusText: error.statusText,
      url: error.url,
      message: error.message
    });
    
    // 根据状态码处理
    switch (error.status) {
      case 401:
        // 未授权，重定向到登录页
        this.redirectToLogin();
        break;
      case 403:
        // 禁止访问，显示权限错误
        this.showPermissionError();
        break;
      case 404:
        // 资源不存在
        this.showNotFoundError();
        break;
      case 500:
        // 服务器错误
        this.showServerError();
        break;
      default:
        this.showGenericError();
    }
  }
  
  private handleClientError(error: Error): void {
    console.error('客户端错误:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    
    // 尝试恢复
    this.attemptRecovery(error);
  }
  
  private reportError(error: Error | HttpErrorResponse): void {
    const errorReport: MicroFrontendError = {
      id: `angular_error_${Date.now()}`,
      type: error instanceof HttpErrorResponse 
        ? ErrorType.NETWORK_ERROR 
        : ErrorType.APPLICATION_ERROR,
      severity: this.determineSeverity(error),
      message: error.message,
      stack: error instanceof Error ? error.stack : undefined,
      context: {
        appName: 'angular-app',
        route: window.location.pathname,
        timestamp: Date.now(),
        userAgent: navigator.userAgent
      },
      metadata: {
        framework: 'angular',
        errorType: error.constructor.name,
        ...(error instanceof HttpErrorResponse && {
          status: error.status,
          statusText: error.statusText,
          url: error.url
        })
      }
    };
    
    // 发送错误报告
    this.sendErrorReport(errorReport);
  }
  
  private determineSeverity(error: Error | HttpErrorResponse): ErrorSeverity {
    if (error instanceof HttpErrorResponse) {
      if (error.status >= 500) return ErrorSeverity.HIGH;
      if (error.status >= 400) return ErrorSeverity.MEDIUM;
      return ErrorSeverity.LOW;
    }
    
    if (error.name === 'ChunkLoadError') return ErrorSeverity.HIGH;
    return ErrorSeverity.MEDIUM;
  }
  
  private async sendErrorReport(errorReport: MicroFrontendError): Promise<void> {
    try {
      // 使用 Angular HttpClient 发送错误报告
      const http = this.injector.get(HttpClient);
      await http.post('/api/errors', errorReport).toPromise();
    } catch (error) {
      console.warn('错误报告发送失败:', error);
    }
  }
}

// 在 AppModule 中注册
@NgModule({
  providers: [
    {
      provide: ErrorHandler,
      useClass: GlobalErrorHandler
    }
  ]
})
export class AppModule {}
```
