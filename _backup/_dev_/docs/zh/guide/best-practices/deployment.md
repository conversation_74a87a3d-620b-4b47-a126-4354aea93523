# 部署指南最佳实践

微前端架构的部署策略需要考虑多个独立应用的协调部署、版本管理、回滚策略等复杂场景。本文档提供了全面的部署指南和最佳实践。

## 🎯 部署策略概述

### 部署模式

| 部署模式 | 优点 | 缺点 | 适用场景 |
|----------|------|------|----------|
| 独立部署 | 完全自治、快速发布 | 版本协调复杂 | 成熟团队、稳定应用 |
| 统一部署 | 版本一致、易于管理 | 发布耦合、风险集中 | 小团队、初期阶段 |
| 混合部署 | 灵活性高、风险可控 | 配置复杂 | 大型企业、复杂场景 |

### 部署架构

```typescript
// 部署配置接口
interface DeploymentConfig {
  // 环境配置
  environment: 'development' | 'staging' | 'production';
  
  // 应用配置
  apps: {
    [appName: string]: {
      version: string;
      entry: string;
      routes: string[];
      dependencies: string[];
      resources: ResourceConfig[];
    };
  };
  
  // 基础设施配置
  infrastructure: {
    cdn: CDNConfig;
    loadBalancer: LoadBalancerConfig;
    monitoring: MonitoringConfig;
    security: SecurityConfig;
  };
  
  // 部署策略
  strategy: {
    type: 'blue-green' | 'canary' | 'rolling';
    rollback: RollbackConfig;
    healthCheck: HealthCheckConfig;
  };
}
```

## 🚀 CI/CD 流水线

### 1. GitHub Actions 配置

```yaml
# .github/workflows/deploy.yml
name: Micro-Frontend Deployment

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # 检测变更的微应用
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      changed-apps: ${{ steps.changes.outputs.changed-apps }}
      matrix: ${{ steps.changes.outputs.matrix }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Detect changed micro-apps
        id: changes
        run: |
          # 检测变更的应用目录
          CHANGED_DIRS=$(git diff --name-only HEAD~1 HEAD | grep -E '^apps/' | cut -d'/' -f2 | sort -u)
          
          if [ -z "$CHANGED_DIRS" ]; then
            echo "changed-apps=[]" >> $GITHUB_OUTPUT
            echo "matrix={\"include\":[]}" >> $GITHUB_OUTPUT
          else
            APPS_JSON=$(echo "$CHANGED_DIRS" | jq -R -s -c 'split("\n")[:-1] | map(select(length > 0))')
            MATRIX_JSON=$(echo "$CHANGED_DIRS" | jq -R -s -c 'split("\n")[:-1] | map(select(length > 0)) | map({"app": .}) | {"include": .}')
            
            echo "changed-apps=$APPS_JSON" >> $GITHUB_OUTPUT
            echo "matrix=$MATRIX_JSON" >> $GITHUB_OUTPUT
          fi

  # 构建和测试
  build-and-test:
    needs: detect-changes
    if: needs.detect-changes.outputs.changed-apps != '[]'
    runs-on: ubuntu-latest
    strategy:
      matrix: ${{ fromJson(needs.detect-changes.outputs.matrix) }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Get pnpm store directory
        id: pnpm-cache
        run: echo "pnpm_cache_dir=$(pnpm store path)" >> $GITHUB_OUTPUT
      
      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.pnpm_cache_dir }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Lint
        run: pnpm lint:${{ matrix.app }}
      
      - name: Type check
        run: pnpm type-check:${{ matrix.app }}
      
      - name: Unit tests
        run: pnpm test:unit:${{ matrix.app }}
      
      - name: Build
        run: pnpm build:${{ matrix.app }}
        env:
          NODE_ENV: production
          VITE_APP_NAME: ${{ matrix.app }}
          VITE_API_BASE_URL: ${{ secrets.API_BASE_URL }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.app }}-build
          path: apps/${{ matrix.app }}/dist
          retention-days: 7

  # 集成测试
  integration-test:
    needs: [detect-changes, build-and-test]
    if: needs.detect-changes.outputs.changed-apps != '[]'
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts
      
      - name: Setup test environment
        run: |
          # 启动本地服务器
          pnpm start:test &
          
          # 等待服务启动
          npx wait-on http://localhost:3000
      
      - name: Run integration tests
        run: pnpm test:integration
      
      - name: Run E2E tests
        run: pnpm test:e2e
        env:
          PLAYWRIGHT_BROWSERS_PATH: ~/.cache/playwright

  # 部署到 Staging
  deploy-staging:
    needs: [detect-changes, build-and-test, integration-test]
    if: github.ref == 'refs/heads/develop' && needs.detect-changes.outputs.changed-apps != '[]'
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts
      
      - name: Deploy to staging
        run: |
          # 部署脚本
          ./scripts/deploy.sh staging
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          STAGING_BUCKET: ${{ secrets.STAGING_BUCKET }}
          CLOUDFRONT_DISTRIBUTION_ID: ${{ secrets.STAGING_CLOUDFRONT_ID }}
      
      - name: Run smoke tests
        run: pnpm test:smoke:staging

  # 部署到 Production
  deploy-production:
    needs: [detect-changes, build-and-test, integration-test]
    if: github.ref == 'refs/heads/main' && needs.detect-changes.outputs.changed-apps != '[]'
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts
      
      - name: Deploy to production
        run: |
          # 生产部署脚本
          ./scripts/deploy.sh production
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          PRODUCTION_BUCKET: ${{ secrets.PRODUCTION_BUCKET }}
          CLOUDFRONT_DISTRIBUTION_ID: ${{ secrets.PRODUCTION_CLOUDFRONT_ID }}
      
      - name: Run smoke tests
        run: pnpm test:smoke:production
      
      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

### 2. 部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=$1
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DEPLOYMENT_ID="deploy_${TIMESTAMP}"

if [ -z "$ENVIRONMENT" ]; then
  echo "Usage: $0 <environment>"
  exit 1
fi

echo "🚀 Starting deployment to $ENVIRONMENT..."

# 加载环境配置
source "./config/${ENVIRONMENT}.env"

# 创建部署清单
create_deployment_manifest() {
  echo "📋 Creating deployment manifest..."
  
  cat > deployment-manifest.json << EOF
{
  "deploymentId": "$DEPLOYMENT_ID",
  "environment": "$ENVIRONMENT",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "apps": {}
}
EOF

  # 为每个变更的应用添加信息
  for app_dir in artifacts/*-build; do
    if [ -d "$app_dir" ]; then
      app_name=$(basename "$app_dir" -build)
      version=$(cat "$app_dir/version.txt" 2>/dev/null || echo "unknown")
      
      # 更新清单
      jq --arg app "$app_name" --arg version "$version" \
         '.apps[$app] = {"version": $version, "buildPath": $ARGS.positional[0]}' \
         --args "$app_dir" deployment-manifest.json > tmp.json
      mv tmp.json deployment-manifest.json
    fi
  done
}

# 上传静态资源到 CDN
upload_to_cdn() {
  echo "📤 Uploading static assets to CDN..."
  
  for app_dir in artifacts/*-build; do
    if [ -d "$app_dir" ]; then
      app_name=$(basename "$app_dir" -build)
      version=$(cat "$app_dir/version.txt" 2>/dev/null || echo "unknown")
      
      # 上传到 S3
      aws s3 sync "$app_dir" "s3://${S3_BUCKET}/${app_name}/${version}/" \
        --delete \
        --cache-control "public, max-age=31536000, immutable" \
        --exclude "*.html" \
        --exclude "*.json"
      
      # HTML 文件使用较短的缓存时间
      aws s3 sync "$app_dir" "s3://${S3_BUCKET}/${app_name}/${version}/" \
        --cache-control "public, max-age=300" \
        --include "*.html" \
        --include "*.json"
      
      echo "✅ Uploaded $app_name version $version"
    fi
  done
}

# 更新应用注册表
update_app_registry() {
  echo "📝 Updating application registry..."
  
  # 调用应用注册 API
  curl -X POST "${API_BASE_URL}/api/registry/update" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${API_TOKEN}" \
    -d @deployment-manifest.json
  
  if [ $? -eq 0 ]; then
    echo "✅ Application registry updated"
  else
    echo "❌ Failed to update application registry"
    exit 1
  fi
}

# 健康检查
health_check() {
  echo "🏥 Performing health check..."
  
  local max_attempts=30
  local attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    echo "Health check attempt $attempt/$max_attempts..."
    
    # 检查主应用
    if curl -f -s "${APP_BASE_URL}/health" > /dev/null; then
      echo "✅ Main application is healthy"
      break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
      echo "❌ Health check failed after $max_attempts attempts"
      rollback_deployment
      exit 1
    fi
    
    sleep 10
    ((attempt++))
  done
}

# 回滚部署
rollback_deployment() {
  echo "🔄 Rolling back deployment..."
  
  # 获取上一个成功的部署
  local previous_deployment=$(curl -s "${API_BASE_URL}/api/deployments/previous" \
    -H "Authorization: Bearer ${API_TOKEN}" | jq -r '.deploymentId')
  
  if [ "$previous_deployment" != "null" ]; then
    # 恢复到上一个版本
    curl -X POST "${API_BASE_URL}/api/registry/rollback" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer ${API_TOKEN}" \
      -d "{\"deploymentId\": \"$previous_deployment\"}"
    
    echo "✅ Rolled back to deployment: $previous_deployment"
  else
    echo "❌ No previous deployment found for rollback"
  fi
}

# 清理 CDN 缓存
invalidate_cdn_cache() {
  echo "🗑️ Invalidating CDN cache..."
  
  aws cloudfront create-invalidation \
    --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
    --paths "/*" > /dev/null
  
  echo "✅ CDN cache invalidation initiated"
}

# 发送通知
send_notification() {
  local status=$1
  local message=$2
  
  if [ "$status" = "success" ]; then
    emoji="✅"
    color="good"
  else
    emoji="❌"
    color="danger"
  fi
  
  curl -X POST "$SLACK_WEBHOOK_URL" \
    -H "Content-Type: application/json" \
    -d "{
      \"channel\": \"#deployments\",
      \"username\": \"Deploy Bot\",
      \"icon_emoji\": \":rocket:\",
      \"attachments\": [{
        \"color\": \"$color\",
        \"title\": \"$emoji Deployment $status\",
        \"text\": \"$message\",
        \"fields\": [
          {\"title\": \"Environment\", \"value\": \"$ENVIRONMENT\", \"short\": true},
          {\"title\": \"Deployment ID\", \"value\": \"$DEPLOYMENT_ID\", \"short\": true}
        ]
      }]
    }"
}

# 主部署流程
main() {
  trap 'send_notification "failed" "Deployment failed with error"; exit 1' ERR
  
  create_deployment_manifest
  upload_to_cdn
  update_app_registry
  invalidate_cdn_cache
  health_check
  
  send_notification "success" "Deployment completed successfully"
  
  echo "🎉 Deployment to $ENVIRONMENT completed successfully!"
  echo "📋 Deployment ID: $DEPLOYMENT_ID"
}

# 执行主流程
main
```

### 3. 蓝绿部署策略

```typescript
// 蓝绿部署管理器
class BlueGreenDeployment {
  private currentEnvironment: 'blue' | 'green' = 'blue';
  private deploymentConfig: DeploymentConfig;
  
  constructor(config: DeploymentConfig) {
    this.deploymentConfig = config;
  }
  
  async deploy(apps: AppDeployment[]): Promise<DeploymentResult> {
    const targetEnvironment = this.currentEnvironment === 'blue' ? 'green' : 'blue';
    
    try {
      // 1. 部署到目标环境
      await this.deployToEnvironment(targetEnvironment, apps);
      
      // 2. 健康检查
      await this.performHealthCheck(targetEnvironment);
      
      // 3. 切换流量
      await this.switchTraffic(targetEnvironment);
      
      // 4. 更新当前环境标记
      this.currentEnvironment = targetEnvironment;
      
      // 5. 清理旧环境（可选）
      await this.cleanupOldEnvironment();
      
      return {
        success: true,
        environment: targetEnvironment,
        deploymentId: this.generateDeploymentId(),
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      // 部署失败，保持当前环境
      console.error('蓝绿部署失败:', error);
      throw error;
    }
  }
  
  private async deployToEnvironment(environment: string, apps: AppDeployment[]): Promise<void> {
    console.log(`部署到 ${environment} 环境...`);
    
    // 并行部署所有应用
    await Promise.all(
      apps.map(app => this.deployApp(environment, app))
    );
  }
  
  private async deployApp(environment: string, app: AppDeployment): Promise<void> {
    const targetUrl = `https://${environment}.${this.deploymentConfig.domain}`;
    
    // 上传应用文件
    await this.uploadAppFiles(environment, app);
    
    // 更新应用配置
    await this.updateAppConfig(environment, app);
    
    // 验证应用部署
    await this.verifyAppDeployment(targetUrl, app);
  }
  
  private async performHealthCheck(environment: string): Promise<void> {
    const targetUrl = `https://${environment}.${this.deploymentConfig.domain}`;
    const maxAttempts = 30;
    let attempt = 1;
    
    while (attempt <= maxAttempts) {
      try {
        const response = await fetch(`${targetUrl}/health`);
        
        if (response.ok) {
          const healthData = await response.json();
          
          // 检查所有微应用的健康状态
          const allHealthy = Object.values(healthData.apps).every(
            (status: any) => status === 'healthy'
          );
          
          if (allHealthy) {
            console.log(`${environment} 环境健康检查通过`);
            return;
          }
        }
      } catch (error) {
        console.warn(`健康检查失败 (尝试 ${attempt}/${maxAttempts}):`, error.message);
      }
      
      if (attempt === maxAttempts) {
        throw new Error(`${environment} 环境健康检查失败`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 10000));
      attempt++;
    }
  }
  
  private async switchTraffic(targetEnvironment: string): Promise<void> {
    console.log(`切换流量到 ${targetEnvironment} 环境...`);
    
    // 更新负载均衡器配置
    await this.updateLoadBalancer(targetEnvironment);
    
    // 更新 DNS 记录
    await this.updateDNSRecord(targetEnvironment);
    
    // 等待 DNS 传播
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    console.log('流量切换完成');
  }
}
```

通过实施这些部署最佳实践，可以确保微前端应用的可靠部署、快速回滚和高可用性，为用户提供稳定的服务体验。
