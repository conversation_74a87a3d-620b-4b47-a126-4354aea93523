# 性能优化指南

本指南提供了全面的 Micro-Core 微前端性能优化策略，帮助您构建高性能的微前端应用。

## 📋 目录

- [性能概述](#性能概述)
- [应用加载优化](#应用加载优化)
- [资源优化](#资源优化)
- [运行时优化](#运行时优化)
- [内存管理](#内存管理)
- [网络优化](#网络优化)
- [监控和分析](#监控和分析)
- [最佳实践](#最佳实践)

## 性能概述

### 性能指标体系

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 性能指标体系                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   加载性能       │    │   运行时性能     │    │   用户体验       ││
│  │                 │    │                 │    │                 ││
│  │ • FCP: 1.8s     │    │ • FPS: 60       │    │ • LCP: 2.5s     ││
│  │ • LCP: 2.5s     │    │ • Memory: <100MB│    │ • FID: <100ms   ││
│  │ • TTI: 3.8s     │    │ • CPU: <50%     │    │ • CLS: <0.1     ││
│  │ • Bundle: <1MB  │    │ • Network: <10  │    │ • INP: <200ms   ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### 性能基准

```typescript
const performanceBenchmarks = {
  loading: {
    firstContentfulPaint: 1800,    // 首次内容绘制 < 1.8s
    largestContentfulPaint: 2500,  // 最大内容绘制 < 2.5s
    timeToInteractive: 3800,       // 可交互时间 < 3.8s
    firstInputDelay: 100,          // 首次输入延迟 < 100ms
    cumulativeLayoutShift: 0.1     // 累积布局偏移 < 0.1
  },
  
  runtime: {
    frameRate: 60,                 // 帧率 >= 60 FPS
    memoryUsage: 100 * 1024 * 1024, // 内存使用 < 100MB
    cpuUsage: 50,                  // CPU 使用率 < 50%
    networkRequests: 10            // 并发网络请求 < 10
  }
}
```

## 应用加载优化

### 1. 预加载策略

```typescript
const microCore = new MicroCore({
  preload: {
    // 关键应用立即预加载
    critical: ['user-app', 'navigation-app'],
    
    // 高优先级应用在空闲时预加载
    high: ['dashboard-app', 'notification-app'],
    
    // 预加载策略
    strategy: 'idle', // 'immediate' | 'idle' | 'viewport'
    
    // 预加载时机
    timing: {
      idle: 2000,        // 空闲 2 秒后开始
      viewport: 200,     // 进入视口前 200px
      interaction: 100   // 交互前 100ms
    }
  }
})

// 智能预加载
class PreloadManager {
  private preloadedApps: Set<string> = new Set()
  
  // 基于用户行为预测
  predictNextApp(currentPath: string): string[] {
    const predictions = {
      '/dashboard': ['user-app', 'analytics-app'],
      '/user': ['settings-app', 'profile-app'],
      '/order': ['payment-app', 'shipping-app']
    }
    
    return predictions[currentPath] || []
  }
  
  // 智能预加载
  async smartPreload(currentApp: string) {
    const nextApps = this.predictNextApp(currentApp)
    
    for (const app of nextApps) {
      if (!this.preloadedApps.has(app)) {
        await this.preloadApp(app)
      }
    }
  }
  
  private async preloadApp(appName: string) {
    try {
      await microCore.preloadApp(appName)
      this.preloadedApps.add(appName)
      console.log(`预加载完成: ${appName}`)
    } catch (error) {
      console.error(`预加载失败: ${appName}`, error)
    }
  }
}
```

### 2. 懒加载实现

```typescript
// 应用懒加载
const microCore = new MicroCore({
  apps: [
    {
      name: 'admin-app',
      // 动态导入实现懒加载
      entry: () => import('./apps/admin-app'),
      activeWhen: '/admin',
      
      // 懒加载配置
      lazy: {
        enabled: true,
        threshold: 0.1,      // 进入视口 10% 时加载
        rootMargin: '50px',  // 提前 50px 开始加载
        delay: 100           // 延迟 100ms 加载
      }
    }
  ]
})

// 组件级懒加载
import { lazy, Suspense } from 'react'

const LazyComponent = lazy(() => 
  import('./HeavyComponent').then(module => ({
    default: module.HeavyComponent
  }))
)

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  )
}
```

## 资源优化

### 1. 代码分割

```typescript
// Webpack 代码分割配置
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // 第三方库单独打包
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10
        },
        
        // 公共代码提取
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          priority: 5
        },
        
        // 框架代码单独打包
        framework: {
          test: /[\\/]node_modules[\\/](react|react-dom|vue|@angular)[\\/]/,
          name: 'framework',
          chunks: 'all',
          priority: 15
        }
      }
    }
  }
}
```

### 2. Tree Shaking

```typescript
// 启用 Tree Shaking
// package.json
{
  "sideEffects": false, // 标记为无副作用
  "type": "module"      // 使用 ES 模块
}

// 按需导入
// ✅ 推荐
import { debounce } from 'lodash-es'
import { Button } from 'antd'

// ❌ 避免
import _ from 'lodash'
import * as antd from 'antd'
```

### 3. 资源压缩

```typescript
// Webpack 压缩配置
const TerserPlugin = require('terser-webpack-plugin')

module.exports = {
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,    // 移除 console
            drop_debugger: true,   // 移除 debugger
            pure_funcs: ['console.log'] // 移除指定函数
          }
        }
      })
    ]
  }
}

// Gzip 压缩
const CompressionPlugin = require('compression-webpack-plugin')

module.exports = {
  plugins: [
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,      // 大于 8KB 的文件才压缩
      minRatio: 0.8         // 压缩比小于 0.8 才压缩
    })
  ]
}
```

## 运行时优化

### 1. 虚拟滚动

```typescript
// React 虚拟滚动实现
import { FixedSizeList as List } from 'react-window'

function VirtualList({ items }) {
  const Row = ({ index, style }) => (
    <div style={style}>
      {items[index]}
    </div>
  )
  
  return (
    <List
      height={600}        // 容器高度
      itemCount={items.length}
      itemSize={50}       // 每项高度
      width="100%"
    >
      {Row}
    </List>
  )
}
```

### 2. 防抖和节流

```typescript
// 防抖函数
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return function (...args: Parameters<T>) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

// 节流函数
function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return function (...args: Parameters<T>) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func.apply(this, args)
    }
  }
}

// 使用示例
const debouncedSearch = debounce((query: string) => {
  console.log('搜索:', query)
}, 300)

const throttledScroll = throttle(() => {
  console.log('滚动事件')
}, 100)
```

### 3. 组件优化

```typescript
// React 组件优化
import { memo, useMemo, useCallback } from 'react'

const OptimizedComponent = memo(({ data, onUpdate }) => {
  // 缓存计算结果
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      processed: expensiveCalculation(item)
    }))
  }, [data])
  
  // 缓存回调函数
  const handleClick = useCallback((id: string) => {
    onUpdate(id)
  }, [onUpdate])
  
  return (
    <div>
      {processedData.map(item => (
        <div key={item.id} onClick={() => handleClick(item.id)}>
          {item.name}
        </div>
      ))}
    </div>
  )
})
```

## 内存管理

### 1. 内存泄漏检测

```typescript
// 内存监控类
class MemoryMonitor {
  private memoryThreshold = 100 * 1024 * 1024 // 100MB
  
  startMonitoring() {
    // 监控内存使用
    if ('memory' in performance) {
      setInterval(() => {
        const { usedJSHeapSize, totalJSHeapSize } = performance.memory
        const usage = (usedJSHeapSize / totalJSHeapSize) * 100
        
        if (usage > 80) {
          console.warn('内存使用率过高:', usage.toFixed(2) + '%')
          this.triggerGarbageCollection()
        }
      }, 10000)
    }
  }
  
  private triggerGarbageCollection() {
    // 清理缓存
    this.clearCaches()
    
    // 强制垃圾回收（仅开发环境）
    if (process.env.NODE_ENV === 'development' && window.gc) {
      window.gc()
    }
  }
  
  private clearCaches() {
    // 清理图片缓存
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      if (!img.isConnected) {
        img.src = ''
      }
    })
  }
}
```

### 2. 对象池模式

```typescript
// 对象池实现
class ObjectPool<T> {
  private pool: T[] = []
  private createFn: () => T
  private resetFn: (obj: T) => void
  
  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn
    this.resetFn = resetFn
    
    // 预创建对象
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn())
    }
  }
  
  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!
    }
    return this.createFn()
  }
  
  release(obj: T) {
    this.resetFn(obj)
    this.pool.push(obj)
  }
}

// 使用示例
interface ListItem {
  id: string
  name: string
  selected: boolean
}

const listItemPool = new ObjectPool<ListItem>(
  () => ({ id: '', name: '', selected: false }),
  (item) => {
    item.id = ''
    item.name = ''
    item.selected = false
  },
  50 // 预创建 50 个对象
)
```

## 网络优化

### 1. CDN 优化

```typescript
// CDN 配置
const cdnConfig = {
  domains: {
    static: 'https://static.example.com',
    api: 'https://api.example.com',
    images: 'https://images.example.com'
  },
  
  // 多 CDN 配置
  fallback: [
    'https://cdn1.example.com',
    'https://cdn2.example.com'
  ]
}

// CDN 资源加载器
class CDNLoader {
  private currentCDN = 0
  
  async loadResource(path: string, type: 'script' | 'style' | 'image') {
    const cdnUrls = this.getCDNUrls(path)
    
    for (let i = 0; i < cdnUrls.length; i++) {
      try {
        const url = cdnUrls[(this.currentCDN + i) % cdnUrls.length]
        const resource = await this.loadFromCDN(url, type)
        
        // 成功后更新当前 CDN
        this.currentCDN = (this.currentCDN + i) % cdnUrls.length
        return resource
        
      } catch (error) {
        console.warn(`CDN 加载失败: ${cdnUrls[i]}`, error)
        continue
      }
    }
    
    throw new Error('所有 CDN 都无法访问')
  }
  
  private getCDNUrls(path: string): string[] {
    return [
      cdnConfig.domains.static + path,
      ...cdnConfig.fallback.map(cdn => cdn + path)
    ]
  }
}
```

### 2. 缓存策略

```typescript
// Service Worker 缓存策略
class CacheStrategy {
  private cacheName = 'micro-core-cache-v1'
  
  async install() {
    const cache = await caches.open(this.cacheName)
    
    // 预缓存关键资源
    const criticalResources = [
      '/',
      '/app.js',
      '/app.css',
      '/manifest.json'
    ]
    
    await cache.addAll(criticalResources)
  }
  
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url)
    
    // API 请求：网络优先
    if (url.pathname.startsWith('/api/')) {
      return this.networkFirst(request)
    }
    
    // 静态资源：缓存优先
    if (this.isStaticResource(url)) {
      return this.cacheFirst(request)
    }
    
    // HTML 页面：网络优先，缓存降级
    return this.networkFirst(request)
  }
  
  private async networkFirst(request: Request): Promise<Response> {
    try {
      const response = await fetch(request)
      
      if (response.ok) {
        const cache = await caches.open(this.cacheName)
        cache.put(request, response.clone())
      }
      
      return response
    } catch (error) {
      const cachedResponse = await caches.match(request)
      if (cachedResponse) {
        return cachedResponse
      }
      throw error
    }
  }
  
  private async cacheFirst(request: Request): Promise<Response> {
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    const response = await fetch(request)
    if (response.ok) {
      const cache = await caches.open(this.cacheName)
      cache.put(request, response.clone())
    }
    
    return response
  }
}
```

## 监控和分析

### 1. 性能监控

```typescript
// 性能监控配置
const microCore = new MicroCore({
  monitoring: {
    performance: {
      enabled: true,
      metrics: ['loadTime', 'renderTime', 'memoryUsage'],
      reportInterval: 60000 // 1分钟上报一次
    },
    
    // 自定义监控
    custom: {
      onMetric: (metric) => {
        console.log('性能指标:', metric)
        
        // 上报到监控系统
        if (metric.value > metric.threshold) {
          reportToMonitoring(metric)
        }
      }
    }
  }
})

// 手动记录性能
microCore.performance.mark('app-load-start')
// ... 应用加载逻辑
microCore.performance.mark('app-load-end')
microCore.performance.measure('app-load-duration', 'app-load-start', 'app-load-end')
```

### 2. 用户体验监控

```typescript
// Web Vitals 监控
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function setupWebVitals() {
  getCLS(console.log)  // 累积布局偏移
  getFID(console.log)  // 首次输入延迟
  getFCP(console.log)  // 首次内容绘制
  getLCP(console.log)  // 最大内容绘制
  getTTFB(console.log) // 首字节时间
}

// 自定义性能指标
class PerformanceTracker {
  private metrics: Map<string, number> = new Map()
  
  startTiming(name: string) {
    this.metrics.set(`${name}_start`, performance.now())
  }
  
  endTiming(name: string) {
    const start = this.metrics.get(`${name}_start`)
    if (start) {
      const duration = performance.now() - start
      this.metrics.set(name, duration)
      console.log(`${name}: ${duration.toFixed(2)}ms`)
    }
  }
  
  getMetric(name: string): number | undefined {
    return this.metrics.get(name)
  }
  
  getAllMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics)
  }
}
```

## 最佳实践

### 1. 性能预算

```typescript
// 性能预算配置
const performanceBudget = {
  // 资源大小限制
  assets: {
    javascript: 1024 * 1024,    // 1MB
    css: 256 * 1024,            // 256KB
    images: 2 * 1024 * 1024,    // 2MB
    fonts: 512 * 1024           // 512KB
  },
  
  // 性能指标限制
  metrics: {
    firstContentfulPaint: 1800,
    largestContentfulPaint: 2500,
    timeToInteractive: 3800,
    firstInputDelay: 100
  },
  
  // 网络请求限制
  network: {
    maxRequests: 50,
    maxDomainRequests: 6
  }
}

// 预算检查
function checkPerformanceBudget() {
  const entries = performance.getEntriesByType('navigation')
  const navigation = entries[0] as PerformanceNavigationTiming
  
  const metrics = {
    loadTime: navigation.loadEventEnd - navigation.fetchStart,
    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
    firstByte: navigation.responseStart - navigation.fetchStart
  }
  
  Object.entries(metrics).forEach(([key, value]) => {
    const budget = performanceBudget.metrics[key]
    if (budget && value > budget) {
      console.warn(`性能预算超标: ${key} = ${value}ms (预算: ${budget}ms)`)
    }
  })
}
```

### 2. 渐进式增强

```typescript
// 渐进式增强策略
class ProgressiveEnhancement {
  private features: Map<string, boolean> = new Map()
  
  constructor() {
    this.detectFeatures()
  }
  
  private detectFeatures() {
    // 检测浏览器特性
    this.features.set('webp', this.supportsWebP())
    this.features.set('intersectionObserver', 'IntersectionObserver' in window)
    this.features.set('serviceWorker', 'serviceWorker' in navigator)
    this.features.set('webAssembly', 'WebAssembly' in window)
  }
  
  private supportsWebP(): boolean {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1
    return canvas.toDataURL('image/webp').indexOf('webp') > -1
  }
  
  loadOptimalResources() {
    // 根据特性支持情况加载最优资源
    if (this.features.get('webp')) {
      this.loadWebPImages()
    } else {
      this.loadJPEGImages()
    }
    
    if (this.features.get('intersectionObserver')) {
      this.enableLazyLoading()
    } else {
      this.loadAllImages()
    }
    
    if (this.features.get('serviceWorker')) {
      this.registerServiceWorker()
    }
  }
}
```

### 3. 性能优化清单

```typescript
// 性能优化清单
const performanceChecklist = {
  loading: [
    '✅ 启用 HTTP/2',
    '✅ 使用 CDN',
    '✅ 启用 Gzip 压缩',
    '✅ 优化图片格式',
    '✅ 预加载关键资源',
    '✅ 懒加载非关键资源',
    '✅ 代码分割',
    '✅ Tree Shaking'
  ],
  
  runtime: [
    '✅ 虚拟滚动',
    '✅ 防抖节流',
    '✅ 组件缓存',
    '✅ 内存管理',
    '✅ 事件委托',
    '✅ 避免强制同步布局',
    '✅ 使用 Web Workers',
    '✅ 优化动画'
  ],
  
  monitoring: [
    '✅ 性能监控',
    '✅ 错误追踪',
    '✅ 用户体验监控',
    '✅ 性能预算',
    '✅ 持续集成检查'
  ]
}
```

---

更多性能优化相关信息请参考：
- [故障排除指南](/guide/troubleshooting)
- [架构设计文档](/guide/architecture)
- [API 参考文档](/api/)
- [最佳实践](/guide/best-practices)

如需专业的性能优化咨询服务，请联系我们的技术支持团队。
