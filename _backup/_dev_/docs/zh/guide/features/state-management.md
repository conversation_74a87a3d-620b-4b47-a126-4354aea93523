# 状态管理

Micro-Core 提供了强大的状态管理系统，支持全局状态共享、本地状态隔离和响应式状态更新，确保微前端应用间的数据一致性。

## 状态管理概览

### 1. 全局状态 (GlobalState)

用于跨应用共享的状态数据。

```typescript
import { GlobalState } from '@micro-core/core';

// 设置全局状态
GlobalState.set('currentUser', {
  id: '123',
  name: '<PERSON>',
  role: 'admin'
});

// 获取全局状态
const user = GlobalState.get('currentUser');
```

### 2. 应用状态 (AppState)

每个微应用的私有状态，相互隔离。

```typescript
import { AppState } from '@micro-core/core';

// 在微应用中设置本地状态
AppState.set('localData', { theme: 'dark', language: 'zh-CN' });

// 获取本地状态
const localData = AppState.get('localData');
```

### 3. 共享状态 (SharedState)

特定应用组之间共享的状态。

```typescript
import { SharedState } from '@micro-core/core';

// 创建共享状态空间
const userAppsState = SharedState.createNamespace('user-apps');

// 在命名空间中设置状态
userAppsState.set('sharedConfig', { apiUrl: 'https://api.example.com' });
```

## 全局状态管理

### 基础操作

```typescript
import { GlobalState } from '@micro-core/core';

// 1. 设置状态
GlobalState.set('theme', 'dark');
GlobalState.set('user', { id: 1, name: 'John', email: '<EMAIL>' });

// 2. 获取状态
const theme = GlobalState.get('theme');
const user = GlobalState.get('user');

// 3. 检查状态是否存在
if (GlobalState.has('user')) {
  console.log('用户已登录');
}

// 4. 获取所有状态
const allState = GlobalState.getAll();

// 5. 删除状态
GlobalState.remove('tempData');

// 6. 清空所有状态
GlobalState.clear();
```

### 嵌套状态操作

```typescript
// 设置嵌套状态
GlobalState.set('app.settings.theme', 'dark');
GlobalState.set('user.profile.avatar', '/avatars/john.jpg');
GlobalState.set('cart.items', [
  { id: 1, name: 'Product 1', price: 100 },
  { id: 2, name: 'Product 2', price: 200 }
]);

// 获取嵌套状态
const theme = GlobalState.get('app.settings.theme');
const avatar = GlobalState.get('user.profile.avatar');
const cartItems = GlobalState.get('cart.items');

// 更新嵌套状态的部分内容
GlobalState.merge('user.profile', {
  lastLogin: Date.now(),
  loginCount: 5
});

// 数组操作
GlobalState.push('cart.items', { id: 3, name: 'Product 3', price: 150 });
GlobalState.splice('cart.items', 0, 1); // 删除第一个商品
```

### 状态监听

```typescript
// 监听特定状态变化
const unwatch = GlobalState.watch('theme', (newTheme, oldTheme) => {
  console.log(`主题从 ${oldTheme} 变更为 ${newTheme}`);
  document.body.className = `theme-${newTheme}`;
});

// 监听嵌套状态变化
GlobalState.watch('user.profile', (newProfile, oldProfile) => {
  console.log('用户资料更新:', newProfile);
});

// 监听数组变化
GlobalState.watch('cart.items', (newItems, oldItems) => {
  console.log(`购物车商品数量: ${newItems.length}`);
});

// 监听所有状态变化
GlobalState.watchAll((key, newValue, oldValue) => {
  console.log(`状态 ${key} 发生变化:`, { newValue, oldValue });
});

// 取消监听
unwatch();
```

### 状态计算属性

```typescript
// 定义计算属性
GlobalState.computed('cartTotal', ['cart.items'], (items) => {
  return items.reduce((total, item) => total + item.price * item.quantity, 0);
});

GlobalState.computed('userDisplayName', ['user.profile'], (profile) => {
  return profile ? `${profile.firstName} ${profile.lastName}` : '未登录';
});

// 获取计算属性
const total = GlobalState.get('cartTotal');
const displayName = GlobalState.get('userDisplayName');

// 监听计算属性变化
GlobalState.watch('cartTotal', (newTotal) => {
  console.log(`购物车总价: ¥${newTotal}`);
});
```

## 应用状态管理

### 应用级状态隔离

```typescript
// 在 React 微应用中
import { AppState } from '@micro-core/core';

const ReactApp = () => {
  // 设置应用私有状态
  AppState.set('componentState', {
    currentPage: 1,
    pageSize: 10,
    filters: { category: 'electronics' }
  });

  // 获取应用私有状态
  const componentState = AppState.get('componentState');

  // 监听应用状态变化
  AppState.watch('componentState', (newState) => {
    console.log('组件状态更新:', newState);
  });

  return <div>React 应用内容</div>;
};
```

### 状态持久化

```typescript
// 启用状态持久化
GlobalState.enablePersistence('localStorage', {
  include: ['user', 'app.settings'], // 只持久化指定状态
  exclude: ['tempData'], // 排除临时数据
  prefix: 'micro-core-', // 存储前缀
  serialize: JSON.stringify, // 自定义序列化
  deserialize: JSON.parse // 自定义反序列化
});

// 手动保存状态
GlobalState.save();

// 手动恢复状态
GlobalState.restore();
```

## 状态中间件

### 日志中间件

```typescript
import { GlobalState } from '@micro-core/core';

// 添加日志中间件
GlobalState.use((action, next) => {
  console.log(`[StateLog] ${action.type}:`, action.payload);
  const startTime = performance.now();
  
  const result = next(action);
  
  const endTime = performance.now();
  console.log(`[StateLog] ${action.type} 执行时间: ${endTime - startTime}ms`);
  
  return result;
});
```

### 验证中间件

```typescript
// 添加状态验证中间件
GlobalState.use((action, next) => {
  if (action.type === 'SET' && action.key === 'user') {
    const user = action.payload;
    
    // 验证用户数据
    if (!user.id || !user.name) {
      throw new Error('用户数据格式不正确');
    }
    
    // 验证邮箱格式
    if (user.email && !/\S+@\S+\.\S+/.test(user.email)) {
      throw new Error('邮箱格式不正确');
    }
  }
  
  return next(action);
});
```

### 权限中间件

```typescript
// 添加权限检查中间件
GlobalState.use((action, next) => {
  const protectedKeys = ['admin.settings', 'system.config'];
  
  if (action.type === 'SET' && protectedKeys.some(key => action.key.startsWith(key))) {
    const currentUser = GlobalState.get('currentUser');
    
    if (!currentUser || currentUser.role !== 'admin') {
      throw new Error('权限不足，无法修改系统设置');
    }
  }
  
  return next(action);
});
```

## React 集成

### useGlobalState Hook

```typescript
import React from 'react';
import { GlobalState } from '@micro-core/core';

// 自定义 Hook
function useGlobalState<T>(key: string, defaultValue?: T): [T, (value: T) => void] {
  const [state, setState] = React.useState<T>(() => {
    return GlobalState.get(key) ?? defaultValue;
  });

  React.useEffect(() => {
    const unwatch = GlobalState.watch(key, (newValue) => {
      setState(newValue);
    });

    return unwatch;
  }, [key]);

  const updateState = React.useCallback((value: T) => {
    GlobalState.set(key, value);
  }, [key]);

  return [state, updateState];
}

// 使用示例
const UserProfile: React.FC = () => {
  const [user, setUser] = useGlobalState('currentUser', null);
  const [theme, setTheme] = useGlobalState('theme', 'light');

  const handleLogin = (userData) => {
    setUser(userData);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <div className={`user-profile theme-${theme}`}>
      {user ? (
        <div>
          <h2>欢迎, {user.name}!</h2>
          <button onClick={toggleTheme}>切换主题</button>
        </div>
      ) : (
        <button onClick={() => handleLogin({ id: 1, name: 'John' })}>
          登录
        </button>
      )}
    </div>
  );
};
```

### 状态提供者组件

```typescript
import React, { createContext, useContext, useEffect, useState } from 'react';
import { GlobalState } from '@micro-core/core';

// 创建状态上下文
const StateContext = createContext(null);

// 状态提供者组件
export const StateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState(() => GlobalState.getAll());

  useEffect(() => {
    const unwatch = GlobalState.watchAll((key, newValue) => {
      setState(prevState => ({
        ...prevState,
        [key]: newValue
      }));
    });

    return unwatch;
  }, []);

  return (
    <StateContext.Provider value={state}>
      {children}
    </StateContext.Provider>
  );
};

// 使用状态的 Hook
export const useAppState = () => {
  const state = useContext(StateContext);
  if (!state) {
    throw new Error('useAppState must be used within StateProvider');
  }
  return state;
};
```

## Vue 集成

### Vue 3 Composition API

```typescript
import { ref, watch, onUnmounted } from 'vue';
import { GlobalState } from '@micro-core/core';

// 全局状态组合函数
export function useGlobalState<T>(key: string, defaultValue?: T) {
  const state = ref<T>(GlobalState.get(key) ?? defaultValue);

  const unwatch = GlobalState.watch(key, (newValue) => {
    state.value = newValue;
  });

  const updateState = (value: T) => {
    GlobalState.set(key, value);
  };

  onUnmounted(() => {
    unwatch();
  });

  return {
    state: readonly(state),
    updateState
  };
}

// 使用示例
export default {
  setup() {
    const { state: user, updateState: setUser } = useGlobalState('currentUser');
    const { state: theme, updateState: setTheme } = useGlobalState('theme', 'light');

    const handleLogin = (userData) => {
      setUser(userData);
    };

    const toggleTheme = () => {
      setTheme(theme.value === 'light' ? 'dark' : 'light');
    };

    return {
      user,
      theme,
      handleLogin,
      toggleTheme
    };
  }
};
```

### Vue 2 Options API

```javascript
import { GlobalState } from '@micro-core/core';

export default {
  data() {
    return {
      user: GlobalState.get('currentUser'),
      theme: GlobalState.get('theme') || 'light'
    };
  },
  created() {
    // 监听状态变化
    this.unwatchUser = GlobalState.watch('currentUser', (newUser) => {
      this.user = newUser;
    });

    this.unwatchTheme = GlobalState.watch('theme', (newTheme) => {
      this.theme = newTheme;
    });
  },
  beforeDestroy() {
    // 清理监听器
    if (this.unwatchUser) this.unwatchUser();
    if (this.unwatchTheme) this.unwatchTheme();
  },
  methods: {
    handleLogin(userData) {
      GlobalState.set('currentUser', userData);
    },
    toggleTheme() {
      const newTheme = this.theme === 'light' ? 'dark' : 'light';
      GlobalState.set('theme', newTheme);
    }
  }
};
```

## 状态同步策略

### 乐观更新

```typescript
// 乐观更新示例
const updateUserProfile = async (profileData) => {
  // 立即更新本地状态
  const oldProfile = GlobalState.get('user.profile');
  GlobalState.set('user.profile', { ...oldProfile, ...profileData });

  try {
    // 发送到服务器
    const updatedProfile = await api.updateProfile(profileData);
    
    // 服务器返回后更新为最新数据
    GlobalState.set('user.profile', updatedProfile);
  } catch (error) {
    // 失败时回滚到原始状态
    GlobalState.set('user.profile', oldProfile);
    
    // 显示错误信息
    GlobalState.set('app.error', '更新用户资料失败');
  }
};
```

### 悲观更新

```typescript
// 悲观更新示例
const updateUserProfile = async (profileData) => {
  // 设置加载状态
  GlobalState.set('app.loading', true);

  try {
    // 先发送到服务器
    const updatedProfile = await api.updateProfile(profileData);
    
    // 成功后更新本地状态
    GlobalState.set('user.profile', updatedProfile);
    GlobalState.set('app.success', '用户资料更新成功');
  } catch (error) {
    // 显示错误信息
    GlobalState.set('app.error', '更新用户资料失败');
  } finally {
    // 清除加载状态
    GlobalState.set('app.loading', false);
  }
};
```

## 性能优化

### 状态分片

```typescript
// 将大状态对象分片存储
const largeDataset = await fetchLargeDataset();

// 不推荐：存储为单个大对象
// GlobalState.set('dataset', largeDataset);

// 推荐：分片存储
largeDataset.forEach((item, index) => {
  GlobalState.set(`dataset.items.${item.id}`, item);
});

GlobalState.set('dataset.meta', {
  total: largeDataset.length,
  lastUpdated: Date.now()
});
```

### 延迟更新

```typescript
import { debounce } from 'lodash';

// 防抖更新
const debouncedUpdate = debounce((key, value) => {
  GlobalState.set(key, value);
}, 300);

// 在输入框中使用
const handleInputChange = (value) => {
  debouncedUpdate('search.query', value);
};
```

### 批量更新

```typescript
// 批量更新多个状态
GlobalState.batch(() => {
  GlobalState.set('user.profile.name', 'John Doe');
  GlobalState.set('user.profile.email', '<EMAIL>');
  GlobalState.set('user.profile.avatar', '/avatars/john.jpg');
  GlobalState.set('user.lastLogin', Date.now());
});

// 只会触发一次状态变化通知
```

## 调试和开发工具

### 状态调试

```typescript
// 开发环境下启用状态调试
if (process.env.NODE_ENV === 'development') {
  GlobalState.enableDebug({
    logStateChanges: true,
    logPerformance: true,
    trackHistory: true,
    maxHistorySize: 100
  });

  // 在控制台中查看状态历史
  console.log('状态历史:', GlobalState.getHistory());
  
  // 时间旅行调试
  GlobalState.timeTravel(-5); // 回到5步之前的状态
}
```

### 状态快照

```typescript
// 创建状态快照
const snapshot = GlobalState.createSnapshot();

// 恢复到快照状态
GlobalState.restoreSnapshot(snapshot);

// 比较两个快照的差异
const diff = GlobalState.compareSnapshots(snapshot1, snapshot2);
console.log('状态差异:', diff);
```

## 最佳实践

### 1. 状态结构设计

```typescript
// 推荐的状态结构
const stateStructure = {
  // 用户相关状态
  user: {
    profile: { id: '', name: '', email: '' },
    preferences: { theme: 'light', language: 'zh-CN' },
    permissions: ['read', 'write']
  },
  
  // 应用相关状态
  app: {
    loading: false,
    error: null,
    success: null,
    settings: { apiUrl: '', timeout: 5000 }
  },
  
  // 业务相关状态
  business: {
    cart: { items: [], total: 0 },
    orders: { list: [], current: null },
    products: { list: [], filters: {} }
  }
};
```

### 2. 状态更新模式

```typescript
// 使用不可变更新
const updateUserProfile = (updates) => {
  const currentProfile = GlobalState.get('user.profile');
  GlobalState.set('user.profile', {
    ...currentProfile,
    ...updates,
    updatedAt: Date.now()
  });
};

// 使用深度合并
GlobalState.merge('user.preferences', {
  theme: 'dark',
  notifications: { email: true, push: false }
});
```

### 3. 错误处理

```typescript
// 统一的错误处理
const handleStateError = (error, context) => {
  console.error(`状态操作失败 [${context}]:`, error);
  
  GlobalState.set('app.error', {
    message: error.message,
    context,
    timestamp: Date.now()
  });
};

// 在状态操作中使用
try {
  GlobalState.set('user.profile', profileData);
} catch (error) {
  handleStateError(error, 'updateUserProfile');
}
```
