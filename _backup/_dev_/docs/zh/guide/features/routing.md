# 路由系统

Micro-Core 提供了强大而灵活的路由系统，支持多种路由模式和高级特性，能够协调主应用和子应用的路由状态。

## 📋 目录

- [路由架构概览](#路由架构概览)
- [路由模式](#路由模式)
- [路由配置](#路由配置)
- [路由匹配](#路由匹配)
- [路由守卫](#路由守卫)
- [嵌套路由](#嵌套路由)
- [路由同步](#路由同步)
- [路由缓存](#路由缓存)
- [路由预加载](#路由预加载)
- [路由动画](#路由动画)
- [性能优化](#性能优化)
- [调试工具](#调试工具)

## 路由架构概览

### 整体架构图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 路由系统架构                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   主应用路由层                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 路由管理器   │  │ 路由守卫     │  │ 路由缓存         │ │   │
│  │  │ • 路由注册   │  │ • 权限检查   │  │ • 智能缓存       │ │   │
│  │  │ • 路由匹配   │  │ • 导航拦截   │  │ • 预加载策略     │ │   │
│  │  │ • 路由跳转   │  │ • 错误处理   │  │ • 性能优化       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   路由协调层                             │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 路由同步     │  │ 状态管理     │  │ 事件通信         │ │   │
│  │  │ • 主子同步   │  │ • 路由状态   │  │ • 路由事件       │ │   │
│  │  │ • 历史管理   │  │ • 参数传递   │  │ • 生命周期       │ │   │
│  │  │ • 导航控制   │  │ • 元信息     │  │ • 错误通知       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   子应用路由层                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ React Router│  │ Vue Router  │  │ Angular Router  │ │   │
│  │  │ • 组件路由   │  │ • 页面路由   │  │ • 模块路由       │ │   │
│  │  │ • 嵌套路由   │  │ • 动态路由   │  │ • 懒加载路由     │ │   │
│  │  │ • 路由守卫   │  │ • 路由元信息 │  │ • 路由解析器     │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 路由生命周期流程

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由导航生命周期                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 1. 导航触发  │───▶│ 2. 路由解析  │───▶│ 3. 路由匹配         │ │
│  │ • 用户点击   │    │ • URL 解析  │    │ • 规则匹配          │ │
│  │ • 编程导航   │    │ • 参数提取   │    │ • 应用选择          │ │
│  │ • 浏览器导航 │    │ • 查询解析   │    │ • 容器分配          │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
│                                                   │             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 6. 导航完成  │◀───│ 5. 应用挂载  │◀───│ 4. 路由守卫         │ │
│  │ • 状态更新   │    │ • 应用加载   │    │ • 权限检查          │ │
│  │ • 事件通知   │    │ • 组件渲染   │    │ • 前置守卫          │ │
│  │ • 历史记录   │    │ • 生命周期   │    │ • 后置守卫          │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 路由模式

Micro-Core 支持三种路由模式，每种模式都有其特定的使用场景：

::: code-group

```typescript [History 模式]
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  router: {
    mode: 'history',        // HTML5 History API
    base: '/',              // 基础路径
    fallback: true,         // 降级到 hash 模式
    scrollBehavior: 'smooth' // 滚动行为
  }
})

// 特点：
// ✅ URL 美观，无 # 符号
// ✅ 支持服务端渲染
// ❌ 需要服务器配置支持
// ❌ IE9 以下不支持
```

```typescript [Hash 模式]
const microCore = new MicroCore({
  router: {
    mode: 'hash',           // URL Hash
    base: '/',
    hashPrefix: '#!'        // 自定义 hash 前缀
  }
})

// 特点：
// ✅ 兼容性好，支持所有浏览器
// ✅ 无需服务器配置
// ❌ URL 包含 # 符号
// ❌ SEO 不友好
```

```typescript [Memory 模式]
const microCore = new MicroCore({
  router: {
    mode: 'memory',         // 内存路由
    initialEntries: ['/dashboard'], // 初始路由
    initialIndex: 0         // 初始索引
  }
})

// 特点：
// ✅ 适用于 SSR 和测试
// ✅ 不依赖浏览器 API
// ❌ 无法直接访问 URL
// ❌ 刷新会丢失状态
```

:::

## 路由配置

### 配置层次结构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由配置层次结构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   全局路由配置                           │   │
│  │                                                         │   │
│  │  • 路由模式 (mode)                                      │   │
│  │  • 基础路径 (base)                                      │   │
│  │  • 滚动行为 (scrollBehavior)                           │   │
│  │  • 链接样式 (linkActiveClass)                          │   │
│  │  • 错误处理 (onError)                                  │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   应用路由配置                           │   │
│  │                                                         │   │
│  │  • 激活规则 (activeWhen)                               │   │
│  │  • 路由守卫 (beforeEnter/beforeLeave)                  │   │
│  │  • 路由元信息 (meta)                                   │   │
│  │  • 缓存配置 (cache)                                    │   │
│  │  • 预加载配置 (preload)                                │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   组件路由配置                           │   │
│  │                                                         │   │
│  │  • 路由路径 (path)                                     │   │
│  │  • 路由组件 (component)                                │   │
│  │  • 子路由 (children)                                   │   │
│  │  • 路由参数 (params)                                   │   │
│  │  • 查询参数 (query)                                    │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 基础配置

::: code-group

```typescript [基础配置]
import { RouterPlugin } from '@micro-core/plugins';

const routerPlugin = new RouterPlugin({
  // 路由模式
  mode: 'history',        // 'hash' | 'history' | 'memory'
  
  // 基础路径
  base: '/',
  
  // 激活链接的CSS类
  linkActiveClass: 'router-link-active',
  
  // 精确激活链接的CSS类
  linkExactActiveClass: 'router-link-exact-active',
  
  // 滚动行为
  scrollBehavior: 'smooth'  // 'auto' | 'smooth' | function
});

// 注册路由插件
microCore.use(routerPlugin);
```

```typescript [高级配置]
const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/micro-app',
  
  // 路由表
  routes: [
    {
      path: '/users/*',
      app: 'user-management',
      meta: {
        title: '用户管理',
        requiresAuth: true,
        permissions: ['user:read']
      }
    },
    {
      path: '/orders/*',
      app: 'order-system',
      meta: {
        title: '订单系统',
        requiresAuth: true,
        permissions: ['order:read']
      }
    }
  ],
  
  // 路由守卫
  beforeEach: (to, from, next) => {
    // 全局前置守卫逻辑
    if (to.meta?.requiresAuth && !isAuthenticated()) {
      next('/login');
    } else {
      next();
    }
  },
  
  afterEach: (to, from) => {
    // 全局后置守卫逻辑
    document.title = to.meta?.title || 'Micro App';
    
    // 页面访问统计
    analytics.track('page_view', {
      path: to.path,
      from: from.path
    });
  },
  
  // 错误处理
  onError: (error) => {
    console.error('路由错误:', error);
    // 错误上报
    errorReporter.report(error);
  }
});
```

```typescript [企业级配置]
const enterpriseRouterConfig = {
  mode: 'history',
  base: '/enterprise',
  
  // 安全配置
  security: {
    // CSRF 保护
    csrfProtection: true,
    
    // 路由加密
    encryption: {
      enabled: true,
      algorithm: 'AES-256-GCM'
    },
    
    // 访问控制
    accessControl: {
      enabled: true,
      whitelist: ['/public/*'],
      blacklist: ['/admin/dangerous/*']
    }
  },
  
  // 性能配置
  performance: {
    // 预加载策略
    preload: {
      strategy: 'predictive',
      threshold: 0.7
    },
    
    // 缓存配置
    cache: {
      enabled: true,
      strategy: 'lru',
      maxSize: 100,
      ttl: 300000 // 5分钟
    },
    
    // 懒加载配置
    lazyLoad: {
      enabled: true,
      chunkSize: 'optimal',
      priority: 'high'
    }
  },
  
  // 监控配置
  monitoring: {
    // 性能监控
    performance: true,
    
    // 错误监控
    errorTracking: true,
    
    // 用户行为分析
    analytics: {
      provider: 'google-analytics',
      trackingId: 'GA_TRACKING_ID'
    }
  }
};
```

:::

## 路由匹配

### 匹配规则优先级

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由匹配优先级图                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   高优先级                               │   │
│  │                                                         │   │
│  │  1. 精确匹配 (Exact Match)                              │   │
│  │     /users/profile  →  /users/profile                  │   │
│  │                                                         │   │
│  │  2. 参数匹配 (Parameter Match)                          │   │
│  │     /users/:id      →  /users/123                      │   │
│  │                                                         │   │
│  │  3. 静态前缀匹配 (Static Prefix)                        │   │
│  │     /users/admin    →  /users/admin/settings           │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   中优先级                               │   │
│  │                                                         │   │
│  │  4. 动态前缀匹配 (Dynamic Prefix)                       │   │
│  │     /users/*        →  /users/anything                 │   │
│  │                                                         │   │
│  │  5. 正则表达式匹配 (Regex Match)                        │   │
│  │     /^\/api\/v\d+/  →  /api/v1, /api/v2               │   │
│  │                                                         │   │
│  │  6. 函数匹配 (Function Match)                           │   │
│  │     (location) => boolean                               │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   低优先级                               │   │
│  │                                                         │   │
│  │  7. 通配符匹配 (Wildcard Match)                         │   │
│  │     /**             →  任何路径                         │   │
│  │                                                         │   │
│  │  8. 默认匹配 (Default Match)                            │   │
│  │     fallback route  →  404 页面                        │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 路径匹配规则

::: code-group

```typescript [基础匹配]
// 精确匹配
activeWhen: '/users'

// 前缀匹配
activeWhen: '/users/*'

// 多路径匹配
activeWhen: ['/users', '/user-profile', '/user-settings']

// 正则表达式匹配
activeWhen: /^\/users?(\/.*)?$/

// 参数匹配
activeWhen: '/users/:id'
activeWhen: '/users/:id/orders/:orderId'

// 可选参数
activeWhen: '/users/:id?'

// 通配符匹配
activeWhen: '/admin/*'
```

```typescript [高级匹配]
// 命名参数匹配
activeWhen: '/users/:userId(\\d+)' // 只匹配数字

// 可选参数组
activeWhen: '/posts/:year?/:month?/:day?'

// 重复参数
activeWhen: '/files/*filepath' // 匹配多级路径

// 查询参数匹配
activeWhen: (location) => {
  const params = new URLSearchParams(location.search)
  return location.pathname === '/search' && params.has('q')
}

// 条件匹配
activeWhen: (location) => {
  const isAdminPath = location.pathname.startsWith('/admin')
  const hasPermission = checkAdminPermission()
  return isAdminPath && hasPermission
}
```

```typescript [复杂匹配场景]
// 时间条件匹配
activeWhen: (location) => {
  const now = new Date()
  const isBusinessHours = now.getHours() >= 9 && now.getHours() <= 18
  const isWorkingDay = now.getDay() >= 1 && now.getDay() <= 5
  
  return location.pathname.startsWith('/business') && 
         isBusinessHours && 
         isWorkingDay
}

// 用户角色匹配
activeWhen: (location) => {
  const userRole = getCurrentUserRole()
  const requiredRoles = ['admin', 'manager']
  
  return location.pathname.startsWith('/management') &&
         requiredRoles.includes(userRole)
}

// 设备类型匹配
activeWhen: (location) => {
  const isMobile = /Mobile|Android|iPhone|iPad/.test(navigator.userAgent)
  
  return location.pathname.startsWith('/mobile') && isMobile
}
```

:::

## 路由守卫

### 守卫执行时序图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由守卫执行时序                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  导航触发                                                       │
│      │                                                         │
│      ▼                                                         │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 1. beforeEach (全局前置守卫)                            │   │
│  │    • 身份验证检查                                       │   │
│  │    • 权限验证                                           │   │
│  │    • 全局拦截逻辑                                       │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 2. beforeEnter (路由级守卫)                             │   │
│  │    • 路由特定检查                                       │   │
│  │    • 数据预加载                                         │   │
│  │    • 条件验证                                           │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 3. beforeRouteEnter (组件内守卫)                        │   │
│  │    • 组件实例创建前                                     │   │
│  │    • 无法访问 this                                     │   │
│  │    • 数据获取准备                                       │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 4. beforeResolve (全局解析守卫)                         │   │
│  │    • 异步组件解析完成                                   │   │
│  │    • 最终确认导航                                       │   │
│  │    • 资源准备就绪                                       │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 5. afterEach (全局后置守卫)                             │   │
│  │    • 导航完成后执行                                     │   │
│  │    • 页面统计分析                                       │   │
│  │    • 标题更新等                                         │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  导航完成                                                       │
└─────────────────────────────────────────────────────────────────┘
```

### 全局守卫

::: code-group

```typescript [全局前置守卫]
// 全局前置守卫
microCore.router.beforeEach((to, from, next) => {
  console.log(`导航到: ${to.path}`);
  
  // 权限检查
  if (to.meta?.requiresAuth) {
    const isAuthenticated = checkAuthentication();
    if (!isAuthenticated) {
      next('/login');
      return;
    }
  }
  
  // 权限检查
  if (to.meta?.permissions) {
    const hasPermission = checkPermissions(to.meta.permissions);
    if (!hasPermission) {
      next('/403');
      return;
    }
  }
  
  // 继续导航
  next();
});
```

```typescript [全局解析守卫]
// 全局解析守卫
microCore.router.beforeResolve((to, from, next) => {
  // 在导航被确认之前，同时在所有组件内守卫和异步路由组件被解析之后调用
  console.log('路由解析完成');
  
  // 预加载关键资源
  if (to.meta?.preloadResources) {
    Promise.all(to.meta.preloadResources.map(loadResource))
      .then(() => next())
      .catch(() => next());
  } else {
    next();
  }
});
```

```typescript [全局后置守卫]
// 全局后置守卫
microCore.router.afterEach((to, from) => {
  // 导航完成后调用
  console.log(`从 ${from.path} 导航到 ${to.path}`);
  
  // 更新页面标题
  document.title = to.meta?.title || 'Default Title';
  
  // 页面访问统计
  analytics.track('page_view', {
    path: to.path,
    title: to.meta?.title
  });
  
  // 滚动到顶部
  window.scrollTo(0, 0);
  
  // 更新面包屑导航
  updateBreadcrumb(to.meta?.breadcrumb);
});
```

```typescript [错误处理守卫]
// 路由错误处理
microCore.router.onError((error) => {
  console.error('路由错误:', error);
  
  // 错误分类处理
  if (error.name === 'ChunkLoadError') {
    // 代码分割加载失败
    window.location.reload();
  } else if (error.name === 'NavigationDuplicated') {
    // 重复导航错误，可以忽略
    console.warn('重复导航:', error.message);
  } else {
    // 其他错误，显示错误页面
    microCore.router.push('/error');
  }
  
  // 错误上报
  errorReporter.report(error);
});
```

:::

## 路由同步

### 主子应用路由同步架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由同步架构图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   主应用路由                             │   │
│  │                                                         │   │
│  │  URL: /workspace/projects/123                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │   │
│  │  │ 路由监听器   │───▶│ 事件分发器   │───▶│ 状态同步器   │ │   │
│  │  │ • URL 变化   │    │ • 路由事件   │    │ • 全局状态   │ │   │
│  │  │ • 历史管理   │    │ • 参数传递   │    │ • 参数同步   │ │   │
│  │  └─────────────┘    └─────────────┘    └─────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   路由同步总线                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 事件总线     │  │ 状态管理     │  │ 消息队列         │ │   │
│  │  │ • 路由事件   │  │ • 路由状态   │  │ • 异步同步       │ │   │
│  │  │ • 生命周期   │  │ • 参数状态   │  │ • 批量处理       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   子应用路由                             │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 路由接收器   │  │ 路由适配器   │  │ 本地路由器       │ │   │
│  │  │ • 事件监听   │  │ • 路径转换   │  │ • 内部导航       │ │   │
│  │  │ • 状态接收   │  │ • 参数映射   │  │ • 组件渲染       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 主子应用路由同步

::: code-group

```typescript [主应用路由同步]
// 主应用监听路由变化
microCore.router.afterEach((to, from) => {
  // 通知子应用路由变化
  microCore.eventBus.emit('route:change', {
    to: to.path,
    from: from.path,
    query: to.query,
    params: to.params,
    meta: to.meta
  });
  
  // 更新全局路由状态
  microCore.globalState.set('currentRoute', {
    path: to.path,
    fullPath: to.fullPath,
    params: to.params,
    query: to.query,
    meta: to.meta
  });
});

// 监听子应用路由请求
microCore.eventBus.on('child:route:request', (data) => {
  const { appName, path, replace = false } = data;
  
  if (replace) {
    microCore.router.replace(path);
  } else {
    microCore.router.push(path);
  }
});
```

```typescript [子应用路由同步]
// 子应用监听路由变化
microCore.eventBus.on('route:change', (routeInfo) => {
  // 同步子应用路由
  if (routeInfo.to.startsWith('/user')) {
    const subPath = routeInfo.to.replace('/user', '');
    userAppRouter.push(subPath || '/');
  }
});

// 子应用请求路由变化
function navigateToParentRoute(path: string) {
  microCore.eventBus.emit('child:route:request', {
    appName: 'user-app',
    path: path,
    replace: false
  });
}

// React Hook 示例
import { useEffect } from 'react';
import { useHistory } from 'react-router-dom';

function useRouteSync() {
  const history = useHistory();
  
  useEffect(() => {
    const handleRouteChange = (routeInfo) => {
      if (routeInfo.to.startsWith('/user')) {
        const subPath = routeInfo.to.replace('/user', '');
        history.push(subPath || '/');
      }
    };
    
    microCore.eventBus.on('route:change', handleRouteChange);
    
    return () => {
      microCore.eventBus.off('route:change', handleRouteChange);
    };
  }, [history]);
}
```

```typescript [Vue 子应用路由同步]
// Vue 子应用路由同步
import { onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';

export function useRouteSync() {
  const router = useRouter();
  
  const handleRouteChange = (routeInfo) => {
    if (routeInfo.to.startsWith('/orders')) {
      const subPath = routeInfo.to.replace('/orders', '');
      router.push(subPath || '/');
    }
  };
  
  onMounted(() => {
    microCore.eventBus.on('route:change', handleRouteChange);
  });
  
  onUnmounted(() => {
    microCore.eventBus.off('route:change', handleRouteChange);
  });
  
  // 请求主应用路由变化
  const navigateToParent = (path: string) => {
    microCore.eventBus.emit('child:route:request', {
      appName: 'order-app',
      path: `/orders${path}`,
      replace: false
    });
  };
  
  return { navigateToParent };
}
```

:::

### 路由状态共享

```typescript
// 路由状态管理器
class RouteStateManager {
  private state = {
    currentRoute: null,
    routeHistory: [],
    navigationStack: [],
    breadcrumbs: []
  };
  
  private listeners = new Set();
  
  // 更新当前路由
  updateCurrentRoute(route: Route) {
    const previousRoute = this.state.currentRoute;
    this.state.currentRoute = route;
    
    // 更新历史记录
    this.updateHistory(route);
    
    // 更新面包屑
    this.updateBreadcrumbs(route);
    
    // 通知监听者
    this.notifyListeners('route:update', {
      current: route,
      previous: previousRoute
    });
  }
  
  // 更新历史记录
  private updateHistory(route: Route) {
    this.state.routeHistory.push({
      ...route,
      timestamp: Date.now()
    });
    
    // 限制历史记录数量
    if (this.state.routeHistory.length > 50) {
      this.state.routeHistory.shift();
    }
  }
  
  // 更新面包屑
  private updateBreadcrumbs(route: Route) {
    if (route.meta?.breadcrumb) {
      this.state.breadcrumbs = route.meta.breadcrumb;
    } else {
      // 自动生成面包屑
      this.state.breadcrumbs = this.generateBreadcrumbs(route.path);
    }
  }
  
  // 生成面包屑
  private generateBreadcrumbs(path: string): Breadcrumb[] {
    const segments = path.split('/').filter(Boolean);
    const breadcrumbs: Breadcrumb[] = [];
    
    let currentPath = '';
    for (const segment of segments) {
      currentPath += `/${segment}`;
      breadcrumbs.push({
        name: this.formatSegmentName(segment),
        path: currentPath
      });
    }
    
    return breadcrumbs;
  }
  
  // 格式化路径段名称
  private formatSegmentName(segment: string): string {
    // 处理参数路径
    if (segment.startsWith(':')) {
      return segment.slice(1);
    }
    
    // 转换为标题格式
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
  
  // 订阅状态变化
  subscribe(listener: Function) {
    this.listeners.add(listener);
    
    return () => {
      this.listeners.delete(listener);
    };
  }
  
  // 通知监听者
  private notifyListeners(event: string, data: any) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        console.error('路由状态监听器错误:', error);
      }
    });
  }
  
  // 获取当前状态
  getState() {
    return { ...this.state };
  }
}

// 全局路由状态管理器
const routeStateManager = new RouteStateManager();

// 集成到路由系统
microCore.router.afterEach((to, from) => {
  routeStateManager.updateCurrentRoute(to);
});
```

## 路由缓存

### 缓存策略架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由缓存策略架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   缓存决策层                             │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 缓存策略     │  │ 失效策略     │  │ 更新策略         │ │   │
│  │  │ • LRU       │  │ • TTL       │  │ • 主动更新       │ │   │
│  │  │ • LFU       │  │ • 版本控制   │  │ • 被动更新       │ │   │
│  │  │ • FIFO      │  │ • 依赖失效   │  │ • 增量更新       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   缓存存储层                             │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 内存缓存     │  │ 本地存储     │  │ 会话存储         │ │   │
│  │  │ • 快速访问   │  │ • 持久化     │  │ • 会话级别       │ │   │
│  │  │ • 容量限制   │  │ • 跨会话     │  │ • 自动清理       │ │   │
│  │  │ • 易失性     │  │ • 大容量     │  │ • 隐私保护       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   缓存应用层                             │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 路由缓存     │  │ 组件缓存     │  │ 数据缓存         │ │   │
│  │  │ • 路由配置   │  │ • 组件实例   │  │ • API 响应      │ │   │
│  │  │ • 路由状态   │  │ • 渲染结果   │  │ • 静态资源       │ │   │
│  │  │ • 导航历史   │  │ • 组件状态   │  │ • 用户数据       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 应用级缓存

::: code-group

```typescript [基础缓存配置]
// 应用级缓存配置
microCore.registerApp({
  name: 'user-management',
  entry: 'http://localhost:3001/index.js',
  container: '#user-container',
  activeWhen: '/users/*',
  
  // 缓存配置
  cache: {
    // 启用缓存
    enabled: true,
    
    // 缓存策略
    strategy: 'memory', // 'memory' | 'localStorage' | 'sessionStorage'
    
    // 缓存时间（毫秒）
    maxAge: 5 * 60 * 1000, // 5分钟
    
    // 缓存条件
    condition: (route) => {
      // 只缓存列表页面
      return route.path === '/users';
    },
    
    // 缓存键生成
    keyGenerator: (route) => {
      return `user-app-${route.path}-${JSON.stringify(route.query)}`;
    }
  }
});
```

```typescript [高级缓存策略]
// 智能缓存管理器
class SmartCacheManager {
  private caches = new Map();
  private accessCount = new Map();
  private lastAccess = new Map();
  private dependencies = new Map();
  
  // 设置缓存
  set(key: string, data: any, options: CacheOptions = {}) {
    const cacheItem = {
      data,
      timestamp: Date.now(),
      maxAge: options.maxAge || 300000, // 默认5分钟
      dependencies: options.dependencies || [],
      version: options.version || 1,
      metadata: options.metadata || {}
    };
    
    this.caches.set(key, cacheItem);
    this.accessCount.set(key, 1);
    this.lastAccess.set(key, Date.now());
    
    // 设置依赖关系
    if (options.dependencies) {
      options.dependencies.forEach(dep => {
        if (!this.dependencies.has(dep)) {
          this.dependencies.set(dep, new Set());
        }
        this.dependencies.get(dep).add(key);
      });
    }
    
    // 清理过期缓存
    this.cleanup();
  }
  
  // 获取缓存
  get(key: string) {
    const item = this.caches.get(key);
    
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.maxAge) {
      this.delete(key);
      return null;
    }
    
    // 更新访问统计
    this.accessCount.set(key, (this.accessCount.get(key) || 0) + 1);
    this.lastAccess.set(key, Date.now());
    
    return item.data;
  }
  
  // 删除缓存
  delete(key: string) {
    this.caches.delete(key);
    this.accessCount.delete(key);
    this.lastAccess.delete(key);
    
    // 清理依赖关系
    this.dependencies.forEach((deps, depKey) => {
      deps.delete(key);
      if (deps.size === 0) {
        this.dependencies.delete(depKey);
      }
    });
  }
  
  // 根据依赖失效缓存
  invalidateByDependency(dependency: string) {
    const dependentKeys = this.dependencies.get(dependency);
    if (dependentKeys) {
      dependentKeys.forEach(key => this.delete(key));
    }
  }
  
  // 清理过期缓存
  private cleanup() {
    const now = Date.now();
    const maxSize = 100; // 最大缓存数量
    
    // 删除过期项
    for (const [key, item] of this.caches) {
      if (now - item.timestamp > item.maxAge) {
        this.delete(key);
      }
    }
    
    // 如果缓存过多，使用 LRU 策略删除
    if (this.caches.size > maxSize) {
      const entries = Array.from(this.lastAccess.entries())
        .sort((a, b) => a[1] - b[1])
        .slice(0, this.caches.size - maxSize);
      
      entries.forEach(([key]) => this.delete(key));
    }
  }
  
  // 获取缓存统计
  getStats() {
    return {
      size: this.caches.size,
      hitRate: this.calculateHitRate(),
      memoryUsage: this.calculateMemoryUsage()
    };
  }
  
  private calculateHitRate(): number {
    // 计算缓存命中率的逻辑
    return 0.85; // 示例值
  }
  
  private calculateMemoryUsage(): number {
    // 计算内存使用量的逻辑
    return this.caches.size * 1024; // 示例值
  }
}

// 全局缓存管理器
const cacheManager = new SmartCacheManager();
```

```typescript [路由级缓存]
// 路由级缓存配置
microCore.router.addRoute({
  path: '/users',
  app: 'user-list',
  meta: {
    // 启用页面缓存
    keepAlive: true,
    
    // 缓存键
    cacheKey: (route) => `user-list-${route.query.page || 1}`,
    
    // 缓存条件
    cacheable: (route) => {
      // 只有在特定条件下才缓存
      return !route.query.refresh;
    },
    
    // 缓存依赖
    dependencies: ['user-data', 'user-permissions'],
    
    // 缓存配置
    cache: {
      maxAge: 10 * 60 * 1000, // 10分钟
      strategy: 'lru',
      maxSize: 20
    }
  }
});

// 缓存中间件
function createCacheMiddleware() {
  return (to: Route, from: Route, next: Function) => {
    const cacheKey = to.meta?.cacheKey?.(to) || to.path;
    
    // 检查是否可缓存
    if (to.meta?.cacheable?.(to) !== false) {
      const cachedData = cacheManager.get(cacheKey);
      
      if (cachedData) {
        // 使用缓存数据
        to.meta.cachedData = cachedData;
        console.log('使用缓存数据:', cacheKey);
      }
    }
    
    next();
  };
}

// 应用缓存中间件
microCore.router.beforeEach(createCacheMiddleware());
```

:::

## 路由预加载

### 预加载策略

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由预加载策略                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   预加载触发条件                         │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 空闲时预加载 │  │ 悬停预加载   │  │ 可视区域预加载   │ │   │
│  │  │ • CPU 空闲   │  │ • 鼠标悬停   │  │ • 元素可见       │ │   │
│  │  │ • 网络空闲   │  │ • 延迟触发   │  │ • 交叉观察       │ │   │
│  │  │ • 用户无操作 │  │ • 取消机制   │  │ • 阈值控制       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   预加载执行策略                         │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 优先级队列   │  │ 并发控制     │  │ 资源管理         │ │   │
│  │  │ • 高优先级   │  │ • 最大并发   │  │ • 内存限制       │ │   │
│  │  │ • 普通优先级 │  │ • 队列管理   │  │ • 网络带宽       │ │   │
│  │  │ • 低优先级   │  │ • 错误重试   │  │ • 存储空间       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   预加载效果评估                         │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 命中率统计   │  │ 性能提升     │  │ 资源消耗         │ │   │
│  │  │ • 预加载命中 │  │ • 加载时间   │  │ • CPU 使用      │ │   │
│  │  │ • 使用率     │  │ • 用户体验   │  │ • 内存占用       │ │   │
│  │  │ • 浪费率     │  │ • 响应速度   │  │ • 网络流量       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 智能预加载

::: code-group

```typescript [基础预加载]
// 智能预加载配置
microCore.registerApp({
  name: 'preload-app',
  entry: 'http://localhost:3007',
  container: '#preload-container',
  activeWhen: '/preload',
  preload: {
    enabled: true,
    strategy: 'hover',    // 'idle' | 'hover' | 'visible'
    delay: 200,           // 延迟时间
    priority: 'normal'    // 'high' | 'normal' | 'low'
  }
});

// 手动预加载
await microCore.preloadApp('user-app');

// 批量预加载
await Promise.all([
  microCore.preloadApp('user-app'),
  microCore.preloadApp('order-app')
]);
```

```typescript [高级预加载策略]
// 预加载管理器
class PreloadManager {
  private preloadQueue: PreloadTask[] = [];
  private activePreloads = new Set<string>();
  private preloadStats = new Map<string, PreloadStats>();
  private maxConcurrent = 3;
  
  // 添加预加载任务
  addTask(task: PreloadTask) {
    // 检查是否已在队列中
    if (this.preloadQueue.some(t => t.id === task.id)) {
      return;
    }
    
    // 根据优先级插入队列
    const insertIndex = this.findInsertIndex(task.priority);
    this.preloadQueue.splice(insertIndex, 0, task);
    
    // 尝试执行任务
    this.processQueue();
  }
  
  // 处理预加载队列
  private async processQueue() {
    while (
      this.preloadQueue.length > 0 && 
      this.activePreloads.size < this.maxConcurrent
    ) {
      const task = this.preloadQueue.shift();
      if (!task) break;
      
      this.activePreloads.add(task.id);
      
      try {
        await this.executeTask(task);
        this.recordSuccess(task);
      } catch (error) {
        this.recordFailure(task, error);
      } finally {
        this.activePreloads.delete(task.id);
      }
    }
  }
  
  // 执行预加载任务
  private async executeTask(task: PreloadTask) {
    const startTime = performance.now();
    
    switch (task.type) {
      case 'app':
        await this.preloadApp(task);
        break;
      case 'route':
        await this.preloadRoute(task);
        break;
      case 'resource':
        await this.preloadResource(task);
        break;
    }
    
    const endTime = performance.now();
    task.duration = endTime - startTime;
  }
  
  // 预加载应用
  private async preloadApp(task: PreloadTask) {
    const app = microCore.getApp(task.target);
    if (!app) throw new Error(`App ${task.target} not found`);
    
    // 预加载应用资源
    await microCore.loadAppResources(app.name);
    
    // 预编译模板
    if (task.options?.precompile) {
      await microCore.precompileApp(app.name);
    }
  }
  
  // 预加载路由
  private async preloadRoute(task: PreloadTask) {
    const route = microCore.router.resolve(task.target);
    
    // 预加载路由组件
    if (route.component) {
      await this.loadComponent(route.component);
    }
    
    // 预加载路由数据
    if (task.options?.preloadData) {
      await this.preloadRouteData(route);
    }
  }
  
  // 预加载资源
  private async preloadResource(task: PreloadTask) {
    const { url, type } = task.options;
    
    switch (type) {
      case 'script':
        await this.preloadScript(url);
        break;
      case 'style':
        await this.preloadStyle(url);
        break;
      case 'image':
        await this.preloadImage(url);
        break;
      case 'data':
        await this.preloadData(url);
        break;
    }
  }
  
  // 预加载脚本
  private preloadScript(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'script';
      link.href = url;
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to preload script: ${url}`));
      document.head.appendChild(link);
    });
  }
  
  // 记录成功统计
  private recordSuccess(task: PreloadTask) {
    const stats = this.getStats(task.id);
    stats.successCount++;
    stats.totalDuration += task.duration || 0;
    stats.lastSuccess = Date.now();
  }
  
  // 记录失败统计
  private recordFailure(task: PreloadTask, error: Error) {
    const stats = this.getStats(task.id);
    stats.failureCount++;
    stats.lastError = error.message;
    stats.lastFailure = Date.now();
  }
  
  // 获取统计信息
  private getStats(taskId: string): PreloadStats {
    if (!this.preloadStats.has(taskId)) {
      this.preloadStats.set(taskId, {
        successCount: 0,
        failureCount: 0,
        totalDuration: 0,
        lastSuccess: 0,
        lastFailure: 0,
        lastError: null
      });
    }
    return this.preloadStats.get(taskId)!;
  }
  
  // 获取预加载统计
  getPreloadStats() {
    const stats = Array.from(this.preloadStats.entries()).map(([id, stats]) => ({
      id,
      ...stats,
      successRate: stats.successCount / (stats.successCount + stats.failureCount),
      averageDuration: stats.totalDuration / stats.successCount
    }));
    
    return {
      tasks: stats,
      queueLength: this.preloadQueue.length,
      activeCount: this.activePreloads.size,
      totalSuccess: stats.reduce((sum, s) => sum + s.successCount, 0),
      totalFailure: stats.reduce((sum, s) => sum + s.failureCount, 0)
    };
  }
}

// 全局预加载管理器
const preloadManager = new PreloadManager();
```

```typescript [预测性预加载]
// 预测性预加载
class PredictivePreloader {
  private userBehavior = new Map<string, BehaviorPattern>();
  private transitionMatrix = new Map<string, Map<string, number>>();
  
  // 记录用户行为
  recordNavigation(from: string, to: string) {
    // 更新转移矩阵
    if (!this.transitionMatrix.has(from)) {
      this.transitionMatrix.set(from, new Map());
    }
    
    const transitions = this.transitionMatrix.get(from)!;
    transitions.set(to, (transitions.get(to) || 0) + 1);
    
    // 更新用户行为模式
    this.updateBehaviorPattern(from, to);
  }
  
  // 预测下一个可能访问的路由
  predictNextRoutes(currentRoute: string, limit = 3): string[] {
    const transitions = this.transitionMatrix.get(currentRoute);
    if (!transitions) return [];
    
    return Array.from(transitions.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([route]) => route);
  }
  
  // 触发预测性预加载
  triggerPredictivePreload(currentRoute: string) {
    const predictedRoutes = this.predictNextRoutes(currentRoute);
    
    predictedRoutes.forEach((route, index) => {
      preloadManager.addTask({
        id: `predictive-${route}`,
        type: 'route',
        target: route,
        priority: index === 0 ? 'high' : 'normal',
        options: { predictive: true }
      });
    });
  }
}

// 全局预测性预加载器
const predictivePreloader = new PredictivePreloader();

// 集成到路由系统
microCore.router.afterEach((to, from) => {
  if (from.path) {
    predictivePreloader.recordNavigation(from.path, to.path);
    predictivePreloader.triggerPredictivePreload(to.path);
  }
});
```

:::

## 路由动画

### 动画类型和配置

::: code-group

```css [基础过渡动画]
/* 路由切换动画 */
.route-enter-active,
.route-leave-active {
  transition: all 0.3s ease;
}

.route-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.route-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 滑动动画 */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: transform 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
}

.slide-left-leave-to {
  transform: translateX(-100%);
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 缩放动画 */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.scale-leave-to {
  opacity: 0;
  transform: scale(1.1);
}
```

```typescript [JavaScript 动画控制]
// 动画控制器
class RouteAnimationController {
  private animationQueue: AnimationTask[] = [];
  private currentAnimation: AnimationTask | null = null;
  
  // 添加路由动画
  addAnimation(from: Route, to: Route): Promise<void> {
    return new Promise((resolve) => {
      const task: AnimationTask = {
        id: `${from.path}-to-${to.path}`,
        from,
        to,
        resolve,
        type: this.determineAnimationType(from, to),
        duration: this.getAnimationDuration(from, to)
      };
      
      this.animationQueue.push(task);
      this.processQueue();
    });
  }
  
  // 确定动画类型
  private determineAnimationType(from: Route, to: Route): string {
    // 根据路由深度确定动画方向
    const fromDepth = from.path.split('/').length;
    const toDepth = to.path.split('/').length;
    
    if (toDepth > fromDepth) {
      return 'slide-left'; // 进入子页面
    } else if (toDepth < fromDepth) {
      return 'slide-right'; // 返回上级页面
    } else {
      return 'fade'; // 同级页面切换
    }
  }
  
  // 获取动画持续时间
  private getAnimationDuration(from: Route, to: Route): number {
    // 根据路由元信息或默认值确定动画时间
    return to.meta?.transition?.duration || 300;
  }
  
  // 处理动画队列
  private async processQueue() {
    if (this.currentAnimation || this.animationQueue.length === 0) {
      return;
    }
    
    const task = this.animationQueue.shift()!;
    this.currentAnimation = task;
    
    try {
      await this.executeAnimation(task);
      task.resolve();
    } catch (error) {
      console.error('路由动画执行失败:', error);
      task.resolve(); // 即使动画失败也要继续
    } finally {
      this.currentAnimation = null;
      this.processQueue(); // 处理下一个动画
    }
  }
  
  // 执行动画
  private async executeAnimation(task: AnimationTask): Promise<void> {
    const { from, to, type, duration } = task;
    
    // 获取容器元素
    const fromContainer = document.querySelector(`[data-app="${from.app}"]`);
    const toContainer = document.querySelector(`[data-app="${to.app}"]`);
    
    if (!fromContainer || !toContainer) {
      return;
    }
    
    // 设置动画类
    fromContainer.classList.add(`${type}-leave-active`);
    toContainer.classList.add(`${type}-enter-active`);
    
    // 触发动画
    await new Promise(resolve => {
      setTimeout(() => {
        fromContainer.classList.add(`${type}-leave-to`);
        toContainer.classList.add(`${type}-enter-from`);
        
        setTimeout(() => {
          // 清理动画类
          fromContainer.classList.remove(
            `${type}-leave-active`,
            `${type}-leave-to`
          );
          toContainer.classList.remove(
            `${type}-enter-active`,
            `${type}-enter-from`
          );
          
          resolve(undefined);
        }, duration);
      }, 16); // 下一帧
    });
  }
}

// 全局动画控制器
const animationController = new RouteAnimationController();

// 集成到路由系统
microCore.router.beforeResolve(async (to, from, next) => {
  if (from.path && to.path !== from.path) {
    await animationController.addAnimation(from, to);
  }
  next();
});
```

```typescript [高级动画配置]
// 高级动画配置
const advancedAnimationConfig = {
  // 动画映射
  animations: {
    'dashboard-to-users': {
      type: 'slide-left',
      duration: 400,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    },
    'users-to-dashboard': {
      type: 'slide-right',
      duration: 400,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    },
    'default': {
      type: 'fade',
      duration: 300,
      easing: 'ease'
    }
  },
  
  // 动画条件
  conditions: {
    // 移动端使用更快的动画
    mobile: {
      duration: 200,
      type: 'fade'
    },
    
    // 低性能设备禁用动画
    lowPerformance: {
      duration: 0,
      type: 'none'
    }
  },
  
  // 性能监控
  performance: {
    enabled: true,
    maxFrameTime: 16, // 60fps
    fallbackAnimation: 'fade'
  }
};

// 应用高级动画配置
microCore.router.beforeEach((to, from, next) => {
  // 检查设备性能
  if (isLowPerformanceDevice()) {
    to.meta.transition = advancedAnimationConfig.conditions.lowPerformance;
  } else if (isMobileDevice()) {
    to.meta.transition = advancedAnimationConfig.conditions.mobile;
  }
  
  next();
});
```

:::

## 性能优化

### 性能监控和优化策略

```typescript
// 路由性能监控器
class RoutePerformanceMonitor {
  private metrics = new Map<string, PerformanceMetrics>();
  private thresholds = {
    navigationTime: 1000,    // 导航时间阈值 1秒
    renderTime: 500,         // 渲染时间阈值 500ms
    memoryUsage: 50 * 1024 * 1024, // 内存使用阈值 50MB
  };
  
  // 开始监控导航
  startNavigation(route: string) {
    const metrics: PerformanceMetrics = {
      route,
      startTime: performance.now(),
      navigationTime: 0,
      renderTime: 0,
      memoryUsage: this.getMemoryUsage(),
      cacheHit: false,
      errors: []
    };
    
    this.metrics.set(route, metrics);
  }
  
  // 结束导航监控
  endNavigation(route: string, success: boolean = true) {
    const metrics = this.metrics.get(route);
    if (!metrics) return;
    
    metrics.navigationTime = performance.now() - metrics.startTime;
    metrics.success = success;
    
    // 检查性能阈值
    this.checkThresholds(metrics);
    
    // 记录到历史
    this.recordMetrics(metrics);
  }
  
  // 检查性能阈值
  private checkThresholds(metrics: PerformanceMetrics) {
    const warnings: string[] = [];
    
    if (metrics.navigationTime > this.thresholds.navigationTime) {
      warnings.push(`导航时间过长: ${metrics.navigationTime}ms`);
    }
    
    if (metrics.renderTime > this.thresholds.renderTime) {
      warnings.push(`渲染时间过长: ${metrics.renderTime}ms`);
    }
    
    if (metrics.memoryUsage > this.thresholds.memoryUsage) {
      warnings.push(`内存使用过高: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
    }
    
    if (warnings.length > 0) {
      console.warn(`路由性能警告 [${metrics.route}]:`, warnings);
      
      // 触发性能优化
      this.triggerOptimization(metrics);
    }
  }
  
  // 触发性能优化
  private triggerOptimization(metrics: PerformanceMetrics) {
    // 清理不必要的缓存
    if (metrics.memoryUsage > this.thresholds.memoryUsage) {
      cacheManager.cleanup();
    }
    
    // 预加载优化
    if (metrics.navigationTime > this.thresholds.navigationTime) {
      preloadManager.adjustStrategy(metrics.route, 'aggressive');
    }
  }
  
  // 获取内存使用情况
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }
  
  // 生成性能报告
  generateReport(): PerformanceReport {
    const allMetrics = Array.from(this.metrics.values());
    
    return {
      totalNavigations: allMetrics.length,
      averageNavigationTime: this.calculateAverage(allMetrics, 'navigationTime'),
      averageRenderTime: this.calculateAverage(allMetrics, 'renderTime'),
      cacheHitRate: allMetrics.filter(m => m.cacheHit).length / allMetrics.length,
      errorRate: allMetrics.filter(m => !m.success).length / allMetrics.length,
      slowRoutes: allMetrics
        .filter(m => m.navigationTime > this.thresholds.navigationTime)
        .sort((a, b) => b.navigationTime - a.navigationTime)
        .slice(0, 10),
      recommendations: this.generateRecommendations(allMetrics)
    };
  }
  
  // 生成优化建议
  private generateRecommendations(metrics: PerformanceMetrics[]): string[] {
    const recommendations: string[] = [];
    
    const slowRoutes = metrics.filter(m => 
      m.navigationTime > this.thresholds.navigationTime
    );
    
    if (slowRoutes.length > 0) {
      recommendations.push('考虑为慢速路由启用预加载');
      recommendations.push('优化慢速路由的资源加载');
    }
    
    const lowCacheHitRate = metrics.filter(m => m.cacheHit).length / metrics.length < 0.5;
    if (lowCacheHitRate) {
      recommendations.push('调整缓存策略以提高命中率');
    }
    
    return recommendations;
  }
}

// 全局性能监控器
const performanceMonitor = new RoutePerformanceMonitor();

// 集成到路由系统
microCore.router.beforeEach((to, from, next) => {
  performanceMonitor.startNavigation(to.path);
  next();
});

microCore.router.afterEach((to, from) => {
  performanceMonitor.endNavigation(to.path, true);
});

microCore.router.onError((error) => {
  const currentRoute = microCore.router.currentRoute?.path;
  if (currentRoute) {
    performanceMonitor.endNavigation(currentRoute, false);
  }
});
```

## 调试工具

### 路由调试面板

```typescript
// 路由调试工具
class RouteDebugger {
  private enabled = process.env.NODE_ENV === 'development';
  private logs: RouteLog[] = [];
  private maxLogs = 100;
  
  constructor() {
    if (this.enabled) {
      this.setupDebugger();
      this.createDebugPanel();
    }
  }
  
  private setupDebugger() {
    // 监听所有路由事件
    microCore.router.beforeEach((to, from, next) => {
      this.addLog({
        type: 'navigation-start',
        from: from.path,
        to: to.path,
        timestamp: Date.now(),
        params: to.params,
        query: to.query
      });
      
      console.group(`🧭 路由导航: ${from.path} → ${to.path}`);
      console.log('目标路由:', to);
      console.log('来源路由:', from);
      console.log('参数:', to.params);
      console.log('查询:', to.query);
      
      next();
    });
    
    microCore.router.afterEach((to, from) => {
      this.addLog({
        type: 'navigation-end',
        from: from.path,
        to: to.path,
        timestamp: Date.now()
      });
      
      console.log('✅ 导航完成');
      console.groupEnd();
    });
    
    microCore.router.onError((error) => {
      this.addLog({
        type: 'navigation-error',
        error: error.message,
        timestamp: Date.now()
      });
      
      console.error('❌ 路由错误:', error);
      console.groupEnd();
    });
  }
  
  private createDebugPanel() {
    // 创建调试面板 DOM
    const panel = document.createElement('div');
    panel.id = 'micro-core-debug-panel';
    panel.innerHTML = `
      <div class="debug-header">
        <h3>Micro-Core 路由调试</h3>
        <button id="toggle-debug">收起</button>
      </div>
      <div class="debug-content">
        <div class="debug-section">
          <h4>当前路由</h4>
          <div id="current-route"></div>
        </div>
        <div class="debug-section">
          <h4>路由历史</h4>
          <div id="route-history"></div>
        </div>
        <div class="debug-section">
          <h4>性能指标</h4>
          <div id="performance-metrics"></div>
        </div>
      </div>
    `;
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      #micro-core-debug-panel {
        position: fixed;
        top: 10px;
        right: 10px;
        width: 400px;
        background: #1a1a1a;
        color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 12px;
        z-index: 10000;
      }
      
      .debug-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background: #2d2d2d;
        border-radius: 8px 8px 0 0;
      }
      
      .debug-content {
        padding: 15px;
        max-height: 500px;
        overflow-y: auto;
      }
      
      .debug-section {
        margin-bottom: 15px;
      }
      
      .debug-section h4 {
        margin: 0 0 8px 0;
        color: #4CAF50;
      }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(panel);
    
    // 绑定事件
    this.bindDebugEvents();
    
    // 定期更新面板
    setInterval(() => this.updateDebugPanel(), 1000);
  }
  
  private updateDebugPanel() {
    const currentRoute = microCore.router.currentRoute;
    if (currentRoute) {
      const currentRouteEl = document.getElementById('current-route');
      if (currentRouteEl) {
        currentRouteEl.innerHTML = `
          <div><strong>路径:</strong> ${currentRoute.path}</div>
          <div><strong>参数:</strong> ${JSON.stringify(currentRoute.params)}</div>
          <div><strong>查询:</strong> ${JSON.stringify(currentRoute.query)}</div>
        `;
      }
    }
    
    // 更新路由历史
    const historyEl = document.getElementById('route-history');
    if (historyEl) {
      const recentLogs = this.logs.slice(-10).reverse();
      historyEl.innerHTML = recentLogs.map(log => `
        <div class="log-entry">
          <span class="log-time">${new Date(log.timestamp).toLocaleTimeString()}</span>
          <span class="log-type">${log.type}</span>
          <span class="log-details">${log.from || ''} → ${log.to || ''}</span>
        </div>
      `).join('');
    }
    
    // 更新性能指标
    const metricsEl = document.getElementById('performance-metrics');
    if (metricsEl) {
      const report = performanceMonitor.generateReport();
      metricsEl.innerHTML = `
        <div><strong>平均导航时间:</strong> ${report.averageNavigationTime.toFixed(2)}ms</div>
        <div><strong>缓存命中率:</strong> ${(report.cacheHitRate * 100).toFixed(1)}%</div>
        <div><strong>错误率:</strong> ${(report.errorRate * 100).toFixed(1)}%</div>
      `;
    }
  }
  
  private addLog(log: RouteLog) {
    this.logs.push(log);
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }
  }
  
  // 获取调试信息
  getDebugInfo() {
    return {
      logs: this.logs,
      currentRoute: microCore.router.currentRoute,
      registeredRoutes: microCore.router.getRoutes(),
      performance: performanceMonitor.generateReport(),
      cache: cacheManager.getStats(),
      preload: preloadManager.getPreloadStats()
    };
  }
}

// 开发环境启用调试器
if (process.env.NODE_ENV === 'development') {
  const debugger = new RouteDebugger();
  
  // 暴露到全局
  window.__MICRO_CORE_ROUTER_DEBUG__ = debugger;
  
  console.log('🔧 Micro-Core 路由调试器已启用');
  console.log('使用 __MICRO_CORE_ROUTER_DEBUG__.getDebugInfo() 获取调试信息');
}
```

## 最佳实践

### 路由设计原则

1. **RESTful 设计**: 遵循 RESTful API 设计原则
2. **语义化路径**: 路径应该清晰表达页面功能  
3. **层级结构**: 合理的路由层级结构
4. **参数设计**: 合理使用路径参数和查询参数
5. **向后兼容**: 保持路由的向后兼容性

### 性能优化建议

```typescript
// 路由性能优化最佳实践
const performanceOptimizations = {
  // 1. 路由懒加载
  lazyLoading: {
    // 动态导入组件
    component: () => import('./HeavyComponent.vue'),
    
    // 分组加载
    webpackChunkName: 'user-pages'
  },
  
  // 2. 预加载策略
  preloading: {
    // 预加载关键路由
    critical: ['/dashboard', '/profile'],
    
    // 智能预加载
    predictive: true,
    
    // 网络感知预加载
    networkAware: true
  },
  
  // 3. 缓存优化
  caching: {
    // 路由级缓存
    routeCache: true,
    
    // 组件缓存
    componentCache: true,
    
    // 数据缓存
    dataCache: {
      enabled: true,
      strategy: 'stale-while-revalidate'
    }
  },
  
  // 4. 代码分割
  codeSplitting: {
    // 按路由分割
    byRoute: true,
    
    // 按功能分割
    byFeature: true,
    
    // 共享依赖提取
    sharedDependencies: ['vue', 'vue-router', 'axios']
  }
};
```

### 错误处理策略

```typescript
// 路由错误处理最佳实践
const errorHandlingStrategies = {
  // 1. 分类错误处理
  errorTypes: {
    // 网络错误
    network: {
      retry: true,
      maxRetries: 3,
      fallback: '/offline'
    },
    
    // 权限错误
    permission: {
      redirect: '/403',
      message: '您没有访问权限'
    },
    
    // 资源不存在
    notFound: {
      redirect: '/404',
      message: '页面不存在'
    }
  },
  
  // 2. 错误恢复机制
  recovery: {
    // 自动重试
    autoRetry: true,
    
    // 降级处理
    fallback: true,
    
    // 用户反馈
    userFeedback: true
  },
  
  // 3. 错误监控
  monitoring: {
    // 错误上报
    reporting: true,
    
    // 性能监控
    performance: true,
    
    // 用户行为分析
    analytics: true
  }
};
```

## 总结

Micro-Core 的路由系统提供了完整的微前端路由解决方案：

- **🏗️ 灵活架构**: 支持多种路由模式和配置方式
- **🛡️ 强大守卫**: 完善的权限控制和导航守卫机制  
- **🔄 智能同步**: 主子应用路由状态同步
- **⚡ 性能优化**: 内置缓存、预加载等优化策略
- **🎨 动画支持**: 丰富的路由切换动画效果
- **🔧 调试工具**: 完善的开发调试和性能监控工具

通过合理使用这些功能，您可以构建出用户体验良好、性能优秀的微前端路由系统。

接下来，您可以学习 [应用间通信](./communication.md) 了解如何实现微应用间的数据交换。
# 路由系统

Micro-Core 提供了强大而灵活的路由系统，支持多种路由模式和高级特性，能够协调主应用和子应用的路由状态。

## 📋 目录

- [路由架构概览](#路由架构概览)
- [路由模式](#路由模式)
- [路由配置](#路由配置)
- [路由匹配](#路由匹配)
- [路由守卫](#路由守卫)
- [嵌套路由](#嵌套路由)
- [路由同步](#路由同步)
- [路由缓存](#路由缓存)
- [路由预加载](#路由预加载)
- [路由动画](#路由动画)
- [性能优化](#性能优化)
- [调试工具](#调试工具)

## 路由架构概览

### 整体架构图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 路由系统架构                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   主应用路由层                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 路由管理器   │  │ 路由守卫     │  │ 路由缓存         │ │   │
│  │  │ • 路由注册   │  │ • 权限检查   │  │ • 智能缓存       │ │   │
│  │  │ • 路由匹配   │  │ • 导航拦截   │  │ • 预加载策略     │ │   │
│  │  │ • 路由跳转   │  │ • 错误处理   │  │ • 性能优化       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   路由协调层                             │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 路由同步     │  │ 状态管理     │  │ 事件通信         │ │   │
│  │  │ • 主子同步   │  │ • 路由状态   │  │ • 路由事件       │ │   │
│  │  │ • 历史管理   │  │ • 参数传递   │  │ • 生命周期       │ │   │
│  │  │ • 导航控制   │  │ • 元信息     │  │ • 错误通知       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   子应用路由层                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ React Router│  │ Vue Router  │  │ Angular Router  │ │   │
│  │  │ • 组件路由   │  │ • 页面路由   │  │ • 模块路由       │ │   │
│  │  │ • 嵌套路由   │  │ • 动态路由   │  │ • 懒加载路由     │ │   │
│  │  │ • 路由守卫   │  │ • 路由元信息 │  │ • 路由解析器     │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 路由生命周期流程

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由导航生命周期                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 1. 导航触发  │───▶│ 2. 路由解析  │───▶│ 3. 路由匹配         │ │
│  │ • 用户点击   │    │ • URL 解析  │    │ • 规则匹配          │ │
│  │ • 编程导航   │    │ • 参数提取   │    │ • 应用选择          │ │
│  │ • 浏览器导航 │    │ • 查询解析   │    │ • 容器分配          │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
│                                                   │             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 6. 导航完成  │◀───│ 5. 应用挂载  │◀───│ 4. 路由守卫         │ │
│  │ • 状态更新   │    │ • 应用加载   │    │ • 权限检查          │ │
│  │ • 事件通知   │    │ • 组件渲染   │    │ • 前置守卫          │ │
│  │ • 历史记录   │    │ • 生命周期   │    │ • 后置守卫          │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 路由模式

Micro-Core 支持三种路由模式，每种模式都有其特定的使用场景：

::: code-group

```typescript [History 模式]
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  router: {
    mode: 'history',        // HTML5 History API
    base: '/',              // 基础路径
    fallback: true,         // 降级到 hash 模式
    scrollBehavior: 'smooth' // 滚动行为
  }
})

// 特点：
// ✅ URL 美观，无 # 符号
// ✅ 支持服务端渲染
// ❌ 需要服务器配置支持
// ❌ IE9 以下不支持
```

```typescript [Hash 模式]
const microCore = new MicroCore({
  router: {
    mode: 'hash',           // URL Hash
    base: '/',
    hashPrefix: '#!'        // 自定义 hash 前缀
  }
})

// 特点：
// ✅ 兼容性好，支持所有浏览器
// ✅ 无需服务器配置
// ❌ URL 包含 # 符号
// ❌ SEO 不友好
```

```typescript [Memory 模式]
const microCore = new MicroCore({
  router: {
    mode: 'memory',         // 内存路由
    initialEntries: ['/dashboard'], // 初始路由
    initialIndex: 0         // 初始索引
  }
})

// 特点：
// ✅ 适用于 SSR 和测试
// ✅ 不依赖浏览器 API
// ❌ 无法直接访问 URL
// ❌ 刷新会丢失状态
```

:::

## 路由配置

### 配置层次结构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由配置层次结构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   全局路由配置                           │   │
│  │                                                         │   │
│  │  • 路由模式 (mode)                                      │   │
│  │  • 基础路径 (base)                                      │   │
│  │  • 滚动行为 (scrollBehavior)                           │   │
│  │  • 链接样式 (linkActiveClass)                          │   │
│  │  • 错误处理 (onError)                                  │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   应用路由配置                           │   │
│  │                                                         │   │
│  │  • 激活规则 (activeWhen)                               │   │
│  │  • 路由守卫 (beforeEnter/beforeLeave)                  │   │
│  │  • 路由元信息 (meta)                                   │   │
│  │  • 缓存配置 (cache)                                    │   │
│  │  • 预加载配置 (preload)                                │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   组件路由配置                           │   │
│  │                                                         │   │
│  │  • 路由路径 (path)                                     │   │
│  │  • 路由组件 (component)                                │   │
│  │  • 子路由 (children)                                   │   │
│  │  • 路由参数 (params)                                   │   │
│  │  • 查询参数 (query)                                    │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 基础配置

::: code-group

```typescript [基础配置]
import { RouterPlugin } from '@micro-core/plugins';

const routerPlugin = new RouterPlugin({
  // 路由模式
  mode: 'history',        // 'hash' | 'history' | 'memory'
  
  // 基础路径
  base: '/',
  
  // 激活链接的CSS类
  linkActiveClass: 'router-link-active',
  
  // 精确激活链接的CSS类
  linkExactActiveClass: 'router-link-exact-active',
  
  // 滚动行为
  scrollBehavior: 'smooth'  // 'auto' | 'smooth' | function
});

// 注册路由插件
microCore.use(routerPlugin);
```

```typescript [高级配置]
const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/micro-app',
  
  // 路由表
  routes: [
    {
      path: '/users/*',
      app: 'user-management',
      meta: {
        title: '用户管理',
        requiresAuth: true,
        permissions: ['user:read']
      }
    },
    {
      path: '/orders/*',
      app: 'order-system',
      meta: {
        title: '订单系统',
        requiresAuth: true,
        permissions: ['order:read']
      }
    }
  ],
  
  // 路由守卫
  beforeEach: (to, from, next) => {
    // 全局前置守卫逻辑
    if (to.meta?.requiresAuth && !isAuthenticated()) {
      next('/login');
    } else {
      next();
    }
  },
  
  afterEach: (to, from) => {
    // 全局后置守卫逻辑
    document.title = to.meta?.title || 'Micro App';
    
    // 页面访问统计
    analytics.track('page_view', {
      path: to.path,
      from: from.path
    });
  },
  
  // 错误处理
  onError: (error) => {
    console.error('路由错误:', error);
    // 错误上报
    errorReporter.report(error);
  }
});
```

```typescript [企业级配置]
const enterpriseRouterConfig = {
  mode: 'history',
  base: '/enterprise',
  
  // 安全配置
  security: {
    // CSRF 保护
    csrfProtection: true,
    
    // 路由加密
    encryption: {
      enabled: true,
      algorithm: 'AES-256-GCM'
    },
    
    // 访问控制
    accessControl: {
      enabled: true,
      whitelist: ['/public/*'],
      blacklist: ['/admin/dangerous/*']
    }
  },
  
  // 性能配置
  performance: {
    // 预加载策略
    preload: {
      strategy: 'predictive',
      threshold: 0.7
    },
    
    // 缓存配置
    cache: {
      enabled: true,
      strategy: 'lru',
      maxSize: 100,
      ttl: 300000 // 5分钟
    },
    
    // 懒加载配置
    lazyLoad: {
      enabled: true,
      chunkSize: 'optimal',
      priority: 'high'
    }
  },
  
  // 监控配置
  monitoring: {
    // 性能监控
    performance: true,
    
    // 错误监控
    errorTracking: true,
    
    // 用户行为分析
    analytics: {
      provider: 'google-analytics',
      trackingId: 'GA_TRACKING_ID'
    }
  }
};
```

:::

## 路由匹配

### 匹配规则优先级

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由匹配优先级图                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   高优先级                               │   │
│  │                                                         │   │
│  │  1. 精确匹配 (Exact Match)                              │   │
│  │     /users/profile  →  /users/profile                  │   │
│  │                                                         │   │
│  │  2. 参数匹配 (Parameter Match)                          │   │
│  │     /users/:id      →  /users/123                      │   │
│  │                                                         │   │
│  │  3. 静态前缀匹配 (Static Prefix)                        │   │
│  │     /users/admin    →  /users/admin/settings           │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   中优先级                               │   │
│  │                                                         │   │
│  │  4. 动态前缀匹配 (Dynamic Prefix)                       │   │
│  │     /users/*        →  /users/anything                 │   │
│  │                                                         │   │
│  │  5. 正则表达式匹配 (Regex Match)                        │   │
│  │     /^\/api\/v\d+/  →  /api/v1, /api/v2               │   │
│  │                                                         │   │
│  │  6. 函数匹配 (Function Match)                           │   │
│  │     (location) => boolean                               │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   低优先级                               │   │
│  │                                                         │   │
│  │  7. 通配符匹配 (Wildcard Match)                         │   │
│  │     /**             →  任何路径                         │   │
│  │                                                         │   │
│  │  8. 默认匹配 (Default Match)                            │   │
│  │     fallback route  →  404 页面                        │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 路径匹配规则

::: code-group

```typescript [基础匹配]
// 精确匹配
activeWhen: '/users'

// 前缀匹配
activeWhen: '/users/*'

// 多路径匹配
activeWhen: ['/users', '/user-profile', '/user-settings']

// 正则表达式匹配
activeWhen: /^\/users?(\/.*)?$/

// 参数匹配
activeWhen: '/users/:id'
activeWhen: '/users/:id/orders/:orderId'

// 可选参数
activeWhen: '/users/:id?'

// 通配符匹配
activeWhen: '/admin/*'
```

```typescript [高级匹配]
// 命名参数匹配
activeWhen: '/users/:userId(\\d+)' // 只匹配数字

// 可选参数组
activeWhen: '/posts/:year?/:month?/:day?'

// 重复参数
activeWhen: '/files/*filepath' // 匹配多级路径

// 查询参数匹配
activeWhen: (location) => {
  const params = new URLSearchParams(location.search)
  return location.pathname === '/search' && params.has('q')
}

// 条件匹配
activeWhen: (location) => {
  const isAdminPath = location.pathname.startsWith('/admin')
  const hasPermission = checkAdminPermission()
  return isAdminPath && hasPermission
}
```

```typescript [复杂匹配场景]
// 时间条件匹配
activeWhen: (location) => {
  const now = new Date()
  const isBusinessHours = now.getHours() >= 9 && now.getHours() <= 18
  const isWorkingDay = now.getDay() >= 1 && now.getDay() <= 5
  
  return location.pathname.startsWith('/business') && 
         isBusinessHours && 
         isWorkingDay
}

// 用户角色匹配
activeWhen: (location) => {
  const userRole = getCurrentUserRole()
  const requiredRoles = ['admin', 'manager']
  
  return location.pathname.startsWith('/management') &&
         requiredRoles.includes(userRole)
}

// 设备类型匹配
activeWhen: (location) => {
  const isMobile = /Mobile|Android|iPhone|iPad/.test(navigator.userAgent)
  
  return location.pathname.startsWith('/mobile') && isMobile
}
```

:::

## 路由守卫

### 守卫执行时序图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由守卫执行时序                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  导航触发                                                       │
│      │                                                         │
│      ▼                                                         │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 1. beforeEach (全局前置守卫)                            │   │
│  │    • 身份验证检查                                       │   │
│  │    • 权限验证                                           │   │
│  │    • 全局拦截逻辑                                       │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 2. beforeEnter (路由级守卫)                             │   │
│  │    • 路由特定检查                                       │   │
│  │    • 数据预加载                                         │   │
│  │    • 条件验证                                           │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 3. beforeRouteEnter (组件内守卫)                        │   │
│  │    • 组件实例创建前                                     │   │
│  │    • 无法访问 this                                     │   │
│  │    • 数据获取准备                                       │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 4. beforeResolve (全局解析守卫)                         │   │
│  │    • 异步组件解析完成                                   │   │
│  │    • 最终确认导航                                       │   │
│  │    • 资源准备就绪                                       │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ 5. afterEach (全局后置守卫)                             │   │
│  │    • 导航完成后执行                                     │   │
│  │    • 页面统计分析                                       │   │
│  │    • 标题更新等                                         │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  导航完成                                                       │
└─────────────────────────────────────────────────────────────────┘
```

### 全局守卫

::: code-group

```typescript [全局前置守卫]
// 全局前置守卫
microCore.router.beforeEach((to, from, next) => {
  console.log(`导航到: ${to.path}`);
  
  // 权限检查
  if (to.meta?.requiresAuth) {
    const isAuthenticated = checkAuthentication();
    if (!isAuthenticated) {
      next('/login');
      return;
    }
  }
  
  // 权限检查
  if (to.meta?.permissions) {
    const hasPermission = checkPermissions(to.meta.permissions);
    if (!hasPermission) {
      next('/403');
      return;
    }
  }
  
  // 继续导航
  next();
});
```

```typescript [全局解析守卫]
// 全局解析守卫
microCore.router.beforeResolve((to, from, next) => {
  // 在导航被确认之前，同时在所有组件内守卫和异步路由组件被解析之后调用
  console.log('路由解析完成');
  
  // 预加载关键资源
  if (to.meta?.preloadResources) {
    Promise.all(to.meta.preloadResources.map(loadResource))
      .then(() => next())
      .catch(() => next());
  } else {
    next();
  }
});
```

```typescript [全局后置守卫]
// 全局后置守卫
microCore.router.afterEach((to, from) => {
  // 导航完成后调用
  console.log(`从 ${from.path} 导航到 ${to.path}`);
  
  // 更新页面标题
  document.title = to.meta?.title || 'Default Title';
  
  // 页面访问统计
  analytics.track('page_view', {
    path: to.path,
    title: to.meta?.title
  });
  
  // 滚动到顶部
  window.scrollTo(0, 0);
  
  // 更新面包屑导航
  updateBreadcrumb(to.meta?.breadcrumb);
});
```

```typescript [错误处理守卫]
// 路由错误处理
microCore.router.onError((error) => {
  console.error('路由错误:', error);
  
  // 错误分类处理
  if (error.name === 'ChunkLoadError') {
    // 代码分割加载失败
    window.location.reload();
  } else if (error.name === 'NavigationDuplicated') {
    // 重复导航错误，可以忽略
    console.warn('重复导航:', error.message);
  } else {
    // 其他错误，显示错误页面
    microCore.router.push('/error');
  }
  
  // 错误上报
  errorReporter.report(error);
});
```

:::

## 路由同步

### 主子应用路由同步架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    路由同步架构图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   主应用路由                             │   │
│  │                                                         │   │
│  │  URL: /workspace/projects/123                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │   │
│  │  │ 路由监听器   │───▶│ 事件分发器   │───▶│ 状态同步器   │ │   │
│  │  │ • URL 变化   │    │ • 路由事件   │    │ • 全局状态   │ │   │
│  │  │ • 历史管理   │    │ • 参数传递   │    │ • 参数同步   │ │   │
│  │  └─────────────┘    └─────────────┘    └─────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   路由同步总线                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 事件总线     │  │ 状态管理     │  │ 消息队列         │ │   │
│  │  │ • 路由事件   │  │ • 路由状态   │  │ • 异步同步       │ │   │
│  │  │ • 生命周期   │  │ • 参数状态   │  │ • 批量处理       │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                              ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   子应用路由                             │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │   │
│  │  │ 路由接收器   │  │ 路由适配器   │  │ 本地路由器       │ │   │
│  │  │ • 事件监听   │  │ • 路径转换   │  │ • 内部导航       │ │   │
│  │  │ • 状态接