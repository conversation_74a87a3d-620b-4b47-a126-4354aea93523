# 应用管理

应用管理是 Micro-Core 的核心功能之一，负责微应用的注册、发现、加载、生命周期管理等关键操作。本章将详细介绍如何有效地管理微前端应用。

## 应用注册

### 基础注册

```typescript
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore()

// 注册单个应用
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react'
})
```

### 批量注册

```typescript
const apps = [
  {
    name: 'react-app',
    entry: 'http://localhost:3001',
    container: '#react-container',
    activeWhen: '/react'
  },
  {
    name: 'vue-app',
    entry: 'http://localhost:3002',
    container: '#vue-container',
    activeWhen: '/vue'
  }
]

// 批量注册应用
microCore.registerApps(apps)
```

### 动态注册

```typescript
// 运行时动态注册应用
async function loadDynamicApp() {
  const appConfig = await fetch('/api/apps/dynamic-app').then(res => res.json())
  
  microCore.registerApp({
    name: appConfig.name,
    entry: appConfig.entry,
    container: '#dynamic-container',
    activeWhen: appConfig.route,
    props: appConfig.props
  })
}
```

## 应用配置

### 完整配置选项

```typescript
interface AppConfig {
  // 基础配置
  name: string                    // 应用名称（必需）
  entry: string | AppEntry       // 应用入口（必需）
  container: string | Element    // 挂载容器（必需）
  activeWhen: ActiveRule         // 激活规则（必需）
  
  // 可选配置
  props?: Record<string, any>    // 传递给应用的属性
  loader?: CustomLoader          // 自定义加载器
  sandbox?: SandboxConfig        // 沙箱配置
  prefetch?: boolean | PrefetchConfig  // 预加载配置
  
  // 生命周期钩子
  beforeLoad?: LifecycleHook
  afterLoad?: LifecycleHook
  beforeMount?: LifecycleHook
  afterMount?: LifecycleHook
  beforeUnmount?: LifecycleHook
  afterUnmount?: LifecycleHook
  beforeUpdate?: LifecycleHook
  afterUpdate?: LifecycleHook
}
```

### 入口配置

```typescript
// 字符串入口
entry: 'http://localhost:3001'

// 对象入口
entry: {
  scripts: ['http://localhost:3001/static/js/main.js'],
  styles: ['http://localhost:3001/static/css/main.css'],
  html: 'http://localhost:3001/index.html'
}

// 函数入口
entry: async () => {
  const manifest = await fetch('/api/app-manifest').then(res => res.json())
  return {
    scripts: manifest.scripts,
    styles: manifest.styles
  }
}
```

### 激活规则

```typescript
// 路径匹配
activeWhen: '/react-app'

// 正则表达式
activeWhen: /^\/react-app/

// 函数匹配
activeWhen: (location) => {
  return location.pathname.startsWith('/react-app') && 
         location.search.includes('module=react')
}

// 多条件匹配
activeWhen: ['/react-app', '/react/*', (location) => location.hash === '#react']
```

## 应用生命周期

### 生命周期状态

```typescript
enum AppStatus {
  NOT_LOADED = 'NOT_LOADED',       // 未加载
  LOADING = 'LOADING',             // 加载中
  LOAD_ERROR = 'LOAD_ERROR',       // 加载错误
  LOADED = 'LOADED',               // 已加载
  BOOTSTRAPPING = 'BOOTSTRAPPING', // 启动中
  NOT_MOUNTED = 'NOT_MOUNTED',     // 未挂载
  MOUNTING = 'MOUNTING',           // 挂载中
  MOUNTED = 'MOUNTED',             // 已挂载
  UNMOUNTING = 'UNMOUNTING',       // 卸载中
  UPDATING = 'UPDATING',           // 更新中
  SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN' // 跳过（损坏）
}
```

### 生命周期流程图

```
┌─────────────┐
│ NOT_LOADED  │
└──────┬──────┘
       │ load()
       ▼
┌─────────────┐    error    ┌─────────────┐
│   LOADING   │────────────▶│ LOAD_ERROR  │
└──────┬──────┘             └─────────────┘
       │ success
       ▼
┌─────────────┐
│   LOADED    │
└──────┬──────┘
       │ bootstrap()
       ▼
┌─────────────┐
│BOOTSTRAPPING│
└──────┬──────┘
       │
       ▼
┌─────────────┐    mount()    ┌─────────────┐
│ NOT_MOUNTED │──────────────▶│  MOUNTING   │
└─────────────┘               └──────┬──────┘
       ▲                             │
       │ unmount()                   ▼
       │                      ┌─────────────┐
┌─────────────┐               │   MOUNTED   │
│ UNMOUNTING  │◀──────────────┤             │
└─────────────┘               └──────┬──────┘
                                     │ update()
                                     ▼
                              ┌─────────────┐
                              │  UPDATING   │
                              └─────────────┘
```

### 生命周期钩子

```typescript
microCore.registerApp({
  name: 'my-app',
  entry: 'http://localhost:3001',
  container: '#container',
  activeWhen: '/my-app',
  
  // 加载前钩子
  beforeLoad: async (app) => {
    console.log(`准备加载应用: ${app.name}`)
    // 可以在这里做一些准备工作，如权限检查
    const hasPermission = await checkPermission(app.name)
    if (!hasPermission) {
      throw new Error('没有权限访问此应用')
    }
  },
  
  // 加载后钩子
  afterLoad: (app) => {
    console.log(`应用加载完成: ${app.name}`)
    // 可以在这里做一些后续处理，如埋点统计
    analytics.track('app_loaded', { appName: app.name })
  },
  
  // 挂载前钩子
  beforeMount: (app) => {
    console.log(`准备挂载应用: ${app.name}`)
    // 可以在这里设置应用的初始状态
    app.props = {
      ...app.props,
      user: getCurrentUser(),
      theme: getTheme()
    }
  },
  
  // 挂载后钩子
  afterMount: (app) => {
    console.log(`应用挂载完成: ${app.name}`)
    // 可以在这里做一些 DOM 操作或事件绑定
    setupAppAnalytics(app.container)
  },
  
  // 卸载前钩子
  beforeUnmount: (app) => {
    console.log(`准备卸载应用: ${app.name}`)
    // 可以在这里做一些清理工作
    cleanupAppAnalytics(app.container)
  },
  
  // 卸载后钩子
  afterUnmount: (app) => {
    console.log(`应用卸载完成: ${app.name}`)
    // 可以在这里做一些后续清理
    clearAppCache(app.name)
  }
})
```

## 应用查询和管理

### 获取应用信息

```typescript
// 获取单个应用信息
const app = microCore.getApp('react-app')
console.log(app.status, app.props, app.container)

// 获取所有应用
const allApps = microCore.getApps()
console.log('已注册应用:', allApps.map(app => app.name))

// 获取活跃应用
const activeApps = microCore.getActiveApps()
console.log('当前活跃应用:', activeApps.map(app => app.name))

// 根据状态筛选应用
const mountedApps = microCore.getAppsByStatus(AppStatus.MOUNTED)
console.log('已挂载应用:', mountedApps.map(app => app.name))
```

### 应用状态监听

```typescript
// 监听应用状态变化
microCore.onAppStatusChange((app, newStatus, oldStatus) => {
  console.log(`应用 ${app.name} 状态从 ${oldStatus} 变为 ${newStatus}`)
  
  // 根据状态变化执行相应操作
  switch (newStatus) {
    case AppStatus.MOUNTED:
      // 应用挂载完成
      onAppMounted(app)
      break
    case AppStatus.UNMOUNTING:
      // 应用开始卸载
      onAppUnmounting(app)
      break
    case AppStatus.LOAD_ERROR:
      // 应用加载失败
      onAppLoadError(app)
      break
  }
})

// 监听特定应用的状态变化
microCore.onAppStatusChange('react-app', (app, newStatus, oldStatus) => {
  console.log(`React 应用状态变化: ${oldStatus} -> ${newStatus}`)
})
```

### 手动控制应用

```typescript
// 手动加载应用
await microCore.loadApp('react-app')

// 手动挂载应用
await microCore.mountApp('react-app')

// 手动卸载应用
await microCore.unmountApp('react-app')

// 手动更新应用
await microCore.updateApp('react-app', { 
  props: { theme: 'dark' } 
})

// 重新加载应用
await microCore.reloadApp('react-app')
```

## 应用注销和清理

### 注销应用

```typescript
// 注销单个应用
microCore.unregisterApp('react-app')

// 批量注销应用
microCore.unregisterApps(['react-app', 'vue-app'])

// 注销所有应用
microCore.unregisterAllApps()
```

### 应用清理

```typescript
// 清理应用缓存
microCore.clearAppCache('react-app')

// 清理所有缓存
microCore.clearAllCache()

// 强制清理应用（包括 DOM 和事件）
microCore.forceCleanApp('react-app')
```

## 错误处理

### 应用加载错误

```typescript
microCore.registerApp({
  name: 'error-prone-app',
  entry: 'http://localhost:3001',
  container: '#container',
  activeWhen: '/error-app',
  
  // 加载错误处理
  onLoadError: (error, app) => {
    console.error(`应用 ${app.name} 加载失败:`, error)
    
    // 显示错误页面
    showErrorPage(app.container, {
      title: '应用加载失败',
      message: '请稍后重试或联系管理员',
      retry: () => microCore.reloadApp(app.name)
    })
  },
  
  // 挂载错误处理
  onMountError: (error, app) => {
    console.error(`应用 ${app.name} 挂载失败:`, error)
    
    // 回退到默认页面
    showFallbackPage(app.container)
  }
})
```

### 全局错误处理

```typescript
// 设置全局错误处理器
microCore.setErrorHandler((error, app, phase) => {
  console.error(`应用 ${app?.name} 在 ${phase} 阶段发生错误:`, error)
  
  // 发送错误报告
  errorReporting.report({
    error: error.message,
    stack: error.stack,
    app: app?.name,
    phase,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    url: location.href
  })
  
  // 根据错误类型采取不同策略
  if (error.name === 'ChunkLoadError') {
    // 资源加载失败，尝试重新加载
    microCore.reloadApp(app.name)
  } else if (error.name === 'NetworkError') {
    // 网络错误，显示离线提示
    showOfflineMessage()
  }
})
```

## 性能优化

### 预加载策略

```typescript
// 配置预加载
microCore.registerApp({
  name: 'dashboard-app',
  entry: 'http://localhost:3001',
  container: '#dashboard',
  activeWhen: '/dashboard',
  
  // 启用预加载
  prefetch: true,
  
  // 或者配置详细的预加载策略
  prefetch: {
    // 在空闲时预加载
    idle: true,
    
    // 鼠标悬停时预加载
    hover: '.dashboard-link',
    
    // 在视口内时预加载
    viewport: true,
    
    // 延迟预加载（毫秒）
    delay: 2000
  }
})
```

### 应用缓存

```typescript
// 配置应用缓存
microCore.configure({
  cache: {
    // 启用应用缓存
    enabled: true,
    
    // 缓存策略
    strategy: 'memory', // 'memory' | 'localStorage' | 'sessionStorage'
    
    // 缓存大小限制
    maxSize: 50, // MB
    
    // 缓存过期时间
    maxAge: 30 * 60 * 1000, // 30分钟
    
    // 缓存键生成函数
    keyGenerator: (app) => `${app.name}@${app.version || 'latest'}`
  }
})
```

### 资源优化

```typescript
// 配置资源加载优化
microCore.configure({
  loader: {
    // 并发加载数量
    concurrency: 3,
    
    // 超时时间
    timeout: 30000,
    
    // 重试次数
    retries: 2,
    
    // 资源完整性检查
    integrity: true,
    
    // 跨域配置
    crossOrigin: 'anonymous'
  }
})
```

## 最佳实践

### 1. 应用命名规范

```typescript
// 推荐的命名规范
const apps = [
  { name: 'user-management', ... },    // 使用 kebab-case
  { name: 'order-system', ... },       // 描述性名称
  { name: 'dashboard-v2', ... },       // 包含版本信息
]

// 避免的命名方式
const badApps = [
  { name: 'app1', ... },               // 无意义名称
  { name: 'UserManagement', ... },     // 使用 PascalCase
  { name: 'user_management', ... },    // 使用 snake_case
]
```

### 2. 容器管理

```typescript
// 推荐：为每个应用创建独立容器
<div id="app-container">
  <div id="user-management-container"></div>
  <div id="order-system-container"></div>
  <div id="dashboard-container"></div>
</div>

// 避免：多个应用共享同一容器
<div id="shared-container"></div> <!-- 不推荐 -->
```

### 3. 生命周期管理

```typescript
// 推荐：完整的生命周期处理
microCore.registerApp({
  name: 'my-app',
  // ... 其他配置
  
  beforeMount: (app) => {
    // 设置应用上下文
    setupAppContext(app)
  },
  
  afterMount: (app) => {
    // 初始化应用特定功能
    initializeAppFeatures(app)
  },
  
  beforeUnmount: (app) => {
    // 清理应用资源
    cleanupAppResources(app)
  },
  
  afterUnmount: (app) => {
    // 最终清理
    finalCleanup(app)
  }
})
```

### 4. 错误边界

```typescript
// 推荐：为每个应用设置错误边界
microCore.registerApp({
  name: 'my-app',
  // ... 其他配置
  
  errorBoundary: {
    // 错误回退组件
    fallback: ErrorFallback,
    
    // 错误处理函数
    onError: (error, errorInfo) => {
      console.error('应用错误:', error, errorInfo)
      reportError(error, errorInfo)
    }
  }
})
```

### 5. 性能监控

```typescript
// 推荐：监控应用性能
microCore.onAppStatusChange((app, newStatus, oldStatus) => {
  const timestamp = Date.now()
  
  // 记录性能指标
  performance.mark(`${app.name}-${newStatus}-${timestamp}`)
  
  if (newStatus === AppStatus.MOUNTED) {
    // 计算加载时间
    const loadTime = timestamp - app.loadStartTime
    analytics.track('app_load_time', {
      appName: app.name,
      loadTime
    })
  }
})
```

## 常见问题

### Q: 如何处理应用加载失败？

A: 可以通过多种方式处理：

```typescript
// 1. 设置重试机制
microCore.registerApp({
  name: 'my-app',
  // ... 其他配置
  
  onLoadError: async (error, app) => {
    // 重试 3 次
    for (let i = 0; i < 3; i++) {
      try {
        await microCore.reloadApp(app.name)
        break
      } catch (retryError) {
        if (i === 2) {
          // 最后一次重试失败，显示错误页面
          showErrorPage(app.container)
        }
      }
    }
  }
})

// 2. 设置降级方案
microCore.registerApp({
  name: 'my-app',
  // ... 其他配置
  
  fallback: {
    // 降级到静态页面
    html: '<div>应用暂时不可用，请稍后重试</div>',
    
    // 或者降级到另一个应用
    app: 'fallback-app'
  }
})
```

### Q: 如何实现应用的热更新？

A: 可以通过以下方式实现：

```typescript
// 1. 监听应用更新事件
microCore.on('app:update-available', async (app) => {
  const shouldUpdate = await confirm(`应用 ${app.name} 有新版本，是否更新？`)
  
  if (shouldUpdate) {
    // 热更新应用
    await microCore.hotReload(app.name)
  }
})

// 2. 定期检查更新
setInterval(async () => {
  const apps = microCore.getApps()
  
  for (const app of apps) {
    const hasUpdate = await checkAppUpdate(app)
    if (hasUpdate) {
      microCore.emit('app:update-available', app)
    }
  }
}, 5 * 60 * 1000) // 每 5 分钟检查一次
```

### Q: 如何处理应用间的依赖关系？

A: 可以通过依赖配置来管理：

```typescript
microCore.registerApp({
  name: 'child-app',
  // ... 其他配置
  
  // 声明依赖
  dependencies: ['parent-app', 'shared-lib'],
  
  // 依赖加载完成后再加载当前应用
  waitForDependencies: true
})

// 或者手动控制加载顺序
async function loadAppsWithDependencies() {
  // 先加载依赖应用
  await microCore.loadApp('parent-app')
  await microCore.loadApp('shared-lib')
  
  // 再加载子应用
  await microCore.loadApp('child-app')
}
```

## 总结

应用管理是微前端架构的核心，通过合理的应用注册、生命周期管理、错误处理和性能优化，可以构建稳定、高效的微前端系统。

关键要点：
- 使用清晰的命名规范和容器管理
- 完善的生命周期钩子处理
- 健壮的错误处理和降级机制
- 合理的性能优化策略
- 完整的监控和日志记录

## 下一步

- [路由系统](./routing.md) - 学习微前端路由配置
- [沙箱隔离](./sandbox.md) - 了解应用隔离机制
- [应用通信](./communication.md) - 掌握应用间通信
- [性能优化](../best-practices/performance.md) - 深入性能优化技巧