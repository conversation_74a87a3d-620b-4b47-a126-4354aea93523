# 应用间通信

Micro-Core 提供了多种应用间通信方式，支持主应用与子应用、子应用与子应用之间的数据交换和事件传递。

## 通信方式概览

### 1. 全局事件总线 (EventBus)

最常用的通信方式，基于发布-订阅模式实现。

```typescript
import { EventBus } from '@micro-core/core';

// 发送事件
EventBus.emit('user-login', { userId: '123', username: 'john' });

// 监听事件
EventBus.on('user-login', (userData) => {
  console.log('用户登录:', userData);
});

// 移除监听
EventBus.off('user-login', handler);
```

### 2. 全局状态管理 (GlobalState)

用于共享应用状态，支持响应式更新。

```typescript
import { GlobalState } from '@micro-core/core';

// 设置全局状态
GlobalState.set('currentUser', {
  id: '123',
  name: '<PERSON>',
  role: 'admin'
});

// 获取全局状态
const user = GlobalState.get('currentUser');

// 监听状态变化
GlobalState.watch('currentUser', (newUser, oldUser) => {
  console.log('用户状态变化:', newUser, oldUser);
});
```

### 3. 直接通信 (Direct Communication)

通过应用实例直接调用方法。

```typescript
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel();

// 获取应用实例
const reactApp = kernel.getApplication('react-app');

// 直接调用应用方法
if (reactApp && reactApp.instance) {
  reactApp.instance.updateTheme('dark');
}
```

### 4. Props 传递

通过应用配置传递初始数据。

```typescript
kernel.registerApplication({
  name: 'child-app',
  entry: 'http://localhost:3001',
  container: '#child-container',
  activeWhen: '/child',
  props: {
    theme: 'light',
    userInfo: { id: '123', name: 'John' },
    apiBaseUrl: 'https://api.example.com'
  }
});
```

## 详细使用指南

### EventBus 详细用法

#### 基础事件操作

```typescript
import { EventBus } from '@micro-core/core';

// 1. 发送简单事件
EventBus.emit('theme-change', 'dark');

// 2. 发送复杂数据
EventBus.emit('user-action', {
  type: 'click',
  target: 'button',
  timestamp: Date.now(),
  data: { buttonId: 'submit-btn' }
});

// 3. 监听事件
const handleThemeChange = (theme) => {
  document.body.className = `theme-${theme}`;
};

EventBus.on('theme-change', handleThemeChange);

// 4. 一次性监听
EventBus.once('app-ready', () => {
  console.log('应用已准备就绪');
});

// 5. 移除特定监听器
EventBus.off('theme-change', handleThemeChange);

// 6. 移除所有监听器
EventBus.off('theme-change');
```

#### 命名空间事件

```typescript
// 使用命名空间避免事件冲突
EventBus.emit('user:login', userData);
EventBus.emit('user:logout', {});
EventBus.emit('app:error', errorInfo);
EventBus.emit('route:change', routeInfo);

// 监听命名空间事件
EventBus.on('user:*', (eventName, data) => {
  console.log(`用户事件: ${eventName}`, data);
});
```

#### 事件优先级

```typescript
// 高优先级事件监听器
EventBus.on('critical-error', handler, { priority: 'high' });

// 普通优先级
EventBus.on('user-action', handler, { priority: 'normal' });

// 低优先级
EventBus.on('analytics', handler, { priority: 'low' });
```

### GlobalState 详细用法

#### 基础状态操作

```typescript
import { GlobalState } from '@micro-core/core';

// 1. 设置状态
GlobalState.set('theme', 'dark');
GlobalState.set('user', { id: 1, name: 'John' });

// 2. 获取状态
const theme = GlobalState.get('theme');
const user = GlobalState.get('user');

// 3. 检查状态是否存在
if (GlobalState.has('user')) {
  console.log('用户已登录');
}

// 4. 删除状态
GlobalState.remove('tempData');

// 5. 清空所有状态
GlobalState.clear();
```

#### 深度状态操作

```typescript
// 设置嵌套状态
GlobalState.set('app.settings.theme', 'dark');
GlobalState.set('user.profile.avatar', 'avatar.jpg');

// 获取嵌套状态
const theme = GlobalState.get('app.settings.theme');
const avatar = GlobalState.get('user.profile.avatar');

// 更新部分状态
GlobalState.merge('user', { lastLogin: Date.now() });
```

#### 状态监听

```typescript
// 监听特定状态变化
GlobalState.watch('theme', (newTheme, oldTheme) => {
  console.log(`主题从 ${oldTheme} 变更为 ${newTheme}`);
});

// 监听嵌套状态变化
GlobalState.watch('user.profile', (newProfile, oldProfile) => {
  console.log('用户资料更新:', newProfile);
});

// 监听所有状态变化
GlobalState.watchAll((key, newValue, oldValue) => {
  console.log(`状态 ${key} 发生变化:`, newValue);
});

// 取消监听
const unwatch = GlobalState.watch('theme', handler);
unwatch(); // 取消监听
```

### 主应用通信示例

```typescript
// main.ts - 主应用
import { MicroCoreKernel, EventBus, GlobalState } from '@micro-core/core';

const kernel = new MicroCoreKernel();

// 初始化全局状态
GlobalState.set('currentUser', null);
GlobalState.set('theme', 'light');

// 监听子应用事件
EventBus.on('child:user-login', (userData) => {
  GlobalState.set('currentUser', userData);
  
  // 通知其他子应用
  EventBus.emit('user:login-success', userData);
});

EventBus.on('child:theme-change', (theme) => {
  GlobalState.set('theme', theme);
  
  // 更新主应用主题
  document.body.className = `theme-${theme}`;
  
  // 通知所有子应用
  EventBus.emit('theme:changed', theme);
});

// 注册子应用
kernel.registerApplication({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user',
  props: {
    // 传递初始数据
    initialTheme: GlobalState.get('theme'),
    apiConfig: {
      baseUrl: 'https://api.example.com',
      timeout: 5000
    }
  }
});
```

### 子应用通信示例

#### React 子应用

```typescript
// React 子应用
import React, { useEffect, useState } from 'react';
import { EventBus, GlobalState } from '@micro-core/core';

const UserApp: React.FC = () => {
  const [theme, setTheme] = useState('light');
  const [user, setUser] = useState(null);

  useEffect(() => {
    // 获取初始状态
    const initialTheme = GlobalState.get('theme');
    const currentUser = GlobalState.get('currentUser');
    
    setTheme(initialTheme);
    setUser(currentUser);

    // 监听主题变化
    const handleThemeChange = (newTheme) => {
      setTheme(newTheme);
    };

    // 监听用户状态变化
    const handleUserChange = (newUser) => {
      setUser(newUser);
    };

    EventBus.on('theme:changed', handleThemeChange);
    GlobalState.watch('currentUser', handleUserChange);

    return () => {
      EventBus.off('theme:changed', handleThemeChange);
      // GlobalState.watch 返回的 unwatch 函数会自动清理
    };
  }, []);

  const handleLogin = (userData) => {
    // 通知主应用用户登录
    EventBus.emit('child:user-login', userData);
  };

  const handleThemeToggle = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    // 通知主应用主题变化
    EventBus.emit('child:theme-change', newTheme);
  };

  return (
    <div className={`user-app theme-${theme}`}>
      <h1>用户应用</h1>
      <button onClick={handleThemeToggle}>
        切换主题 ({theme})
      </button>
      {user ? (
        <div>欢迎, {user.name}!</div>
      ) : (
        <button onClick={() => handleLogin({ id: 1, name: 'John' })}>
          登录
        </button>
      )}
    </div>
  );
};

export default UserApp;
```

#### Vue 子应用

```vue
<!-- Vue 子应用 -->
<template>
  <div :class="`shopping-app theme-${theme}`">
    <h1>购物应用</h1>
    <div>当前用户: {{ user?.name || '未登录' }}</div>
    <div>购物车商品数: {{ cartCount }}</div>
    <button @click="addToCart">添加商品</button>
  </div>
</template>

<script>
import { EventBus, GlobalState } from '@micro-core/core';

export default {
  name: 'ShoppingApp',
  data() {
    return {
      theme: 'light',
      user: null,
      cartCount: 0
    };
  },
  mounted() {
    // 获取初始状态
    this.theme = GlobalState.get('theme') || 'light';
    this.user = GlobalState.get('currentUser');

    // 监听事件
    EventBus.on('theme:changed', this.handleThemeChange);
    EventBus.on('user:login-success', this.handleUserLogin);
    
    // 监听全局状态
    GlobalState.watch('currentUser', this.handleUserChange);
  },
  beforeUnmount() {
    // 清理监听器
    EventBus.off('theme:changed', this.handleThemeChange);
    EventBus.off('user:login-success', this.handleUserLogin);
  },
  methods: {
    handleThemeChange(newTheme) {
      this.theme = newTheme;
    },
    handleUserLogin(userData) {
      this.user = userData;
    },
    handleUserChange(newUser) {
      this.user = newUser;
    },
    addToCart() {
      this.cartCount++;
      
      // 通知其他应用购物车变化
      EventBus.emit('cart:item-added', {
        userId: this.user?.id,
        itemCount: this.cartCount,
        timestamp: Date.now()
      });
      
      // 更新全局购物车状态
      GlobalState.set('cart.itemCount', this.cartCount);
    }
  }
};
</script>
```

## 通信最佳实践

### 1. 事件命名规范

```typescript
// 推荐的事件命名规范
EventBus.emit('user:login');           // 用户相关事件
EventBus.emit('cart:item-added');      // 购物车相关事件
EventBus.emit('route:change');         // 路由相关事件
EventBus.emit('app:error');            // 应用相关事件
EventBus.emit('theme:changed');        // 主题相关事件

// 避免的命名方式
EventBus.emit('login');                // 太简单，容易冲突
EventBus.emit('userLoginSuccess');     // 驼峰命名不够清晰
EventBus.emit('USER_LOGIN');           // 全大写不够友好
```

### 2. 状态管理规范

```typescript
// 推荐的状态结构
GlobalState.set('user.profile', { id: 1, name: 'John' });
GlobalState.set('app.settings.theme', 'dark');
GlobalState.set('cart.items', []);
GlobalState.set('route.current', '/dashboard');

// 避免的状态结构
GlobalState.set('userProfile', {}); // 扁平结构不够清晰
GlobalState.set('data', {});         // 名称太泛化
```

### 3. 错误处理

```typescript
// 事件错误处理
EventBus.on('user:login', (userData) => {
  try {
    // 处理登录逻辑
    processUserLogin(userData);
  } catch (error) {
    console.error('处理用户登录失败:', error);
    EventBus.emit('app:error', {
      type: 'user-login-error',
      error: error.message
    });
  }
});

// 状态更新错误处理
try {
  GlobalState.set('user.profile', userData);
} catch (error) {
  console.error('更新用户状态失败:', error);
}
```

### 4. 内存泄漏防护

```typescript
// React Hook 示例
import { useEffect } from 'react';
import { EventBus, GlobalState } from '@micro-core/core';

const useGlobalCommunication = () => {
  useEffect(() => {
    const handleUserChange = (userData) => {
      // 处理用户变化
    };

    const handleThemeChange = (theme) => {
      // 处理主题变化
    };

    // 添加监听器
    EventBus.on('user:changed', handleUserChange);
    EventBus.on('theme:changed', handleThemeChange);
    
    const unwatchUser = GlobalState.watch('currentUser', handleUserChange);

    // 清理函数
    return () => {
      EventBus.off('user:changed', handleUserChange);
      EventBus.off('theme:changed', handleThemeChange);
      unwatchUser();
    };
  }, []);
};
```

## 调试和监控

### 1. 通信调试

```typescript
// 开启调试模式
if (process.env.NODE_ENV === 'development') {
  // 监听所有事件
  EventBus.onAny((eventName, ...args) => {
    console.log(`[EventBus] ${eventName}:`, args);
  });

  // 监听所有状态变化
  GlobalState.watchAll((key, newValue, oldValue) => {
    console.log(`[GlobalState] ${key}:`, { newValue, oldValue });
  });
}
```

### 2. 性能监控

```typescript
// 事件性能监控
const originalEmit = EventBus.emit;
EventBus.emit = function(eventName, ...args) {
  const startTime = performance.now();
  const result = originalEmit.call(this, eventName, ...args);
  const endTime = performance.now();
  
  if (endTime - startTime > 10) { // 超过10ms的事件
    console.warn(`[Performance] Event ${eventName} took ${endTime - startTime}ms`);
  }
  
  return result;
};
```

## 常见问题

### Q: 如何避免事件循环？

A: 使用事件命名空间和明确的数据流向：

```typescript
// 避免循环
EventBus.on('user:update', (userData) => {
  // 不要在这里再次发送 user:update 事件
  // EventBus.emit('user:update', userData); // ❌ 会造成循环
  
  // 应该发送不同的事件
  EventBus.emit('user:updated', userData); // ✅ 正确
});
```

### Q: 如何处理异步通信？

A: 使用 Promise 或 async/await：

```typescript
// 异步事件处理
EventBus.on('user:login', async (credentials) => {
  try {
    const userData = await loginAPI(credentials);
    EventBus.emit('user:login-success', userData);
  } catch (error) {
    EventBus.emit('user:login-error', error);
  }
});
```

### Q: 如何确保通信的类型安全？

A: 使用 TypeScript 接口定义：

```typescript
// 定义事件类型
interface EventMap {
  'user:login': { username: string; password: string };
  'user:logout': void;
  'theme:change': 'light' | 'dark';
}

// 类型安全的事件发送
EventBus.emit<'user:login'>('user:login', {
  username: 'john',
  password: 'secret'
});
```
