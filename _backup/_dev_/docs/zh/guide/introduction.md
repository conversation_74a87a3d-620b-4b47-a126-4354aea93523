# Micro-Core 介绍

Micro-Core 是一个轻量级的微前端框架，专为构建可扩展、可维护的前端应用而设计。它使团队能够独立工作，同时提供统一的用户体验。

## 什么是 Micro-Core？

Micro-Core 是一个微前端框架，帮助您：

- **管理多个应用**：在单个页面中协调多个前端应用
- **实现团队独立**：允许不同团队使用他们偏好的技术
- **保持应用隔离**：保持应用分离以防止冲突
- **提供统一体验**：为最终用户呈现无缝体验

## 核心特性

### 🏗️ 轻量级内核

采用最小化内核构建，仅包含必要功能，保持包体积小巧和高性能。

### 🔧 简单集成

易于与现有应用集成，只需最少的配置即可开始使用。

### 🌐 框架无关

支持 React、Vue、Angular 和其他流行的前端框架。

### 🛡️ 应用隔离

提供沙箱隔离，防止应用之间相互干扰。

### 🚀 性能优先

通过懒加载和资源共享等特性，优化快速加载和流畅的用户体验。

## 为什么选择 Micro-Core？

### 微前端架构的优势

- **团队独立**：不同团队可以使用他们偏好的技术独立开发应用
- **可扩展开发**：添加新功能而不影响现有应用
- **技术灵活性**：根据需要混合和匹配不同的框架
- **独立部署**：独立部署应用而无需协调发布
- **易于维护**：更小、专注的代码库更容易理解和维护

### 何时使用 Micro-Core

Micro-Core 适用于：

- **大型应用**：当您的应用已经大到单个团队无法有效管理时
- **多团队项目**：当多个团队需要在同一个应用的不同部分工作时
- **遗留系统迁移**：当您需要逐步现代化现有应用时
- **技术多样性**：当应用的不同部分受益于不同技术时

## 开始使用

准备尝试 Micro-Core？以下是您需要了解的：

1. **安装**：使用 npm 或 yarn 将 Micro-Core 添加到您的项目
2. **配置**：设置您的主应用并注册微应用
3. **开发**：使用您偏好的框架构建微应用
4. **部署**：独立或一起部署应用

## 下一步

- **[核心概念](./concepts.md)** - 学习基本概念和架构
- **[快速开始](./getting-started.md)** - 按照分步设置指南操作
- **[API 参考](../api/core.md)** - 探索完整的 API 文档

---

*Micro-Core 通过实现团队独立、技术灵活性和可扩展架构，帮助您构建更好的应用。*
