# 安装配置

## 📦 环境要求

在开始之前，请确保你的开发环境满足以下要求：

### Node.js 版本
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **pnpm**: >= 8.0.0 (推荐)

### 浏览器支持
- **Chrome**: >= 88
- **Firefox**: >= 78
- **Safari**: >= 14
- **Edge**: >= 88

## 🚀 快速安装

### 使用 npm

```bash
# 安装核心包
npm install @micro-core/core

# 安装常用插件 (可选)
npm install @micro-core/plugin-router @micro-core/plugin-communication
```

### 使用 pnpm (推荐)

```bash
# 安装核心包
pnpm add @micro-core/core

# 安装常用插件 (可选)
pnpm add @micro-core/plugin-router @micro-core/plugin-communication
```

### 使用 yarn

```bash
# 安装核心包
yarn add @micro-core/core

# 安装常用插件 (可选)
yarn add @micro-core/plugin-router @micro-core/plugin-communication
```

## 🎯 完整安装

如果你需要完整的功能集，可以安装完整的包集合：

```bash
# 使用 pnpm 安装完整包
pnpm add @micro-core/core \
         @micro-core/sidecar \
         @micro-core/plugin-router \
         @micro-core/plugin-communication \
         @micro-core/plugin-auth \
         @micro-core/adapter-react \
         @micro-core/adapter-vue \
         @micro-core/adapter-angular
```

## 📋 包说明

### 核心包

| 包名 | 描述 | 大小 | 必需 |
|------|------|------|------|
| `@micro-core/core` | 核心运行时 | < 15KB | ✅ |
| `@micro-core/sidecar` | 边车模式入口 | < 5KB | 可选 |
| `@micro-core/shared` | 共享工具库 | < 8KB | 自动 |

### 插件包

| 包名 | 描述 | 大小 | 推荐 |
|------|------|------|------|
| `@micro-core/plugin-router` | 路由管理插件 | < 10KB | ⭐⭐⭐ |
| `@micro-core/plugin-communication` | 应用间通信插件 | < 8KB | ⭐⭐⭐ |
| `@micro-core/plugin-auth` | 认证管理插件 | < 6KB | ⭐⭐ |
| `@micro-core/plugin-monitor` | 性能监控插件 | < 12KB | ⭐⭐ |
| `@micro-core/plugin-logger` | 日志管理插件 | < 4KB | ⭐ |

### 适配器包

| 包名 | 描述 | 大小 | 适用框架 |
|------|------|------|----------|
| `@micro-core/adapter-react` | React 适配器 | < 8KB | React 16.8+ |
| `@micro-core/adapter-vue` | Vue 适配器 | < 8KB | Vue 2.7+/3.x |
| `@micro-core/adapter-angular` | Angular 适配器 | < 10KB | Angular 12+ |
| `@micro-core/adapter-svelte` | Svelte 适配器 | < 6KB | Svelte 3.x+ |
| `@micro-core/adapter-solid` | Solid 适配器 | < 6KB | Solid.js 1.x+ |

### 构建工具包

| 包名 | 描述 | 大小 | 适用工具 |
|------|------|------|----------|
| `@micro-core/builder-vite` | Vite 构建插件 | < 15KB | Vite 4.x+ |
| `@micro-core/builder-webpack` | Webpack 构建插件 | < 20KB | Webpack 5.x |
| `@micro-core/builder-rollup` | Rollup 构建插件 | < 12KB | Rollup 3.x+ |

## ⚙️ 基础配置

### 1. 创建配置文件

在项目根目录创建 `micro-core.config.js`：

```javascript
// micro-core.config.js
export default {
  // 应用配置
  apps: [
    {
      name: 'main-app',
      entry: './src/main.js',
      activeWhen: '/',
      container: '#app'
    }
  ],
  
  // 插件配置
  plugins: [
    '@micro-core/plugin-router',
    '@micro-core/plugin-communication'
  ],
  
  // 沙箱配置
  sandbox: {
    css: true,
    js: true
  },
  
  // 性能配置
  performance: {
    prefetch: true,
    preload: false
  }
}
```

### 2. TypeScript 配置

如果使用 TypeScript，创建类型声明文件：

```typescript
// types/micro-core.d.ts
declare module '@micro-core/core' {
  export interface MicroCoreConfig {
    apps: AppConfig[]
    plugins?: string[]
    sandbox?: SandboxConfig
    performance?: PerformanceConfig
  }
  
  export interface AppConfig {
    name: string
    entry: string
    activeWhen: string | ((location: Location) => boolean)
    container?: string | HTMLElement
    props?: Record<string, any>
  }
  
  export class MicroCore {
    constructor(config?: MicroCoreConfig)
    registerApp(config: AppConfig): Promise<void>
    start(): Promise<void>
    destroy(): void
  }
}
```

### 3. 主应用初始化

```javascript
// src/main.js
import { MicroCore } from '@micro-core/core'

// 创建微前端实例
const microCore = new MicroCore({
  apps: [
    {
      name: 'user-app',
      entry: 'http://localhost:3001/index.js',
      activeWhen: '/user',
      container: '#user-container'
    },
    {
      name: 'product-app', 
      entry: 'http://localhost:3002/index.js',
      activeWhen: '/product',
      container: '#product-container'
    }
  ]
})

// 启动应用
microCore.start()
```

## 🔧 开发环境配置

### Vite 配置

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/builder-vite'

export default defineConfig({
  plugins: [
    microCore({
      // 微前端配置
      apps: [
        {
          name: 'sub-app',
          entry: './src/sub-app/main.js'
        }
      ]
    })
  ],
  
  // 开发服务器配置
  server: {
    port: 3000,
    cors: true
  },
  
  // 构建配置
  build: {
    rollupOptions: {
      external: ['@micro-core/core']
    }
  }
})
```

### Webpack 配置

```javascript
// webpack.config.js
const { MicroCorePlugin } = require('@micro-core/builder-webpack')

module.exports = {
  plugins: [
    new MicroCorePlugin({
      apps: [
        {
          name: 'sub-app',
          entry: './src/sub-app/main.js'
        }
      ]
    })
  ],
  
  externals: {
    '@micro-core/core': 'MicroCore'
  }
}
```

## 🎨 边车模式配置

如果你想使用边车模式来集成现有应用：

```javascript
// 现有应用中添加一行代码
import '@micro-core/sidecar'

// 或者通过 script 标签引入
// <script src="https://unpkg.com/@micro-core/sidecar"></script>
```

边车模式会自动：
- 检测当前应用的框架类型
- 注入微前端能力
- 提供统一的生命周期接口

## 🔍 验证安装

创建一个简单的测试文件来验证安装：

```javascript
// test-installation.js
import { MicroCore } from '@micro-core/core'

console.log('Micro-Core 版本:', MicroCore.version)

const microCore = new MicroCore()
console.log('Micro-Core 实例创建成功:', microCore)

// 测试基本功能
microCore.registerApp({
  name: 'test-app',
  entry: 'data:text/javascript,console.log("测试应用加载成功")',
  activeWhen: () => true,
  container: document.body
})

microCore.start().then(() => {
  console.log('Micro-Core 启动成功!')
})
```

运行测试：

```bash
node test-installation.js
```

## 🚨 常见问题

### 1. 版本兼容性问题

**问题**: 不同包版本不兼容

**解决方案**:
```bash
# 检查版本
npm ls @micro-core/core

# 统一版本
npm install @micro-core/core@latest @micro-core/plugin-router@latest
```

### 2. TypeScript 类型错误

**问题**: 找不到类型声明

**解决方案**:
```bash
# 安装类型包
npm install @types/micro-core

# 或者在 tsconfig.json 中添加
{
  "compilerOptions": {
    "moduleResolution": "node",
    "esModuleInterop": true
  }
}
```

### 3. 构建工具集成问题

**问题**: 构建时出现模块解析错误

**解决方案**:
```javascript
// 在构建配置中添加别名
{
  resolve: {
    alias: {
      '@micro-core/core': require.resolve('@micro-core/core')
    }
  }
}
```

## 📚 下一步

安装完成后，你可以：

1. 查看[快速开始](/zh/guide/getting-started)了解基本用法
2. 阅读[基础概念](/zh/guide/concepts)理解核心概念
3. 浏览[示例代码](/zh/examples/)学习最佳实践
4. 参考[API 文档](/zh/api/)了解详细接口

---

遇到问题？查看[故障排除](/zh/guide/troubleshooting)或在 [GitHub](https://github.com/echo008/micro-core/issues) 提交问题。