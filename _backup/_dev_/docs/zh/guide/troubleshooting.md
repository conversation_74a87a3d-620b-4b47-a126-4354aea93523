# 故障排除指南

本指南帮助您诊断和解决 Micro-Core 微前端开发中的常见问题。我们按问题类型分类，提供详细的解决方案和预防措施。

## 📋 目录

- [应用加载问题](#应用加载问题)
- [路由问题](#路由问题)
- [通信问题](#通信问题)
- [样式冲突](#样式冲突)
- [JavaScript 沙箱问题](#javascript-沙箱问题)
- [性能问题](#性能问题)
- [构建和部署问题](#构建和部署问题)
- [调试工具](#调试工具)

## 应用加载问题

### 1. 应用无法加载

**症状**：微应用白屏或加载失败

**可能原因**：
- 应用入口地址错误
- 网络连接问题
- CORS 跨域限制
- 应用生命周期函数未正确导出

**解决方案**：

```typescript
// 1. 检查应用配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'my-app',
      entry: 'http://localhost:3001', // 确保地址正确
      activeWhen: '/my-app',
      container: '#app-container' // 确保容器存在
    }
  ]
})

// 2. 验证生命周期函数导出
// 在微应用中确保正确导出
export async function bootstrap(props) {
  console.log('App bootstrapped:', props)
}

export async function mount(props) {
  console.log('App mounted:', props)
  // 挂载逻辑
}

export async function unmount(props) {
  console.log('App unmounted:', props)
  // 卸载逻辑
}

// 3. 配置 CORS
// 在微应用的开发服务器中
module.exports = {
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
    }
  }
}
```

**调试步骤**：

```typescript
// 启用详细日志
const microCore = new MicroCore({
  debug: true,
  logger: {
    level: 'debug',
    enableConsole: true
  }
})

// 监听加载事件
microCore.on('app:load:start', (app) => {
  console.log('开始加载应用:', app.name)
})

microCore.on('app:load:success', (app) => {
  console.log('应用加载成功:', app.name)
})

microCore.on('app:load:error', (app, error) => {
  console.error('应用加载失败:', app.name, error)
})
```

### 2. 应用加载缓慢

**症状**：应用加载时间过长

**解决方案**：

```typescript
// 1. 启用预加载
const microCore = new MicroCore({
  apps: [
    {
      name: 'my-app',
      entry: 'http://localhost:3001',
      preload: true, // 预加载
      prefetch: true // 预获取
    }
  ],
  
  // 2. 配置缓存策略
  cache: {
    enabled: true,
    strategy: 'stale-while-revalidate',
    maxAge: 3600 // 1小时
  },
  
  // 3. 启用资源压缩
  compression: {
    enabled: true,
    algorithm: 'gzip'
  }
})

// 4. 使用 CDN 加速
const microCore = new MicroCore({
  apps: [
    {
      name: 'my-app',
      entry: 'https://cdn.example.com/my-app',
      fallback: 'http://localhost:3001' // 降级地址
    }
  ]
})
```

## 路由问题

### 1. 路由跳转失效

**症状**：点击导航无反应或跳转错误

**解决方案**：

```typescript
// 1. 检查路由配置
const microCore = new MicroCore({
  router: {
    mode: 'history', // 或 'hash'
    base: '/app'
  },
  apps: [
    {
      name: 'user-app',
      activeWhen: '/app/user', // 确保路径匹配
      entry: 'http://localhost:3001'
    }
  ]
})

// 2. 使用正确的导航方法
// ✅ 正确
microCore.router.push('/app/user/profile')

// ❌ 错误
window.location.href = '/app/user/profile'

// 3. 监听路由变化
microCore.router.beforeEach((to, from, next) => {
  console.log('路由变化:', from.path, '->', to.path)
  next()
})
```

### 2. 路由状态不同步

**症状**：浏览器地址栏与应用状态不一致

**解决方案**：

```typescript
// 1. 启用路由同步
const microCore = new MicroCore({
  router: {
    syncMode: 'auto', // 自动同步
    syncDelay: 100 // 同步延迟
  }
})

// 2. 手动同步路由状态
microCore.router.sync()

// 3. 监听浏览器前进后退
window.addEventListener('popstate', (event) => {
  microCore.router.handlePopState(event)
})
```

### 3. 子应用路由冲突

**症状**：多个应用的路由相互干扰

**解决方案**：

```typescript
// 1. 使用路由命名空间
const microCore = new MicroCore({
  apps: [
    {
      name: 'user-app',
      activeWhen: '/user',
      routeNamespace: 'user' // 路由命名空间
    },
    {
      name: 'order-app',
      activeWhen: '/order',
      routeNamespace: 'order'
    }
  ]
})

// 2. 配置路由隔离
const microCore = new MicroCore({
  router: {
    isolation: true, // 启用路由隔离
    strictMode: true // 严格模式
  }
})
```

## 通信问题

### 1. 应用间通信失败

**症状**：事件发送后没有响应

**解决方案**：

```typescript
// 1. 检查事件名称和监听器
// 发送方
microCore.eventBus.emit('user:login', { userId: 123 })

// 接收方
microCore.eventBus.on('user:login', (data) => {
  console.log('收到登录事件:', data)
})

// 2. 使用通配符监听
microCore.eventBus.on('user:*', (eventName, data) => {
  console.log('用户相关事件:', eventName, data)
})

// 3. 检查事件总线状态
console.log('事件监听器:', microCore.eventBus.getListeners())
console.log('事件历史:', microCore.eventBus.getHistory())
```

### 2. 全局状态不更新

**症状**：状态变化后组件没有重新渲染

**解决方案**：

```typescript
// 1. 确保使用响应式 API
// React
import { useGlobalState } from '@micro-core/adapter-react'

function MyComponent() {
  const [user, setUser] = useGlobalState('user')
  
  return (
    <div>
      <p>{user?.name}</p>
      <button onClick={() => setUser({ name: 'New Name' })}>
        Update
      </button>
    </div>
  )
}

// Vue
import { useGlobalState } from '@micro-core/adapter-vue'

export default {
  setup() {
    const [user, setUser] = useGlobalState('user')
    
    return {
      user,
      updateUser: () => setUser({ name: 'New Name' })
    }
  }
}

// 2. 手动触发更新
microCore.communication.setState('user', newUser)
microCore.communication.notifyStateChange('user')
```

## 样式冲突

### 1. CSS 样式相互覆盖

**症状**：应用样式混乱或被覆盖

**解决方案**：

```typescript
// 1. 启用样式隔离
const microCore = new MicroCore({
  sandbox: {
    css: {
      isolation: 'scoped', // 'scoped' | 'shadow-dom'
      prefix: true, // 自动添加前缀
      strictStyleIsolation: true
    }
  }
})

// 2. 使用 CSS Modules
// webpack.config.js
module.exports = {
  module: {
    rules: [
      {
        test: /\.module\.css$/,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              modules: {
                localIdentName: '[name]__[local]__[hash:base64:5]'
              }
            }
          }
        ]
      }
    ]
  }
}

// 3. 使用 Shadow DOM
const microCore = new MicroCore({
  sandbox: {
    css: {
      isolation: 'shadow-dom',
      mode: 'closed' // 'open' | 'closed'
    }
  }
})
```

### 2. 全局样式污染

**症状**：微应用的样式影响了主应用

**解决方案**：

```typescript
// 1. 配置样式作用域
const microCore = new MicroCore({
  apps: [
    {
      name: 'my-app',
      entry: 'http://localhost:3001',
      styleScope: 'my-app', // 样式作用域
      styleIsolation: true
    }
  ]
})

// 2. 使用 PostCSS 插件
// postcss.config.js
module.exports = {
  plugins: [
    require('postcss-prefixwrap')('.my-app-scope')
  ]
}

// 3. 运行时样式隔离
microCore.on('app:mount', (app) => {
  const styles = document.querySelectorAll(`style[data-app="${app.name}"]`)
  styles.forEach(style => {
    style.setAttribute('scoped', app.name)
  })
})
```

## JavaScript 沙箱问题

### 1. 全局变量冲突

**症状**：不同应用的全局变量相互覆盖

**解决方案**：

```typescript
// 1. 启用 JavaScript 沙箱
const microCore = new MicroCore({
  sandbox: {
    js: {
      isolation: 'proxy', // 'proxy' | 'snapshot'
      strictGlobalIsolation: true
    }
  }
})

// 2. 配置全局变量白名单
const microCore = new MicroCore({
  sandbox: {
    js: {
      globalWhitelist: ['React', 'Vue', 'jQuery'],
      globalBlacklist: ['eval', 'Function']
    }
  }
})

// 3. 手动清理全局变量
microCore.on('app:unmount', (app) => {
  // 清理应用相关的全局变量
  delete window[`${app.name}GlobalVar`]
})
```

### 2. 事件监听器泄漏

**症状**：应用卸载后事件监听器仍然存在

**解决方案**：

```typescript
// 1. 自动清理事件监听器
const microCore = new MicroCore({
  sandbox: {
    eventListenerCleanup: true,
    autoCleanupOnUnmount: true
  }
})

// 2. 手动管理事件监听器
class EventManager {
  private listeners: Array<{ element: Element, event: string, handler: Function }> = []
  
  addEventListener(element: Element, event: string, handler: Function) {
    element.addEventListener(event, handler)
    this.listeners.push({ element, event, handler })
  }
  
  cleanup() {
    this.listeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler)
    })
    this.listeners = []
  }
}

// 在应用中使用
const eventManager = new EventManager()

// 添加监听器
eventManager.addEventListener(button, 'click', handleClick)

// 应用卸载时清理
export async function unmount() {
  eventManager.cleanup()
}
```

## 性能问题

### 1. 应用启动缓慢

**症状**：首次加载或切换应用时延迟明显

**解决方案**：

```typescript
// 1. 启用预加载和预获取
const microCore = new MicroCore({
  apps: [
    {
      name: 'critical-app',
      preload: true, // 预加载
      priority: 'high'
    },
    {
      name: 'secondary-app',
      prefetch: true, // 预获取
      priority: 'low'
    }
  ],
  
  // 2. 配置资源优化
  optimization: {
    splitChunks: true,
    treeShaking: true,
    minification: true
  }
})

// 3. 使用懒加载
const microCore = new MicroCore({
  apps: [
    {
      name: 'admin-app',
      entry: () => import('./admin-app'), // 懒加载
      activeWhen: '/admin'
    }
  ]
})
```

### 2. 内存泄漏

**症状**：长时间使用后页面卡顿或崩溃

**解决方案**：

```typescript
// 1. 启用内存监控
const microCore = new MicroCore({
  monitoring: {
    memory: {
      enabled: true,
      threshold: 100 * 1024 * 1024, // 100MB
      interval: 30000 // 30秒检查一次
    }
  }
})

// 2. 自动垃圾回收
microCore.on('app:unmount', (app) => {
  // 强制垃圾回收（仅在开发环境）
  if (process.env.NODE_ENV === 'development' && window.gc) {
    window.gc()
  }
})

// 3. 监控内存使用
setInterval(() => {
  if (performance.memory) {
    const { usedJSHeapSize, totalJSHeapSize } = performance.memory
    const usage = (usedJSHeapSize / totalJSHeapSize) * 100
    
    if (usage > 80) {
      console.warn('内存使用率过高:', usage.toFixed(2) + '%')
    }
  }
}, 10000)
```

## 构建和部署问题

### 1. 构建失败

**症状**：构建过程中出现错误

**解决方案**：

```typescript
// 1. 检查构建配置
// vite.config.js
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/vite-plugin'

export default defineConfig({
  plugins: [
    microCore({
      name: 'my-app',
      entry: './src/main.ts',
      exposes: {
        './App': './src/App.tsx'
      }
    })
  ],
  
  build: {
    lib: {
      entry: './src/main.ts',
      name: 'MyApp',
      formats: ['umd']
    },
    
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      }
    }
  }
})

// 2. 处理依赖问题
// package.json
{
  "peerDependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0"
  }
}
```

### 2. 部署后访问失败

**症状**：本地正常，部署后无法访问

**解决方案**：

```typescript
// 1. 配置正确的公共路径
// vite.config.js
export default defineConfig({
  base: '/my-app/', // 部署路径
  
  build: {
    outDir: 'dist',
    assetsDir: 'assets'
  }
})

// 2. 配置 CDN 地址
const microCore = new MicroCore({
  apps: [
    {
      name: 'my-app',
      entry: 'https://cdn.example.com/my-app/index.js',
      publicPath: 'https://cdn.example.com/my-app/'
    }
  ]
})

// 3. 配置 Nginx
// nginx.conf
server {
  listen 80;
  server_name example.com;
  
  location /my-app/ {
    try_files $uri $uri/ /my-app/index.html;
  }
  
  location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
  }
}
```

## 调试工具

### 1. 开发者工具

```typescript
// 1. 启用开发者工具
const microCore = new MicroCore({
  devtools: {
    enabled: process.env.NODE_ENV === 'development',
    features: {
      inspector: true,      // 应用检查器
      profiler: true,       // 性能分析器
      logger: true,         // 日志查看器
      stateViewer: true,    // 状态查看器
      eventTracker: true    // 事件追踪器
    }
  }
})

// 2. 使用浏览器扩展
// 安装 Micro-Core DevTools 浏览器扩展
// Chrome Web Store: Micro-Core DevTools
```

### 2. 日志系统

```typescript
// 1. 配置详细日志
const microCore = new MicroCore({
  logger: {
    level: 'debug', // 'error' | 'warn' | 'info' | 'debug'
    enableConsole: true,
    enableFile: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    
    // 自定义日志格式
    format: (level, message, meta) => {
      return `[${new Date().toISOString()}] ${level.toUpperCase()}: ${message} ${JSON.stringify(meta)}`
    }
  }
})

// 2. 使用日志 API
microCore.logger.debug('应用加载开始', { app: 'my-app' })
microCore.logger.info('应用加载成功', { app: 'my-app', duration: 1200 })
microCore.logger.warn('应用加载缓慢', { app: 'my-app', duration: 5000 })
microCore.logger.error('应用加载失败', { app: 'my-app', error: 'Network error' })
```

### 3. 性能监控

```typescript
// 1. 启用性能监控
const microCore = new MicroCore({
  monitoring: {
    performance: {
      enabled: true,
      metrics: ['loadTime', 'renderTime', 'memoryUsage'],
      reportInterval: 60000 // 1分钟上报一次
    },
    
    // 自定义监控
    custom: {
      onMetric: (metric) => {
        console.log('性能指标:', metric)
        
        // 上报到监控系统
        if (metric.value > metric.threshold) {
          reportToMonitoring(metric)
        }
      }
    }
  }
})

// 2. 手动记录性能
microCore.performance.mark('app-load-start')
// ... 应用加载逻辑
microCore.performance.mark('app-load-end')
microCore.performance.measure('app-load-duration', 'app-load-start', 'app-load-end')
```

### 4. 错误追踪

```typescript
// 1. 配置错误处理
const microCore = new MicroCore({
  errorHandler: {
    enabled: true,
    captureUnhandledRejections: true,
    captureConsoleErrors: true,
    
    // 错误过滤
    beforeCapture: (error) => {
      // 过滤掉不重要的错误
      if (error.message.includes('Script error')) {
        return false
      }
      return true
    },
    
    // 错误上报
    onError: (error, context) => {
      console.error('捕获到错误:', error, context)
      
      // 上报到错误追踪系统
      if (window.Sentry) {
        window.Sentry.captureException(error, {
          extra: context
        })
      }
    }
  }
})

// 2. 手动上报错误
try {
  // 可能出错的代码
} catch (error) {
  microCore.errorHandler.captureException(error, {
    level: 'error',
    tags: { component: 'my-component' },
    extra: { userId: 123 }
  })
}
```

---

## 获取帮助

如果以上解决方案无法解决您的问题，请通过以下方式获取帮助：

- 📖 [查看完整文档](/guide/)
- 🐛 [提交 Issue](https://github.com/micro-core/micro-core/issues)
- 💬 [加入社区讨论](https://discord.gg/micro-core)
- 📧 [联系技术支持](mailto:<EMAIL>)

在寻求帮助时，请提供：
1. 详细的错误信息和堆栈跟踪
2. 相关的配置文件
3. 重现问题的最小示例
4. 环境信息（浏览器版本、Node.js 版本等）
