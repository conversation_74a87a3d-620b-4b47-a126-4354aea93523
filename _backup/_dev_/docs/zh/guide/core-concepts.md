# 核心概念

本章将深入介绍 Micro-Core 的核心概念和设计理念，帮助您更好地理解和使用这个微前端框架。

## 微前端架构概述

微前端是一种将单体前端应用拆分为多个独立、可部署的小型应用的架构模式。每个微应用可以由不同的团队开发、测试和部署，使用不同的技术栈。

### 传统单体应用 vs 微前端应用

```
传统单体应用:
┌─────────────────────────────────────┐
│           单体前端应用               │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │模块A│ │模块B│ │模块C│ │模块D│   │
│  └─────┘ └─────┘ └─────┘ └─────┘   │
└─────────────────────────────────────┘

微前端应用:
┌─────────────────────────────────────┐
│              主应用                 │
├─────────────────────────────────────┤
│ ┌─────┐   ┌─────┐   ┌─────┐        │
│ │应用A│   │应用B│   │应用C│        │
│ │React│   │ Vue │   │Angular│      │
│ └─────┘   └─────┘   └─────┘        │
└─────────────────────────────────────┘
```

## Micro-Core 架构设计

Micro-Core 采用微内核架构设计，将系统分为核心层和扩展层：

### 架构层次图

```
┌─────────────────────────────────────────────────────────┐
│                    应用层 (Applications)                 │
├─────────────────────────────────────────────────────────┤
│  React App  │  Vue App   │ Angular App │  Vanilla JS   │
├─────────────────────────────────────────────────────────┤
│                   适配器层 (Adapters)                   │
├─────────────────────────────────────────────────────────┤
│ React Adapter │ Vue Adapter │ Angular Adapter │ ...    │
├─────────────────────────────────────────────────────────┤
│                   插件层 (Plugins)                      │
├─────────────────────────────────────────────────────────┤
│ Router Plugin │ Auth Plugin │ State Plugin │ ...       │
├─────────────────────────────────────────────────────────┤
│                   核心层 (Core)                         │
├─────────────────────────────────────────────────────────┤
│ App Registry │ Lifecycle │ Event Bus │ Sandbox │ ...   │
└─────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. 应用注册器 (App Registry)

负责管理所有微应用的注册、发现和生命周期：

```typescript
interface AppRegistry {
  // 注册应用
  register(app: AppConfig): void
  
  // 注销应用
  unregister(name: string): void
  
  // 获取应用信息
  getApp(name: string): AppInfo | null
  
  // 获取所有应用
  getApps(): AppInfo[]
  
  // 获取活跃应用
  getActiveApps(): AppInfo[]
}
```

#### 2. 生命周期管理器 (Lifecycle Manager)

管理微应用的完整生命周期：

```typescript
interface LifecycleManager {
  // 加载应用
  load(app: AppInfo): Promise<void>
  
  // 启动应用
  bootstrap(app: AppInfo): Promise<void>
  
  // 挂载应用
  mount(app: AppInfo): Promise<void>
  
  // 卸载应用
  unmount(app: AppInfo): Promise<void>
  
  // 更新应用
  update(app: AppInfo): Promise<void>
}
```

#### 3. 事件总线 (Event Bus)

提供应用间通信机制：

```typescript
interface EventBus {
  // 发送事件
  emit(event: string, data?: any): void
  
  // 监听事件
  on(event: string, handler: Function): () => void
  
  // 监听一次事件
  once(event: string, handler: Function): () => void
  
  // 移除监听器
  off(event: string, handler?: Function): void
  
  // 清除所有监听器
  clear(): void
}
```

#### 4. 沙箱系统 (Sandbox)

提供应用隔离机制：

```typescript
interface Sandbox {
  // 创建沙箱
  create(app: AppInfo): SandboxInstance
  
  // 激活沙箱
  activate(sandbox: SandboxInstance): void
  
  // 停用沙箱
  deactivate(sandbox: SandboxInstance): void
  
  // 销毁沙箱
  destroy(sandbox: SandboxInstance): void
}
```

## 关键概念详解

### 1. 微应用 (Micro Application)

微应用是一个独立的前端应用，具有以下特征：

- **独立开发**: 可以使用不同的技术栈
- **独立部署**: 可以单独部署和更新
- **独立运行**: 可以脱离主应用独立运行
- **生命周期**: 具有完整的生命周期管理

```typescript
interface MicroApp {
  name: string              // 应用名称
  entry: string            // 应用入口
  container: string        // 挂载容器
  activeWhen: string | Function  // 激活条件
  props?: object           // 传递给应用的属性
  loader?: Function        // 自定义加载器
}
```

### 2. 应用生命周期

每个微应用都有完整的生命周期：

```
┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐
│  LOAD   │───▶│BOOTSTRAP│───▶│  MOUNT  │───▶│ ACTIVE  │
└─────────┘    └─────────┘    └─────────┘    └─────────┘
                                                  │
┌─────────┐    ┌─────────┐    ┌─────────┐       │
│DESTROYED│◀───│ UNMOUNT │◀───│INACTIVE │◀──────┘
└─────────┘    └─────────┘    └─────────┘
```

#### 生命周期阶段说明

1. **LOAD**: 加载应用资源
2. **BOOTSTRAP**: 初始化应用
3. **MOUNT**: 挂载应用到 DOM
4. **ACTIVE**: 应用处于活跃状态
5. **INACTIVE**: 应用处于非活跃状态
6. **UNMOUNT**: 从 DOM 卸载应用
7. **DESTROYED**: 销毁应用实例

### 3. 路由系统

Micro-Core 提供灵活的路由系统：

#### 路由匹配规则

```typescript
// 字符串匹配
activeWhen: '/app1'

// 正则表达式匹配
activeWhen: /^\/app1/

// 函数匹配
activeWhen: (location) => location.pathname.startsWith('/app1')

// 数组匹配（多个条件）
activeWhen: ['/app1', '/app1/*']
```

#### 路由模式

```typescript
const microCore = new MicroCore({
  router: {
    mode: 'history',    // 'history' | 'hash'
    base: '/',          // 基础路径
    strict: false,      // 严格模式
    sensitive: false    // 大小写敏感
  }
})
```

### 4. 沙箱隔离

Micro-Core 提供多种沙箱隔离策略：

#### JavaScript 沙箱

```typescript
// Proxy 沙箱（推荐）
sandbox: {
  type: 'proxy',
  strict: true
}

// Snapshot 沙箱
sandbox: {
  type: 'snapshot'
}

// 无沙箱
sandbox: {
  type: 'none'
}
```

#### CSS 沙箱

```typescript
// 样式隔离配置
sandbox: {
  css: {
    enabled: true,
    mode: 'scoped',     // 'scoped' | 'shadow-dom'
    prefix: 'micro-'    // CSS 前缀
  }
}
```

### 5. 应用间通信

#### 事件通信

```typescript
// 发送事件
microCore.eventBus.emit('user-login', { userId: 123 })

// 监听事件
const unsubscribe = microCore.eventBus.on('user-login', (data) => {
  console.log('用户登录:', data)
})

// 取消监听
unsubscribe()
```

#### 状态共享

```typescript
// 设置全局状态
microCore.setGlobalState({
  user: { name: 'John', role: 'admin' },
  theme: 'dark'
})

// 获取全局状态
const user = microCore.getGlobalState('user')

// 监听状态变化
microCore.onGlobalStateChange((state, prevState) => {
  console.log('状态变化:', state, prevState)
})
```

#### Props 传递

```typescript
// 注册应用时传递 props
microCore.registerApp({
  name: 'child-app',
  entry: 'http://localhost:3001',
  container: '#container',
  activeWhen: '/child',
  props: {
    user: { name: 'John' },
    theme: 'dark'
  }
})
```

### 6. 适配器系统

适配器用于适配不同的前端框架：

#### React 适配器

```typescript
import { ReactAdapter } from '@micro-core/adapter-react'

const microCore = new MicroCore({
  adapters: [new ReactAdapter()]
})
```

#### Vue 适配器

```typescript
import { VueAdapter } from '@micro-core/adapter-vue'

const microCore = new MicroCore({
  adapters: [new VueAdapter()]
})
```

#### 自定义适配器

```typescript
class CustomAdapter implements Adapter {
  name = 'custom'
  
  async load(app: AppInfo): Promise<any> {
    // 加载应用逻辑
  }
  
  async mount(app: AppInfo, props: any): Promise<void> {
    // 挂载应用逻辑
  }
  
  async unmount(app: AppInfo): Promise<void> {
    // 卸载应用逻辑
  }
}
```

### 7. 插件系统

插件用于扩展 Micro-Core 的功能：

#### 内置插件

```typescript
import { RouterPlugin, AuthPlugin } from '@micro-core/plugins'

const microCore = new MicroCore({
  plugins: [
    new RouterPlugin(),
    new AuthPlugin({
      loginUrl: '/login',
      tokenKey: 'access_token'
    })
  ]
})
```

#### 自定义插件

```typescript
class CustomPlugin implements Plugin {
  name = 'custom-plugin'
  
  install(microCore: MicroCore): void {
    // 插件安装逻辑
    microCore.eventBus.on('app:mounted', (app) => {
      console.log(`应用 ${app.name} 已挂载`)
    })
  }
  
  uninstall(microCore: MicroCore): void {
    // 插件卸载逻辑
  }
}
```

## 设计原则

### 1. 技术栈无关

Micro-Core 不限制微应用使用的技术栈，支持：

- React、Vue、Angular 等主流框架
- jQuery、Vanilla JS 等传统技术
- 任何可以在浏览器中运行的前端技术

### 2. 独立部署

每个微应用都可以：

- 独立开发和测试
- 独立部署和更新
- 独立回滚和版本管理
- 独立的 CI/CD 流程

### 3. 运行时集成

Micro-Core 采用运行时集成方式：

- 应用在浏览器中动态加载
- 支持应用的热插拔
- 支持应用的按需加载
- 支持应用的预加载

### 4. 隔离性

提供完善的隔离机制：

- JavaScript 执行环境隔离
- CSS 样式隔离
- DOM 操作隔离
- 全局变量隔离

### 5. 通信机制

提供多种通信方式：

- 事件总线通信
- 全局状态共享
- Props 传递
- URL 参数传递
- LocalStorage/SessionStorage

## 最佳实践

### 1. 应用拆分原则

- **业务边界清晰**: 按业务模块拆分
- **团队边界清晰**: 按团队职责拆分
- **技术边界清晰**: 按技术栈拆分
- **部署边界清晰**: 按部署频率拆分

### 2. 通信设计

- **最小化通信**: 减少应用间的耦合
- **标准化接口**: 定义统一的通信协议
- **异步通信**: 使用异步方式进行通信
- **错误处理**: 完善的错误处理机制

### 3. 状态管理

- **局部状态**: 应用内部状态自行管理
- **共享状态**: 通过全局状态管理器共享
- **持久化状态**: 使用持久化存储
- **状态同步**: 确保状态的一致性

### 4. 性能优化

- **按需加载**: 只加载当前需要的应用
- **预加载**: 预加载可能访问的应用
- **缓存策略**: 合理的缓存策略
- **资源共享**: 共享公共依赖

## 总结

Micro-Core 通过微内核架构、完善的生命周期管理、灵活的路由系统、强大的沙箱隔离和丰富的通信机制，为构建现代化微前端应用提供了完整的解决方案。

理解这些核心概念将帮助您更好地设计和实现微前端架构，构建可维护、可扩展的大型前端应用。

## 下一步

- [应用管理](./features/app-management.md) - 学习如何管理微应用
- [路由系统](./features/routing.md) - 深入了解路由配置
- [沙箱隔离](./features/sandbox.md) - 掌握应用隔离机制
- [应用通信](./features/communication.md) - 实现应用间通信
- [插件开发](./advanced/plugins.md) - 开发自定义插件