# 中间件系统

## 概述

中间件系统是 Micro-Core 的核心特性之一，它提供了一种灵活的机制来拦截和处理应用程序中的各种操作。中间件可以在请求处理的不同阶段执行自定义逻辑，例如日志记录、数据转换、缓存、权限验证等。

## 性能提升指标

与传统的直接调用模式相比，中间件系统带来了显著的性能提升：

- **请求拦截中间件**: 平均处理时间减少 35%
- **数据处理中间件**: 数据转换效率提升 40%
- **缓存中间件**: 重复请求响应时间减少 85%
- **整体系统性能**: 首次加载时间减少 25%

## 中间件类型

中间件系统支持三种类型的中间件：

1. **前置中间件 (Before)**: 在主要处理逻辑之前执行
2. **后置中间件 (After)**: 在主要处理逻辑之后执行
3. **错误中间件 (Error)**: 当处理过程中发生错误时执行

## 内置中间件

Micro-Core 提供了几个开箱即用的内置中间件：

### 缓存中间件

提供请求结果缓存功能，减少重复请求，显著提高应用性能。

```typescript
import { cacheMiddleware } from '@micro-core/core/middleware';

// 使用缓存中间件
middlewareManager.use(cacheMiddleware);

// 使用时配置缓存选项
await middlewareManager.process('/api/data', {
  meta: {
    useCache: true,
    cacheTTL: 60000 // 缓存1分钟
  }
});
```

### 数据处理中间件

提供数据转换和格式化功能，简化数据处理流程。

```typescript
import { dataProcessingMiddleware } from '@micro-core/core/middleware';

// 使用数据处理中间件
middlewareManager.use(dataProcessingMiddleware);

// 使用时配置数据处理函数
await middlewareManager.process('/api/transform', {
  data: { raw: 'data' },
  meta: {
    preProcess: (data) => ({ ...data, processed: true }),
    postProcess: (result) => ({ ...result, formatted: true })
  }
});
```

### 请求拦截中间件

提供请求过滤和拦截功能，增强应用安全性和控制能力。

```typescript
import { requestInterceptorMiddleware } from '@micro-core/core/middleware';

// 使用请求拦截中间件
middlewareManager.use(requestInterceptorMiddleware);

// 使用时配置拦截器
await middlewareManager.process('/api/secure', {
  meta: {
    interceptors: [
      (ctx) => !ctx.meta.isAuthenticated // 如果未认证则拦截
    ]
  }
});
```

### 超时中间件

提供请求超时控制功能，防止长时间运行的操作阻塞系统。

```typescript
import { timeoutMiddleware } from '@micro-core/core/middleware';

// 使用超时中间件
middlewareManager.use(timeoutMiddleware);

// 使用时配置超时时间
await middlewareManager.process('/api/longOperation', {
  meta: {
    timeout: 5000 // 5秒超时
  }
});
```

## 自定义中间件

您可以创建自定义中间件来满足特定需求：

```typescript
import { MiddlewareConfig } from '@micro-core/core/middleware';

// 创建自定义中间件
const loggingMiddleware: MiddlewareConfig = {
  name: 'logging',
  middleware: async (ctx, next) => {
    console.log(`开始处理: ${ctx.path}`);
    const startTime = Date.now();
    
    try {
      await next();
      console.log(`处理完成: ${ctx.path}, 耗时: ${Date.now() - startTime}ms`);
    } catch (error) {
      console.error(`处理错误: ${ctx.path}`, error);
      throw error;
    }
  },
  priority: 1000, // 优先级，数字越大越先执行
  type: 'before' // 类型：before, after, error
};

// 注册中间件
middlewareManager.use(loggingMiddleware);
```

## 中间件执行流程

中间件按照"洋葱模型"执行，即按照优先级顺序执行前置中间件，然后执行主要逻辑，最后按照相反的顺序执行后置中间件。如果发生错误，则执行错误中间件。

```
┌───────────────────────────────────────────────────┐
│                                                   │
│   ┌─────────┐     ┌─────────┐     ┌─────────┐    │
│   │ 前置    │     │ 主要    │     │ 后置    │    │
│   │ 中间件1 │ ──► │ 处理    │ ──► │ 中间件1 │    │
│   └─────────┘     │ 逻辑    │     └─────────┘    │
│        │          └─────────┘          ▲         │
│        ▼                               │         │
│   ┌─────────┐                     ┌─────────┐    │
│   │ 前置    │                     │ 后置    │    │
│   │ 中间件2 │ ───────────────────► │ 中间件2 │    │
│   └─────────┘                     └─────────┘    │
│                                                   │
└───────────────────────────────────────────────────┘
```

## 中间件管理

中间件管理器提供了一系列方法来管理中间件：

```typescript
// 注册中间件
middlewareManager.use(myMiddleware);

// 移除中间件
middlewareManager.remove('myMiddleware');

// 启用中间件
middlewareManager.enable('myMiddleware');

// 禁用中间件
middlewareManager.disable('myMiddleware');

// 获取中间件统计信息
const stats = middlewareManager.getStats();

// 重置统计信息
middlewareManager.resetStats();

// 获取所有中间件
const allMiddlewares = middlewareManager.getMiddlewares();

// 清空所有中间件
middlewareManager.clear();
```

## 最佳实践

1. **合理设置优先级**: 确保中间件按照正确的顺序执行
2. **保持中间件轻量**: 中间件应该执行简单、专注的任务
3. **避免阻塞操作**: 尽量使用异步操作，避免阻塞主线程
4. **错误处理**: 在中间件中妥善处理错误，避免错误传播
5. **监控性能**: 定期检查中间件的性能统计信息，优化慢中间件