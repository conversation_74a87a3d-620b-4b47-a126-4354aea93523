# 构建集成

Micro-Core 支持多种构建工具，包括 Webpack、Vite、Rollup、esbuild、Rspack、Parcel 和 Turbopack。本指南将帮助您将 Micro-Core 集成到现有的构建流程中。

## 支持的构建工具

### Webpack 集成

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      // 微应用配置
      apps: [
        {
          name: 'app1',
          entry: './src/apps/app1/index.js',
          template: './src/apps/app1/index.html'
        }
      ],
      // 主应用配置
      host: {
        entry: './src/host/index.js',
        template: './src/host/index.html'
      }
    })
  ]
}
```

### Vite 集成

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/vite-plugin'

export default defineConfig({
  plugins: [
    microCore({
      // 微应用配置
      apps: {
        app1: {
          entry: './src/apps/app1/main.js',
          formats: ['es', 'umd']
        },
        app2: {
          entry: './src/apps/app2/main.js',
          formats: ['es']
        }
      },
      // 构建优化
      optimization: {
        splitChunks: true,
        minify: true
      }
    })
  ]
})
```

### Rollup 集成

```javascript
// rollup.config.js
import { microCore } from '@micro-core/rollup-plugin'

export default {
  plugins: [
    microCore({
      apps: ['app1', 'app2'],
      output: {
        format: 'es',
        dir: 'dist'
      }
    })
  ]
}
```

## 构建配置

### 基础配置

```typescript
interface BuildConfig {
  // 应用配置
  apps: AppConfig[]
  
  // 输出配置
  output: {
    dir: string
    format: 'es' | 'umd' | 'cjs'
    filename: string
  }
  
  // 优化配置
  optimization: {
    splitChunks: boolean
    minify: boolean
    treeshake: boolean
  }
  
  // 开发配置
  dev: {
    port: number
    host: string
    https: boolean
  }
}
```

### 应用配置

```typescript
interface AppConfig {
  name: string
  entry: string
  template?: string
  publicPath?: string
  externals?: Record<string, string>
  shared?: SharedConfig
}

interface SharedConfig {
  react?: string
  vue?: string
  'react-dom'?: string
  '@micro-core/core'?: string
}
```

## 开发环境配置

### 热更新支持

```javascript
// webpack.config.js
module.exports = {
  devServer: {
    hot: true,
    port: 3000,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  },
  plugins: [
    new MicroCoreWebpackPlugin({
      dev: {
        hotReload: true,
        liveReload: true
      }
    })
  ]
}
```

### 代理配置

```javascript
// vite.config.js
export default defineConfig({
  server: {
    proxy: {
      '/api': 'http://localhost:8080',
      '/micro-app': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/micro-app/, '')
      }
    }
  }
})
```

## 生产环境优化

### 代码分割

```javascript
// webpack.config.js
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        },
        microCore: {
          test: /[\\/]node_modules[\\/]@micro-core[\\/]/,
          name: 'micro-core',
          chunks: 'all'
        }
      }
    }
  }
}
```

### 资源优化

```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'micro-core': ['@micro-core/core'],
          'vendor': ['react', 'react-dom', 'vue']
        }
      }
    },
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
})
```

## 多环境配置

### 环境变量

```javascript
// .env.development
VITE_MICRO_CORE_ENV=development
VITE_API_BASE_URL=http://localhost:8080
VITE_MICRO_APP_BASE_URL=http://localhost:3000

// .env.production
VITE_MICRO_CORE_ENV=production
VITE_API_BASE_URL=https://api.example.com
VITE_MICRO_APP_BASE_URL=https://micro.example.com
```

### 配置文件

```javascript
// config/webpack.dev.js
const { merge } = require('webpack-merge')
const common = require('./webpack.common.js')

module.exports = merge(common, {
  mode: 'development',
  devtool: 'eval-source-map',
  devServer: {
    hot: true,
    port: 3000
  }
})

// config/webpack.prod.js
const { merge } = require('webpack-merge')
const common = require('./webpack.common.js')

module.exports = merge(common, {
  mode: 'production',
  optimization: {
    minimize: true
  }
})
```

## CI/CD 集成

### GitHub Actions

```yaml
# .github/workflows/build.yml
name: Build and Deploy

on:
  push:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build applications
      run: npm run build
    
    - name: Run tests
      run: npm test
    
    - name: Deploy
      run: npm run deploy
```

### Docker 构建

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

## 性能优化

### 构建性能

```javascript
// webpack.config.js
module.exports = {
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    }
  },
  
  optimization: {
    usedExports: true,
    sideEffects: false
  },
  
  resolve: {
    symlinks: false
  }
}
```

### 运行时性能

```javascript
// 预加载配置
const microCore = new MicroCore({
  preload: {
    apps: ['app1', 'app2'],
    strategy: 'idle'
  },
  
  cache: {
    enabled: true,
    strategy: 'memory'
  }
})
```

## 故障排除

### 常见构建问题

1. **模块解析失败**
   ```javascript
   // 解决方案：配置模块别名
   resolve: {
     alias: {
       '@': path.resolve(__dirname, 'src'),
       '@micro-core': path.resolve(__dirname, 'node_modules/@micro-core')
     }
   }
   ```

2. **跨域问题**
   ```javascript
   // 解决方案：配置 CORS 头
   devServer: {
     headers: {
       'Access-Control-Allow-Origin': '*',
       'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
       'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
     }
   }
   ```

3. **构建体积过大**
   ```javascript
   // 解决方案：启用代码分割和 Tree Shaking
   optimization: {
     splitChunks: {
       chunks: 'all'
     },
     usedExports: true,
     sideEffects: false
   }
   ```

## 最佳实践

1. **使用 TypeScript**：提供更好的类型安全和开发体验
2. **启用 Source Map**：便于调试和错误追踪
3. **配置 ESLint 和 Prettier**：保持代码质量和一致性
4. **使用环境变量**：管理不同环境的配置
5. **监控构建性能**：定期优化构建时间和产物大小

## 参考资料

- [Webpack 插件文档](/ecosystem/webpack-plugin)
- [Vite 插件文档](/ecosystem/vite-plugin)
- [构建优化指南](/guide/performance)
- [部署指南](/guide/deployment)
