# 插件系统

Micro-Core 采用插件化架构设计，核心功能通过插件系统实现，提供了强大的扩展能力和灵活性。

## 📋 目录

- [插件概述](#插件概述)
- [内置插件](#内置插件)
- [插件开发](#插件开发)
- [插件配置](#插件配置)
- [插件生命周期](#插件生命周期)
- [最佳实践](#最佳实践)

## 插件概述

### 🎯 设计理念

Micro-Core 的插件系统基于以下设计理念：

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 插件架构                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    应用层                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ React App   │  │ Vue App     │  │ Angular App         │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    插件层                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 路由插件     │  │ 通信插件     │  │ 认证插件             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 监控插件     │  │ 日志插件     │  │ 自定义插件           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    核心层                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 插件管理器   │  │ 事件总线     │  │ 生命周期管理器       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 🔧 核心特性

- **🎯 按需加载**: 只加载需要的插件，保持核心轻量
- **🔌 热插拔**: 支持运行时动态加载和卸载插件
- **🎨 可组合**: 插件之间可以相互依赖和组合
- **🛡️ 隔离性**: 插件之间相互隔离，避免冲突
- **📊 可观测**: 提供插件状态监控和调试能力

## 内置插件

### 1. 路由插件 (RouterPlugin)

统一管理微前端应用的路由。

```typescript
import { RouterPlugin } from '@micro-core/plugin-router';

const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/',
  
  // 路由守卫
  beforeEach: (to, from, next) => {
    // 权限检查
    if (to.meta?.requiresAuth && !isAuthenticated()) {
      next('/login');
    } else {
      next();
    }
  },
  
  // 路由缓存
  cache: {
    enabled: true,
    maxSize: 10
  }
});

microCore.use(routerPlugin);
```

### 2. 通信插件 (CommunicationPlugin)

提供应用间通信能力。

```typescript
import { CommunicationPlugin } from '@micro-core/plugin-communication';

const communicationPlugin = new CommunicationPlugin({
  // 事件总线配置
  eventBus: {
    maxListeners: 100,
    enableWildcard: true
  },
  
  // 全局状态配置
  globalState: {
    persistent: true,
    storage: 'localStorage'
  },
  
  // 通信中间件
  middleware: [
    // 日志中间件
    (event, data, next) => {
      console.log(`[Communication] ${event}:`, data);
      next();
    },
    
    // 权限中间件
    (event, data, next) => {
      if (event.startsWith('admin:') && !hasAdminPermission()) {
        throw new Error('Permission denied');
      }
      next();
    }
  ]
});

microCore.use(communicationPlugin);
```

### 3. 认证插件 (AuthPlugin)

统一身份认证和权限管理。

```typescript
import { AuthPlugin } from '@micro-core/plugin-auth';

const authPlugin = new AuthPlugin({
  // 认证配置
  auth: {
    tokenKey: 'access_token',
    refreshTokenKey: 'refresh_token',
    tokenExpiry: 3600000, // 1小时
    
    // 自动刷新
    autoRefresh: true,
    refreshThreshold: 300000 // 5分钟前刷新
  },
  
  // 权限配置
  permissions: {
    roles: ['admin', 'user', 'guest'],
    resources: ['user', 'order', 'product'],
    actions: ['create', 'read', 'update', 'delete']
  },
  
  // 登录回调
  onLogin: (user) => {
    console.log('用户登录:', user);
    // 同步用户信息到全局状态
    microCore.getGlobalState().set('user', user);
  },
  
  // 登出回调
  onLogout: () => {
    console.log('用户登出');
    // 清理用户信息
    microCore.getGlobalState().remove('user');
  }
});

microCore.use(authPlugin);
```

### 4. 监控插件 (MonitorPlugin)

应用性能监控和错误追踪。

```typescript
import { MonitorPlugin } from '@micro-core/plugin-monitor';

const monitorPlugin = new MonitorPlugin({
  // 性能监控
  performance: {
    enabled: true,
    sampleRate: 0.1, // 10% 采样率
    
    // 监控指标
    metrics: [
      'app-load-time',
      'app-mount-time',
      'route-change-time',
      'memory-usage'
    ]
  },
  
  // 错误监控
  error: {
    enabled: true,
    maxErrors: 100,
    
    // 错误过滤
    filter: (error) => {
      // 忽略网络错误
      return !error.message.includes('Network Error');
    }
  },
  
  // 上报配置
  report: {
    endpoint: '/api/monitor',
    interval: 30000, // 30秒上报一次
    batchSize: 50
  }
});

microCore.use(monitorPlugin);
```

## 插件开发

### 1. 插件基础结构

```typescript
import { Plugin, PluginContext } from '@micro-core/core';

export class MyPlugin implements Plugin {
  name = 'my-plugin';
  version = '1.0.0';
  
  private context: PluginContext;
  private config: MyPluginConfig;
  
  constructor(config: MyPluginConfig = {}) {
    this.config = config;
  }
  
  // 插件安装
  install(context: PluginContext) {
    this.context = context;
    
    // 注册服务
    context.registerService('myService', new MyService());
    
    // 监听事件
    context.eventBus.on('app:mounted', this.onAppMounted.bind(this));
    
    // 添加中间件
    context.addMiddleware('beforeMount', this.beforeMountMiddleware.bind(this));
  }
  
  // 插件卸载
  uninstall() {
    // 清理资源
    this.context.eventBus.off('app:mounted', this.onAppMounted);
    this.context.removeService('myService');
  }
  
  private onAppMounted(app: MicroApp) {
    console.log(`应用 ${app.name} 已挂载`);
  }
  
  private beforeMountMiddleware(app: MicroApp, next: Function) {
    // 挂载前的处理逻辑
    console.log(`准备挂载应用: ${app.name}`);
    next();
  }
}

// 插件配置接口
interface MyPluginConfig {
  enabled?: boolean;
  options?: Record<string, any>;
}
```

### 2. 插件服务

```typescript
export class MyService {
  private data: Map<string, any> = new Map();
  
  set(key: string, value: any) {
    this.data.set(key, value);
  }
  
  get(key: string) {
    return this.data.get(key);
  }
  
  has(key: string) {
    return this.data.has(key);
  }
  
  delete(key: string) {
    return this.data.delete(key);
  }
  
  clear() {
    this.data.clear();
  }
}
```

### 3. 插件中间件

```typescript
export class MiddlewarePlugin implements Plugin {
  name = 'middleware-plugin';
  
  install(context: PluginContext) {
    // 应用加载中间件
    context.addMiddleware('beforeLoad', async (app, next) => {
      console.log(`开始加载应用: ${app.name}`);
      const startTime = Date.now();
      
      await next();
      
      const loadTime = Date.now() - startTime;
      console.log(`应用 ${app.name} 加载完成，耗时: ${loadTime}ms`);
    });
    
    // 应用挂载中间件
    context.addMiddleware('beforeMount', async (app, next) => {
      // 预处理逻辑
      await this.preprocess(app);
      
      await next();
      
      // 后处理逻辑
      await this.postprocess(app);
    });
  }
  
  private async preprocess(app: MicroApp) {
    // 预处理逻辑，如权限检查、数据预加载等
  }
  
  private async postprocess(app: MicroApp) {
    // 后处理逻辑，如统计上报、状态同步等
  }
}
```

## 插件配置

### 1. 全局配置

```typescript
import { MicroCore } from '@micro-core/core';
import { RouterPlugin, CommunicationPlugin, AuthPlugin } from '@micro-core/plugins';

const microCore = new MicroCore({
  // 插件配置
  plugins: [
    // 路由插件
    {
      plugin: RouterPlugin,
      config: {
        mode: 'history',
        base: '/app'
      }
    },
    
    // 通信插件
    {
      plugin: CommunicationPlugin,
      config: {
        eventBus: { maxListeners: 200 },
        globalState: { persistent: true }
      }
    },
    
    // 认证插件
    {
      plugin: AuthPlugin,
      config: {
        tokenKey: 'jwt_token',
        autoRefresh: true
      }
    }
  ]
});
```

### 2. 动态配置

```typescript
// 运行时添加插件
const monitorPlugin = new MonitorPlugin({
  performance: { enabled: true },
  error: { enabled: true }
});

microCore.use(monitorPlugin);

// 运行时移除插件
microCore.unuse('monitor-plugin');

// 获取插件实例
const router = microCore.getPlugin('router-plugin');
router.navigate('/dashboard');

// 检查插件状态
if (microCore.hasPlugin('auth-plugin')) {
  const auth = microCore.getPlugin('auth-plugin');
  console.log('当前用户:', auth.getCurrentUser());
}
```

## 插件生命周期

### 1. 生命周期钩子

```typescript
export class LifecyclePlugin implements Plugin {
  name = 'lifecycle-plugin';
  
  // 插件安装前
  beforeInstall?(context: PluginContext) {
    console.log('插件即将安装');
  }
  
  // 插件安装
  install(context: PluginContext) {
    console.log('插件正在安装');
    
    // 注册生命周期钩子
    context.addHook('app:beforeLoad', this.onBeforeLoad);
    context.addHook('app:loaded', this.onLoaded);
    context.addHook('app:beforeMount', this.onBeforeMount);
    context.addHook('app:mounted', this.onMounted);
    context.addHook('app:beforeUnmount', this.onBeforeUnmount);
    context.addHook('app:unmounted', this.onUnmounted);
  }
  
  // 插件安装后
  afterInstall?(context: PluginContext) {
    console.log('插件安装完成');
  }
  
  // 插件卸载前
  beforeUninstall?() {
    console.log('插件即将卸载');
  }
  
  // 插件卸载
  uninstall() {
    console.log('插件正在卸载');
    // 清理资源
  }
  
  // 插件卸载后
  afterUninstall?() {
    console.log('插件卸载完成');
  }
  
  private onBeforeLoad = (app: MicroApp) => {
    console.log(`应用 ${app.name} 即将加载`);
  }
  
  private onLoaded = (app: MicroApp) => {
    console.log(`应用 ${app.name} 加载完成`);
  }
  
  private onBeforeMount = (app: MicroApp) => {
    console.log(`应用 ${app.name} 即将挂载`);
  }
  
  private onMounted = (app: MicroApp) => {
    console.log(`应用 ${app.name} 挂载完成`);
  }
  
  private onBeforeUnmount = (app: MicroApp) => {
    console.log(`应用 ${app.name} 即将卸载`);
  }
  
  private onUnmounted = (app: MicroApp) => {
    console.log(`应用 ${app.name} 卸载完成`);
  }
}
```

### 2. 生命周期流程

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    插件生命周期流程                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ beforeInstall   │───▶│ install         │───▶│ afterInstall    ││
│  │ 安装前钩子       │    │ 插件安装         │    │ 安装后钩子       ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│                                   │                               │
│                                   ▼                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    插件运行时                               │ │
│  │  • 处理应用生命周期事件                                     │ │
│  │  • 提供服务和中间件                                         │ │
│  │  • 响应用户操作                                             │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                   │                               │
│                                   ▼                               │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ beforeUninstall │───▶│ uninstall       │───▶│ afterUninstall  ││
│  │ 卸载前钩子       │    │ 插件卸载         │    │ 卸载后钩子       ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## 最佳实践

### 1. 插件设计原则

```typescript
// ✅ 好的插件设计
export class GoodPlugin implements Plugin {
  name = 'good-plugin';
  
  // 明确的依赖声明
  dependencies = ['router-plugin'];
  
  // 可选的依赖
  optionalDependencies = ['auth-plugin'];
  
  constructor(private config: GoodPluginConfig) {
    // 配置验证
    this.validateConfig(config);
  }
  
  install(context: PluginContext) {
    // 检查依赖
    if (!context.hasPlugin('router-plugin')) {
      throw new Error('GoodPlugin requires router-plugin');
    }
    
    // 优雅的错误处理
    try {
      this.setupServices(context);
    } catch (error) {
      console.error('Failed to setup services:', error);
      throw error;
    }
  }
  
  uninstall() {
    // 完整的资源清理
    this.cleanup();
  }
  
  private validateConfig(config: GoodPluginConfig) {
    if (!config.apiEndpoint) {
      throw new Error('apiEndpoint is required');
    }
  }
  
  private setupServices(context: PluginContext) {
    // 服务设置逻辑
  }
  
  private cleanup() {
    // 清理逻辑
  }
}
```

### 2. 插件通信

```typescript
// 插件间通信示例
export class PluginA implements Plugin {
  name = 'plugin-a';
  
  install(context: PluginContext) {
    // 发布服务
    context.registerService('serviceA', new ServiceA());
    
    // 发布事件
    context.eventBus.emit('plugin-a:ready', { version: '1.0.0' });
  }
}

export class PluginB implements Plugin {
  name = 'plugin-b';
  dependencies = ['plugin-a'];
  
  install(context: PluginContext) {
    // 使用其他插件的服务
    const serviceA = context.getService('serviceA');
    
    // 监听其他插件的事件
    context.eventBus.on('plugin-a:ready', (data) => {
      console.log('Plugin A is ready:', data);
    });
  }
}
```

### 3. 插件测试

```typescript
// 插件单元测试
describe('MyPlugin', () => {
  let plugin: MyPlugin;
  let mockContext: PluginContext;
  
  beforeEach(() => {
    mockContext = createMockPluginContext();
    plugin = new MyPlugin({ enabled: true });
  });
  
  afterEach(() => {
    plugin.uninstall();
  });
  
  it('should install successfully', () => {
    expect(() => plugin.install(mockContext)).not.toThrow();
    expect(mockContext.hasService('myService')).toBe(true);
  });
  
  it('should handle app mounted event', () => {
    plugin.install(mockContext);
    
    const mockApp = createMockApp('test-app');
    mockContext.eventBus.emit('app:mounted', mockApp);
    
    // 验证事件处理逻辑
  });
  
  it('should cleanup resources on uninstall', () => {
    plugin.install(mockContext);
    plugin.uninstall();
    
    expect(mockContext.hasService('myService')).toBe(false);
  });
});

// 插件集成测试
describe('Plugin Integration', () => {
  let microCore: MicroCore;
  
  beforeEach(() => {
    microCore = new MicroCore();
  });
  
  it('should work with other plugins', async () => {
    microCore.use(new RouterPlugin());
    microCore.use(new MyPlugin());
    
    await microCore.start();
    
    // 验证插件协作
  });
});
```

### 4. 插件文档

```typescript
/**
 * MyPlugin - 示例插件
 * 
 * @description 这是一个示例插件，展示了插件开发的最佳实践
 * @version 1.0.0
 * <AUTHOR> Name
 * 
 * @example
 * ```typescript
 * import { MyPlugin } from '@my-org/micro-core-plugin-my';
 * 
 * const plugin = new MyPlugin({
 *   enabled: true,
 *   apiEndpoint: 'https://api.example.com'
 * });
 * 
 * microCore.use(plugin);
 * ```
 */
export class MyPlugin implements Plugin {
  /**
   * 插件名称
   */
  readonly name = 'my-plugin';
  
  /**
   * 插件版本
   */
  readonly version = '1.0.0';
  
  /**
   * 插件依赖
   */
  readonly dependencies = ['router-plugin'];
  
  /**
   * 创建插件实例
   * @param config 插件配置
   */
  constructor(config: MyPluginConfig) {
    // 构造函数实现
  }
  
  /**
   * 安装插件
   * @param context 插件上下文
   */
  install(context: PluginContext): void {
    // 安装逻辑
  }
  
  /**
   * 卸载插件
   */
  uninstall(): void {
    // 卸载逻辑
  }
}

/**
 * 插件配置接口
 */
export interface MyPluginConfig {
  /** 是否启用插件 */
  enabled?: boolean;
  
  /** API 端点 */
  apiEndpoint: string;
  
  /** 其他选项 */
  options?: {
    /** 超时时间 */
    timeout?: number;
    
    /** 重试次数 */
    retries?: number;
  };
}
```

## 相关链接

- [插件 API 参考](/api/plugins/)
- [内置插件文档](/ecosystem/plugins)
- [插件开发指南](/guide/advanced/plugin-development)
- [插件示例项目](https://github.com/micro-core/plugin-examples)

---

通过插件系统，Micro-Core 提供了强大的扩展能力，让你可以根据项目需求灵活组合功能，构建适合自己的微前端解决方案。