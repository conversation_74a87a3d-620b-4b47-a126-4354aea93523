{"hash": "c8103361", "configHash": "f02f7219", "lockfileHash": "962a6d78", "browserHash": "c123b468", "optimized": {"vue": {"src": "../../../node_modules/.pnpm/vue@3.5.18_typescript@5.8.3/node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "f35a41e7", "needsInterop": false}, "vitepress > @vue/devtools-api": {"src": "../../../node_modules/.pnpm/@vue+devtools-api@7.7.7/node_modules/@vue/devtools-api/dist/index.js", "file": "vitepress___@vue_devtools-api.js", "fileHash": "e1216f89", "needsInterop": false}, "vitepress > @vueuse/core": {"src": "../../../node_modules/.pnpm/@vueuse+core@13.5.0_vue@3.5.18_typescript@5.8.3_/node_modules/@vueuse/core/index.mjs", "file": "vitepress___@vueuse_core.js", "fileHash": "558b3770", "needsInterop": false}, "vitepress > @vueuse/integrations/useFocusTrap": {"src": "../../../node_modules/.pnpm/@vueuse+integrations@13.5.0_focus-trap@7.6.5_vue@3.5.18_typescript@5.8.3_/node_modules/@vueuse/integrations/useFocusTrap.mjs", "file": "vitepress___@vueuse_integrations_useFocusTrap.js", "fileHash": "a6961428", "needsInterop": false}, "vitepress > mark.js/src/vanilla.js": {"src": "../../../node_modules/.pnpm/mark.js@8.11.1/node_modules/mark.js/src/vanilla.js", "file": "vitepress___mark__js_src_vanilla__js.js", "fileHash": "015446bc", "needsInterop": false}, "vitepress > minisearch": {"src": "../../../node_modules/.pnpm/minisearch@7.1.2/node_modules/minisearch/dist/es/index.js", "file": "vitepress___minisearch.js", "fileHash": "82d18404", "needsInterop": false}}, "chunks": {"chunk-OYVFD5CH": {"file": "chunk-OYVFD5CH.js"}, "chunk-4XCGBR3I": {"file": "chunk-4XCGBR3I.js"}}}