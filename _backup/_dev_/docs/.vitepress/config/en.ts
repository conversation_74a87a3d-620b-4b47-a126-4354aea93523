import { createRequire } from 'module'
import { defineConfig } from 'vitepress'

const require = createRequire(import.meta.url)
const pkg = require('../../package.json')

export const en = defineConfig({
    lang: 'en-US',
    description: 'Next-Generation Micro-Frontend Architecture - Micro-Kernel + Sidecar Pattern + Multi-Project Design',

    themeConfig: {
        // Navigation
        nav: [
            { text: 'Guide', link: '/en/guide/getting-started', activeMatch: '/en/guide/' },
            { text: 'API', link: '/en/api/', activeMatch: '/en/api/' },
            { text: 'Examples', link: '/en/examples/', activeMatch: '/en/examples/' },
            { text: 'Ecosystem', link: '/en/ecosystem/', activeMatch: '/en/ecosystem/' },
            {
                text: 'More',
                items: [
                    { text: 'Migration', link: '/en/migration/' },
                    { text: 'Playground', link: '/en/playground/' },
                    { text: 'Changelog', link: '/en/CHANGELOG' },
                    { text: 'Contributing', link: '/en/CONTRIBUTING' }
                ]
            },
            {
                text: `v${pkg.version}`,
                items: [
                    { text: 'Changelog', link: '/en/CHANGELOG' },
                    { text: 'Contributing', link: '/en/CONTRIBUTING' }
                ]
            }
        ],

        // Sidebar
        sidebar: {
            '/en/guide/': [
                {
                    text: 'Getting Started',
                    collapsed: false,
                    items: [
                        { text: 'Introduction', link: '/en/guide/introduction' },
                        { text: 'Getting Started', link: '/en/guide/getting-started' },
                        { text: 'Installation', link: '/en/guide/installation' },
                        { text: 'Core Concepts', link: '/en/guide/core-concepts' }
                    ]
                },
                {
                    text: 'Core Features',
                    collapsed: false,
                    items: [
                        { text: 'App Management', link: '/en/guide/features/app-management' },
                        { text: 'Lifecycle', link: '/en/guide/features/lifecycle' },
                        { text: 'Sandbox Isolation', link: '/en/guide/features/sandbox' },
                        { text: 'Routing System', link: '/en/guide/features/routing' },
                        { text: 'Communication', link: '/en/guide/features/communication' },
                        { text: 'State Management', link: '/en/guide/features/state-management' }
                    ]
                },
                {
                    text: 'Advanced Features',
                    collapsed: false,
                    items: [
                        { text: 'Adapter System', link: '/en/guide/advanced/adapters' },
                        { text: 'Plugin System', link: '/en/guide/advanced/plugins' },
                        { text: 'Loaders', link: '/en/guide/advanced/loaders' },
                        { text: 'Prefetch', link: '/en/guide/advanced/prefetch' },
                        { text: 'Sidecar Mode', link: '/en/guide/advanced/sidecar-mode' },
                        { text: 'Build Integration', link: '/en/guide/advanced/build-integration' }
                    ]
                },
                {
                    text: 'Best Practices',
                    collapsed: false,
                    items: [
                        { text: 'Architecture Design', link: '/en/guide/best-practices/architecture' },
                        { text: 'Performance', link: '/en/guide/best-practices/performance' },
                        { text: 'Error Handling', link: '/en/guide/best-practices/error-handling' },
                        { text: 'Testing Strategy', link: '/en/guide/best-practices/testing' },
                        { text: 'Deployment', link: '/en/guide/best-practices/deployment' }
                    ]
                }
            ],
            '/en/api/': [
                {
                    text: 'Core API',
                    collapsed: false,
                    items: [
                        { text: 'Overview', link: '/en/api/' },
                        { text: 'Core Module', link: '/en/api/core' },
                        { text: 'App Management', link: '/en/api/app-management' },
                        { text: 'Routing System', link: '/en/api/routing' },
                        { text: 'Event Bus', link: '/en/api/event-bus' },
                        { text: 'State Management', link: '/en/api/state-management' },
                        { text: 'Sandbox System', link: '/en/api/sandbox' },
                        { text: 'Loader', link: '/en/api/loader' },
                        { text: 'Communication', link: '/en/api/communication' }
                    ]
                },
                {
                    text: 'Plugin API',
                    collapsed: false,
                    items: [
                        { text: 'Plugin Base', link: '/en/api/plugins/base' },
                        { text: 'Router Plugin', link: '/en/api/plugins/router' },
                        { text: 'Communication Plugin', link: '/en/api/plugins/communication' },
                        { text: 'Auth Plugin', link: '/en/api/plugins/auth' }
                    ]
                },
                {
                    text: 'Adapter API',
                    collapsed: false,
                    items: [
                        { text: 'Adapter Base', link: '/en/api/adapters/base' },
                        { text: 'React Adapter', link: '/en/api/adapters/react' },
                        { text: 'Vue Adapter', link: '/en/api/adapters/vue' },
                        { text: 'Angular Adapter', link: '/en/api/adapters/angular' }
                    ]
                }
            ],
            '/en/examples/': [
                {
                    text: 'Basic Examples',
                    collapsed: false,
                    items: [
                        { text: 'Overview', link: '/en/examples/' },
                        { text: 'Quick Start', link: '/en/examples/basic/quick-start' }
                    ]
                },
                {
                    text: 'Framework Examples',
                    collapsed: false,
                    items: [
                        { text: 'React App', link: '/en/examples/frameworks/react' },
                        { text: 'Vue App', link: '/en/examples/frameworks/vue' },
                        { text: 'Angular App', link: '/en/examples/frameworks/angular' },
                        { text: 'Svelte App', link: '/en/examples/frameworks/svelte' },
                        { text: 'Solid.js App', link: '/en/examples/frameworks/solid' }
                    ]
                },
                {
                    text: 'Advanced Examples',
                    collapsed: false,
                    items: [
                        { text: 'Multi-Framework', link: '/en/examples/advanced/multi-framework' },
                        { text: 'Multi-App Management', link: '/en/examples/advanced/multi-app' },
                        { text: 'Communication', link: '/en/examples/advanced/communication' },
                        { text: 'Shared State', link: '/en/examples/advanced/shared-state' }
                    ]
                }
            ],
            '/en/ecosystem/': [
                {
                    text: 'Ecosystem',
                    collapsed: false,
                    items: [
                        { text: 'Overview', link: '/en/ecosystem/' },
                        { text: 'Adapters', link: '/en/ecosystem/adapters' },
                        { text: 'Plugins', link: '/en/ecosystem/plugins' },
                        { text: 'Build Tools', link: '/en/ecosystem/builders' },
                        { text: 'Sidecar Mode', link: '/en/ecosystem/sidecar' }
                    ]
                },
                {
                    text: 'Official Adapters',
                    collapsed: false,
                    items: [
                        { text: 'React Adapter', link: '/en/ecosystem/adapters/react' },
                        { text: 'Vue Adapter', link: '/en/ecosystem/adapters/vue' },
                        { text: 'Angular Adapter', link: '/en/ecosystem/adapters/angular' }
                    ]
                },
                {
                    text: 'Official Plugins',
                    collapsed: false,
                    items: [
                        { text: 'Router Plugin', link: '/en/ecosystem/plugins/router' },
                        { text: 'Communication Plugin', link: '/en/ecosystem/plugins/communication' },
                        { text: 'Auth Plugin', link: '/en/ecosystem/plugins/auth' }
                    ]
                },
                {
                    text: 'Build Tools',
                    collapsed: false,
                    items: [
                        { text: 'Vite Integration', link: '/en/ecosystem/builders/vite' },
                        { text: 'Webpack Integration', link: '/en/ecosystem/builders/webpack' },
                        { text: 'Rollup Integration', link: '/en/ecosystem/builders/rollup' }
                    ]
                }
            ],
            '/en/migration/': [
                {
                    text: 'Migration Guide',
                    collapsed: false,
                    items: [
                        { text: 'From qiankun', link: '/en/migration/from-qiankun' },
                        { text: 'From single-spa', link: '/en/migration/from-single-spa' },
                        { text: 'From Wujie', link: '/en/migration/from-wujie' },
                        { text: 'Compatibility', link: '/en/migration/general/compatibility' },
                        { text: 'Testing', link: '/en/migration/general/testing' }
                    ]
                }
            ],
            '/en/playground/': [
                {
                    text: 'Playground',
                    collapsed: false,
                    items: [
                        { text: 'Overview', link: '/en/playground/' },
                        { text: 'Basic Example', link: '/en/playground/basic-example' },
                        { text: 'Framework Example', link: '/en/playground/framework-example' },
                        { text: 'Advanced Features', link: '/en/playground/advanced-features' },
                        { text: 'Config Generator', link: '/en/playground/config-generator' },
                        { text: 'Debug Panel', link: '/en/playground/debug-panel' },
                        { text: 'Dev Tools', link: '/en/playground/dev-tools' },
                        { text: 'Performance Test', link: '/en/playground/performance-test' },
                        { text: 'Performance Analysis', link: '/en/playground/performance-analysis' },
                        { text: 'Benchmark', link: '/en/playground/benchmark' },
                        { text: 'Tutorial', link: '/en/playground/tutorial' },
                        { text: 'qiankun Migration', link: '/en/playground/qiankun-migration' },
                        { text: 'Wujie Migration', link: '/en/playground/wujie-migration' }
                    ]
                }
            ]
        },

        // Footer
        footer: {
            message: 'Released under the MIT License',
            copyright: 'Copyright © 2024-present Echo & Micro-Core Contributors'
        },

        // Edit link
        editLink: {
            pattern: 'https://github.com/echo008/micro-core/edit/main/docs/:path',
            text: 'Edit this page on GitHub'
        },

        // Last updated
        lastUpdated: {
            text: 'Last updated',
            formatOptions: {
                dateStyle: 'short',
                timeStyle: 'medium'
            }
        },

        // Doc footer
        docFooter: {
            prev: 'Previous page',
            next: 'Next page'
        },

        // Outline
        outline: {
            level: [2, 3],
            label: 'On this page'
        },

        // Return to top
        returnToTopLabel: 'Return to top',

        // Sidebar menu label
        sidebarMenuLabel: 'Menu',

        // Dark mode switch
        darkModeSwitchLabel: 'Theme',
        lightModeSwitchTitle: 'Switch to light theme',
        darkModeSwitchTitle: 'Switch to dark theme'
    }
})