import { createRequire } from 'module'
import { defineConfig } from 'vitepress'

const require = createRequire(import.meta.url)
const pkg = require('../../package.json')

export const zh = defineConfig({
    lang: 'zh-CN',
    description: '下一代微前端架构解决方案 - 微内核 + 边车模式 + 多工程设计',

    themeConfig: {
        // 导航栏
        nav: [
            { text: '指南', link: '/zh/guide/getting-started', activeMatch: '/zh/guide/' },
            { text: 'API', link: '/zh/api/', activeMatch: '/zh/api/' },
            { text: '示例', link: '/zh/examples/', activeMatch: '/zh/examples/' },
            { text: '生态系统', link: '/zh/ecosystem/', activeMatch: '/zh/ecosystem/' },
            {
                text: '更多',
                items: [
                    { text: '迁移指南', link: '/zh/migration/' },
                    { text: '演练场', link: '/zh/playground/' },
                    { text: '更新日志', link: '/zh/changelog' },
                    { text: '贡献指南', link: '/zh/CONTRIBUTING' }
                ]
            },
            {
                text: `v${pkg.version}`,
                items: [
                    { text: '更新日志', link: '/zh/changelog' },
                    { text: '贡献指南', link: '/zh/CONTRIBUTING' }
                ]
            }
        ],

        // 侧边栏
        sidebar: {
            '/zh/guide/': [
                {
                    text: '开始',
                    collapsed: false,
                    items: [
                        { text: '介绍', link: '/zh/guide/introduction' },
                        { text: '快速开始', link: '/zh/guide/getting-started' },
                        { text: '安装', link: '/zh/guide/installation' },
                        { text: '核心概念', link: '/zh/guide/core-concepts' }
                    ]
                },
                {
                    text: '核心功能',
                    collapsed: false,
                    items: [
                        { text: '应用管理', link: '/zh/guide/features/app-management' },
                        { text: '生命周期', link: '/zh/guide/features/lifecycle' },
                        { text: '沙箱隔离', link: '/zh/guide/features/sandbox' },
                        { text: '路由系统', link: '/zh/guide/features/routing' },
                        { text: '应用通信', link: '/zh/guide/features/communication' },
                        { text: '状态管理', link: '/zh/guide/features/state-management' }
                    ]
                },
                {
                    text: '高级特性',
                    collapsed: false,
                    items: [
                        { text: '适配器系统', link: '/zh/guide/advanced/adapters' },
                        { text: '插件系统', link: '/zh/guide/advanced/plugins' },
                        { text: '加载器', link: '/zh/guide/advanced/loaders' },
                        { text: '预加载', link: '/zh/guide/advanced/prefetch' },
                        { text: '边车模式', link: '/zh/guide/advanced/sidecar-mode' },
                        { text: '构建集成', link: '/zh/guide/advanced/build-integration' }
                    ]
                },
                {
                    text: '最佳实践',
                    collapsed: false,
                    items: [
                        { text: '架构设计', link: '/zh/guide/best-practices/architecture' },
                        { text: '性能优化', link: '/zh/guide/best-practices/performance' },
                        { text: '错误处理', link: '/zh/guide/best-practices/error-handling' },
                        { text: '测试策略', link: '/zh/guide/best-practices/testing' },
                        { text: '部署指南', link: '/zh/guide/best-practices/deployment' }
                    ]
                }
            ],
            '/zh/api/': [
                {
                    text: '核心 API',
                    collapsed: false,
                    items: [
                        { text: '概览', link: '/zh/api/' },
                        { text: '核心模块', link: '/zh/api/core' },
                        { text: '应用管理', link: '/zh/api/app-management' },
                        { text: '路由系统', link: '/zh/api/routing' },
                        { text: '事件总线', link: '/zh/api/event-bus' },
                        { text: '状态管理', link: '/zh/api/state-management' },
                        { text: '沙箱系统', link: '/zh/api/sandbox' },
                        { text: '加载器', link: '/zh/api/loader' },
                        { text: '通信系统', link: '/zh/api/communication' }
                    ]
                },
                {
                    text: '插件 API',
                    collapsed: false,
                    items: [
                        { text: '插件基类', link: '/zh/api/plugins/base' },
                        { text: '路由插件', link: '/zh/api/plugins/router' },
                        { text: '通信插件', link: '/zh/api/plugins/communication' },
                        { text: '认证插件', link: '/zh/api/plugins/auth' }
                    ]
                },
                {
                    text: '适配器 API',
                    collapsed: false,
                    items: [
                        { text: '适配器基类', link: '/zh/api/adapters/base' },
                        { text: 'React 适配器', link: '/zh/api/adapters/react' },
                        { text: 'Vue 适配器', link: '/zh/api/adapters/vue' },
                        { text: 'Angular 适配器', link: '/zh/api/adapters/angular' }
                    ]
                }
            ],
            '/zh/examples/': [
                {
                    text: '基础示例',
                    collapsed: false,
                    items: [
                        { text: '概览', link: '/zh/examples/' },
                        { text: '快速开始', link: '/zh/examples/basic/quick-start' }
                    ]
                },
                {
                    text: '框架示例',
                    collapsed: false,
                    items: [
                        { text: 'React 应用', link: '/zh/examples/frameworks/react' },
                        { text: 'Vue 应用', link: '/zh/examples/frameworks/vue' },
                        { text: 'Angular 应用', link: '/zh/examples/frameworks/angular' },
                        { text: 'Svelte 应用', link: '/zh/examples/frameworks/svelte' },
                        { text: 'Solid.js 应用', link: '/zh/examples/frameworks/solid' }
                    ]
                },
                {
                    text: '高级示例',
                    collapsed: false,
                    items: [
                        { text: '多框架集成', link: '/zh/examples/advanced/multi-framework' },
                        { text: '多应用管理', link: '/zh/examples/advanced/multi-app' },
                        { text: '应用通信', link: '/zh/examples/advanced/communication' },
                        { text: '共享状态', link: '/zh/examples/advanced/shared-state' }
                    ]
                }
            ],
            '/zh/ecosystem/': [
                {
                    text: '生态系统',
                    collapsed: false,
                    items: [
                        { text: '概览', link: '/zh/ecosystem/' },
                        { text: '适配器', link: '/zh/ecosystem/adapters' },
                        { text: '插件', link: '/zh/ecosystem/plugins' },
                        { text: '构建工具', link: '/zh/ecosystem/builders' },
                        { text: '边车模式', link: '/zh/ecosystem/sidecar' }
                    ]
                },
                {
                    text: '官方适配器',
                    collapsed: false,
                    items: [
                        { text: 'React 适配器', link: '/zh/ecosystem/adapters/react' },
                        { text: 'Vue 适配器', link: '/zh/ecosystem/adapters/vue' },
                        { text: 'Angular 适配器', link: '/zh/ecosystem/adapters/angular' }
                    ]
                },
                {
                    text: '官方插件',
                    collapsed: false,
                    items: [
                        { text: '路由插件', link: '/zh/ecosystem/plugins/router' },
                        { text: '通信插件', link: '/zh/ecosystem/plugins/communication' },
                        { text: '认证插件', link: '/zh/ecosystem/plugins/auth' }
                    ]
                },
                {
                    text: '构建工具',
                    collapsed: false,
                    items: [
                        { text: 'Vite 集成', link: '/zh/ecosystem/builders/vite' },
                        { text: 'Webpack 集成', link: '/zh/ecosystem/builders/webpack' },
                        { text: 'Rollup 集成', link: '/zh/ecosystem/builders/rollup' }
                    ]
                }
            ],
            '/zh/migration/': [
                {
                    text: '迁移指南',
                    collapsed: false,
                    items: [
                        { text: '概览', link: '/zh/migration/' },
                        { text: '通用迁移', link: '/zh/migration/general' },
                        { text: '配置迁移', link: '/zh/migration/config-migration' },
                        { text: '从 qiankun 迁移', link: '/zh/migration/qiankun' },
                        { text: '从 Wujie 迁移', link: '/zh/migration/wujie' }
                    ]
                }
            ],
            '/zh/playground/': [
                {
                    text: '演练场',
                    collapsed: false,
                    items: [
                        { text: '概览', link: '/zh/playground/' },
                        { text: '基础示例', link: '/zh/playground/basic-example' },
                        { text: '框架示例', link: '/zh/playground/framework-example' },
                        { text: '高级功能', link: '/zh/playground/advanced-features' },
                        { text: '配置生成器', link: '/zh/playground/config-generator' },
                        { text: '调试面板', link: '/zh/playground/debug-panel' },
                        { text: '开发工具', link: '/zh/playground/dev-tools' },
                        { text: '性能测试', link: '/zh/playground/performance-test' },
                        { text: '性能分析', link: '/zh/playground/performance-analysis' },
                        { text: '基准测试', link: '/zh/playground/benchmark' },
                        { text: '教程', link: '/zh/playground/tutorial' },
                        { text: 'qiankun 迁移', link: '/zh/playground/qiankun-migration' },
                        { text: 'Wujie 迁移', link: '/zh/playground/wujie-migration' }
                    ]
                }
            ]
        },

        // 页脚
        footer: {
            message: '基于 MIT 许可证发布',
            copyright: 'Copyright © 2024-present Echo & Micro-Core Contributors'
        },

        // 编辑链接
        editLink: {
            pattern: 'https://github.com/echo008/micro-core/edit/main/docs/:path',
            text: '在 GitHub 上编辑此页面'
        },

        // 最后更新时间
        lastUpdated: {
            text: '最后更新于',
            formatOptions: {
                dateStyle: 'short',
                timeStyle: 'medium'
            }
        },

        // 文档页脚导航
        docFooter: {
            prev: '上一页',
            next: '下一页'
        },

        // 大纲配置
        outline: {
            level: [2, 3],
            label: '页面导航'
        },

        // 返回顶部
        returnToTopLabel: '回到顶部',

        // 侧边栏菜单标签
        sidebarMenuLabel: '菜单',

        // 深色模式切换标签
        darkModeSwitchLabel: '主题',
        lightModeSwitchTitle: '切换到浅色模式',
        darkModeSwitchTitle: '切换到深色模式'
    }
})