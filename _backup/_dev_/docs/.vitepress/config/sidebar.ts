export const sidebar = {
    zh: {
        '/guide/': [
            {
                text: '开始使用',
                items: [
                    { text: '介绍', link: '/guide/introduction' },
                    { text: '快速开始', link: '/guide/getting-started' },
                    { text: '安装配置', link: '/guide/installation' },
                    { text: '基础概念', link: '/guide/concepts' }
                ]
            },
            {
                text: '核心功能',
                items: [
                    { text: '功能概览', link: '/guide/features/' },
                    { text: '应用管理', link: '/guide/features/app-management' },
                    { text: '路由系统', link: '/guide/features/routing' },
                    { text: '应用间通信', link: '/guide/features/communication' },
                    { text: '状态管理', link: '/guide/features/state-management' },
                    { text: '生命周期', link: '/guide/features/lifecycle' },
                    { text: '沙箱隔离', link: '/guide/features/sandbox' }
                ]
            },
            {
                text: '高级特性',
                items: [
                    { text: '插件系统', link: '/guide/advanced/plugins' },
                    { text: '多框架适配', link: '/guide/advanced/adapters' },
                    { text: '构建集成', link: '/guide/advanced/build-integration' },
                    { text: '边车模式', link: '/guide/advanced/sidecar-mode' },
                    { text: '高性能加载器', link: '/guide/advanced/loaders' },
                    { text: '智能预加载', link: '/guide/advanced/prefetch' }
                ]
            },
            {
                text: '最佳实践',
                items: [
                    { text: '架构设计', link: '/guide/best-practices/architecture' },
                    { text: '性能优化', link: '/guide/best-practices/performance' },
                    { text: '错误处理', link: '/guide/best-practices/error-handling' },
                    { text: '测试策略', link: '/guide/best-practices/testing' },
                    { text: '部署指南', link: '/guide/best-practices/deployment' }
                ]
            }
        ],
        '/api/': [
            {
                text: '核心API',
                items: [
                    { text: 'MicroCore', link: '/api/core' },
                    { text: '应用管理', link: '/api/app-management' },
                    { text: '路由系统', link: '/api/routing' },
                    { text: '通信系统', link: '/api/communication' },
                    { text: '状态管理', link: '/api/state-management' }
                ]
            },
            {
                text: '插件API',
                items: [
                    { text: '插件基类', link: '/api/plugins/base' },
                    { text: '路由插件', link: '/api/plugins/router' },
                    { text: '通信插件', link: '/api/plugins/communication' },
                    { text: '认证插件', link: '/api/plugins/auth' }
                ]
            },
            {
                text: '适配器API',
                items: [
                    { text: '适配器基类', link: '/api/adapters/base' },
                    { text: 'React适配器', link: '/api/adapters/react' },
                    { text: 'Vue适配器', link: '/api/adapters/vue' },
                    { text: 'Angular适配器', link: '/api/adapters/angular' }
                ]
            }
        ],
        '/examples/': [
            {
                text: '基础示例',
                items: [
                    { text: '概览', link: '/examples/' },
                    { text: '基本使用', link: '/examples/basic/' },
                    { text: 'React应用', link: '/examples/frameworks/react' },
                    { text: 'Vue应用', link: '/examples/frameworks/vue' },
                    { text: 'Angular应用', link: '/examples/frameworks/angular' },
                    { text: 'Svelte应用', link: '/examples/frameworks/svelte' },
                    { text: 'Solid应用', link: '/examples/frameworks/solid' }
                ]
            },
            {
                text: '进阶示例',
                items: [
                    { text: '多应用协作', link: '/examples/advanced/multi-app' },
                    { text: '应用间通信', link: '/examples/advanced/communication' },
                    { text: '共享状态', link: '/examples/advanced/shared-state' },
                    { text: '动态路由', link: '/examples/advanced/dynamic-routing' },
                    { text: '高性能加载', link: '/examples/advanced/performance' }
                ]
            }
        ],
        '/migration/': [
            {
                text: 'qiankun 迁移',
                items: [
                    { text: '迁移概述', link: '/migration/qiankun' },
                    { text: 'API 对照表', link: '/migration/qiankun/api-mapping' },
                    { text: '配置迁移', link: '/migration/qiankun/config-migration' },
                    { text: '生命周期迁移', link: '/migration/qiankun/lifecycle-migration' },
                    { text: '通信迁移', link: '/migration/qiankun/communication-migration' },
                    { text: '完整示例', link: '/migration/qiankun/complete-example' }
                ]
            },
            {
                text: 'wujie 迁移',
                items: [
                    { text: '迁移概述', link: '/migration/wujie' },
                    { text: 'API 对照表', link: '/migration/wujie/api-mapping' },
                    { text: '配置迁移', link: '/migration/wujie/config-migration' },
                    { text: '沙箱迁移', link: '/migration/wujie/sandbox-migration' },
                    { text: '通信迁移', link: '/migration/wujie/communication-migration' },
                    { text: '完整示例', link: '/migration/wujie/complete-example' }
                ]
            },
            {
                text: '通用迁移策略',
                items: [
                    { text: '迁移规划', link: '/migration/general' },
                    { text: '渐进式迁移', link: '/migration/general/progressive' },
                    { text: '兼容性处理', link: '/migration/general/compatibility' },
                    { text: '测试策略', link: '/migration/general/testing' }
                ]
            }
        ],
        '/ecosystem/': [
            {
                text: '插件生态',
                items: [
                    { text: '插件概览', link: '/ecosystem/plugins' },
                    { text: '路由插件', link: '/ecosystem/plugins/router' },
                    { text: '通信插件', link: '/ecosystem/plugins/communication' },
                    { text: '认证插件', link: '/ecosystem/plugins/auth' }
                ]
            },
            {
                text: '框架适配器',
                items: [
                    { text: '适配器概览', link: '/ecosystem/adapters' },
                    { text: 'React适配器', link: '/ecosystem/adapters/react' },
                    { text: 'Vue适配器', link: '/ecosystem/adapters/vue' },
                    { text: 'Angular适配器', link: '/ecosystem/adapters/angular' }
                ]
            },
            {
                text: '构建工具',
                items: [
                    { text: '构建工具概览', link: '/ecosystem/builders' },
                    { text: 'Webpack集成', link: '/ecosystem/builders/webpack' },
                    { text: 'Vite集成', link: '/ecosystem/builders/vite' },
                    { text: 'Rollup集成', link: '/ecosystem/builders/rollup' }
                ]
            }
        ]
    },

    en: {
        // 英文侧边栏配置
        '/en/guide/': [
            {
                text: 'Getting Started',
                items: [
                    { text: 'Introduction', link: '/en/guide/introduction' },
                    { text: 'Quick Start', link: '/en/guide/getting-started' },
                    { text: 'Installation', link: '/en/guide/installation' },
                    { text: 'Core Concepts', link: '/en/guide/concepts' }
                ]
            }
            // ... 其他英文配置
        ]
    }
}