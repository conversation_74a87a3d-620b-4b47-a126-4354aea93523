/**
 * Micro-Core 文档自定义样式
 */

/* 自定义 CSS 变量 */
:root {
  /* 品牌色彩 */
  --vp-c-brand-1: #3b82f6;
  --vp-c-brand-2: #2563eb;
  --vp-c-brand-3: #1d4ed8;
  --vp-c-brand-soft: rgba(59, 130, 246, 0.14);

  /* 自定义颜色 */
  --micro-core-primary: #3b82f6;
  --micro-core-secondary: #10b981;
  --micro-core-accent: #f59e0b;
  --micro-core-danger: #ef4444;
  --micro-core-warning: #f59e0b;
  --micro-core-success: #10b981;
  --micro-core-info: #06b6d4;

  /* 渐变色 */
  --micro-core-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --micro-core-gradient-soft: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.1) 100%);
}

/* 深色模式变量 */
.dark {
  --vp-c-brand-1: #60a5fa;
  --vp-c-brand-2: #3b82f6;
  --vp-c-brand-3: #2563eb;
  --vp-c-brand-soft: rgba(96, 165, 250, 0.16);
}

/* Logo 样式优化 */
.VPNavBarTitle .title {
  font-weight: 700;
  background: var(--micro-core-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 导航栏高亮优化 */
.VPNavBarMenuLink.active {
  color: var(--vp-c-brand-1) !important;
  font-weight: 600;
}

/* 侧边栏高亮优化 */
.VPSidebarItem.is-active>.item>.link {
  color: var(--vp-c-brand-1) !important;
  font-weight: 600;
}

.VPSidebarItem.is-active>.item>.link::before {
  content: '';
  position: absolute;
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--vp-c-brand-1);
  border-radius: 2px;
}

/* 首页样式优化 */
.VPHome .VPHero .main {
  max-width: 1152px;
}

.VPHome .VPHero .name {
  background: var(--micro-core-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.VPHome .VPHero .text {
  font-size: 32px;
  line-height: 1.2;
  font-weight: 600;
  color: var(--vp-c-text-1);
}

.VPHome .VPHero .tagline {
  font-size: 18px;
  line-height: 1.4;
  color: var(--vp-c-text-2);
  max-width: 720px;
  margin: 0 auto;
}

/* 特性卡片样式 */
.VPFeatures .VPFeature {
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  background: var(--vp-c-bg-soft);
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.VPFeatures .VPFeature:hover {
  border-color: var(--vp-c-brand-1);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.VPFeatures .VPFeature .icon {
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--vp-c-bg-alt);
  border-radius: 8px;
  flex-shrink: 0;
  margin: 0;
}

.VPFeatures .VPFeature .content {
  flex: 1;
}

.VPFeatures .VPFeature .title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--vp-c-text-1);
}

.VPFeatures .VPFeature .details {
  font-size: 14px;
  line-height: 1.6;
  color: var(--vp-c-text-2);
  margin: 0;
}

/* 代码块样式优化 */
.vp-code-group .tabs {
  border-bottom: 1px solid var(--vp-c-divider);
  background: var(--vp-c-bg-soft);
  border-radius: 8px 8px 0 0;
}

.vp-code-group .tabs label {
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  padding: 8px 16px;
  font-weight: 500;
}

.vp-code-group .tabs label:hover {
  background: var(--vp-c-bg-alt);
}

.vp-code-group .tabs label.active {
  border-bottom-color: var(--vp-c-brand-1);
  color: var(--vp-c-brand-1);
  background: var(--vp-c-bg);
}

.vp-code-group .blocks {
  border-radius: 0 0 8px 8px;
}

/* 自定义容器样式 */
.custom-block {
  border-radius: 8px;
  border-left: 4px solid;
  padding: 16px 20px;
  margin: 16px 0;
}

.custom-block.tip {
  border-left-color: var(--micro-core-info);
  background-color: rgba(6, 182, 212, 0.1);
}

.custom-block.warning {
  border-left-color: var(--micro-core-warning);
  background-color: rgba(245, 158, 11, 0.1);
}

.custom-block.danger {
  border-left-color: var(--micro-core-danger);
  background-color: rgba(239, 68, 68, 0.1);
}

.custom-block.info {
  border-left-color: var(--micro-core-success);
  background-color: rgba(16, 185, 129, 0.1);
}

/* ASCII Art 样式 */
.ascii-art {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.2;
  white-space: pre;
  background: var(--vp-c-bg-soft);
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
}

/* 表格样式优化 */
.vp-doc table {
  border-collapse: collapse;
  margin: 20px 0;
  width: 100%;
}

.vp-doc table th,
.vp-doc table td {
  border: 1px solid var(--vp-c-border);
  padding: 12px 16px;
  text-align: left;
}

.vp-doc table th {
  background: var(--vp-c-bg-soft);
  font-weight: 600;
  color: var(--vp-c-text-1);
}

.vp-doc table tr:nth-child(even) {
  background: var(--vp-c-bg-soft);
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  margin: 0 4px;
}

.badge.new {
  background: var(--micro-core-success);
  color: white;
}

.badge.updated {
  background: var(--micro-core-info);
  color: white;
}

.badge.deprecated {
  background: var(--micro-core-warning);
  color: white;
}

.badge.removed {
  background: var(--micro-core-danger);
  color: white;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .VPHome .VPHero .text {
    font-size: 24px;
  }

  .VPHome .VPHero .tagline {
    font-size: 16px;
  }

  .ascii-art {
    font-size: 10px;
    padding: 12px;
  }
}

/* 打印样式 */
@media print {

  .VPNav,
  .VPSidebar,
  .VPDocFooter {
    display: none !important;
  }

  .VPContent {
    padding-left: 0 !important;
  }
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --vp-c-brand-1: #0066cc;
    --vp-c-brand-2: #0052a3;
    --vp-c-brand-3: #003d7a;
  }

  .dark {
    --vp-c-brand-1: #66b3ff;
    --vp-c-brand-2: #4da6ff;
    --vp-c-brand-3: #3399ff;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--vp-c-bg-soft);
}

::-webkit-scrollbar-thumb {
  background: var(--vp-c-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--vp-c-brand-1);
}

/* 选择文本样式 */
::selection {
  background: rgba(59, 130, 246, 0.2);
  color: var(--vp-c-text-1);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--vp-c-brand-1);
  outline-offset: 2px;
}

/* 链接样式优化 */
.vp-doc a {
  color: var(--vp-c-brand-1);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.3s ease;
}

.vp-doc a:hover {
  border-bottom-color: var(--vp-c-brand-1);
}

/* 代码内联样式 */
.vp-doc :not(pre)>code {
  background: var(--vp-c-bg-soft);
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.875em;
  color: var(--vp-c-text-1);
}

/* 搜索框样式优化 */
.DocSearch-Button {
  border-radius: 8px !important;
  border: 1px solid var(--vp-c-border) !important;
  background: var(--vp-c-bg-soft) !important;
}

.DocSearch-Button:hover {
  border-color: var(--vp-c-brand-1) !important;
  box-shadow: 0 0 0 1px var(--vp-c-brand-1) !important;
}