/**
 * @fileoverview 插件管理器
 * @description 管理插件的注册、加载、卸载等生命周期
 * <AUTHOR> <<EMAIL>>
 */
export declare enum PluginStatus {
    NOT_LOADED = "NOT_LOADED",
    LOADING = "LOADING",
    LOADED = "LOADED",
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    ERROR = "ERROR"
}
export interface PluginLifecycle {
    install?: () => Promise<void> | void;
    uninstall?: () => Promise<void> | void;
    activate?: () => Promise<void> | void;
    deactivate?: () => Promise<void> | void;
}
export interface PluginInfo {
    name: string;
    version: string;
    description?: string;
    status: PluginStatus;
    lifecycle: PluginLifecycle;
    dependencies?: string[];
    loadPromise?: Promise<void>;
}
/**
 * 插件管理器
 * 负责管理插件的生命周期和依赖关系
 */
export declare class PluginManager {
    private plugins;
    private loadOrder;
    /**
     * 注册插件
     */
    registerPlugin(name: string, version: string, lifecycle: PluginLifecycle, options?: {
        description?: string;
        dependencies?: string[];
    }): void;
    /**
     * 获取插件信息
     */
    getPlugin(name: string): PluginInfo | undefined;
    /**
     * 获取插件状态
     */
    getPluginStatus(name: string): PluginStatus | undefined;
    /**
     * 设置插件状态
     */
    setPluginStatus(name: string, status: PluginStatus): void;
    /**
     * 加载插件
     */
    loadPlugin(name: string): Promise<void>;
    /**
     * 激活插件
     */
    activatePlugin(name: string): Promise<void>;
    /**
     * 停用插件
     */
    deactivatePlugin(name: string): Promise<void>;
    /**
     * 卸载插件
     */
    unloadPlugin(name: string): Promise<void>;
    /**
     * 获取所有插件
     */
    getAllPlugins(): PluginInfo[];
    /**
     * 获取活跃插件
     */
    getActivePlugins(): PluginInfo[];
    /**
     * 获取加载顺序
     */
    getLoadOrder(): string[];
    /**
     * 检查插件是否存在
     */
    hasPlugin(name: string): boolean;
    /**
     * 清理所有插件
     */
    clear(): void;
    private loadDependencies;
    private performLoad;
}
//# sourceMappingURL=plugin-manager.d.ts.map