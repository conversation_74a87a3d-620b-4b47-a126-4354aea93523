/**
 * @fileoverview 插件管理器
 * @description 管理插件的注册、加载、卸载等生命周期
 * <AUTHOR> <<EMAIL>>
 */

import { createLogger } from '@micro-core/shared'

const logger = createLogger('PluginManager')

export enum PluginStatus {
    NOT_LOADED = 'NOT_LOADED',
    LOADING = 'LOADING',
    LOADED = 'LOADED',
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    ERROR = 'ERROR'
}

export interface PluginLifecycle {
    install?: () => Promise<void> | void
    uninstall?: () => Promise<void> | void
    activate?: () => Promise<void> | void
    deactivate?: () => Promise<void> | void
}

export interface PluginInfo {
    name: string
    version: string
    description?: string
    status: PluginStatus
    lifecycle: PluginLifecycle
    dependencies?: string[]
    loadPromise?: Promise<void>
}

/**
 * 插件管理器
 * 负责管理插件的生命周期和依赖关系
 */
export class PluginManager {
    private plugins = new Map<string, PluginInfo>()
    private loadOrder: string[] = []

    /**
     * 注册插件
     */
    registerPlugin(
        name: string,
        version: string,
        lifecycle: PluginLifecycle,
        options: {
            description?: string
            dependencies?: string[]
        } = {}
    ): void {
        if (this.plugins.has(name)) {
            logger.warn(`插件 ${name} 已经注册`)
            return
        }

        const plugin: PluginInfo = {
            name,
            version,
            description: options.description,
            status: PluginStatus.NOT_LOADED,
            lifecycle,
            dependencies: options.dependencies || []
        }

        this.plugins.set(name, plugin)
        logger.info(`插件 ${name}@${version} 注册成功`)
    }

    /**
     * 获取插件信息
     */
    getPlugin(name: string): PluginInfo | undefined {
        return this.plugins.get(name)
    }

    /**
     * 获取插件状态
     */
    getPluginStatus(name: string): PluginStatus | undefined {
        return this.plugins.get(name)?.status
    }

    /**
     * 设置插件状态
     */
    setPluginStatus(name: string, status: PluginStatus): void {
        const plugin = this.plugins.get(name)
        if (plugin) {
            plugin.status = status
            logger.debug(`插件 ${name} 状态变更为 ${status}`)
        }
    }

    /**
     * 加载插件
     */
    async loadPlugin(name: string): Promise<void> {
        const plugin = this.plugins.get(name)
        if (!plugin) {
            throw new Error(`插件 ${name} 未注册`)
        }

        if (plugin.status === PluginStatus.LOADED || plugin.status === PluginStatus.ACTIVE) {
            return
        }

        if (plugin.loadPromise) {
            return plugin.loadPromise
        }

        // 检查依赖
        await this.loadDependencies(plugin)

        this.setPluginStatus(name, PluginStatus.LOADING)

        plugin.loadPromise = this.performLoad(plugin)

        try {
            await plugin.loadPromise
            this.setPluginStatus(name, PluginStatus.LOADED)
            this.loadOrder.push(name)
        } catch (error) {
            this.setPluginStatus(name, PluginStatus.ERROR)
            throw error
        }
    }

    /**
     * 激活插件
     */
    async activatePlugin(name: string): Promise<void> {
        const plugin = this.plugins.get(name)
        if (!plugin) {
            throw new Error(`插件 ${name} 未注册`)
        }

        if (plugin.status === PluginStatus.ACTIVE) {
            return
        }

        if (plugin.status !== PluginStatus.LOADED) {
            await this.loadPlugin(name)
        }

        try {
            if (plugin.lifecycle.activate) {
                await plugin.lifecycle.activate()
            }
            this.setPluginStatus(name, PluginStatus.ACTIVE)
            logger.info(`插件 ${name} 激活成功`)
        } catch (error) {
            this.setPluginStatus(name, PluginStatus.ERROR)
            throw error
        }
    }

    /**
     * 停用插件
     */
    async deactivatePlugin(name: string): Promise<void> {
        const plugin = this.plugins.get(name)
        if (!plugin) {
            throw new Error(`插件 ${name} 未注册`)
        }

        if (plugin.status !== PluginStatus.ACTIVE) {
            return
        }

        try {
            if (plugin.lifecycle.deactivate) {
                await plugin.lifecycle.deactivate()
            }
            this.setPluginStatus(name, PluginStatus.INACTIVE)
            logger.info(`插件 ${name} 停用成功`)
        } catch (error) {
            this.setPluginStatus(name, PluginStatus.ERROR)
            throw error
        }
    }

    /**
     * 卸载插件
     */
    async unloadPlugin(name: string): Promise<void> {
        const plugin = this.plugins.get(name)
        if (!plugin) {
            throw new Error(`插件 ${name} 未注册`)
        }

        if (plugin.status === PluginStatus.ACTIVE) {
            await this.deactivatePlugin(name)
        }

        try {
            if (plugin.lifecycle.uninstall) {
                await plugin.lifecycle.uninstall()
            }
            this.setPluginStatus(name, PluginStatus.NOT_LOADED)

            // 从加载顺序中移除
            const index = this.loadOrder.indexOf(name)
            if (index > -1) {
                this.loadOrder.splice(index, 1)
            }

            logger.info(`插件 ${name} 卸载成功`)
        } catch (error) {
            this.setPluginStatus(name, PluginStatus.ERROR)
            throw error
        }
    }

    /**
     * 获取所有插件
     */
    getAllPlugins(): PluginInfo[] {
        return Array.from(this.plugins.values())
    }

    /**
     * 获取活跃插件
     */
    getActivePlugins(): PluginInfo[] {
        return this.getAllPlugins().filter(plugin => plugin.status === PluginStatus.ACTIVE)
    }

    /**
     * 获取加载顺序
     */
    getLoadOrder(): string[] {
        return [...this.loadOrder]
    }

    /**
     * 检查插件是否存在
     */
    hasPlugin(name: string): boolean {
        return this.plugins.has(name)
    }

    /**
     * 清理所有插件
     */
    clear(): void {
        this.plugins.clear()
        this.loadOrder = []
    }

    private async loadDependencies(plugin: PluginInfo): Promise<void> {
        if (!plugin.dependencies || plugin.dependencies.length === 0) {
            return
        }

        for (const dep of plugin.dependencies) {
            if (!this.plugins.has(dep)) {
                throw new Error(`插件 ${plugin.name} 的依赖 ${dep} 未注册`)
            }

            const depPlugin = this.plugins.get(dep)!
            if (depPlugin.status === PluginStatus.NOT_LOADED) {
                await this.loadPlugin(dep)
            }
        }
    }

    private async performLoad(plugin: PluginInfo): Promise<void> {
        if (plugin.lifecycle.install) {
            await plugin.lifecycle.install()
        }
        logger.debug(`加载插件 ${plugin.name}`)
    }
}