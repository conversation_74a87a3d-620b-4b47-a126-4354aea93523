/**
 * 微前端微内核实现
 * 
 * @description 微内核架构的核心实现，提供最小运行时功能
 * <AUTHOR> <<EMAIL>>
 */

import {
  ApplicationConfig,
  ApplicationEventData,
  ApplicationInstance,
  ApplicationStatus,
  ManagerInterface,
  MicroCoreConfig,
  MicroCoreError,
  PluginInterface,
  SystemEventData,
  createLogger,
  globalErrorHandler
} from '@micro-core/shared';

import type {
  ApplicationManager,
  EventBus,
  MicroCoreStatus,
  PluginManager
} from '@micro-core/shared';

/**
 * 简单事件总线实现
 */
class SimpleEventBus implements EventBus {
  private listeners = new Map<string, Set<Function>>();

  emit(event: string, data?: any): void {
    const handlers = this.listeners.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`事件处理器错误 [${event}]:`, error);
        }
      });
    }
  }

  on(event: string, handler: (data?: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(handler);
  }

  off(event: string, handler?: (data?: any) => void): void {
    if (!handler) {
      this.listeners.delete(event);
      return;
    }
    const handlers = this.listeners.get(event);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  once(event: string, handler: (data?: any) => void): void {
    const onceHandler = (data?: any) => {
      handler(data);
      this.off(event, onceHandler);
    };
    this.on(event, onceHandler);
  }

  clear(): void {
    this.listeners.clear();
  }
}

/**
 * 简单插件管理器实现
 */
class SimplePluginManager implements PluginManager {
  private plugins = new Map<string, PluginInterface>();
  private enabledPlugins = new Set<string>();

  constructor(private core: MicroCore) { }

  async register(plugin: PluginInterface): Promise<void> {
    if (this.plugins.has(plugin.name)) {
      throw new MicroCoreError(3001, `插件 ${plugin.name} 已存在`);
    }

    this.plugins.set(plugin.name, plugin);

    // 安装插件
    await plugin.install(this.core);

    // 初始化插件
    if (plugin.initialize) {
      await plugin.initialize(this.core);
    }

    this.core.eventBus.emit('plugin:registered', { plugin });
  }

  get(name: string): PluginInterface | undefined {
    return this.plugins.get(name);
  }

  getAll(): PluginInterface[] {
    return Array.from(this.plugins.values());
  }

  async enable(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    if (!plugin) {
      throw new MicroCoreError(3002, `插件 ${name} 不存在`);
    }

    if (plugin.start) {
      await plugin.start(this.core);
    }

    this.enabledPlugins.add(name);
    this.core.eventBus.emit('plugin:enabled', { plugin });
  }

  async disable(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    if (!plugin) {
      throw new MicroCoreError(3002, `插件 ${name} 不存在`);
    }

    if (plugin.stop) {
      await plugin.stop(this.core);
    }

    this.enabledPlugins.delete(name);
    this.core.eventBus.emit('plugin:disabled', { plugin });
  }

  async uninstall(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    if (!plugin) {
      throw new MicroCoreError(3002, `插件 ${name} 不存在`);
    }

    // 先禁用插件
    if (this.enabledPlugins.has(name)) {
      await this.disable(name);
    }

    // 销毁插件
    if (plugin.destroy) {
      await plugin.destroy(this.core);
    }

    this.plugins.delete(name);
    this.core.eventBus.emit('plugin:uninstalled', { plugin });
  }

  has(name: string): boolean {
    return this.plugins.has(name);
  }
}

/**
 * 简单应用管理器实现
 */
class SimpleApplicationManager implements ApplicationManager {
  private applications = new Map<string, ApplicationInstance>();

  constructor(private core: MicroCore) { }

  async register(config: ApplicationConfig): Promise<void> {
    if (this.applications.has(config.name)) {
      throw new MicroCoreError(2001, `应用 ${config.name} 已存在`);
    }

    const instance: ApplicationInstance = {
      name: config.name,
      config,
      status: 'registered',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    this.applications.set(config.name, instance);

    // 触发生命周期钩子
    if (config.lifecycle?.beforeLoad) {
      await config.lifecycle.beforeLoad(instance);
    }

    this.core.eventBus.emit('application:registered', {
      type: 'application:registered',
      name: config.name,
      instance,
      config,
      timestamp: Date.now()
    } as ApplicationEventData);
  }

  get(name: string): ApplicationInstance | undefined {
    return this.applications.get(name);
  }

  getAll(): ApplicationInstance[] {
    return Array.from(this.applications.values());
  }

  async load(name: string): Promise<void> {
    const instance = this.applications.get(name);
    if (!instance) {
      throw new MicroCoreError(2002, `应用 ${name} 不存在`);
    }

    if (instance.status !== 'registered') {
      throw new MicroCoreError(2003, `应用 ${name} 状态错误: ${instance.status}`);
    }

    try {
      instance.status = 'loading';
      instance.updatedAt = Date.now();

      // 通过插件系统加载应用
      const plugins = this.core.pluginManager.getAll();
      for (const plugin of plugins) {
        if (plugin.loadApplication) {
          await plugin.loadApplication(instance);
        }
      }

      // 触发生命周期钩子
      if (instance.config.lifecycle?.afterLoad) {
        await instance.config.lifecycle.afterLoad(instance);
      }

      instance.status = 'loaded';
      instance.updatedAt = Date.now();

      this.core.eventBus.emit('application:loaded', {
        type: 'application:loaded',
        name,
        instance,
        timestamp: Date.now()
      } as ApplicationEventData);

    } catch (error) {
      instance.status = 'error';
      instance.lastError = error as Error;
      instance.updatedAt = Date.now();

      this.core.eventBus.emit('application:error', {
        type: 'application:error',
        name,
        instance,
        error: error as Error,
        timestamp: Date.now()
      } as ApplicationEventData);

      throw error;
    }
  }

  async mount(name: string, container?: HTMLElement): Promise<void> {
    const instance = this.applications.get(name);
    if (!instance) {
      throw new MicroCoreError(2002, `应用 ${name} 不存在`);
    }

    if (instance.status !== 'loaded') {
      await this.load(name);
    }

    try {
      instance.status = 'mounting';
      instance.updatedAt = Date.now();

      // 确定挂载容器
      const mountContainer = container ||
        (typeof instance.config.container === 'string'
          ? document.querySelector(instance.config.container) as HTMLElement
          : instance.config.container as HTMLElement);

      if (!mountContainer) {
        throw new MicroCoreError(2004, `应用 ${name} 挂载容器不存在`);
      }

      instance.container = mountContainer;

      // 触发生命周期钩子
      if (instance.config.lifecycle?.beforeMount) {
        await instance.config.lifecycle.beforeMount(instance);
      }

      // 通过插件系统挂载应用
      const plugins = this.core.pluginManager.getAll();
      for (const plugin of plugins) {
        if (plugin.mountApplication) {
          await plugin.mountApplication(instance, mountContainer);
        }
      }

      // 触发生命周期钩子
      if (instance.config.lifecycle?.afterMount) {
        await instance.config.lifecycle.afterMount(instance);
      }

      instance.status = 'mounted';
      instance.updatedAt = Date.now();

      this.core.eventBus.emit('application:mounted', {
        type: 'application:mounted',
        name,
        instance,
        container: mountContainer,
        timestamp: Date.now()
      } as ApplicationEventData);

    } catch (error) {
      instance.status = 'error';
      instance.lastError = error as Error;
      instance.updatedAt = Date.now();

      this.core.eventBus.emit('application:error', {
        type: 'application:error',
        name,
        instance,
        error: error as Error,
        timestamp: Date.now()
      } as ApplicationEventData);

      throw error;
    }
  }

  async unmount(name: string): Promise<void> {
    const instance = this.applications.get(name);
    if (!instance) {
      throw new MicroCoreError(2002, `应用 ${name} 不存在`);
    }

    if (instance.status !== 'mounted') {
      return;
    }

    try {
      instance.status = 'unmounting';
      instance.updatedAt = Date.now();

      // 触发生命周期钩子
      if (instance.config.lifecycle?.beforeUnmount) {
        await instance.config.lifecycle.beforeUnmount(instance);
      }

      // 通过插件系统卸载应用
      const plugins = this.core.pluginManager.getAll();
      for (const plugin of plugins) {
        if (plugin.unmountApplication) {
          await plugin.unmountApplication(instance);
        }
      }

      // 触发生命周期钩子
      if (instance.config.lifecycle?.afterUnmount) {
        await instance.config.lifecycle.afterUnmount(instance);
      }

      instance.status = 'loaded';
      if (instance.container) {
        delete (instance as any).container;
      }
      instance.updatedAt = Date.now();

      this.core.eventBus.emit('application:unmounted', {
        type: 'application:unmounted',
        name,
        instance,
        timestamp: Date.now()
      } as ApplicationEventData);

    } catch (error) {
      instance.status = 'error';
      instance.lastError = error as Error;
      instance.updatedAt = Date.now();

      this.core.eventBus.emit('application:error', {
        type: 'application:error',
        name,
        instance,
        error: error as Error,
        timestamp: Date.now()
      } as ApplicationEventData);

      throw error;
    }
  }

  async unregister(name: string): Promise<void> {
    const instance = this.applications.get(name);
    if (!instance) {
      throw new MicroCoreError(2002, `应用 ${name} 不存在`);
    }

    // 先卸载应用
    if (instance.status === 'mounted') {
      await this.unmount(name);
    }

    try {
      // 触发生命周期钩子
      if (instance.config.lifecycle?.beforeUnregister) {
        await instance.config.lifecycle.beforeUnregister(instance);
      }

      // 通过插件系统注销应用
      const plugins = this.core.pluginManager.getAll();
      for (const plugin of plugins) {
        if (plugin.unregisterApplication) {
          await plugin.unregisterApplication(instance);
        }
      }

      // 触发生命周期钩子
      if (instance.config.lifecycle?.afterUnregister) {
        await instance.config.lifecycle.afterUnregister(instance);
      }

      this.applications.delete(name);

      this.core.eventBus.emit('application:unregistered', {
        type: 'application:unregistered',
        name,
        instance,
        timestamp: Date.now()
      } as ApplicationEventData);

    } catch (error) {
      instance.status = 'error';
      instance.lastError = error as Error;
      instance.updatedAt = Date.now();

      this.core.eventBus.emit('application:error', {
        type: 'application:error',
        name,
        instance,
        error: error as Error,
        timestamp: Date.now()
      } as ApplicationEventData);

      throw error;
    }
  }

  has(name: string): boolean {
    return this.applications.has(name);
  }

  getStatus(name: string): ApplicationStatus | undefined {
    const instance = this.applications.get(name);
    return instance?.status;
  }
}

/**
 * 微前端微内核实现
 */
export class MicroCore {
  private logger = createLogger('MicroCore');
  private managers = new Map<string, ManagerInterface>();
  private config: MicroCoreConfig;
  private status: MicroCoreStatus = {
    initialized: false,
    started: false,
    applications: 0,
    plugins: 0,
    managers: 0
  };

  public readonly eventBus: EventBus;
  public readonly pluginManager: PluginManager;
  public readonly applicationManager: ApplicationManager;

  constructor(config: MicroCoreConfig = {}) {
    this.config = config;
    this.eventBus = new SimpleEventBus();
    this.pluginManager = new SimplePluginManager(this);
    this.applicationManager = new SimpleApplicationManager(this);

    // 设置全局错误处理
    this.setupErrorHandling();
  }

  /**
   * 初始化系统
   */
  async initialize(): Promise<void> {
    if (this.status.initialized) {
      this.logger.warn('系统已初始化');
      return;
    }

    try {
      this.logger.info('开始初始化微前端系统...');

      // 触发生命周期钩子
      if (this.config.lifecycle?.beforeInit) {
        await this.config.lifecycle.beforeInit();
      }

      // 初始化管理器
      for (const [name, manager] of this.managers) {
        if (manager.initialize) {
          await manager.initialize();
        }
      }

      this.status.initialized = true;
      this.status.managers = this.managers.size;

      // 触发生命周期钩子
      if (this.config.lifecycle?.afterInit) {
        await this.config.lifecycle.afterInit();
      }

      this.eventBus.emit('system:initialized', {
        type: 'system:initialized',
        status: this.status,
        timestamp: Date.now()
      } as SystemEventData);

      this.logger.info('微前端系统初始化完成');

    } catch (error) {
      this.logger.error('系统初始化失败:', error);
      this.eventBus.emit('system:error', {
        type: 'system:error',
        error: error as Error,
        timestamp: Date.now()
      } as SystemEventData);
      throw error;
    }
  }

  /**
   * 启动系统
   */
  async start(): Promise<void> {
    if (!this.status.initialized) {
      await this.initialize();
    }

    if (this.status.started) {
      this.logger.warn('系统已启动');
      return;
    }

    try {
      this.logger.info('开始启动微前端系统...');

      // 触发生命周期钩子
      if (this.config.lifecycle?.beforeStart) {
        await this.config.lifecycle.beforeStart();
      }

      // 启动管理器
      for (const [name, manager] of this.managers) {
        if (manager.start) {
          await manager.start();
        }
      }

      this.status.started = true;
      this.status.startTime = Date.now();
      this.status.applications = this.applicationManager.getAll().length;
      this.status.plugins = this.pluginManager.getAll().length;

      // 触发生命周期钩子
      if (this.config.lifecycle?.afterStart) {
        await this.config.lifecycle.afterStart();
      }

      this.eventBus.emit('system:started', {
        type: 'system:started',
        status: this.status,
        timestamp: Date.now()
      } as SystemEventData);

      this.logger.info('微前端系统启动完成');

    } catch (error) {
      this.logger.error('系统启动失败:', error);
      this.eventBus.emit('system:error', {
        type: 'system:error',
        error: error as Error,
        timestamp: Date.now()
      } as SystemEventData);
      throw error;
    }
  }

  /**
   * 注册插件
   */
  async registerPlugin(plugin: PluginInterface): Promise<void> {
    await this.pluginManager.register(plugin);
    this.status.plugins = this.pluginManager.getAll().length;
  }

  /**
   * 注册管理器
   */
  registerManager(name: string, manager: ManagerInterface): void {
    if (this.managers.has(name)) {
      throw new MicroCoreError(4001, `管理器 ${name} 已存在`);
    }

    this.managers.set(name, manager);
    this.status.managers = this.managers.size;
    this.logger.info(`管理器 ${name} 注册成功`);
  }

  /**
   * 获取管理器
   */
  getManager<T extends ManagerInterface>(name: string): T | undefined {
    return this.managers.get(name) as T;
  }

  /**
   * 注册应用
   */
  async registerApplication(config: ApplicationConfig): Promise<void> {
    await this.applicationManager.register(config);
    this.status.applications = this.applicationManager.getAll().length;
  }

  /**
   * 加载应用
   */
  async loadApplication(name: string): Promise<void> {
    await this.applicationManager.load(name);
  }

  /**
   * 挂载应用
   */
  async mountApplication(name: string, container?: HTMLElement): Promise<void> {
    await this.applicationManager.mount(name, container);
  }

  /**
   * 卸载应用
   */
  async unmountApplication(name: string): Promise<void> {
    await this.applicationManager.unmount(name);
  }

  /**
   * 注销应用
   */
  async unregisterApplication(name: string): Promise<void> {
    await this.applicationManager.unregister(name);
    this.status.applications = this.applicationManager.getAll().length;
  }

  /**
   * 获取应用实例
   */
  getApplication(name: string): ApplicationInstance | undefined {
    return this.applicationManager.get(name);
  }

  /**
   * 获取所有应用
   */
  getAllApplications(): ApplicationInstance[] {
    return this.applicationManager.getAll();
  }

  /**
   * 获取应用状态
   */
  getApplicationStatus(name: string): string | undefined {
    return this.applicationManager.getStatus(name);
  }

  /**
   * 检查应用是否存在
   */
  hasApplication(name: string): boolean {
    return this.applicationManager.has(name);
  }

  /**
   * 获取系统状态
   */
  getStatus(): MicroCoreStatus {
    if (this.status.started && this.status.startTime) {
      this.status.uptime = Date.now() - this.status.startTime;
    }
    return { ...this.status };
  }

  /**
   * 停止系统
   */
  async stop(): Promise<void> {
    if (!this.status.started) {
      this.logger.warn('系统未启动');
      return;
    }

    try {
      this.logger.info('开始停止微前端系统...');

      // 触发生命周期钩子
      if (this.config.lifecycle?.beforeStop) {
        await this.config.lifecycle.beforeStop();
      }

      // 卸载所有应用
      const applications = this.applicationManager.getAll();
      for (const app of applications) {
        if (app.status === 'mounted') {
          await this.applicationManager.unmount(app.name);
        }
      }

      // 停止管理器
      for (const [name, manager] of this.managers) {
        if (manager.stop) {
          await manager.stop();
        }
      }

      this.status.started = false;
      delete (this.status as any).startTime;
      delete (this.status as any).uptime;

      // 触发生命周期钩子
      if (this.config.lifecycle?.afterStop) {
        await this.config.lifecycle.afterStop();
      }

      this.eventBus.emit('system:stopped', {
        type: 'system:stopped',
        status: this.status,
        timestamp: Date.now()
      } as SystemEventData);

      this.logger.info('微前端系统停止完成');

    } catch (error) {
      this.logger.error('系统停止失败:', error);
      this.eventBus.emit('system:error', {
        type: 'system:error',
        error: error as Error,
        timestamp: Date.now()
      } as SystemEventData);
      throw error;
    }
  }

  /**
   * 销毁系统
   */
  async destroy(): Promise<void> {
    try {
      this.logger.info('开始销毁微前端系统...');

      // 先停止系统
      if (this.status.started) {
        await this.stop();
      }

      // 触发生命周期钩子
      if (this.config.lifecycle?.beforeDestroy) {
        await this.config.lifecycle.beforeDestroy();
      }

      // 注销所有应用
      const applications = this.applicationManager.getAll();
      for (const app of applications) {
        await this.applicationManager.unregister(app.name);
      }

      // 卸载所有插件
      const plugins = this.pluginManager.getAll();
      for (const plugin of plugins) {
        await this.pluginManager.uninstall(plugin.name);
      }

      // 销毁管理器
      for (const [name, manager] of this.managers) {
        if (manager.destroy) {
          await manager.destroy();
        }
      }

      // 清理状态
      this.managers.clear();
      this.eventBus.clear();
      this.status = {
        initialized: false,
        started: false,
        applications: 0,
        plugins: 0,
        managers: 0
      };

      // 触发生命周期钩子
      if (this.config.lifecycle?.afterDestroy) {
        await this.config.lifecycle.afterDestroy();
      }

      this.eventBus.emit('system:destroyed', {
        type: 'system:destroyed',
        status: this.status,
        timestamp: Date.now()
      } as SystemEventData);

      this.logger.info('微前端系统销毁完成');

    } catch (error) {
      this.logger.error('系统销毁失败:', error);
      this.eventBus.emit('system:error', {
        type: 'system:error',
        error: error as Error,
        timestamp: Date.now()
      } as SystemEventData);
      throw error;
    }
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandling(): void {
    // 监听未捕获的错误
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        const error = event.error instanceof MicroCoreError
          ? event.error
          : new MicroCoreError(1002, event.error?.message || '未知错误', {
            component: 'system',
            method: 'global-error',
            data: { event }
          });
        globalErrorHandler.handle(error);
      });

      window.addEventListener('unhandledrejection', (event) => {
        const error = event.reason instanceof MicroCoreError
          ? event.reason
          : new MicroCoreError(1002, event.reason?.message || '未处理的Promise拒绝', {
            component: 'system',
            method: 'unhandled-rejection',
            data: { event }
          });
        globalErrorHandler.handle(error);
      });
    }
  }
}

/**
 * 默认微内核实例
 */
export const defaultMicroCore = new MicroCore();