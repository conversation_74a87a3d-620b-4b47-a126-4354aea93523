/**
 * @fileoverview 生命周期管理器
 * @description 管理微应用的生命周期状态和转换
 * <AUTHOR> <<EMAIL>>
 */
export declare enum ApplicationStatus {
    NOT_LOADED = "NOT_LOADED",
    LOADING = "LOADING",
    NOT_BOOTSTRAPPED = "NOT_BOOTSTRAPPED",
    BOOTSTRAPPING = "BOOTSTRAPPING",
    NOT_MOUNTED = "NOT_MOUNTED",
    MOUNTING = "MOUNTING",
    MOUNTED = "MOUNTED",
    UNMOUNTING = "UNMOUNTING",
    UNLOADING = "UNLOADING",
    LOAD_ERROR = "LOAD_ERROR",
    SKIP_BECAUSE_BROKEN = "SKIP_BECAUSE_BROKEN"
}
export interface ApplicationLifecycle {
    bootstrap?: () => Promise<void>;
    mount?: () => Promise<void>;
    unmount?: () => Promise<void>;
    unload?: () => Promise<void>;
}
export interface ApplicationInfo {
    name: string;
    status: ApplicationStatus;
    lifecycle: ApplicationLifecycle;
    loadPromise?: Promise<void>;
    bootstrapPromise?: Promise<void>;
    mountPromise?: Promise<void>;
    unmountPromise?: Promise<void>;
    unloadPromise?: Promise<void>;
}
/**
 * 生命周期管理器
 * 负责管理微应用的生命周期状态转换
 */
export declare class LifecycleManager {
    private applications;
    /**
     * 注册应用
     */
    registerApplication(name: string, lifecycle: ApplicationLifecycle): void;
    /**
     * 获取应用信息
     */
    getApplication(name: string): ApplicationInfo | undefined;
    /**
     * 获取应用状态
     */
    getApplicationStatus(name: string): ApplicationStatus | undefined;
    /**
     * 设置应用状态
     */
    setApplicationStatus(name: string, status: ApplicationStatus): void;
    /**
     * 加载应用
     */
    loadApplication(name: string): Promise<void>;
    /**
     * 引导应用
     */
    bootstrapApplication(name: string): Promise<void>;
    /**
     * 挂载应用
     */
    mountApplication(name: string): Promise<void>;
    /**
     * 卸载应用
     */
    unmountApplication(name: string): Promise<void>;
    /**
     * 卸载应用
     */
    unloadApplication(name: string): Promise<void>;
    /**
     * 获取所有应用
     */
    getAllApplications(): ApplicationInfo[];
    /**
     * 清理所有应用
     */
    clear(): void;
    private performLoad;
    private performBootstrap;
    private performMount;
    private performUnmount;
    private performUnload;
}
//# sourceMappingURL=lifecycle-manager.d.ts.map