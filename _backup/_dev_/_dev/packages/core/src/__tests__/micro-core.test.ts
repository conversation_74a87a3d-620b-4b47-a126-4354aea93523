/**
 * 微前端微内核测试
 */

import { MicroCoreError } from '@micro-core/shared'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { MicroCore, defaultMicroCore } from '../micro-core'

describe('MicroCore', () => {
    let microCore: MicroCore

    beforeEach(() => {
        microCore = new MicroCore()
    })

    afterEach(async () => {
        if (microCore.getStatus().started) {
            await microCore.stop()
        }
        if (microCore.getStatus().initialized) {
            await microCore.destroy()
        }
    })

    describe('初始化', () => {
        it('应该能够创建微内核实例', () => {
            expect(microCore).toBeDefined()
            expect(microCore.eventBus).toBeDefined()
            expect(microCore.pluginManager).toBeDefined()
            expect(microCore.applicationManager).toBeDefined()
        })

        it('应该能够初始化系统', async () => {
            await microCore.initialize()
            const status = microCore.getStatus()
            expect(status.initialized).toBe(true)
            expect(status.started).toBe(false)
        })

        it('重复初始化应该不会报错', async () => {
            await microCore.initialize()
            await microCore.initialize()
            const status = microCore.getStatus()
            expect(status.initialized).toBe(true)
        })

        it('应该能够启动系统', async () => {
            await microCore.start()
            const status = microCore.getStatus()
            expect(status.initialized).toBe(true)
            expect(status.started).toBe(true)
            expect(status.startTime).toBeDefined()
        })

        it('重复启动应该不会报错', async () => {
            await microCore.start()
            await microCore.start()
            const status = microCore.getStatus()
            expect(status.started).toBe(true)
        })
    })

    describe('生命周期钩子', () => {
        it('应该调用初始化生命周期钩子', async () => {
            const beforeInit = vi.fn()
            const afterInit = vi.fn()

            const coreWithHooks = new MicroCore({
                lifecycle: {
                    beforeInit,
                    afterInit
                }
            })

            await coreWithHooks.initialize()

            expect(beforeInit).toHaveBeenCalled()
            expect(afterInit).toHaveBeenCalled()

            await coreWithHooks.destroy()
        })

        it('应该调用启动生命周期钩子', async () => {
            const beforeStart = vi.fn()
            const afterStart = vi.fn()

            const coreWithHooks = new MicroCore({
                lifecycle: {
                    beforeStart,
                    afterStart
                }
            })

            await coreWithHooks.start()

            expect(beforeStart).toHaveBeenCalled()
            expect(afterStart).toHaveBeenCalled()

            await coreWithHooks.destroy()
        })

        it('应该调用停止生命周期钩子', async () => {
            const beforeStop = vi.fn()
            const afterStop = vi.fn()

            const coreWithHooks = new MicroCore({
                lifecycle: {
                    beforeStop,
                    afterStop
                }
            })

            await coreWithHooks.start()
            await coreWithHooks.stop()

            expect(beforeStop).toHaveBeenCalled()
            expect(afterStop).toHaveBeenCalled()

            await coreWithHooks.destroy()
        })

        it('应该调用销毁生命周期钩子', async () => {
            const beforeDestroy = vi.fn()
            const afterDestroy = vi.fn()

            const coreWithHooks = new MicroCore({
                lifecycle: {
                    beforeDestroy,
                    afterDestroy
                }
            })

            await coreWithHooks.start()
            await coreWithHooks.destroy()

            expect(beforeDestroy).toHaveBeenCalled()
            expect(afterDestroy).toHaveBeenCalled()
        })
    })

    describe('插件管理', () => {
        it('应该能够注册插件', async () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                initialize: vi.fn()
            }

            await microCore.registerPlugin(plugin)

            expect(plugin.install).toHaveBeenCalledWith(microCore)
            expect(plugin.initialize).toHaveBeenCalledWith(microCore)
            expect(microCore.pluginManager.has('test-plugin')).toBe(true)
        })

        it('注册重复插件应该抛出错误', async () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn()
            }

            await microCore.registerPlugin(plugin)

            await expect(microCore.registerPlugin(plugin)).rejects.toThrow(MicroCoreError)
        })

        it('应该能够启用和禁用插件', async () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                start: vi.fn(),
                stop: vi.fn()
            }

            await microCore.registerPlugin(plugin)
            await microCore.pluginManager.enable('test-plugin')

            expect(plugin.start).toHaveBeenCalledWith(microCore)

            await microCore.pluginManager.disable('test-plugin')

            expect(plugin.stop).toHaveBeenCalledWith(microCore)
        })

        it('应该能够卸载插件', async () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                destroy: vi.fn()
            }

            await microCore.registerPlugin(plugin)
            await microCore.pluginManager.uninstall('test-plugin')

            expect(plugin.destroy).toHaveBeenCalledWith(microCore)
            expect(microCore.pluginManager.has('test-plugin')).toBe(false)
        })
    })

    describe('应用管理', () => {
        it('应该能够注册应用', async () => {
            const config = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            }

            await microCore.registerApplication(config)

            expect(microCore.hasApplication('test-app')).toBe(true)
            expect(microCore.getApplicationStatus('test-app')).toBe('registered')
        })

        it('注册重复应用应该抛出错误', async () => {
            const config = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            }

            await microCore.registerApplication(config)

            await expect(microCore.registerApplication(config)).rejects.toThrow(MicroCoreError)
        })

        it('应该能够加载应用', async () => {
            const config = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                lifecycle: {
                    afterLoad: vi.fn()
                }
            }

            await microCore.registerApplication(config)
            await microCore.loadApplication('test-app')

            expect(microCore.getApplicationStatus('test-app')).toBe('loaded')
            expect(config.lifecycle.afterLoad).toHaveBeenCalled()
        })

        it('应该能够挂载应用', async () => {
            // 创建测试容器
            const container = document.createElement('div')
            container.id = 'app'
            document.body.appendChild(container)

            const config = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                lifecycle: {
                    beforeMount: vi.fn(),
                    afterMount: vi.fn()
                }
            }

            await microCore.registerApplication(config)
            await microCore.mountApplication('test-app')

            expect(microCore.getApplicationStatus('test-app')).toBe('mounted')
            expect(config.lifecycle.beforeMount).toHaveBeenCalled()
            expect(config.lifecycle.afterMount).toHaveBeenCalled()

            // 清理
            document.body.removeChild(container)
        })

        it('应该能够卸载应用', async () => {
            // 创建测试容器
            const container = document.createElement('div')
            container.id = 'app'
            document.body.appendChild(container)

            const config = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                lifecycle: {
                    beforeUnmount: vi.fn(),
                    afterUnmount: vi.fn()
                }
            }

            await microCore.registerApplication(config)
            await microCore.mountApplication('test-app')
            await microCore.unmountApplication('test-app')

            expect(microCore.getApplicationStatus('test-app')).toBe('loaded')
            expect(config.lifecycle.beforeUnmount).toHaveBeenCalled()
            expect(config.lifecycle.afterUnmount).toHaveBeenCalled()

            // 清理
            document.body.removeChild(container)
        })

        it('应该能够注销应用', async () => {
            const config = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                lifecycle: {
                    beforeUnregister: vi.fn(),
                    afterUnregister: vi.fn()
                }
            }

            await microCore.registerApplication(config)
            await microCore.unregisterApplication('test-app')

            expect(microCore.hasApplication('test-app')).toBe(false)
            expect(config.lifecycle.beforeUnregister).toHaveBeenCalled()
            expect(config.lifecycle.afterUnregister).toHaveBeenCalled()
        })
    })

    describe('管理器注册', () => {
        it('应该能够注册自定义管理器', () => {
            const manager = {
                name: 'test-manager',
                version: '1.0.0',
                initialize: vi.fn(),
                start: vi.fn(),
                stop: vi.fn(),
                destroy: vi.fn()
            }

            microCore.registerManager('test-manager', manager)

            expect(microCore.getManager('test-manager')).toBe(manager)
        })

        it('注册重复管理器应该抛出错误', () => {
            const manager = {
                name: 'test-manager',
                version: '1.0.0'
            }

            microCore.registerManager('test-manager', manager)

            expect(() => microCore.registerManager('test-manager', manager)).toThrow(MicroCoreError)
        })
    })

    describe('事件系统', () => {
        it('应该能够发送和接收事件', () => {
            const handler = vi.fn()

            microCore.eventBus.on('test-event', handler)
            microCore.eventBus.emit('test-event', { data: 'test' })

            expect(handler).toHaveBeenCalledWith({ data: 'test' })
        })

        it('应该能够取消事件监听', () => {
            const handler = vi.fn()

            microCore.eventBus.on('test-event', handler)
            microCore.eventBus.off('test-event', handler)
            microCore.eventBus.emit('test-event', { data: 'test' })

            expect(handler).not.toHaveBeenCalled()
        })

        it('应该能够监听一次性事件', () => {
            const handler = vi.fn()

            microCore.eventBus.once('test-event', handler)
            microCore.eventBus.emit('test-event', { data: 'test' })
            microCore.eventBus.emit('test-event', { data: 'test2' })

            expect(handler).toHaveBeenCalledTimes(1)
            expect(handler).toHaveBeenCalledWith({ data: 'test' })
        })

        it('应该能够清除所有事件监听', () => {
            const handler1 = vi.fn()
            const handler2 = vi.fn()

            microCore.eventBus.on('test-event1', handler1)
            microCore.eventBus.on('test-event2', handler2)
            microCore.eventBus.clear()
            microCore.eventBus.emit('test-event1', { data: 'test' })
            microCore.eventBus.emit('test-event2', { data: 'test' })

            expect(handler1).not.toHaveBeenCalled()
            expect(handler2).not.toHaveBeenCalled()
        })
    })

    describe('系统状态', () => {
        it('应该能够获取系统状态', () => {
            const status = microCore.getStatus()

            expect(status).toHaveProperty('initialized')
            expect(status).toHaveProperty('started')
            expect(status).toHaveProperty('applications')
            expect(status).toHaveProperty('plugins')
            expect(status).toHaveProperty('managers')
        })

        it('启动后应该包含运行时间', async () => {
            await microCore.start()

            // 等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 10))

            const status = microCore.getStatus()
            expect(status.uptime).toBeGreaterThan(0)
        })
    })

    describe('系统停止和销毁', () => {
        it('应该能够停止系统', async () => {
            await microCore.start()
            await microCore.stop()

            const status = microCore.getStatus()
            expect(status.started).toBe(false)
            expect(status.startTime).toBeUndefined()
            expect(status.uptime).toBeUndefined()
        })

        it('应该能够销毁系统', async () => {
            await microCore.start()
            await microCore.destroy()

            const status = microCore.getStatus()
            expect(status.initialized).toBe(false)
            expect(status.started).toBe(false)
            expect(status.applications).toBe(0)
            expect(status.plugins).toBe(0)
            expect(status.managers).toBe(0)
        })

        it('销毁时应该卸载所有应用和插件', async () => {
            // 注册应用
            await microCore.registerApplication({
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            })

            // 注册插件
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                destroy: vi.fn()
            }
            await microCore.registerPlugin(plugin)

            await microCore.start()
            await microCore.destroy()

            expect(microCore.hasApplication('test-app')).toBe(false)
            expect(microCore.pluginManager.has('test-plugin')).toBe(false)
            expect(plugin.destroy).toHaveBeenCalled()
        })
    })

    describe('错误处理', () => {
        it('应该处理插件注册错误', async () => {
            const plugin = {
                name: 'error-plugin',
                version: '1.0.0',
                install: vi.fn().mockRejectedValue(new Error('安装失败'))
            }

            await expect(microCore.registerPlugin(plugin)).rejects.toThrow('安装失败')
        })

        it('应该处理应用加载错误', async () => {
            await microCore.registerApplication({
                name: 'error-app',
                entry: 'http://localhost:3000',
                container: '#app',
                lifecycle: {
                    afterLoad: vi.fn().mockRejectedValue(new Error('加载失败'))
                }
            })

            await expect(microCore.loadApplication('error-app')).rejects.toThrow('加载失败')
            expect(microCore.getApplicationStatus('error-app')).toBe('error')
        })

        it('应该处理不存在的应用操作', async () => {
            await expect(microCore.loadApplication('non-existent')).rejects.toThrow(MicroCoreError)
            await expect(microCore.mountApplication('non-existent')).rejects.toThrow(MicroCoreError)
            await expect(microCore.unmountApplication('non-existent')).rejects.toThrow(MicroCoreError)
            await expect(microCore.unregisterApplication('non-existent')).rejects.toThrow(MicroCoreError)
        })

        it('应该处理不存在的插件操作', async () => {
            await expect(microCore.pluginManager.enable('non-existent')).rejects.toThrow(MicroCoreError)
            await expect(microCore.pluginManager.disable('non-existent')).rejects.toThrow(MicroCoreError)
            await expect(microCore.pluginManager.uninstall('non-existent')).rejects.toThrow(MicroCoreError)
        })
    })

    describe('默认实例', () => {
        it('应该提供默认微内核实例', () => {
            expect(defaultMicroCore).toBeInstanceOf(MicroCore)
        })
    })
})