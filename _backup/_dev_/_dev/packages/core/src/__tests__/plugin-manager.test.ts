/**
 * @fileoverview 插件管理器测试
 * @description 测试插件系统管理功能
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { PluginManager } from '../plugin/plugin-manager'

describe('PluginManager', () => {
    let pluginManager: PluginManager

    beforeEach(() => {
        pluginManager = new PluginManager()
    })

    describe('插件注册', () => {
        it('应该能够注册插件', () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(plugin)
            const registeredPlugin = pluginManager.getPlugin('test-plugin')

            expect(registeredPlugin).toBeDefined()
            expect(registeredPlugin?.name).toBe('test-plugin')
            expect(registeredPlugin?.status).toBe(PluginStatus.REGISTERED)
        })

        it('应该拒绝重复注册同名插件', () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(plugin)

            expect(() => {
                pluginManager.registerPlugin(plugin)
            }).toThrow('插件 test-plugin 已存在')
        })
    })

    describe('插件生命周期', () => {
        let mockPlugin: any

        beforeEach(() => {
            mockPlugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn().mockResolvedValue(undefined)
            }
            pluginManager.registerPlugin(mockPlugin)
        })

        it('应该能够安装插件', async () => {
            await pluginManager.installPlugin('test-plugin')

            const plugin = pluginManager.getPlugin('test-plugin')
            expect(plugin?.status).toBe(PluginStatus.INSTALLED)
            expect(mockPlugin.install).toHaveBeenCalled()
        })

        it('应该能够卸载插件', async () => {
            await pluginManager.installPlugin('test-plugin')
            await pluginManager.uninstallPlugin('test-plugin')

            const plugin = pluginManager.getPlugin('test-plugin')
            expect(plugin?.status).toBe(PluginStatus.REGISTERED)
            expect(mockPlugin.uninstall).toHaveBeenCalled()
        })

        it('应该能够启用插件', async () => {
            await pluginManager.installPlugin('test-plugin')
            await pluginManager.enablePlugin('test-plugin')

            const plugin = pluginManager.getPlugin('test-plugin')
            expect(plugin?.status).toBe(PluginStatus.ENABLED)
        })

        it('应该能够禁用插件', async () => {
            await pluginManager.installPlugin('test-plugin')
            await pluginManager.enablePlugin('test-plugin')
            await pluginManager.disablePlugin('test-plugin')

            const plugin = pluginManager.getPlugin('test-plugin')
            expect(plugin?.status).toBe(PluginStatus.DISABLED)
        })
    })

    describe('插件查询', () => {
        it('应该能够获取所有插件', () => {
            const plugin1 = {
                name: 'plugin1',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            }
            const plugin2 = {
                name: 'plugin2',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(plugin1)
            pluginManager.registerPlugin(plugin2)

            const plugins = pluginManager.getAllPlugins()
            expect(plugins.size).toBe(2)
            expect(plugins.has('plugin1')).toBe(true)
            expect(plugins.has('plugin2')).toBe(true)
        })

        it('应该能够获取指定状态的插件', async () => {
            const plugin1 = {
                name: 'plugin1',
                version: '1.0.0',
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn()
            }
            const plugin2 = {
                name: 'plugin2',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(plugin1)
            pluginManager.registerPlugin(plugin2)

            await pluginManager.installPlugin('plugin1')

            const installedPlugins = pluginManager.getPluginsByStatus(PluginStatus.INSTALLED)
            const registeredPlugins = pluginManager.getPluginsByStatus(PluginStatus.REGISTERED)

            expect(installedPlugins).toHaveLength(1)
            expect(installedPlugins[0].name).toBe('plugin1')
            expect(registeredPlugins).toHaveLength(1)
            expect(registeredPlugins[0].name).toBe('plugin2')
        })
    })

    describe('插件依赖', () => {
        it('应该能够处理插件依赖关系', () => {
            const pluginA = {
                name: 'plugin-a',
                version: '1.0.0',
                dependencies: ['plugin-b'],
                install: vi.fn(),
                uninstall: vi.fn()
            }
            const pluginB = {
                name: 'plugin-b',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(pluginB)
            pluginManager.registerPlugin(pluginA)

            const dependencies = pluginManager.getPluginDependencies('plugin-a')
            expect(dependencies).toContain('plugin-b')
        })

        it('应该检测循环依赖', () => {
            const pluginA = {
                name: 'plugin-a',
                version: '1.0.0',
                dependencies: ['plugin-b'],
                install: vi.fn(),
                uninstall: vi.fn()
            }
            const pluginB = {
                name: 'plugin-b',
                version: '1.0.0',
                dependencies: ['plugin-a'],
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(pluginA)
            pluginManager.registerPlugin(pluginB)

            expect(() => {
                pluginManager.validateDependencies('plugin-a')
            }).toThrow('检测到循环依赖')
        })
    })

    describe('错误处理', () => {
        it('应该处理不存在的插件', () => {
            expect(() => {
                pluginManager.getPlugin('non-existent')
            }).not.toThrow()

            expect(pluginManager.getPlugin('non-existent')).toBeUndefined()
        })

        it('应该处理插件安装失败的情况', async () => {
            const plugin = {
                name: 'failing-plugin',
                version: '1.0.0',
                install: vi.fn().mockRejectedValue(new Error('安装失败')),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(plugin)

            await expect(pluginManager.installPlugin('failing-plugin')).rejects.toThrow('安装失败')
        })
    })

    describe('事件系统', () => {
        it('应该发送插件注册事件', () => {
            const eventSpy = vi.fn()
            pluginManager.on('plugin:registered', eventSpy)

            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(plugin)

            expect(eventSpy).toHaveBeenCalledWith({
                name: 'test-plugin',
                version: '1.0.0',
                status: PluginStatus.REGISTERED
            })
        })

        it('应该发送插件状态变更事件', async () => {
            const eventSpy = vi.fn()
            pluginManager.on('plugin:status-changed', eventSpy)

            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn()
            }

            pluginManager.registerPlugin(plugin)
            await pluginManager.installPlugin('test-plugin')

            expect(eventSpy).toHaveBeenCalledWith({
                name: 'test-plugin',
                from: PluginStatus.REGISTERED,
                to: PluginStatus.INSTALLED
            })
        })
    })
})