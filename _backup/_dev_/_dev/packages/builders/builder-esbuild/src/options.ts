/**
 * @fileoverview ESBuild Builder Options and Configuration
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import type { BuildOptions, ServeOptions } from 'esbuild';
import type { BaseBuilderConfig } from '../../shared/types';

/**
 * ESBuild-specific builder configuration
 */
export interface EsbuildBuilderConfig extends BaseBuilderConfig {
  esbuildConfig?: BuildOptions;
  plugins?: any[];
  format?: 'iife' | 'cjs' | 'esm';
  target?: string | string[];
  platform?: 'browser' | 'node' | 'neutral';
  minify?: boolean;
  sourcemap?: boolean | 'inline' | 'external';
  bundle?: boolean;
  splitting?: boolean;
  treeShaking?: boolean;
  define?: Record<string, string>;
  loader?: Record<string, string>;
  alias?: Record<string, string>;
  externals?: string[];
  publicPath?: string;
  inject?: string[];
  banner?: Record<string, string>;
  footer?: Record<string, string>;
}

/**
 * ESBuild development server configuration
 */
export interface EsbuildDevServerConfig {
  port?: number;
  host?: string;
  servedir?: string;
  fallback?: string;
  onRequest?: (args: any) => void;
}

/**
 * Default ESBuild configuration
 */
export const DEFAULT_ESBUILD_CONFIG: Partial<EsbuildBuilderConfig> = {
  format: 'esm',
  target: 'es2020',
  platform: 'browser',
  minify: false,
  sourcemap: true,
  bundle: true,
  splitting: true,
  treeShaking: true,
  publicPath: '/',
  define: {
    'process.env.NODE_ENV': '"development"'
  }
};

/**
 * Create ESBuild configuration from base config
 */
export function createEsbuildConfig(config: EsbuildBuilderConfig): BuildOptions {
  const {
    entry,
    outDir = 'dist',
    mode = 'development',
    esbuildConfig = {},
    format = 'esm',
    target = 'es2020',
    platform = 'browser',
    minify,
    sourcemap = true,
    bundle = true,
    splitting = true,
    treeShaking = true,
    define = {},
    loader = {},
    alias = {},
    externals = [],
    publicPath = '/',
    inject = [],
    banner = {},
    footer = {}
  } = config;

  const isProd = mode === 'production';

  const baseConfig: BuildOptions = {
    entryPoints: Array.isArray(entry) ? entry : [entry],
    outdir: outDir,
    format: format as any,
    target: Array.isArray(target) ? target : [target],
    platform: platform as any,
    minify: minify ?? isProd,
    sourcemap: sourcemap as any,
    bundle,
    splitting: splitting && format === 'esm',
    treeShaking: treeShaking ? 'ignore-annotations' : false,
    define: {
      ...DEFAULT_ESBUILD_CONFIG.define,
      'process.env.NODE_ENV': isProd ? '"production"' : '"development"',
      __MICRO_CORE_DEV__: (!isProd).toString(),
      __MICRO_CORE_PROD__: isProd.toString(),
      ...define
    },
    loader: {
      '.png': 'file',
      '.jpg': 'file',
      '.jpeg': 'file',
      '.gif': 'file',
      '.svg': 'file',
      '.woff': 'file',
      '.woff2': 'file',
      '.ttf': 'file',
      '.eot': 'file',
      ...loader
    },
    alias,
    external: externals,
    publicPath,
    inject,
    banner,
    footer,
    metafile: true,
    write: true,
    logLevel: 'info'
  };

  // Merge with user-provided ESBuild config
  return {
    ...baseConfig,
    ...esbuildConfig
  };
}

/**
 * Create ESBuild development server configuration
 */
export function createEsbuildDevConfig(
  buildConfig: BuildOptions,
  devConfig: EsbuildDevServerConfig = {}
): ServeOptions {
  return {
    port: devConfig.port || 3000,
    host: devConfig.host || 'localhost',
    servedir: devConfig.servedir || buildConfig.outdir || 'dist',
    fallback: devConfig.fallback,
    onRequest: devConfig.onRequest
  };
}

/**
 * Validate ESBuild configuration
 */
export function validateEsbuildConfig(config: EsbuildBuilderConfig): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required fields
  if (!config.entry) {
    errors.push('Entry point is required');
  }

  if (!config.outDir) {
    warnings.push('Output directory not specified, using default "dist"');
  }

  // Check format compatibility
  if (config.splitting && config.format !== 'esm') {
    warnings.push('Code splitting is only supported with ESM format');
  }

  // Check platform compatibility
  if (config.platform === 'node' && config.format === 'iife') {
    warnings.push('IIFE format is not recommended for Node.js platform');
  }

  // Check target compatibility
  if (config.target && Array.isArray(config.target)) {
    const invalidTargets = config.target.filter(t => 
      !t.match(/^(es\d{4}|esnext|chrome\d+|firefox\d+|safari\d+|edge\d+|node\d+)$/)
    );
    if (invalidTargets.length > 0) {
      warnings.push(`Invalid target(s): ${invalidTargets.join(', ')}`);
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}
