/**
 * esbuild 构建器类型定义
 */

export interface EsbuildBuilderOptions {
    /** 应用名称 */
    appName: string;
    /** 入口文件路径 */
    entry: string;
    /** 输出目录 */
    outdir?: string;
    /** 输出格式 */
    format?: 'esm' | 'cjs' | 'iife';
    /** 目标环境 */
    target?: string | string[];
    /** 外部依赖 */
    external?: string[];
    /** 是否生成清单文件 */
    generateManifest?: boolean;
}

export interface EsbuildPluginOptions {
    /** 应用名称 */
    appName: string;
    /** 入口文件路径 */
    entry: string;
    /** 输出目录 */
    outdir?: string;
    /** 是否生成清单文件 */
    generateManifest?: boolean;
}

export interface MicroAppManifest {
    /** 应用名称 */
    name: string;
    /** 应用版本 */
    version: string;
    /** 入口文件 */
    entry: string;
    /** 资源文件 */
    assets: {
        js: string[];
        css: string[];
    };
    /** 构建工具 */
    buildTool: string;
    /** 构建时间 */
    buildTime: string;
}