{"name": "@micro-core/builder-webpack", "version": "0.1.0", "description": "Webpack构建器适配 - micro-core微前端架构", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "peerDependencies": {"webpack": "^5.0.0", "webpack-dev-server": "^4.0.0"}, "devDependencies": {"@types/webpack": "^5.28.5", "typescript": "^5.3.3", "vitest": "^3.2.4", "eslint": "^8.57.0"}, "keywords": ["micro-frontend", "微前端", "webpack", "builder", "构建工具"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/builders/builder-webpack"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "homepage": "https://micro-core.dev"}