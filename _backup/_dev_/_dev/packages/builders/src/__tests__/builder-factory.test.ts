/**
 * @fileoverview 构建器工厂测试
 * @description 测试构建器工厂的功能
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { BuilderFactory } from '../builder-factory'
import { BuilderType } from '../types'

describe('BuilderFactory', () => {
    let builderFactory: BuilderFactory

    beforeEach(() => {
        builderFactory = new BuilderFactory()
    })

    describe('构建器创建', () => {
        it('应该能够创建Vite构建器', () => {
            const builder = builderFactory.createBuilder('test-vite', BuilderType.VITE)
            expect(builder).toBeDefined()
            expect(builder.getName()).toBe('test-vite')
            expect(builder.getType()).toBe('vite')
        })

        it('应该能够创建Webpack构建器', () => {
            const builder = builderFactory.createBuilder('test-webpack', BuilderType.WEBPACK)
            expect(builder).toBeDefined()
            expect(builder.getName()).toBe('test-webpack')
            expect(builder.getType()).toBe('webpack')
        })

        it('应该能够创建Rollup构建器', () => {
            const builder = builderFactory.createBuilder('test-rollup', BuilderType.ROLLUP)
            expect(builder).toBeDefined()
            expect(builder.getName()).toBe('test-rollup')
            expect(builder.getType()).toBe('rollup')
        })

        it('应该能够创建Parcel构建器', () => {
            const builder = builderFactory.createBuilder('test-parcel', BuilderType.PARCEL)
            expect(builder).toBeDefined()
            expect(builder.getName()).toBe('test-parcel')
            expect(builder.getType()).toBe('parcel')
        })

        it('应该能够创建esbuild构建器', () => {
            const builder = builderFactory.createBuilder('test-esbuild', BuilderType.ESBUILD)
            expect(builder).toBeDefined()
            expect(builder.getName()).toBe('test-esbuild')
            expect(builder.getType()).toBe('esbuild')
        })

        it('应该能够创建Rspack构建器', () => {
            const builder = builderFactory.createBuilder('test-rspack', BuilderType.RSPACK)
            expect(builder).toBeDefined()
            expect(builder.getName()).toBe('test-rspack')
            expect(builder.getType()).toBe('rspack')
        })

        it('应该能够创建Turbopack构建器', () => {
            const builder = builderFactory.createBuilder('test-turbopack', BuilderType.TURBOPACK)
            expect(builder).toBeDefined()
            expect(builder.getName()).toBe('test-turbopack')
            expect(builder.getType()).toBe('turbopack')
        })
    })

    describe('构建器管理', () => {
        it('应该能够获取构建器', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const retrieved = builderFactory.getBuilder('test')
            expect(retrieved).toBe(builder)
        })

        it('应该能够检查构建器是否存在', () => {
            builderFactory.createBuilder('test', BuilderType.VITE)
            expect(builderFactory.hasBuilder('test')).toBe(true)
            expect(builderFactory.hasBuilder('nonexistent')).toBe(false)
        })

        it('应该能够销毁构建器', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const destroySpy = vi.spyOn(builder, 'destroy')

            builderFactory.destroyBuilder('test')

            expect(destroySpy).toHaveBeenCalled()
            expect(builderFactory.hasBuilder('test')).toBe(false)
        })

        it('应该能够获取所有构建器', () => {
            builderFactory.createBuilder('test1', BuilderType.VITE)
            builderFactory.createBuilder('test2', BuilderType.WEBPACK)

            const builders = builderFactory.getAllBuilders()
            expect(builders).toHaveLength(2)
        })

        it('应该能够清理所有构建器', () => {
            builderFactory.createBuilder('test1', BuilderType.VITE)
            builderFactory.createBuilder('test2', BuilderType.WEBPACK)

            builderFactory.clear()

            expect(builderFactory.getAllBuilders()).toHaveLength(0)
        })
    })

    describe('构建器检测', () => {
        it('应该能够自动检测Vite构建器', () => {
            // 模拟Vite环境
            const mockPackageJson = {
                devDependencies: {
                    'vite': '^7.0.0'
                }
            }

            const builderType = builderFactory.detectBuilder(mockPackageJson)
            expect(builderType).toBe(BuilderType.VITE)
        })

        it('应该能够自动检测Webpack构建器', () => {
            // 模拟Webpack环境
            const mockPackageJson = {
                devDependencies: {
                    'webpack': '^5.0.0'
                }
            }

            const builderType = builderFactory.detectBuilder(mockPackageJson)
            expect(builderType).toBe(BuilderType.WEBPACK)
        })

        it('应该在无法检测时返回Vite构建器', () => {
            const mockPackageJson = {}
            const builderType = builderFactory.detectBuilder(mockPackageJson)
            expect(builderType).toBe(BuilderType.VITE)
        })
    })

    describe('构建器生命周期', () => {
        it('应该能够初始化构建器', async () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const initSpy = vi.spyOn(builder, 'initialize')

            await builder.initialize({})

            expect(initSpy).toHaveBeenCalled()
        })

        it('应该能够构建项目', async () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const buildSpy = vi.spyOn(builder, 'build')

            await builder.build()

            expect(buildSpy).toHaveBeenCalled()
        })

        it('应该能够启动开发服务器', async () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const devSpy = vi.spyOn(builder, 'dev')

            await builder.dev()

            expect(devSpy).toHaveBeenCalled()
        })

        it('应该能够预览构建结果', async () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const previewSpy = vi.spyOn(builder, 'preview')

            await builder.preview()

            expect(previewSpy).toHaveBeenCalled()
        })
    })

    describe('配置管理', () => {
        it('应该能够合并配置', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const baseConfig = { entry: 'src/index.ts' }
            const userConfig = { output: 'dist' }

            const mergedConfig = builder.mergeConfig(baseConfig, userConfig)

            expect(mergedConfig).toEqual({
                entry: 'src/index.ts',
                output: 'dist'
            })
        })

        it('应该能够验证配置', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const validConfig = { entry: 'src/index.ts' }
            const invalidConfig = {}

            expect(builder.validateConfig(validConfig)).toBe(true)
            expect(builder.validateConfig(invalidConfig)).toBe(false)
        })

        it('应该能够获取默认配置', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const defaultConfig = builder.getDefaultConfig()

            expect(defaultConfig).toBeDefined()
            expect(typeof defaultConfig).toBe('object')
        })
    })

    describe('插件系统', () => {
        it('应该能够添加插件', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const plugin = { name: 'test-plugin', apply: vi.fn() }

            builder.addPlugin(plugin)

            expect(builder.getPlugins()).toContain(plugin)
        })

        it('应该能够移除插件', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const plugin = { name: 'test-plugin', apply: vi.fn() }

            builder.addPlugin(plugin)
            builder.removePlugin('test-plugin')

            expect(builder.getPlugins()).not.toContain(plugin)
        })

        it('应该能够获取所有插件', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)
            const plugin1 = { name: 'plugin1', apply: vi.fn() }
            const plugin2 = { name: 'plugin2', apply: vi.fn() }

            builder.addPlugin(plugin1)
            builder.addPlugin(plugin2)

            const plugins = builder.getPlugins()
            expect(plugins).toHaveLength(2)
            expect(plugins).toContain(plugin1)
            expect(plugins).toContain(plugin2)
        })
    })

    describe('错误处理', () => {
        it('应该在创建重复构建器时抛出错误', () => {
            builderFactory.createBuilder('test', BuilderType.VITE)

            expect(() => {
                builderFactory.createBuilder('test', BuilderType.VITE)
            }).toThrow('构建器 test 已存在')
        })

        it('应该在获取不存在的构建器时返回undefined', () => {
            expect(builderFactory.getBuilder('nonexistent')).toBeUndefined()
        })

        it('应该在销毁不存在的构建器时不抛出错误', () => {
            expect(() => {
                builderFactory.destroyBuilder('nonexistent')
            }).not.toThrow()
        })
    })

    describe('性能优化', () => {
        it('应该能够启用代码分割', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)

            builder.enableCodeSplitting()

            expect(builder.isCodeSplittingEnabled()).toBe(true)
        })

        it('应该能够启用Tree Shaking', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)

            builder.enableTreeShaking()

            expect(builder.isTreeShakingEnabled()).toBe(true)
        })

        it('应该能够启用压缩', () => {
            const builder = builderFactory.createBuilder('test', BuilderType.VITE)

            builder.enableMinification()

            expect(builder.isMinificationEnabled()).toBe(true)
        })
    })
})