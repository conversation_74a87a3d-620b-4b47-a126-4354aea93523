/**
 * Vite Plugin for Micro-Core
 * Custom Vite plugin to handle micro-frontend specific transformations
 */

import type { Plugin, ResolvedConfig } from 'vite';
import { createFilter } from '@rollup/pluginutils';
import type { VitePluginOptions, ViteHMRContext } from './types';

export function VitePlugin(options: VitePluginOptions = {}): Plugin {
  let config: ResolvedConfig;
  let isDev: boolean;
  let isProd: boolean;

  const filter = createFilter(
    options.microApp?.entry ? [options.microApp.entry] : ['**/*.{js,ts,jsx,tsx,vue}'],
    ['node_modules/**']
  );

  return {
    name: 'micro-core:vite',
    enforce: 'pre',

    configResolved(resolvedConfig) {
      config = resolvedConfig;
      isDev = config.command === 'serve';
      isProd = config.command === 'build';
    },

    buildStart() {
      if (options.dev) {
        console.log('🔧 Micro-Core Vite plugin initialized (development mode)');
      } else {
        console.log('🔧 Micro-Core Vite plugin initialized (production mode)');
      }
    },

    resolveId(id, importer) {
      // Handle micro-app specific module resolution
      if (id.startsWith('@micro-core/')) {
        return this.resolve(id, importer, { skipSelf: true });
      }

      // Handle federation modules
      if (options.microApp?.federation && id.startsWith('federation:')) {
        return this.resolveFederationModule(id);
      }

      return null;
    },

    load(id) {
      // Handle virtual modules
      if (id.startsWith('virtual:micro-core')) {
        return this.loadVirtualModule(id);
      }

      return null;
    },

    transform(code, id) {
      if (!filter(id)) return null;

      let transformedCode = code;

      // Apply custom transformations
      if (options.transforms) {
        for (const transform of options.transforms) {
          if (transform.test.test(id)) {
            transformedCode = transform.transform(transformedCode, id) as string;
          }
        }
      }

      // Add micro-app runtime injection
      if (this.shouldInjectRuntime(id)) {
        transformedCode = this.injectMicroAppRuntime(transformedCode, id);
      }

      // Handle HMR in development
      if (isDev && this.isEntryFile(id)) {
        transformedCode = this.injectHMR(transformedCode, id);
      }

      return {
        code: transformedCode,
        map: null // Source map handling can be added here
      };
    },

    generateBundle(outputOptions, bundle) {
      // Generate micro-app manifest
      if (options.microApp) {
        this.generateManifest(bundle, outputOptions);
      }

      // Handle federation exports
      if (options.microApp?.federation) {
        this.generateFederationExports(bundle, outputOptions);
      }
    },

    writeBundle(outputOptions, bundle) {
      if (isProd) {
        console.log('📦 Micro-app bundle generated successfully');
        this.logBundleInfo(bundle);
      }
    },

    handleHotUpdate(ctx: ViteHMRContext) {
      if (!isDev) return;

      const { file, modules } = ctx;
      
      // Handle micro-app specific HMR
      if (this.isMicroAppFile(file)) {
        console.log(`🔥 Hot updating micro-app file: ${file}`);
        
        // Custom HMR logic for micro-apps
        modules.forEach(mod => {
          this.invalidateModule(mod);
        });

        return modules;
      }

      return undefined;
    },

    // Custom methods
    resolveFederationModule(id: string) {
      const moduleName = id.replace('federation:', '');
      const federationConfig = options.microApp?.federationConfig;
      
      if (federationConfig?.remotes?.[moduleName]) {
        return {
          id: `virtual:federation:${moduleName}`,
          external: true
        };
      }

      return null;
    },

    loadVirtualModule(id: string) {
      if (id === 'virtual:micro-core:runtime') {
        return this.generateRuntimeCode();
      }

      if (id.startsWith('virtual:federation:')) {
        const moduleName = id.replace('virtual:federation:', '');
        return this.generateFederationCode(moduleName);
      }

      return null;
    },

    shouldInjectRuntime(id: string): boolean {
      return this.isEntryFile(id) && !!options.microApp;
    },

    isEntryFile(id: string): boolean {
      const entry = options.microApp?.entry || 'src/index.ts';
      return id.includes(entry) || id.endsWith('/main.ts') || id.endsWith('/main.js');
    },

    isMicroAppFile(file: string): boolean {
      return file.includes('src/') && !file.includes('node_modules/');
    },

    injectMicroAppRuntime(code: string, id: string): string {
      const runtimeImport = `import '@micro-core/runtime';\n`;
      const microAppConfig = `
        window.__MICRO_APP_CONFIG__ = ${JSON.stringify(options.microApp)};
      `;

      return runtimeImport + microAppConfig + code;
    },

    injectHMR(code: string, id: string): string {
      const hmrCode = `
        if (import.meta.hot) {
          import.meta.hot.accept(() => {
            console.log('🔥 Micro-app hot reloaded');
            window.dispatchEvent(new CustomEvent('micro-app:hmr', { 
              detail: { file: '${id}' } 
            }));
          });
        }
      `;

      return code + hmrCode;
    },

    generateRuntimeCode(): string {
      return `
        // Micro-Core Runtime
        export const microCoreRuntime = {
          version: '${process.env.npm_package_version || '0.1.0'}',
          config: window.__MICRO_APP_CONFIG__ || {},
          isDev: ${isDev},
          isProd: ${isProd}
        };

        // Initialize micro-app
        if (typeof window !== 'undefined') {
          window.__MICRO_CORE__ = microCoreRuntime;
          console.log('🚀 Micro-Core runtime initialized');
        }
      `;
    },

    generateFederationCode(moduleName: string): string {
      const federationConfig = options.microApp?.federationConfig;
      const remoteUrl = federationConfig?.remotes?.[moduleName];

      if (!remoteUrl) {
        throw new Error(`Federation remote not found: ${moduleName}`);
      }

      return `
        // Federation module: ${moduleName}
        const loadRemote = async () => {
          const container = await import('${remoteUrl}');
          await container.init(__webpack_share_scopes__.default);
          return container;
        };

        export default loadRemote();
      `;
    },

    generateManifest(bundle: any, outputOptions: any): void {
      const manifest = {
        name: options.microApp?.entry || 'micro-app',
        version: process.env.npm_package_version || '0.1.0',
        framework: this.detectFramework(),
        entry: this.getEntryFile(bundle),
        assets: this.getAssetFiles(bundle),
        shared: options.microApp?.shared || {},
        federation: options.microApp?.federation || false,
        timestamp: new Date().toISOString()
      };

      this.emitFile({
        type: 'asset',
        fileName: 'micro-app.manifest.json',
        source: JSON.stringify(manifest, null, 2)
      });
    },

    generateFederationExports(bundle: any, outputOptions: any): void {
      if (!options.microApp?.federationConfig?.exposes) return;

      const exposes = options.microApp.federationConfig.exposes;
      const federationEntry = `
        // Module Federation Entry
        const exposes = ${JSON.stringify(exposes)};
        
        const moduleMap = {};
        
        Object.keys(exposes).forEach(key => {
          moduleMap[key] = () => import(exposes[key]);
        });
        
        export default moduleMap;
      `;

      this.emitFile({
        type: 'asset',
        fileName: 'federation-entry.js',
        source: federationEntry
      });
    },

    detectFramework(): string {
      // Simple framework detection based on dependencies
      if (config.resolve?.alias?.vue || config.plugins?.some(p => p.name?.includes('vue'))) {
        return 'vue';
      }
      if (config.resolve?.alias?.react || config.plugins?.some(p => p.name?.includes('react'))) {
        return 'react';
      }
      if (config.plugins?.some(p => p.name?.includes('angular'))) {
        return 'angular';
      }
      return 'vanilla';
    },

    getEntryFile(bundle: any): string {
      const entryChunk = Object.values(bundle).find((chunk: any) => chunk.isEntry);
      return entryChunk ? (entryChunk as any).fileName : 'index.js';
    },

    getAssetFiles(bundle: any): string[] {
      return Object.values(bundle)
        .filter((chunk: any) => chunk.type === 'asset')
        .map((chunk: any) => chunk.fileName);
    },

    invalidateModule(mod: any): void {
      // Custom module invalidation logic
      if (mod.id && mod.id.includes('micro-app')) {
        console.log(`♻️  Invalidating micro-app module: ${mod.id}`);
      }
    },

    logBundleInfo(bundle: any): void {
      const chunks = Object.values(bundle).filter((chunk: any) => chunk.type === 'chunk');
      const assets = Object.values(bundle).filter((chunk: any) => chunk.type === 'asset');
      
      console.log(`📊 Bundle info: ${chunks.length} chunks, ${assets.length} assets`);
      
      chunks.forEach((chunk: any) => {
        const size = chunk.code ? chunk.code.length : 0;
        console.log(`   📄 ${chunk.fileName}: ${this.formatBytes(size)}`);
      });
    },

    formatBytes(bytes: number): string {
      if (bytes === 0) return '0 Bytes';
      
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  };
}
