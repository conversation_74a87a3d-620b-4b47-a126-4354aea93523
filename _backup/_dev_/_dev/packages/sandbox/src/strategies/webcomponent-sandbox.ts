/**
 * WebComponent 沙箱策略实现
 * 
 * @description 使用 Web Components 和 Shadow DOM 实现的沙箱隔离策略
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type {
    SandboxConfig,
    SandboxContext,
    SandboxPerformanceMetrics,
    SandboxStrategy
} from '../types';

/**
 * WebComponent 沙箱策略
 * 使用 Web Components 和 Shadow DOM 实现沙箱隔离
 */
export class WebComponentSandbox implements SandboxStrategy {
    readonly type = 'webcomponent' as const;
    readonly name = 'WebComponent沙箱策略';

    private readonly logger: Logger;
    private readonly contexts = new Map<string, WebComponentSandboxContext>();

    constructor() {
        this.logger = new Logger('WebComponentSandbox');
    }

    /**
     * 创建沙箱
     */
    async create(config: SandboxConfig): Promise<SandboxContext> {
        try {
            const sandboxId = this.generateSandboxId();

            // 创建自定义元素
            const customElement = this.createCustomElement(sandboxId, config);

            // 创建 Shadow DOM
            const shadowRoot = this.createShadowDOM(customElement, config);

            // 创建沙箱全局对象
            const sandboxGlobal = this.createSandboxGlobal(shadowRoot, config);

            // 创建沙箱上下文
            const context: SandboxContext = {
                id: sandboxId,
                name: config.name,
                type: 'webcomponent',
                globalProxy: sandboxGlobal,
                state: 'inactive',
                createdAt: Date.now(),
                lastActivity: Date.now()
            };

            // 创建内部上下文
            const internalContext: WebComponentSandboxContext = {
                ...context,
                config,
                customElement,
                shadowRoot,
                sandboxGlobal,
                styleSheets: new Set(),
                eventListeners: new Map(),
                metrics: this.createInitialMetrics()
            };

            this.contexts.set(sandboxId, internalContext);

            this.logger.info('WebComponent沙箱创建成功', {
                sandboxId,
                name: config.name
            });

            return context;
        } catch (error) {
            this.logger.error('WebComponent沙箱创建失败', error);
            throw createError('WEBCOMPONENT_SANDBOX_CREATION_FAILED', `WebComponent沙箱创建失败: ${error.message}`, error);
        }
    }

    /**
     * 激活沙箱
     */
    async activate(context: SandboxContext): Promise<void> {
        const internalContext = this.getInternalContext(context.id);

        try {
            // 将自定义元素添加到 DOM
            this.attachToDOM(internalContext);

            // 应用样式隔离
            this.applyCSSIsolation(internalContext);

            // 设置事件监听
            this.setupEventListeners(internalContext);

            // 更新状态
            context.state = 'active';
            context.lastActivity = Date.now();
            internalContext.metrics.activationCount++;

            this.logger.debug('WebComponent沙箱激活成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('WebComponent沙箱激活失败', error, { sandboxId: context.id });
            throw createError('WEBCOMPONENT_SANDBOX_ACTIVATION_FAILED', `WebComponent沙箱激活失败: ${error.message}`, error);
        }
    }

    /**
     * 停用沙箱
     */
    async deactivate(context: SandboxContext): Promise<void> {
        const internalContext = this.getInternalContext(context.id);

        try {
            // 清理事件监听
            this.cleanupEventListeners(internalContext);

            // 从 DOM 中移除
            this.detachFromDOM(internalContext);

            // 更新状态
            context.state = 'inactive';
            context.lastActivity = Date.now();

            this.logger.debug('WebComponent沙箱停用成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('WebComponent沙箱停用失败', error, { sandboxId: context.id });
            throw createError('WEBCOMPONENT_SANDBOX_DEACTIVATION_FAILED', `WebComponent沙箱停用失败: ${error.message}`, error);
        }
    }

    /**
     * 销毁沙箱
     */
    async destroy(context: SandboxContext): Promise<void> {
        try {
            const internalContext = this.getInternalContext(context.id);

            // 先停用沙箱
            if (context.state === 'active') {
                await this.deactivate(context);
            }

            // 清理样式表
            this.cleanupStyleSheets(internalContext);

            // 清理资源
            this.contexts.delete(context.id);
            context.state = 'destroyed';

            this.logger.debug('WebComponent沙箱销毁成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('WebComponent沙箱销毁失败', error, { sandboxId: context.id });
            throw createError('WEBCOMPONENT_SANDBOX_DESTRUCTION_FAILED', `WebComponent沙箱销毁失败: ${error.message}`, error);
        }
    }

    /**
     * 执行代码
     */
    async execute(context: SandboxContext, code: string): Promise<any> {
        const internalContext = this.getInternalContext(context.id);
        const startTime = Date.now();

        try {
            // 确保沙箱处于激活状态
            if (context.state !== 'active') {
                await this.activate(context);
            }

            // 在 Shadow DOM 环境中执行代码
            const result = this.executeInShadowDOM(internalContext, code);

            // 更新性能指标
            const executionTime = Date.now() - startTime;
            internalContext.metrics.executionTime += executionTime;
            internalContext.metrics.lastUpdated = Date.now();
            context.lastActivity = Date.now();

            this.logger.debug('代码执行成功', {
                sandboxId: context.id,
                executionTime,
                codeLength: code.length
            });

            return result;
        } catch (error) {
            internalContext.metrics.errorCount++;
            this.logger.error('代码执行失败', error, {
                sandboxId: context.id,
                codeSnippet: code.substring(0, 100)
            });
            throw createError('WEBCOMPONENT_SANDBOX_EXECUTION_FAILED', `代码执行失败: ${error.message}`, error);
        }
    }

    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics {
        const internalContext = this.contexts.get(context.id);
        if (!internalContext) {
            return this.createInitialMetrics();
        }

        // 更新内存使用情况
        internalContext.metrics.memoryUsage = this.calculateMemoryUsage(internalContext);
        internalContext.metrics.lastUpdated = Date.now();

        return { ...internalContext.metrics };
    }

    /**
     * 创建自定义元素
     */
    private createCustomElement(sandboxId: string, config: SandboxConfig): HTMLElement {
        const elementName = `micro-sandbox-${sandboxId}`;

        // 定义自定义元素类
        class MicroSandboxElement extends HTMLElement {
            constructor() {
                super();
                this.style.display = 'block';
                this.style.width = '100%';
                this.style.height = '100%';
            }

            connectedCallback() {
                // 元素被添加到 DOM 时的回调
            }

            disconnectedCallback() {
                // 元素从 DOM 中移除时的回调
            }
        }

        // 注册自定义元素
        if (!customElements.get(elementName)) {
            customElements.define(elementName, MicroSandboxElement);
        }

        // 创建元素实例
        const element = document.createElement(elementName) as HTMLElement;
        element.setAttribute('data-sandbox-id', sandboxId);
        element.setAttribute('data-sandbox-name', config.name);

        return element;
    }

    /**
     * 创建 Shadow DOM
     */
    private createShadowDOM(element: HTMLElement, config: SandboxConfig): ShadowRoot {
        // 创建 Shadow Root
        const shadowRoot = element.attachShadow({
            mode: config.isolation.css.strategy === 'shadow' ? 'closed' : 'open'
        });

        // 添加基础样式
        const style = document.createElement('style');
        style.textContent = `
            :host {
                display: block;
                width: 100%;
                height: 100%;
                contain: layout style paint;
            }
            
            * {
                box-sizing: border-box;
            }
        `;
        shadowRoot.appendChild(style);

        // 创建内容容器
        const container = document.createElement('div');
        container.className = 'sandbox-container';
        container.style.width = '100%';
        container.style.height = '100%';
        shadowRoot.appendChild(container);

        return shadowRoot;
    }

    /**
     * 创建沙箱全局对象
     */
    private createSandboxGlobal(shadowRoot: ShadowRoot, config: SandboxConfig): Record<string, any> {
        const sandboxGlobal: Record<string, any> = {};

        // 添加白名单中的全局变量
        if (config.isolation.javascript.enabled) {
            const whitelist = config.isolation.javascript.globalWhitelist;

            for (const prop of whitelist) {
                if (prop in window) {
                    try {
                        sandboxGlobal[prop] = (window as any)[prop];
                    } catch (error) {
                        this.logger.warn('无法复制全局变量', { prop, error: error.message });
                    }
                }
            }
        }

        // 添加 Shadow DOM 特有的 API
        sandboxGlobal.shadowRoot = shadowRoot;
        sandboxGlobal.document = shadowRoot;
        sandboxGlobal.window = sandboxGlobal;
        sandboxGlobal.self = sandboxGlobal;
        sandboxGlobal.globalThis = sandboxGlobal;

        // 重写 DOM 查询方法
        sandboxGlobal.querySelector = (selector: string) => shadowRoot.querySelector(selector);
        sandboxGlobal.querySelectorAll = (selector: string) => shadowRoot.querySelectorAll(selector);
        sandboxGlobal.getElementById = (id: string) => shadowRoot.getElementById(id);

        return sandboxGlobal;
    }

    /**
     * 附加到 DOM
     */
    private attachToDOM(context: WebComponentSandboxContext): void {
        if (!context.customElement.parentNode) {
            document.body.appendChild(context.customElement);
        }
    }

    /**
     * 从 DOM 中分离
     */
    private detachFromDOM(context: WebComponentSandboxContext): void {
        if (context.customElement.parentNode) {
            context.customElement.parentNode.removeChild(context.customElement);
        }
    }

    /**
     * 应用 CSS 隔离
     */
    private applyCSSIsolation(context: WebComponentSandboxContext): void {
        const { css } = context.config.isolation;

        if (!css.enabled) {
            return;
        }

        // Shadow DOM 天然提供 CSS 隔离
        // 这里可以添加额外的样式隔离逻辑

        if (css.isolateGlobalStyles) {
            // 阻止全局样式影响 Shadow DOM
            const style = document.createElement('style');
            style.textContent = `
                :host {
                    all: initial;
                }
            `;
            context.shadowRoot.appendChild(style);
            context.styleSheets.add(style);
        }
    }

    /**
     * 设置事件监听
     */
    private setupEventListeners(context: WebComponentSandboxContext): void {
        // 监听自定义事件
        const customEventHandler = (event: CustomEvent) => {
            this.handleCustomEvent(context, event);
        };

        context.customElement.addEventListener('micro-sandbox-event', customEventHandler);
        context.eventListeners.set('custom', customEventHandler);
    }

    /**
     * 清理事件监听
     */
    private cleanupEventListeners(context: WebComponentSandboxContext): void {
        for (const [eventType, handler] of context.eventListeners) {
            context.customElement.removeEventListener(eventType, handler);
        }
        context.eventListeners.clear();
    }

    /**
     * 处理自定义事件
     */
    private handleCustomEvent(context: WebComponentSandboxContext, event: CustomEvent): void {
        try {
            this.logger.debug('收到自定义事件', {
                sandboxId: context.id,
                eventType: event.type,
                detail: event.detail
            });
            // 这里可以处理来自沙箱的自定义事件
        } catch (error) {
            this.logger.error('处理自定义事件失败', error, { sandboxId: context.id });
        }
    }

    /**
     * 在 Shadow DOM 环境中执行代码
     */
    private executeInShadowDOM(context: WebComponentSandboxContext, code: string): any {
        // 创建执行函数
        const executeFunction = new Function(
            'shadowRoot',
            'document',
            'window',
            'self',
            'globalThis',
            `
            with (window) {
                return (function() {
                    "use strict";
                    ${code}
                })();
            }
        `);

        // 在沙箱环境中执行
        return executeFunction(
            context.shadowRoot,
            context.shadowRoot,
            context.sandboxGlobal,
            context.sandboxGlobal,
            context.sandboxGlobal
        );
    }

    /**
     * 清理样式表
     */
    private cleanupStyleSheets(context: WebComponentSandboxContext): void {
        for (const styleSheet of context.styleSheets) {
            if (styleSheet.parentNode) {
                styleSheet.parentNode.removeChild(styleSheet);
            }
        }
        context.styleSheets.clear();
    }

    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage(context: WebComponentSandboxContext): number {
        // WebComponent 沙箱的内存使用量估算
        let size = 0;

        // 自定义元素的大小
        size += 1000; // 估算自定义元素 1KB

        // Shadow DOM 的大小
        size += context.shadowRoot.childNodes.length * 100;

        // 样式表的大小
        for (const styleSheet of context.styleSheets) {
            size += styleSheet.textContent?.length || 0;
        }

        // 事件监听器的大小
        size += context.eventListeners.size * 100;

        // 沙箱全局对象的大小
        for (const [key, value] of Object.entries(context.sandboxGlobal)) {
            size += key.length * 2;
            size += this.estimateValueSize(value);
        }

        return size;
    }

    /**
     * 估算值的大小
     */
    private estimateValueSize(value: any): number {
        if (value === null || value === undefined) {
            return 8;
        }

        switch (typeof value) {
            case 'boolean':
                return 4;
            case 'number':
                return 8;
            case 'string':
                return value.length * 2;
            case 'object':
                try {
                    return JSON.stringify(value).length * 2;
                } catch {
                    return 100; // 估算值
                }
            case 'function':
                return value.toString().length * 2;
            default:
                return 8;
        }
    }

    /**
     * 获取内部上下文
     */
    private getInternalContext(sandboxId: string): WebComponentSandboxContext {
        const context = this.contexts.get(sandboxId);
        if (!context) {
            throw createError('SANDBOX_NOT_FOUND', `沙箱不存在: ${sandboxId}`);
        }
        return context;
    }

    /**
     * 生成沙箱ID
     */
    private generateSandboxId(): string {
        return `webcomponent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 创建初始性能指标
     */
    private createInitialMetrics(): SandboxPerformanceMetrics {
        return {
            memoryUsage: 0,
            cpuUsage: 0,
            executionTime: 0,
            creationTime: 0,
            activationCount: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };
    }
}

/**
 * WebComponent 沙箱内部上下文
 */
interface WebComponentSandboxContext extends SandboxContext {
    config: SandboxConfig;
    customElement: HTMLElement;
    shadowRoot: ShadowRoot;
    sandboxGlobal: Record<string, any>;
    styleSheets: Set<HTMLStyleElement>;
    eventListeners: Map<string, EventListener>;
    metrics: SandboxPerformanceMetrics;
}