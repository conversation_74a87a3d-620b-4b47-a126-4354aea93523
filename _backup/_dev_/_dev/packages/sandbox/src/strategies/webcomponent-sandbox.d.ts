/**
 * WebComponent 沙箱策略实现
 *
 * @description 使用 Web Components 和 Shadow DOM 实现的沙箱隔离策略
 * <AUTHOR> <<EMAIL>>
 */
import type { SandboxConfig, SandboxContext, SandboxPerformanceMetrics, SandboxStrategy } from '../types';
/**
 * WebComponent 沙箱策略
 * 使用 Web Components 和 Shadow DOM 实现沙箱隔离
 */
export declare class WebComponentSandbox implements SandboxStrategy {
    readonly type: "webcomponent";
    readonly name = "WebComponent\u6C99\u7BB1\u7B56\u7565";
    private readonly logger;
    private readonly contexts;
    constructor();
    /**
     * 创建沙箱
     */
    create(config: SandboxConfig): Promise<SandboxContext>;
    /**
     * 激活沙箱
     */
    activate(context: SandboxContext): Promise<void>;
    /**
     * 停用沙箱
     */
    deactivate(context: SandboxContext): Promise<void>;
    /**
     * 销毁沙箱
     */
    destroy(context: SandboxContext): Promise<void>;
    /**
     * 执行代码
     */
    execute(context: SandboxContext, code: string): Promise<any>;
    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics;
    /**
     * 创建自定义元素
     */
    private createCustomElement;
    /**
     * 创建 Shadow DOM
     */
    private createShadowDOM;
    /**
     * 创建沙箱全局对象
     */
    private createSandboxGlobal;
    /**
     * 附加到 DOM
     */
    private attachToDOM;
    /**
     * 从 DOM 中分离
     */
    private detachFromDOM;
    /**
     * 应用 CSS 隔离
     */
    private applyCSSIsolation;
    /**
     * 设置事件监听
     */
    private setupEventListeners;
    /**
     * 清理事件监听
     */
    private cleanupEventListeners;
    /**
     * 处理自定义事件
     */
    private handleCustomEvent;
    /**
     * 在 Shadow DOM 环境中执行代码
     */
    private executeInShadowDOM;
    /**
     * 清理样式表
     */
    private cleanupStyleSheets;
    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage;
    /**
     * 估算值的大小
     */
    private estimateValueSize;
    /**
     * 获取内部上下文
     */
    private getInternalContext;
    /**
     * 生成沙箱ID
     */
    private generateSandboxId;
    /**
     * 创建初始性能指标
     */
    private createInitialMetrics;
}
//# sourceMappingURL=webcomponent-sandbox.d.ts.map