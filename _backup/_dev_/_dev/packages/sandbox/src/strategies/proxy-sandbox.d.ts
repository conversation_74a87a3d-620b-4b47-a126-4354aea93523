/**
 * @fileoverview Proxy沙箱策略
 * @description 基于Proxy的JavaScript沙箱隔离实现
 * <AUTHOR> <<EMAIL>>
 */
import { SandboxContext, SandboxOptions, SandboxStrategy } from '../types';
/**
 * Proxy沙箱策略实现
 * 通过Proxy代理全局对象，实现JavaScript运行时隔离
 */
export declare class ProxySandbox implements SandboxStrategy {
    private readonly name;
    private readonly options;
    private proxyWindow;
    private fakeWindow;
    private addedPropsMapInSandbox;
    private modifiedPropsOriginalValueMapInSandbox;
    private currentUpdatedPropsValueMap;
    private active;
    constructor(name: string, options?: SandboxOptions);
    private createProxyWindow;
    private isPropertyConfigurable;
    getName(): string;
    getType(): string;
    isActive(): boolean;
    activate(): void;
    deactivate(): void;
    private restoreGlobalProps;
    getContext(): SandboxContext;
    execScript(script: string): any;
    destroy(): void;
}
//# sourceMappingURL=proxy-sandbox.d.ts.map