/**
 * Federation 沙箱策略实现
 *
 * @description 使用模块联邦实现的沙箱隔离策略
 * <AUTHOR> <<EMAIL>>
 */
import type { SandboxConfig, SandboxContext, SandboxPerformanceMetrics, SandboxStrategy } from '../types';
/**
 * Federation 沙箱策略
 * 使用模块联邦实现沙箱隔离
 */
export declare class FederationSandbox implements SandboxStrategy {
    readonly type: "federation";
    readonly name = "Federation\u6C99\u7BB1\u7B56\u7565";
    private readonly logger;
    private readonly contexts;
    private readonly federationRegistry;
    constructor();
    /**
     * 创建沙箱
     */
    create(config: SandboxConfig): Promise<SandboxContext>;
    /**
     * 激活沙箱
     */
    activate(context: SandboxContext): Promise<void>;
    /**
     * 停用沙箱
     */
    deactivate(context: SandboxContext): Promise<void>;
    /**
     * 销毁沙箱
     */
    destroy(context: SandboxContext): Promise<void>;
    /**
     * 执行代码
     */
    execute(context: SandboxContext, code: string): Promise<any>;
    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics;
    /**
     * 初始化联邦环境
     */
    private initializeFederationEnvironment;
    /**
     * 生成联邦名称
     */
    private generateFederationName;
    /**
     * 创建联邦模块
     */
    private createFederationModule;
    /**
     * 创建联邦运行时
     */
    private createFederationRuntime;
    /**
     * 注册联邦模块
     */
    private registerFederationModule;
    /**
     * 注销联邦模块
     */
    private unregisterFederationModule;
    /**
     * 创建沙箱全局对象
     */
    private createSandboxGlobal;
    /**
     * 激活联邦模块
     */
    private activateFederationModule;
    /**
     * 停用联邦模块
     */
    private deactivateFederationModule;
    /**
     * 初始化联邦容器
     */
    private initializeFederationContainer;
    /**
     * 清理联邦容器
     */
    private cleanupFederationContainer;
    /**
     * 设置模块共享
     */
    private setupModuleSharing;
    /**
     * 清理模块共享
     */
    private cleanupModuleSharing;
    /**
     * 加载远程模块
     */
    private loadRemoteModules;
    /**
     * 卸载远程模块
     */
    private unloadRemoteModules;
    /**
     * 导入模块
     */
    private importModule;
    /**
     * 导出模块
     */
    private exportModule;
    /**
     * 共享模块
     */
    private shareModule;
    /**
     * 获取共享模块
     */
    private getSharedModule;
    /**
     * 在联邦环境中执行代码
     */
    private executeInFederation;
    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage;
    /**
     * 估算值的大小
     */
    private estimateValueSize;
    /**
     * 获取内部上下文
     */
    private getInternalContext;
    /**
     * 生成沙箱ID
     */
    private generateSandboxId;
    /**
     * 创建初始性能指标
     */
    private createInitialMetrics;
}
//# sourceMappingURL=federation-sandbox.d.ts.map