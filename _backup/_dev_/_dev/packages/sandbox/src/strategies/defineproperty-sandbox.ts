/**
 * DefineProperty 沙箱策略实现
 * 
 * @description 使用 Object.defineProperty 实现的沙箱隔离策略
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type {
    SandboxConfig,
    SandboxContext,
    SandboxPerformanceMetrics,
    SandboxStrategy
} from '../types';

/**
 * DefineProperty 沙箱策略
 * 使用 Object.defineProperty 实现全局变量隔离
 */
export class DefinePropertySandbox implements SandboxStrategy {
    readonly type = 'defineproperty' as const;
    readonly name = 'DefineProperty沙箱策略';

    private readonly logger: Logger;
    private readonly contexts = new Map<string, DefinePropertySandboxContext>();

    constructor() {
        this.logger = new Logger('DefinePropertySandbox');
    }

    /**
     * 创建沙箱
     */
    async create(config: SandboxConfig): Promise<SandboxContext> {
        try {
            const sandboxId = this.generateSandboxId();

            // 创建沙箱全局对象
            const sandboxGlobal = this.createSandboxGlobal(config);

            // 创建沙箱上下文
            const context: SandboxContext = {
                id: sandboxId,
                name: config.name,
                type: 'defineproperty',
                globalProxy: sandboxGlobal,
                state: 'inactive',
                createdAt: Date.now(),
                lastActivity: Date.now()
            };

            // 创建内部上下文
            const internalContext: DefinePropertySandboxContext = {
                ...context,
                config,
                sandboxGlobal,
                originalDescriptors: new Map(),
                modifiedProps: new Set(),
                metrics: this.createInitialMetrics()
            };

            this.contexts.set(sandboxId, internalContext);

            this.logger.info('DefineProperty沙箱创建成功', {
                sandboxId,
                name: config.name
            });

            return context;
        } catch (error) {
            this.logger.error('DefineProperty沙箱创建失败', error);
            throw createError('DEFINEPROPERTY_SANDBOX_CREATION_FAILED', `DefineProperty沙箱创建失败: ${error.message}`, error);
        }
    }

    /**
     * 激活沙箱
     */
    async activate(context: SandboxContext): Promise<void> {
        const internalContext = this.getInternalContext(context.id);

        try {
            // 应用沙箱环境
            this.applySandboxEnvironment(internalContext);

            // 更新状态
            context.state = 'active';
            context.lastActivity = Date.now();
            internalContext.metrics.activationCount++;

            this.logger.debug('DefineProperty沙箱激活成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('DefineProperty沙箱激活失败', error, { sandboxId: context.id });
            throw createError('DEFINEPROPERTY_SANDBOX_ACTIVATION_FAILED', `DefineProperty沙箱激活失败: ${error.message}`, error);
        }
    }

    /**
     * 停用沙箱
     */
    async deactivate(context: SandboxContext): Promise<void> {
        const internalContext = this.getInternalContext(context.id);

        try {
            // 恢复原始环境
            this.restoreOriginalEnvironment(internalContext);

            // 更新状态
            context.state = 'inactive';
            context.lastActivity = Date.now();

            this.logger.debug('DefineProperty沙箱停用成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('DefineProperty沙箱停用失败', error, { sandboxId: context.id });
            throw createError('DEFINEPROPERTY_SANDBOX_DEACTIVATION_FAILED', `DefineProperty沙箱停用失败: ${error.message}`, error);
        }
    }

    /**
     * 销毁沙箱
     */
    async destroy(context: SandboxContext): Promise<void> {
        try {
            // 先停用沙箱
            if (context.state === 'active') {
                await this.deactivate(context);
            }

            // 清理资源
            this.contexts.delete(context.id);
            context.state = 'destroyed';

            this.logger.debug('DefineProperty沙箱销毁成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('DefineProperty沙箱销毁失败', error, { sandboxId: context.id });
            throw createError('DEFINEPROPERTY_SANDBOX_DESTRUCTION_FAILED', `DefineProperty沙箱销毁失败: ${error.message}`, error);
        }
    }

    /**
     * 执行代码
     */
    async execute(context: SandboxContext, code: string): Promise<any> {
        const internalContext = this.getInternalContext(context.id);
        const startTime = Date.now();

        try {
            // 确保沙箱处于激活状态
            if (context.state !== 'active') {
                await this.activate(context);
            }

            // 在沙箱环境中执行代码
            const result = this.executeInSandboxEnvironment(internalContext, code);

            // 更新性能指标
            const executionTime = Date.now() - startTime;
            internalContext.metrics.executionTime += executionTime;
            internalContext.metrics.lastUpdated = Date.now();
            context.lastActivity = Date.now();

            this.logger.debug('代码执行成功', {
                sandboxId: context.id,
                executionTime,
                codeLength: code.length
            });

            return result;
        } catch (error) {
            internalContext.metrics.errorCount++;
            this.logger.error('代码执行失败', error, {
                sandboxId: context.id,
                codeSnippet: code.substring(0, 100)
            });
            throw createError('DEFINEPROPERTY_SANDBOX_EXECUTION_FAILED', `代码执行失败: ${error.message}`, error);
        }
    }

    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics {
        const internalContext = this.contexts.get(context.id);
        if (!internalContext) {
            return this.createInitialMetrics();
        }

        // 更新内存使用情况
        internalContext.metrics.memoryUsage = this.calculateMemoryUsage(internalContext);
        internalContext.metrics.lastUpdated = Date.now();

        return { ...internalContext.metrics };
    }

    /**
     * 创建沙箱全局对象
     */
    private createSandboxGlobal(config: SandboxConfig): Record<string, any> {
        const sandboxGlobal: Record<string, any> = {};

        // 添加白名单中的全局变量
        if (config.isolation.javascript.enabled) {
            const whitelist = config.isolation.javascript.globalWhitelist;

            for (const prop of whitelist) {
                if (prop in window) {
                    try {
                        sandboxGlobal[prop] = (window as any)[prop];
                    } catch (error) {
                        this.logger.warn('无法复制全局变量', { prop, error: error.message });
                    }
                }
            }
        }

        // 添加基础 API
        sandboxGlobal.window = sandboxGlobal;
        sandboxGlobal.self = sandboxGlobal;
        sandboxGlobal.globalThis = sandboxGlobal;

        return sandboxGlobal;
    }

    /**
     * 应用沙箱环境
     */
    private applySandboxEnvironment(context: DefinePropertySandboxContext): void {
        const { globalBlacklist } = context.config.isolation.javascript;

        // 使用 defineProperty 劫持全局变量
        for (const prop of globalBlacklist) {
            if (prop in window) {
                try {
                    // 保存原始描述符
                    const originalDescriptor = Object.getOwnPropertyDescriptor(window, prop);
                    if (originalDescriptor) {
                        context.originalDescriptors.set(prop, originalDescriptor);
                    }

                    // 重新定义属性，指向沙箱全局对象
                    Object.defineProperty(window, prop, {
                        get: () => {
                            // 检查权限
                            if (globalBlacklist.includes(prop)) {
                                throw createError('PERMISSION_DENIED', `访问被禁止的全局变量: ${prop}`);
                            }
                            return context.sandboxGlobal[prop];
                        },
                        set: (value) => {
                            // 检查权限
                            if (globalBlacklist.includes(prop)) {
                                throw createError('PERMISSION_DENIED', `设置被禁止的全局变量: ${prop}`);
                            }
                            context.sandboxGlobal[prop] = value;
                            context.modifiedProps.add(prop);
                        },
                        configurable: true,
                        enumerable: originalDescriptor?.enumerable ?? true
                    });

                    this.logger.debug('全局变量劫持成功', { prop });
                } catch (error) {
                    this.logger.warn('无法劫持全局变量', { prop, error: error.message });
                }
            }
        }
    }

    /**
     * 恢复原始环境
     */
    private restoreOriginalEnvironment(context: DefinePropertySandboxContext): void {
        // 恢复原始属性描述符
        for (const [prop, descriptor] of context.originalDescriptors) {
            try {
                if (descriptor) {
                    Object.defineProperty(window, prop, descriptor);
                } else {
                    delete (window as any)[prop];
                }
            } catch (error) {
                this.logger.warn('无法恢复全局变量', { prop, error: error.message });
            }
        }

        // 清理状态
        context.originalDescriptors.clear();
        context.modifiedProps.clear();
    }

    /**
     * 在沙箱环境中执行代码
     */
    private executeInSandboxEnvironment(context: DefinePropertySandboxContext, code: string): any {
        // 创建执行函数
        const executeFunction = new Function('window', 'self', 'globalThis', `
            return (function() {
                "use strict";
                ${code}
            })();
        `);

        // 在沙箱环境中执行
        return executeFunction(context.sandboxGlobal, context.sandboxGlobal, context.sandboxGlobal);
    }

    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage(context: DefinePropertySandboxContext): number {
        // 简单的内存使用量估算
        let size = 0;

        // 计算沙箱全局对象的大小
        for (const [key, value] of Object.entries(context.sandboxGlobal)) {
            size += key.length * 2; // 字符串键的大小
            size += this.estimateValueSize(value);
        }

        // 计算描述符映射的大小
        size += context.originalDescriptors.size * 100; // 估算每个描述符100字节

        return size;
    }

    /**
     * 估算值的大小
     */
    private estimateValueSize(value: any): number {
        if (value === null || value === undefined) {
            return 8;
        }

        switch (typeof value) {
            case 'boolean':
                return 4;
            case 'number':
                return 8;
            case 'string':
                return value.length * 2;
            case 'object':
                return JSON.stringify(value).length * 2;
            case 'function':
                return value.toString().length * 2;
            default:
                return 8;
        }
    }

    /**
     * 获取内部上下文
     */
    private getInternalContext(sandboxId: string): DefinePropertySandboxContext {
        const context = this.contexts.get(sandboxId);
        if (!context) {
            throw createError('SANDBOX_NOT_FOUND', `沙箱不存在: ${sandboxId}`);
        }
        return context;
    }

    /**
     * 生成沙箱ID
     */
    private generateSandboxId(): string {
        return `defineproperty_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 创建初始性能指标
     */
    private createInitialMetrics(): SandboxPerformanceMetrics {
        return {
            memoryUsage: 0,
            cpuUsage: 0,
            executionTime: 0,
            creationTime: 0,
            activationCount: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };
    }
}

/**
 * DefineProperty 沙箱内部上下文
 */
interface DefinePropertySandboxContext extends SandboxContext {
    config: SandboxConfig;
    sandboxGlobal: Record<string, any>;
    originalDescriptors: Map<string, PropertyDescriptor | undefined>;
    modifiedProps: Set<string>;
    metrics: SandboxPerformanceMetrics;
}