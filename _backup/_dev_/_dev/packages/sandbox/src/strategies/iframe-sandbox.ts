/**
 * iframe 沙箱策略实现
 * 
 * @description 使用 iframe 实现的完全隔离沙箱策略
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type {
    SandboxConfig,
    SandboxContext,
    SandboxPerformanceMetrics,
    SandboxStrategy
} from '../types';

/**
 * iframe 沙箱策略
 * 使用 iframe 实现完全隔离的沙箱环境
 */
export class IframeSandbox implements SandboxStrategy {
    readonly type = 'iframe' as const;
    readonly name = 'iframe沙箱策略';

    private readonly logger: Logger;
    private readonly contexts = new Map<string, IframeSandboxContext>();

    constructor() {
        this.logger = new Logger('IframeSandbox');
    }

    /**
     * 创建沙箱
     */
    async create(config: SandboxConfig): Promise<SandboxContext> {
        try {
            const sandboxId = this.generateSandboxId();

            // 创建 iframe 元素
            const iframe = this.createIframe(config);

            // 等待 iframe 加载完成
            await this.waitForIframeLoad(iframe);

            // 获取 iframe 的 window 对象
            const iframeWindow = iframe.contentWindow;
            if (!iframeWindow) {
                throw createError('IFRAME_WINDOW_NOT_AVAILABLE', 'iframe window 对象不可用');
            }

            // 设置沙箱环境
            this.setupSandboxEnvironment(iframeWindow, config);

            // 创建沙箱上下文
            const context: SandboxContext = {
                id: sandboxId,
                name: config.name,
                type: 'iframe',
                globalProxy: iframeWindow,
                state: 'inactive',
                createdAt: Date.now(),
                lastActivity: Date.now()
            };

            // 创建内部上下文
            const internalContext: IframeSandboxContext = {
                ...context,
                config,
                iframe,
                iframeWindow,
                messageHandlers: new Map(),
                metrics: this.createInitialMetrics()
            };

            this.contexts.set(sandboxId, internalContext);

            this.logger.info('iframe沙箱创建成功', {
                sandboxId,
                name: config.name
            });

            return context;
        } catch (error) {
            this.logger.error('iframe沙箱创建失败', error);
            throw createError('IFRAME_SANDBOX_CREATION_FAILED', `iframe沙箱创建失败: ${error.message}`, error);
        }
    }

    /**
     * 激活沙箱
     */
    async activate(context: SandboxContext): Promise<void> {
        const internalContext = this.getInternalContext(context.id);

        try {
            // 显示 iframe
            this.showIframe(internalContext.iframe);

            // 设置消息监听
            this.setupMessageListeners(internalContext);

            // 更新状态
            context.state = 'active';
            context.lastActivity = Date.now();
            internalContext.metrics.activationCount++;

            this.logger.debug('iframe沙箱激活成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('iframe沙箱激活失败', error, { sandboxId: context.id });
            throw createError('IFRAME_SANDBOX_ACTIVATION_FAILED', `iframe沙箱激活失败: ${error.message}`, error);
        }
    }

    /**
     * 停用沙箱
     */
    async deactivate(context: SandboxContext): Promise<void> {
        const internalContext = this.getInternalContext(context.id);

        try {
            // 隐藏 iframe
            this.hideIframe(internalContext.iframe);

            // 清理消息监听
            this.cleanupMessageListeners(internalContext);

            // 更新状态
            context.state = 'inactive';
            context.lastActivity = Date.now();

            this.logger.debug('iframe沙箱停用成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('iframe沙箱停用失败', error, { sandboxId: context.id });
            throw createError('IFRAME_SANDBOX_DEACTIVATION_FAILED', `iframe沙箱停用失败: ${error.message}`, error);
        }
    }

    /**
     * 销毁沙箱
     */
    async destroy(context: SandboxContext): Promise<void> {
        try {
            const internalContext = this.getInternalContext(context.id);

            // 先停用沙箱
            if (context.state === 'active') {
                await this.deactivate(context);
            }

            // 移除 iframe
            this.removeIframe(internalContext.iframe);

            // 清理资源
            this.contexts.delete(context.id);
            context.state = 'destroyed';

            this.logger.debug('iframe沙箱销毁成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('iframe沙箱销毁失败', error, { sandboxId: context.id });
            throw createError('IFRAME_SANDBOX_DESTRUCTION_FAILED', `iframe沙箱销毁失败: ${error.message}`, error);
        }
    }

    /**
     * 执行代码
     */
    async execute(context: SandboxContext, code: string): Promise<any> {
        const internalContext = this.getInternalContext(context.id);
        const startTime = Date.now();

        try {
            // 确保沙箱处于激活状态
            if (context.state !== 'active') {
                await this.activate(context);
            }

            // 在 iframe 中执行代码
            const result = await this.executeInIframe(internalContext, code);

            // 更新性能指标
            const executionTime = Date.now() - startTime;
            internalContext.metrics.executionTime += executionTime;
            internalContext.metrics.lastUpdated = Date.now();
            context.lastActivity = Date.now();

            this.logger.debug('代码执行成功', {
                sandboxId: context.id,
                executionTime,
                codeLength: code.length
            });

            return result;
        } catch (error) {
            internalContext.metrics.errorCount++;
            this.logger.error('代码执行失败', error, {
                sandboxId: context.id,
                codeSnippet: code.substring(0, 100)
            });
            throw createError('IFRAME_SANDBOX_EXECUTION_FAILED', `代码执行失败: ${error.message}`, error);
        }
    }

    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics {
        const internalContext = this.contexts.get(context.id);
        if (!internalContext) {
            return this.createInitialMetrics();
        }

        // 更新内存使用情况
        internalContext.metrics.memoryUsage = this.calculateMemoryUsage(internalContext);
        internalContext.metrics.lastUpdated = Date.now();

        return { ...internalContext.metrics };
    }

    /**
     * 创建 iframe 元素
     */
    private createIframe(config: SandboxConfig): HTMLIFrameElement {
        const iframe = document.createElement('iframe');

        // 设置 iframe 属性
        iframe.style.display = 'none';
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';

        // 设置安全属性
        iframe.sandbox.add(
            'allow-scripts',
            'allow-same-origin'
        );

        // 如果配置允许，添加额外权限
        if (config.permissions?.allowForms) {
            iframe.sandbox.add('allow-forms');
        }
        if (config.permissions?.allowPopups) {
            iframe.sandbox.add('allow-popups');
        }
        if (config.permissions?.allowModals) {
            iframe.sandbox.add('allow-modals');
        }

        // 设置 src 为空白页面
        iframe.src = 'about:blank';

        // 添加到 DOM
        document.body.appendChild(iframe);

        return iframe;
    }

    /**
     * 等待 iframe 加载完成
     */
    private waitForIframeLoad(iframe: HTMLIFrameElement): Promise<void> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(createError('IFRAME_LOAD_TIMEOUT', 'iframe 加载超时'));
            }, 5000);

            iframe.onload = () => {
                clearTimeout(timeout);
                resolve();
            };

            iframe.onerror = () => {
                clearTimeout(timeout);
                reject(createError('IFRAME_LOAD_ERROR', 'iframe 加载失败'));
            };
        });
    }

    /**
     * 设置沙箱环境
     */
    private setupSandboxEnvironment(iframeWindow: Window, config: SandboxConfig): void {
        try {
            // 注入基础 API
            this.injectBasicAPIs(iframeWindow, config);

            // 设置全局变量限制
            this.setupGlobalVariableRestrictions(iframeWindow, config);

            // 设置通信接口
            this.setupCommunicationInterface(iframeWindow);

            this.logger.debug('沙箱环境设置完成');
        } catch (error) {
            this.logger.error('沙箱环境设置失败', error);
            throw createError('SANDBOX_ENVIRONMENT_SETUP_FAILED', `沙箱环境设置失败: ${error.message}`, error);
        }
    }

    /**
     * 注入基础 API
     */
    private injectBasicAPIs(iframeWindow: Window, config: SandboxConfig): void {
        const { globalWhitelist } = config.isolation.javascript;

        // 注入白名单中的全局变量
        for (const prop of globalWhitelist) {
            if (prop in window && !(prop in iframeWindow)) {
                try {
                    (iframeWindow as any)[prop] = (window as any)[prop];
                } catch (error) {
                    this.logger.warn('无法注入全局变量', { prop, error: error.message });
                }
            }
        }

        // 注入必要的 API
        if (!iframeWindow.console) {
            iframeWindow.console = window.console;
        }
    }

    /**
     * 设置全局变量限制
     */
    private setupGlobalVariableRestrictions(iframeWindow: Window, config: SandboxConfig): void {
        const { globalBlacklist } = config.isolation.javascript;

        // 删除黑名单中的全局变量
        for (const prop of globalBlacklist) {
            if (prop in iframeWindow) {
                try {
                    delete (iframeWindow as any)[prop];
                } catch (error) {
                    this.logger.warn('无法删除全局变量', { prop, error: error.message });
                }
            }
        }
    }

    /**
     * 设置通信接口
     */
    private setupCommunicationInterface(iframeWindow: Window): void {
        // 注入通信函数
        (iframeWindow as any).__MICRO_CORE_COMMUNICATION__ = {
            postMessage: (data: any) => {
                window.postMessage({
                    type: 'MICRO_CORE_MESSAGE',
                    data
                }, '*');
            }
        };
    }

    /**
     * 设置消息监听
     */
    private setupMessageListeners(context: IframeSandboxContext): void {
        const messageHandler = (event: MessageEvent) => {
            if (event.source === context.iframeWindow) {
                this.handleIframeMessage(context, event.data);
            }
        };

        window.addEventListener('message', messageHandler);
        context.messageHandlers.set('main', messageHandler);
    }

    /**
     * 清理消息监听
     */
    private cleanupMessageListeners(context: IframeSandboxContext): void {
        for (const [key, handler] of context.messageHandlers) {
            window.removeEventListener('message', handler);
        }
        context.messageHandlers.clear();
    }

    /**
     * 处理 iframe 消息
     */
    private handleIframeMessage(context: IframeSandboxContext, data: any): void {
        try {
            if (data.type === 'MICRO_CORE_MESSAGE') {
                this.logger.debug('收到iframe消息', { sandboxId: context.id, data: data.data });
                // 这里可以处理来自 iframe 的消息
            }
        } catch (error) {
            this.logger.error('处理iframe消息失败', error, { sandboxId: context.id });
        }
    }

    /**
     * 在 iframe 中执行代码
     */
    private async executeInIframe(context: IframeSandboxContext, code: string): Promise<any> {
        return new Promise((resolve, reject) => {
            try {
                // 创建执行函数
                const executeFunction = context.iframeWindow.Function(`
                    return (function() {
                        "use strict";
                        ${code}
                    })();
                `);

                // 执行代码
                const result = executeFunction();
                resolve(result);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 显示 iframe
     */
    private showIframe(iframe: HTMLIFrameElement): void {
        iframe.style.display = 'block';
    }

    /**
     * 隐藏 iframe
     */
    private hideIframe(iframe: HTMLIFrameElement): void {
        iframe.style.display = 'none';
    }

    /**
     * 移除 iframe
     */
    private removeIframe(iframe: HTMLIFrameElement): void {
        if (iframe.parentNode) {
            iframe.parentNode.removeChild(iframe);
        }
    }

    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage(context: IframeSandboxContext): number {
        // iframe 沙箱的内存使用量估算
        let size = 0;

        // iframe 元素本身的大小
        size += 1000; // 估算 iframe 元素 1KB

        // 消息处理器的大小
        size += context.messageHandlers.size * 100;

        return size;
    }

    /**
     * 获取内部上下文
     */
    private getInternalContext(sandboxId: string): IframeSandboxContext {
        const context = this.contexts.get(sandboxId);
        if (!context) {
            throw createError('SANDBOX_NOT_FOUND', `沙箱不存在: ${sandboxId}`);
        }
        return context;
    }

    /**
     * 生成沙箱ID
     */
    private generateSandboxId(): string {
        return `iframe_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 创建初始性能指标
     */
    private createInitialMetrics(): SandboxPerformanceMetrics {
        return {
            memoryUsage: 0,
            cpuUsage: 0,
            executionTime: 0,
            creationTime: 0,
            activationCount: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };
    }
}

/**
 * iframe 沙箱内部上下文
 */
interface IframeSandboxContext extends SandboxContext {
    config: SandboxConfig;
    iframe: HTMLIFrameElement;
    iframeWindow: Window;
    messageHandlers: Map<string, (event: MessageEvent) => void>;
    metrics: SandboxPerformanceMetrics;
}