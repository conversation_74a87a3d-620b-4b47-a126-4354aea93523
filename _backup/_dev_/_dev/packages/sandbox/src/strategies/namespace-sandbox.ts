/**
 * Namespace 沙箱策略实现
 * 
 * @description 使用命名空间实现的轻量级沙箱隔离策略
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type {
    SandboxConfig,
    SandboxContext,
    SandboxPerformanceMetrics,
    SandboxStrategy
} from '../types';

/**
 * Namespace 沙箱策略
 * 使用命名空间实现轻量级沙箱隔离
 */
export class NamespaceSandbox implements SandboxStrategy {
    readonly type = 'namespace' as const;
    readonly name = 'Namespace沙箱策略';

    private readonly logger: Logger;
    private readonly contexts = new Map<string, NamespaceSandboxContext>();
    private readonly globalNamespace = '__MICRO_CORE_NAMESPACES__';

    constructor() {
        this.logger = new Logger('NamespaceSandbox');
        this.initializeGlobalNamespace();
    }

    /**
     * 创建沙箱
     */
    async create(config: SandboxConfig): Promise<SandboxContext> {
        try {
            const sandboxId = this.generateSandboxId();
            const namespace = this.generateNamespace(sandboxId);

            // 创建命名空间对象
            const namespaceObject = this.createNamespaceObject(namespace, config);

            // 注册命名空间
            this.registerNamespace(namespace, namespaceObject);

            // 创建沙箱全局对象
            const sandboxGlobal = this.createSandboxGlobal(namespaceObject, config);

            // 创建沙箱上下文
            const context: SandboxContext = {
                id: sandboxId,
                name: config.name,
                type: 'namespace',
                globalProxy: sandboxGlobal,
                state: 'inactive',
                createdAt: Date.now(),
                lastActivity: Date.now()
            };

            // 创建内部上下文
            const internalContext: NamespaceSandboxContext = {
                ...context,
                config,
                namespace,
                namespaceObject,
                sandboxGlobal,
                isolatedModules: new Map(),
                exportedValues: new Map(),
                metrics: this.createInitialMetrics()
            };

            this.contexts.set(sandboxId, internalContext);

            this.logger.info('Namespace沙箱创建成功', {
                sandboxId,
                name: config.name,
                namespace
            });

            return context;
        } catch (error) {
            this.logger.error('Namespace沙箱创建失败', error);
            throw createError('NAMESPACE_SANDBOX_CREATION_FAILED', `Namespace沙箱创建失败: ${error.message}`, error);
        }
    }

    /**
     * 激活沙箱
     */
    async activate(context: SandboxContext): Promise<void> {
        const internalContext = this.getInternalContext(context.id);

        try {
            // 激活命名空间
            this.activateNamespace(internalContext);

            // 设置模块隔离
            this.setupModuleIsolation(internalContext);

            // 更新状态
            context.state = 'active';
            context.lastActivity = Date.now();
            internalContext.metrics.activationCount++;

            this.logger.debug('Namespace沙箱激活成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('Namespace沙箱激活失败', error, { sandboxId: context.id });
            throw createError('NAMESPACE_SANDBOX_ACTIVATION_FAILED', `Namespace沙箱激活失败: ${error.message}`, error);
        }
    }

    /**
     * 停用沙箱
     */
    async deactivate(context: SandboxContext): Promise<void> {
        const internalContext = this.getInternalContext(context.id);

        try {
            // 停用命名空间
            this.deactivateNamespace(internalContext);

            // 清理模块隔离
            this.cleanupModuleIsolation(internalContext);

            // 更新状态
            context.state = 'inactive';
            context.lastActivity = Date.now();

            this.logger.debug('Namespace沙箱停用成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('Namespace沙箱停用失败', error, { sandboxId: context.id });
            throw createError('NAMESPACE_SANDBOX_DEACTIVATION_FAILED', `Namespace沙箱停用失败: ${error.message}`, error);
        }
    }

    /**
     * 销毁沙箱
     */
    async destroy(context: SandboxContext): Promise<void> {
        try {
            const internalContext = this.getInternalContext(context.id);

            // 先停用沙箱
            if (context.state === 'active') {
                await this.deactivate(context);
            }

            // 注销命名空间
            this.unregisterNamespace(internalContext.namespace);

            // 清理资源
            this.contexts.delete(context.id);
            context.state = 'destroyed';

            this.logger.debug('Namespace沙箱销毁成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('Namespace沙箱销毁失败', error, { sandboxId: context.id });
            throw createError('NAMESPACE_SANDBOX_DESTRUCTION_FAILED', `Namespace沙箱销毁失败: ${error.message}`, error);
        }
    }

    /**
     * 执行代码
     */
    async execute(context: SandboxContext, code: string): Promise<any> {
        const internalContext = this.getInternalContext(context.id);
        const startTime = Date.now();

        try {
            // 确保沙箱处于激活状态
            if (context.state !== 'active') {
                await this.activate(context);
            }

            // 在命名空间环境中执行代码
            const result = this.executeInNamespace(internalContext, code);

            // 更新性能指标
            const executionTime = Date.now() - startTime;
            internalContext.metrics.executionTime += executionTime;
            internalContext.metrics.lastUpdated = Date.now();
            context.lastActivity = Date.now();

            this.logger.debug('代码执行成功', {
                sandboxId: context.id,
                executionTime,
                codeLength: code.length
            });

            return result;
        } catch (error) {
            internalContext.metrics.errorCount++;
            this.logger.error('代码执行失败', error, {
                sandboxId: context.id,
                codeSnippet: code.substring(0, 100)
            });
            throw createError('NAMESPACE_SANDBOX_EXECUTION_FAILED', `代码执行失败: ${error.message}`, error);
        }
    }

    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics {
        const internalContext = this.contexts.get(context.id);
        if (!internalContext) {
            return this.createInitialMetrics();
        }

        // 更新内存使用情况
        internalContext.metrics.memoryUsage = this.calculateMemoryUsage(internalContext);
        internalContext.metrics.lastUpdated = Date.now();

        return { ...internalContext.metrics };
    }

    /**
     * 初始化全局命名空间
     */
    private initializeGlobalNamespace(): void {
        if (!(this.globalNamespace in window)) {
            (window as any)[this.globalNamespace] = {
                namespaces: new Map(),
                activeNamespace: null,
                register: (name: string, namespace: any) => {
                    (window as any)[this.globalNamespace].namespaces.set(name, namespace);
                },
                unregister: (name: string) => {
                    (window as any)[this.globalNamespace].namespaces.delete(name);
                },
                get: (name: string) => {
                    return (window as any)[this.globalNamespace].namespaces.get(name);
                }
            };
        }
    }

    /**
     * 生成命名空间名称
     */
    private generateNamespace(sandboxId: string): string {
        return `ns_${sandboxId}`;
    }

    /**
     * 创建命名空间对象
     */
    private createNamespaceObject(namespace: string, config: SandboxConfig): Record<string, any> {
        const namespaceObject: Record<string, any> = {
            __namespace__: namespace,
            __config__: config,
            __modules__: new Map(),
            __exports__: {},
            __imports__: new Map(),

            // 模块系统
            define: (name: string, factory: Function) => {
                namespaceObject.__modules__.set(name, factory);
            },

            require: (name: string) => {
                const factory = namespaceObject.__modules__.get(name);
                if (!factory) {
                    throw createError('MODULE_NOT_FOUND', `模块不存在: ${name}`);
                }
                return factory();
            },

            export: (name: string, value: any) => {
                namespaceObject.__exports__[name] = value;
            },

            import: (name: string, from?: string) => {
                if (from) {
                    // 从其他命名空间导入
                    const otherNamespace = this.getNamespace(from);
                    if (otherNamespace && otherNamespace.__exports__[name]) {
                        return otherNamespace.__exports__[name];
                    }
                }
                return namespaceObject.__exports__[name];
            }
        };

        // 添加白名单中的全局变量
        if (config.isolation.javascript.enabled) {
            const whitelist = config.isolation.javascript.globalWhitelist;

            for (const prop of whitelist) {
                if (prop in window) {
                    try {
                        namespaceObject[prop] = (window as any)[prop];
                    } catch (error) {
                        this.logger.warn('无法复制全局变量', { prop, error: error.message });
                    }
                }
            }
        }

        return namespaceObject;
    }

    /**
     * 注册命名空间
     */
    private registerNamespace(namespace: string, namespaceObject: Record<string, any>): void {
        (window as any)[this.globalNamespace].register(namespace, namespaceObject);
    }

    /**
     * 注销命名空间
     */
    private unregisterNamespace(namespace: string): void {
        (window as any)[this.globalNamespace].unregister(namespace);
    }

    /**
     * 获取命名空间
     */
    private getNamespace(namespace: string): Record<string, any> | undefined {
        return (window as any)[this.globalNamespace].get(namespace);
    }

    /**
     * 创建沙箱全局对象
     */
    private createSandboxGlobal(namespaceObject: Record<string, any>, config: SandboxConfig): Record<string, any> {
        const sandboxGlobal = { ...namespaceObject };

        // 添加基础 API
        sandboxGlobal.window = sandboxGlobal;
        sandboxGlobal.self = sandboxGlobal;
        sandboxGlobal.globalThis = sandboxGlobal;

        // 添加命名空间特有的 API
        sandboxGlobal.namespace = namespaceObject.__namespace__;
        sandboxGlobal.getNamespace = (name: string) => this.getNamespace(name);

        return sandboxGlobal;
    }

    /**
     * 激活命名空间
     */
    private activateNamespace(context: NamespaceSandboxContext): void {
        // 设置当前活跃的命名空间
        (window as any)[this.globalNamespace].activeNamespace = context.namespace;

        // 应用命名空间隔离
        this.applyNamespaceIsolation(context);
    }

    /**
     * 停用命名空间
     */
    private deactivateNamespace(context: NamespaceSandboxContext): void {
        // 清除当前活跃的命名空间
        if ((window as any)[this.globalNamespace].activeNamespace === context.namespace) {
            (window as any)[this.globalNamespace].activeNamespace = null;
        }

        // 移除命名空间隔离
        this.removeNamespaceIsolation(context);
    }

    /**
     * 应用命名空间隔离
     */
    private applyNamespaceIsolation(context: NamespaceSandboxContext): void {
        const { globalBlacklist } = context.config.isolation.javascript;

        // 在命名空间中隐藏黑名单变量
        for (const prop of globalBlacklist) {
            if (prop in context.namespaceObject) {
                delete context.namespaceObject[prop];
            }
        }
    }

    /**
     * 移除命名空间隔离
     */
    private removeNamespaceIsolation(context: NamespaceSandboxContext): void {
        // 命名空间隔离的移除通常不需要特殊处理
        // 因为隔离是通过命名空间边界实现的
    }

    /**
     * 设置模块隔离
     */
    private setupModuleIsolation(context: NamespaceSandboxContext): void {
        // 为每个模块创建独立的作用域
        const originalDefine = context.namespaceObject.define;

        context.namespaceObject.define = (name: string, factory: Function) => {
            // 包装工厂函数，提供隔离的执行环境
            const wrappedFactory = () => {
                try {
                    return factory.call(context.sandboxGlobal);
                } catch (error) {
                    this.logger.error('模块执行失败', error, {
                        namespace: context.namespace,
                        module: name
                    });
                    throw error;
                }
            };

            context.isolatedModules.set(name, wrappedFactory);
            return originalDefine.call(context.namespaceObject, name, wrappedFactory);
        };
    }

    /**
     * 清理模块隔离
     */
    private cleanupModuleIsolation(context: NamespaceSandboxContext): void {
        context.isolatedModules.clear();
        context.exportedValues.clear();
    }

    /**
     * 在命名空间环境中执行代码
     */
    private executeInNamespace(context: NamespaceSandboxContext, code: string): any {
        // 创建执行函数，绑定命名空间上下文
        const executeFunction = new Function(
            'namespace',
            'define',
            'require',
            'export',
            'import',
            'window',
            'self',
            'globalThis',
            `
            with (window) {
                return (function() {
                    "use strict";
                    ${code}
                })();
            }
        `);

        // 在命名空间环境中执行
        return executeFunction(
            context.namespace,
            context.namespaceObject.define,
            context.namespaceObject.require,
            context.namespaceObject.export,
            context.namespaceObject.import,
            context.sandboxGlobal,
            context.sandboxGlobal,
            context.sandboxGlobal
        );
    }

    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage(context: NamespaceSandboxContext): number {
        // Namespace 沙箱的内存使用量估算
        let size = 0;

        // 命名空间对象的大小
        for (const [key, value] of Object.entries(context.namespaceObject)) {
            size += key.length * 2;
            size += this.estimateValueSize(value);
        }

        // 隔离模块的大小
        for (const [name, factory] of context.isolatedModules) {
            size += name.length * 2;
            size += factory.toString().length * 2;
        }

        // 导出值的大小
        for (const [name, value] of context.exportedValues) {
            size += name.length * 2;
            size += this.estimateValueSize(value);
        }

        return size;
    }

    /**
     * 估算值的大小
     */
    private estimateValueSize(value: any): number {
        if (value === null || value === undefined) {
            return 8;
        }

        switch (typeof value) {
            case 'boolean':
                return 4;
            case 'number':
                return 8;
            case 'string':
                return value.length * 2;
            case 'object':
                try {
                    return JSON.stringify(value).length * 2;
                } catch {
                    return 100; // 估算值
                }
            case 'function':
                return value.toString().length * 2;
            default:
                return 8;
        }
    }

    /**
     * 获取内部上下文
     */
    private getInternalContext(sandboxId: string): NamespaceSandboxContext {
        const context = this.contexts.get(sandboxId);
        if (!context) {
            throw createError('SANDBOX_NOT_FOUND', `沙箱不存在: ${sandboxId}`);
        }
        return context;
    }

    /**
     * 生成沙箱ID
     */
    private generateSandboxId(): string {
        return `namespace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 创建初始性能指标
     */
    private createInitialMetrics(): SandboxPerformanceMetrics {
        return {
            memoryUsage: 0,
            cpuUsage: 0,
            executionTime: 0,
            creationTime: 0,
            activationCount: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };
    }
}

/**
 * Namespace 沙箱内部上下文
 */
interface NamespaceSandboxContext extends SandboxContext {
    config: SandboxConfig;
    namespace: string;
    namespaceObject: Record<string, any>;
    sandboxGlobal: Record<string, any>;
    isolatedModules: Map<string, Function>;
    exportedValues: Map<string, any>;
    metrics: SandboxPerformanceMetrics;
}