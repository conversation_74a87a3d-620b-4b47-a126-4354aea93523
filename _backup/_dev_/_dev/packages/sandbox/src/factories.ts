/**
 * 沙箱系统工厂函数
 * 
 * @description 提供创建沙箱组件的工厂函数
 * <AUTHOR> <<EMAIL>>
 */

import { SandboxFactory } from './sandbox-factory';
import { SandboxManager } from './sandbox-manager';
import { ProxySandbox } from './strategies/proxy-sandbox';
import type {
    SandboxConfig,
    SandboxFactoryConfig,
    SandboxManagerConfig,
    SandboxType
} from './types';

/**
 * 创建沙箱实例
 */
export function createSandbox(config: Partial<SandboxConfig> = {}) {
    const defaultConfig: SandboxConfig = {
        type: 'proxy',
        name: `sandbox_${Date.now()}`,
        strict: true,
        isolation: {
            javascript: {
                enabled: true,
                globalWhitelist: ['console', 'setTimeout', 'clearTimeout', 'setInterval', 'clearInterval'],
                globalBlacklist: ['eval', 'Function'],
                allowNativeAPI: false
            },
            css: {
                enabled: true,
                strategy: 'scoped',
                isolateGlobalStyles: true
            },
            dom: {
                enabled: true,
                useShadowDOM: false
            }
        },
        performance: {
            monitoring: true,
            memoryLimit: 100, // 100MB
            timeout: 30000    // 30秒
        },
        permissions: []
    };

    const finalConfig = { ...defaultConfig, ...config };

    // 根据类型创建对应的沙箱策略
    switch (finalConfig.type) {
        case 'proxy':
            return new ProxySandbox();
        default:
            return new ProxySandbox(); // 默认使用 Proxy 沙箱
    }
}

/**
 * 创建沙箱管理器实例
 */
export function createSandboxManager(config?: Partial<SandboxManagerConfig>): SandboxManager {
    return new SandboxManager(config);
}

/**
 * 创建沙箱工厂实例
 */
export function createSandboxFactory(config?: Partial<SandboxFactoryConfig>): SandboxFactory {
    return new SandboxFactory(config);
}

/**
 * 智能选择沙箱类型
 */
export function selectOptimalSandboxType(): SandboxType {
    // 检测浏览器环境和能力
    if (typeof Proxy !== 'undefined') {
        return 'proxy';
    }

    if (typeof Object.defineProperty !== 'undefined') {
        return 'defineproperty';
    }

    // 检测是否支持 iframe
    if (typeof document !== 'undefined' && document.createElement) {
        try {
            const iframe = document.createElement('iframe');
            if (iframe) {
                return 'iframe';
            }
        } catch {
            // 忽略错误
        }
    }

    // 检测是否支持 Web Components
    if (typeof customElements !== 'undefined') {
        return 'webcomponent';
    }

    // 默认使用命名空间沙箱
    return 'namespace';
}

/**
 * 获取沙箱类型的兼容性信息
 */
export function getSandboxCompatibility(): Record<SandboxType, boolean> {
    return {
        proxy: typeof Proxy !== 'undefined',
        defineproperty: typeof Object.defineProperty !== 'undefined',
        iframe: typeof document !== 'undefined' && !!document.createElement,
        webcomponent: typeof customElements !== 'undefined',
        namespace: true, // 命名空间沙箱总是可用
        federation: typeof window !== 'undefined' && 'webpackChunkName' in window
    };
}

/**
 * 创建默认沙箱配置
 */
export function createDefaultSandboxConfig(overrides: Partial<SandboxConfig> = {}): SandboxConfig {
    return {
        type: 'proxy',
        name: `sandbox_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        strict: true,
        isolation: {
            javascript: {
                enabled: true,
                globalWhitelist: [
                    'console', 'setTimeout', 'clearTimeout',
                    'setInterval', 'clearInterval', 'Promise',
                    'Array', 'Object', 'String', 'Number', 'Boolean',
                    'Date', 'RegExp', 'JSON', 'Math'
                ],
                globalBlacklist: [
                    'eval', 'Function', 'WebAssembly',
                    'importScripts', 'postMessage'
                ],
                allowNativeAPI: false
            },
            css: {
                enabled: true,
                strategy: 'scoped',
                isolateGlobalStyles: true
            },
            dom: {
                enabled: true,
                useShadowDOM: false
            }
        },
        performance: {
            monitoring: true,
            memoryLimit: 100,
            timeout: 30000
        },
        permissions: [
            {
                type: 'api',
                name: 'console',
                allowed: true,
                level: 'execute',
                description: '允许使用控制台 API'
            },
            {
                type: 'dom',
                name: 'querySelector',
                allowed: true,
                level: 'read',
                description: '允许查询 DOM 元素'
            }
        ],
        ...overrides
    };
}