/**
 * 沙箱工厂实现
 * 
 * @description 负责创建和管理不同类型的沙箱策略
 * <AUTHOR> <<EMAIL>>
 */

import type {
    SandboxConfig,
    SandboxContext,
    SandboxFactoryConfig,
    SandboxPerformanceMetrics,
    SandboxStrategy,
    SandboxType
} from '@micro-core/shared';
import { createError, Logger } from '@micro-core/shared';
import { DefinePropertySandbox } from './strategies/defineproperty-sandbox';
import { FederationSandbox } from './strategies/federation-sandbox';
import { IframeSandbox } from './strategies/iframe-sandbox';
import { NamespaceSandbox } from './strategies/namespace-sandbox';
import { ProxySandbox } from './strategies/proxy-sandbox';
import { WebComponentSandbox } from './strategies/webcomponent-sandbox';

/**
 * 沙箱工厂类
 * 负责创建和管理不同类型的沙箱策略
 */
export class SandboxFactory {
    private readonly config: SandboxFactoryConfig;
    private readonly logger: Logger;
    private readonly strategies = new Map<SandboxType, SandboxStrategy>();

    constructor(config: Partial<SandboxFactoryConfig> = {}) {
        this.config = {
            defaultType: 'proxy',
            strategyPriority: ['proxy', 'defineproperty', 'webcomponent', 'iframe', 'namespace', 'federation'],
            autoSelect: true,
            performanceThreshold: {
                memory: 100, // MB
                cpu: 80,     // %
                executionTime: 5000 // ms
            },
            ...config
        };

        this.logger = new Logger('SandboxFactory');
        this.initializeStrategies();
    }

    /**
     * 创建沙箱
     */
    async createSandbox(config: SandboxConfig): Promise<SandboxContext> {
        try {
            const strategy = this.getStrategy(config.type);
            const context = await strategy.create(config);

            this.logger.info('沙箱创建成功', {
                sandboxId: context.id,
                type: context.type,
                strategy: strategy.name
            });

            return context;
        } catch (error) {
            this.logger.error('沙箱策略初始化失败', error);
            throw createError('STRATEGY_INITIALIZATION_FAILED', `策略初始化失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 激活沙箱
     */
    async activateSandbox(context: SandboxContext): Promise<void> {
        try {
            const strategy = this.getStrategy(context.type);
            await strategy.activate(context);

            this.logger.debug('沙箱激活成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('沙箱激活失败', error, { sandboxId: context.id });
            throw createError('SANDBOX_ACTIVATION_FAILED', `沙箱激活失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 停用沙箱
     */
    async deactivateSandbox(context: SandboxContext): Promise<void> {
        try {
            const strategy = this.getStrategy(context.type);
            await strategy.deactivate(context);

            this.logger.debug('沙箱停用成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('沙箱停用失败', error, { sandboxId: context.id });
            throw createError('SANDBOX_DEACTIVATION_FAILED', `沙箱停用失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 销毁沙箱
     */
    async destroySandbox(context: SandboxContext): Promise<void> {
        try {
            const strategy = this.getStrategy(context.type);
            await strategy.destroy(context);

            this.logger.debug('沙箱销毁成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('沙箱销毁失败', error, { sandboxId: context.id });
            throw createError('SANDBOX_DESTRUCTION_FAILED', `沙箱销毁失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 在沙箱中执行代码
     */
    async executeInSandbox(context: SandboxContext, code: string): Promise<any> {
        try {
            const strategy = this.getStrategy(context.type);
            const result = await strategy.execute(context, code);

            this.logger.debug('代码执行成功', {
                sandboxId: context.id,
                codeLength: code.length
            });

            return result;
        } catch (error) {
            this.logger.error('代码执行失败', error, {
                sandboxId: context.id,
                codeSnippet: code.substring(0, 100)
            });
            throw createError('SANDBOX_EXECUTION_FAILED', `代码执行失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 获取沙箱性能指标
     */
    getPerformanceMetrics(context: SandboxContext): SandboxPerformanceMetrics {
        try {
            const strategy = this.getStrategy(context.type);
            return strategy.getMetrics(context);
        } catch (error) {
            this.logger.error('获取性能指标失败', error, { sandboxId: context.id });

            // 返回默认指标
            return {
                memoryUsage: 0,
                cpuUsage: 0,
                executionTime: 0,
                creationTime: 0,
                activationCount: 0,
                errorCount: 0,
                lastUpdated: Date.now()
            };
        }
    }

    /**
     * 获取可用的沙箱类型
     */
    getAvailableTypes(): SandboxType[] {
        return Array.from(this.strategies.keys());
    }

    /**
     * 检查沙箱类型是否支持
     */
    isTypeSupported(type: SandboxType): boolean {
        return this.strategies.has(type);
    }

    /**
     * 自动选择最优沙箱类型
     */
    selectOptimalType(): SandboxType {
        if (!this.config.autoSelect) {
            return this.config.defaultType;
        }

        // 按优先级检查可用性
        for (const type of this.config.strategyPriority) {
            if (this.isTypeSupported(type) && this.isTypeCompatible(type)) {
                this.logger.debug('自动选择沙箱类型', { selectedType: type });
                return type;
            }
        }

        // 如果没有找到合适的类型，返回默认类型
        this.logger.warn('未找到最优沙箱类型，使用默认类型', {
            defaultType: this.config.defaultType
        });
        return this.config.defaultType;
    }

    /**
     * 获取策略实例
     */
    private getStrategy(type: SandboxType): SandboxStrategy {
        const strategy = this.strategies.get(type);
        if (!strategy) {
            throw createError('STRATEGY_NOT_FOUND', `沙箱策略不存在: ${type}`);
        }
        return strategy;
    }

    /**
     * 初始化所有策略
     */
    private initializeStrategies(): void {
        try {
            // 注册所有可用的沙箱策略
            this.registerStrategy(new ProxySandbox());
            this.registerStrategy(new DefinePropertySandbox());
            this.registerStrategy(new IframeSandbox());
            this.registerStrategy(new WebComponentSandbox());
            this.registerStrategy(new NamespaceSandbox());
            this.registerStrategy(new FederationSandbox());

            this.logger.info('沙箱策略初始化完成', {
                strategiesCount: this.strategies.size,
                availableTypes: this.getAvailableTypes()
            });
        } catch (error) {
            this.logger.error('沙箱策略初始化失败', error);
            throw createError('STRATEGY_INITIALIZATION_FAILED', `策略初始化失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 注册策略
     */
    private registerStrategy(strategy: SandboxStrategy): void {
        if (this.strategies.has(strategy.type)) {
            this.logger.warn('策略已存在，将被覆盖', { type: strategy.type });
        }

        this.strategies.set(strategy.type, strategy);
        this.logger.debug('策略注册成功', {
            type: strategy.type,
            name: strategy.name
        });
    }

    /**
     * 检查类型兼容性
     */
    private isTypeCompatible(type: SandboxType): boolean {
        switch (type) {
            case 'proxy':
                return typeof Proxy !== 'undefined';

            case 'defineproperty':
                return typeof Object.defineProperty !== 'undefined';

            case 'iframe':
                return typeof document !== 'undefined' &&
                    typeof document.createElement === 'function';

            case 'webcomponent':
                return typeof customElements !== 'undefined' &&
                    typeof HTMLElement !== 'undefined';

            case 'namespace':
                return true; // 命名空间沙箱总是兼容的

            case 'federation':
                return typeof window !== 'undefined' &&
                    'webpackChunkName' in window;

            default:
                return false;
        }
    }

    /**
     * 销毁工厂
     */
    async destroy(): Promise<void> {
        this.strategies.clear();
        this.logger.info('沙箱工厂已销毁');
    }
}