/**
 * JavaScript 隔离实现
 * 
 * @description 实现 JavaScript 执行环境的隔离
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type { IsolationConfig, Isolator, SandboxContext } from '../types';

/**
 * JavaScript 隔离器
 * 负责隔离 JavaScript 执行环境
 */
export class JavaScriptIsolation implements Isolator {
    readonly name = 'JavaScript隔离器';
    enabled = true;

    private readonly logger: Logger;
    private config?: IsolationConfig;

    constructor() {
        this.logger = new Logger('JavaScriptIsolation');
    }

    /**
     * 初始化隔离器
     */
    async initialize(config: IsolationConfig): Promise<void> {
        this.config = config;
        this.enabled = config.javascript.enabled;

        this.logger.info('JavaScript隔离器初始化完成', {
            enabled: this.enabled,
            whitelistCount: config.javascript.globalWhitelist.length,
            blacklistCount: config.javascript.globalBlacklist.length
        });
    }

    /**
     * 应用隔离
     */
    async apply(context: SandboxContext): Promise<void> {
        if (!this.enabled || !this.config) {
            return;
        }

        try {
            // 应用全局变量隔离
            this.applyGlobalVariableIsolation(context);

            // 应用原生 API 隔离
            this.applyNativeAPIIsolation(context);

            this.logger.debug('JavaScript隔离应用成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('JavaScript隔离应用失败', error, { sandboxId: context.id });
            throw createError('JS_ISOLATION_FAILED', `JavaScript隔离失败: ${error.message}`, error);
        }
    }

    /**
     * 移除隔离
     */
    async remove(context: SandboxContext): Promise<void> {
        if (!this.enabled) {
            return;
        }

        try {
            // 恢复原始环境
            this.restoreOriginalEnvironment(context);

            this.logger.debug('JavaScript隔离移除成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('JavaScript隔离移除失败', error, { sandboxId: context.id });
            throw createError('JS_ISOLATION_REMOVAL_FAILED', `JavaScript隔离移除失败: ${error.message}`, error);
        }
    }

    /**
     * 清理资源
     */
    async cleanup(): Promise<void> {
        this.config = undefined;
        this.logger.info('JavaScript隔离器已清理');
    }

    /**
     * 应用全局变量隔离
     */
    private applyGlobalVariableIsolation(context: SandboxContext): void {
        if (!this.config) return;

        const { globalWhitelist, globalBlacklist } = this.config.javascript;

        // 创建白名单映射
        const whitelistSet = new Set(globalWhitelist);
        const blacklistSet = new Set(globalBlacklist);

        // 通过代理实现隔离
        if (context.globalProxy && typeof context.globalProxy === 'object') {
            // 已经通过 Proxy 实现隔离，这里可以添加额外的检查逻辑
            this.logger.debug('全局变量隔离已通过Proxy实现', {
                whitelistCount: whitelistSet.size,
                blacklistCount: blacklistSet.size
            });
        }
    }

    /**
     * 应用原生 API 隔离
     */
    private applyNativeAPIIsolation(context: SandboxContext): void {
        if (!this.config) return;

        const { allowNativeAPI } = this.config.javascript;

        if (!allowNativeAPI) {
            // 禁用危险的原生 API
            const dangerousAPIs = [
                'eval',
                'Function',
                'WebAssembly',
                'importScripts',
                'postMessage'
            ];

            for (const api of dangerousAPIs) {
                if (context.globalProxy && api in context.globalProxy) {
                    try {
                        delete context.globalProxy[api];
                    } catch (error) {
                        this.logger.warn('无法删除危险API', { api, error: error.message });
                    }
                }
            }

            this.logger.debug('原生API隔离应用完成', {
                disabledAPIs: dangerousAPIs.length
            });
        }
    }

    /**
     * 恢复原始环境
     */
    private restoreOriginalEnvironment(context: SandboxContext): void {
        // 对于 JavaScript 隔离，通常不需要恢复
        // 因为隔离是通过代理实现的，销毁代理即可
        this.logger.debug('JavaScript环境恢复完成', { sandboxId: context.id });
    }

    /**
     * 检查变量是否被允许访问
     */
    isVariableAllowed(variableName: string): boolean {
        if (!this.config) return true;

        const { globalWhitelist, globalBlacklist } = this.config.javascript;

        // 检查黑名单
        if (globalBlacklist.includes(variableName)) {
            return false;
        }

        // 如果有白名单，检查是否在白名单中
        if (globalWhitelist.length > 0) {
            return globalWhitelist.includes(variableName);
        }

        return true;
    }

    /**
     * 检查 API 是否被允许访问
     */
    isAPIAllowed(apiName: string): boolean {
        if (!this.config) return true;

        const { allowNativeAPI, globalBlacklist } = this.config.javascript;

        // 检查黑名单
        if (globalBlacklist.includes(apiName)) {
            return false;
        }

        // 检查是否允许原生 API
        if (!allowNativeAPI) {
            const dangerousAPIs = [
                'eval', 'Function', 'WebAssembly',
                'importScripts', 'postMessage'
            ];
            return !dangerousAPIs.includes(apiName);
        }

        return true;
    }
}