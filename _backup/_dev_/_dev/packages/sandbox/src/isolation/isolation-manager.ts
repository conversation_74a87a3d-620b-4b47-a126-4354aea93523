/**
 * 隔离管理器实现
 * 
 * @description 统一管理所有隔离能力
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type { IsolationConfig, Isolator, SandboxContext } from '../types';
import { CSSIsolation } from './css-isolation';
import { GlobalVariableIsolation } from './global-variable-isolation';
import { JavaScriptIsolation } from './javascript-isolation';

/**
 * 隔离管理器
 * 负责协调和管理所有隔离器
 */
export class IsolationManager {
    private readonly logger: Logger;
    private readonly isolators = new Map<string, Isolator>();
    private config?: IsolationConfig;
    private initialized = false;

    constructor() {
        this.logger = new Logger('IsolationManager');
        this.initializeIsolators();
    }

    /**
     * 初始化隔离管理器
     */
    async initialize(config: IsolationConfig): Promise<void> {
        try {
            this.config = config;

            // 初始化所有隔离器
            const initPromises = Array.from(this.isolators.values()).map(isolator =>
                isolator.initialize(config)
            );

            await Promise.all(initPromises);
            this.initialized = true;

            this.logger.info('隔离管理器初始化完成', {
                isolatorCount: this.isolators.size,
                enabledIsolators: this.getEnabledIsolators().length
            });
        } catch (error) {
            this.logger.error('隔离管理器初始化失败', error);
            throw createError('ISOLATION_MANAGER_INIT_FAILED', `隔离管理器初始化失败: ${error.message}`, error);
        }
    }

    /**
     * 应用所有隔离
     */
    async applyIsolation(context: SandboxContext): Promise<void> {
        if (!this.initialized) {
            throw createError('ISOLATION_MANAGER_NOT_INITIALIZED', '隔离管理器未初始化');
        }

        try {
            // 按顺序应用所有启用的隔离器
            const enabledIsolators = this.getEnabledIsolators();

            for (const isolator of enabledIsolators) {
                await isolator.apply(context);
            }

            this.logger.debug('隔离应用完成', {
                sandboxId: context.id,
                appliedCount: enabledIsolators.length
            });
        } catch (error) {
            this.logger.error('隔离应用失败', error, { sandboxId: context.id });

            // 尝试回滚已应用的隔离
            await this.rollbackIsolation(context);

            throw createError('ISOLATION_APPLICATION_FAILED', `隔离应用失败: ${error.message}`, error);
        }
    }

    /**
     * 移除所有隔离
     */
    async removeIsolation(context: SandboxContext): Promise<void> {
        if (!this.initialized) {
            return;
        }

        try {
            // 按相反顺序移除所有启用的隔离器
            const enabledIsolators = this.getEnabledIsolators().reverse();

            for (const isolator of enabledIsolators) {
                await isolator.remove(context);
            }

            this.logger.debug('隔离移除完成', {
                sandboxId: context.id,
                removedCount: enabledIsolators.length
            });
        } catch (error) {
            this.logger.error('隔离移除失败', error, { sandboxId: context.id });
            throw createError('ISOLATION_REMOVAL_FAILED', `隔离移除失败: ${error.message}`, error);
        }
    }

    /**
     * 回滚隔离（在应用失败时使用）
     */
    async rollbackIsolation(context: SandboxContext): Promise<void> {
        try {
            await this.removeIsolation(context);
            this.logger.info('隔离回滚完成', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('隔离回滚失败', error, { sandboxId: context.id });
        }
    }

    /**
     * 获取隔离器
     */
    getIsolator(name: string): Isolator | undefined {
        return this.isolators.get(name);
    }

    /**
     * 获取所有隔离器
     */
    getAllIsolators(): Isolator[] {
        return Array.from(this.isolators.values());
    }

    /**
     * 获取启用的隔离器
     */
    getEnabledIsolators(): Isolator[] {
        return Array.from(this.isolators.values()).filter(isolator => isolator.enabled);
    }

    /**
     * 检查隔离器是否启用
     */
    isIsolatorEnabled(name: string): boolean {
        const isolator = this.isolators.get(name);
        return isolator ? isolator.enabled : false;
    }

    /**
     * 获取隔离状态
     */
    getIsolationStatus(): Record<string, boolean> {
        const status: Record<string, boolean> = {};

        for (const [name, isolator] of this.isolators) {
            status[name] = isolator.enabled;
        }

        return status;
    }

    /**
     * 清理资源
     */
    async cleanup(): Promise<void> {
        try {
            // 清理所有隔离器
            const cleanupPromises = Array.from(this.isolators.values()).map(isolator =>
                isolator.cleanup().catch(error =>
                    this.logger.error('隔离器清理失败', error, { isolatorName: isolator.name })
                )
            );

            await Promise.all(cleanupPromises);

            this.isolators.clear();
            this.config = undefined;
            this.initialized = false;

            this.logger.info('隔离管理器已清理');
        } catch (error) {
            this.logger.error('隔离管理器清理失败', error);
            throw createError('ISOLATION_MANAGER_CLEANUP_FAILED', `隔离管理器清理失败: ${error.message}`, error);
        }
    }

    /**
     * 初始化所有隔离器
     */
    private initializeIsolators(): void {
        try {
            // 注册内置隔离器
            this.registerIsolator('javascript', new JavaScriptIsolation());
            this.registerIsolator('css', new CSSIsolation());
            this.registerIsolator('globalVariable', new GlobalVariableIsolation());

            this.logger.debug('隔离器注册完成', {
                registeredCount: this.isolators.size
            });
        } catch (error) {
            this.logger.error('隔离器初始化失败', error);
            throw createError('ISOLATOR_INITIALIZATION_FAILED', `隔离器初始化失败: ${error.message}`, error);
        }
    }

    /**
     * 注册隔离器
     */
    private registerIsolator(name: string, isolator: Isolator): void {
        if (this.isolators.has(name)) {
            this.logger.warn('隔离器已存在，将被覆盖', { name });
        }

        this.isolators.set(name, isolator);
        this.logger.debug('隔离器注册成功', {
            name,
            isolatorName: isolator.name
        });
    }

    /**
     * 验证配置
     */
    private validateConfig(config: IsolationConfig): void {
        if (!config) {
            throw createError('INVALID_CONFIG', '隔离配置不能为空');
        }

        if (!config.javascript || !config.css) {
            throw createError('INVALID_CONFIG', '隔离配置缺少必要字段');
        }

        this.logger.debug('隔离配置验证通过');
    }
}