/**
 * 全局变量隔离实现
 *
 * @description 实现全局变量的隔离
 * <AUTHOR> <<EMAIL>>
 */
import type { IsolationConfig, Isolator, SandboxContext } from '../types';
/**
 * 全局变量隔离器
 * 负责隔离全局变量访问
 */
export declare class GlobalVariableIsolation implements Isolator {
    readonly name = "\u5168\u5C40\u53D8\u91CF\u9694\u79BB\u5668";
    enabled: boolean;
    private readonly logger;
    private config?;
    private readonly originalValues;
    constructor();
    /**
     * 初始化隔离器
     */
    initialize(config: IsolationConfig): Promise<void>;
    /**
     * 应用隔离
     */
    apply(context: SandboxContext): Promise<void>;
    /**
     * 移除隔离
     */
    remove(context: SandboxContext): Promise<void>;
    /**
     * 清理资源
     */
    cleanup(): Promise<void>;
    /**
     * 备份原始全局变量
     */
    private backupOriginalGlobals;
    /**
     * 应用全局变量隔离
     */
    private applyGlobalIsolation;
    /**
     * 恢复原始全局变量
     */
    private restoreOriginalGlobals;
    /**
     * 检查全局变量是否被隔离
     */
    isVariableIsolated(varName: string): boolean;
    /**
     * 获取被隔离的变量列表
     */
    getIsolatedVariables(): string[];
    /**
     * 获取允许的变量列表
     */
    getAllowedVariables(): string[];
}
//# sourceMappingURL=global-variable-isolation.d.ts.map