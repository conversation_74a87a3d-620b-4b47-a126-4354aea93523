/**
 * 全局变量隔离实现
 * 
 * @description 实现全局变量的隔离
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type { IsolationConfig, Isolator, SandboxContext } from '../types';

/**
 * 全局变量隔离器
 * 负责隔离全局变量访问
 */
export class GlobalVariableIsolation implements Isolator {
    readonly name = '全局变量隔离器';
    enabled = true;

    private readonly logger: Logger;
    private config?: IsolationConfig;
    private readonly originalValues = new Map<string, any>();

    constructor() {
        this.logger = new Logger('GlobalVariableIsolation');
    }

    /**
     * 初始化隔离器
     */
    async initialize(config: IsolationConfig): Promise<void> {
        this.config = config;
        this.enabled = config.javascript.enabled;

        this.logger.info('全局变量隔离器初始化完成', {
            enabled: this.enabled,
            whitelistCount: config.javascript.globalWhitelist.length,
            blacklistCount: config.javascript.globalBlacklist.length
        });
    }

    /**
     * 应用隔离
     */
    async apply(context: SandboxContext): Promise<void> {
        if (!this.enabled || !this.config) {
            return;
        }

        try {
            // 备份原始全局变量
            this.backupOriginalGlobals();

            // 应用全局变量隔离
            this.applyGlobalIsolation(context);

            this.logger.debug('全局变量隔离应用成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('全局变量隔离应用失败', error, { sandboxId: context.id });
            throw createError('GLOBAL_ISOLATION_FAILED', `全局变量隔离失败: ${error.message}`, error);
        }
    }

    /**
     * 移除隔离
     */
    async remove(context: SandboxContext): Promise<void> {
        if (!this.enabled) {
            return;
        }

        try {
            // 恢复原始全局变量
            this.restoreOriginalGlobals();

            this.logger.debug('全局变量隔离移除成功', { sandboxId: context.id });
        } catch (error) {
            this.logger.error('全局变量隔离移除失败', error, { sandboxId: context.id });
            throw createError('GLOBAL_ISOLATION_REMOVAL_FAILED', `全局变量隔离移除失败: ${error.message}`, error);
        }
    }

    /**
     * 清理资源
     */
    async cleanup(): Promise<void> {
        this.originalValues.clear();
        this.config = undefined;
        this.logger.info('全局变量隔离器已清理');
    }

    /**
     * 备份原始全局变量
     */
    private backupOriginalGlobals(): void {
        if (!this.config) return;

        const { globalBlacklist } = this.config.javascript;

        // 备份黑名单中的全局变量
        for (const varName of globalBlacklist) {
            if (varName in window) {
                try {
                    this.originalValues.set(varName, (window as any)[varName]);
                } catch (error) {
                    this.logger.warn('无法备份全局变量', { varName, error: error.message });
                }
            }
        }

        this.logger.debug('全局变量备份完成', {
            backupCount: this.originalValues.size
        });
    }

    /**
     * 应用全局变量隔离
     */
    private applyGlobalIsolation(context: SandboxContext): void {
        if (!this.config) return;

        const { globalBlacklist } = this.config.javascript;

        // 隐藏黑名单中的全局变量
        for (const varName of globalBlacklist) {
            if (varName in window) {
                try {
                    // 使用 undefined 覆盖黑名单变量
                    (window as any)[varName] = undefined;

                    // 或者使用 delete 删除（如果可能）
                    try {
                        delete (window as any)[varName];
                    } catch {
                        // 某些内置属性无法删除，忽略错误
                    }
                } catch (error) {
                    this.logger.warn('无法隐藏全局变量', { varName, error: error.message });
                }
            }
        }

        this.logger.debug('全局变量隔离应用完成', {
            hiddenCount: globalBlacklist.length
        });
    }

    /**
     * 恢复原始全局变量
     */
    private restoreOriginalGlobals(): void {
        // 恢复备份的全局变量
        for (const [varName, originalValue] of this.originalValues) {
            try {
                (window as any)[varName] = originalValue;
            } catch (error) {
                this.logger.warn('无法恢复全局变量', { varName, error: error.message });
            }
        }

        this.originalValues.clear();

        this.logger.debug('全局变量恢复完成');
    }

    /**
     * 检查全局变量是否被隔离
     */
    isVariableIsolated(varName: string): boolean {
        if (!this.config) return false;

        const { globalBlacklist } = this.config.javascript;
        return globalBlacklist.includes(varName);
    }

    /**
     * 获取被隔离的变量列表
     */
    getIsolatedVariables(): string[] {
        if (!this.config) return [];

        return [...this.config.javascript.globalBlacklist];
    }

    /**
     * 获取允许的变量列表
     */
    getAllowedVariables(): string[] {
        if (!this.config) return [];

        return [...this.config.javascript.globalWhitelist];
    }
}