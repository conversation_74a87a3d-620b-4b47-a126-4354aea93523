/**
 * CSS 隔离实现
 *
 * @description 实现 CSS 样式的隔离
 * <AUTHOR> <<EMAIL>>
 */
import type { IsolationConfig, Isolator, SandboxContext } from '../types';
/**
 * CSS 隔离器
 * 负责隔离 CSS 样式
 */
export declare class CSSIsolation implements Isolator {
    readonly name = "CSS\u9694\u79BB\u5668";
    enabled: boolean;
    private readonly logger;
    private config?;
    constructor();
    /**
     * 初始化隔离器
     */
    initialize(config: IsolationConfig): Promise<void>;
    /**
     * 应用隔离
     */
    apply(context: SandboxContext): Promise<void>;
    /**
     * 移除隔离
     */
    remove(context: SandboxContext): Promise<void>;
    /**
     * 清理资源
     */
    cleanup(): Promise<void>;
    /**
     * 应用作用域隔离
     */
    private applyScopedIsolation;
    /**
     * 应用Shadow DOM隔离
     */
    private applyShadowIsolation;
    /**
     * 应用命名空间隔离
     */
    private applyNamespaceIsolation;
    /**
     * 移除CSS隔离
     */
    private removeCSSIsolation;
}
//# sourceMappingURL=css-isolation.d.ts.map