/**
 * 沙箱管理器实现
 * 
 * @description 统一管理所有沙箱实例的生命周期和资源
 * <AUTHOR> <<EMAIL>>
 */

import type {
    SandboxConfig,
    SandboxContext,
    SandboxEvent,
    SandboxManagerConfig,
    SandboxPerformanceMetrics
} from '@micro-core/shared';
import { createError, createLogger, EventEmitter } from '@micro-core/shared';

/**
 * 简化的沙箱工厂类
 */
class SandboxFactory {
    async createSandbox(config: SandboxConfig): Promise<SandboxContext> {
        const context: SandboxContext = {
            id: `sandbox_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: config.name || 'unnamed',
            type: config.type || 'proxy',
            state: 'inactive',
            createdAt: Date.now(),
            lastActivity: Date.now(),
            config,
            instance: null,
            globals: new Map(),
            cleanup: []
        };
        return context;
    }

    async activateSandbox(context: SandboxContext): Promise<void> {
        // 激活沙箱逻辑
        context.state = 'active';
        context.lastActivity = Date.now();
    }

    async deactivateSandbox(context: SandboxContext): Promise<void> {
        // 停用沙箱逻辑
        context.state = 'inactive';
        context.lastActivity = Date.now();
    }

    async destroySandbox(context: SandboxContext): Promise<void> {
        // 销毁沙箱逻辑
        context.state = 'destroyed';
        context.cleanup.forEach(fn => {
            try {
                fn();
            } catch (error) {
                console.warn('清理函数执行失败:', error);
            }
        });
    }

    async executeInSandbox(context: SandboxContext, code: string): Promise<any> {
        // 在沙箱中执行代码
        try {
            // 这里应该根据沙箱类型执行不同的策略
            return eval(code);
        } catch (error) {
            throw new Error(`代码执行失败: ${(error as Error).message}`);
        }
    }
}

/**
 * 沙箱管理器类
 * 负责沙箱的创建、管理和资源清理
 */
export class SandboxManager extends EventEmitter {
    private readonly config: SandboxManagerConfig;
    private readonly logger: ReturnType<typeof createLogger>;
    private readonly factory: SandboxFactory;
    private readonly sandboxes = new Map<string, SandboxContext>();
    private readonly performanceMetrics = new Map<string, SandboxPerformanceMetrics>();
    private cleanupTimer?: NodeJS.Timeout;

    constructor(config: Partial<SandboxManagerConfig> = {}) {
        super();

        this.config = {
            maxSandboxes: 50,
            defaultConfig: {
                name: 'default',
                type: 'proxy',
                strict: true,
                isolation: {
                    javascript: {
                        enabled: true,
                        globalWhitelist: [],
                        globalBlacklist: [],
                        allowNativeAPI: false
                    },
                    css: {
                        enabled: true,
                        strategy: 'scoped',
                        isolateGlobalStyles: true
                    },
                    dom: {
                        enabled: true,
                        useShadowDOM: false
                    }
                },
                performance: {
                    monitoring: true,
                    memoryLimit: 100,
                    timeout: 30000
                },
                permissions: []
            },
            performanceMonitoring: true,
            cleanupInterval: 60000, // 1分钟
            autoCleanup: true,
            ...config
        };

        this.logger = createLogger('SandboxManager');
        this.factory = new SandboxFactory();

        this.setupCleanupTimer();
        this.setupErrorHandling();
    }

    /**
     * 创建沙箱
     */
    async createSandbox(config: Partial<SandboxConfig> = {}): Promise<SandboxContext> {
        // 检查沙箱数量限制
        if (this.sandboxes.size >= this.config.maxSandboxes) {
            throw createError(
                'SANDBOX_LIMIT_EXCEEDED',
                `沙箱数量已达到上限: ${this.config.maxSandboxes}`
            );
        }

        try {
            // 合并配置
            const finalConfig: SandboxConfig = {
                ...this.config.defaultConfig,
                ...config,
                name: config.name || `sandbox_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            } as SandboxConfig;

            // 使用工厂创建沙箱
            const context = await this.factory.createSandbox(finalConfig);

            // 注册沙箱
            this.sandboxes.set(context.id, context);

            // 初始化性能监控
            if (this.config.performanceMonitoring) {
                this.initializePerformanceMonitoring(context);
            }

            // 发送创建事件
            this.emitSandboxEvent('created', context.id, { config: finalConfig });

            this.logger.info('沙箱创建成功', {
                sandboxId: context.id,
                type: context.type,
                name: context.name
            });

            return context;
        } catch (error) {
            this.logger.error('沙箱创建失败', error, { config });
            throw createError('SANDBOX_CREATION_FAILED', `沙箱创建失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 获取沙箱
     */
    getSandbox(sandboxId: string): SandboxContext | undefined {
        return this.sandboxes.get(sandboxId);
    }

    /**
     * 激活沙箱
     */
    async activateSandbox(sandboxId: string): Promise<void> {
        const context = this.sandboxes.get(sandboxId);
        if (!context) {
            throw createError('SANDBOX_NOT_FOUND', `沙箱不存在: ${sandboxId}`);
        }

        try {
            await this.factory.activateSandbox(context);
            context.state = 'active';
            context.lastActivity = Date.now();

            this.emitSandboxEvent('activated', sandboxId);
            this.logger.debug('沙箱激活成功', { sandboxId });
        } catch (error) {
            this.logger.error('沙箱激活失败', error, { sandboxId });
            throw createError('SANDBOX_ACTIVATION_FAILED', `沙箱激活失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 停用沙箱
     */
    async deactivateSandbox(sandboxId: string): Promise<void> {
        const context = this.sandboxes.get(sandboxId);
        if (!context) {
            throw createError('SANDBOX_NOT_FOUND', `沙箱不存在: ${sandboxId}`);
        }

        try {
            await this.factory.deactivateSandbox(context);
            context.state = 'inactive';
            context.lastActivity = Date.now();

            this.emitSandboxEvent('deactivated', sandboxId);
            this.logger.debug('沙箱停用成功', { sandboxId });
        } catch (error) {
            this.logger.error('沙箱停用失败', error, { sandboxId });
            throw createError('SANDBOX_DEACTIVATION_FAILED', `沙箱停用失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 销毁沙箱
     */
    async destroySandbox(sandboxId: string): Promise<void> {
        const context = this.sandboxes.get(sandboxId);
        if (!context) {
            throw createError('SANDBOX_NOT_FOUND', `沙箱不存在: ${sandboxId}`);
        }

        try {
            await this.factory.destroySandbox(context);

            // 清理资源
            this.sandboxes.delete(sandboxId);
            this.performanceMetrics.delete(sandboxId);

            this.emitSandboxEvent('destroyed', sandboxId);
            this.logger.info('沙箱销毁成功', { sandboxId });
        } catch (error) {
            this.logger.error('沙箱销毁失败', error, { sandboxId });
            throw createError('SANDBOX_DESTRUCTION_FAILED', `沙箱销毁失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 执行代码
     */
    async executeInSandbox(sandboxId: string, code: string): Promise<any> {
        const context = this.sandboxes.get(sandboxId);
        if (!context) {
            throw createError('SANDBOX_NOT_FOUND', `沙箱不存在: ${sandboxId}`);
        }

        try {
            const result = await this.factory.executeInSandbox(context, code);
            context.lastActivity = Date.now();

            this.logger.debug('代码执行成功', { sandboxId, codeLength: code.length });
            return result;
        } catch (error) {
            this.logger.error('代码执行失败', error, { sandboxId });
            this.emitSandboxEvent('error', sandboxId, { error, code });
            throw createError('SANDBOX_EXECUTION_FAILED', `代码执行失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 获取所有沙箱
     */
    getAllSandboxes(): SandboxContext[] {
        return Array.from(this.sandboxes.values());
    }

    /**
     * 获取活跃沙箱
     */
    getActiveSandboxes(): SandboxContext[] {
        return this.getAllSandboxes().filter(context => context.state === 'active');
    }

    /**
     * 获取性能指标
     */
    getPerformanceMetrics(sandboxId?: string): SandboxPerformanceMetrics | Map<string, SandboxPerformanceMetrics> | undefined {
        if (sandboxId) {
            return this.performanceMetrics.get(sandboxId);
        }
        return new Map(this.performanceMetrics);
    }

    /**
     * 清理非活跃沙箱
     */
    async cleanupInactiveSandboxes(maxIdleTime: number = 300000): Promise<void> {
        const now = Date.now();
        const toDestroy: string[] = [];

        for (const [id, context] of this.sandboxes) {
            if (context.state === 'inactive' && (now - context.lastActivity) > maxIdleTime) {
                toDestroy.push(id);
            }
        }

        for (const id of toDestroy) {
            try {
                await this.destroySandbox(id);
                this.logger.debug('清理非活跃沙箱', { sandboxId: id });
            } catch (error) {
                this.logger.error('清理沙箱失败', error, { sandboxId: id });
            }
        }

        if (toDestroy.length > 0) {
            this.logger.info('清理完成', { cleanedCount: toDestroy.length });
        }
    }

    /**
     * 销毁管理器
     */
    async destroy(): Promise<void> {
        // 清理定时器
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }

        // 销毁所有沙箱
        const destroyPromises = Array.from(this.sandboxes.keys()).map(id =>
            this.destroySandbox(id).catch(error =>
                this.logger.error('销毁沙箱失败', error, { sandboxId: id })
            )
        );

        await Promise.allSettled(destroyPromises);

        // 清理资源
        this.sandboxes.clear();
        this.performanceMetrics.clear();
        this.removeAllListeners();

        this.logger.info('沙箱管理器已销毁');
    }

    /**
     * 初始化性能监控
     */
    private initializePerformanceMonitoring(context: SandboxContext): void {
        const metrics: SandboxPerformanceMetrics = {
            memoryUsage: 0,
            cpuUsage: 0,
            executionTime: 0,
            creationTime: Date.now() - context.createdAt,
            activationCount: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };

        this.performanceMetrics.set(context.id, metrics);
    }

    /**
     * 发送沙箱事件
     */
    private emitSandboxEvent(type: SandboxEvent['type'], sandboxId: string, data?: any): void {
        const event: SandboxEvent = {
            type,
            sandboxId,
            data,
            timestamp: Date.now()
        };

        this.emit('sandboxEvent', event);
        this.emit(type, event);
    }

    /**
     * 设置清理定时器
     */
    private setupCleanupTimer(): void {
        if (this.config.autoCleanup) {
            this.cleanupTimer = setInterval(() => {
                this.cleanupInactiveSandboxes().catch(error => {
                    this.logger.error('自动清理失败', error);
                });
            }, this.config.cleanupInterval);
        }
    }

    /**
     * 设置错误处理
     */
    private setupErrorHandling(): void {
        this.on('error', (error: Error) => {
            this.logger.error('沙箱管理器内部错误', error);
        });

        // 监听沙箱错误事件
        this.on('sandboxEvent', (event: SandboxEvent) => {
            if (event.type === 'error') {
                const metrics = this.performanceMetrics.get(event.sandboxId);
                if (metrics) {
                    metrics.errorCount++;
                    metrics.lastUpdated = Date.now();
                }
            }
        });
    }
}