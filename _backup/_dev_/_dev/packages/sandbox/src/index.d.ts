/**
 * 沙箱系统包入口文件
 *
 * @description 提供完整的沙箱隔离能力，支持6种沙箱策略
 * <AUTHOR> <<EMAIL>>
 */
export { SandboxFactory } from './sandbox-factory';
export { SandboxManager } from './sandbox-manager';
export { DefinePropertySandbox } from './strategies/defineproperty-sandbox';
export { FederationSandbox } from './strategies/federation-sandbox';
export { IframeSandbox } from './strategies/iframe-sandbox';
export { NamespaceSandbox } from './strategies/namespace-sandbox';
export { ProxySandbox } from './strategies/proxy-sandbox';
export { WebComponentSandbox } from './strategies/webcomponent-sandbox';
export { CSSIsolation } from './isolation/css-isolation';
export { GlobalVariableIsolation } from './isolation/global-variable-isolation';
export { IsolationManager } from './isolation/isolation-manager';
export { JavaScriptIsolation } from './isolation/javascript-isolation';
export * from './factories';
//# sourceMappingURL=index.d.ts.map