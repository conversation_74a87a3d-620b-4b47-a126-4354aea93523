{"name": "@micro-core/sandbox", "version": "0.1.0", "description": "微前端沙箱隔离系统 - 6种沙箱策略，JavaScript、CSS、全局变量完全隔离", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"dev": "vite build --watch", "build": "vite build && tsc --emitDeclarationOnly", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "keywords": ["微前端", "沙箱", "隔离", "proxy", "iframe", "webcomponent", "micro-frontend", "sandbox"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/sandbox"}, "dependencies": {"@micro-core/shared": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.5", "rimraf": "^5.0.5", "typescript": "^5.7.2", "vite": "^7.0.6", "vite-plugin-dts": "^4.3.0", "vitest": "^3.2.4"}, "peerDependencies": {"@micro-core/core": "workspace:*"}}