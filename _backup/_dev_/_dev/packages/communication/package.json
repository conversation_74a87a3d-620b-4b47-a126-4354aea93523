{"name": "@micro-core/communication", "version": "0.1.0", "description": "微前端应用间通信系统 - 事件总线、消息通道、状态同步", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"dev": "vite build --watch", "build": "vite build && tsc --emitDeclarationOnly", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "keywords": ["微前端", "通信", "事件总线", "消息通道", "状态同步", "micro-frontend", "communication"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/communication"}, "dependencies": {"@micro-core/shared": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.5", "rimraf": "^5.0.5", "typescript": "^5.7.2", "vite": "^7.0.6", "vite-plugin-dts": "^4.3.0", "vitest": "^3.2.4"}, "peerDependencies": {"@micro-core/core": "workspace:*"}}