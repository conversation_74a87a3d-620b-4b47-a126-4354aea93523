/**
 * 序列化器实现
 * 
 * @description 消息序列化和反序列化处理
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';

// 本地类型定义
type SerializationFormat = 'json' | 'msgpack' | 'protobuf' | 'cbor';

interface SerializationOptions {
    format?: SerializationFormat;
    compress?: boolean;
    validate?: boolean;
}

interface SerializerConfig {
    defaultFormat?: SerializationFormat;
    enableCompression?: boolean;
    enableValidation?: boolean;
    maxSize?: number;
}

interface SerializerMetrics {
    totalSerializations: number;
    totalDeserializations: number;
    totalBytes: number;
    compressionRatio: number;
    averageSerializationTime: number;
    averageDeserializationTime: number;
    errorCount: number;
    lastUpdated: number;
}

/**
 * 序列化器
 * 提供多种格式的序列化和反序列化能力
 */
export class Serializer {
    private readonly logger: Logger;
    private readonly config: SerializerConfig;
    private readonly metrics: SerializerMetrics;
    private readonly formatHandlers = new Map<SerializationFormat, FormatHandler>();

    constructor(config: SerializerConfig = {}) {
        this.logger = new Logger('Serializer');
        this.config = {
            defaultFormat: 'json',
            enableCompression: false,
            enableValidation: true,
            maxSize: 10 * 1024 * 1024, // 10MB
            ...config
        };

        this.metrics = {
            totalSerializations: 0,
            totalDeserializations: 0,
            totalBytes: 0,
            compressionRatio: 0,
            averageSerializationTime: 0,
            averageDeserializationTime: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };

        // 注册默认格式处理器
        this.registerDefaultHandlers();

        this.logger.info('序列化器初始化完成', { config: this.config });
    }

    /**
     * 序列化数据
     */
    serialize(data: any, options: SerializationOptions = {}): string {
        const startTime = Date.now();

        try {
            const format = options.format || this.config.defaultFormat!;
            const handler = this.getFormatHandler(format);

            // 验证数据
            if (this.config.enableValidation) {
                this.validateData(data);
            }

            // 执行序列化
            let serialized = handler.serialize(data, options);

            // 压缩处理
            if (this.config.enableCompression || options.compress) {
                const originalSize = serialized.length;
                serialized = this.compress(serialized);
                const compressedSize = serialized.length;

                // 更新压缩比率
                const ratio = compressedSize / originalSize;
                this.metrics.compressionRatio = (this.metrics.compressionRatio + ratio) / 2;
            }

            // 检查大小限制
            if (serialized.length > this.config.maxSize!) {
                throw createError('SERIALIZED_DATA_TOO_LARGE', `序列化数据过大: ${serialized.length} > ${this.config.maxSize}`);
            }

            // 更新指标
            this.updateSerializationMetrics(startTime, serialized.length);

            this.logger.debug('数据序列化成功', {
                format,
                originalSize: JSON.stringify(data).length,
                serializedSize: serialized.length,
                compressed: this.config.enableCompression || options.compress
            });

            return serialized;
        } catch (error) {
            this.metrics.errorCount++;
            this.logger.error('数据序列化失败', error);
            throw createError('SERIALIZATION_FAILED', `数据序列化失败: ${error.message}`, error);
        }
    }

    /**
     * 反序列化数据
     */
    deserialize<T = any>(serialized: string, options: SerializationOptions = {}): T {
        const startTime = Date.now();

        try {
            const format = options.format || this.config.defaultFormat!;
            const handler = this.getFormatHandler(format);

            let data = serialized;

            // 解压缩处理
            if (this.config.enableCompression || options.compress) {
                data = this.decompress(data);
            }

            // 执行反序列化
            const result = handler.deserialize(data, options);

            // 验证结果
            if (this.config.enableValidation) {
                this.validateData(result);
            }

            // 更新指标
            this.updateDeserializationMetrics(startTime);

            this.logger.debug('数据反序列化成功', {
                format,
                serializedSize: serialized.length,
                decompressed: this.config.enableCompression || options.compress
            });

            return result;
        } catch (error) {
            this.metrics.errorCount++;
            this.logger.error('数据反序列化失败', error);
            throw createError('DESERIALIZATION_FAILED', `数据反序列化失败: ${error.message}`, error);
        }
    }

    /**
     * 批量序列化
     */
    serializeBatch(dataArray: any[], options: SerializationOptions = {}): string[] {
        try {
            const results = dataArray.map((data, index) => {
                try {
                    return this.serialize(data, options);
                } catch (error) {
                    this.logger.error('批量序列化项失败', error, { index });
                    throw error;
                }
            });

            this.logger.debug('批量序列化完成', {
                totalItems: dataArray.length,
                format: options.format || this.config.defaultFormat
            });

            return results;
        } catch (error) {
            this.logger.error('批量序列化失败', error);
            throw createError('BATCH_SERIALIZATION_FAILED', `批量序列化失败: ${error.message}`, error);
        }
    }

    /**
     * 批量反序列化
     */
    deserializeBatch<T = any>(serializedArray: string[], options: SerializationOptions = {}): T[] {
        try {
            const results = serializedArray.map((serialized, index) => {
                try {
                    return this.deserialize<T>(serialized, options);
                } catch (error) {
                    this.logger.error('批量反序列化项失败', error, { index });
                    throw error;
                }
            });

            this.logger.debug('批量反序列化完成', {
                totalItems: serializedArray.length,
                format: options.format || this.config.defaultFormat
            });

            return results;
        } catch (error) {
            this.logger.error('批量反序列化失败', error);
            throw createError('BATCH_DESERIALIZATION_FAILED', `批量反序列化失败: ${error.message}`, error);
        }
    }

    /**
     * 注册格式处理器
     */
    registerFormatHandler(format: SerializationFormat, handler: FormatHandler): void {
        try {
            this.validateFormatHandler(handler);
            this.formatHandlers.set(format, handler);

            this.logger.debug('格式处理器注册成功', { format });
        } catch (error) {
            this.logger.error('格式处理器注册失败', error, { format });
            throw createError('FORMAT_HANDLER_REGISTRATION_FAILED', `格式处理器注册失败: ${error.message}`, error);
        }
    }

    /**
     * 注销格式处理器
     */
    unregisterFormatHandler(format: SerializationFormat): boolean {
        const result = this.formatHandlers.delete(format);

        if (result) {
            this.logger.debug('格式处理器注销成功', { format });
        }

        return result;
    }

    /**
     * 获取支持的格式
     */
    getSupportedFormats(): SerializationFormat[] {
        return Array.from(this.formatHandlers.keys());
    }

    /**
     * 检查格式是否支持
     */
    isFormatSupported(format: SerializationFormat): boolean {
        return this.formatHandlers.has(format);
    }

    /**
     * 估算序列化大小
     */
    estimateSize(data: any, format?: SerializationFormat): number {
        try {
            const testFormat = format || this.config.defaultFormat!;
            const handler = this.getFormatHandler(testFormat);

            // 执行测试序列化
            const serialized = handler.serialize(data, {});
            return serialized.length;
        } catch (error) {
            this.logger.error('估算序列化大小失败', error);
            return 0;
        }
    }

    /**
     * 获取性能指标
     */
    getMetrics(): SerializerMetrics {
        this.updateMetrics();
        return { ...this.metrics };
    }

    /**
     * 重置指标
     */
    resetMetrics(): void {
        this.metrics.totalSerializations = 0;
        this.metrics.totalDeserializations = 0;
        this.metrics.totalBytes = 0;
        this.metrics.compressionRatio = 0;
        this.metrics.averageSerializationTime = 0;
        this.metrics.averageDeserializationTime = 0;
        this.metrics.errorCount = 0;
        this.metrics.lastUpdated = Date.now();

        this.logger.debug('序列化器指标已重置');
    }

    /**
     * 销毁序列化器
     */
    destroy(): void {
        try {
            this.formatHandlers.clear();
            this.resetMetrics();
            this.logger.info('序列化器销毁完成');
        } catch (error) {
            this.logger.error('序列化器销毁失败', error);
        }
    }

    /**
     * 注册默认格式处理器
     */
    private registerDefaultHandlers(): void {
        // JSON 格式处理器
        this.registerFormatHandler('json', {
            serialize: (data: any) => JSON.stringify(data),
            deserialize: (serialized: string) => JSON.parse(serialized)
        });

        // MessagePack 格式处理器（模拟实现）
        this.registerFormatHandler('msgpack', {
            serialize: (data: any) => {
                // 这里应该使用真正的 MessagePack 库
                // 目前使用 JSON 作为占位符
                return JSON.stringify(data);
            },
            deserialize: (serialized: string) => {
                // 这里应该使用真正的 MessagePack 库
                // 目前使用 JSON 作为占位符
                return JSON.parse(serialized);
            }
        });

        // Protocol Buffers 格式处理器（模拟实现）
        this.registerFormatHandler('protobuf', {
            serialize: (data: any) => {
                // 这里应该使用真正的 Protocol Buffers 库
                // 目前使用 JSON 作为占位符
                return JSON.stringify(data);
            },
            deserialize: (serialized: string) => {
                // 这里应该使用真正的 Protocol Buffers 库
                // 目前使用 JSON 作为占位符
                return JSON.parse(serialized);
            }
        });

        // CBOR 格式处理器（模拟实现）
        this.registerFormatHandler('cbor', {
            serialize: (data: any) => {
                // 这里应该使用真正的 CBOR 库
                // 目前使用 JSON 作为占位符
                return JSON.stringify(data);
            },
            deserialize: (serialized: string) => {
                // 这里应该使用真正的 CBOR 库
                // 目前使用 JSON 作为占位符
                return JSON.parse(serialized);
            }
        });
    }

    /**
     * 获取格式处理器
     */
    private getFormatHandler(format: SerializationFormat): FormatHandler {
        const handler = this.formatHandlers.get(format);
        if (!handler) {
            throw createError('UNSUPPORTED_FORMAT', `不支持的序列化格式: ${format}`);
        }
        return handler;
    }

    /**
     * 验证数据
     */
    private validateData(data: any): void {
        if (data === undefined) {
            throw createError('INVALID_DATA', '数据不能为 undefined');
        }

        // 检查循环引用
        try {
            JSON.stringify(data);
        } catch (error) {
            if (error.message.includes('circular')) {
                throw createError('CIRCULAR_REFERENCE', '数据包含循环引用');
            }
            throw error;
        }
    }

    /**
     * 验证格式处理器
     */
    private validateFormatHandler(handler: FormatHandler): void {
        if (!handler || typeof handler !== 'object') {
            throw createError('INVALID_FORMAT_HANDLER', '格式处理器必须是对象');
        }

        if (typeof handler.serialize !== 'function') {
            throw createError('INVALID_FORMAT_HANDLER', '格式处理器必须包含 serialize 方法');
        }

        if (typeof handler.deserialize !== 'function') {
            throw createError('INVALID_FORMAT_HANDLER', '格式处理器必须包含 deserialize 方法');
        }
    }

    /**
     * 压缩数据
     */
    private compress(data: string): string {
        // 这里应该使用真正的压缩算法（如 gzip、deflate 等）
        // 目前使用简单的字符串压缩作为占位符
        try {
            // 简单的重复字符压缩
            return data.replace(/(.)\1{2,}/g, (match, char) => {
                return `${char}${match.length}`;
            });
        } catch (error) {
            this.logger.error('数据压缩失败', error);
            return data; // 压缩失败时返回原数据
        }
    }

    /**
     * 解压缩数据
     */
    private decompress(data: string): string {
        // 这里应该使用真正的解压缩算法
        // 目前使用简单的字符串解压缩作为占位符
        try {
            // 简单的重复字符解压缩
            return data.replace(/(.)\d+/g, (match, char) => {
                const count = parseInt(match.slice(1));
                return char.repeat(count);
            });
        } catch (error) {
            this.logger.error('数据解压缩失败', error);
            return data; // 解压缩失败时返回原数据
        }
    }

    /**
     * 更新序列化指标
     */
    private updateSerializationMetrics(startTime: number, serializedSize: number): void {
        this.metrics.totalSerializations++;
        this.metrics.totalBytes += serializedSize;

        const serializationTime = Date.now() - startTime;
        this.metrics.averageSerializationTime =
            (this.metrics.averageSerializationTime + serializationTime) / 2;

        this.updateMetrics();
    }

    /**
     * 更新反序列化指标
     */
    private updateDeserializationMetrics(startTime: number): void {
        this.metrics.totalDeserializations++;

        const deserializationTime = Date.now() - startTime;
        this.metrics.averageDeserializationTime =
            (this.metrics.averageDeserializationTime + deserializationTime) / 2;

        this.updateMetrics();
    }

    /**
     * 更新指标
     */
    private updateMetrics(): void {
        this.metrics.lastUpdated = Date.now();
    }
}

/**
 * 格式处理器接口
 */
interface FormatHandler {
    serialize: (data: any, options?: SerializationOptions) => string;
    deserialize: (serialized: string, options?: SerializationOptions) => any;
}