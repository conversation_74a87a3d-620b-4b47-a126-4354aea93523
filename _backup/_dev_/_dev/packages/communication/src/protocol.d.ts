/**
 * 通信协议实现
 *
 * @description 标准化的通信协议定义和处理
 * <AUTHOR> <<EMAIL>>
 */
import type { MessageType, ProtocolConfig, ProtocolHandler, ProtocolMessage, ProtocolMetrics } from '@micro-core/shared';
/**
 * 通信协议处理器
 * 提供标准化的消息协议处理
 */
export declare class Protocol {
    private readonly logger;
    private readonly config;
    private readonly handlers;
    private readonly metrics;
    private readonly messageQueue;
    private isProcessingQueue;
    constructor(config?: ProtocolConfig);
    /**
     * 注册消息处理器
     */
    registerHandler(messageType: MessageType, handler: ProtocolHandler): string;
    /**
     * 注销消息处理器
     */
    unregisterHandler(messageType: MessageType, handlerId: string): boolean;
    /**
     * 创建协议消息
     */
    createMessage(type: MessageType, payload: any, options?: {
        target?: string;
        source?: string;
        priority?: number;
        timeout?: number;
        metadata?: Record<string, any>;
    }): ProtocolMessage;
    /**
     * 处理协议消息
     */
    processMessage(message: ProtocolMessage): Promise<boolean>;
    /**
     * 批量处理消息
     */
    processMessages(messages: ProtocolMessage[]): Promise<boolean[]>;
    /**
     * 序列化消息
     */
    serializeMessage(message: ProtocolMessage): string;
    /**
     * 反序列化消息
     */
    deserializeMessage(data: string): ProtocolMessage;
    /**
     * 获取消息处理器数量
     */
    getHandlerCount(messageType?: MessageType): number;
    /**
     * 获取支持的消息类型
     */
    getSupportedMessageTypes(): MessageType[];
    /**
     * 获取性能指标
     */
    getMetrics(): ProtocolMetrics;
    /**
     * 清理所有处理器和队列
     */
    clear(): void;
    /**
     * 销毁协议处理器
     */
    destroy(): void;
    /**
     * 验证消息类型
     */
    private validateMessageType;
    /**
     * 验证处理器
     */
    private validateHandler;
    /**
     * 验证消息
     */
    private validateMessage;
    /**
     * 计算校验和
     */
    private calculateChecksum;
    /**
     * 生成处理器ID
     */
    private generateHandlerId;
    /**
     * 生成消息ID
     */
    private generateMessageId;
    /**
     * 创建处理器包装器
     */
    private createHandlerWrapper;
    /**
     * 将消息加入队列
     */
    private enqueueMessage;
    /**
     * 处理消息队列
     */
    private processMessageQueue;
    /**
     * 处理消息
     */
    private handleMessage;
    /**
     * 更新指标
     */
    private updateMetrics;
}
//# sourceMappingURL=protocol.d.ts.map