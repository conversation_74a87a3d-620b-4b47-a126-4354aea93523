/**
 * 状态同步系统实现
 *
 * @description 应用间状态同步和冲突解决
 * <AUTHOR> <<EMAIL>>
 */
import type { StateChangeHandler, StateConflictResolver, StateSyncConfig, StateSyncMetrics, StateSyncOptions } from './types';
/**
 * 状态同步器
 * 提供应用间的状态同步能力
 */
export declare class StateSync {
    private readonly logger;
    private readonly config;
    private readonly states;
    private readonly subscribers;
    private readonly conflictResolvers;
    private readonly metrics;
    private readonly syncQueue;
    private isProcessingSync;
    constructor(config?: StateSyncConfig);
    /**
     * 设置状态
     */
    setState(key: string, value: any, options?: StateSyncOptions): Promise<boolean>;
    /**
     * 获取状态
     */
    getState(key: string): any;
    /**
     * 删除状态
     */
    deleteState(key: string, options?: StateSyncOptions): Promise<boolean>;
    /**
     * 订阅状态变化
     */
    subscribe(key: string, handler: StateChangeHandler, options?: StateSyncOptions): string;
    /**
     * 取消订阅
     */
    unsubscribe(subscriptionId: string): boolean;
    /**
     * 批量设置状态
     */
    setBatch(states: Record<string, any>, options?: StateSyncOptions): Promise<boolean>;
    /**
     * 获取所有状态
     */
    getAllStates(): Record<string, any>;
    /**
     * 获取状态键列表
     */
    getStateKeys(): string[];
    /**
     * 检查状态是否存在
     */
    hasState(key: string): boolean;
    /**
     * 设置冲突解决器
     */
    setConflictResolver(key: string, resolver: StateConflictResolver): void;
    /**
     * 移除冲突解决器
     */
    removeConflictResolver(key: string): boolean;
    /**
     * 获取性能指标
     */
    getMetrics(): StateSyncMetrics;
    /**
     * 清理所有状态和订阅
     */
    clear(): void;
    /**
     * 销毁状态同步器
     */
    destroy(): void;
    /**
     * 验证状态键
     */
    private validateStateKey;
    /**
     * 验证处理器
     */
    private validateHandler;
    /**
     * 生成操作ID
     */
    private generateOperationId;
    /**
     * 生成订阅ID
     */
    private generateSubscriptionId;
    /**
     * 将同步操作加入队列
     */
    private enqueueSyncOperation;
    /**
     * 处理同步队列
     */
    private processSyncQueue;
    /**
     * 处理同步操作
     */
    private processSyncOperation;
    /**
     * 检查冲突
     */
    private checkConflict;
    /**
     * 解决冲突
     */
    private resolveConflict;
    /**
     * 执行操作
     */
    private executeOperation;
    /**
     * 通知订阅者
     */
    private notifySubscribers;
    /**
     * 通知单个订阅者
     */
    private notifySubscriber;
    /**
     * 更新同步指标
     */
    private updateSyncMetrics;
    /**
     * 更新指标
     */
    private updateMetrics;
}
//# sourceMappingURL=state-sync.d.ts.map