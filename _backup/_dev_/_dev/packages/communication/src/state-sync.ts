/**
 * 状态同步系统实现
 * 
 * @description 应用间状态同步和冲突解决
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type {
    StateChangeHandler,
    StateConflictResolver,
    StateSyncConfig,
    StateSyncMetrics,
    StateSyncOptions
} from './types';

/**
 * 状态同步器
 * 提供应用间的状态同步能力
 */
export class StateSync {
    private readonly logger: Logger;
    private readonly config: StateSyncConfig;
    private readonly states = new Map<string, any>();
    private readonly subscribers = new Map<string, Set<StateSubscription>>();
    private readonly conflictResolvers = new Map<string, StateConflictResolver>();
    private readonly metrics: StateSyncMetrics;
    private readonly syncQueue: StateSyncOperation[] = [];
    private isProcessingSync = false;

    constructor(config: StateSyncConfig = {}) {
        this.logger = new Logger('StateSync');
        this.config = {
            enableConflictResolution: true,
            enablePersistence: false,
            enableQueue: true,
            queueSize: 1000,
            syncInterval: 100,
            maxRetries: 3,
            ...config
        };

        this.metrics = {
            totalStates: 0,
            totalSubscriptions: 0,
            totalSyncs: 0,
            conflictCount: 0,
            errorCount: 0,
            averageSyncTime: 0,
            lastUpdated: Date.now()
        };

        this.logger.info('状态同步器初始化完成', { config: this.config });
    }

    /**
     * 设置状态
     */
    setState(key: string, value: any, options: StateSyncOptions = {}): Promise<boolean> {
        try {
            this.validateStateKey(key);

            const operation: StateSyncOperation = {
                id: this.generateOperationId(),
                type: 'set',
                key,
                value,
                previousValue: this.states.get(key),
                timestamp: Date.now(),
                source: options.source || 'unknown',
                options
            };

            // 如果启用队列，将操作加入队列
            if (this.config.enableQueue) {
                this.enqueueSyncOperation(operation);
                this.processSyncQueue();
                return Promise.resolve(true);
            }

            // 直接处理操作
            return this.processSyncOperation(operation);
        } catch (error) {
            this.logger.error('设置状态失败', error, { key });
            this.metrics.errorCount++;
            return Promise.resolve(false);
        }
    }

    /**
     * 获取状态
     */
    getState(key: string): any {
        try {
            this.validateStateKey(key);
            return this.states.get(key);
        } catch (error) {
            this.logger.error('获取状态失败', error, { key });
            return undefined;
        }
    }

    /**
     * 删除状态
     */
    deleteState(key: string, options: StateSyncOptions = {}): Promise<boolean> {
        try {
            this.validateStateKey(key);

            if (!this.states.has(key)) {
                return Promise.resolve(false);
            }

            const operation: StateSyncOperation = {
                id: this.generateOperationId(),
                type: 'delete',
                key,
                value: undefined,
                previousValue: this.states.get(key),
                timestamp: Date.now(),
                source: options.source || 'unknown',
                options
            };

            // 如果启用队列，将操作加入队列
            if (this.config.enableQueue) {
                this.enqueueSyncOperation(operation);
                this.processSyncQueue();
                return Promise.resolve(true);
            }

            // 直接处理操作
            return this.processSyncOperation(operation);
        } catch (error) {
            this.logger.error('删除状态失败', error, { key });
            this.metrics.errorCount++;
            return Promise.resolve(false);
        }
    }

    /**
     * 订阅状态变化
     */
    subscribe(key: string, handler: StateChangeHandler, options: StateSyncOptions = {}): string {
        try {
            this.validateStateKey(key);
            this.validateHandler(handler);

            const subscriptionId = this.generateSubscriptionId();
            const subscription: StateSubscription = {
                id: subscriptionId,
                key,
                handler,
                options,
                createdAt: Date.now(),
                callCount: 0,
                lastCalled: 0
            };

            if (!this.subscribers.has(key)) {
                this.subscribers.set(key, new Set());
            }
            this.subscribers.get(key)!.add(subscription);

            this.metrics.totalSubscriptions++;

            this.logger.debug('状态订阅成功', {
                key,
                subscriptionId,
                totalSubscriptions: this.metrics.totalSubscriptions
            });

            return subscriptionId;
        } catch (error) {
            this.logger.error('状态订阅失败', error, { key });
            throw createError('STATE_SUBSCRIPTION_FAILED', `状态订阅失败: ${error.message}`, error);
        }
    }

    /**
     * 取消订阅
     */
    unsubscribe(subscriptionId: string): boolean {
        try {
            for (const [key, subscriptions] of this.subscribers) {
                for (const subscription of subscriptions) {
                    if (subscription.id === subscriptionId) {
                        subscriptions.delete(subscription);
                        if (subscriptions.size === 0) {
                            this.subscribers.delete(key);
                        }
                        this.metrics.totalSubscriptions--;

                        this.logger.debug('取消状态订阅成功', {
                            key,
                            subscriptionId,
                            totalSubscriptions: this.metrics.totalSubscriptions
                        });

                        return true;
                    }
                }
            }

            return false;
        } catch (error) {
            this.logger.error('取消状态订阅失败', error, { subscriptionId });
            return false;
        }
    }

    /**
     * 批量设置状态
     */
    async setBatch(states: Record<string, any>, options: StateSyncOptions = {}): Promise<boolean> {
        try {
            const operations: StateSyncOperation[] = [];

            for (const [key, value] of Object.entries(states)) {
                this.validateStateKey(key);

                operations.push({
                    id: this.generateOperationId(),
                    type: 'set',
                    key,
                    value,
                    previousValue: this.states.get(key),
                    timestamp: Date.now(),
                    source: options.source || 'unknown',
                    options
                });
            }

            // 批量处理操作
            const results = await Promise.all(
                operations.map(op => this.processSyncOperation(op))
            );

            const success = results.every(result => result);

            this.logger.debug('批量设置状态完成', {
                totalOperations: operations.length,
                successCount: results.filter(r => r).length
            });

            return success;
        } catch (error) {
            this.logger.error('批量设置状态失败', error);
            this.metrics.errorCount++;
            return false;
        }
    }

    /**
     * 获取所有状态
     */
    getAllStates(): Record<string, any> {
        const result: Record<string, any> = {};
        for (const [key, value] of this.states) {
            result[key] = value;
        }
        return result;
    }

    /**
     * 获取状态键列表
     */
    getStateKeys(): string[] {
        return Array.from(this.states.keys());
    }

    /**
     * 检查状态是否存在
     */
    hasState(key: string): boolean {
        try {
            this.validateStateKey(key);
            return this.states.has(key);
        } catch (error) {
            return false;
        }
    }

    /**
     * 设置冲突解决器
     */
    setConflictResolver(key: string, resolver: StateConflictResolver): void {
        try {
            this.validateStateKey(key);
            this.conflictResolvers.set(key, resolver);

            this.logger.debug('冲突解决器设置成功', { key });
        } catch (error) {
            this.logger.error('设置冲突解决器失败', error, { key });
        }
    }

    /**
     * 移除冲突解决器
     */
    removeConflictResolver(key: string): boolean {
        try {
            this.validateStateKey(key);
            const result = this.conflictResolvers.delete(key);

            this.logger.debug('冲突解决器移除成功', { key });
            return result;
        } catch (error) {
            this.logger.error('移除冲突解决器失败', error, { key });
            return false;
        }
    }

    /**
     * 获取性能指标
     */
    getMetrics(): StateSyncMetrics {
        this.updateMetrics();
        return { ...this.metrics };
    }

    /**
     * 清理所有状态和订阅
     */
    clear(): void {
        try {
            this.states.clear();
            this.subscribers.clear();
            this.conflictResolvers.clear();
            this.syncQueue.length = 0;

            this.metrics.totalStates = 0;
            this.metrics.totalSubscriptions = 0;

            this.logger.debug('状态同步器清理完成');
        } catch (error) {
            this.logger.error('状态同步器清理失败', error);
        }
    }

    /**
     * 销毁状态同步器
     */
    destroy(): void {
        try {
            this.clear();
            this.logger.info('状态同步器销毁完成');
        } catch (error) {
            this.logger.error('状态同步器销毁失败', error);
        }
    }

    /**
     * 验证状态键
     */
    private validateStateKey(key: string): void {
        if (!key || typeof key !== 'string') {
            throw createError('INVALID_STATE_KEY', '状态键必须是非空字符串');
        }
    }

    /**
     * 验证处理器
     */
    private validateHandler(handler: StateChangeHandler): void {
        if (typeof handler !== 'function') {
            throw createError('INVALID_STATE_HANDLER', '状态变化处理器必须是函数');
        }
    }

    /**
     * 生成操作ID
     */
    private generateOperationId(): string {
        return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 生成订阅ID
     */
    private generateSubscriptionId(): string {
        return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 将同步操作加入队列
     */
    private enqueueSyncOperation(operation: StateSyncOperation): void {
        if (this.syncQueue.length >= this.config.queueSize!) {
            // 队列满了，移除最旧的操作
            this.syncQueue.shift();
        }

        this.syncQueue.push(operation);
    }

    /**
     * 处理同步队列
     */
    private async processSyncQueue(): Promise<void> {
        if (this.isProcessingSync || this.syncQueue.length === 0) {
            return;
        }

        this.isProcessingSync = true;

        try {
            while (this.syncQueue.length > 0) {
                const operation = this.syncQueue.shift();
                if (operation) {
                    await this.processSyncOperation(operation);
                }
            }
        } finally {
            this.isProcessingSync = false;
        }
    }

    /**
     * 处理同步操作
     */
    private async processSyncOperation(operation: StateSyncOperation): Promise<boolean> {
        const startTime = Date.now();

        try {
            // 检查冲突
            const hasConflict = await this.checkConflict(operation);
            if (hasConflict) {
                const resolved = await this.resolveConflict(operation);
                if (!resolved) {
                    this.logger.warn('状态冲突解决失败', {
                        operationId: operation.id,
                        key: operation.key
                    });
                    return false;
                }
            }

            // 执行操作
            const success = await this.executeOperation(operation);
            if (!success) {
                return false;
            }

            // 通知订阅者
            await this.notifySubscribers(operation);

            // 更新指标
            this.updateSyncMetrics(startTime);

            return true;
        } catch (error) {
            this.logger.error('处理同步操作失败', error, {
                operationId: operation.id,
                key: operation.key
            });
            this.metrics.errorCount++;
            return false;
        }
    }

    /**
     * 检查冲突
     */
    private async checkConflict(operation: StateSyncOperation): Promise<boolean> {
        if (!this.config.enableConflictResolution) {
            return false;
        }

        const currentValue = this.states.get(operation.key);
        const hasCurrentValue = this.states.has(operation.key);

        // 如果当前没有值，不存在冲突
        if (!hasCurrentValue) {
            return false;
        }

        // 如果操作的前一个值与当前值不匹配，存在冲突
        if (operation.previousValue !== currentValue) {
            this.metrics.conflictCount++;
            return true;
        }

        return false;
    }

    /**
     * 解决冲突
     */
    private async resolveConflict(operation: StateSyncOperation): Promise<boolean> {
        const resolver = this.conflictResolvers.get(operation.key);
        if (!resolver) {
            // 没有冲突解决器，使用默认策略（最后写入获胜）
            return true;
        }

        try {
            const currentValue = this.states.get(operation.key);
            const resolvedValue = await resolver(
                currentValue,
                operation.value,
                operation.previousValue,
                operation
            );

            // 更新操作的值为解决后的值
            operation.value = resolvedValue;
            return true;
        } catch (error) {
            this.logger.error('冲突解决器执行失败', error, {
                operationId: operation.id,
                key: operation.key
            });
            return false;
        }
    }

    /**
     * 执行操作
     */
    private async executeOperation(operation: StateSyncOperation): Promise<boolean> {
        try {
            switch (operation.type) {
                case 'set':
                    this.states.set(operation.key, operation.value);
                    if (!this.states.has(operation.key)) {
                        this.metrics.totalStates++;
                    }
                    break;

                case 'delete':
                    const deleted = this.states.delete(operation.key);
                    if (deleted) {
                        this.metrics.totalStates--;
                    }
                    break;

                default:
                    throw createError('UNKNOWN_OPERATION_TYPE', `未知的操作类型: ${operation.type}`);
            }

            this.metrics.totalSyncs++;
            return true;
        } catch (error) {
            this.logger.error('执行操作失败', error, {
                operationId: operation.id,
                type: operation.type,
                key: operation.key
            });
            return false;
        }
    }

    /**
     * 通知订阅者
     */
    private async notifySubscribers(operation: StateSyncOperation): Promise<void> {
        const subscriptions = this.subscribers.get(operation.key);
        if (!subscriptions || subscriptions.size === 0) {
            return;
        }

        const promises = Array.from(subscriptions).map(subscription =>
            this.notifySubscriber(subscription, operation)
        );

        await Promise.allSettled(promises);
    }

    /**
     * 通知单个订阅者
     */
    private async notifySubscriber(subscription: StateSubscription, operation: StateSyncOperation): Promise<void> {
        try {
            subscription.callCount++;
            subscription.lastCalled = Date.now();

            await subscription.handler(
                operation.value,
                operation.previousValue,
                {
                    key: operation.key,
                    type: operation.type,
                    source: operation.source,
                    timestamp: operation.timestamp,
                    operationId: operation.id
                }
            );
        } catch (error) {
            this.logger.error('通知订阅者失败', error, {
                subscriptionId: subscription.id,
                key: operation.key,
                operationId: operation.id
            });
        }
    }

    /**
     * 更新同步指标
     */
    private updateSyncMetrics(startTime: number): void {
        const syncTime = Date.now() - startTime;
        this.metrics.averageSyncTime = (this.metrics.averageSyncTime + syncTime) / 2;
        this.updateMetrics();
    }

    /**
     * 更新指标
     */
    private updateMetrics(): void {
        this.metrics.lastUpdated = Date.now();
    }
}

/**
 * 状态订阅信息
 */
interface StateSubscription {
    id: string;
    key: string;
    handler: StateChangeHandler;
    options: StateSyncOptions;
    createdAt: number;
    callCount: number;
    lastCalled: number;
}

/**
 * 同步操作
 */
interface StateSyncOperation {
    id: string;
    type: 'set' | 'delete';
    key: string;
    value: any;
    previousValue: any;
    timestamp: number;
    source: string;
    options: StateSyncOptions;
}