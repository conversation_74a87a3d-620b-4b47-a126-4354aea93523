/**
 * 消息通道系统实现
 * 
 * @description 跨应用消息传递通道
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import type {
    ChannelMessage,
    MessageChannelConfig,
    MessageChannelMetrics,
    MessageChannelOptions,
    MessageHandler,
    MessageSubscription
} from './types';

/**
 * 消息通道实现
 * 提供跨应用的消息传递能力
 */
export class MessageChannel {
    private readonly logger: Logger;
    private readonly config: MessageChannelConfig;
    private readonly channels = new Map<string, ChannelInfo>();
    private readonly subscriptions = new Map<string, MessageSubscription>();
    private readonly metrics: MessageChannelMetrics;
    private readonly messageQueue: ChannelMessage[] = [];
    private isProcessingQueue = false;

    constructor(config: MessageChannelConfig = {}) {
        this.logger = new Logger('MessageChannel');
        this.config = {
            maxChannels: 50,
            maxSubscriptions: 200,
            enableQueue: true,
            queueSize: 1000,
            enablePersistence: false,
            enableEncryption: false,
            ...config
        };

        this.metrics = {
            totalChannels: 0,
            totalSubscriptions: 0,
            totalMessages: 0,
            messagesPerSecond: 0,
            averageLatency: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };

        this.logger.info('消息通道初始化完成', { config: this.config });
    }

    /**
     * 创建通道
     */
    createChannel(channelId: string, options: MessageChannelOptions = {}): void {
        try {
            this.validateChannelId(channelId);

            if (this.channels.has(channelId)) {
                throw createError('CHANNEL_ALREADY_EXISTS', `通道已存在: ${channelId}`);
            }

            // 检查通道数量限制
            if (this.channels.size >= this.config.maxChannels!) {
                throw createError('MAX_CHANNELS_EXCEEDED', `通道数量超过限制: ${this.config.maxChannels}`);
            }

            const channelInfo: ChannelInfo = {
                id: channelId,
                options,
                subscribers: new Set(),
                messageCount: 0,
                createdAt: Date.now(),
                lastActivity: Date.now()
            };

            this.channels.set(channelId, channelInfo);
            this.metrics.totalChannels++;

            this.logger.debug('通道创建成功', {
                channelId,
                totalChannels: this.metrics.totalChannels
            });
        } catch (error) {
            this.logger.error('通道创建失败', error, { channelId });
            throw createError('CHANNEL_CREATION_FAILED', `通道创建失败: ${error.message}`, error);
        }
    }

    /**
     * 删除通道
     */
    deleteChannel(channelId: string): boolean {
        try {
            const channelInfo = this.channels.get(channelId);
            if (!channelInfo) {
                return false;
            }

            // 清理所有订阅
            for (const subscriptionId of channelInfo.subscribers) {
                this.subscriptions.delete(subscriptionId);
                this.metrics.totalSubscriptions--;
            }

            this.channels.delete(channelId);
            this.metrics.totalChannels--;

            this.logger.debug('通道删除成功', {
                channelId,
                totalChannels: this.metrics.totalChannels
            });

            return true;
        } catch (error) {
            this.logger.error('通道删除失败', error, { channelId });
            return false;
        }
    }

    /**
     * 订阅通道消息
     */
    subscribe(channelId: string, handler: MessageHandler, options: MessageChannelOptions = {}): MessageSubscription {
        try {
            this.validateChannelId(channelId);
            this.validateHandler(handler);

            // 确保通道存在
            if (!this.channels.has(channelId)) {
                this.createChannel(channelId, options);
            }

            // 检查订阅数量限制
            if (this.subscriptions.size >= this.config.maxSubscriptions!) {
                throw createError('MAX_SUBSCRIPTIONS_EXCEEDED', `订阅数量超过限制: ${this.config.maxSubscriptions}`);
            }

            const subscriptionId = this.generateSubscriptionId();
            const subscription: MessageSubscription = {
                id: subscriptionId,
                channelId,
                handler,
                options,
                createdAt: Date.now(),
                messageCount: 0,
                lastMessage: 0
            };

            this.subscriptions.set(subscriptionId, subscription);
            this.channels.get(channelId)!.subscribers.add(subscriptionId);
            this.metrics.totalSubscriptions++;

            this.logger.debug('通道订阅成功', {
                channelId,
                subscriptionId,
                totalSubscriptions: this.metrics.totalSubscriptions
            });

            return subscription;
        } catch (error) {
            this.logger.error('通道订阅失败', error, { channelId });
            throw createError('CHANNEL_SUBSCRIPTION_FAILED', `通道订阅失败: ${error.message}`, error);
        }
    }

    /**
     * 取消订阅
     */
    unsubscribe(subscription: MessageSubscription): boolean;
    unsubscribe(subscriptionId: string): boolean;
    unsubscribe(subscriptionOrId: MessageSubscription | string): boolean {
        try {
            const subscriptionId = typeof subscriptionOrId === 'string'
                ? subscriptionOrId
                : subscriptionOrId.id;

            const subscription = this.subscriptions.get(subscriptionId);
            if (!subscription) {
                return false;
            }

            // 从通道中移除订阅
            const channelInfo = this.channels.get(subscription.channelId);
            if (channelInfo) {
                channelInfo.subscribers.delete(subscriptionId);
            }

            this.subscriptions.delete(subscriptionId);
            this.metrics.totalSubscriptions--;

            this.logger.debug('取消订阅成功', {
                channelId: subscription.channelId,
                subscriptionId,
                totalSubscriptions: this.metrics.totalSubscriptions
            });

            return true;
        } catch (error) {
            this.logger.error('取消订阅失败', error);
            return false;
        }
    }

    /**
     * 发送消息
     */
    async send(channelId: string, data: any, options: MessageChannelOptions = {}): Promise<boolean> {
        try {
            this.validateChannelId(channelId);

            const channelInfo = this.channels.get(channelId);
            if (!channelInfo) {
                throw createError('CHANNEL_NOT_FOUND', `通道不存在: ${channelId}`);
            }

            const message: ChannelMessage = {
                id: this.generateMessageId(),
                channelId,
                data,
                timestamp: Date.now(),
                sender: options.sender || 'unknown',
                options
            };

            // 如果启用队列，将消息加入队列
            if (this.config.enableQueue) {
                this.enqueueMessage(message);
                this.processMessageQueue();
                return true;
            }

            // 直接处理消息
            return await this.processMessage(message);
        } catch (error) {
            this.logger.error('消息发送失败', error, { channelId });
            this.metrics.errorCount++;
            return false;
        }
    }

    /**
     * 广播消息到所有通道
     */
    async broadcast(data: any, options: MessageChannelOptions = {}): Promise<number> {
        let successCount = 0;

        for (const channelId of this.channels.keys()) {
            try {
                const success = await this.send(channelId, data, options);
                if (success) {
                    successCount++;
                }
            } catch (error) {
                this.logger.error('广播消息失败', error, { channelId });
            }
        }

        this.logger.debug('广播消息完成', {
            totalChannels: this.channels.size,
            successCount
        });

        return successCount;
    }

    /**
     * 获取通道信息
     */
    getChannelInfo(channelId: string): ChannelInfo | undefined {
        return this.channels.get(channelId);
    }

    /**
     * 获取所有通道ID
     */
    getChannelIds(): string[] {
        return Array.from(this.channels.keys());
    }

    /**
     * 获取通道订阅者数量
     */
    getSubscriberCount(channelId: string): number {
        const channelInfo = this.channels.get(channelId);
        return channelInfo ? channelInfo.subscribers.size : 0;
    }

    /**
     * 获取性能指标
     */
    getMetrics(): MessageChannelMetrics {
        this.updateMetrics();
        return { ...this.metrics };
    }

    /**
     * 清理所有通道和订阅
     */
    clear(): void {
        try {
            this.channels.clear();
            this.subscriptions.clear();
            this.messageQueue.length = 0;

            this.metrics.totalChannels = 0;
            this.metrics.totalSubscriptions = 0;

            this.logger.debug('消息通道清理完成');
        } catch (error) {
            this.logger.error('消息通道清理失败', error);
        }
    }

    /**
     * 销毁消息通道
     */
    destroy(): void {
        try {
            this.clear();
            this.logger.info('消息通道销毁完成');
        } catch (error) {
            this.logger.error('消息通道销毁失败', error);
        }
    }

    /**
     * 验证通道ID
     */
    private validateChannelId(channelId: string): void {
        if (!channelId || typeof channelId !== 'string') {
            throw createError('INVALID_CHANNEL_ID', '通道ID必须是非空字符串');
        }
    }

    /**
     * 验证处理器
     */
    private validateHandler(handler: MessageHandler): void {
        if (typeof handler !== 'function') {
            throw createError('INVALID_MESSAGE_HANDLER', '消息处理器必须是函数');
        }
    }

    /**
     * 生成订阅ID
     */
    private generateSubscriptionId(): string {
        return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 生成消息ID
     */
    private generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 将消息加入队列
     */
    private enqueueMessage(message: ChannelMessage): void {
        if (this.messageQueue.length >= this.config.queueSize!) {
            // 队列满了，移除最旧的消息
            this.messageQueue.shift();
        }

        this.messageQueue.push(message);
    }

    /**
     * 处理消息队列
     */
    private async processMessageQueue(): Promise<void> {
        if (this.isProcessingQueue || this.messageQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        try {
            while (this.messageQueue.length > 0) {
                const message = this.messageQueue.shift();
                if (message) {
                    await this.processMessage(message);
                }
            }
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * 处理消息
     */
    private async processMessage(message: ChannelMessage): Promise<boolean> {
        const startTime = Date.now();
        const channelInfo = this.channels.get(message.channelId);

        if (!channelInfo) {
            return false;
        }

        const subscribers = Array.from(channelInfo.subscribers)
            .map(id => this.subscriptions.get(id))
            .filter(Boolean) as MessageSubscription[];

        if (subscribers.length === 0) {
            return false;
        }

        // 执行所有订阅者的处理器
        const promises = subscribers.map(subscription =>
            this.executeHandler(subscription, message)
        );

        await Promise.allSettled(promises);

        // 更新统计信息
        channelInfo.messageCount++;
        channelInfo.lastActivity = Date.now();
        this.metrics.totalMessages++;

        // 更新延迟指标
        const latency = Date.now() - startTime;
        this.metrics.averageLatency = (this.metrics.averageLatency + latency) / 2;

        this.updateMetrics();

        return true;
    }

    /**
     * 执行处理器
     */
    private async executeHandler(subscription: MessageSubscription, message: ChannelMessage): Promise<void> {
        try {
            subscription.messageCount++;
            subscription.lastMessage = Date.now();

            await subscription.handler(message.data, {
                messageId: message.id,
                channelId: message.channelId,
                timestamp: message.timestamp,
                sender: message.sender
            });
        } catch (error) {
            this.logger.error('消息处理器执行失败', error, {
                channelId: message.channelId,
                subscriptionId: subscription.id,
                messageId: message.id
            });
            this.metrics.errorCount++;
        }
    }

    /**
     * 更新指标
     */
    private updateMetrics(): void {
        const now = Date.now();
        const timeDiff = (now - this.metrics.lastUpdated) / 1000;

        if (timeDiff > 0) {
            this.metrics.messagesPerSecond = this.metrics.totalMessages / timeDiff;
        }

        this.metrics.lastUpdated = now;
    }
}

/**
 * 通道信息接口
 */
interface ChannelInfo {
    id: string;
    options: MessageChannelOptions;
    subscribers: Set<string>;
    messageCount: number;
    createdAt: number;
    lastActivity: number;
}