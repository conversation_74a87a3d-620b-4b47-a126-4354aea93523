/**
 * 通信管理器实现
 * 
 * @description 统一的通信接口和状态管理
 * <AUTHOR> <<EMAIL>>
 */

import { createError, createLogger } from '@micro-core/shared';
import { EventBus } from './event-bus';
import { MessageChannel } from './message-channel';
import type {
    CommunicationManagerOptions,
    CommunicationMetrics,
    CommunicationState,
    EventHandler,
    ExtendedCommunicationConfig,
    MessageHandler
} from './types';

/**
 * 通信管理器
 * 统一管理事件总线和消息通道
 */
export class CommunicationManager {
    private readonly logger: ReturnType<typeof createLogger>;
    private readonly config: ExtendedCommunicationConfig;
    private readonly eventBus: EventBus;
    private readonly messageChannel: MessageChannel;
    private readonly state: CommunicationState;
    private readonly subscriptions = new Map<string, any>();

    constructor(config: Partial<ExtendedCommunicationConfig> = {}) {
        this.logger = createLogger('CommunicationManager');
        this.config = {
            enabled: true,
            protocolVersion: '1.0.0',
            maxMessageSize: 1024 * 1024, // 1MB
            timeout: 5000,
            persistence: false,
            permissions: [],
            enableEventBus: true,
            enableMessageChannel: true,
            enableMetrics: true,
            enablePersistence: false,
            enableEncryption: false,
            ...config
        };

        // 初始化状态
        this.state = {
            initialized: false,
            eventBusActive: false,
            messageChannelActive: false,
            totalSubscriptions: 0,
            totalChannels: 0,
            lastActivity: Date.now()
        };

        // 初始化事件总线
        this.eventBus = new EventBus(this.config.eventBus);

        // 初始化消息通道
        this.messageChannel = new MessageChannel(this.config.messageChannel);

        this.logger.info('通信管理器初始化完成', { config: this.config });
    }

    /**
     * 初始化通信管理器
     */
    async initialize(options: CommunicationManagerOptions = {}): Promise<void> {
        try {
            if (this.state.initialized) {
                this.logger.warn('通信管理器已经初始化');
                return;
            }

            // 激活事件总线
            if (this.config.enableEventBus) {
                this.state.eventBusActive = true;
                this.logger.debug('事件总线已激活');
            }

            // 激活消息通道
            if (this.config.enableMessageChannel) {
                this.state.messageChannelActive = true;
                this.logger.debug('消息通道已激活');
            }

            // 设置跨通信桥接
            this.setupCommunicationBridge();

            this.state.initialized = true;
            this.state.lastActivity = Date.now();

            this.logger.info('通信管理器初始化成功');
        } catch (error) {
            this.logger.error('通信管理器初始化失败', error);
            throw createError('COMMUNICATION_MANAGER_INIT_FAILED', `通信管理器初始化失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 订阅事件
     */
    on(event: string, handler: EventHandler, options: any = {}): string {
        try {
            this.ensureInitialized();
            this.ensureEventBusActive();

            const subscription = this.eventBus.on(event, handler, options);
            const subscriptionId = this.generateSubscriptionId('event');

            this.subscriptions.set(subscriptionId, {
                type: 'event',
                subscription,
                event,
                handler,
                createdAt: Date.now()
            });

            this.state.totalSubscriptions++;
            this.state.lastActivity = Date.now();

            this.logger.debug('事件订阅成功', { event, subscriptionId });
            return subscriptionId;
        } catch (error) {
            this.logger.error('事件订阅失败', error, { event });
            throw createError('EVENT_SUBSCRIPTION_FAILED', `事件订阅失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 订阅事件（仅触发一次）
     */
    once(event: string, handler: EventHandler, options: any = {}): string {
        try {
            this.ensureInitialized();
            this.ensureEventBusActive();

            const subscription = this.eventBus.once(event, handler, options);
            const subscriptionId = this.generateSubscriptionId('event_once');

            this.subscriptions.set(subscriptionId, {
                type: 'event_once',
                subscription,
                event,
                handler,
                createdAt: Date.now()
            });

            this.state.totalSubscriptions++;
            this.state.lastActivity = Date.now();

            this.logger.debug('一次性事件订阅成功', { event, subscriptionId });
            return subscriptionId;
        } catch (error) {
            this.logger.error('一次性事件订阅失败', error, { event });
            throw createError('EVENT_ONCE_SUBSCRIPTION_FAILED', `一次性事件订阅失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 发布事件
     */
    async emit(event: string, data?: any): Promise<boolean> {
        try {
            this.ensureInitialized();
            this.ensureEventBusActive();

            const result = await this.eventBus.emit(event, data);
            this.state.lastActivity = Date.now();

            this.logger.debug('事件发布成功', { event, hasData: data !== undefined });
            return result;
        } catch (error) {
            this.logger.error('事件发布失败', error, { event });
            return false;
        }
    }

    /**
     * 同步发布事件
     */
    emitSync(event: string, data?: any): boolean {
        try {
            this.ensureInitialized();
            this.ensureEventBusActive();

            const result = this.eventBus.emitSync(event, data);
            this.state.lastActivity = Date.now();

            this.logger.debug('同步事件发布成功', { event, hasData: data !== undefined });
            return result;
        } catch (error) {
            this.logger.error('同步事件发布失败', error, { event });
            return false;
        }
    }

    /**
     * 创建消息通道
     */
    createChannel(channelId: string, options: any = {}): void {
        try {
            this.ensureInitialized();
            this.ensureMessageChannelActive();

            this.messageChannel.createChannel(channelId, options);
            this.state.totalChannels++;
            this.state.lastActivity = Date.now();

            this.logger.debug('消息通道创建成功', { channelId });
        } catch (error) {
            this.logger.error('消息通道创建失败', error, { channelId });
            throw createError('CHANNEL_CREATION_FAILED', `消息通道创建失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 订阅消息通道
     */
    subscribe(channelId: string, handler: MessageHandler, options: any = {}): string {
        try {
            this.ensureInitialized();
            this.ensureMessageChannelActive();

            const subscription = this.messageChannel.subscribe(channelId, handler, options);
            const subscriptionId = this.generateSubscriptionId('channel');

            this.subscriptions.set(subscriptionId, {
                type: 'channel',
                subscription,
                channelId,
                handler,
                createdAt: Date.now()
            });

            this.state.totalSubscriptions++;
            this.state.lastActivity = Date.now();

            this.logger.debug('消息通道订阅成功', { channelId, subscriptionId });
            return subscriptionId;
        } catch (error) {
            this.logger.error('消息通道订阅失败', error, { channelId });
            throw createError('CHANNEL_SUBSCRIPTION_FAILED', `消息通道订阅失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 发送消息
     */
    async send(channelId: string, data: any, options: any = {}): Promise<boolean> {
        try {
            this.ensureInitialized();
            this.ensureMessageChannelActive();

            const result = await this.messageChannel.send(channelId, data, options);
            this.state.lastActivity = Date.now();

            this.logger.debug('消息发送成功', { channelId, hasData: data !== undefined });
            return result;
        } catch (error) {
            this.logger.error('消息发送失败', error, { channelId });
            return false;
        }
    }

    /**
     * 广播消息
     */
    async broadcast(data: any, options: any = {}): Promise<number> {
        try {
            this.ensureInitialized();
            this.ensureMessageChannelActive();

            const result = await this.messageChannel.broadcast(data, options);
            this.state.lastActivity = Date.now();

            this.logger.debug('消息广播成功', { successCount: result });
            return result;
        } catch (error) {
            this.logger.error('消息广播失败', error);
            return 0;
        }
    }

    /**
     * 取消订阅
     */
    unsubscribe(subscriptionId: string): boolean {
        try {
            const subscriptionInfo = this.subscriptions.get(subscriptionId);
            if (!subscriptionInfo) {
                return false;
            }

            let result = false;

            switch (subscriptionInfo.type) {
                case 'event':
                case 'event_once':
                    result = this.eventBus.off(subscriptionInfo.subscription);
                    break;
                case 'channel':
                    result = this.messageChannel.unsubscribe(subscriptionInfo.subscription);
                    break;
                default:
                    this.logger.warn('未知的订阅类型', { type: subscriptionInfo.type });
                    return false;
            }

            if (result) {
                this.subscriptions.delete(subscriptionId);
                this.state.totalSubscriptions--;
                this.state.lastActivity = Date.now();

                this.logger.debug('取消订阅成功', { subscriptionId, type: subscriptionInfo.type });
            }

            return result;
        } catch (error) {
            this.logger.error('取消订阅失败', error, { subscriptionId });
            return false;
        }
    }

    /**
     * 获取事件监听器数量
     */
    getEventListenerCount(event: string): number {
        try {
            this.ensureEventBusActive();
            return this.eventBus.listenerCount(event);
        } catch (error) {
            this.logger.error('获取事件监听器数量失败', error, { event });
            return 0;
        }
    }

    /**
     * 获取通道订阅者数量
     */
    getChannelSubscriberCount(channelId: string): number {
        try {
            this.ensureMessageChannelActive();
            return this.messageChannel.getSubscriberCount(channelId);
        } catch (error) {
            this.logger.error('获取通道订阅者数量失败', error, { channelId });
            return 0;
        }
    }

    /**
     * 获取所有事件名称
     */
    getEventNames(): string[] {
        try {
            this.ensureEventBusActive();
            return this.eventBus.eventNames();
        } catch (error) {
            this.logger.error('获取事件名称失败', error);
            return [];
        }
    }

    /**
     * 获取所有通道ID
     */
    getChannelIds(): string[] {
        try {
            this.ensureMessageChannelActive();
            return this.messageChannel.getChannelIds();
        } catch (error) {
            this.logger.error('获取通道ID失败', error);
            return [];
        }
    }

    /**
     * 获取通信状态
     */
    getState(): CommunicationState {
        return { ...this.state };
    }

    /**
     * 获取性能指标
     */
    getMetrics(): CommunicationMetrics {
        const eventBusMetrics = this.config.enableEventBus ? this.eventBus.getMetrics() : null;
        const messageChannelMetrics = this.config.enableMessageChannel ? this.messageChannel.getMetrics() : null;

        return {
            eventBus: eventBusMetrics,
            messageChannel: messageChannelMetrics,
            totalSubscriptions: this.state.totalSubscriptions,
            totalChannels: this.state.totalChannels,
            lastActivity: this.state.lastActivity
        };
    }

    /**
     * 清理所有订阅和通道
     */
    clear(): void {
        try {
            // 清理事件总线
            if (this.config.enableEventBus) {
                this.eventBus.removeAllListeners();
            }

            // 清理消息通道
            if (this.config.enableMessageChannel) {
                this.messageChannel.clear();
            }

            // 清理订阅记录
            this.subscriptions.clear();

            // 重置状态
            this.state.totalSubscriptions = 0;
            this.state.totalChannels = 0;
            this.state.lastActivity = Date.now();

            this.logger.debug('通信管理器清理完成');
        } catch (error) {
            this.logger.error('通信管理器清理失败', error);
        }
    }

    /**
     * 销毁通信管理器
     */
    destroy(): void {
        try {
            // 清理资源
            this.clear();

            // 销毁事件总线
            if (this.config.enableEventBus) {
                this.eventBus.destroy();
            }

            // 销毁消息通道
            if (this.config.enableMessageChannel) {
                this.messageChannel.destroy();
            }

            // 重置状态
            this.state.initialized = false;
            this.state.eventBusActive = false;
            this.state.messageChannelActive = false;

            this.logger.info('通信管理器销毁完成');
        } catch (error) {
            this.logger.error('通信管理器销毁失败', error);
        }
    }

    /**
     * 设置通信桥接
     */
    private setupCommunicationBridge(): void {
        if (!this.config.enableEventBus || !this.config.enableMessageChannel) {
            return;
        }

        // 事件总线到消息通道的桥接
        this.eventBus.on('bridge:to-channel', (data: any) => {
            if (data.channelId && data.message) {
                this.messageChannel.send(data.channelId, data.message, data.options);
            }
        });

        // 消息通道到事件总线的桥接
        this.messageChannel.subscribe('bridge:to-event', (data: any) => {
            if (data.event && data.data !== undefined) {
                this.eventBus.emit(data.event, data.data);
            }
        });

        this.logger.debug('通信桥接设置完成');
    }

    /**
     * 确保管理器已初始化
     */
    private ensureInitialized(): void {
        if (!this.state.initialized) {
            throw createError('COMMUNICATION_MANAGER_NOT_INITIALIZED', '通信管理器未初始化');
        }
    }

    /**
     * 确保事件总线已激活
     */
    private ensureEventBusActive(): void {
        if (!this.state.eventBusActive) {
            throw createError('EVENT_BUS_NOT_ACTIVE', '事件总线未激活');
        }
    }

    /**
     * 确保消息通道已激活
     */
    private ensureMessageChannelActive(): void {
        if (!this.state.messageChannelActive) {
            throw createError('MESSAGE_CHANNEL_NOT_ACTIVE', '消息通道未激活');
        }
    }

    /**
     * 生成订阅ID
     */
    private generateSubscriptionId(type: string): string {
        return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}