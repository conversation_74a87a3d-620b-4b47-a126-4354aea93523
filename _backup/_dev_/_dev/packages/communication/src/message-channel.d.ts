/**
 * 消息通道系统实现
 *
 * @description 跨应用消息传递通道
 * <AUTHOR> <<EMAIL>>
 */
import type { MessageChannelConfig, MessageChannelMetrics, MessageChannelOptions, MessageHandler, MessageSubscription } from './types';
/**
 * 消息通道实现
 * 提供跨应用的消息传递能力
 */
export declare class MessageChannel {
    private readonly logger;
    private readonly config;
    private readonly channels;
    private readonly subscriptions;
    private readonly metrics;
    private readonly messageQueue;
    private isProcessingQueue;
    constructor(config?: MessageChannelConfig);
    /**
     * 创建通道
     */
    createChannel(channelId: string, options?: MessageChannelOptions): void;
    /**
     * 删除通道
     */
    deleteChannel(channelId: string): boolean;
    /**
     * 订阅通道消息
     */
    subscribe(channelId: string, handler: MessageHandler, options?: MessageChannelOptions): MessageSubscription;
    /**
     * 取消订阅
     */
    unsubscribe(subscription: MessageSubscription): boolean;
    unsubscribe(subscriptionId: string): boolean;
    /**
     * 发送消息
     */
    send(channelId: string, data: any, options?: MessageChannelOptions): Promise<boolean>;
    /**
     * 广播消息到所有通道
     */
    broadcast(data: any, options?: MessageChannelOptions): Promise<number>;
    /**
     * 获取通道信息
     */
    getChannelInfo(channelId: string): ChannelInfo | undefined;
    /**
     * 获取所有通道ID
     */
    getChannelIds(): string[];
    /**
     * 获取通道订阅者数量
     */
    getSubscriberCount(channelId: string): number;
    /**
     * 获取性能指标
     */
    getMetrics(): MessageChannelMetrics;
    /**
     * 清理所有通道和订阅
     */
    clear(): void;
    /**
     * 销毁消息通道
     */
    destroy(): void;
    /**
     * 验证通道ID
     */
    private validateChannelId;
    /**
     * 验证处理器
     */
    private validateHandler;
    /**
     * 生成订阅ID
     */
    private generateSubscriptionId;
    /**
     * 生成消息ID
     */
    private generateMessageId;
    /**
     * 将消息加入队列
     */
    private enqueueMessage;
    /**
     * 处理消息队列
     */
    private processMessageQueue;
    /**
     * 处理消息
     */
    private processMessage;
    /**
     * 执行处理器
     */
    private executeHandler;
    /**
     * 更新指标
     */
    private updateMetrics;
}
/**
 * 通道信息接口
 */
interface ChannelInfo {
    id: string;
    options: MessageChannelOptions;
    subscribers: Set<string>;
    messageCount: number;
    createdAt: number;
    lastActivity: number;
}
export {};
//# sourceMappingURL=message-channel.d.ts.map