/**
 * 事件总线系统实现
 * 
 * @description 高性能的发布订阅模式事件总线
 * <AUTHOR> <<EMAIL>>
 */

import { createError, createLogger } from '@micro-core/shared';
import type {
    EventBusConfig,
    EventBusMetrics,
    EventBusOptions,
    EventHandler,
    EventSubscription
} from './types';

/**
 * 事件总线实现
 * 提供高性能的发布订阅模式事件系统
 */
export class EventBus {
    private readonly logger: ReturnType<typeof createLogger>;
    private readonly config: EventBusConfig;
    private readonly listeners = new Map<string, Set<EventSubscription>>();
    private readonly onceListeners = new Map<string, Set<EventSubscription>>();
    private readonly wildcardListeners = new Set<EventSubscription>();
    private readonly metrics: EventBusMetrics;
    private readonly eventQueue: Array<{ event: string; data: any; timestamp: number }> = [];
    private isProcessingQueue = false;

    constructor(config: EventBusConfig = {}) {
        this.logger = createLogger('EventBus');
        this.config = {
            maxListeners: 100,
            enableMetrics: true,
            enableQueue: true,
            queueSize: 1000,
            enableWildcard: true,
            enableAsync: true,
            ...config
        };

        this.metrics = {
            totalEvents: 0,
            totalListeners: 0,
            eventsPerSecond: 0,
            averageProcessingTime: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };

        this.logger.info('事件总线初始化完成', { config: this.config });
    }

    /**
     * 订阅事件
     */
    on(event: string, handler: EventHandler, options: EventBusOptions = {}): EventSubscription {
        try {
            this.validateEventName(event);
            this.validateHandler(handler);

            const subscription: EventSubscription = {
                id: this.generateSubscriptionId(),
                event,
                handler,
                options,
                createdAt: Date.now(),
                callCount: 0,
                lastCalled: 0
            };

            // 检查监听器数量限制
            this.checkListenerLimit(event);

            // 添加到监听器集合
            if (!this.listeners.has(event)) {
                this.listeners.set(event, new Set());
            }
            this.listeners.get(event)!.add(subscription);

            // 更新指标
            this.metrics.totalListeners++;
            this.updateMetrics();

            this.logger.debug('事件订阅成功', {
                event,
                subscriptionId: subscription.id,
                totalListeners: this.metrics.totalListeners
            });

            return subscription;
        } catch (error) {
            this.logger.error('事件订阅失败', error, { event });
            throw createError('EVENT_SUBSCRIPTION_FAILED', `事件订阅失败: ${error.message}`, error);
        }
    }

    /**
     * 订阅事件（仅触发一次）
     */
    once(event: string, handler: EventHandler, options: EventBusOptions = {}): EventSubscription {
        try {
            this.validateEventName(event);
            this.validateHandler(handler);

            const subscription: EventSubscription = {
                id: this.generateSubscriptionId(),
                event,
                handler,
                options: { ...options, once: true },
                createdAt: Date.now(),
                callCount: 0,
                lastCalled: 0
            };

            // 检查监听器数量限制
            this.checkListenerLimit(event);

            // 添加到一次性监听器集合
            if (!this.onceListeners.has(event)) {
                this.onceListeners.set(event, new Set());
            }
            this.onceListeners.get(event)!.add(subscription);

            // 更新指标
            this.metrics.totalListeners++;
            this.updateMetrics();

            this.logger.debug('一次性事件订阅成功', {
                event,
                subscriptionId: subscription.id
            });

            return subscription;
        } catch (error) {
            this.logger.error('一次性事件订阅失败', error, { event });
            throw createError('EVENT_ONCE_SUBSCRIPTION_FAILED', `一次性事件订阅失败: ${error.message}`, error);
        }
    }

    /**
     * 取消订阅
     */
    off(subscription: EventSubscription): boolean;
    off(event: string, handler?: EventHandler): boolean;
    off(eventOrSubscription: string | EventSubscription, handler?: EventHandler): boolean {
        try {
            if (typeof eventOrSubscription === 'object') {
                // 通过订阅对象取消订阅
                return this.removeSubscription(eventOrSubscription);
            } else {
                // 通过事件名和处理器取消订阅
                return this.removeByEventAndHandler(eventOrSubscription, handler);
            }
        } catch (error) {
            this.logger.error('取消订阅失败', error);
            return false;
        }
    }

    /**
     * 发布事件
     */
    emit(event: string, data?: any): Promise<boolean> {
        try {
            this.validateEventName(event);

            const startTime = Date.now();

            // 如果启用队列，将事件加入队列
            if (this.config.enableQueue) {
                this.enqueueEvent(event, data);
                this.processEventQueue();
                return Promise.resolve(true);
            }

            // 直接处理事件
            return this.processEvent(event, data, startTime);
        } catch (error) {
            this.logger.error('事件发布失败', error, { event });
            this.metrics.errorCount++;
            return Promise.resolve(false);
        }
    }

    /**
     * 同步发布事件
     */
    emitSync(event: string, data?: any): boolean {
        try {
            this.validateEventName(event);

            const startTime = Date.now();
            const listeners = this.getEventListeners(event);

            if (listeners.length === 0) {
                return false;
            }

            // 同步执行所有监听器
            for (const subscription of listeners) {
                try {
                    this.executeHandler(subscription, data);
                } catch (error) {
                    this.logger.error('监听器执行失败', error, {
                        event,
                        subscriptionId: subscription.id
                    });
                    this.metrics.errorCount++;
                }
            }

            // 更新指标
            this.updateEventMetrics(startTime, listeners.length);

            return true;
        } catch (error) {
            this.logger.error('同步事件发布失败', error, { event });
            this.metrics.errorCount++;
            return false;
        }
    }

    /**
     * 获取事件监听器数量
     */
    listenerCount(event: string): number {
        const regularListeners = this.listeners.get(event)?.size || 0;
        const onceListeners = this.onceListeners.get(event)?.size || 0;
        return regularListeners + onceListeners;
    }

    /**
     * 获取所有事件名称
     */
    eventNames(): string[] {
        const events = new Set<string>();

        for (const event of this.listeners.keys()) {
            events.add(event);
        }

        for (const event of this.onceListeners.keys()) {
            events.add(event);
        }

        return Array.from(events);
    }

    /**
     * 移除所有监听器
     */
    removeAllListeners(event?: string): void {
        try {
            if (event) {
                // 移除指定事件的所有监听器
                const regularCount = this.listeners.get(event)?.size || 0;
                const onceCount = this.onceListeners.get(event)?.size || 0;

                this.listeners.delete(event);
                this.onceListeners.delete(event);

                this.metrics.totalListeners -= (regularCount + onceCount);
            } else {
                // 移除所有监听器
                this.listeners.clear();
                this.onceListeners.clear();
                this.wildcardListeners.clear();
                this.metrics.totalListeners = 0;
            }

            this.updateMetrics();
            this.logger.debug('监听器移除完成', { event, totalListeners: this.metrics.totalListeners });
        } catch (error) {
            this.logger.error('移除监听器失败', error, { event });
        }
    }

    /**
     * 获取性能指标
     */
    getMetrics(): EventBusMetrics {
        this.updateMetrics();
        return { ...this.metrics };
    }

    /**
     * 销毁事件总线
     */
    destroy(): void {
        try {
            // 清理所有监听器
            this.removeAllListeners();

            // 清理事件队列
            this.eventQueue.length = 0;

            this.logger.info('事件总线销毁完成');
        } catch (error) {
            this.logger.error('事件总线销毁失败', error);
        }
    }

    /**
     * 验证事件名称
     */
    private validateEventName(event: string): void {
        if (!event || typeof event !== 'string') {
            throw createError('INVALID_EVENT_NAME', '事件名称必须是非空字符串');
        }
    }

    /**
     * 验证处理器
     */
    private validateHandler(handler: EventHandler): void {
        if (typeof handler !== 'function') {
            throw createError('INVALID_EVENT_HANDLER', '事件处理器必须是函数');
        }
    }

    /**
     * 检查监听器数量限制
     */
    private checkListenerLimit(event: string): void {
        const currentCount = this.listenerCount(event);
        if (currentCount >= this.config.maxListeners!) {
            throw createError('MAX_LISTENERS_EXCEEDED', `事件 ${event} 的监听器数量超过限制: ${this.config.maxListeners}`);
        }
    }

    /**
     * 生成订阅ID
     */
    private generateSubscriptionId(): string {
        return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 移除订阅
     */
    private removeSubscription(subscription: EventSubscription): boolean {
        const { event } = subscription;

        // 从常规监听器中移除
        const regularListeners = this.listeners.get(event);
        if (regularListeners?.has(subscription)) {
            regularListeners.delete(subscription);
            if (regularListeners.size === 0) {
                this.listeners.delete(event);
            }
            this.metrics.totalListeners--;
            return true;
        }

        // 从一次性监听器中移除
        const onceListeners = this.onceListeners.get(event);
        if (onceListeners?.has(subscription)) {
            onceListeners.delete(subscription);
            if (onceListeners.size === 0) {
                this.onceListeners.delete(event);
            }
            this.metrics.totalListeners--;
            return true;
        }

        return false;
    }

    /**
     * 通过事件名和处理器移除订阅
     */
    private removeByEventAndHandler(event: string, handler?: EventHandler): boolean {
        let removed = false;

        if (handler) {
            // 移除指定处理器
            removed = this.removeHandlerFromListeners(event, handler, this.listeners) || removed;
            removed = this.removeHandlerFromListeners(event, handler, this.onceListeners) || removed;
        } else {
            // 移除所有处理器
            const regularCount = this.listeners.get(event)?.size || 0;
            const onceCount = this.onceListeners.get(event)?.size || 0;

            this.listeners.delete(event);
            this.onceListeners.delete(event);

            this.metrics.totalListeners -= (regularCount + onceCount);
            removed = (regularCount + onceCount) > 0;
        }

        return removed;
    }

    /**
     * 从监听器集合中移除处理器
     */
    private removeHandlerFromListeners(
        event: string,
        handler: EventHandler,
        listenersMap: Map<string, Set<EventSubscription>>
    ): boolean {
        const listeners = listenersMap.get(event);
        if (!listeners) {
            return false;
        }

        let removed = false;
        for (const subscription of listeners) {
            if (subscription.handler === handler) {
                listeners.delete(subscription);
                this.metrics.totalListeners--;
                removed = true;
            }
        }

        if (listeners.size === 0) {
            listenersMap.delete(event);
        }

        return removed;
    }

    /**
     * 获取事件监听器
     */
    private getEventListeners(event: string): EventSubscription[] {
        const listeners: EventSubscription[] = [];

        // 添加常规监听器
        const regularListeners = this.listeners.get(event);
        if (regularListeners) {
            listeners.push(...regularListeners);
        }

        // 添加一次性监听器
        const onceListeners = this.onceListeners.get(event);
        if (onceListeners) {
            listeners.push(...onceListeners);
        }

        // 添加通配符监听器
        if (this.config.enableWildcard) {
            for (const subscription of this.wildcardListeners) {
                if (this.matchWildcard(subscription.event, event)) {
                    listeners.push(subscription);
                }
            }
        }

        return listeners;
    }

    /**
     * 匹配通配符
     */
    private matchWildcard(pattern: string, event: string): boolean {
        // 简单的通配符匹配实现
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(event);
    }

    /**
     * 将事件加入队列
     */
    private enqueueEvent(event: string, data: any): void {
        if (this.eventQueue.length >= this.config.queueSize!) {
            // 队列满了，移除最旧的事件
            this.eventQueue.shift();
        }

        this.eventQueue.push({
            event,
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 处理事件队列
     */
    private async processEventQueue(): Promise<void> {
        if (this.isProcessingQueue || this.eventQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        try {
            while (this.eventQueue.length > 0) {
                const queuedEvent = this.eventQueue.shift();
                if (queuedEvent) {
                    await this.processEvent(queuedEvent.event, queuedEvent.data, queuedEvent.timestamp);
                }
            }
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * 处理事件
     */
    private async processEvent(event: string, data: any, startTime: number): Promise<boolean> {
        const listeners = this.getEventListeners(event);

        if (listeners.length === 0) {
            return false;
        }

        // 执行监听器
        if (this.config.enableAsync) {
            await this.executeListenersAsync(listeners, data);
        } else {
            this.executeListenersSync(listeners, data);
        }

        // 移除一次性监听器
        this.removeOnceListeners(event, listeners);

        // 更新指标
        this.updateEventMetrics(startTime, listeners.length);

        return true;
    }

    /**
     * 异步执行监听器
     */
    private async executeListenersAsync(listeners: EventSubscription[], data: any): Promise<void> {
        const promises = listeners.map(subscription =>
            this.executeHandlerSafely(subscription, data)
        );

        await Promise.allSettled(promises);
    }

    /**
     * 同步执行监听器
     */
    private executeListenersSync(listeners: EventSubscription[], data: any): void {
        for (const subscription of listeners) {
            this.executeHandlerSafely(subscription, data);
        }
    }

    /**
     * 安全执行处理器
     */
    private async executeHandlerSafely(subscription: EventSubscription, data: any): Promise<void> {
        try {
            await this.executeHandler(subscription, data);
        } catch (error) {
            this.logger.error('监听器执行失败', error, {
                event: subscription.event,
                subscriptionId: subscription.id
            });
            this.metrics.errorCount++;
        }
    }

    /**
     * 执行处理器
     */
    private async executeHandler(subscription: EventSubscription, data: any): Promise<void> {
        subscription.callCount++;
        subscription.lastCalled = Date.now();

        if (subscription.options.async !== false) {
            await subscription.handler(data);
        } else {
            subscription.handler(data);
        }
    }

    /**
     * 移除一次性监听器
     */
    private removeOnceListeners(event: string, listeners: EventSubscription[]): void {
        const onceListeners = this.onceListeners.get(event);
        if (!onceListeners) {
            return;
        }

        for (const subscription of listeners) {
            if (subscription.options.once && onceListeners.has(subscription)) {
                onceListeners.delete(subscription);
                this.metrics.totalListeners--;
            }
        }

        if (onceListeners.size === 0) {
            this.onceListeners.delete(event);
        }
    }

    /**
     * 更新事件指标
     */
    private updateEventMetrics(startTime: number, listenerCount: number): void {
        this.metrics.totalEvents++;

        const processingTime = Date.now() - startTime;
        this.metrics.averageProcessingTime =
            (this.metrics.averageProcessingTime + processingTime) / 2;

        this.updateMetrics();
    }

    /**
     * 更新指标
     */
    private updateMetrics(): void {
        const now = Date.now();
        const timeDiff = (now - this.metrics.lastUpdated) / 1000;

        if (timeDiff > 0) {
            this.metrics.eventsPerSecond = this.metrics.totalEvents / timeDiff;
        }

        this.metrics.lastUpdated = now;
    }
}