/**
 * 微前端应用间通信系统
 * 
 * @description 提供事件总线、消息通道、状态同步等通信能力
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// ============= 核心通信组件 =============
export { CommunicationManager } from './communication-manager';
export { EventBus } from './event-bus';
export { MessageChannel } from './message-channel';
export { StateSync } from './state-sync';

// ============= 通信协议 =============
export { Protocol as CommunicationProtocol } from './protocol';
export { Serializer as MessageSerializer } from './serializer';

// ============= 类型定义 =============
export type {
    CommunicationConfig, CommunicationEvent, CommunicationPermission, EventBusConfig,
    MessageChannelConfig, MessagePayload,
    StateChangeEvent
} from '@micro-core/shared';

// ============= 工厂函数 =============
export {
    createCommunicationManager, createEventBus,
    createMessageChannel, createStateSync
} from './factories';

// ============= 版本信息 =============
export const VERSION = '0.1.0';
export const PACKAGE_NAME = '@micro-core/communication';