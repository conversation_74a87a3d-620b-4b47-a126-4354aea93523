/**
 * 通信协议实现
 * 
 * @description 标准化的通信协议定义和处理
 * <AUTHOR> <<EMAIL>>
 */

import type {
    MessageType,
    ProtocolConfig,
    ProtocolHandler,
    ProtocolMessage,
    ProtocolMetrics,
    ProtocolVersion
} from '@micro-core/shared';
import { createError, Logger } from '@micro-core/shared';

/**
 * 通信协议处理器
 * 提供标准化的消息协议处理
 */
export class Protocol {
    private readonly logger: Logger;
    private readonly config: ProtocolConfig;
    private readonly handlers = new Map<MessageType, Set<ProtocolHandler>>();
    private readonly metrics: ProtocolMetrics;
    private readonly messageQueue: ProtocolMessage[] = [];
    private isProcessingQueue = false;

    constructor(config: ProtocolConfig = {}) {
        this.logger = new Logger('Protocol');
        this.config = {
            version: '1.0.0' as ProtocolVersion,
            enableValidation: true,
            enableQueue: true,
            queueSize: 1000,
            enableCompression: false,
            enableEncryption: false,
            maxMessageSize: 1024 * 1024, // 1MB
            ...config
        };

        this.metrics = {
            totalMessages: 0,
            validMessages: 0,
            invalidMessages: 0,
            processedMessages: 0,
            queuedMessages: 0,
            averageProcessingTime: 0,
            errorCount: 0,
            lastUpdated: Date.now()
        };

        this.logger.info('通信协议初始化完成', {
            version: this.config.version,
            config: this.config
        });
    }

    /**
     * 注册消息处理器
     */
    registerHandler(messageType: MessageType, handler: ProtocolHandler): string {
        try {
            this.validateMessageType(messageType);
            this.validateHandler(handler);

            if (!this.handlers.has(messageType)) {
                this.handlers.set(messageType, new Set());
            }

            const handlerId = this.generateHandlerId();
            const handlerWrapper = this.createHandlerWrapper(handlerId, handler);

            this.handlers.get(messageType)!.add(handlerWrapper);

            this.logger.debug('消息处理器注册成功', {
                messageType,
                handlerId,
                totalHandlers: this.handlers.get(messageType)!.size
            });

            return handlerId;
        } catch (error) {
            this.logger.error('消息处理器注册失败', error, { messageType });
            throw createError('HANDLER_REGISTRATION_FAILED', `消息处理器注册失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 注销消息处理器
     */
    unregisterHandler(messageType: MessageType, handlerId: string): boolean {
        try {
            const handlers = this.handlers.get(messageType);
            if (!handlers) {
                return false;
            }

            for (const handler of handlers) {
                if (handler.id === handlerId) {
                    handlers.delete(handler);
                    if (handlers.size === 0) {
                        this.handlers.delete(messageType);
                    }

                    this.logger.debug('消息处理器注销成功', {
                        messageType,
                        handlerId,
                        remainingHandlers: handlers.size
                    });

                    return true;
                }
            }

            return false;
        } catch (error) {
            this.logger.error('消息处理器注销失败', error, { messageType, handlerId });
            return false;
        }
    }

    /**
     * 创建协议消息
     */
    createMessage(
        type: MessageType,
        payload: any,
        options: {
            target?: string;
            source?: string;
            priority?: number;
            timeout?: number;
            metadata?: Record<string, any>;
        } = {}
    ): ProtocolMessage {
        try {
            this.validateMessageType(type);

            const message: ProtocolMessage = {
                id: this.generateMessageId(),
                version: this.config.version!,
                type,
                payload,
                timestamp: Date.now(),
                source: options.source || 'unknown',
                target: options.target,
                priority: options.priority || 0,
                timeout: options.timeout,
                metadata: options.metadata || {},
                checksum: this.calculateChecksum(payload)
            };

            // 验证消息
            if (this.config.enableValidation) {
                this.validateMessage(message);
            }

            this.logger.debug('协议消息创建成功', {
                messageId: message.id,
                type: message.type,
                source: message.source,
                target: message.target
            });

            return message;
        } catch (error) {
            this.logger.error('协议消息创建失败', error, { type });
            throw createError('MESSAGE_CREATION_FAILED', `协议消息创建失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 处理协议消息
     */
    async processMessage(message: ProtocolMessage): Promise<boolean> {
        try {
            this.metrics.totalMessages++;

            // 验证消息
            if (this.config.enableValidation) {
                const isValid = this.validateMessage(message);
                if (!isValid) {
                    this.metrics.invalidMessages++;
                    return false;
                }
            }

            this.metrics.validMessages++;

            // 如果启用队列，将消息加入队列
            if (this.config.enableQueue) {
                this.enqueueMessage(message);
                this.processMessageQueue();
                return true;
            }

            // 直接处理消息
            return await this.handleMessage(message);
        } catch (error) {
            this.logger.error('处理协议消息失败', error, {
                messageId: message.id,
                type: message.type
            });
            this.metrics.errorCount++;
            return false;
        }
    }

    /**
     * 批量处理消息
     */
    async processMessages(messages: ProtocolMessage[]): Promise<boolean[]> {
        try {
            const results = await Promise.all(
                messages.map(message => this.processMessage(message))
            );

            this.logger.debug('批量处理消息完成', {
                totalMessages: messages.length,
                successCount: results.filter(r => r).length
            });

            return results;
        } catch (error) {
            this.logger.error('批量处理消息失败', error);
            return messages.map(() => false);
        }
    }

    /**
     * 序列化消息
     */
    serializeMessage(message: ProtocolMessage): string {
        try {
            // 压缩处理
            let serialized = JSON.stringify(message);

            if (this.config.enableCompression && serialized.length > 1000) {
                // 这里可以添加压缩逻辑
                // serialized = compress(serialized);
            }

            // 加密处理
            if (this.config.enableEncryption) {
                // 这里可以添加加密逻辑
                // serialized = encrypt(serialized);
            }

            return serialized;
        } catch (error) {
            this.logger.error('消息序列化失败', error, { messageId: message.id });
            throw createError('MESSAGE_SERIALIZATION_FAILED', `消息序列化失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 反序列化消息
     */
    deserializeMessage(data: string): ProtocolMessage {
        try {
            let deserializedData = data;

            // 解密处理
            if (this.config.enableEncryption) {
                // 这里可以添加解密逻辑
                // deserializedData = decrypt(deserializedData);
            }

            // 解压缩处理
            if (this.config.enableCompression) {
                // 这里可以添加解压缩逻辑
                // deserializedData = decompress(deserializedData);
            }

            const message: ProtocolMessage = JSON.parse(deserializedData);

            // 验证反序列化的消息
            if (this.config.enableValidation) {
                this.validateMessage(message);
            }

            return message;
        } catch (error) {
            this.logger.error('消息反序列化失败', error);
            throw createError('MESSAGE_DESERIALIZATION_FAILED', `消息反序列化失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 获取消息处理器数量
     */
    getHandlerCount(messageType?: MessageType): number {
        if (messageType) {
            return this.handlers.get(messageType)?.size || 0;
        }

        let total = 0;
        for (const handlers of this.handlers.values()) {
            total += handlers.size;
        }
        return total;
    }

    /**
     * 获取支持的消息类型
     */
    getSupportedMessageTypes(): MessageType[] {
        return Array.from(this.handlers.keys());
    }

    /**
     * 获取性能指标
     */
    getMetrics(): ProtocolMetrics {
        this.updateMetrics();
        return { ...this.metrics };
    }

    /**
     * 清理所有处理器和队列
     */
    clear(): void {
        try {
            this.handlers.clear();
            this.messageQueue.length = 0;

            this.logger.debug('协议处理器清理完成');
        } catch (error) {
            this.logger.error('协议处理器清理失败', error);
        }
    }

    /**
     * 销毁协议处理器
     */
    destroy(): void {
        try {
            this.clear();
            this.logger.info('协议处理器销毁完成');
        } catch (error) {
            this.logger.error('协议处理器销毁失败', error);
        }
    }

    /**
     * 验证消息类型
     */
    private validateMessageType(messageType: MessageType): void {
        if (!messageType || typeof messageType !== 'string') {
            throw createError('INVALID_MESSAGE_TYPE', '消息类型必须是非空字符串');
        }
    }

    /**
     * 验证处理器
     */
    private validateHandler(handler: ProtocolHandler): void {
        if (typeof handler !== 'function') {
            throw createError('INVALID_PROTOCOL_HANDLER', '协议处理器必须是函数');
        }
    }

    /**
     * 验证消息
     */
    private validateMessage(message: ProtocolMessage): boolean {
        try {
            // 检查必需字段
            if (!message.id || !message.version || !message.type || !message.timestamp) {
                throw createError('INVALID_MESSAGE_FORMAT', '消息格式无效：缺少必需字段');
            }

            // 检查版本兼容性
            if (message.version !== this.config.version) {
                this.logger.warn('消息版本不匹配', {
                    messageVersion: message.version,
                    protocolVersion: this.config.version
                });
            }

            // 检查消息大小
            const messageSize = JSON.stringify(message).length;
            if (messageSize > this.config.maxMessageSize!) {
                throw createError('MESSAGE_TOO_LARGE', `消息大小超过限制: ${messageSize} > ${this.config.maxMessageSize}`);
            }

            // 检查校验和
            if (message.checksum) {
                const calculatedChecksum = this.calculateChecksum(message.payload);
                if (message.checksum !== calculatedChecksum) {
                    throw createError('CHECKSUM_MISMATCH', '消息校验和不匹配');
                }
            }

            // 检查超时
            if (message.timeout && Date.now() > message.timestamp + message.timeout) {
                throw createError('MESSAGE_TIMEOUT', '消息已超时');
            }

            return true;
        } catch (error) {
            this.logger.error('消息验证失败', error, {
                messageId: message.id,
                type: message.type
            });
            return false;
        }
    }

    /**
     * 计算校验和
     */
    private calculateChecksum(payload: any): string {
        try {
            const data = JSON.stringify(payload);
            // 简单的校验和计算（实际应用中应使用更强的算法）
            let hash = 0;
            for (let i = 0; i < data.length; i++) {
                const char = data.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return hash.toString(16);
        } catch (error) {
            this.logger.error('计算校验和失败', error);
            return '';
        }
    }

    /**
     * 生成处理器ID
     */
    private generateHandlerId(): string {
        return `handler_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 生成消息ID
     */
    private generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 创建处理器包装器
     */
    private createHandlerWrapper(handlerId: string, handler: ProtocolHandler): ProtocolHandler & { id: string } {
        const wrapper = async (message: ProtocolMessage) => {
            try {
                return await handler(message);
            } catch (error) {
                this.logger.error('处理器执行失败', error, {
                    handlerId,
                    messageId: message.id,
                    messageType: message.type
                });
                throw error;
            }
        };

        (wrapper as any).id = handlerId;
        return wrapper as ProtocolHandler & { id: string };
    }

    /**
     * 将消息加入队列
     */
    private enqueueMessage(message: ProtocolMessage): void {
        if (this.messageQueue.length >= this.config.queueSize!) {
            // 队列满了，移除最旧的消息
            this.messageQueue.shift();
        }

        this.messageQueue.push(message);
        this.metrics.queuedMessages++;
    }

    /**
     * 处理消息队列
     */
    private async processMessageQueue(): Promise<void> {
        if (this.isProcessingQueue || this.messageQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        try {
            while (this.messageQueue.length > 0) {
                const message = this.messageQueue.shift();
                if (message) {
                    await this.handleMessage(message);
                    this.metrics.queuedMessages--;
                }
            }
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * 处理消息
     */
    private async handleMessage(message: ProtocolMessage): Promise<boolean> {
        const startTime = Date.now();

        try {
            const handlers = this.handlers.get(message.type);
            if (!handlers || handlers.size === 0) {
                this.logger.warn('没有找到消息处理器', {
                    messageId: message.id,
                    type: message.type
                });
                return false;
            }

            // 按优先级排序处理器（如果有的话）
            const sortedHandlers = Array.from(handlers).sort((a, b) => {
                const aPriority = (a as any).priority || 0;
                const bPriority = (b as any).priority || 0;
                return bPriority - aPriority;
            });

            // 执行所有处理器
            const results = await Promise.allSettled(
                sortedHandlers.map(handler => handler(message))
            );

            const successCount = results.filter(result => result.status === 'fulfilled').length;
            const hasSuccess = successCount > 0;

            if (hasSuccess) {
                this.metrics.processedMessages++;
            }

            // 更新处理时间指标
            const processingTime = Date.now() - startTime;
            this.metrics.averageProcessingTime =
                (this.metrics.averageProcessingTime + processingTime) / 2;

            this.logger.debug('消息处理完成', {
                messageId: message.id,
                type: message.type,
                handlerCount: handlers.size,
                successCount,
                processingTime
            });

            return hasSuccess;
        } catch (error) {
            this.logger.error('处理消息失败', error, {
                messageId: message.id,
                type: message.type
            });
            this.metrics.errorCount++;
            return false;
        }
    }

    /**
     * 更新指标
     */
    private updateMetrics(): void {
        this.metrics.lastUpdated = Date.now();
    }
}