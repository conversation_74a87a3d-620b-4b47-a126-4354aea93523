/**
 * 微前端应用间通信系统
 *
 * @description 提供事件总线、消息通道、状态同步等通信能力
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
export { CommunicationManager } from './communication-manager';
export { EventBus } from './event-bus';
export { MessageChannel } from './message-channel';
export { StateSync } from './state-sync';
export { Protocol as CommunicationProtocol } from './protocol';
export { Serializer as MessageSerializer } from './serializer';
export type { CommunicationConfig, CommunicationEvent, CommunicationPermission, EventBusConfig, MessageChannelConfig, MessagePayload, StateChangeEvent } from '@micro-core/shared';
export { createCommunicationManager, createEventBus, createMessageChannel, createStateSync } from './factories';
export declare const VERSION = "0.1.0";
export declare const PACKAGE_NAME = "@micro-core/communication";
//# sourceMappingURL=index.d.ts.map