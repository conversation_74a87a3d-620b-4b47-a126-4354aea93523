/**
 * 通信系统工厂函数
 * 
 * @description 提供便捷的通信组件创建方法
 * <AUTHOR> <<EMAIL>>
 */

import { CommunicationManager } from './communication-manager';
import { EventBus } from './event-bus';
import { MessageChannel } from './message-channel';
import { Protocol } from './protocol';
import { Serializer } from './serializer';
import { StateSync } from './state-sync';
import type {
    CommunicationConfig,
    EventBusConfig,
    MessageChannelConfig,
    ProtocolConfig,
    SerializerConfig,
    StateSyncConfig
} from './types';

/**
 * 创建事件总线实例
 */
export function createEventBus(config?: EventBusConfig): EventBus {
    return new EventBus(config);
}

/**
 * 创建消息通道实例
 */
export function createMessageChannel(config?: MessageChannelConfig): MessageChannel {
    return new MessageChannel(config);
}

/**
 * 创建通信管理器实例
 */
export function createCommunicationManager(config?: CommunicationConfig): CommunicationManager {
    return new CommunicationManager(config);
}

/**
 * 创建状态同步器实例
 */
export function createStateSync(config?: StateSyncConfig): StateSync {
    return new StateSync(config);
}

/**
 * 创建协议处理器实例
 */
export function createProtocol(config?: ProtocolConfig): Protocol {
    return new Protocol(config);
}

/**
 * 创建序列化器实例
 */
export function createSerializer(config?: SerializerConfig): Serializer {
    return new Serializer(config);
}

/**
 * 创建完整的通信系统
 */
export function createCommunicationSystem(config: CommunicationConfig = {}) {
    const manager = createCommunicationManager(config);
    const eventBus = createEventBus(config.eventBus);
    const messageChannel = createMessageChannel(config.messageChannel);
    const stateSync = createStateSync(config.stateSync);
    const protocol = createProtocol(config.protocol);
    const serializer = createSerializer(config.serializer);

    return {
        manager,
        eventBus,
        messageChannel,
        stateSync,
        protocol,
        serializer
    };
}

/**
 * 创建默认通信系统
 * 使用推荐的默认配置
 */
export function createDefaultCommunicationSystem() {
    return createCommunicationSystem({
        enableEventBus: true,
        enableMessageChannel: true,
        enableMetrics: true,
        enablePersistence: false,
        enableEncryption: false,
        eventBus: {
            maxListeners: 100,
            enableAsync: true,
            enableMetrics: true
        },
        messageChannel: {
            maxChannels: 50,
            maxSubscriptions: 200,
            enableQueue: true,
            queueSize: 1000
        },
        stateSync: {
            enableConflictResolution: true,
            enableQueue: true,
            queueSize: 1000,
            syncInterval: 100
        },
        protocol: {
            version: '1.0.0',
            enableValidation: true,
            enableQueue: true,
            queueSize: 1000
        },
        serializer: {
            defaultFormat: 'json',
            enableCompression: false,
            enableValidation: true,
            maxSize: 10 * 1024 * 1024
        }
    });
}