{"name": "@micro-core/plugin-sandbox-federation", "version": "0.1.0", "description": "联邦组件沙箱插件，基于模块联邦实现的沙箱策略", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest"}, "keywords": ["micro-frontend", "sandbox", "federation", "module-federation", "plugin", "微前端"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"typescript": "^5.3.3", "vitest": "^3.2.4"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-sandbox-federation"}, "publishConfig": {"access": "public"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}