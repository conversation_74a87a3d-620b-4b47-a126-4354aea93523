import type { FederationSandboxOptions, RemoteEntry, SharedScope } from './types';

/**
 * 联邦组件沙箱实现
 * 基于模块联邦提供组件共享和动态加载
 */
export class FederationSandbox {
    private name: string;
    private options: FederationSandboxOptions;
    private isActive = false;
    private moduleCache = new Map<string, any>();
    private sharedScope: SharedScope = {};
    private remoteEntries = new Map<string, RemoteEntry>();

    constructor(name: string, options: FederationSandboxOptions = {}) {
        this.name = name;
        this.options = {
            enableLogging: false,
            enableSharedScope: true,
            enableModuleCache: true,
            sharedDependencies: [],
            ...options
        };

        this.initializeSharedScope();
        this.initializeRemoteEntry();
    }

    /**
     * 初始化共享作用域
     */
    private initializeSharedScope() {
        if (!this.options.enableSharedScope) return;

        // 初始化共享依赖
        for (const dep of this.options.sharedDependencies || []) {
            try {
                // 尝试从全局获取共享依赖
                const sharedModule = this.getGlobalSharedModule(dep);
                if (sharedModule) {
                    this.sharedScope[dep] = {
                        version: sharedModule.version || '1.0.0',
                        get: () => sharedModule,
                        loaded: true
                    };
                    this.log(`共享依赖 ${dep} 初始化成功`);
                }
            } catch (error) {
                this.log(`共享依赖 ${dep} 初始化失败: ${error}`);
            }
        }
    }

    /**
     * 获取全局共享模块
     */
    private getGlobalSharedModule(name: string): any {
        // 尝试从不同的全局位置获取模块
        const globalLocations = [
            () => (window as any)[name],
            () => (window as any).__webpack_share_scopes__?.[name],
            () => (window as any).__SHARED_MODULES__?.[name]
        ];

        for (const getModule of globalLocations) {
            try {
                const module = getModule();
                if (module) return module;
            } catch (error) {
                // 忽略错误，继续尝试下一个位置
            }
        }

        return null;
    }

    /**
     * 初始化远程入口
     */
    private initializeRemoteEntry() {
        if (!this.options.remoteEntry) return;

        const entry: RemoteEntry = {
            url: this.options.remoteEntry,
            name: this.name,
            loaded: false,
            modules: new Map()
        };

        this.remoteEntries.set(this.name, entry);
        this.log(`远程入口 ${this.options.remoteEntry} 初始化完成`);
    }

    /**
     * 激活沙箱
     */
    activate() {
        if (this.isActive) {
            this.log('沙箱已经激活，跳过重复激活');
            return;
        }

        try {
            this.setupModuleFederation();
            this.isActive = true;
            this.log('沙箱激活成功');
        } catch (error) {
            console.error(`[FederationSandbox:${this.name}] 激活沙箱失败:`, error);
            throw error;
        }
    }

    /**
     * 停用沙箱
     */
    deactivate() {
        if (!this.isActive) {
            this.log('沙箱未激活，跳过停用');
            return;
        }

        try {
            this.cleanupModuleFederation();
            this.isActive = false;
            this.log('沙箱停用成功');
        } catch (error) {
            console.error(`[FederationSandbox:${this.name}] 停用沙箱失败:`, error);
            throw error;
        }
    }

    /**
     * 销毁沙箱
     */
    destroy() {
        this.deactivate();

        // 清理资源
        this.moduleCache.clear();
        this.remoteEntries.clear();
        this.sharedScope = {};

        this.log('沙箱销毁完成');
    }

    /**
     * 设置模块联邦
     */
    private setupModuleFederation() {
        // 设置全局共享作用域
        if (this.options.enableSharedScope) {
            this.setupGlobalSharedScope();
        }

        // 设置模块加载器
        this.setupModuleLoader();
    }

    /**
     * 设置全局共享作用域
     */
    private setupGlobalSharedScope() {
        const globalSharedScope = (window as any).__webpack_share_scopes__ || {};

        // 合并共享作用域
        for (const [name, module] of Object.entries(this.sharedScope)) {
            if (!globalSharedScope[name]) {
                globalSharedScope[name] = module;
            }
        }

        (window as any).__webpack_share_scopes__ = globalSharedScope;
        this.log('全局共享作用域设置完成');
    }

    /**
     * 设置模块加载器
     */
    private setupModuleLoader() {
        // 创建自定义的模块加载器
        const originalImport = (window as any).__webpack_require__;

        (window as any).__webpack_require__ = (moduleId: string) => {
            // 首先检查缓存
            if (this.moduleCache.has(moduleId)) {
                return this.moduleCache.get(moduleId);
            }

            // 尝试从共享作用域加载
            const sharedModule = this.loadFromSharedScope(moduleId);
            if (sharedModule) {
                this.moduleCache.set(moduleId, sharedModule);
                return sharedModule;
            }

            // 回退到原始加载器
            if (originalImport) {
                return originalImport(moduleId);
            }

            throw new Error(`模块 ${moduleId} 无法加载`);
        };

        this.log('模块加载器设置完成');
    }

    /**
     * 从共享作用域加载模块
     */
    private loadFromSharedScope(moduleId: string): any {
        const sharedModule = this.sharedScope[moduleId];
        if (sharedModule && sharedModule.loaded) {
            return sharedModule.get();
        }
        return null;
    }

    /**
     * 清理模块联邦
     */
    private cleanupModuleFederation() {
        // 清理全局共享作用域中的模块
        const globalSharedScope = (window as any).__webpack_share_scopes__;
        if (globalSharedScope) {
            for (const name of Object.keys(this.sharedScope)) {
                delete globalSharedScope[name];
            }
        }

        this.log('模块联邦清理完成');
    }

    /**
     * 加载远程模块
     */
    async loadRemoteModule(moduleName: string): Promise<any> {
        if (!this.isActive) {
            throw new Error('沙箱未激活，无法加载远程模块');
        }

        try {
            // 检查缓存
            if (this.options.enableModuleCache && this.moduleCache.has(moduleName)) {
                this.log(`从缓存加载模块 ${moduleName}`);
                return this.moduleCache.get(moduleName);
            }

            // 加载远程模块
            const module = await this.fetchRemoteModule(moduleName);

            // 缓存模块
            if (this.options.enableModuleCache) {
                this.moduleCache.set(moduleName, module);
            }

            this.log(`远程模块 ${moduleName} 加载成功`);
            return module;
        } catch (error) {
            console.error(`[FederationSandbox:${this.name}] 加载远程模块 ${moduleName} 失败:`, error);
            throw error;
        }
    }

    /**
     * 获取远程模块
     */
    private async fetchRemoteModule(moduleName: string): Promise<any> {
        const entry = this.remoteEntries.get(this.name);
        if (!entry) {
            throw new Error(`远程入口 ${this.name} 不存在`);
        }

        // 如果入口未加载，先加载入口
        if (!entry.loaded) {
            await this.loadRemoteEntry(entry);
        }

        // 从入口获取模块
        const module = entry.modules.get(moduleName);
        if (!module) {
            throw new Error(`模块 ${moduleName} 在远程入口中不存在`);
        }

        return module;
    }

    /**
     * 加载远程入口
     */
    private async loadRemoteEntry(entry: RemoteEntry): Promise<void> {
        try {
            // 动态加载远程入口脚本
            const script = document.createElement('script');
            script.src = entry.url;
            script.type = 'text/javascript';

            await new Promise((resolve, reject) => {
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });

            // 获取远程容器
            const container = (window as any)[entry.name];
            if (!container) {
                throw new Error(`远程容器 ${entry.name} 不存在`);
            }

            // 初始化容器
            await container.init(this.sharedScope);

            // 获取可用模块
            const modules = await container.get('./');
            entry.modules = new Map(Object.entries(modules));
            entry.loaded = true;

            this.log(`远程入口 ${entry.url} 加载成功`);
        } catch (error) {
            console.error(`[FederationSandbox:${this.name}] 加载远程入口失败:`, error);
            throw error;
        }
    }

    /**
     * 获取共享作用域
     */
    getSharedScope(): SharedScope {
        return { ...this.sharedScope };
    }

    /**
     * 获取模块缓存
     */
    getModuleCache(): Map<string, any> {
        return new Map(this.moduleCache);
    }

    /**
     * 检查是否激活
     */
    isActivated(): boolean {
        return this.isActive;
    }

    /**
     * 记录日志
     */
    private log(message: string) {
        if (this.options.enableLogging) {
            console.log(`[FederationSandbox:${this.name}] ${message}`);
        }
    }
}