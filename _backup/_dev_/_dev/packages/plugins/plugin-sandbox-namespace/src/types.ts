/**
 * 命名空间沙箱插件类型定义
 */

export interface NamespaceSandboxPluginOptions {
    /** 是否启用日志 */
    enableLogging?: boolean;
    /** 命名空间前缀 */
    namespacePrefix?: string;
    /** 是否启用自动清理 */
    enableAutoCleanup?: boolean;
    /** 是否启用冲突检测 */
    enableConflictDetection?: boolean;
}

export interface NamespaceSandboxOptions {
    /** 是否启用日志 */
    enableLogging?: boolean;
    /** 命名空间前缀 */
    namespacePrefix?: string;
    /** 是否启用自动清理 */
    enableAutoCleanup?: boolean;
    /** 是否启用冲突检测 */
    enableConflictDetection?: boolean;
}

export interface NamespaceContext {
    /** 命名空间名称 */
    namespace: string;
    /** 变量映射 */
    variables: Map<string, any>;
    /** 函数映射 */
    functions: Map<string, Function>;
    /** 对象映射 */
    objects: Map<string, object>;
    /** 事件映射 */
    events: Map<string, EventListener[]>;
}

export interface NamespaceProxy {
    /** 属性获取器 */
    get: (target: any, prop: string) => any;
    /** 属性设置器 */
    set: (target: any, prop: string, value: any) => boolean;
    /** 属性检查器 */
    has: (target: any, prop: string) => boolean;
    /** 属性删除器 */
    deleteProperty: (target: any, prop: string) => boolean;
    /** 属性枚举器 */
    ownKeys: (target: any) => string[];
    /** 属性描述符获取器 */
    getOwnPropertyDescriptor: (target: any, prop: string) => PropertyDescriptor | undefined;
}

export interface NamespaceVariable {
    /** 变量名 */
    name: string;
    /** 变量值 */
    value: any;
    /** 变量类型 */
    type: string;
    /** 是否只读 */
    readonly?: boolean;
    /** 创建时间 */
    createdAt: Date;
    /** 最后修改时间 */
    updatedAt: Date;
}

export interface NamespaceConflict {
    /** 冲突的属性名 */
    property: string;
    /** 冲突的命名空间 */
    conflictingNamespaces: string[];
    /** 冲突类型 */
    type: 'global' | 'namespace';
    /** 冲突描述 */
    description: string;
}