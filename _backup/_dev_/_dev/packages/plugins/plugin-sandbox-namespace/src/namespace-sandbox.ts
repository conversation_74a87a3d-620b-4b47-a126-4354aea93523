import type { NamespaceContext, NamespaceProxy, NamespaceSandboxOptions } from './types';

/**
 * 命名空间沙箱实现
 * 通过命名空间前缀提供轻量级变量隔离
 */
export class NamespaceSandbox {
    private name: string;
    private options: NamespaceSandboxOptions;
    private isActive = false;
    private namespace: string;
    private context: NamespaceContext;
    private proxy: NamespaceProxy;
    private originalProperties = new Map<string, any>();

    constructor(name: string, options: NamespaceSandboxOptions = {}) {
        this.name = name;
        this.options = {
            enableLogging: false,
            namespacePrefix: '__MICRO_APP_',
            enableAutoCleanup: true,
            enableConflictDetection: true,
            ...options
        };

        this.namespace = this.generateNamespace();
        this.initializeContext();
        this.createProxy();
    }

    /**
     * 生成命名空间
     */
    private generateNamespace(): string {
        const prefix = this.options.namespacePrefix || '__MICRO_APP_';
        const suffix = this.name.toUpperCase().replace(/[^A-Z0-9]/g, '_');
        return `${prefix}${suffix}__`;
    }

    /**
     * 初始化命名空间上下文
     */
    private initializeContext() {
        this.context = {
            namespace: this.namespace,
            variables: new Map(),
            functions: new Map(),
            objects: new Map(),
            events: new Map()
        };

        // 在全局对象上创建命名空间
        (window as any)[this.namespace] = {};

        this.log(`命名空间 ${this.namespace} 初始化完成`);
    }

    /**
     * 创建命名空间代理
     */
    private createProxy() {
        const namespaceObject = (window as any)[this.namespace];

        this.proxy = {
            get: (target: any, prop: string) => {
                // 首先从命名空间获取
                if (prop in namespaceObject) {
                    return namespaceObject[prop];
                }

                // 然后从全局获取
                if (prop in window) {
                    return (window as any)[prop];
                }

                return undefined;
            },

            set: (target: any, prop: string, value: any) => {
                // 检查冲突
                if (this.options.enableConflictDetection && this.checkConflict(prop)) {
                    this.log(`属性 ${prop} 存在冲突，使用命名空间隔离`);
                }

                // 设置到命名空间
                namespaceObject[prop] = value;
                this.context.variables.set(prop, value);

                this.log(`设置命名空间变量: ${prop} = ${value}`);
                return true;
            },

            has: (target: any, prop: string) => {
                return prop in namespaceObject || prop in window;
            },

            deleteProperty: (target: any, prop: string) => {
                if (prop in namespaceObject) {
                    delete namespaceObject[prop];
                    this.context.variables.delete(prop);
                    this.log(`删除命名空间变量: ${prop}`);
                    return true;
                }
                return false;
            },

            ownKeys: (target: any) => {
                return [
                    ...Object.keys(namespaceObject),
                    ...Object.keys(window).filter(key => !(key in namespaceObject))
                ];
            },

            getOwnPropertyDescriptor: (target: any, prop: string) => {
                if (prop in namespaceObject) {
                    return Object.getOwnPropertyDescriptor(namespaceObject, prop);
                }
                return Object.getOwnPropertyDescriptor(window, prop);
            }
        };
    }

    /**
     * 检查属性冲突
     */
    private checkConflict(prop: string): boolean {
        if (!this.options.enableConflictDetection) {
            return false;
        }

        // 检查是否与全局属性冲突
        if (prop in window && typeof (window as any)[prop] !== 'undefined') {
            return true;
        }

        // 检查是否与其他命名空间冲突
        for (const key of Object.keys(window)) {
            if (key.startsWith(this.options.namespacePrefix || '__MICRO_APP_') && key !== this.namespace) {
                const otherNamespace = (window as any)[key];
                if (otherNamespace && prop in otherNamespace) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 激活沙箱
     */
    activate() {
        if (this.isActive) {
            this.log('沙箱已经激活，跳过重复激活');
            return;
        }

        try {
            this.patchGlobalAccess();
            this.isActive = true;
            this.log('沙箱激活成功');
        } catch (error) {
            console.error(`[NamespaceSandbox:${this.name}] 激活沙箱失败:`, error);
            throw error;
        }
    }

    /**
     * 停用沙箱
     */
    deactivate() {
        if (!this.isActive) {
            this.log('沙箱未激活，跳过停用');
            return;
        }

        try {
            this.restoreGlobalAccess();
            this.isActive = false;
            this.log('沙箱停用成功');
        } catch (error) {
            console.error(`[NamespaceSandbox:${this.name}] 停用沙箱失败:`, error);
            throw error;
        }
    }

    /**
     * 销毁沙箱
     */
    destroy() {
        this.deactivate();

        // 清理命名空间
        if (this.options.enableAutoCleanup) {
            this.cleanupNamespace();
        }

        // 清理上下文
        this.context.variables.clear();
        this.context.functions.clear();
        this.context.objects.clear();
        this.context.events.clear();

        this.log('沙箱销毁完成');
    }

    /**
     * 修补全局访问
     */
    private patchGlobalAccess() {
        // 这里可以实现更复杂的全局访问修补逻辑
        // 目前使用简单的命名空间隔离
        this.log('全局访问修补完成');
    }

    /**
     * 恢复全局访问
     */
    private restoreGlobalAccess() {
        // 恢复被修改的全局属性
        for (const [prop, value] of this.originalProperties) {
            try {
                if (value === undefined) {
                    delete (window as any)[prop];
                } else {
                    (window as any)[prop] = value;
                }
            } catch (error) {
                this.log(`恢复全局属性 ${prop} 失败: ${error}`);
            }
        }

        this.originalProperties.clear();
        this.log('全局访问恢复完成');
    }

    /**
     * 清理命名空间
     */
    private cleanupNamespace() {
        try {
            delete (window as any)[this.namespace];
            this.log(`命名空间 ${this.namespace} 清理完成`);
        } catch (error) {
            console.error(`[NamespaceSandbox:${this.name}] 清理命名空间失败:`, error);
        }
    }

    /**
     * 执行代码
     */
    execScript(code: string): any {
        if (!this.isActive) {
            throw new Error('沙箱未激活，无法执行代码');
        }

        try {
            // 创建执行上下文
            const context = this.createExecutionContext();

            // 在命名空间上下文中执行代码
            const func = new Function(...Object.keys(context), code);
            return func.apply(context, Object.values(context));
        } catch (error) {
            console.error(`[NamespaceSandbox:${this.name}] 执行代码失败:`, error);
            throw error;
        }
    }

    /**
     * 创建执行上下文
     */
    private createExecutionContext(): Record<string, any> {
        const namespaceObject = (window as any)[this.namespace];

        return {
            // 提供命名空间对象
            [this.namespace.slice(0, -2)]: namespaceObject,

            // 提供常用的全局对象（安全的）
            console: window.console,
            setTimeout: window.setTimeout.bind(window),
            clearTimeout: window.clearTimeout.bind(window),
            setInterval: window.setInterval.bind(window),
            clearInterval: window.clearInterval.bind(window),

            // 提供受限的 window 访问
            window: new Proxy(window, this.proxy)
        };
    }

    /**
     * 设置命名空间变量
     */
    setVariable(name: string, value: any) {
        const namespaceObject = (window as any)[this.namespace];
        namespaceObject[name] = value;
        this.context.variables.set(name, value);
        this.log(`设置命名空间变量: ${name}`);
    }

    /**
     * 获取命名空间变量
     */
    getVariable(name: string): any {
        const namespaceObject = (window as any)[this.namespace];
        return namespaceObject[name];
    }

    /**
     * 删除命名空间变量
     */
    deleteVariable(name: string): boolean {
        const namespaceObject = (window as any)[this.namespace];
        if (name in namespaceObject) {
            delete namespaceObject[name];
            this.context.variables.delete(name);
            this.log(`删除命名空间变量: ${name}`);
            return true;
        }
        return false;
    }

    /**
     * 获取所有命名空间变量
     */
    getAllVariables(): Record<string, any> {
        const namespaceObject = (window as any)[this.namespace];
        return { ...namespaceObject };
    }

    /**
     * 获取命名空间
     */
    getNamespace(): string {
        return this.namespace;
    }

    /**
     * 获取上下文
     */
    getContext(): NamespaceContext {
        return { ...this.context };
    }

    /**
     * 检查是否激活
     */
    isActivated(): boolean {
        return this.isActive;
    }

    /**
     * 记录日志
     */
    private log(message: string) {
        if (this.options.enableLogging) {
            console.log(`[NamespaceSandbox:${this.name}] ${message}`);
        }
    }
}