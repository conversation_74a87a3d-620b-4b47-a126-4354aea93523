/**
 * PostMessage 桥接器 - 处理 iframe 与主应用间的通信
 */

import { PostMessageData } from './types';

export class PostMessageBridge {
    private messageHandlers: Map<string, (message: PostMessageData) => void> = new Map();
    private isInitialized = false;

    /**
     * 初始化消息桥接器
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        // 监听来自 iframe 的消息
        window.addEventListener('message', this.handleMessage.bind(this));
        this.isInitialized = true;
    }

    /**
     * 发送消息到指定应用
     */
    sendMessage(appName: string, message: PostMessageData): void {
        // 查找对应的 iframe
        const iframe = document.getElementById(`micro-app-iframe-${appName}`) as HTMLIFrameElement;
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(message, '*');
        }
    }

    /**
     * 广播消息到所有应用
     */
    broadcast(message: PostMessageData): void {
        // 查找所有微应用 iframe
        const iframes = document.querySelectorAll('iframe[id^="micro-app-iframe-"]');
        iframes.forEach((iframe) => {
            const iframeElement = iframe as HTMLIFrameElement;
            if (iframeElement.contentWindow) {
                iframeElement.contentWindow.postMessage(message, '*');
            }
        });
    }

    /**
     * 监听来自指定应用的消息
     */
    onAppMessage(appName: string, handler: (message: PostMessageData) => void): void {
        this.messageHandlers.set(appName, handler);
    }

    /**
     * 移除应用消息监听器
     */
    removeAppListener(appName: string): void {
        this.messageHandlers.delete(appName);
    }

    /**
     * 处理来自 iframe 的消息
     */
    private handleMessage(event: MessageEvent): void {
        // 验证消息来源和格式
        if (!this.isValidMessage(event.data)) {
            return;
        }

        const message: PostMessageData = event.data;

        // 确定消息来源应用
        const appName = this.getAppNameFromSource(event.source);
        if (!appName) {
            return;
        }

        // 调用对应的消息处理器
        const handler = this.messageHandlers.get(appName);
        if (handler) {
            handler(message);
        }

        // 如果有指定接收方，转发消息
        if (message.to && message.to !== appName) {
            this.sendMessage(message.to, message);
        }
    }

    /**
     * 验证消息格式
     */
    private isValidMessage(data: any): data is PostMessageData {
        return (
            data &&
            typeof data === 'object' &&
            typeof data.type === 'string' &&
            typeof data.timestamp === 'number'
        );
    }

    /**
     * 从消息源获取应用名称
     */
    private getAppNameFromSource(source: MessageEventSource | null): string | null {
        if (!source) {
            return null;
        }

        // 查找对应的 iframe
        const iframes = document.querySelectorAll('iframe[id^="micro-app-iframe-"]');
        for (const iframe of Array.from(iframes)) {
            const iframeElement = iframe as HTMLIFrameElement;
            if (iframeElement.contentWindow === source) {
                const id = iframeElement.id;
                return id.replace('micro-app-iframe-', '');
            }
        }

        return null;
    }

    /**
     * 销毁消息桥接器
     */
    destroy(): void {
        if (this.isInitialized) {
            window.removeEventListener('message', this.handleMessage.bind(this));
            this.messageHandlers.clear();
            this.isInitialized = false;
        }
    }
}