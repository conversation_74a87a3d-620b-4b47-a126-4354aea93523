/**
 * @micro-core/plugin-sandbox-iframe
 * Iframe 沙箱插件 - 提供基于 iframe 的强隔离沙箱环境
 */

import { MicroApp, MicroCorePlugin, PluginContext, SandboxPlugin } from '@micro-core/core';
import { IframeSandbox } from './iframe-sandbox';
import { PostMessageBridge } from './post-message-bridge';
import { IframeSandboxOptions } from './types';

export class IframeSandboxPlugin implements MicroCorePlugin, SandboxPlugin {
    name = 'iframe-sandbox';
    version = '1.0.0';

    private sandboxes: Map<string, IframeSandbox> = new Map();
    private messageBridge: PostMessageBridge;
    private options: IframeSandboxOptions;

    constructor(options: IframeSandboxOptions = {}) {
        this.options = {
            enablePostMessage: true,
            enableSandboxAttributes: true,
            allowScripts: true,
            allowSameOrigin: false,
            allowForms: true,
            allowPopups: false,
            ...options
        };

        this.messageBridge = new PostMessageBridge();
    }

    async install(context: PluginContext): Promise<void> {
        // 注册沙箱创建器
        context.registerSandboxCreator('iframe', this.createSandbox.bind(this));

        // 初始化消息桥接器
        if (this.options.enablePostMessage) {
            await this.messageBridge.initialize();
        }
    }

    async uninstall(): Promise<void> {
        // 销毁所有沙箱
        for (const [appName, sandbox] of this.sandboxes) {
            await sandbox.destroy();
        }
        this.sandboxes.clear();

        // 销毁消息桥接器
        this.messageBridge.destroy();
    }

    /**
     * 创建 iframe 沙箱
     */
    async createSandbox(app: MicroApp): Promise<IframeSandbox> {
        const sandbox = new IframeSandbox(app, this.options, this.messageBridge);
        await sandbox.initialize();

        this.sandboxes.set(app.name, sandbox);
        return sandbox;
    }

    /**
     * 获取应用的沙箱实例
     */
    getSandbox(appName: string): IframeSandbox | undefined {
        return this.sandboxes.get(appName);
    }

    /**
     * 销毁指定应用的沙箱
     */
    async destroySandbox(appName: string): Promise<void> {
        const sandbox = this.sandboxes.get(appName);
        if (sandbox) {
            await sandbox.destroy();
            this.sandboxes.delete(appName);
        }
    }
}

export * from './iframe-sandbox';
export * from './post-message-bridge';
export * from './types';
