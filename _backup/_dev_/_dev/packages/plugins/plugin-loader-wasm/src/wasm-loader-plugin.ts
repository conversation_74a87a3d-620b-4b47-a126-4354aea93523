/**
 * WebAssembly 加载器插件
 * 支持 WASM 模块的高性能加载和执行
 */

import type { MicroCoreKernel, Plugin } from '@micro-core/core';
import type {
    WasmExecuteParams,
    WasmExecuteResult,
    WasmImportObject,
    WasmLoadProgress,
    WasmLoaderConfig,
    WasmLoaderEvents,
    WasmLoaderStats,
    WasmModuleInfo
} from './types';
import { WasmInstancePool } from './wasm-instance-pool';
import { WasmModuleManager } from './wasm-module-manager';

/**
 * WebAssembly 加载器插件类
 */
export class WasmLoaderPlugin implements Plugin {
    name = 'wasm-loader';
    version = '0.1.0';

    private moduleManager: WasmModuleManager | null = null;
    private instancePool: WasmInstancePool | null = null;
    private config: Required<WasmLoaderConfig>;
    private eventListeners: Partial<WasmLoaderEvents> = {};
    private stats: WasmLoaderStats = {
        loadedModules: 0,
        activeInstances: 0,
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageLoadTime: 0,
        averageExecutionTime: 0,
        totalMemoryUsage: 0,
        cacheHitRate: 0,
        errorRate: 0
    };

    constructor(config: WasmLoaderConfig = {}) {
        this.config = {
            enableStreaming: true,
            instancePoolSize: 10,
            memoryOptimization: true,
            maxMemoryPages: 256,
            enableCache: true,
            cacheStrategy: 'memory',
            cacheSize: 100,
            timeout: 30000,
            enableProfiling: false,
            debug: false,
            ...config
        };
    }

    /**
     * 插件安装
     */
    install(kernel: MicroCoreKernel): void {
        // 检查浏览器支持
        if (!WasmLoaderPlugin.isSupported()) {
            throw new Error('当前浏览器不支持 WebAssembly');
        }

        // 初始化管理器
        this.moduleManager = new WasmModuleManager(this.config);
        this.instancePool = new WasmInstancePool(this.config);

        // 注册到内核
        kernel.registerPlugin(this);

        // 添加方法到内核
        (kernel as any).loadWasmModule = this.loadModule.bind(this);
        (kernel as any).executeWasmFunction = this.executeFunction.bind(this);
        (kernel as any).getWasmStats = this.getStats.bind(this);

        if (this.config.debug) {
            console.log('WebAssembly 加载器插件已安装');
        }
    }

    /**
     * 插件卸载
     */
    uninstall(kernel: MicroCoreKernel): void {
        if (this.moduleManager) {
            this.moduleManager.destroy();
            this.moduleManager = null;
        }

        if (this.instancePool) {
            this.instancePool.destroy();
            this.instancePool = null;
        }

        // 从内核移除方法
        delete (kernel as any).loadWasmModule;
        delete (kernel as any).executeWasmFunction;
        delete (kernel as any).getWasmStats;

        this.eventListeners = {};

        if (this.config.debug) {
            console.log('WebAssembly 加载器插件已卸载');
        }
    }

    /**
     * 加载 WASM 模块
     */
    async loadModule(
        url: string,
        options: {
            moduleId?: string;
            importObject?: WasmImportObject;
            onProgress?: (progress: WasmLoadProgress) => void;
        } = {}
    ): Promise<WasmModuleInfo> {
        if (!this.moduleManager) {
            throw new Error('模块管理器未初始化');
        }

        const startTime = Date.now();

        try {
            // 触发加载开始事件
            this.emit('moduleLoadStart', options.moduleId || url, url);

            // 加载模块
            const moduleInfo = await this.moduleManager.loadModule(url, {
                ...options,
                onProgress: (progress) => {
                    options.onProgress?.(progress);
                    this.emit('moduleLoadProgress', progress);
                }
            });

            // 更新统计信息
            this.stats.loadedModules++;
            const loadTime = Date.now() - startTime;
            this.updateAverageLoadTime(loadTime);

            // 触发加载完成事件
            this.emit('moduleLoadComplete', moduleInfo);

            return moduleInfo;

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);

            // 触发加载错误事件
            this.emit('moduleLoadError', options.moduleId || url, errorMessage);

            throw error;
        }
    }

    /**
     * 执行 WASM 函数
     */
    async executeFunction(
        moduleId: string,
        functionName: string,
        args?: any[],
        options: {
            async?: boolean;
            timeout?: number;
            memoryData?: ArrayBuffer | Uint8Array;
            memoryOffset?: number;
        } = {}
    ): Promise<WasmExecuteResult> {
        if (!this.moduleManager || !this.instancePool) {
            throw new Error('管理器未初始化');
        }

        // 获取模块信息
        const moduleInfo = this.moduleManager.getModuleInfo(moduleId);
        if (!moduleInfo) {
            throw new Error(`模块 ${moduleId} 不存在`);
        }

        try {
            // 获取实例
            const instance = await this.instancePool.getInstance(
                moduleId,
                // 这里需要从缓存中获取编译后的模块
                await this.getCompiledModule(moduleId)
            );

            // 触发执行开始事件
            this.emit('executionStart', instance.id, functionName);

            // 执行函数
            const executeParams: WasmExecuteParams = {
                functionName,
                args,
                async: options.async,
                timeout: options.timeout || this.config.timeout,
                memoryData: options.memoryData,
                memoryOffset: options.memoryOffset
            };

            const result = await this.instancePool.executeFunction(instance.id, executeParams);

            // 释放实例
            this.instancePool.releaseInstance(instance.id);

            // 更新统计信息
            this.stats.totalExecutions++;
            if (result.success) {
                this.stats.successfulExecutions++;
            } else {
                this.stats.failedExecutions++;
            }

            this.updateAverageExecutionTime(result.executionTime);
            this.updateErrorRate();

            // 触发执行完成事件
            this.emit('executionComplete', instance.id, result);

            return result;

        } catch (error) {
            this.stats.totalExecutions++;
            this.stats.failedExecutions++;
            this.updateErrorRate();

            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`执行 WASM 函数失败: ${errorMessage}`);
        }
    }

    /**
     * 获取编译后的模块
     */
    private async getCompiledModule(moduleId: string): Promise<WebAssembly.Module> {
        // 这里应该从缓存中获取编译后的模块
        // 简化实现，实际应该与 WasmModuleManager 集成
        throw new Error('获取编译后的模块功能需要实现');
    }

    /**
     * 预加载多个模块
     */
    async preloadModules(
        urls: string[],
        options: {
            onProgress?: (url: string, progress: WasmLoadProgress) => void;
            onComplete?: (url: string, moduleInfo: WasmModuleInfo) => void;
            onError?: (url: string, error: string) => void;
        } = {}
    ): Promise<WasmModuleInfo[]> {
        const promises = urls.map(async (url) => {
            try {
                const moduleInfo = await this.loadModule(url, {
                    onProgress: options.onProgress ? (progress) => options.onProgress!(url, progress) : undefined
                });

                if (options.onComplete) {
                    options.onComplete(url, moduleInfo);
                }

                return moduleInfo;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);

                if (options.onError) {
                    options.onError(url, errorMessage);
                }

                throw error;
            }
        });

        return Promise.all(promises);
    }

    /**
     * 获取模块信息
     */
    getModuleInfo(moduleId: string): WasmModuleInfo | undefined {
        return this.moduleManager?.getModuleInfo(moduleId);
    }

    /**
     * 获取所有模块信息
     */
    getAllModules(): WasmModuleInfo[] {
        return this.moduleManager?.getAllModules() || [];
    }

    /**
     * 获取实例统计信息
     */
    getInstanceStats(moduleId?: string) {
        return this.instancePool?.getInstanceStats(moduleId) || {
            totalInstances: 0,
            busyInstances: 0,
            availableInstances: 0
        };
    }

    /**
     * 获取性能指标
     */
    getPerformanceMetrics(moduleId?: string) {
        return this.instancePool?.getPerformanceMetrics(moduleId) || [];
    }

    /**
     * 获取统计信息
     */
    getStats(): WasmLoaderStats {
        const instanceStats = this.getInstanceStats();
        this.stats.activeInstances = instanceStats.totalInstances;

        // 计算总内存使用量
        const performanceMetrics = this.getPerformanceMetrics();
        this.stats.totalMemoryUsage = performanceMetrics.reduce(
            (total, metrics) => total + metrics.currentMemoryUsage,
            0
        );

        return { ...this.stats };
    }

    /**
     * 清理未使用的实例
     */
    cleanupInstances(maxIdleTime?: number): void {
        if (this.instancePool) {
            this.instancePool.cleanupUnusedInstances(maxIdleTime);
        }
    }

    /**
     * 清空所有模块和实例
     */
    async clearAll(): Promise<void> {
        if (this.moduleManager) {
            await this.moduleManager.clearAll();
        }

        if (this.instancePool) {
            this.instancePool.destroy();
            this.instancePool = new WasmInstancePool(this.config);
        }

        // 重置统计信息
        this.stats = {
            loadedModules: 0,
            activeInstances: 0,
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageLoadTime: 0,
            averageExecutionTime: 0,
            totalMemoryUsage: 0,
            cacheHitRate: 0,
            errorRate: 0
        };
    }

    /**
     * 更新平均加载时间
     */
    private updateAverageLoadTime(loadTime: number): void {
        const totalModules = this.stats.loadedModules;
        this.stats.averageLoadTime =
            (this.stats.averageLoadTime * (totalModules - 1) + loadTime) / totalModules;
    }

    /**
     * 更新平均执行时间
     */
    private updateAverageExecutionTime(executionTime: number): void {
        const totalExecutions = this.stats.totalExecutions;
        this.stats.averageExecutionTime =
            (this.stats.averageExecutionTime * (totalExecutions - 1) + executionTime) / totalExecutions;
    }

    /**
     * 更新错误率
     */
    private updateErrorRate(): void {
        if (this.stats.totalExecutions > 0) {
            this.stats.errorRate = this.stats.failedExecutions / this.stats.totalExecutions;
        }
    }

    /**
     * 添加事件监听器
     */
    on<K extends keyof WasmLoaderEvents>(event: K, listener: WasmLoaderEvents[K]): void {
        this.eventListeners[event] = listener;
    }

    /**
     * 移除事件监听器
     */
    off<K extends keyof WasmLoaderEvents>(event: K): void {
        delete this.eventListeners[event];
    }

    /**
     * 触发事件
     */
    private emit<K extends keyof WasmLoaderEvents>(event: K, ...args: Parameters<WasmLoaderEvents[K]>): void {
        const listener = this.eventListeners[event];
        if (listener) {
            try {
                (listener as any)(...args);
            } catch (error) {
                console.error(`WASM 加载器事件监听器 ${event} 执行失败:`, error);
            }
        }
    }

    /**
     * 检查浏览器支持
     */
    static isSupported(): boolean {
        return typeof WebAssembly !== 'undefined' &&
            typeof WebAssembly.compile === 'function' &&
            typeof WebAssembly.instantiate === 'function';
    }

    /**
     * 创建插件实例
     */
    static create(config?: WasmLoaderConfig): WasmLoaderPlugin {
        if (!WasmLoaderPlugin.isSupported()) {
            throw new Error('当前浏览器不支持 WebAssembly');
        }
        return new WasmLoaderPlugin(config);
    }
}
