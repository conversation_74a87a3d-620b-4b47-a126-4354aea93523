/**
 * @fileoverview WASM 模块管理器
 * @description 管理 WebAssembly 模块的加载、编译和缓存
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { WasmImportObject, WasmLoaderConfig, WasmLoadProgress, WasmModuleInfo } from './types';
import { WasmCacheManager } from './wasm/cache-manager';
import { WasmModuleLoader } from './wasm/module-loader';
import { WasmUtils } from './wasm/utils';

/**
 * WASM 模块管理器类
 */
export class WasmModuleManager {
    private config: Required<WasmLoaderConfig>;
    private modules = new Map<string, WasmModuleInfo>();
    private loadingPromises = new Map<string, Promise<WasmModuleInfo>>();
    private cacheManager: WasmCacheManager;
    private moduleLoader: WasmModuleLoader;

    constructor(config: WasmLoaderConfig) {
        this.config = {
            enableStreaming: true,
            instancePoolSize: 10,
            memoryOptimization: true,
            maxMemoryPages: 256,
            enableCache: true,
            cacheStrategy: 'memory',
            cacheSize: 100,
            timeout: 30000,
            enableProfiling: false,
            debug: false,
            ...config
        };

        this.cacheManager = new WasmCacheManager(this.config);
        this.moduleLoader = new WasmModuleLoader(this.config);
    }

    /**
     * 加载 WASM 模块
     */
    async loadModule(
        url: string,
        options: {
            moduleId?: string;
            importObject?: WasmImportObject;
            onProgress?: (progress: WasmLoadProgress) => void;
        } = {}
    ): Promise<WasmModuleInfo> {
        const moduleId = options.moduleId || WasmUtils.generateModuleId(url);

        // 检查是否已经在加载中
        const existingPromise = this.loadingPromises.get(moduleId);
        if (existingPromise) {
            return existingPromise;
        }

        // 检查缓存
        if (this.config.enableCache) {
            const cachedModule = await this.cacheManager.getFromCache(moduleId);
            if (cachedModule) {
                this.modules.set(moduleId, cachedModule.moduleInfo);
                return cachedModule.moduleInfo;
            }
        }

        // 创建加载 Promise
        const loadPromise = this.doLoadModule(url, moduleId, options);
        this.loadingPromises.set(moduleId, loadPromise);

        try {
            const moduleInfo = await loadPromise;
            this.loadingPromises.delete(moduleId);
            return moduleInfo;
        } catch (error) {
            this.loadingPromises.delete(moduleId);
            throw error;
        }
    }

    /**
     * 执行模块加载
     */
    private async doLoadModule(
        url: string,
        moduleId: string,
        options: {
            importObject?: WasmImportObject;
            onProgress?: (progress: WasmLoadProgress) => void;
        }
    ): Promise<WasmModuleInfo> {
        try {
            // 使用模块加载器加载和编译
            const { module, moduleInfo } = await this.moduleLoader.loadAndCompile(
                url,
                moduleId,
                options
            );

            // 存储模块信息
            this.modules.set(moduleId, moduleInfo);

            // 缓存模块
            if (this.config.enableCache) {
                await this.cacheManager.saveToCache(moduleId, module, moduleInfo);
            }

            return moduleInfo;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`加载 WASM 模块失败: ${errorMessage}`);
        }
    }

    /**
     * 获取模块信息
     */
    getModuleInfo(moduleId: string): WasmModuleInfo | undefined {
        return this.modules.get(moduleId);
    }

    /**
     * 获取所有模块信息
     */
    getAllModules(): WasmModuleInfo[] {
        return Array.from(this.modules.values());
    }

    /**
     * 删除模块
     */
    async deleteModule(moduleId: string): Promise<void> {
        this.modules.delete(moduleId);
        await this.cacheManager.deleteFromCache(moduleId);
    }

    /**
     * 清空所有模块和缓存
     */
    async clearAll(): Promise<void> {
        this.modules.clear();
        this.loadingPromises.clear();
        await this.cacheManager.clearCache();
    }

    /**
     * 获取缓存统计信息
     */
    async getCacheStats(): Promise<{
        totalModules: number;
        cacheSize: number;
        memoryUsage: number;
        hitRate: number;
    }> {
        const totalModules = this.modules.size;
        const cacheSize = this.cacheManager.getCacheSize();

        // 计算内存使用量
        let memoryUsage = 0;
        for (const moduleInfo of this.modules.values()) {
            memoryUsage += moduleInfo.size;
        }

        return {
            totalModules,
            cacheSize,
            memoryUsage,
            hitRate: 0 // 这里可以实现命中率统计
        };
    }

    /**
     * 检查浏览器支持
     */
    static isSupported(): boolean {
        return WasmUtils.isSupported();
    }

    /**
     * 获取功能支持信息
     */
    static async getFeatureSupport() {
        return WasmUtils.getFeatureSupport();
    }

    /**
     * 销毁管理器
     */
    destroy(): void {
        this.clearAll();
        this.cacheManager.destroy();
    }
}

// 导出工具类
export { WasmCacheManager } from './wasm/cache-manager';
export { WasmModuleLoader } from './wasm/module-loader';
export { WasmUtils } from './wasm/utils';
