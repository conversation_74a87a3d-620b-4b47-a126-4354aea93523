/**
 * WASM 实例池管理器
 * 管理 WebAssembly 实例的创建、复用和销毁
 */

import type {
    WasmExecuteParams,
    WasmExecuteResult,
    WasmImportObject,
    WasmInstance,
    WasmLoaderConfig,
    WasmPerformanceMetrics
} from './types';

/**
 * WASM 实例池管理器类
 */
export class WasmInstancePool {
    private config: Required<WasmLoaderConfig>;
    private instances = new Map<string, WasmInstance[]>();
    private busyInstances = new Set<string>();
    private performanceMetrics = new Map<string, WasmPerformanceMetrics>();
    private nextInstanceId = 1;

    constructor(config: WasmLoaderConfig) {
        this.config = {
            enableStreaming: true,
            instancePoolSize: 10,
            memoryOptimization: true,
            maxMemoryPages: 256,
            enableCache: true,
            cacheStrategy: 'memory',
            cacheSize: 100,
            timeout: 30000,
            enableProfiling: false,
            debug: false,
            ...config
        };
    }

    /**
     * 获取或创建实例
     */
    async getInstance(
        moduleId: string,
        module: WebAssembly.Module,
        importObject?: WasmImportObject
    ): Promise<WasmInstance> {
        // 查找可用实例
        const availableInstance = this.findAvailableInstance(moduleId);
        if (availableInstance) {
            availableInstance.busy = true;
            availableInstance.lastUsed = Date.now();
            availableInstance.useCount++;
            this.busyInstances.add(availableInstance.id);
            return availableInstance;
        }

        // 创建新实例
        return await this.createInstance(moduleId, module, importObject);
    }

    /**
     * 释放实例
     */
    releaseInstance(instanceId: string): void {
        // 查找实例
        for (const instances of this.instances.values()) {
            const instance = instances.find(inst => inst.id === instanceId);
            if (instance) {
                instance.busy = false;
                instance.lastUsed = Date.now();
                this.busyInstances.delete(instanceId);
                break;
            }
        }
    }

    /**
     * 执行 WASM 函数
     */
    async executeFunction(
        instanceId: string,
        params: WasmExecuteParams
    ): Promise<WasmExecuteResult> {
        const instance = this.findInstanceById(instanceId);
        if (!instance) {
            throw new Error(`实例 ${instanceId} 不存在`);
        }

        const startTime = Date.now();
        const startMemory = this.getMemoryUsage(instance);

        try {
            // 检查函数是否存在
            const func = instance.instance.exports[params.functionName];
            if (typeof func !== 'function') {
                throw new Error(`函数 ${params.functionName} 不存在`);
            }

            // 处理内存数据传输
            if (params.memoryData) {
                this.writeMemory(instance, params.memoryData, params.memoryOffset || 0);
            }

            // 执行函数
            let result: any;
            if (params.async) {
                result = await this.executeAsync(func, params.args || [], params.timeout);
            } else {
                result = func.apply(null, params.args || []);
            }

            const executionTime = Date.now() - startTime;
            const endMemory = this.getMemoryUsage(instance);

            // 更新性能指标
            this.updatePerformanceMetrics(instance.moduleId, executionTime, endMemory);

            // 读取返回的内存数据
            let memoryData: ArrayBuffer | undefined;
            if (params.memoryOffset !== undefined) {
                memoryData = this.readMemory(instance, params.memoryOffset, 1024); // 读取 1KB
            }

            return {
                success: true,
                result,
                executionTime,
                memoryUsage: endMemory,
                memoryData
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : String(error);

            if (this.config.debug) {
                console.error(`WASM 函数执行失败 ${params.functionName}:`, error);
            }

            return {
                success: false,
                error: errorMessage,
                executionTime,
                memoryUsage: this.getMemoryUsage(instance)
            };
        }
    }

    /**
     * 查找可用实例
     */
    private findAvailableInstance(moduleId: string): WasmInstance | null {
        const instances = this.instances.get(moduleId);
        if (!instances) {
            return null;
        }

        return instances.find(instance => !instance.busy) || null;
    }

    /**
     * 创建新实例
     */
    private async createInstance(
        moduleId: string,
        module: WebAssembly.Module,
        importObject?: WasmImportObject
    ): Promise<WasmInstance> {
        const instanceId = `${moduleId}-instance-${this.nextInstanceId++}`;

        try {
            const wasmInstance = await WebAssembly.instantiate(module, importObject || {});

            const instance: WasmInstance = {
                id: instanceId,
                moduleId,
                instance: wasmInstance,
                module,
                busy: true,
                createdAt: Date.now(),
                lastUsed: Date.now(),
                useCount: 1
            };

            // 添加到实例池
            if (!this.instances.has(moduleId)) {
                this.instances.set(moduleId, []);
            }

            const instances = this.instances.get(moduleId)!;

            // 检查实例池大小限制
            if (instances.length >= this.config.instancePoolSize) {
                // 移除最旧的未使用实例
                this.removeOldestUnusedInstance(moduleId);
            }

            instances.push(instance);
            this.busyInstances.add(instanceId);

            // 初始化性能指标
            if (!this.performanceMetrics.has(moduleId)) {
                this.performanceMetrics.set(moduleId, {
                    moduleId,
                    downloadTime: 0,
                    compileTime: 0,
                    instantiateTime: 0,
                    totalLoadTime: 0,
                    averageExecutionTime: 0,
                    executionCount: 0,
                    peakMemoryUsage: 0,
                    currentMemoryUsage: this.getMemoryUsage(instance)
                });
            }

            if (this.config.debug) {
                console.log(`创建 WASM 实例 ${instanceId}`);
            }

            return instance;

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`创建 WASM 实例失败: ${errorMessage}`);
        }
    }

    /**
     * 移除最旧的未使用实例
     */
    private removeOldestUnusedInstance(moduleId: string): void {
        const instances = this.instances.get(moduleId);
        if (!instances) {
            return;
        }

        // 找到最旧的未使用实例
        const unusedInstances = instances.filter(inst => !inst.busy);
        if (unusedInstances.length === 0) {
            return;
        }

        unusedInstances.sort((a, b) => a.lastUsed - b.lastUsed);
        const oldestInstance = unusedInstances[0];

        // 从实例池中移除
        const index = instances.indexOf(oldestInstance);
        if (index !== -1) {
            instances.splice(index, 1);
        }

        if (this.config.debug) {
            console.log(`移除旧实例 ${oldestInstance.id}`);
        }
    }

    /**
     * 根据 ID 查找实例
     */
    private findInstanceById(instanceId: string): WasmInstance | null {
        for (const instances of this.instances.values()) {
            const instance = instances.find(inst => inst.id === instanceId);
            if (instance) {
                return instance;
            }
        }
        return null;
    }

    /**
     * 异步执行函数
     */
    private async executeAsync(
        func: Function,
        args: any[],
        timeout?: number
    ): Promise<any> {
        return new Promise((resolve, reject) => {
            const timeoutId = timeout ? setTimeout(() => {
                reject(new Error('函数执行超时'));
            }, timeout) : null;

            try {
                // 在下一个事件循环中执行，避免阻塞
                setTimeout(() => {
                    try {
                        const result = func.apply(null, args);
                        if (timeoutId) clearTimeout(timeoutId);
                        resolve(result);
                    } catch (error) {
                        if (timeoutId) clearTimeout(timeoutId);
                        reject(error);
                    }
                }, 0);
            } catch (error) {
                if (timeoutId) clearTimeout(timeoutId);
                reject(error);
            }
        });
    }

    /**
     * 获取内存使用量
     */
    private getMemoryUsage(instance: WasmInstance): number {
        const memory = instance.instance.exports.memory as WebAssembly.Memory;
        return memory ? memory.buffer.byteLength : 0;
    }

    /**
     * 写入内存数据
     */
    private writeMemory(
        instance: WasmInstance,
        data: ArrayBuffer | Uint8Array,
        offset: number
    ): void {
        const memory = instance.instance.exports.memory as WebAssembly.Memory;
        if (!memory) {
            throw new Error('实例没有导出内存');
        }

        const memoryArray = new Uint8Array(memory.buffer);
        const dataArray = data instanceof ArrayBuffer ? new Uint8Array(data) : data;

        if (offset + dataArray.length > memoryArray.length) {
            throw new Error('内存写入超出边界');
        }

        memoryArray.set(dataArray, offset);
    }

    /**
     * 读取内存数据
     */
    private readMemory(
        instance: WasmInstance,
        offset: number,
        length: number
    ): ArrayBuffer {
        const memory = instance.instance.exports.memory as WebAssembly.Memory;
        if (!memory) {
            throw new Error('实例没有导出内存');
        }

        const memoryArray = new Uint8Array(memory.buffer);

        if (offset + length > memoryArray.length) {
            throw new Error('内存读取超出边界');
        }

        return memoryArray.slice(offset, offset + length).buffer;
    }

    /**
     * 更新性能指标
     */
    private updatePerformanceMetrics(
        moduleId: string,
        executionTime: number,
        memoryUsage: number
    ): void {
        const metrics = this.performanceMetrics.get(moduleId);
        if (!metrics) {
            return;
        }

        metrics.executionCount++;
        metrics.averageExecutionTime =
            (metrics.averageExecutionTime * (metrics.executionCount - 1) + executionTime) /
            metrics.executionCount;

        metrics.currentMemoryUsage = memoryUsage;
        if (memoryUsage > metrics.peakMemoryUsage) {
            metrics.peakMemoryUsage = memoryUsage;
        }
    }

    /**
     * 获取实例统计信息
     */
    getInstanceStats(moduleId?: string): {
        totalInstances: number;
        busyInstances: number;
        availableInstances: number;
        moduleStats?: Map<string, { total: number; busy: number; available: number }>;
    } {
        let totalInstances = 0;
        let busyInstances = 0;
        const moduleStats = new Map<string, { total: number; busy: number; available: number }>();

        for (const [modId, instances] of this.instances) {
            if (moduleId && modId !== moduleId) {
                continue;
            }

            const total = instances.length;
            const busy = instances.filter(inst => inst.busy).length;
            const available = total - busy;

            totalInstances += total;
            busyInstances += busy;

            if (!moduleId) {
                moduleStats.set(modId, { total, busy, available });
            }
        }

        return {
            totalInstances,
            busyInstances,
            availableInstances: totalInstances - busyInstances,
            moduleStats: moduleId ? undefined : moduleStats
        };
    }

    /**
     * 获取性能指标
     */
    getPerformanceMetrics(moduleId?: string): WasmPerformanceMetrics[] {
        if (moduleId) {
            const metrics = this.performanceMetrics.get(moduleId);
            return metrics ? [metrics] : [];
        }

        return Array.from(this.performanceMetrics.values());
    }

    /**
     * 清理未使用的实例
     */
    cleanupUnusedInstances(maxIdleTime: number = 300000): void { // 默认 5 分钟
        const now = Date.now();

        for (const [moduleId, instances] of this.instances) {
            const activeInstances = instances.filter(instance => {
                if (instance.busy) {
                    return true;
                }

                const idleTime = now - instance.lastUsed;
                return idleTime < maxIdleTime;
            });

            if (activeInstances.length !== instances.length) {
                this.instances.set(moduleId, activeInstances);

                if (this.config.debug) {
                    console.log(`清理模块 ${moduleId} 的 ${instances.length - activeInstances.length} 个未使用实例`);
                }
            }
        }
    }

    /**
     * 销毁所有实例
     */
    destroy(): void {
        this.instances.clear();
        this.busyInstances.clear();
        this.performanceMetrics.clear();
    }
}