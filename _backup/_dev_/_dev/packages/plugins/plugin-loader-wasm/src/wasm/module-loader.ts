/**
 * @fileoverview WASM 模块加载器
 * @description 负责 WASM 模块的下载、编译和实例化
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { WasmImport, WasmImportObject, WasmLoaderConfig, WasmLoadProgress, WasmMemoryInfo, WasmModuleInfo } from '../types';

/**
 * WASM 模块加载器
 */
export class WasmModuleLoader {
    private config: Required<WasmLoaderConfig>;

    constructor(config: Required<WasmLoaderConfig>) {
        this.config = config;
    }

    /**
     * 加载并编译 WASM 模块
     */
    async loadAndCompile(
        url: string,
        moduleId: string,
        options: {
            importObject?: WasmImportObject;
            onProgress?: (progress: WasmLoadProgress) => void;
        } = {}
    ): Promise<{ module: WebAssembly.Module; moduleInfo: WasmModuleInfo }> {
        const startTime = Date.now();
        const { onProgress } = options;

        try {
            // 报告开始下载
            onProgress?.({
                moduleId,
                stage: 'downloading',
                percentage: 0,
                loaded: 0,
                total: 0,
                message: '开始下载 WASM 模块'
            });

            // 下载模块
            const downloadStartTime = Date.now();
            const response = await this.fetchWithTimeout(url);
            const downloadTime = Date.now() - downloadStartTime;

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const contentLength = response.headers.get('content-length');
            const total = contentLength ? parseInt(contentLength, 10) : 0;

            // 读取响应数据
            const arrayBuffer = await this.readResponseWithProgress(
                response,
                moduleId,
                total,
                onProgress
            );

            // 报告开始编译
            onProgress?.({
                moduleId,
                stage: 'compiling',
                percentage: 50,
                loaded: arrayBuffer.byteLength,
                total: arrayBuffer.byteLength,
                message: '编译 WASM 模块'
            });

            // 编译模块
            const compileStartTime = Date.now();
            const module = this.config.enableStreaming && 'compileStreaming' in WebAssembly
                ? await this.compileStreaming(url)
                : await WebAssembly.compile(arrayBuffer);
            const compileTime = Date.now() - compileStartTime;

            // 报告开始实例化
            onProgress?.({
                moduleId,
                stage: 'instantiating',
                percentage: 75,
                loaded: arrayBuffer.byteLength,
                total: arrayBuffer.byteLength,
                message: '实例化 WASM 模块'
            });

            // 创建临时实例以获取模块信息
            const instantiateStartTime = Date.now();
            const instance = await WebAssembly.instantiate(module, options.importObject || {});
            const instantiateTime = Date.now() - instantiateStartTime;

            // 分析模块信息
            const moduleInfo: WasmModuleInfo = {
                id: moduleId,
                url,
                size: arrayBuffer.byteLength,
                compileTime,
                instantiateTime,
                exports: this.extractExports(instance),
                imports: this.extractImports(module),
                memory: this.extractMemoryInfo(instance),
                fromCache: false,
                createdAt: Date.now()
            };

            // 报告完成
            onProgress?.({
                moduleId,
                stage: 'complete',
                percentage: 100,
                loaded: arrayBuffer.byteLength,
                total: arrayBuffer.byteLength,
                message: 'WASM 模块加载完成'
            });

            if (this.config.debug) {
                console.log(`WASM 模块 ${moduleId} 加载完成:`, {
                    downloadTime,
                    compileTime,
                    instantiateTime,
                    totalTime: Date.now() - startTime,
                    size: arrayBuffer.byteLength
                });
            }

            return { module, moduleInfo };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);

            if (this.config.debug) {
                console.error(`WASM 模块 ${moduleId} 加载失败:`, error);
            }

            throw new Error(`加载 WASM 模块失败: ${errorMessage}`);
        }
    }

    /**
     * 带超时的 fetch
     */
    private async fetchWithTimeout(url: string): Promise<Response> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        try {
            const response = await fetch(url, { signal: controller.signal });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * 读取响应并跟踪进度
     */
    private async readResponseWithProgress(
        response: Response,
        moduleId: string,
        total: number,
        onProgress?: (progress: WasmLoadProgress) => void
    ): Promise<ArrayBuffer> {
        if (!response.body || !onProgress) {
            return await response.arrayBuffer();
        }

        const reader = response.body.getReader();
        const chunks: Uint8Array[] = [];
        let loaded = 0;

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                chunks.push(value);
                loaded += value.length;

                onProgress({
                    moduleId,
                    stage: 'downloading',
                    percentage: total > 0 ? (loaded / total) * 40 : 0, // 下载占总进度的 40%
                    loaded,
                    total,
                    message: `下载中: ${this.formatBytes(loaded)}${total > 0 ? ` / ${this.formatBytes(total)}` : ''}`
                });
            }
        } finally {
            reader.releaseLock();
        }

        // 合并所有块
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        return result.buffer;
    }

    /**
     * 流式编译
     */
    private async compileStreaming(url: string): Promise<WebAssembly.Module> {
        if ('compileStreaming' in WebAssembly) {
            return await WebAssembly.compileStreaming(fetch(url));
        } else {
            // 降级到普通编译
            const response = await fetch(url);
            const arrayBuffer = await response.arrayBuffer();
            return await WebAssembly.compile(arrayBuffer);
        }
    }

    /**
     * 提取导出信息
     */
    private extractExports(instance: WebAssembly.Instance): string[] {
        return Object.keys(instance.exports);
    }

    /**
     * 提取导入信息
     */
    private extractImports(module: WebAssembly.Module): WasmImport[] {
        const imports: WasmImport[] = [];

        try {
            const moduleImports = WebAssembly.Module.imports(module);
            for (const imp of moduleImports) {
                imports.push({
                    module: imp.module,
                    field: imp.name,
                    kind: imp.kind as any
                });
            }
        } catch (error) {
            if (this.config.debug) {
                console.warn('无法提取模块导入信息:', error);
            }
        }

        return imports;
    }

    /**
     * 提取内存信息
     */
    private extractMemoryInfo(instance: WebAssembly.Instance): WasmMemoryInfo | undefined {
        const memory = instance.exports.memory as WebAssembly.Memory;
        if (!memory) {
            return undefined;
        }

        return {
            initial: memory.buffer.byteLength / 65536, // 页大小为 64KB
            current: memory.buffer.byteLength / 65536,
            shared: memory.buffer instanceof SharedArrayBuffer
        };
    }

    /**
     * 格式化字节数
     */
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}