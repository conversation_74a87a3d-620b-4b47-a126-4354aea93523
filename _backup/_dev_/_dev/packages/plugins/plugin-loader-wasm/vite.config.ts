import { resolve } from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'MicroCorePluginLoaderWasm',
            formats: ['es', 'cjs'],
            fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
        },
        rollupOptions: {
            external: ['@micro-core/core'],
            output: {
                globals: {
                    '@micro-core/core': 'MicroCore'
                }
            }
        },
        sourcemap: true,
        minify: 'terser'
    },
    test: {
        environment: 'jsdom',
        globals: true
    }
});