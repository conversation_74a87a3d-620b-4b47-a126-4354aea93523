import type { MicroCoreKernel, Plugin } from '@micro-core/core';
import { logger } from '@micro-core/core';
import { RouterManager } from './router-manager';

/**
 * 路由插件配置
 */
export interface RouterPluginConfig {
    /** 路由模式 */
    mode?: 'hash' | 'history' | 'memory';
    /** 基础路径 */
    base?: string;
    /** 是否自动启动 */
    autoStart?: boolean;
    /** 路由守卫 */
    guards?: Array<{
        name?: string;
        canActivate: (from: string, to: string) => boolean | Promise<boolean>;
    }>;
}

/**
 * 路由插件
 * 提供统一的路由管理能力
 */
export class RouterPlugin implements Plugin {
    name = 'router';
    version = '0.1.0';
    private routerManager?: RouterManager;
    private config: RouterPluginConfig;

    constructor(config: RouterPluginConfig = {}) {
        this.config = {
            mode: 'history',
            base: '/',
            autoStart: true,
            ...config
        };
    }

    /**
     * 安装插件
     */
    install(kernel: MicroCoreKernel): void {
        this.routerManager = new RouterManager(kernel);

        // 添加配置的路由守卫
        if (this.config.guards) {
            this.config.guards.forEach(guard => {
                this.routerManager!.addGuard(guard);
            });
        }

        // 扩展内核方法
        this.extendKernel(kernel);

        // 自动启动
        if (this.config.autoStart) {
            this.routerManager.startListening();
        }

        logger.info(`路由插件安装完成 (模式: ${this.config.mode})`);
    }

    /**
     * 卸载插件
     */
    uninstall(kernel: MicroCoreKernel): void {
        if (this.routerManager) {
            this.routerManager.destroy();
            this.routerManager = undefined;
        }

        // 移除扩展的方法
        this.removeKernelExtensions(kernel);

        logger.info('路由插件卸载完成');
    }

    /**
     * 扩展内核方法
     */
    private extendKernel(kernel: MicroCoreKernel): void {
        // 添加路由相关方法到内核
        (kernel as any).router = {
            navigate: (path: string, replace = false) => {
                return this.routerManager?.navigate(path, replace);
            },
            goBack: () => {
                this.routerManager?.goBack();
            },
            goForward: () => {
                this.routerManager?.goForward();
            },
            getCurrentPath: () => {
                return this.routerManager?.getCurrentPath();
            },
            getHistory: () => {
                return this.routerManager?.getHistory();
            },
            addGuard: (guard: any) => {
                this.routerManager?.addGuard(guard);
            },
            removeGuard: (guard: any) => {
                this.routerManager?.removeGuard(guard);
            }
        };
    }

    /**
     * 移除内核扩展
     */
    private removeKernelExtensions(kernel: MicroCoreKernel): void {
        delete (kernel as any).router;
    }
}

/**
 * 创建路由插件实例
 */
export function createRouterPlugin(config?: RouterPluginConfig): RouterPlugin {
    return new RouterPlugin(config);
}

/**
 * 默认路由插件实例
 */
export const routerPlugin = new RouterPlugin();