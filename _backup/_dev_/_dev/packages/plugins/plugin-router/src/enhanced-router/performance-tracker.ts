/**
 * @fileoverview 路由性能跟踪器
 * @description 提供路由性能监控和统计功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { RouterPerformanceStats } from './types';

/**
 * 路由性能跟踪器
 * 负责收集和分析路由相关的性能数据
 */
export class PerformanceTracker {
    private stats: RouterPerformanceStats;
    private matchTimes: number[] = [];
    private navigationTimes: number[] = [];
    private maxHistorySize = 100;

    constructor() {
        this.stats = {
            totalMatches: 0,
            cacheHits: 0,
            avgMatchTime: 0,
            slowestMatchTime: 0,
            totalNavigations: 0,
            failedNavigations: 0
        };
    }

    /**
     * 记录路由匹配时间
     */
    recordMatchTime(time: number): void {
        this.stats.totalMatches++;
        this.matchTimes.push(time);

        // 保持历史记录大小限制
        if (this.matchTimes.length > this.maxHistorySize) {
            this.matchTimes.shift();
        }

        // 更新统计数据
        this.updateMatchTimeStats();
    }

    /**
     * 记录导航时间
     */
    recordNavigationTime(time: number): void {
        this.navigationTimes.push(time);

        // 保持历史记录大小限制
        if (this.navigationTimes.length > this.maxHistorySize) {
            this.navigationTimes.shift();
        }
    }

    /**
     * 记录缓存命中
     */
    recordCacheHit(): void {
        this.stats.cacheHits++;
    }

    /**
     * 记录导航成功
     */
    recordNavigationSuccess(): void {
        this.stats.totalNavigations++;
    }

    /**
     * 记录导航失败
     */
    recordNavigationFailure(): void {
        this.stats.totalNavigations++;
        this.stats.failedNavigations++;
    }

    /**
     * 获取性能统计数据
     */
    getStats(): RouterPerformanceStats {
        return { ...this.stats };
    }

    /**
     * 获取详细的性能报告
     */
    getDetailedReport(): {
        stats: RouterPerformanceStats;
        matchTimePercentiles: {
            p50: number;
            p90: number;
            p95: number;
            p99: number;
        };
        navigationTimePercentiles: {
            p50: number;
            p90: number;
            p95: number;
            p99: number;
        };
        cacheHitRate: number;
        navigationSuccessRate: number;
    } {
        return {
            stats: this.getStats(),
            matchTimePercentiles: this.calculatePercentiles(this.matchTimes),
            navigationTimePercentiles: this.calculatePercentiles(this.navigationTimes),
            cacheHitRate: this.stats.totalMatches > 0
                ? (this.stats.cacheHits / this.stats.totalMatches) * 100
                : 0,
            navigationSuccessRate: this.stats.totalNavigations > 0
                ? ((this.stats.totalNavigations - this.stats.failedNavigations) / this.stats.totalNavigations) * 100
                : 0
        };
    }

    /**
     * 重置统计数据
     */
    reset(): void {
        this.stats = {
            totalMatches: 0,
            cacheHits: 0,
            avgMatchTime: 0,
            slowestMatchTime: 0,
            totalNavigations: 0,
            failedNavigations: 0
        };
        this.matchTimes.length = 0;
        this.navigationTimes.length = 0;
    }

    /**
     * 更新匹配时间统计
     */
    private updateMatchTimeStats(): void {
        if (this.matchTimes.length === 0) return;

        const sum = this.matchTimes.reduce((a, b) => a + b, 0);
        this.stats.avgMatchTime = sum / this.matchTimes.length;
        this.stats.slowestMatchTime = Math.max(...this.matchTimes);
    }

    /**
     * 计算百分位数
     */
    private calculatePercentiles(values: number[]): {
        p50: number;
        p90: number;
        p95: number;
        p99: number;
    } {
        if (values.length === 0) {
            return { p50: 0, p90: 0, p95: 0, p99: 0 };
        }

        const sorted = [...values].sort((a, b) => a - b);
        const getPercentile = (p: number) => {
            const index = Math.ceil((p / 100) * sorted.length) - 1;
            return sorted[Math.max(0, index)];
        };

        return {
            p50: getPercentile(50),
            p90: getPercentile(90),
            p95: getPercentile(95),
            p99: getPercentile(99)
        };
    }

    /**
     * 创建性能监控装饰器
     */
    static createPerformanceDecorator<T extends (...args: any[]) => any>(
        tracker: PerformanceTracker,
        recordType: 'match' | 'navigation'
    ) {
        return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
            const originalMethod = descriptor.value;

            descriptor.value = async function (...args: any[]) {
                const startTime = performance.now();
                try {
                    const result = await originalMethod.apply(this, args);
                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    if (recordType === 'match') {
                        tracker.recordMatchTime(duration);
                    } else if (recordType === 'navigation') {
                        tracker.recordNavigationTime(duration);
                        tracker.recordNavigationSuccess();
                    }

                    return result;
                } catch (error) {
                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    if (recordType === 'navigation') {
                        tracker.recordNavigationTime(duration);
                        tracker.recordNavigationFailure();
                    }

                    throw error;
                }
            };

            return descriptor;
        };
    }

    /**
     * 创建性能监控中间件
     */
    static createPerformanceMiddleware(tracker: PerformanceTracker) {
        return {
            beforeMatch: () => performance.now(),
            afterMatch: (startTime: number, success: boolean) => {
                const duration = performance.now() - startTime;
                tracker.recordMatchTime(duration);
            },
            beforeNavigation: () => performance.now(),
            afterNavigation: (startTime: number, success: boolean) => {
                const duration = performance.now() - startTime;
                tracker.recordNavigationTime(duration);
                if (success) {
                    tracker.recordNavigationSuccess();
                } else {
                    tracker.recordNavigationFailure();
                }
            }
        };
    }

    /**
     * 生成性能报告
     */
    generateReport(): string {
        const report = this.getDetailedReport();

        return `
路由性能报告
====================
总匹配次数: ${report.stats.totalMatches}
缓存命中次数: ${report.stats.cacheHits}
缓存命中率: ${report.cacheHitRate.toFixed(2)}%
平均匹配时间: ${report.stats.avgMatchTime.toFixed(2)}ms
最慢匹配时间: ${report.stats.slowestMatchTime.toFixed(2)}ms

匹配时间百分位数:
  P50: ${report.matchTimePercentiles.p50.toFixed(2)}ms
  P90: ${report.matchTimePercentiles.p90.toFixed(2)}ms
  P95: ${report.matchTimePercentiles.p95.toFixed(2)}ms
  P99: ${report.matchTimePercentiles.p99.toFixed(2)}ms

总导航次数: ${report.stats.totalNavigations}
失败导航次数: ${report.stats.failedNavigations}
导航成功率: ${report.navigationSuccessRate.toFixed(2)}%

导航时间百分位数:
  P50: ${report.navigationTimePercentiles.p50.toFixed(2)}ms
  P90: ${report.navigationTimePercentiles.p90.toFixed(2)}ms
  P95: ${report.navigationTimePercentiles.p95.toFixed(2)}ms
  P99: ${report.navigationTimePercentiles.p99.toFixed(2)}ms
        `.trim();
    }

    /**
     * 设置历史记录大小限制
     */
    setMaxHistorySize(size: number): void {
        this.maxHistorySize = size;

        // 调整现有数据
        while (this.matchTimes.length > size) {
            this.matchTimes.shift();
        }
        while (this.navigationTimes.length > size) {
            this.navigationTimes.shift();
        }
    }

    /**
     * 获取当前历史记录大小
     */
    getMaxHistorySize(): number {
        return this.maxHistorySize;
    }
}