/**
 * @fileoverview 路由树管理器
 * @description 提供基于树结构的高效路由匹配功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type {
    EnhancedRouteConfig,
    RouteMatch,
    RouteTreeNode,
    SegmentParseResult
} from './types';

/**
 * 路由树管理器
 * 负责构建和维护路由树结构，提供高效的路由匹配
 */
export class RouteTreeManager {
    private rootNode: RouteTreeNode;

    constructor() {
        this.rootNode = this.createRootNode();
    }

    /**
     * 创建根节点
     */
    private createRootNode(): RouteTreeNode {
        return {
            segment: '',
            isParam: false,
            children: new Map(),
        };
    }

    /**
     * 添加路由到树中
     */
    addRoute(route: EnhancedRouteConfig): void {
        const segments = this.parsePathSegments(route.path);
        let currentNode = this.rootNode;

        for (const segment of segments) {
            const { key, isParam, paramName } = this.parseSegment(segment);

            if (!currentNode.children.has(key)) {
                currentNode.children.set(key, {
                    segment: key,
                    isParam,
                    paramName,
                    children: new Map(),
                });
            }

            currentNode = currentNode.children.get(key)!;
        }

        currentNode.route = route;
    }

    /**
     * 从树中移除路由
     */
    removeRoute(route: EnhancedRouteConfig): void {
        const segments = this.parsePathSegments(route.path);
        const nodePath: RouteTreeNode[] = [this.rootNode];
        let currentNode = this.rootNode;

        // 找到路由节点的路径
        for (const segment of segments) {
            const { key } = this.parseSegment(segment);
            const childNode = currentNode.children.get(key);
            if (!childNode) return;

            nodePath.push(childNode);
            currentNode = childNode;
        }

        // 从叶子节点移除路由
        currentNode.route = undefined;

        // 从叶子到根清理空节点
        for (let i = nodePath.length - 1; i > 0; i--) {
            const node = nodePath[i];
            const parent = nodePath[i - 1];

            // 如果节点没有路由且没有子节点，则移除它
            if (!node.route && node.children.size === 0) {
                const key = node.segment;
                parent.children.delete(key);
            } else {
                break; // 如果找到应该保留的节点则停止
            }
        }
    }

    /**
     * 在树中匹配路由
     */
    matchRoute(path: string): RouteMatch | null {
        const segments = this.parsePathSegments(path);
        const params: Record<string, string> = {};
        const query = this.parseQuery(path);

        const match = this.matchSegments(this.rootNode, segments, 0, params);

        if (match) {
            return {
                route: match,
                params,
                query,
                score: this.calculateMatchScore(match, path),
                path
            };
        }

        return null;
    }

    /**
     * 递归匹配段
     */
    private matchSegments(
        node: RouteTreeNode,
        segments: string[],
        index: number,
        params: Record<string, string>
    ): EnhancedRouteConfig | null {
        // 如果已匹配所有段
        if (index >= segments.length) {
            return node.route || null;
        }

        const segment = segments[index];

        // 首先尝试精确匹配
        const exactChild = node.children.get(segment);
        if (exactChild) {
            const result = this.matchSegments(exactChild, segments, index + 1, params);
            if (result) return result;
        }

        // 尝试参数匹配
        for (const [key, child] of node.children) {
            if (child.isParam && child.paramName) {
                params[child.paramName] = segment;
                const result = this.matchSegments(child, segments, index + 1, params);
                if (result) return result;
                delete params[child.paramName]; // 回溯
            }
        }

        // 尝试通配符匹配
        if (node.wildcardChild) {
            const result = this.matchSegments(node.wildcardChild, segments, segments.length, params);
            if (result) return result;
        }

        return null;
    }

    /**
     * 解析路径段
     */
    private parsePathSegments(path: string): string[] {
        return path.split('/').filter(segment => segment.length > 0);
    }

    /**
     * 解析段以提取参数信息
     */
    private parseSegment(segment: string): SegmentParseResult {
        if (segment.startsWith(':')) {
            return {
                key: ':param',
                isParam: true,
                paramName: segment.slice(1)
            };
        }

        if (segment === '*') {
            return {
                key: '*',
                isParam: false
            };
        }

        return {
            key: segment,
            isParam: false
        };
    }

    /**
     * 解析查询参数
     */
    private parseQuery(path: string): Record<string, string> {
        const queryIndex = path.indexOf('?');
        if (queryIndex === -1) return {};

        const queryString = path.slice(queryIndex + 1);
        const params: Record<string, string> = {};

        queryString.split('&').forEach(param => {
            const [key, value] = param.split('=');
            if (key) {
                params[decodeURIComponent(key)] = decodeURIComponent(value || '');
            }
        });

        return params;
    }

    /**
     * 计算匹配分数
     */
    private calculateMatchScore(route: EnhancedRouteConfig, path: string): number {
        let score = route.priority || 0;

        // 精确匹配获得更高分数
        if (route.exact && route.path === path) {
            score += 100;
        }

        // 更长的路径获得更高分数
        score += route.path.length;

        return score;
    }

    /**
     * 清空路由树
     */
    clear(): void {
        this.rootNode = this.createRootNode();
    }

    /**
     * 获取根节点（用于调试）
     */
    getRootNode(): RouteTreeNode {
        return this.rootNode;
    }

    /**
     * 获取树的统计信息
     */
    getTreeStats(): {
        totalNodes: number;
        totalRoutes: number;
        maxDepth: number;
    } {
        let totalNodes = 0;
        let totalRoutes = 0;
        let maxDepth = 0;

        const traverse = (node: RouteTreeNode, depth: number) => {
            totalNodes++;
            if (node.route) totalRoutes++;
            maxDepth = Math.max(maxDepth, depth);

            for (const child of node.children.values()) {
                traverse(child, depth + 1);
            }
        };

        traverse(this.rootNode, 0);

        return {
            totalNodes,
            totalRoutes,
            maxDepth
        };
    }
}