/**
 * @fileoverview 路由守卫管理器
 * @description 提供路由守卫功能，控制路由导航的访问权限
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { RouteGuard, RouteMatch } from './types';

/**
 * 路由守卫管理器
 * 负责管理全局和路由级别的守卫，控制导航访问
 */
export class RouteGuardManager {
    private globalGuards: RouteGuard[] = [];

    /**
     * 添加全局路由守卫
     */
    addGlobalGuard(guard: RouteGuard): void {
        this.globalGuards.push(guard);
        console.log('[RouteGuardManager] 全局守卫已添加');
    }

    /**
     * 移除全局路由守卫
     */
    removeGlobalGuard(guard: RouteGuard): void {
        const index = this.globalGuards.indexOf(guard);
        if (index > -1) {
            this.globalGuards.splice(index, 1);
            console.log('[RouteGuardManager] 全局守卫已移除');
        }
    }

    /**
     * 清空所有全局守卫
     */
    clearGlobalGuards(): void {
        this.globalGuards.length = 0;
        console.log('[RouteGuardManager] 所有全局守卫已清空');
    }

    /**
     * 获取所有全局守卫
     */
    getGlobalGuards(): RouteGuard[] {
        return [...this.globalGuards];
    }

    /**
     * 运行路由守卫
     */
    async runGuards(to: RouteMatch, from: RouteMatch | null): Promise<boolean | string> {
        // 运行全局守卫
        for (const guard of this.globalGuards) {
            const result = await this.executeGuard(guard, to, from);
            if (result !== true) {
                return result;
            }
        }

        // 运行路由特定守卫
        if (to.route.guards) {
            for (const guard of to.route.guards) {
                const result = await this.executeGuard(guard, to, from);
                if (result !== true) {
                    return result;
                }
            }
        }

        return true;
    }

    /**
     * 执行单个守卫
     */
    private async executeGuard(
        guard: RouteGuard,
        to: RouteMatch,
        from: RouteMatch | null
    ): Promise<boolean | string> {
        try {
            const result = await guard(to, from);
            return result;
        } catch (error) {
            console.error('[RouteGuardManager] 守卫执行失败:', error);
            return `守卫执行失败: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * 创建常用的守卫函数
     */
    static createAuthGuard(checkAuth: () => boolean | Promise<boolean>): RouteGuard {
        return async (to, from) => {
            const isAuthenticated = await checkAuth();
            if (!isAuthenticated) {
                return '需要登录才能访问此页面';
            }
            return true;
        };
    }

    /**
     * 创建权限守卫
     */
    static createPermissionGuard(
        requiredPermissions: string[],
        checkPermission: (permission: string) => boolean | Promise<boolean>
    ): RouteGuard {
        return async (to, from) => {
            for (const permission of requiredPermissions) {
                const hasPermission = await checkPermission(permission);
                if (!hasPermission) {
                    return `缺少必要权限: ${permission}`;
                }
            }
            return true;
        };
    }

    /**
     * 创建角色守卫
     */
    static createRoleGuard(
        requiredRoles: string[],
        getUserRoles: () => string[] | Promise<string[]>
    ): RouteGuard {
        return async (to, from) => {
            const userRoles = await getUserRoles();
            const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

            if (!hasRequiredRole) {
                return `需要以下角色之一: ${requiredRoles.join(', ')}`;
            }
            return true;
        };
    }

    /**
     * 创建条件守卫
     */
    static createConditionalGuard(
        condition: (to: RouteMatch, from: RouteMatch | null) => boolean | Promise<boolean>,
        message: string = '不满足访问条件'
    ): RouteGuard {
        return async (to, from) => {
            const result = await condition(to, from);
            return result ? true : message;
        };
    }

    /**
     * 创建重定向守卫
     */
    static createRedirectGuard(
        condition: (to: RouteMatch, from: RouteMatch | null) => boolean | Promise<boolean>,
        redirectPath: string
    ): RouteGuard {
        return async (to, from) => {
            const shouldRedirect = await condition(to, from);
            return shouldRedirect ? redirectPath : true;
        };
    }

    /**
     * 创建确认守卫（需要用户确认）
     */
    static createConfirmGuard(
        getMessage: (to: RouteMatch, from: RouteMatch | null) => string,
        confirm: (message: string) => boolean | Promise<boolean> = (msg) => window.confirm(msg)
    ): RouteGuard {
        return async (to, from) => {
            const message = getMessage(to, from);
            const confirmed = await confirm(message);
            return confirmed ? true : '用户取消了导航';
        };
    }

    /**
     * 创建延迟守卫（用于测试或特殊场景）
     */
    static createDelayGuard(delay: number): RouteGuard {
        return async (to, from) => {
            await new Promise(resolve => setTimeout(resolve, delay));
            return true;
        };
    }

    /**
     * 组合多个守卫（所有守卫都必须通过）
     */
    static combineGuards(...guards: RouteGuard[]): RouteGuard {
        return async (to, from) => {
            for (const guard of guards) {
                const result = await guard(to, from);
                if (result !== true) {
                    return result;
                }
            }
            return true;
        };
    }

    /**
     * 创建或守卫（任一守卫通过即可）
     */
    static createOrGuard(...guards: RouteGuard[]): RouteGuard {
        return async (to, from) => {
            const results: (boolean | string)[] = [];

            for (const guard of guards) {
                const result = await guard(to, from);
                if (result === true) {
                    return true;
                }
                results.push(result);
            }

            return `所有守卫都未通过: ${results.join('; ')}`;
        };
    }

    /**
     * 获取守卫统计信息
     */
    getStats(): {
        globalGuardsCount: number;
        totalGuardsExecuted: number;
        averageExecutionTime: number;
    } {
        return {
            globalGuardsCount: this.globalGuards.length,
            totalGuardsExecuted: 0, // 这里可以添加执行计数逻辑
            averageExecutionTime: 0 // 这里可以添加执行时间统计逻辑
        };
    }
}