/**
 * 路由同步核心 - 实现主子应用路由状态同步
 */

import type { MicroCoreKernel } from '@micro-core/core';
import type { HistoryAdapter } from './history-adapter';

export interface RouterSyncOptions {
    syncDelay?: number;
    enableDebug?: boolean;
}

export interface AppRouteInfo {
    name: string;
    activeWhen: string | RegExp | ((location: Location) => boolean);
    path?: string;
    exact?: boolean;
}

export class RouterSync {
    private kernel: MicroCoreKernel;
    private historyAdapter: HistoryAdapter;
    private options: RouterSyncOptions;
    private registeredApps = new Map<string, AppRouteInfo>();
    private currentActiveApp: string | null = null;
    private syncTimer?: number;

    constructor(
        kernel: MicroCoreKernel,
        historyAdapter: HistoryAdapter,
        options: RouterSyncOptions = {}
    ) {
        this.kernel = kernel;
        this.historyAdapter = historyAdapter;
        this.options = {
            syncDelay: 0,
            enableDebug: false,
            ...options
        };

        this.init();
    }

    /**
     * 初始化路由同步
     */
    private init(): void {
        // 监听路由变化
        this.historyAdapter.onRouteChange((location) => {
            this.handleRouteChange(location);
        });

        // 初始路由检查
        this.handleRouteChange(window.location);

        this.debug('路由同步器已初始化');
    }

    /**
     * 注册应用路由信息
     */
    registerApp(app: any): void {
        const routeInfo: AppRouteInfo = {
            name: app.name,
            activeWhen: app.activeWhen || app.path,
            path: app.path,
            exact: app.exact
        };

        this.registeredApps.set(app.name, routeInfo);
        this.debug(`注册应用路由: ${app.name}`, routeInfo);

        // 重新检查当前路由
        this.handleRouteChange(window.location);
    }

    /**
     * 注销应用路由信息
     */
    unregisterApp(appName: string): void {
        if (this.registeredApps.has(appName)) {
            this.registeredApps.delete(appName);
            this.debug(`注销应用路由: ${appName}`);

            // 如果当前激活的应用被注销，重新检查路由
            if (this.currentActiveApp === appName) {
                this.currentActiveApp = null;
                this.handleRouteChange(window.location);
            }
        }
    }

    /**
     * 处理路由变化
     */
    private handleRouteChange(location: Location): void {
        if (this.syncTimer) {
            clearTimeout(this.syncTimer);
        }

        this.syncTimer = window.setTimeout(() => {
            this.doRouteSync(location);
        }, this.options.syncDelay);
    }

    /**
     * 执行路由同步
     */
    private doRouteSync(location: Location): void {
        const matchedApp = this.findMatchedApp(location);

        this.debug(`路由同步检查: ${location.pathname}`, {
            matchedApp,
            currentActiveApp: this.currentActiveApp
        });

        // 如果匹配的应用发生变化
        if (matchedApp !== this.currentActiveApp) {
            const previousApp = this.currentActiveApp;
            this.currentActiveApp = matchedApp;

            // 卸载之前的应用
            if (previousApp) {
                this.kernel.emit('router:app-deactivate', {
                    appName: previousApp,
                    location
                });
            }

            // 激活新的应用
            if (matchedApp) {
                this.kernel.emit('router:app-activate', {
                    appName: matchedApp,
                    location
                });
            }

            // 发送路由变化事件
            this.kernel.emit('router:change', {
                location,
                previousApp,
                currentApp: matchedApp,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 查找匹配的应用
     */
    private findMatchedApp(location: Location): string | null {
        for (const [appName, routeInfo] of this.registeredApps) {
            if (this.isAppMatched(routeInfo, location)) {
                return appName;
            }
        }
        return null;
    }

    /**
     * 检查应用是否匹配当前路由
     */
    private isAppMatched(routeInfo: AppRouteInfo, location: Location): boolean {
        const { activeWhen, exact = false } = routeInfo;
        const pathname = location.pathname;

        if (typeof activeWhen === 'function') {
            return activeWhen(location);
        } else if (activeWhen instanceof RegExp) {
            return activeWhen.test(pathname);
        } else if (typeof activeWhen === 'string') {
            if (exact) {
                return pathname === activeWhen;
            } else {
                return pathname.startsWith(activeWhen);
            }
        }

        return false;
    }

    /**
     * 获取当前激活的应用
     */
    getCurrentActiveApp(): string | null {
        return this.currentActiveApp;
    }

    /**
     * 获取已注册应用数量
     */
    getRegisteredAppsCount(): number {
        return this.registeredApps.size;
    }

    /**
     * 获取已注册的应用列表
     */
    getRegisteredApps(): AppRouteInfo[] {
        return Array.from(this.registeredApps.values());
    }

    /**
     * 强制重新检查路由
     */
    forceSync(): void {
        this.handleRouteChange(window.location);
    }

    /**
     * 销毁路由同步器
     */
    destroy(): void {
        if (this.syncTimer) {
            clearTimeout(this.syncTimer);
            this.syncTimer = undefined;
        }

        this.registeredApps.clear();
        this.currentActiveApp = null;

        this.debug('路由同步器已销毁');
    }

    /**
     * 调试日志
     */
    private debug(message: string, data?: any): void {
        if (this.options.enableDebug) {
            console.log(`[RouterSync] ${message}`, data || '');
        }
    }

    /**
     * 获取统计信息
     */
    getStats(): {
        registeredApps: number;
        currentActiveApp: string | null;
        syncDelay: number;
    } {
        return {
            registeredApps: this.registeredApps.size,
            currentActiveApp: this.currentActiveApp,
            syncDelay: this.options.syncDelay || 0
        };
    }
}