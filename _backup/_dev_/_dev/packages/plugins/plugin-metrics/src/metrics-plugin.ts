import type { Plugin } from '@micro-core/core';
import { createMetricsCollector } from './metrics-collector';
import type { MetricsPluginOptions } from './types';

/**
 * 性能监控插件
 * 提供微前端应用的性能指标收集和分析功能
 */
export class MetricsPlugin implements Plugin {
    name = 'metrics';
    private options: MetricsPluginOptions;
    private collector: ReturnType<typeof createMetricsCollector>;

    constructor(options: MetricsPluginOptions = {}) {
        this.options = {
            enablePerformance: true,
            enableMemory: true,
            enableNetwork: false,
            reportInterval: 30000, // 30秒
            maxMetricsSize: 1000,
            ...options
        };

        this.collector = createMetricsCollector(this.options);
    }

    /**
     * 插件安装
     */
    install(kernel: any) {
        // 将性能监控器注入到内核中
        kernel.metrics = this.collector;

        // 监听应用生命周期事件并收集性能指标
        kernel.hooks.beforeAppLoad.tap('MetricsPlugin', (appName: string) => {
            this.collector.startTiming(`${appName}_load`);
        });

        kernel.hooks.afterAppLoad.tap('MetricsPlugin', (appName: string) => {
            this.collector.endTiming(`${appName}_load`);
        });

        kernel.hooks.beforeAppMount.tap('MetricsPlugin', (appName: string) => {
            this.collector.startTiming(`${appName}_mount`);
        });

        kernel.hooks.afterAppMount.tap('MetricsPlugin', (appName: string) => {
            this.collector.endTiming(`${appName}_mount`);
            this.collector.recordEvent('app_mounted', { appName });
        });

        kernel.hooks.beforeAppUnmount.tap('MetricsPlugin', (appName: string) => {
            this.collector.startTiming(`${appName}_unmount`);
        });

        kernel.hooks.afterAppUnmount.tap('MetricsPlugin', (appName: string) => {
            this.collector.endTiming(`${appName}_unmount`);
            this.collector.recordEvent('app_unmounted', { appName });
        });

        // 监听错误事件
        kernel.hooks.appError.tap('MetricsPlugin', (error: Error, appName: string) => {
            this.collector.recordEvent('app_error', {
                appName,
                error: error.message,
                stack: error.stack
            });
        });

        // 启动性能监控
        this.collector.start();

        console.log('[MetricsPlugin] 性能监控插件已安装');
    }

    /**
     * 插件卸载
     */
    uninstall() {
        // 停止性能监控
        this.collector.stop();
        console.log('[MetricsPlugin] 性能监控插件已卸载');
    }

    /**
     * 获取性能指标
     */
    getMetrics() {
        return this.collector.getMetrics();
    }

    /**
     * 获取性能报告
     */
    getReport() {
        return this.collector.generateReport();
    }
}