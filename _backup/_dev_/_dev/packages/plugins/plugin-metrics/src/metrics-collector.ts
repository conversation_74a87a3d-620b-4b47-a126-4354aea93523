import type { MemoryMetrics, MetricEntry, MetricsCollectorOptions, PerformanceMetrics } from './types';

/**
 * 创建性能指标收集器
 */
export function createMetricsCollector(options: MetricsCollectorOptions) {
    const metrics: MetricEntry[] = [];
    const timings = new Map<string, number>();
    let reportTimer: NodeJS.Timeout | null = null;

    const { 
        enablePerformance, 
        enableMemory, 
        enableNetwork, 
        reportInterval, 
        maxMetricsSize,
        reportUrl 
    } = options;

    /**
     * 记录指标
     */
    function recordMetric(name: string, value: number, type: string, tags?: Record<string, any>) {
        const metric: MetricEntry = {
            name,
            value,
            type,
            tags: tags || {},
            timestamp: Date.now()
        };

        metrics.push(metric);

        // 限制指标缓存大小
        if (metrics.length > maxMetricsSize) {
            metrics.shift();
        }
    }

    /**
     * 开始计时
     */
    function startTiming(name: string) {
        timings.set(name, performance.now());
    }

    /**
     * 结束计时
     */
    function endTiming(name: string) {
        const startTime = timings.get(name);
        if (startTime !== undefined) {
            const duration = performance.now() - startTime;
            recordMetric(name, duration, 'timing');
            timings.delete(name);
            return duration;
        }
        return 0;
    }

    /**
     * 记录事件
     */
    function recordEvent(name: string, data?: Record<string, any>) {
        recordMetric(name, 1, 'event', data);
    }

    /**
     * 收集性能指标
     */
    function collectPerformanceMetrics(): PerformanceMetrics | null {
        if (!enablePerformance || !window.performance) {
            return null;
        }

        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (!navigation) return null;

        return {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            firstPaint: getFirstPaint(),
            firstContentfulPaint: getFirstContentfulPaint(),
            largestContentfulPaint: getLargestContentfulPaint()
        };
    }

    /**
     * 收集内存指标
     */
    function collectMemoryMetrics(): MemoryMetrics | null {
        if (!enableMemory || !(performance as any).memory) {
            return null;
        }

        const memory = (performance as any).memory;
        return {
            usedJSHeapSize: memory.usedJSHeapSize,
            totalJSHeapSize: memory.totalJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit
        };
    }

    /**
     * 获取首次绘制时间
     */
    function getFirstPaint(): number {
        const paintEntries = performance.getEntriesByType('paint');
        const fpEntry = paintEntries.find(entry => entry.name === 'first-paint');
        return fpEntry ? fpEntry.startTime : 0;
    }

    /**
     * 获取首次内容绘制时间
     */
    function getFirstContentfulPaint(): number {
        const paintEntries = performance.getEntriesByType('paint');
        const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        return fcpEntry ? fcpEntry.startTime : 0;
    }

    /**
     * 获取最大内容绘制时间
     */
    function getLargestContentfulPaint(): number {
        return new Promise((resolve) => {
            if ('PerformanceObserver' in window) {
                const observer = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    resolve(lastEntry.startTime);
                });
                observer.observe({ entryTypes: ['largest-contentful-paint'] });
                
                // 超时处理
                setTimeout(() => resolve(0), 5000);
            } else {
                resolve(0);
            }
        }) as any;
    }

    /**
     * 定期收集和上报指标
     */
    function collectAndReport() {
        // 收集性能指标
        const perfMetrics = collectPerformanceMetrics();
        if (perfMetrics) {
            Object.entries(perfMetrics).forEach(([key, value]) => {
                recordMetric(`performance_${key}`, value, 'performance');
            });
        }

        // 收集内存指标
        const memMetrics = collectMemoryMetrics();
        if (memMetrics) {
            Object.entries(memMetrics).forEach(([key, value]) => {
                recordMetric(`memory_${key}`, value, 'memory');
            });
        }

        // 上报到远程服务器
        if (reportUrl) {
            sendMetricsReport();
        }
    }

    /**
     * 发送指标报告
     */
    async function sendMetricsReport() {
        if (!reportUrl) return;

        try {
            const report = generateReport();
            await fetch(reportUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(report)
            });
        } catch (error) {
            console.warn('[MetricsCollector] 指标上报失败:', error);
        }
    }

    /**
     * 生成性能报告
     */
    function generateReport() {
        const now = Date.now();
        const recentMetrics = metrics.filter(m => now - m.timestamp < reportInterval);

        return {
            timestamp: now,
            metrics: recentMetrics,
            summary: {
                totalMetrics: metrics.length,
                recentMetrics: recentMetrics.length,
                avgLoadTime: calculateAverage(recentMetrics.filter(m => m.name.includes('_load'))),
                avgMountTime: calculateAverage(recentMetrics.filter(m => m.name.includes('_mount'))),
                errorCount: recentMetrics.filter(m => m.name === 'app_error').length
            }
        };
    }

    /**
     * 计算平均值
     */
    function calculateAverage(metrics: MetricEntry[]): number {
        if (metrics.length === 0) return 0;
        const sum = metrics.reduce((acc, m) => acc + m.value, 0);
        return sum / metrics.length;
    }

    return {
        /**
         * 启动指标收集
         */
        start() {
            if (reportTimer) return;
            
            reportTimer = setInterval(collectAndReport, reportInterval);
            console.log('[MetricsCollector] 性能监控已启动');
        },

        /**
         * 停止指标收集
         */
        stop() {
            if (reportTimer) {
                clearInterval(reportTimer);
                reportTimer = null;
            }
            console.log('[MetricsCollector] 性能监控已停止');
        },

        startTiming,
        endTiming,
        recordEvent,
        recordMetric,

        /**
         * 获取所有指标
         */
        getMetrics: () => [...metrics],

        /**
         * 清空指标
         */
        clear: () => {
            metrics.length = 0;
            timings.clear();
        },

        generateReport
    };
}