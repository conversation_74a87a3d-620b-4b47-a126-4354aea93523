/**
 * Worker 脚本 - 在 Web Worker 中执行的资源加载逻辑
 */

interface WorkerMessage {
    type: 'load' | 'result' | 'progress' | 'error';
    data: any;
    taskId?: string;
}

interface LoadTask {
    id: string;
    url: string;
    type: 'script' | 'style' | 'json' | 'text' | 'blob';
    priority: 'high' | 'normal' | 'low';
    timeout?: number;
}

// Worker 全局作用域
declare const self: DedicatedWorkerGlobalScope;

/**
 * 发送消息到主线程
 */
function postMessage(message: WorkerMessage): void {
    self.postMessage(message);
}

/**
 * 发送进度信息
 */
function sendProgress(taskId: string, loaded: number, total: number): void {
    postMessage({
        type: 'progress',
        taskId,
        data: {
            taskId,
            loaded,
            total,
            percentage: total > 0 ? Math.round((loaded / total) * 100) : 0
        }
    });
}

/**
 * 发送错误信息
 */
function sendError(taskId: string, error: string): void {
    postMessage({
        type: 'error',
        taskId,
        data: { error }
    });
}

/**
 * 发送结果
 */
function sendResult(taskId: string, content: string | ArrayBuffer, loadTime: number, size: number): void {
    postMessage({
        type: 'result',
        taskId,
        data: {
            id: taskId,
            success: true,
            content,
            loadTime,
            size
        }
    });
}

/**
 * 加载资源
 */
async function loadResource(task: LoadTask): Promise<void> {
    const startTime = Date.now();
    const controller = new AbortController();

    // 设置超时
    const timeoutId = setTimeout(() => {
        controller.abort();
    }, task.timeout || 30000);

    try {
        const response = await fetch(task.url, {
            signal: controller.signal,
            headers: {
                'Cache-Control': 'no-cache'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentLength = response.headers.get('content-length');
        const total = contentLength ? parseInt(contentLength, 10) : 0;
        let loaded = 0;

        // 创建可读流读取器
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('无法创建响应流读取器');
        }

        const chunks: Uint8Array[] = [];

        while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            chunks.push(value);
            loaded += value.length;

            // 发送进度更新
            if (total > 0) {
                sendProgress(task.id, loaded, total);
            }
        }

        // 合并所有数据块
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        // 根据资源类型处理内容
        let content: string | ArrayBuffer;

        switch (task.type) {
            case 'script':
            case 'style':
            case 'json':
            case 'text':
                content = new TextDecoder().decode(result);
                break;
            case 'blob':
            default:
                content = result.buffer;
                break;
        }

        const loadTime = Date.now() - startTime;
        sendResult(task.id, content, loadTime, totalLength);

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        sendError(task.id, errorMessage);
    } finally {
        clearTimeout(timeoutId);
    }
}

// 监听主线程消息
self.addEventListener('message', (event: MessageEvent<WorkerMessage>) => {
    const { type, data } = event.data;

    if (type === 'load') {
        const task = data as LoadTask;
        loadResource(task).catch((error) => {
            sendError(task.id, error.message || '加载失败');
        });
    }
});

// 导出类型供 TypeScript 使用
export type { WorkerMessage, LoadTask };
