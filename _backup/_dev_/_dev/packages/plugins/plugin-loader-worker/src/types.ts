/**
 * Worker 加载器插件类型定义
 */

export interface WorkerLoaderOptions {
    /** 最大 Worker 数量 */
    maxWorkers?: number;
    /** 缓存策略 */
    cacheStrategy?: 'memory' | 'localStorage' | 'indexedDB';
    /** 启用进度跟踪 */
    enableProgressTracking?: boolean;
    /** 超时时间 (毫秒) */
    timeout?: number;
    /** 重试次数 */
    retryCount?: number;
    /** 并发限制 */
    concurrency?: number;
}

export interface LoadTask {
    /** 任务ID */
    id: string;
    /** 资源URL */
    url: string;
    /** 资源类型 */
    type: 'script' | 'style' | 'json' | 'text' | 'blob';
    /** 优先级 */
    priority: 'high' | 'normal' | 'low';
    /** 创建时间 */
    createdAt: number;
    /** 重试次数 */
    retryCount: number;
}

export interface LoadResult {
    /** 任务ID */
    id: string;
    /** 是否成功 */
    success: boolean;
    /** 资源内容 */
    content?: string | ArrayBuffer;
    /** 错误信息 */
    error?: string;
    /** 加载时间 */
    loadTime: number;
    /** 资源大小 */
    size: number;
}

export interface WorkerMessage {
    /** 消息类型 */
    type: 'load' | 'result' | 'progress' | 'error';
    /** 消息数据 */
    data: any;
    /** 任务ID */
    taskId?: string;
}

export interface ProgressInfo {
    /** 任务ID */
    taskId: string;
    /** 已加载字节数 */
    loaded: number;
    /** 总字节数 */
    total: number;
    /** 进度百分比 */
    percentage: number;
}

export interface CacheItem {
    /** 缓存内容 */
    content: string | ArrayBuffer;
    /** 缓存时间 */
    timestamp: number;
    /** 过期时间 */
    expireTime: number;
    /** 资源大小 */
    size: number;
    /** 资源类型 */
    type: string;
}

export interface WorkerPool {
    /** 可用的 Worker */
    available: Worker[];
    /** 忙碌的 Worker */
    busy: Map<Worker, LoadTask>;
    /** 任务队列 */
    queue: LoadTask[];
}

export interface LoaderStats {
    /** 总任务数 */
    totalTasks: number;
    /** 成功任务数 */
    successTasks: number;
    /** 失败任务数 */
    failedTasks: number;
    /** 缓存命中数 */
    cacheHits: number;
    /** 平均加载时间 */
    averageLoadTime: number;
    /** 总传输字节数 */
    totalBytes: number;
}