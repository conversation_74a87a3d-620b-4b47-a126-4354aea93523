/**
 * 缓存管理器 - 管理资源缓存
 */

import type { CacheItem, WorkerLoaderOptions } from './types';

export class CacheManager {
    private cacheStrategy: WorkerLoaderOptions['cacheStrategy'];
    private memoryCache: Map<string, CacheItem>;
    private maxCacheSize: number;
    private currentCacheSize: number;

    constructor(strategy: WorkerLoaderOptions['cacheStrategy'] = 'memory') {
        this.cacheStrategy = strategy;
        this.memoryCache = new Map();
        this.maxCacheSize = 50 * 1024 * 1024; // 50MB
        this.currentCacheSize = 0;
    }

    /**
     * 生成缓存键
     */
    private generateCacheKey(url: string): string {
        return `micro-core-worker-cache:${url}`;
    }

    /**
     * 检查缓存项是否过期
     */
    private isExpired(item: CacheItem): boolean {
        return Date.now() > item.expireTime;
    }

    /**
     * 清理过期缓存
     */
    private cleanExpiredCache(): void {
        if (this.cacheStrategy === 'memory') {
            for (const [key, item] of this.memoryCache.entries()) {
                if (this.isExpired(item)) {
                    this.currentCacheSize -= item.size;
                    this.memoryCache.delete(key);
                }
            }
        }
    }

    /**
     * 清理最旧的缓存项
     */
    private evictOldestCache(): void {
        if (this.cacheStrategy === 'memory' && this.memoryCache.size > 0) {
            const oldestKey = this.memoryCache.keys().next().value;
            const oldestItem = this.memoryCache.get(oldestKey);
            if (oldestItem) {
                this.currentCacheSize -= oldestItem.size;
                this.memoryCache.delete(oldestKey);
            }
        }
    }

    /**
     * 确保缓存空间足够
     */
    private ensureCacheSpace(size: number): void {
        // 清理过期缓存
        this.cleanExpiredCache();

        // 如果空间仍然不够，清理最旧的缓存
        while (this.currentCacheSize + size > this.maxCacheSize && this.memoryCache.size > 0) {
            this.evictOldestCache();
        }
    }

    /**
     * 获取缓存
     */
    public async get(url: string): Promise<CacheItem | null> {
        const key = this.generateCacheKey(url);

        try {
            switch (this.cacheStrategy) {
                case 'memory':
                    const memoryItem = this.memoryCache.get(key);
                    if (memoryItem && !this.isExpired(memoryItem)) {
                        return memoryItem;
                    }
                    if (memoryItem) {
                        this.memoryCache.delete(key);
                        this.currentCacheSize -= memoryItem.size;
                    }
                    return null;

                case 'localStorage':
                    const localStorageData = localStorage.getItem(key);
                    if (localStorageData) {
                        const item: CacheItem = JSON.parse(localStorageData);
                        if (!this.isExpired(item)) {
                            return item;
                        }
                        localStorage.removeItem(key);
                    }
                    return null;

                case 'indexedDB':
                    return this.getFromIndexedDB(key);

                default:
                    return null;
            }
        } catch (error) {
            console.warn('获取缓存失败:', error);
            return null;
        }
    }

    /**
     * 设置缓存
     */
    public async set(url: string, content: string | ArrayBuffer, type: string, ttl: number = 3600000): Promise<void> {
        const key = this.generateCacheKey(url);
        const size = typeof content === 'string' ? content.length : content.byteLength;

        const item: CacheItem = {
            content,
            timestamp: Date.now(),
            expireTime: Date.now() + ttl,
            size,
            type
        };

        try {
            switch (this.cacheStrategy) {
                case 'memory':
                    this.ensureCacheSpace(size);
                    this.memoryCache.set(key, item);
                    this.currentCacheSize += size;
                    break;

                case 'localStorage':
                    try {
                        const serializedItem = JSON.stringify({
                            ...item,
                            content: typeof content === 'string' ? content : Array.from(new Uint8Array(content))
                        });
                        localStorage.setItem(key, serializedItem);
                    } catch (error) {
                        // localStorage 空间不足，清理一些数据
                        this.clearOldLocalStorageCache();
                        const serializedItem = JSON.stringify({
                            ...item,
                            content: typeof content === 'string' ? content : Array.from(new Uint8Array(content))
                        });
                        localStorage.setItem(key, serializedItem);
                    }
                    break;

                case 'indexedDB':
                    await this.setToIndexedDB(key, item);
                    break;
            }
        } catch (error) {
            console.warn('设置缓存失败:', error);
        }
    }

    /**
     * 从 IndexedDB 获取缓存
     */
    private async getFromIndexedDB(key: string): Promise<CacheItem | null> {
        return new Promise((resolve) => {
            const request = indexedDB.open('MicroCoreWorkerCache', 1);

            request.onerror = () => resolve(null);

            request.onsuccess = () => {
                const db = request.result;
                const transaction = db.transaction(['cache'], 'readonly');
                const store = transaction.objectStore('cache');
                const getRequest = store.get(key);

                getRequest.onsuccess = () => {
                    const item = getRequest.result;
                    if (item && !this.isExpired(item)) {
                        resolve(item);
                    } else {
                        if (item) {
                            // 删除过期项
                            const deleteTransaction = db.transaction(['cache'], 'readwrite');
                            const deleteStore = deleteTransaction.objectStore('cache');
                            deleteStore.delete(key);
                        }
                        resolve(null);
                    }
                };

                getRequest.onerror = () => resolve(null);
            };

            request.onupgradeneeded = () => {
                const db = request.result;
                if (!db.objectStoreNames.contains('cache')) {
                    db.createObjectStore('cache');
                }
            };
        });
    }

    /**
     * 向 IndexedDB 设置缓存
     */
    private async setToIndexedDB(key: string, item: CacheItem): Promise<void> {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('MicroCoreWorkerCache', 1);

            request.onerror = () => reject(new Error('无法打开 IndexedDB'));

            request.onsuccess = () => {
                const db = request.result;
                const transaction = db.transaction(['cache'], 'readwrite');
                const store = transaction.objectStore('cache');
                const putRequest = store.put(item, key);

                putRequest.onsuccess = () => resolve();
                putRequest.onerror = () => reject(new Error('无法存储到 IndexedDB'));
            };

            request.onupgradeneeded = () => {
                const db = request.result;
                if (!db.objectStoreNames.contains('cache')) {
                    db.createObjectStore('cache');
                }
            };
        });
    }

    /**
     * 清理旧的 localStorage 缓存
     */
    private clearOldLocalStorageCache(): void {
        const keys = Object.keys(localStorage);
        const cacheKeys = keys.filter(key => key.startsWith('micro-core-worker-cache:'));

        // 按时间戳排序，删除最旧的一半
        const cacheItems = cacheKeys.map(key => {
            try {
                const item = JSON.parse(localStorage.getItem(key) || '{}');
                return { key, timestamp: item.timestamp || 0 };
            } catch {
                return { key, timestamp: 0 };
            }
        }).sort((a, b) => a.timestamp - b.timestamp);

        const toDelete = cacheItems.slice(0, Math.ceil(cacheItems.length / 2));
        toDelete.forEach(({ key }) => localStorage.removeItem(key));
    }

    /**
     * 清理所有缓存
     */
    public async clear(): Promise<void> {
        try {
            switch (this.cacheStrategy) {
                case 'memory':
                    this.memoryCache.clear();
                    this.currentCacheSize = 0;
                    break;

                case 'localStorage':
                    const keys = Object.keys(localStorage);
                    keys.filter(key => key.startsWith('micro-core-worker-cache:'))
                        .forEach(key => localStorage.removeItem(key));
                    break;

                case 'indexedDB':
                    await this.clearIndexedDB();
                    break;
            }
        } catch (error) {
            console.warn('清理缓存失败:', error);
        }
    }

    /**
     * 清理 IndexedDB 缓存
     */
    private async clearIndexedDB(): Promise<void> {
        return new Promise((resolve) => {
            const request = indexedDB.open('MicroCoreWorkerCache', 1);

            request.onsuccess = () => {
                const db = request.result;
                const transaction = db.transaction(['cache'], 'readwrite');
                const store = transaction.objectStore('cache');
                const clearRequest = store.clear();

                clearRequest.onsuccess = () => resolve();
                clearRequest.onerror = () => resolve(); // 即使失败也继续
            };

            request.onerror = () => resolve();
        });
    }

    /**
     * 获取缓存统计信息
     */
    public getCacheStats(): { size: number; count: number; strategy: string } {
        return {
            size: this.currentCacheSize,
            count: this.memoryCache.size,
            strategy: this.cacheStrategy ?? 'memory'
        };
    }

    /**
     * 检查缓存是否存在
     */
    public async has(url: string): Promise<boolean> {
        const item = await this.get(url);
        return item !== null;
    }

    /**
     * 删除指定缓存
     */
    public async delete(url: string): Promise<boolean> {
        const key = this.generateCacheKey(url);

        try {
            switch (this.cacheStrategy) {
                case 'memory':
                    const item = this.memoryCache.get(key);
                    if (item) {
                        this.currentCacheSize -= item.size;
                        return this.memoryCache.delete(key);
                    }
                    return false;

                case 'localStorage':
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        return true;
                    }
                    return false;

                case 'indexedDB':
                    return this.deleteFromIndexedDB(key);

                default:
                    return false;
            }
        } catch (error) {
            console.warn('删除缓存失败:', error);
            return false;
        }
    }

    /**
     * 从 IndexedDB 删除缓存
     */
    private async deleteFromIndexedDB(key: string): Promise<boolean> {
        return new Promise((resolve) => {
            const request = indexedDB.open('MicroCoreWorkerCache', 1);

            request.onsuccess = () => {
                const db = request.result;
                const transaction = db.transaction(['cache'], 'readwrite');
                const store = transaction.objectStore('cache');
                const deleteRequest = store.delete(key);

                deleteRequest.onsuccess = () => resolve(true);
                deleteRequest.onerror = () => resolve(false);
            };

            request.onerror = () => resolve(false);
        });
    }
}
