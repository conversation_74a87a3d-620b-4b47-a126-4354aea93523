/**
 * Worker 加载器插件
 * 利用 Web Worker 进行高性能资源加载
 */

import type { MicroCoreKernel, Plugin } from '@micro-core/core';
import { CacheManager } from './cache-manager';
import type { LoadProgress, LoadResult, LoadTask, LoaderStats, WorkerLoaderConfig } from './types';
import { WorkerManager } from './worker-manager';

/**
 * Worker 加载器插件类
 */
export class WorkerLoaderPlugin implements Plugin {
    name = 'worker-loader';
    version = '0.1.0';

    private workerManager: WorkerManager | null = null;
    private cacheManager: CacheManager | null = null;
    private config: Required<WorkerLoaderConfig>;
    private stats: LoaderStats = {
        totalTasks: 0,
        successTasks: 0,
        failedTasks: 0,
        averageLoadTime: 0,
        totalDataLoaded: 0,
        cacheHitRate: 0,
        workerUtilization: 0,
        errorRate: 0
    };
    private loadTimes: number[] = [];

    constructor(config: WorkerLoaderConfig = {}) {
        this.config = {
            maxWorkers: 4,
            workerScript: '',
            cacheStrategy: 'memory',
            cacheSize: 50,
            timeout: 30000,
            enableProgressTracking: true,
            enableRetry: true,
            maxRetries: 3,
            retryDelay: 1000,
            ...config
        };
    }

    /**
     * 插件安装
     */
    install(kernel: MicroCoreKernel): void {
        // 初始化 Worker 管理器
        this.workerManager = new WorkerManager(this.config);

        // 初始化缓存管理器
        this.cacheManager = new CacheManager(this.config);

        // 设置事件监听器
        this.setupEventListeners();

        // 注册到内核
        kernel.registerPlugin(this);

        // 添加加载方法到内核
        (kernel as any).loadResourceWithWorker = this.loadResource.bind(this);
        (kernel as any).preloadResourcesWithWorker = this.preloadResources.bind(this);
    }

    /**
     * 插件卸载
     */
    uninstall(kernel: MicroCoreKernel): void {
        if (this.workerManager) {
            this.workerManager.destroy();
            this.workerManager = null;
        }

        if (this.cacheManager) {
            this.cacheManager.destroy();
            this.cacheManager = null;
        }

        // 从内核移除方法
        delete (kernel as any).loadResourceWithWorker;
        delete (kernel as any).preloadResourcesWithWorker;
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        if (!this.workerManager) return;

        this.workerManager.on('taskStart', (task) => {
            this.stats.totalTasks++;
        });

        this.workerManager.on('taskComplete', (result) => {
            if (result.success) {
                this.stats.successTasks++;
                this.stats.totalDataLoaded += result.size;
                this.loadTimes.push(result.loadTime);

                // 更新平均加载时间
                this.stats.averageLoadTime = this.loadTimes.reduce((sum, time) => sum + time, 0) / this.loadTimes.length;

                // 限制历史记录长度
                if (this.loadTimes.length > 100) {
                    this.loadTimes = this.loadTimes.slice(-50);
                }
            } else {
                this.stats.failedTasks++;
            }

            // 更新错误率
            this.stats.errorRate = this.stats.failedTasks / this.stats.totalTasks;
        });

        this.workerManager.on('cacheHit', () => {
            // 更新缓存命中率
            const totalCacheRequests = this.stats.totalTasks;
            if (totalCacheRequests > 0) {
                // 这里需要跟踪缓存命中次数
                // 暂时使用简化计算
            }
        });
    }

    /**
     * 加载单个资源
     */
    async loadResource(
        url: string,
        type: 'script' | 'style' | 'json' | 'text' | 'blob' = 'text',
        options: {
            priority?: 'high' | 'medium' | 'low';
            cacheKey?: string;
            trackProgress?: boolean;
            onProgress?: (progress: LoadProgress) => void;
        } = {}
    ): Promise<LoadResult> {
        if (!this.workerManager) {
            throw new Error('Worker 管理器未初始化');
        }

        const {
            priority = 'medium',
            cacheKey = url,
            trackProgress = this.config.enableProgressTracking,
            onProgress
        } = options;

        // 检查缓存
        if (this.cacheManager && this.config.cacheStrategy !== 'none') {
            const cachedItem = await this.cacheManager.get(cacheKey);
            if (cachedItem) {
                return {
                    taskId: `cache-${Date.now()}`,
                    success: true,
                    data: cachedItem.data,
                    loadTime: 0,
                    size: cachedItem.size,
                    fromCache: true
                };
            }
        }

        // 创建加载任务
        const task: LoadTask = {
            id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            url,
            type,
            priority,
            cacheKey,
            trackProgress
        };

        try {
            // 使用 Worker 加载
            const result = await this.workerManager.loadResource(task, onProgress);

            // 缓存结果
            if (result.success && this.cacheManager && this.config.cacheStrategy !== 'none') {
                await this.cacheManager.set(cacheKey, result.data);
            }

            return result;
        } catch (error) {
            // 重试逻辑
            if (this.config.enableRetry) {
                return await this.retryLoadResource(task, onProgress, 0);
            }
            throw error;
        }
    }

    /**
     * 重试加载资源
     */
    private async retryLoadResource(
        task: LoadTask,
        onProgress?: (progress: LoadProgress) => void,
        retryCount: number = 0
    ): Promise<LoadResult> {
        if (retryCount >= this.config.maxRetries) {
            throw new Error(`资源加载失败，已重试 ${this.config.maxRetries} 次: ${task.url}`);
        }

        // 等待重试延迟
        if (retryCount > 0) {
            await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * retryCount));
        }

        try {
            return await this.workerManager!.loadResource(task, onProgress);
        } catch (error) {
            return await this.retryLoadResource(task, onProgress, retryCount + 1);
        }
    }

    /**
     * 预加载多个资源
     */
    async preloadResources(
        urls: string[],
        options: {
            type?: 'script' | 'style' | 'json' | 'text' | 'blob';
            priority?: 'high' | 'medium' | 'low';
            onProgress?: (url: string, progress: LoadProgress) => void;
            onComplete?: (url: string, result: LoadResult) => void;
        } = {}
    ): Promise<LoadResult[]> {
        const {
            type = 'text',
            priority = 'low',
            onProgress,
            onComplete
        } = options;

        const promises = urls.map(url =>
            this.loadResource(url, type, {
                priority,
                trackProgress: true,
                onProgress: onProgress ? (progress) => onProgress(url, progress) : undefined
            }).then(result => {
                if (onComplete) {
                    onComplete(url, result);
                }
                return result;
            })
        );

        return Promise.all(promises);
    }

    /**
     * 取消加载任务
     */
    cancelTask(taskId: string): void {
        if (this.workerManager) {
            this.workerManager.cancelTask(taskId);
        }
    }

    /**
     * 获取 Worker 状态
     */
    getWorkerStatus() {
        return this.workerManager?.getWorkerStatus() || [];
    }

    /**
     * 获取队列状态
     */
    getQueueStatus() {
        return this.workerManager?.getQueueStatus() || {
            queueLength: 0,
            activeTasks: 0,
            totalWorkers: 0,
            busyWorkers: 0
        };
    }

    /**
     * 获取缓存统计
     */
    async getCacheStats() {
        return this.cacheManager?.getStats() || {
            totalItems: 0,
            totalSize: 0,
            hitRate: 0,
            strategy: 'none'
        };
    }

    /**
     * 获取性能统计
     */
    getStats(): LoaderStats {
        const queueStatus = this.getQueueStatus();

        // 计算 Worker 利用率
        this.stats.workerUtilization = queueStatus.totalWorkers > 0
            ? queueStatus.busyWorkers / queueStatus.totalWorkers
            : 0;

        return { ...this.stats };
    }

    /**
     * 清空缓存
     */
    async clearCache(): Promise<void> {
        if (this.cacheManager) {
            await this.cacheManager.clear();
        }
    }

    /**
     * 重置统计信息
     */
    resetStats(): void {
        this.stats = {
            totalTasks: 0,
            successTasks: 0,
            failedTasks: 0,
            averageLoadTime: 0,
            totalDataLoaded: 0,
            cacheHitRate: 0,
            workerUtilization: 0,
            errorRate: 0
        };
        this.loadTimes = [];
    }

    /**
     * 检查浏览器支持
     */
    static isSupported(): boolean {
        return typeof Worker !== 'undefined' && typeof Blob !== 'undefined';
    }

    /**
     * 创建插件实例
     */
    static create(config?: WorkerLoaderConfig): WorkerLoaderPlugin {
        if (!WorkerLoaderPlugin.isSupported()) {
            throw new Error('当前浏览器不支持 Web Worker');
        }
        return new WorkerLoaderPlugin(config);
    }
}