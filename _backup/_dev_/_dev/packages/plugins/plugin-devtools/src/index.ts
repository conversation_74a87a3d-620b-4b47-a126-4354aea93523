/**
 * @micro-core/plugin-devtools - 开发者工具插件
 * 提供调试和监控微前端应用的开发工具
 * Enhanced with features migrated from @micro-core/core
 */

export { DevToolsPlugin } from './devtools-plugin';
export {
    EnhancedDevToolsManager,
    type DebugConsole, type EnhancedDevToolsConfig, type PerformanceProfile, type PerformanceProfiler
} from './enhanced-devtools-manager';

// 导出类型定义
export type {
    AppInfo, DevToolsConfig,
    DevToolsEvents, LogEntry,
    PerformanceMetrics
} from './types';

// 默认导出增强版本
export { EnhancedDevToolsManager as default } from './enhanced-devtools-manager';
