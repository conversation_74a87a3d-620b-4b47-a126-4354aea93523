/**
 * 开发者工具插件类型定义
 */

/**
 * 开发工具配置
 */
export interface DevToolsConfig {
    /** 是否启用开发工具 */
    enabled?: boolean;
    /** 是否显示面板 */
    showPanel?: boolean;
    /** 面板位置 */
    panelPosition?: 'top' | 'bottom' | 'left' | 'right';
    /** 是否启用性能监控 */
    enablePerformanceMonitor?: boolean;
    /** 是否启用日志记录 */
    enableLogger?: boolean;
    /** 日志级别 */
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    /** 是否启用应用检查器 */
    enableInspector?: boolean;
    /** 快捷键配置 */
    hotkeys?: {
        togglePanel?: string;
        toggleInspector?: string;
    };
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
    /** 应用加载时间 */
    loadTime: number;
    /** 应用挂载时间 */
    mountTime: number;
    /** 内存使用量 */
    memoryUsage: number;
    /** CPU 使用率 */
    cpuUsage: number;
    /** 网络请求数 */
    networkRequests: number;
    /** 错误数量 */
    errorCount: number;
}

/**
 * 应用信息
 */
export interface AppInfo {
    /** 应用名称 */
    name: string;
    /** 应用状态 */
    status: string;
    /** 应用版本 */
    version?: string;
    /** 应用入口 */
    entry?: string;
    /** 应用容器 */
    container?: string;
    /** 激活规则 */
    activeWhen?: string;
    /** 性能指标 */
    metrics?: PerformanceMetrics;
}

/**
 * 日志条目
 */
export interface LogEntry {
    /** 时间戳 */
    timestamp: number;
    /** 日志级别 */
    level: 'debug' | 'info' | 'warn' | 'error';
    /** 日志消息 */
    message: string;
    /** 应用名称 */
    appName?: string;
    /** 额外数据 */
    data?: any;
}

/**
 * 开发工具事件
 */
export interface DevToolsEvents {
    /** 面板显示/隐藏 */
    panelToggle: (visible: boolean) => void;
    /** 应用选择 */
    appSelect: (appName: string) => void;
    /** 性能指标更新 */
    metricsUpdate: (appName: string, metrics: PerformanceMetrics) => void;
    /** 日志记录 */
    logEntry: (entry: LogEntry) => void;
}