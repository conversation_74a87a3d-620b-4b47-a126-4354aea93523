/**
 * Enhanced DevTools Manager
 * Migrated from @micro-core/core for advanced development tools
 * @packageDocumentation
 */

import { EventEmitter } from 'events';

/**
 * Debug console interface
 */
export interface DebugConsole {
    log(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
    clear(): void;
    getHistory(): LogEntry[];
}

/**
 * Log entry interface
 */
export interface LogEntry {
    id: string;
    level: 'log' | 'warn' | 'error' | 'info' | 'debug';
    message: string;
    args: any[];
    timestamp: number;
    source?: string;
}

/**
 * Performance profiler interface
 */
export interface PerformanceProfiler {
    startProfile(name: string): void;
    endProfile(name: string): number;
    getProfiles(): PerformanceProfile[];
    clearProfiles(): void;
}

/**
 * Performance profile data
 */
export interface PerformanceProfile {
    name: string;
    startTime: number;
    endTime: number;
    duration: number;
    metadata?: Record<string, any>;
}

/**
 * DevTools configuration
 */
export interface EnhancedDevToolsConfig {
    /** Enable debug console */
    enableConsole?: boolean;
    /** Enable performance profiler */
    enableProfiler?: boolean;
    /** Maximum log entries to keep */
    maxLogEntries?: number;
    /** Maximum profiles to keep */
    maxProfiles?: number;
    /** Enable network monitoring */
    enableNetworkMonitoring?: boolean;
    /** Enable error tracking */
    enableErrorTracking?: boolean;
}

/**
 * Enhanced DevTools Manager
 * Provides comprehensive development and debugging tools
 */
export class EnhancedDevToolsManager extends EventEmitter {
    private config: Required<EnhancedDevToolsConfig>;
    private console: DebugConsole;
    private profiler: PerformanceProfiler;
    private logHistory: LogEntry[] = [];
    private profiles: PerformanceProfile[] = [];
    private activeProfiles = new Map<string, number>();
    private errorCount = 0;
    private networkRequests: any[] = [];

    constructor(config: EnhancedDevToolsConfig = {}) {
        super();
        
        this.config = {
            enableConsole: true,
            enableProfiler: true,
            maxLogEntries: 1000,
            maxProfiles: 100,
            enableNetworkMonitoring: true,
            enableErrorTracking: true,
            ...config
        };

        this.console = this.createDebugConsole();
        this.profiler = this.createPerformanceProfiler();

        if (this.config.enableErrorTracking) {
            this.setupErrorTracking();
        }

        if (this.config.enableNetworkMonitoring) {
            this.setupNetworkMonitoring();
        }
    }

    /**
     * Get debug console
     */
    getConsole(): DebugConsole {
        return this.console;
    }

    /**
     * Get performance profiler
     */
    getProfiler(): PerformanceProfiler {
        return this.profiler;
    }

    /**
     * Get development statistics
     */
    getStats(): {
        logEntries: number;
        profiles: number;
        errors: number;
        networkRequests: number;
    } {
        return {
            logEntries: this.logHistory.length,
            profiles: this.profiles.length,
            errors: this.errorCount,
            networkRequests: this.networkRequests.length
        };
    }

    /**
     * Clear all data
     */
    clear(): void {
        this.logHistory.length = 0;
        this.profiles.length = 0;
        this.networkRequests.length = 0;
        this.activeProfiles.clear();
        this.errorCount = 0;
        this.emit('devtools:cleared');
    }

    /**
     * Create debug console
     */
    private createDebugConsole(): DebugConsole {
        const addLogEntry = (level: LogEntry['level'], message: string, ...args: any[]) => {
            if (!this.config.enableConsole) return;

            const entry: LogEntry = {
                id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                level,
                message,
                args,
                timestamp: Date.now(),
                source: 'devtools'
            };

            this.logHistory.push(entry);

            // Limit log history size
            if (this.logHistory.length > this.config.maxLogEntries) {
                this.logHistory.shift();
            }

            this.emit('console:log', entry);

            // Also log to browser console in development
            if (process.env.NODE_ENV === 'development') {
                console[level](message, ...args);
            }
        };

        return {
            log: (message: string, ...args: any[]) => addLogEntry('log', message, ...args),
            warn: (message: string, ...args: any[]) => addLogEntry('warn', message, ...args),
            error: (message: string, ...args: any[]) => addLogEntry('error', message, ...args),
            info: (message: string, ...args: any[]) => addLogEntry('info', message, ...args),
            debug: (message: string, ...args: any[]) => addLogEntry('debug', message, ...args),
            clear: () => {
                this.logHistory.length = 0;
                this.emit('console:cleared');
            },
            getHistory: () => [...this.logHistory]
        };
    }

    /**
     * Create performance profiler
     */
    private createPerformanceProfiler(): PerformanceProfiler {
        return {
            startProfile: (name: string) => {
                if (!this.config.enableProfiler) return;

                const startTime = performance.now();
                this.activeProfiles.set(name, startTime);
                this.emit('profiler:start', { name, startTime });
            },

            endProfile: (name: string) => {
                if (!this.config.enableProfiler) return 0;

                const startTime = this.activeProfiles.get(name);
                if (!startTime) {
                    console.warn(`[DevTools] No active profile found for: ${name}`);
                    return 0;
                }

                const endTime = performance.now();
                const duration = endTime - startTime;

                const profile: PerformanceProfile = {
                    name,
                    startTime,
                    endTime,
                    duration
                };

                this.profiles.push(profile);
                this.activeProfiles.delete(name);

                // Limit profiles history size
                if (this.profiles.length > this.config.maxProfiles) {
                    this.profiles.shift();
                }

                this.emit('profiler:end', profile);
                return duration;
            },

            getProfiles: () => [...this.profiles],

            clearProfiles: () => {
                this.profiles.length = 0;
                this.activeProfiles.clear();
                this.emit('profiler:cleared');
            }
        };
    }

    /**
     * Setup error tracking
     */
    private setupErrorTracking(): void {
        const originalErrorHandler = window.onerror;
        const originalUnhandledRejectionHandler = window.onunhandledrejection;

        window.onerror = (message, source, lineno, colno, error) => {
            this.errorCount++;
            this.console.error('Uncaught Error:', message, {
                source,
                line: lineno,
                column: colno,
                error
            });

            if (originalErrorHandler) {
                originalErrorHandler.call(window, message, source, lineno, colno, error);
            }
        };

        window.onunhandledrejection = (event) => {
            this.errorCount++;
            this.console.error('Unhandled Promise Rejection:', event.reason);

            if (originalUnhandledRejectionHandler) {
                originalUnhandledRejectionHandler.call(window, event);
            }
        };
    }

    /**
     * Setup network monitoring
     */
    private setupNetworkMonitoring(): void {
        // Monitor fetch requests
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const startTime = performance.now();
            const url = args[0] instanceof Request ? args[0].url : args[0];
            
            try {
                const response = await originalFetch(...args);
                const endTime = performance.now();
                
                this.networkRequests.push({
                    url,
                    method: args[1]?.method || 'GET',
                    status: response.status,
                    duration: endTime - startTime,
                    timestamp: Date.now(),
                    type: 'fetch'
                });

                this.emit('network:request', {
                    url,
                    status: response.status,
                    duration: endTime - startTime
                });

                return response;
            } catch (error) {
                const endTime = performance.now();
                
                this.networkRequests.push({
                    url,
                    method: args[1]?.method || 'GET',
                    status: 0,
                    duration: endTime - startTime,
                    timestamp: Date.now(),
                    type: 'fetch',
                    error: error.message
                });

                this.emit('network:error', { url, error });
                throw error;
            }
        };

        // Monitor XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;

        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._devtools_method = method;
            this._devtools_url = url;
            this._devtools_startTime = performance.now();
            return originalXHROpen.call(this, method, url, ...args);
        };

        XMLHttpRequest.prototype.send = function(...args) {
            const xhr = this;
            const originalOnReadyStateChange = xhr.onreadystatechange;

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    const endTime = performance.now();
                    const duration = endTime - (xhr._devtools_startTime || endTime);

                    const networkRequest = {
                        url: xhr._devtools_url,
                        method: xhr._devtools_method,
                        status: xhr.status,
                        duration,
                        timestamp: Date.now(),
                        type: 'xhr'
                    };

                    // Add to manager's network requests
                    const manager = (window as any).__MICRO_CORE_DEVTOOLS__;
                    if (manager) {
                        manager.networkRequests.push(networkRequest);
                        manager.emit('network:request', networkRequest);
                    }
                }

                if (originalOnReadyStateChange) {
                    originalOnReadyStateChange.call(xhr);
                }
            };

            return originalXHRSend.call(this, ...args);
        };

        // Store reference for XHR monitoring
        (window as any).__MICRO_CORE_DEVTOOLS__ = this;
    }

    /**
     * Get network requests
     */
    getNetworkRequests(): any[] {
        return [...this.networkRequests];
    }

    /**
     * Clear network requests
     */
    clearNetworkRequests(): void {
        this.networkRequests.length = 0;
        this.emit('network:cleared');
    }

    /**
     * Export development data
     */
    exportData(): {
        logs: LogEntry[];
        profiles: PerformanceProfile[];
        networkRequests: any[];
        stats: any;
        timestamp: number;
    } {
        return {
            logs: this.logHistory,
            profiles: this.profiles,
            networkRequests: this.networkRequests,
            stats: this.getStats(),
            timestamp: Date.now()
        };
    }

    /**
     * Destroy the devtools manager
     */
    destroy(): void {
        this.clear();
        this.removeAllListeners();
        
        // Clean up global references
        delete (window as any).__MICRO_CORE_DEVTOOLS__;
        
        console.log('[EnhancedDevToolsManager] DevTools manager destroyed');
    }
}
