/**
 * 微应用自定义元素
 * 用于承载微前端应用的 Web Component 容器
 */
export class MicroAppElement extends HTMLElement {
    private shadowRoot: ShadowRoot;
    private appName: string = '';
    private appEntry: string = '';
    private isLoaded: boolean = false;
    private loadPromise: Promise<void> | null = null;

    constructor() {
        super();

        // 创建 Shadow DOM
        this.shadowRoot = this.attachShadow({ mode: 'closed' });

        // 初始化基础样式
        this.initializeStyles();

        // 监听属性变化
        this.observeAttributes();
    }

    /**
     * 监听的属性列表
     */
    static get observedAttributes() {
        return ['app-name', 'app-entry', 'app-props', 'loading'];
    }

    /**
     * 属性变化回调
     */
    attributeChangedCallback(name: string, oldValue: string, newValue: string) {
        if (oldValue === newValue) return;

        switch (name) {
            case 'app-name':
                this.appName = newValue || '';
                break;
            case 'app-entry':
                this.appEntry = newValue || '';
                this.loadApp();
                break;
            case 'app-props':
                this.updateAppProps(newValue);
                break;
            case 'loading':
                this.updateLoadingState(newValue === 'true');
                break;
        }
    }

    /**
     * 元素连接到 DOM 时调用
     */
    connectedCallback() {
        this.dispatchEvent(new CustomEvent('micro-app-connected', {
            detail: { appName: this.appName },
            bubbles: true
        }));
    }

    /**
     * 元素从 DOM 断开时调用
     */
    disconnectedCallback() {
        this.cleanup();
        this.dispatchEvent(new CustomEvent('micro-app-disconnected', {
            detail: { appName: this.appName },
            bubbles: true
        }));
    }

    /**
     * 初始化基础样式
     */
    private initializeStyles() {
        const style = document.createElement('style');
        style.textContent = `
            :host {
                display: block;
                position: relative;
                width: 100%;
                height: 100%;
                contain: layout style paint;
                isolation: isolate;
            }
            
            .micro-app-container {
                width: 100%;
                height: 100%;
                overflow: auto;
            }
            
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .error-message {
                color: #e74c3c;
                text-align: center;
                padding: 20px;
                font-family: Arial, sans-serif;
            }
        `;
        this.shadowRoot.appendChild(style);

        // 创建容器
        const container = document.createElement('div');
        container.className = 'micro-app-container';
        this.shadowRoot.appendChild(container);
    }

    /**
     * 监听属性变化
     */
    private observeAttributes() {
        // 使用 MutationObserver 监听属性变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes') {
                    const attributeName = mutation.attributeName;
                    const newValue = this.getAttribute(attributeName!);
                    const oldValue = mutation.oldValue;

                    if (newValue !== oldValue) {
                        this.attributeChangedCallback(attributeName!, oldValue || '', newValue || '');
                    }
                }
            });
        });

        observer.observe(this, {
            attributes: true,
            attributeOldValue: true,
            attributeFilter: MicroAppElement.observedAttributes
        });
    }

    /**
     * 加载应用
     */
    private async loadApp() {
        if (!this.appEntry || this.isLoaded) {
            return;
        }

        // 避免重复加载
        if (this.loadPromise) {
            return this.loadPromise;
        }

        this.loadPromise = this.performLoad();
        return this.loadPromise;
    }

    /**
     * 执行加载
     */
    private async performLoad(): Promise<void> {
        try {
            this.updateLoadingState(true);
            this.dispatchEvent(new CustomEvent('micro-app-loading', {
                detail: { appName: this.appName, entry: this.appEntry },
                bubbles: true
            }));

            // 加载应用内容
            await this.loadAppContent();

            this.isLoaded = true;
            this.updateLoadingState(false);

            this.dispatchEvent(new CustomEvent('micro-app-loaded', {
                detail: { appName: this.appName, entry: this.appEntry },
                bubbles: true
            }));
        } catch (error) {
            this.updateLoadingState(false);
            this.showError(error as Error);

            this.dispatchEvent(new CustomEvent('micro-app-error', {
                detail: { appName: this.appName, entry: this.appEntry, error },
                bubbles: true
            }));
        }
    }

    /**
     * 加载应用内容
     */
    private async loadAppContent(): Promise<void> {
        if (this.appEntry.startsWith('http')) {
            // 加载远程应用
            await this.loadRemoteApp(this.appEntry);
        } else {
            // 加载本地内容
            await this.loadLocalContent(this.appEntry);
        }
    }

    /**
     * 加载远程应用
     */
    private async loadRemoteApp(url: string): Promise<void> {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to load app from ${url}: ${response.statusText}`);
        }

        const html = await response.text();
        await this.injectContent(html);
    }

    /**
     * 加载本地内容
     */
    private async loadLocalContent(content: string): Promise<void> {
        await this.injectContent(content);
    }

    /**
     * 注入内容
     */
    private async injectContent(html: string): Promise<void> {
        const container = this.shadowRoot.querySelector('.micro-app-container') as HTMLElement;
        if (!container) {
            throw new Error('Container not found');
        }

        // 解析 HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 注入样式
        const styles = doc.querySelectorAll('style, link[rel="stylesheet"]');
        styles.forEach(style => {
            const clonedStyle = style.cloneNode(true) as HTMLElement;
            this.shadowRoot.appendChild(clonedStyle);
        });

        // 注入内容
        const body = doc.body || doc.documentElement;
        while (body.firstChild) {
            container.appendChild(body.firstChild);
        }

        // 注入脚本
        const scripts = doc.querySelectorAll('script');
        for (const script of scripts) {
            await this.injectScript(script);
        }
    }

    /**
     * 注入脚本
     */
    private async injectScript(originalScript: HTMLScriptElement): Promise<void> {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');

            // 复制属性
            Array.from(originalScript.attributes).forEach(attr => {
                script.setAttribute(attr.name, attr.value);
            });

            // 设置内容
            if (originalScript.src) {
                script.onload = () => resolve();
                script.onerror = () => reject(new Error(`Failed to load script: ${originalScript.src}`));
            } else {
                script.textContent = originalScript.textContent;
                setTimeout(resolve, 0); // 异步执行
            }

            this.shadowRoot.appendChild(script);
        });
    }

    /**
     * 更新应用属性
     */
    private updateAppProps(propsString: string) {
        try {
            const props = propsString ? JSON.parse(propsString) : {};

            // 触发属性更新事件
            this.dispatchEvent(new CustomEvent('micro-app-props-updated', {
                detail: { appName: this.appName, props },
                bubbles: true
            }));
        } catch (error) {
            console.error('Failed to parse app props:', error);
        }
    }

    /**
     * 更新加载状态
     */
    private updateLoadingState(loading: boolean) {
        const existingOverlay = this.shadowRoot.querySelector('.loading-overlay');

        if (loading) {
            if (!existingOverlay) {
                const overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = '<div class="loading-spinner"></div>';
                this.shadowRoot.appendChild(overlay);
            }
        } else {
            if (existingOverlay) {
                existingOverlay.remove();
            }
        }
    }

    /**
     * 显示错误
     */
    private showError(error: Error) {
        const container = this.shadowRoot.querySelector('.micro-app-container') as HTMLElement;
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <h3>应用加载失败</h3>
                    <p>${error.message}</p>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    /**
     * 清理资源
     */
    private cleanup() {
        this.isLoaded = false;
        this.loadPromise = null;

        // 清理容器内容
        const container = this.shadowRoot.querySelector('.micro-app-container') as HTMLElement;
        if (container) {
            container.innerHTML = '';
        }
    }

    /**
     * 公共方法：重新加载应用
     */
    reload() {
        this.cleanup();
        this.loadApp();
    }

    /**
     * 公共方法：获取应用信息
     */
    getAppInfo() {
        return {
            name: this.appName,
            entry: this.appEntry,
            isLoaded: this.isLoaded
        };
    }
}