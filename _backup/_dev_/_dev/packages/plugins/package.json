{"name": "@micro-core/plugins", "version": "0.1.0", "description": "Micro-Core 插件集合 - 提供完整的微前端插件生态系统，包括沙箱、通信、路由、认证等核心功能", "keywords": ["micro-frontend", "microfrontend", "plugin", "micro-core", "sandbox", "communication", "router", "auth", "performance"], "homepage": "https://micro-core.dev", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins"}, "license": "MIT", "author": {"name": "Echo", "email": "<EMAIL>"}, "type": "module", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"clean": "rm -rf dist coverage test-results", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:run": "vitest run", "test:integration": "vitest run --config vitest.integration.config.ts", "test:performance": "node test-runner.js --performance", "type-check": "tsc --noEmit", "size-check": "bundlesize", "validate": "pnpm run type-check && pnpm run lint && pnpm run test:run", "ci": "pnpm run clean && pnpm run build && pnpm run validate && pnpm run test:coverage", "prepublishOnly": "pnpm run ci", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest", "preview": "vite preview"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"@types/node": "^20.0.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "bundlesize": "^0.18.0", "eslint": "^8.57.0", "jsdom": "^24.0.0", "typescript": "^5.3.3", "vitest": "^3.2.4", "vite": "^7.0.6", "vite-plugin-dts": "^4.3.0"}, "bundlesize": [{"path": "./dist/index.js", "maxSize": "50kb", "compression": "gzip"}, {"path": "./dist/plugin-auth/index.js", "maxSize": "8kb", "compression": "gzip"}, {"path": "./dist/plugin-sandbox-proxy/index.js", "maxSize": "12kb", "compression": "gzip"}], "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}