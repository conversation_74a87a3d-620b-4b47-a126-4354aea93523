import type { DefinePropertySandboxOptions, SandboxContext } from './types';

/**
 * DefineProperty 沙箱实现
 * 基于 Object.defineProperty 提供变量隔离
 */
export class DefinePropertySandbox {
    private name: string;
    private options: DefinePropertySandboxOptions;
    private isActive = false;
    private context: SandboxContext;
    private originalDescriptors = new Map<string, PropertyDescriptor | undefined>();
    private modifiedProps = new Set<string>();

    constructor(name: string, options: DefinePropertySandboxOptions = {}) {
        this.name = name;
        this.options = {
            enableLogging: false,
            enableStrictMode: true,
            enablePropertyValidation: true,
            ...options
        };

        this.context = {
            window: {} as any,
            document: {} as any,
            location: {} as any,
            history: {} as any
        };

        this.initializeContext();
    }

    /**
     * 初始化沙箱上下文
     */
    private initializeContext() {
        // 创建沙箱化的 window 对象
        this.context.window = this.createSandboxWindow();

        // 创建沙箱化的 document 对象
        this.context.document = this.createSandboxDocument();

        // 创建沙箱化的 location 对象
        this.context.location = this.createSandboxLocation();

        // 创建沙箱化的 history 对象
        this.context.history = this.createSandboxHistory();

        this.log('沙箱上下文初始化完成');
    }

    /**
     * 创建沙箱化的 window 对象
     */
    private createSandboxWindow(): Window {
        const sandboxWindow = Object.create(null);

        // 复制安全的 window 属性
        const safeProps = [
            'console', 'setTimeout', 'clearTimeout', 'setInterval', 'clearInterval',
            'requestAnimationFrame', 'cancelAnimationFrame', 'Promise', 'fetch'
        ];

        for (const prop of safeProps) {
            if (prop in window) {
                try {
                    sandboxWindow[prop] = (window as any)[prop];
                } catch (error) {
                    this.log(`复制 window.${prop} 失败: ${error}`);
                }
            }
        }

        return sandboxWindow;
    }

    /**
     * 创建沙箱化的 document 对象
     */
    private createSandboxDocument(): Document {
        const sandboxDocument = Object.create(null);

        // 复制安全的 document 属性和方法
        const safeProps = [
            'createElement', 'createTextNode', 'createDocumentFragment',
            'getElementById', 'querySelector', 'querySelectorAll'
        ];

        for (const prop of safeProps) {
            if (prop in document) {
                try {
                    sandboxDocument[prop] = (document as any)[prop].bind(document);
                } catch (error) {
                    this.log(`复制 document.${prop} 失败: ${error}`);
                }
            }
        }

        return sandboxDocument;
    }

    /**
     * 创建沙箱化的 location 对象
     */
    private createSandboxLocation(): Location {
        const sandboxLocation = Object.create(null);

        // 复制 location 属性
        const locationProps = ['href', 'origin', 'protocol', 'host', 'hostname', 'port', 'pathname', 'search', 'hash'];

        for (const prop of locationProps) {
            try {
                sandboxLocation[prop] = (location as any)[prop];
            } catch (error) {
                this.log(`复制 location.${prop} 失败: ${error}`);
            }
        }

        return sandboxLocation;
    }

    /**
     * 创建沙箱化的 history 对象
     */
    private createSandboxHistory(): History {
        const sandboxHistory = Object.create(null);

        // 提供安全的 history 方法
        sandboxHistory.pushState = (state: any, title: string, url?: string) => {
            this.log(`History pushState: ${url}`);
            // 在实际实现中，这里应该通知主应用进行路由变更
        };

        sandboxHistory.replaceState = (state: any, title: string, url?: string) => {
            this.log(`History replaceState: ${url}`);
            // 在实际实现中，这里应该通知主应用进行路由变更
        };

        sandboxHistory.back = () => {
            this.log('History back');
            // 在实际实现中，这里应该通知主应用进行路由变更
        };

        sandboxHistory.forward = () => {
            this.log('History forward');
            // 在实际实现中，这里应该通知主应用进行路由变更
        };

        sandboxHistory.go = (delta: number) => {
            this.log(`History go: ${delta}`);
            // 在实际实现中，这里应该通知主应用进行路由变更
        };

        return sandboxHistory;
    }

    /**
     * 激活沙箱
     */
    activate() {
        if (this.isActive) {
            this.log('沙箱已经激活，跳过重复激活');
            return;
        }

        try {
            this.patchGlobalProperties();
            this.isActive = true;
            this.log('沙箱激活成功');
        } catch (error) {
            console.error(`[DefinePropertySandbox:${this.name}] 激活沙箱失败:`, error);
            throw error;
        }
    }

    /**
     * 停用沙箱
     */
    deactivate() {
        if (!this.isActive) {
            this.log('沙箱未激活，跳过停用');
            return;
        }

        try {
            this.restoreGlobalProperties();
            this.isActive = false;
            this.log('沙箱停用成功');
        } catch (error) {
            console.error(`[DefinePropertySandbox:${this.name}] 停用沙箱失败:`, error);
            throw error;
        }
    }

    /**
     * 销毁沙箱
     */
    destroy() {
        this.deactivate();

        // 清理资源
        this.originalDescriptors.clear();
        this.modifiedProps.clear();

        // 清理上下文
        this.context = {} as any;

        this.log('沙箱销毁完成');
    }

    /**
     * 修补全局属性
     */
    private patchGlobalProperties() {
        const globalProps = ['window', 'document', 'location', 'history'];

        for (const prop of globalProps) {
            this.patchGlobalProperty(prop);
        }
    }

    /**
     * 修补单个全局属性
     */
    private patchGlobalProperty(prop: string) {
        try {
            // 保存原始属性描述符
            const originalDescriptor = Object.getOwnPropertyDescriptor(window, prop);
            this.originalDescriptors.set(prop, originalDescriptor);

            // 定义新的属性描述符
            const newDescriptor: PropertyDescriptor = {
                get: () => {
                    return (this.context as any)[prop] || (window as any)[prop];
                },
                set: (value: any) => {
                    if (this.options.enablePropertyValidation && !this.validateProperty(prop, value)) {
                        this.log(`属性 ${prop} 验证失败，拒绝设置`);
                        return;
                    }
                    (this.context as any)[prop] = value;
                },
                configurable: true,
                enumerable: true
            };

            // 应用新的属性描述符
            Object.defineProperty(window, prop, newDescriptor);
            this.modifiedProps.add(prop);

            this.log(`全局属性 ${prop} 修补成功`);
        } catch (error) {
            console.error(`[DefinePropertySandbox:${this.name}] 修补属性 ${prop} 失败:`, error);
        }
    }

    /**
     * 恢复全局属性
     */
    private restoreGlobalProperties() {
        for (const prop of this.modifiedProps) {
            this.restoreGlobalProperty(prop);
        }
        this.modifiedProps.clear();
    }

    /**
     * 恢复单个全局属性
     */
    private restoreGlobalProperty(prop: string) {
        try {
            const originalDescriptor = this.originalDescriptors.get(prop);

            if (originalDescriptor) {
                Object.defineProperty(window, prop, originalDescriptor);
            } else {
                delete (window as any)[prop];
            }

            this.log(`全局属性 ${prop} 恢复成功`);
        } catch (error) {
            console.error(`[DefinePropertySandbox:${this.name}] 恢复属性 ${prop} 失败:`, error);
        }
    }

    /**
     * 验证属性值
     */
    private validateProperty(prop: string, value: any): boolean {
        if (!this.options.enablePropertyValidation) {
            return true;
        }

        // 基本的属性验证逻辑
        if (prop === 'location' && typeof value !== 'object') {
            return false;
        }

        if (prop === 'document' && typeof value !== 'object') {
            return false;
        }

        return true;
    }

    /**
     * 执行代码
     */
    execScript(code: string): any {
        if (!this.isActive) {
            throw new Error('沙箱未激活，无法执行代码');
        }

        try {
            // 在沙箱上下文中执行代码
            const func = new Function('window', 'document', 'location', 'history', code);
            return func.call(
                this.context.window,
                this.context.window,
                this.context.document,
                this.context.location,
                this.context.history
            );
        } catch (error) {
            console.error(`[DefinePropertySandbox:${this.name}] 执行代码失败:`, error);
            throw error;
        }
    }

    /**
     * 获取沙箱上下文
     */
    getContext(): SandboxContext {
        return { ...this.context };
    }

    /**
     * 检查是否激活
     */
    isActivated(): boolean {
        return this.isActive;
    }

    /**
     * 记录日志
     */
    private log(message: string) {
        if (this.options.enableLogging) {
            console.log(`[DefinePropertySandbox:${this.name}] ${message}`);
        }
    }
}