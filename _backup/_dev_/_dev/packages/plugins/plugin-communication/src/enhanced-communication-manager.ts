/**
 * Enhanced Communication Manager
 * Migrated from @micro-core/core for advanced communication features
 * @packageDocumentation
 */

import { EventEmitter } from 'events';

/**
 * Enhanced communication message interface
 */
export interface EnhancedCommunicationMessage {
    /** Message ID */
    id: string;
    /** Message type */
    type: string;
    /** Message data */
    data: any;
    /** Sender application name */
    from: string;
    /** Receiver application name (optional for broadcast) */
    to?: string;
    /** Message timestamp */
    timestamp: number;
    /** Message priority */
    priority?: 'low' | 'normal' | 'high';
    /** Message timeout in milliseconds */
    timeout?: number;
    /** Whether message requires acknowledgment */
    requiresAck?: boolean;
    /** Message metadata */
    metadata?: Record<string, any>;
}

/**
 * Message handler function type
 */
export type MessageHandler = (message: EnhancedCommunicationMessage) => void | Promise<void>;

/**
 * Message filter function type
 */
export type MessageFilter = (message: EnhancedCommunicationMessage) => boolean;

/**
 * Communication channel configuration
 */
export interface ChannelConfig {
    /** Channel name */
    name: string;
    /** Maximum message queue size */
    maxQueueSize?: number;
    /** Message timeout in milliseconds */
    defaultTimeout?: number;
    /** Whether to enable message persistence */
    persistent?: boolean;
    /** Message filters */
    filters?: MessageFilter[];
}

/**
 * Communication statistics
 */
export interface CommunicationStats {
    /** Total messages sent */
    messagesSent: number;
    /** Total messages received */
    messagesReceived: number;
    /** Total messages failed */
    messagesFailed: number;
    /** Active channels count */
    activeChannels: number;
    /** Queued messages count */
    queuedMessages: number;
    /** Average message processing time */
    avgProcessingTime: number;
}

/**
 * Enhanced Communication Manager
 * Provides advanced inter-application communication features
 */
export class EnhancedCommunicationManager extends EventEmitter {
    private channels = new Map<string, Set<string>>();
    private messageQueue = new Map<string, EnhancedCommunicationMessage[]>();
    private messageHandlers = new Map<string, Map<string, Set<MessageHandler>>>();
    private channelConfigs = new Map<string, ChannelConfig>();
    private stats: CommunicationStats;
    private messageIdCounter = 0;
    private processingTimes: number[] = [];

    constructor() {
        super();
        this.stats = {
            messagesSent: 0,
            messagesReceived: 0,
            messagesFailed: 0,
            activeChannels: 0,
            queuedMessages: 0,
            avgProcessingTime: 0
        };

        // Start message queue processor
        this.startMessageProcessor();
    }

    /**
     * Create a communication channel
     */
    createChannel(config: ChannelConfig): void {
        const { name } = config;
        
        if (this.channels.has(name)) {
            console.warn(`[EnhancedCommunicationManager] Channel ${name} already exists`);
            return;
        }

        this.channels.set(name, new Set());
        this.messageQueue.set(name, []);
        this.messageHandlers.set(name, new Map());
        this.channelConfigs.set(name, {
            maxQueueSize: 1000,
            defaultTimeout: 5000,
            persistent: false,
            filters: [],
            ...config
        });

        this.stats.activeChannels++;
        this.emit('channel:created', { name, config });

        console.log(`[EnhancedCommunicationManager] Channel ${name} created`);
    }

    /**
     * Destroy a communication channel
     */
    destroyChannel(name: string): void {
        if (!this.channels.has(name)) {
            console.warn(`[EnhancedCommunicationManager] Channel ${name} does not exist`);
            return;
        }

        // Clear all subscribers
        const subscribers = this.channels.get(name)!;
        subscribers.clear();

        // Clear message queue
        const queue = this.messageQueue.get(name)!;
        queue.length = 0;

        // Clear message handlers
        const handlers = this.messageHandlers.get(name)!;
        handlers.clear();

        // Remove from maps
        this.channels.delete(name);
        this.messageQueue.delete(name);
        this.messageHandlers.delete(name);
        this.channelConfigs.delete(name);

        this.stats.activeChannels--;
        this.emit('channel:destroyed', { name });

        console.log(`[EnhancedCommunicationManager] Channel ${name} destroyed`);
    }

    /**
     * Subscribe to a channel
     */
    subscribe(channelName: string, appName: string): void {
        if (!this.channels.has(channelName)) {
            this.createChannel({ name: channelName });
        }

        const subscribers = this.channels.get(channelName)!;
        subscribers.add(appName);

        this.emit('app:subscribed', { channelName, appName });
        console.log(`[EnhancedCommunicationManager] ${appName} subscribed to ${channelName}`);
    }

    /**
     * Unsubscribe from a channel
     */
    unsubscribe(channelName: string, appName: string): void {
        const subscribers = this.channels.get(channelName);
        if (subscribers) {
            subscribers.delete(appName);
            this.emit('app:unsubscribed', { channelName, appName });
            console.log(`[EnhancedCommunicationManager] ${appName} unsubscribed from ${channelName}`);
        }
    }

    /**
     * Send a message
     */
    sendMessage(message: Partial<EnhancedCommunicationMessage>): string {
        const startTime = Date.now();
        
        // Generate message ID if not provided
        const messageId = message.id || this.generateMessageId();
        
        const fullMessage: EnhancedCommunicationMessage = {
            id: messageId,
            type: message.type || 'default',
            data: message.data,
            from: message.from || 'unknown',
            to: message.to,
            timestamp: Date.now(),
            priority: message.priority || 'normal',
            timeout: message.timeout || 5000,
            requiresAck: message.requiresAck || false,
            metadata: message.metadata || {}
        };

        try {
            if (fullMessage.to) {
                this.sendDirectMessage(fullMessage);
            } else {
                this.broadcastMessage(fullMessage);
            }

            this.stats.messagesSent++;
            this.updateProcessingTime(Date.now() - startTime);
            this.emit('message:sent', fullMessage);

            return messageId;
        } catch (error) {
            this.stats.messagesFailed++;
            this.emit('message:failed', { message: fullMessage, error });
            throw error;
        }
    }

    /**
     * Add message handler
     */
    addMessageHandler(channelName: string, messageType: string, handler: MessageHandler): void {
        if (!this.messageHandlers.has(channelName)) {
            this.messageHandlers.set(channelName, new Map());
        }

        const channelHandlers = this.messageHandlers.get(channelName)!;
        if (!channelHandlers.has(messageType)) {
            channelHandlers.set(messageType, new Set());
        }

        const typeHandlers = channelHandlers.get(messageType)!;
        typeHandlers.add(handler);

        console.log(`[EnhancedCommunicationManager] Handler added for ${channelName}:${messageType}`);
    }

    /**
     * Remove message handler
     */
    removeMessageHandler(channelName: string, messageType: string, handler: MessageHandler): void {
        const channelHandlers = this.messageHandlers.get(channelName);
        if (channelHandlers) {
            const typeHandlers = channelHandlers.get(messageType);
            if (typeHandlers) {
                typeHandlers.delete(handler);
                if (typeHandlers.size === 0) {
                    channelHandlers.delete(messageType);
                }
            }
        }
    }

    /**
     * Send direct message to specific application
     */
    private sendDirectMessage(message: EnhancedCommunicationMessage): void {
        const { to } = message;
        if (!to) return;

        // Check if target application is subscribed to any channel
        let targetFound = false;
        for (const [channelName, subscribers] of this.channels) {
            if (subscribers.has(to)) {
                this.deliverMessage(channelName, message);
                targetFound = true;
                break;
            }
        }

        if (!targetFound) {
            // Queue message for later delivery
            this.queueMessage(to, message);
        }
    }

    /**
     * Broadcast message to all subscribers
     */
    private broadcastMessage(message: EnhancedCommunicationMessage): void {
        for (const [channelName, subscribers] of this.channels) {
            if (subscribers.size > 0) {
                this.deliverMessage(channelName, message);
            }
        }
    }

    /**
     * Deliver message to channel
     */
    private deliverMessage(channelName: string, message: EnhancedCommunicationMessage): void {
        const config = this.channelConfigs.get(channelName);
        if (!config) return;

        // Apply filters
        if (config.filters && config.filters.length > 0) {
            const passesFilters = config.filters.every(filter => filter(message));
            if (!passesFilters) {
                return;
            }
        }

        // Get handlers for this message type
        const channelHandlers = this.messageHandlers.get(channelName);
        if (channelHandlers) {
            const typeHandlers = channelHandlers.get(message.type);
            if (typeHandlers) {
                typeHandlers.forEach(async (handler) => {
                    try {
                        await handler(message);
                        this.stats.messagesReceived++;
                    } catch (error) {
                        this.stats.messagesFailed++;
                        this.emit('message:handler-error', { message, error });
                    }
                });
            }
        }

        // Emit channel-specific event
        this.emit(`channel:${channelName}:message`, message);
    }

    /**
     * Queue message for later delivery
     */
    private queueMessage(target: string, message: EnhancedCommunicationMessage): void {
        if (!this.messageQueue.has(target)) {
            this.messageQueue.set(target, []);
        }

        const queue = this.messageQueue.get(target)!;
        const config = this.channelConfigs.get(target);
        const maxQueueSize = config?.maxQueueSize || 1000;

        if (queue.length >= maxQueueSize) {
            // Remove oldest message
            queue.shift();
        }

        queue.push(message);
        this.stats.queuedMessages++;

        console.log(`[EnhancedCommunicationManager] Message queued for ${target}`);
    }

    /**
     * Process queued messages
     */
    private processQueuedMessages(): void {
        for (const [target, queue] of this.messageQueue) {
            if (queue.length === 0) continue;

            // Check if target is now available
            let targetAvailable = false;
            for (const [channelName, subscribers] of this.channels) {
                if (subscribers.has(target)) {
                    targetAvailable = true;
                    
                    // Deliver all queued messages
                    while (queue.length > 0) {
                        const message = queue.shift()!;
                        this.deliverMessage(channelName, message);
                        this.stats.queuedMessages--;
                    }
                    break;
                }
            }

            // Remove expired messages
            if (!targetAvailable) {
                const now = Date.now();
                const validMessages = queue.filter(message => {
                    const isExpired = now - message.timestamp > (message.timeout || 5000);
                    if (isExpired) {
                        this.stats.queuedMessages--;
                        this.emit('message:expired', message);
                    }
                    return !isExpired;
                });
                this.messageQueue.set(target, validMessages);
            }
        }
    }

    /**
     * Start message processor
     */
    private startMessageProcessor(): void {
        setInterval(() => {
            this.processQueuedMessages();
        }, 1000);
    }

    /**
     * Generate unique message ID
     */
    private generateMessageId(): string {
        return `msg_${Date.now()}_${++this.messageIdCounter}`;
    }

    /**
     * Update processing time statistics
     */
    private updateProcessingTime(time: number): void {
        this.processingTimes.push(time);
        if (this.processingTimes.length > 100) {
            this.processingTimes.shift();
        }
        
        const sum = this.processingTimes.reduce((a, b) => a + b, 0);
        this.stats.avgProcessingTime = sum / this.processingTimes.length;
    }

    /**
     * Get communication statistics
     */
    getStats(): CommunicationStats {
        return { ...this.stats };
    }

    /**
     * Get channel information
     */
    getChannelInfo(name: string): {
        name: string;
        subscribers: string[];
        queuedMessages: number;
        config: ChannelConfig;
    } | null {
        const subscribers = this.channels.get(name);
        const queue = this.messageQueue.get(name);
        const config = this.channelConfigs.get(name);

        if (!subscribers || !config) {
            return null;
        }

        return {
            name,
            subscribers: Array.from(subscribers),
            queuedMessages: queue?.length || 0,
            config
        };
    }

    /**
     * Get all channels information
     */
    getAllChannelsInfo(): Array<{
        name: string;
        subscribers: string[];
        queuedMessages: number;
        config: ChannelConfig;
    }> {
        const channels: Array<{
            name: string;
            subscribers: string[];
            queuedMessages: number;
            config: ChannelConfig;
        }> = [];

        for (const channelName of this.channels.keys()) {
            const info = this.getChannelInfo(channelName);
            if (info) {
                channels.push(info);
            }
        }

        return channels;
    }

    /**
     * Clear all channels and reset state
     */
    clear(): void {
        // Clear all channels
        for (const channelName of this.channels.keys()) {
            this.destroyChannel(channelName);
        }

        // Reset statistics
        this.stats = {
            messagesSent: 0,
            messagesReceived: 0,
            messagesFailed: 0,
            activeChannels: 0,
            queuedMessages: 0,
            avgProcessingTime: 0
        };

        this.processingTimes.length = 0;
        this.messageIdCounter = 0;

        this.emit('manager:cleared');
        console.log('[EnhancedCommunicationManager] All channels cleared');
    }
}
