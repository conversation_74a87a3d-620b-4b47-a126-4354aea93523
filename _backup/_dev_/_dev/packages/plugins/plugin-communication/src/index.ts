/**
 * 通信插件
 * 提供微前端应用间通信功能
 */

import type { Plugin } from '@micro-core/core';
import { EventEmitter } from 'events';
import {
    ChannelConfig,
    CommunicationStats,
    EnhancedCommunicationManager,
    EnhancedCommunicationMessage,
    MessageFilter,
    MessageHandler
} from './enhanced-communication-manager';

/**
 * 通信消息接口
 */
export interface CommunicationMessage {
    /** 消息类型 */
    type: string;
    /** 消息数据 */
    data?: any;
    /** 发送者 */
    from?: string;
    /** 接收者 */
    to?: string;
    /** 消息ID */
    id?: string;
    /** 时间戳 */
    timestamp: number;
}

/**
 * 通信插件选项
 */
export interface CommunicationPluginOptions {
    /** 是否启用全局状态管理 */
    enableGlobalState?: boolean;
    /** 是否启用事件总线 */
    enableEventBus?: boolean;
    /** 消息超时时间 */
    messageTimeout?: number;
}

/**
 * 通信管理器
 */
export class CommunicationManager extends EventEmitter {
    private channels = new Map<string, EventEmitter>();
    private globalState = new Map<string, any>();
    private messageQueue: CommunicationMessage[] = [];
    private options: CommunicationPluginOptions;

    constructor(options: CommunicationPluginOptions = {}) {
        super();
        this.options = {
            enableGlobalState: true,
            enableEventBus: true,
            messageTimeout: 5000,
            ...options
        };
    }

    /**
     * 创建通信通道
     */
    createChannel(name: string): EventEmitter {
        if (this.channels.has(name)) {
            return this.channels.get(name)!;
        }

        const channel = new EventEmitter();
        this.channels.set(name, channel);
        this.emit('channel:created', { name });

        return channel;
    }

    /**
     * 获取通信通道
     */
    getChannel(name: string): EventEmitter | undefined {
        return this.channels.get(name);
    }

    /**
     * 销毁通信通道
     */
    destroyChannel(name: string): void {
        const channel = this.channels.get(name);
        if (channel) {
            channel.removeAllListeners();
            this.channels.delete(name);
            this.emit('channel:destroyed', { name });
        }
    }

    /**
     * 发送消息
     */
    sendMessage(message: CommunicationMessage): void {
        const { type, to } = message;

        // 添加时间戳和ID
        message.timestamp = Date.now();
        message.id = message.id || this.generateMessageId();

        if (to) {
            // 发送到指定通道
            const channel = this.getChannel(to);
            if (channel) {
                channel.emit(type, message);
            } else {
                // 通道不存在，加入消息队列
                this.messageQueue.push(message);
            }
        } else {
            // 广播消息
            this.emit(type, message);
            this.channels.forEach(channel => {
                channel.emit(type, message);
            });
        }
    }

    /**
     * 监听消息
     */
    onMessage(type: string, listener: (message: CommunicationMessage) => void, channel?: string): void {
        if (channel) {
            const channelInstance = this.getChannel(channel);
            if (channelInstance) {
                channelInstance.on(type, listener);
            }
        } else {
            this.on(type, listener);
        }
    }

    /**
     * 取消监听消息
     */
    offMessage(type: string, listener: (message: CommunicationMessage) => void, channel?: string): void {
        if (channel) {
            const channelInstance = this.getChannel(channel);
            if (channelInstance) {
                channelInstance.off(type, listener);
            }
        } else {
            this.off(type, listener);
        }
    }

    /**
     * 设置全局状态
     */
    setGlobalState(key: string, value: any): void {
        if (!this.options.enableGlobalState) {
            console.warn('Global state is disabled');
            return;
        }

        const oldValue = this.globalState.get(key);
        this.globalState.set(key, value);

        // 触发状态变更事件
        this.emit('state:changed', {
            key,
            value,
            oldValue,
            timestamp: Date.now()
        });
    }

    /**
     * 获取全局状态
     */
    getGlobalState(key: string): any {
        if (!this.options.enableGlobalState) {
            console.warn('Global state is disabled');
            return undefined;
        }

        return this.globalState.get(key);
    }

    /**
     * 删除全局状态
     */
    deleteGlobalState(key: string): boolean {
        if (!this.options.enableGlobalState) {
            console.warn('Global state is disabled');
            return false;
        }

        const existed = this.globalState.has(key);
        if (existed) {
            const oldValue = this.globalState.get(key);
            this.globalState.delete(key);

            // 触发状态删除事件
            this.emit('state:deleted', {
                key,
                oldValue,
                timestamp: Date.now()
            });
        }
        return existed;
    }

    /**
     * 获取所有全局状态
     */
    getAllGlobalState(): Record<string, any> {
        if (!this.options.enableGlobalState) {
            console.warn('Global state is disabled');
            return {};
        }

        const state: Record<string, any> = {};
        this.globalState.forEach((value, key) => {
            state[key] = value;
        });
        return state;
    }

    /**
     * 监听状态变更
     */
    onStateChange(key: string, listener: (data: any) => void): void {
        const wrappedListener = (data: any) => {
            if (data.key === key) {
                listener(data);
            }
        };
        this.on('state:changed', wrappedListener);
    }

    /**
     * 处理消息队列
     */
    private processMessageQueue(): void {
        const processedMessages: CommunicationMessage[] = [];

        this.messageQueue.forEach(message => {
            const { to } = message;
            if (to && this.channels.has(to)) {
                const channel = this.getChannel(to);
                if (channel) {
                    channel.emit(message.type, message);
                    processedMessages.push(message);
                }
            }
        });

        // 移除已处理的消息
        processedMessages.forEach(message => {
            const index = this.messageQueue.indexOf(message);
            if (index > -1) {
                this.messageQueue.splice(index, 1);
            }
        });
    }

    /**
     * 生成消息ID
     */
    private generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取通道列表
     */
    getChannelNames(): string[] {
        return Array.from(this.channels.keys());
    }

    /**
     * 清理所有通道和状态
     */
    clear(): void {
        // 清理所有通道
        this.channels.forEach((channel, name) => {
            this.destroyChannel(name);
        });

        // 清理全局状态
        this.globalState.clear();

        // 清理消息队列
        this.messageQueue.length = 0;

        // 移除所有监听器
        this.removeAllListeners();
    }
}

/**
 * 事件总线
 */
export class EventBus extends EventEmitter {
    private static instance: EventBus;

    /**
     * 获取单例实例
     */
    static getInstance(): EventBus {
        if (!EventBus.instance) {
            EventBus.instance = new EventBus();
        }
        return EventBus.instance;
    }

    /**
     * 发布事件
     */
    publish(event: string, data?: any): void {
        this.emit(event, data);
    }

    /**
     * 订阅事件
     */
    subscribe(event: string, listener: (...args: any[]) => void): void {
        this.on(event, listener);
    }

    /**
     * 取消订阅
     */
    unsubscribe(event: string, listener: (...args: any[]) => void): void {
        this.off(event, listener);
    }

    /**
     * 一次性订阅
     */
    subscribeOnce(event: string, listener: (...args: any[]) => void): void {
        this.once(event, listener);
    }
}

/**
 * 通信插件类
 */
export class CommunicationPlugin implements Plugin {
    name = 'communication';
    version = '1.0.0';
    private manager: CommunicationManager;
    private eventBus: EventBus;

    constructor(private options: CommunicationPluginOptions = {}) {
        this.manager = new CommunicationManager(options);
        this.eventBus = EventBus.getInstance();
    }

    /**
    /**
     * 安装插件
     */
    install(kernel: any): void {
        // 将通信管理器注册到内核
        kernel.communicationManager = this.manager;
        kernel.eventBus = this.eventBus;

        // 为每个应用创建专用通道
        if (kernel.on) {
            kernel.on('app:registered', (app: any) => {
                this.manager.createChannel(app.name);
            });

            // 应用卸载时清理通道
            kernel.on('app:unregistered', (app: any) => {
                this.manager.destroyChannel(app.name);
            });
        }

        // 定期处理消息队列
        setInterval(() => {
            this.manager['processMessageQueue']();
        }, 1000);
    }

    /**
     * 卸载插件
     */
    uninstall(): void {
        this.manager.clear();
    }

    /**
     * 获取通信管理器
     */
    getManager(): CommunicationManager {
        return this.manager;
    }

    /**
     * 获取事件总线
     */
    getEventBus(): EventBus {
        return this.eventBus;
    }
}

/**
 * Enhanced Communication Plugin
 * Provides advanced inter-application communication features
 */
export class EnhancedCommunicationPlugin implements Plugin {
    name = 'communication-enhanced';
    version = '2.0.0';
    private manager: EnhancedCommunicationManager;
    private eventBus: EventBus;

    constructor(private options: CommunicationPluginOptions = {}) {
        this.manager = new EnhancedCommunicationManager();
        this.eventBus = EventBus.getInstance();
    }

    /**
     * 安装插件
     */
    install(kernel: any): void {
        // 将增强通信管理器注册到内核
        kernel.communicationManager = this.manager;
        kernel.eventBus = this.eventBus;

        // 为每个应用创建专用通道
        if (kernel.on) {
            kernel.on('app:registered', (app: any) => {
                this.manager.createChannel({
                    name: app.name,
                    maxQueueSize: 1000,
                    defaultTimeout: 5000,
                    persistent: false
                });
                this.manager.subscribe(app.name, app.name);
            });

            // 应用卸载时清理通道
            kernel.on('app:unregistered', (app: any) => {
                this.manager.destroyChannel(app.name);
            });
        }

        console.log('[EnhancedCommunicationPlugin] Enhanced communication plugin installed');
    }

    /**
     * 卸载插件
     */
    uninstall(): void {
        this.manager.clear();
        console.log('[EnhancedCommunicationPlugin] Enhanced communication plugin uninstalled');
    }

    /**
     * 获取增强通信管理器
     */
    getManager(): EnhancedCommunicationManager {
        return this.manager;
    }

    /**
     * 获取事件总线
     */
    getEventBus(): EventBus {
        return this.eventBus;
    }

    /**
     * 创建通信通道
     */
    createChannel(config: ChannelConfig): void {
        this.manager.createChannel(config);
    }

    /**
     * 发送消息
     */
    sendMessage(message: Partial<EnhancedCommunicationMessage>): string {
        return this.manager.sendMessage(message);
    }

    /**
     * 添加消息处理器
     */
    addMessageHandler(channelName: string, messageType: string, handler: MessageHandler): void {
        this.manager.addMessageHandler(channelName, messageType, handler);
    }

    /**
     * 获取通信统计信息
     */
    getStats(): CommunicationStats {
        return this.manager.getStats();
    }

    /**
     * 获取所有通道信息
     */
    getAllChannelsInfo(): Array<{
        name: string;
        subscribers: string[];
        queuedMessages: number;
        config: ChannelConfig;
    }> {
        return this.manager.getAllChannelsInfo();
    }
}

// 工厂函数
export function createCommunicationPlugin(options?: CommunicationPluginOptions): CommunicationPlugin {
    return new CommunicationPlugin(options);
}

export function createEnhancedCommunicationPlugin(options?: CommunicationPluginOptions): EnhancedCommunicationPlugin {
    return new EnhancedCommunicationPlugin(options);
}

// 默认导出增强版本
export default EnhancedCommunicationPlugin;

// 导出类型
export type {
    ChannelConfig, CommunicationMessage,
    CommunicationPluginOptions, CommunicationStats, EnhancedCommunicationMessage, MessageFilter, MessageHandler
};
