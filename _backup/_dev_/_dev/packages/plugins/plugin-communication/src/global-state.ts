import { logger } from '@micro-core/core';
import { EventBus } from './event-bus';

/**
 * 状态变化监听器
 */
export type StateChangeListener<T = any> = (newValue: T, oldValue: T, key: string) => void;

/**
 * 全局状态管理器
 * 提供跨应用的状态共享能力
 */
export class GlobalState {
    private state = new Map<string, any>();
    private eventBus = new EventBus();
    private watchers = new Map<string, Set<StateChangeListener>>();

    /**
     * 设置状态值
     */
    set<T = any>(key: string, value: T): void {
        const oldValue = this.state.get(key);

        // 检查值是否发生变化
        if (this.isEqual(oldValue, value)) {
            return;
        }

        this.state.set(key, value);

        // 触发状态变化事件
        this.notifyStateChange(key, value, oldValue);

        logger.debug(`设置全局状态: ${key}`, { oldValue, newValue: value });
    }

    /**
     * 获取状态值
     */
    get<T = any>(key: string): T | undefined {
        return this.state.get(key);
    }

    /**
     * 获取状态值，如果不存在则返回默认值
     */
    getWithDefault<T = any>(key: string, defaultValue: T): T {
        return this.state.has(key) ? this.state.get(key) : defaultValue;
    }

    /**
     * 检查状态是否存在
     */
    has(key: string): boolean {
        return this.state.has(key);
    }

    /**
     * 删除状态
     */
    delete(key: string): boolean {
        if (!this.state.has(key)) {
            return false;
        }

        const oldValue = this.state.get(key);
        const deleted = this.state.delete(key);

        if (deleted) {
            // 触发状态变化事件
            this.notifyStateChange(key, undefined, oldValue);
            logger.debug(`删除全局状态: ${key}`, { oldValue });
        }

        return deleted;
    }

    /**
     * 清除所有状态
     */
    clear(): void {
        const keys = Array.from(this.state.keys());
        this.state.clear();
        this.watchers.clear();

        // 触发清除事件
        this.eventBus.emitSync('state:cleared', { keys });

        logger.debug('清除所有全局状态');
    }

    /**
     * 获取所有状态键
     */
    keys(): string[] {
        return Array.from(this.state.keys());
    }

    /**
     * 获取所有状态值
     */
    values(): any[] {
        return Array.from(this.state.values());
    }

    /**
     * 获取所有状态条目
     */
    entries(): [string, any][] {
        return Array.from(this.state.entries());
    }

    /**
     * 获取状态数量
     */
    size(): number {
        return this.state.size;
    }

    /**
     * 监听状态变化
     */
    watch<T = any>(key: string, listener: StateChangeListener<T>): void {
        if (!this.watchers.has(key)) {
            this.watchers.set(key, new Set());
        }

        this.watchers.get(key)!.add(listener);
        logger.debug(`添加状态监听器: ${key}`);
    }

    /**
     * 取消监听状态变化
     */
    unwatch<T = any>(key: string, listener?: StateChangeListener<T>): void {
        if (!listener) {
            // 移除所有监听器
            this.watchers.delete(key);
            logger.debug(`移除状态 ${key} 的所有监听器`);
            return;
        }

        const keyWatchers = this.watchers.get(key);
        if (keyWatchers) {
            keyWatchers.delete(listener);
            if (keyWatchers.size === 0) {
                this.watchers.delete(key);
            }
        }

        logger.debug(`移除状态监听器: ${key}`);
    }

    /**
     * 监听全局状态事件
     */
    on(event: string, listener: (data: any) => void): void {
        this.eventBus.on(event, listener);
    }

    /**
     * 移除全局状态事件监听
     */
    off(event: string, listener?: (data: any) => void): void {
        this.eventBus.off(event, listener);
    }

    /**
     * 批量设置状态
     */
    setBatch(states: Record<string, any>): void {
        const changes: Array<{ key: string; newValue: any; oldValue: any }> = [];

        for (const [key, value] of Object.entries(states)) {
            const oldValue = this.state.get(key);

            if (!this.isEqual(oldValue, value)) {
                this.state.set(key, value);
                changes.push({ key, newValue: value, oldValue });
            }
        }

        // 批量触发状态变化
        changes.forEach(({ key, newValue, oldValue }) => {
            this.notifyStateChange(key, newValue, oldValue);
        });

        if (changes.length > 0) {
            logger.debug(`批量设置全局状态`, changes);
        }
    }

    /**
     * 获取状态快照
     */
    getSnapshot(): Record<string, any> {
        const snapshot: Record<string, any> = {};
        for (const [key, value] of this.state.entries()) {
            snapshot[key] = this.deepClone(value);
        }
        return snapshot;
    }

    /**
     * 从快照恢复状态
     */
    restoreFromSnapshot(snapshot: Record<string, any>): void {
        this.clear();
        this.setBatch(snapshot);
        logger.debug('从快照恢复全局状态');
    }

    /**
     * 通知状态变化
     */
    private notifyStateChange(key: string, newValue: any, oldValue: any): void {
        // 触发特定键的监听器
        const keyWatchers = this.watchers.get(key);
        if (keyWatchers) {
            for (const watcher of keyWatchers) {
                try {
                    watcher(newValue, oldValue, key);
                } catch (error) {
                    logger.error(`状态监听器执行失败 (${key}):`, error);
                }
            }
        }

        // 触发全局状态变化事件
        this.eventBus.emitSync('state:changed', {
            key,
            newValue,
            oldValue
        });
    }

    /**
     * 比较两个值是否相等
     */
    private isEqual(a: any, b: any): boolean {
        if (a === b) {
            return true;
        }

        if (a == null || b == null) {
            return a === b;
        }

        if (typeof a !== typeof b) {
            return false;
        }

        if (typeof a === 'object') {
            return JSON.stringify(a) === JSON.stringify(b);
        }

        return false;
    }

    /**
     * 深度克隆对象
     */
    private deepClone(obj: any): any {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }

        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }

        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }

        if (typeof obj === 'object') {
            const cloned: any = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }

        return obj;
    }
}