{"name": "@micro-core/plugin-sandbox-composer", "version": "0.1.0", "description": "沙箱组合器插件，允许将多种沙箱策略组合使用", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest"}, "keywords": ["micro-frontend", "sandbox", "composer", "plugin", "微前端"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"typescript": "^5.3.3", "vitest": "^3.2.4"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-sandbox-composer"}, "publishConfig": {"access": "public"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}