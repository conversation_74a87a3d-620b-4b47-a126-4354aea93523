import type { Plugin } from '@micro-core/core';
import { SandboxComposer } from './sandbox-composer';
import type { SandboxComposerPluginOptions } from './types';

/**
 * 沙箱组合器插件
 * 允许将多种沙箱策略组合使用
 */
export class SandboxComposerPlugin implements Plugin {
    name = 'sandbox-composer';
    private options: SandboxComposerPluginOptions;
    private composers = new Map<string, SandboxComposer>();

    constructor(options: SandboxComposerPluginOptions = {}) {
        this.options = {
            enableAutoCompose: true,
            defaultStrategy: ['proxy', 'namespace'],
            ...options
        };
    }

    /**
     * 插件安装
     */
    install(kernel: any) {
        // 注册沙箱组合器到内核
        kernel.sandboxComposer = {
            create: (name: string, strategies: string[]) => {
                return this.createComposer(name, strategies);
            },
            get: (name: string) => {
                return this.composers.get(name);
            },
            destroy: (name: string) => {
                const composer = this.composers.get(name);
                if (composer) {
                    composer.destroy();
                    this.composers.delete(name);
                }
            }
        };

        // 监听应用加载事件，自动创建组合沙箱
        kernel.hooks.beforeAppLoad.tap('SandboxComposerPlugin', (appName: string, appConfig: any) => {
            if (this.options.enableAutoCompose) {
                const strategies = appConfig.sandboxStrategies || this.options.defaultStrategy;
                this.createComposer(appName, strategies);
            }
        });

        // 监听应用卸载事件，清理组合沙箱
        kernel.hooks.afterAppUnmount.tap('SandboxComposerPlugin', (appName: string) => {
            const composer = this.composers.get(appName);
            if (composer) {
                composer.destroy();
                this.composers.delete(appName);
            }
        });

        console.log('[SandboxComposerPlugin] 沙箱组合器插件已安装');
    }

    /**
     * 插件卸载
     */
    uninstall() {
        // 销毁所有组合器
        for (const composer of this.composers.values()) {
            composer.destroy();
        }
        this.composers.clear();
        console.log('[SandboxComposerPlugin] 沙箱组合器插件已卸载');
    }

    /**
     * 创建沙箱组合器
     */
    private createComposer(name: string, strategies: string[]): SandboxComposer {
        const composer = new SandboxComposer(name, {
            strategies,
            enableLogging: this.options.enableLogging
        });

        this.composers.set(name, composer);
        return composer;
    }

    /**
     * 获取所有组合器
     */
    getAllComposers(): SandboxComposer[] {
        return Array.from(this.composers.values());
    }
}