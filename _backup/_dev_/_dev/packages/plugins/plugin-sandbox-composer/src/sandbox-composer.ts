import type { SandboxComposerOptions, SandboxInstance, SandboxStrategy } from './types';

/**
 * 沙箱组合器
 * 负责管理和协调多个沙箱策略
 */
export class SandboxComposer {
    private name: string;
    private options: SandboxComposerOptions;
    private sandboxes: SandboxInstance[] = [];
    private isActive = false;

    constructor(name: string, options: SandboxComposerOptions) {
        this.name = name;
        this.options = {
            enableLogging: false,
            ...options
        };

        this.initializeSandboxes();
    }

    /**
     * 初始化沙箱实例
     */
    private initializeSandboxes() {
        const { strategies } = this.options;

        for (const strategy of strategies) {
            try {
                const sandbox = this.createSandboxInstance(strategy);
                if (sandbox) {
                    this.sandboxes.push(sandbox);
                    this.log(`沙箱策略 ${strategy} 初始化成功`);
                }
            } catch (error) {
                console.error(`[SandboxComposer] 沙箱策略 ${strategy} 初始化失败:`, error);
            }
        }
    }

    /**
     * 创建沙箱实例
     */
    private createSandboxInstance(strategy: SandboxStrategy): SandboxInstance | null {
        switch (strategy) {
            case 'proxy':
                return this.createProxySandbox();
            case 'iframe':
                return this.createIframeSandbox();
            case 'webcomponent':
                return this.createWebComponentSandbox();
            case 'namespace':
                return this.createNamespaceSandbox();
            case 'defineproperty':
                return this.createDefinePropertySandbox();
            case 'federation':
                return this.createFederationSandbox();
            default:
                console.warn(`[SandboxComposer] 未知的沙箱策略: ${strategy}`);
                return null;
        }
    }

    /**
     * 创建 Proxy 沙箱
     */
    private createProxySandbox(): SandboxInstance {
        const fakeWindow = Object.create(null);
        const proxy = new Proxy(fakeWindow, {
            get: (target, prop) => {
                if (prop in target) {
                    return target[prop];
                }
                return (window as any)[prop];
            },
            set: (target, prop, value) => {
                target[prop] = value;
                return true;
            },
            has: (target, prop) => {
                return prop in target || prop in window;
            }
        });

        return {
            type: 'proxy',
            instance: proxy,
            activate: () => {
                (window as any).__MICRO_SANDBOX_PROXY__ = proxy;
                this.log('Proxy 沙箱已激活');
            },
            deactivate: () => {
                delete (window as any).__MICRO_SANDBOX_PROXY__;
                this.log('Proxy 沙箱已停用');
            },
            destroy: () => {
                // Proxy 沙箱无需特殊清理
                this.log('Proxy 沙箱已销毁');
            }
        };
    }

    /**
     * 创建 Iframe 沙箱
     */
    private createIframeSandbox(): SandboxInstance {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = 'about:blank';
        document.body.appendChild(iframe);

        return {
            type: 'iframe',
            instance: iframe,
            activate: () => {
                iframe.style.display = 'block';
                this.log('Iframe 沙箱已激活');
            },
            deactivate: () => {
                iframe.style.display = 'none';
                this.log('Iframe 沙箱已停用');
            },
            destroy: () => {
                if (iframe.parentNode) {
                    iframe.parentNode.removeChild(iframe);
                }
                this.log('Iframe 沙箱已销毁');
            }
        };
    }

    /**
     * 创建 WebComponent 沙箱
     */
    private createWebComponentSandbox(): SandboxInstance {
        const container = document.createElement('div');
        const shadowRoot = container.attachShadow({ mode: 'closed' });

        return {
            type: 'webcomponent',
            instance: { container, shadowRoot },
            activate: () => {
                document.body.appendChild(container);
                this.log('WebComponent 沙箱已激活');
            },
            deactivate: () => {
                if (container.parentNode) {
                    container.parentNode.removeChild(container);
                }
                this.log('WebComponent 沙箱已停用');
            },
            destroy: () => {
                if (container.parentNode) {
                    container.parentNode.removeChild(container);
                }
                this.log('WebComponent 沙箱已销毁');
            }
        };
    }

    /**
     * 创建命名空间沙箱
     */
    private createNamespaceSandbox(): SandboxInstance {
        const namespace = `__MICRO_APP_${this.name.toUpperCase()}__`;
        (window as any)[namespace] = {};

        return {
            type: 'namespace',
            instance: (window as any)[namespace],
            activate: () => {
                this.log(`命名空间沙箱 ${namespace} 已激活`);
            },
            deactivate: () => {
                this.log(`命名空间沙箱 ${namespace} 已停用`);
            },
            destroy: () => {
                delete (window as any)[namespace];
                this.log(`命名空间沙箱 ${namespace} 已销毁`);
            }
        };
    }

    /**
     * 创建 DefineProperty 沙箱
     */
    private createDefinePropertySandbox(): SandboxInstance {
        const originalDescriptors = new Map();
        const modifiedProps = new Set();

        return {
            type: 'defineproperty',
            instance: { originalDescriptors, modifiedProps },
            activate: () => {
                this.log('DefineProperty 沙箱已激活');
            },
            deactivate: () => {
                // 恢复原始属性描述符
                for (const [prop, descriptor] of originalDescriptors) {
                    if (descriptor) {
                        Object.defineProperty(window, prop, descriptor);
                    } else {
                        delete (window as any)[prop];
                    }
                }
                this.log('DefineProperty 沙箱已停用');
            },
            destroy: () => {
                originalDescriptors.clear();
                modifiedProps.clear();
                this.log('DefineProperty 沙箱已销毁');
            }
        };
    }

    /**
     * 创建联邦组件沙箱
     */
    private createFederationSandbox(): SandboxInstance {
        const moduleCache = new Map();

        return {
            type: 'federation',
            instance: { moduleCache },
            activate: () => {
                this.log('联邦组件沙箱已激活');
            },
            deactivate: () => {
                this.log('联邦组件沙箱已停用');
            },
            destroy: () => {
                moduleCache.clear();
                this.log('联邦组件沙箱已销毁');
            }
        };
    }

    /**
     * 激活所有沙箱
     */
    activate() {
        if (this.isActive) return;

        for (const sandbox of this.sandboxes) {
            try {
                sandbox.activate();
            } catch (error) {
                console.error(`[SandboxComposer] 激活沙箱 ${sandbox.type} 失败:`, error);
            }
        }

        this.isActive = true;
        this.log('沙箱组合器已激活');
    }

    /**
     * 停用所有沙箱
     */
    deactivate() {
        if (!this.isActive) return;

        for (const sandbox of this.sandboxes) {
            try {
                sandbox.deactivate();
            } catch (error) {
                console.error(`[SandboxComposer] 停用沙箱 ${sandbox.type} 失败:`, error);
            }
        }

        this.isActive = false;
        this.log('沙箱组合器已停用');
    }

    /**
     * 销毁所有沙箱
     */
    destroy() {
        this.deactivate();

        for (const sandbox of this.sandboxes) {
            try {
                sandbox.destroy();
            } catch (error) {
                console.error(`[SandboxComposer] 销毁沙箱 ${sandbox.type} 失败:`, error);
            }
        }

        this.sandboxes.length = 0;
        this.log('沙箱组合器已销毁');
    }

    /**
     * 获取指定类型的沙箱实例
     */
    getSandbox(type: SandboxStrategy): SandboxInstance | undefined {
        return this.sandboxes.find(sandbox => sandbox.type === type);
    }

    /**
     * 获取所有沙箱实例
     */
    getAllSandboxes(): SandboxInstance[] {
        return [...this.sandboxes];
    }

    /**
     * 检查是否激活
     */
    isActivated(): boolean {
        return this.isActive;
    }

    /**
     * 记录日志
     */
    private log(message: string) {
        if (this.options.enableLogging) {
            console.log(`[SandboxComposer:${this.name}] ${message}`);
        }
    }
}