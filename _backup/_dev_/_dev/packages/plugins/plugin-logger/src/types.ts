/**
 * 日志插件类型定义
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LoggerPluginOptions {
    /** 日志级别 */
    level?: LogLevel;
    /** 是否启用控制台输出 */
    enableConsole?: boolean;
    /** 是否启用远程日志 */
    enableRemote?: boolean;
    /** 远程日志地址 */
    remoteUrl?: string;
    /** 最大日志缓存数量 */
    maxLogSize?: number;
}

export interface LoggerOptions extends LoggerPluginOptions {
    level: LogLevel;
    enableConsole: boolean;
    enableRemote: boolean;
    maxLogSize: number;
}

export interface LogEntry {
    /** 日志级别 */
    level: LogLevel;
    /** 日志消息 */
    message: string;
    /** 附加参数 */
    args: any[];
    /** 时间戳 */
    timestamp: string;
    /** 日志来源 */
    source: string;
}