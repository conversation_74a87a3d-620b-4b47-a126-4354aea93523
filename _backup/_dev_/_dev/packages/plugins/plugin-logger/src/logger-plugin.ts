import type { Plugin } from '@micro-core/core';
import { createLogger } from './logger';
import type { LoggerPluginOptions } from './types';

/**
 * 日志管理插件
 * 提供统一的日志记录和管理功能
 */
export class LoggerPlugin implements Plugin {
    name = 'logger';
    private options: LoggerPluginOptions;
    private logger: ReturnType<typeof createLogger>;

    constructor(options: LoggerPluginOptions = {}) {
        this.options = {
            level: 'info',
            enableConsole: true,
            enableRemote: false,
            maxLogSize: 1000,
            ...options
        };

        this.logger = createLogger(this.options);
    }

    /**
     * 插件安装
     */
    install(kernel: any) {
        // 将日志器注入到内核中
        kernel.logger = this.logger;

        // 监听应用生命周期事件并记录日志
        kernel.hooks.beforeAppLoad.tap('LoggerPlugin', (appName: string) => {
            this.logger.info(`应用 ${appName} 开始加载`);
        });

        kernel.hooks.afterAppLoad.tap('LoggerPlugin', (appName: string) => {
            this.logger.info(`应用 ${appName} 加载完成`);
        });

        kernel.hooks.beforeAppMount.tap('LoggerPlugin', (appName: string) => {
            this.logger.info(`应用 ${appName} 开始挂载`);
        });

        kernel.hooks.afterAppMount.tap('LoggerPlugin', (appName: string) => {
            this.logger.info(`应用 ${appName} 挂载完成`);
        });

        kernel.hooks.beforeAppUnmount.tap('LoggerPlugin', (appName: string) => {
            this.logger.info(`应用 ${appName} 开始卸载`);
        });

        kernel.hooks.afterAppUnmount.tap('LoggerPlugin', (appName: string) => {
            this.logger.info(`应用 ${appName} 卸载完成`);
        });

        // 监听错误事件
        kernel.hooks.appError.tap('LoggerPlugin', (error: Error, appName: string) => {
            this.logger.error(`应用 ${appName} 发生错误:`, error);
        });

        console.log('[LoggerPlugin] 日志插件已安装');
    }

    /**
     * 插件卸载
     */
    uninstall() {
        // 清理日志缓存
        this.logger.clear();
        console.log('[LoggerPlugin] 日志插件已卸载');
    }

    /**
     * 获取日志器实例
     */
    getLogger() {
        return this.logger;
    }
}