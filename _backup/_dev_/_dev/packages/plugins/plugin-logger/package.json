{"name": "@micro-core/plugin-logger", "version": "0.1.0", "description": "日志管理插件，提供统一的日志记录和管理功能", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest"}, "keywords": ["micro-frontend", "logger", "plugin", "微前端"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"typescript": "^5.3.3", "vitest": "^3.2.4"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-logger"}, "publishConfig": {"access": "public"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}