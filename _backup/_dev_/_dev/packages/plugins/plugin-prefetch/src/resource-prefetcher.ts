/**
 * 资源预加载器
 */

import type { PrefetchConfig, PrefetchItem } from './types';

/**
 * 资源预加载器类
 */
export class ResourcePrefetcher {
    private config: PrefetchConfig;
    private cache = new Map<string, any>();
    private loadingPromises = new Map<string, Promise<any>>();

    constructor(config: PrefetchConfig) {
        this.config = config;
    }

    /**
     * 预加载资源
     */
    async prefetch(item: PrefetchItem): Promise<void> {
        const { url, type } = item;

        // 检查缓存
        if (this.config.enableCache && this.cache.has(url)) {
            return;
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(url)) {
            return this.loadingPromises.get(url);
        }

        // 开始加载
        const promise = this.loadResource(url, type);
        this.loadingPromises.set(url, promise);

        try {
            const result = await promise;

            // 缓存结果
            if (this.config.enableCache) {
                this.cache.set(url, result);
            }
        } finally {
            this.loadingPromises.delete(url);
        }
    }

    /**
     * 加载资源
     */
    private async loadResource(url: string, type: PrefetchItem['type']): Promise<any> {
        switch (type) {
            case 'script':
                return this.loadScript(url);
            case 'style':
                return this.loadStyle(url);
            case 'image':
                return this.loadImage(url);
            case 'font':
                return this.loadFont(url);
            case 'document':
                return this.loadDocument(url);
            default:
                throw new Error(`不支持的资源类型: ${type}`);
        }
    }

    /**
     * 加载脚本
     */
    private loadScript(url: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url;
            link.as = 'script';

            link.onload = () => resolve();
            link.onerror = () => reject(new Error(`脚本加载失败: ${url}`));

            document.head.appendChild(link);
        });
    }

    /**
     * 加载样式
     */
    private loadStyle(url: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url;
            link.as = 'style';

            link.onload = () => resolve();
            link.onerror = () => reject(new Error(`样式加载失败: ${url}`));

            document.head.appendChild(link);
        });
    }

    /**
     * 加载图片
     */
    private loadImage(url: string): Promise<HTMLImageElement> {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`图片加载失败: ${url}`));
            img.src = url;
        });
    }

    /**
     * 加载字体
     */
    private loadFont(url: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url;
            link.as = 'font';
            link.crossOrigin = 'anonymous';

            link.onload = () => resolve();
            link.onerror = () => reject(new Error(`字体加载失败: ${url}`));

            document.head.appendChild(link);
        });
    }

    /**
     * 加载文档
     */
    private async loadDocument(url: string): Promise<string> {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.text();
        } catch (error) {
            throw new Error(`文档加载失败: ${url} - ${error}`);
        }
    }

    /**
     * 获取缓存的资源
     */
    getCached(url: string): any {
        return this.cache.get(url);
    }

    /**
     * 检查资源是否已缓存
     */
    isCached(url: string): boolean {
        return this.cache.has(url);
    }

    /**
     * 清除缓存
     */
    clearCache(): void {
        this.cache.clear();
    }

    /**
     * 销毁预加载器
     */
    destroy(): void {
        this.cache.clear();
        this.loadingPromises.clear();
    }
}