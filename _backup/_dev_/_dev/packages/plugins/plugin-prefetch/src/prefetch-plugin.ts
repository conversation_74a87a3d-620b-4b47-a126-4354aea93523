/**
 * 智能预加载插件
 */

import type { Plugin } from '@micro-core/core';
import { ResourcePrefetcher } from './resource-prefetcher';
import { RoutePredictor } from './route-predictor';
import type { PrefetchConfig, PrefetchEvent, PrefetchItem, PrefetchStats } from './types';
import { ViewportDetector } from './viewport-detector';

/**
 * 智能预加载插件类
 */
export class PrefetchPlugin implements Plugin {
    name = 'prefetch';
    version = '0.1.0';

    private config: PrefetchConfig;
    private resourcePrefetcher: ResourcePrefetcher;
    private routePredictor: RoutePredictor;
    private viewportDetector: ViewportDetector;
    private prefetchQueue: PrefetchItem[] = [];
    private stats: PrefetchStats = {
        total: 0,
        success: 0,
        failed: 0,
        cacheHit: 0,
        avgDuration: 0,
        timeSaved: 0,
        predictionAccuracy: 0
    };

    constructor(config: PrefetchConfig) {
        this.config = {
            strategy: ['idle'],
            priority: 'auto',
            maxConcurrent: 3,
            timeout: 10000,
            enableCache: true,
            cacheStrategy: 'memory',
            networkConditions: {
                minSpeed: 1,
                allowMobile: false,
                allowSaveData: false
            },
            prediction: {
                algorithm: 'frequency',
                threshold: 0.7,
                historyDays: 30
            },
            ...config
        };

        this.resourcePrefetcher = new ResourcePrefetcher(this.config);
        this.routePredictor = new RoutePredictor(this.config.prediction!);
        this.viewportDetector = new ViewportDetector();

        this.init();
    }

    /**
     * 初始化插件
     */
    private init(): void {
        // 监听路由变化
        this.setupRouteListener();

        // 设置空闲时预加载
        if (this.config.strategy.includes('idle')) {
            this.setupIdlePrefetch();
        }

        // 设置可见性预加载
        if (this.config.strategy.includes('visible')) {
            this.setupVisibilityPrefetch();
        }

        // 设置悬停预加载
        if (this.config.strategy.includes('hover')) {
            this.setupHoverPrefetch();
        }

        // 设置路由预测预加载
        if (this.config.strategy.includes('route-predict')) {
            this.setupRoutePredictPrefetch();
        }
    }

    /**
     * 预加载资源
     */
    async prefetch(url: string, type: PrefetchItem['type'] = 'script', priority: number = 1): Promise<void> {
        const item: PrefetchItem = {
            url,
            type,
            priority,
            strategy: 'manual',
            prefetched: false
        };

        await this.prefetchItem(item);
    }

    /**
     * 批量预加载资源
     */
    async prefetchBatch(items: Omit<PrefetchItem, 'prefetched'>[]): Promise<void> {
        const prefetchItems = items.map(item => ({
            ...item,
            prefetched: false
        }));

        // 按优先级排序
        prefetchItems.sort((a, b) => b.priority - a.priority);

        // 添加到队列
        this.prefetchQueue.push(...prefetchItems);

        // 处理队列
        await this.processQueue();
    }

    /**
     * 预加载单个资源
     */
    private async prefetchItem(item: PrefetchItem): Promise<void> {
        if (item.prefetched) return;

        // 检查网络条件
        if (!this.checkNetworkConditions()) {
            return;
        }

        const startTime = Date.now();
        this.emitEvent('start', item);

        try {
            await this.resourcePrefetcher.prefetch(item);

            const duration = Date.now() - startTime;
            item.prefetched = true;
            item.prefetchedAt = Date.now();
            item.duration = duration;

            this.updateStats('success', duration);
            this.emitEvent('success', item);
        } catch (error) {
            item.error = error instanceof Error ? error.message : String(error);
            this.updateStats('failed');
            this.emitEvent('error', item, error);
        }
    }

    /**
     * 处理预加载队列
     */
    private async processQueue(): Promise<void> {
        const maxConcurrent = this.config.maxConcurrent || 3;
        const processing: Promise<void>[] = [];

        while (this.prefetchQueue.length > 0 || processing.length > 0) {
            // 启动新的预加载任务
            while (processing.length < maxConcurrent && this.prefetchQueue.length > 0) {
                const item = this.prefetchQueue.shift()!;
                const promise = this.prefetchItem(item);
                processing.push(promise);
            }

            // 等待至少一个任务完成
            if (processing.length > 0) {
                await Promise.race(processing);
                // 移除已完成的任务
                for (let i = processing.length - 1; i >= 0; i--) {
                    const promise = processing[i];
                    if (await this.isPromiseSettled(promise)) {
                        processing.splice(i, 1);
                    }
                }
            }
        }
    }

    /**
     * 检查 Promise 是否已完成
     */
    private async isPromiseSettled(promise: Promise<void>): Promise<boolean> {
        try {
            await Promise.race([
                promise,
                new Promise(resolve => setTimeout(resolve, 0))
            ]);
            return true;
        } catch {
            return true;
        }
    }

    /**
     * 设置路由监听
     */
    private setupRouteListener(): void {
        // 监听 popstate 事件
        window.addEventListener('popstate', () => {
            this.handleRouteChange();
        });

        // 监听 pushState 和 replaceState
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;

        history.pushState = (...args) => {
            originalPushState.apply(history, args);
            this.handleRouteChange();
        };

        history.replaceState = (...args) => {
            originalReplaceState.apply(history, args);
            this.handleRouteChange();
        };
    }

    /**
     * 处理路由变化
     */
    private handleRouteChange(): void {
        const currentRoute = window.location.pathname;
        this.routePredictor.recordRoute(currentRoute);

        // 预测下一个路由并预加载
        if (this.config.strategy.includes('route-predict')) {
            const predictions = this.routePredictor.predict(currentRoute);
            predictions.forEach(prediction => {
                if (prediction.probability >= (this.config.prediction?.threshold || 0.7)) {
                    this.prefetchRouteResources(prediction.nextRoute);
                }
            });
        }
    }

    /**
     * 预加载路由相关资源
     */
    private prefetchRouteResources(route: string): void {
        // 这里应该根据路由获取相关资源
        // 暂时使用模拟数据
        const resources = this.getRouteResources(route);
        resources.forEach(resource => {
            this.prefetchQueue.push({
                url: resource.url,
                type: resource.type,
                priority: 2,
                strategy: 'route-predict',
                prefetched: false
            });
        });

        this.processQueue();
    }

    /**
     * 获取路由相关资源
     */
    private getRouteResources(route: string): Array<{ url: string; type: PrefetchItem['type'] }> {
        // 这里应该实现实际的资源获取逻辑
        // 暂时返回模拟数据
        return [
            { url: `/assets/route${route}.js`, type: 'script' },
            { url: `/assets/route${route}.css`, type: 'style' }
        ];
    }

    /**
     * 设置空闲时预加载
     */
    private setupIdlePrefetch(): void {
        if ('requestIdleCallback' in window) {
            const idleCallback = () => {
                if (this.prefetchQueue.length > 0) {
                    this.processQueue();
                }
                requestIdleCallback(idleCallback);
            };
            requestIdleCallback(idleCallback);
        } else {
            // 降级到 setTimeout
            const fallbackCallback = () => {
                if (this.prefetchQueue.length > 0) {
                    this.processQueue();
                }
                setTimeout(fallbackCallback, 100);
            };
            setTimeout(fallbackCallback, 100);
        }
    }

    /**
     * 设置可见性预加载
     */
    private setupVisibilityPrefetch(): void {
        this.viewportDetector.observe((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target as HTMLElement;
                    const url = element.dataset.prefetch;
                    const type = (element.dataset.prefetchType as PrefetchItem['type']) || 'script';

                    if (url) {
                        this.prefetch(url, type, 3);
                    }
                }
            });
        });
    }

    /**
     * 设置悬停预加载
     */
    private setupHoverPrefetch(): void {
        document.addEventListener('mouseover', (event) => {
            const target = event.target as HTMLElement;
            const url = target.dataset.prefetch;
            const type = (target.dataset.prefetchType as PrefetchItem['type']) || 'script';

            if (url) {
                this.prefetch(url, type, 4);
            }
        });
    }

    /**
     * 设置路由预测预加载
     */
    private setupRoutePredictPrefetch(): void {
        // 已在 handleRouteChange 中实现
    }

    /**
     * 检查网络条件
     */
    private checkNetworkConditions(): boolean {
        const conditions = this.config.networkConditions;
        if (!conditions) return true;

        // 检查网络连接信息
        const connection = (navigator as any).connection;
        if (connection) {
            // 检查网络速度
            if (conditions.minSpeed && connection.downlink < conditions.minSpeed) {
                return false;
            }

            // 检查是否允许移动网络
            if (!conditions.allowMobile && connection.effectiveType === '2g') {
                return false;
            }

            // 检查是否允许省流量模式
            if (!conditions.allowSaveData && connection.saveData) {
                return false;
            }
        }

        return true;
    }

    /**
     * 更新统计信息
     */
    private updateStats(type: 'success' | 'failed', duration?: number): void {
        this.stats.total++;

        if (type === 'success') {
            this.stats.success++;
            if (duration) {
                this.stats.avgDuration = (this.stats.avgDuration * (this.stats.success - 1) + duration) / this.stats.success;
            }
        } else {
            this.stats.failed++;
        }
    }

    /**
     * 发送事件
     */
    private emitEvent(type: PrefetchEvent['type'], item: PrefetchItem, data?: any): void {
        const event: PrefetchEvent = {
            type,
            item,
            timestamp: Date.now(),
            data
        };

        // 这里可以发送到事件总线或执行回调
        console.debug('[PrefetchPlugin]', event);
    }

    /**
     * 获取统计信息
     */
    getStats(): PrefetchStats {
        return { ...this.stats };
    }

    /**
     * 清除缓存
     */
    clearCache(): void {
        this.resourcePrefetcher.clearCache();
    }

    /**
     * 销毁插件
     */
    destroy(): void {
        this.prefetchQueue = [];
        this.viewportDetector.disconnect();
        this.resourcePrefetcher.destroy();
    }
}