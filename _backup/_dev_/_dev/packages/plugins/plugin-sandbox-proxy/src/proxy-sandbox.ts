/**
 * Proxy 沙箱核心实现
 */

import { ProxyHandler } from './proxy-handler';

export interface ProxySandboxOptions {
    enableStyleIsolation?: boolean;
    enableScriptIsolation?: boolean;
    strictMode?: boolean;
    whiteList?: string[];
    blackList?: string[];
}

export class ProxySandbox {
    private appName: string;
    private options: ProxySandboxOptions;
    private proxyWindow: WindowProxy;
    private fakeWindow: Record<string, any>;
    private proxyHandler: ProxyHandler;
    private active = false;
    private addedPropsMapInSandbox = new Map<PropertyKey, any>();
    private modifiedPropsOriginalValueMapInSandbox = new Map<PropertyKey, any>();
    private currentUpdatedPropsValueMap = new Map<PropertyKey, any>();

    constructor(appName: string, options: ProxySandboxOptions = {}) {
        this.appName = appName;
        this.options = {
            enableStyleIsolation: true,
            enableScriptIsolation: true,
            strictMode: true,
            whiteList: [],
            blackList: [],
            ...options
        };

        this.init();
    }

    /**
     * 初始化沙箱
     */
    private init(): void {
        this.fakeWindow = Object.create(null);
        this.proxyHandler = new ProxyHandler(this.appName, this.options);

        this.proxyWindow = new Proxy(this.fakeWindow, {
            get: (target, prop, receiver) => {
                return this.proxyHandler.get(target, prop, receiver, {
                    addedPropsMapInSandbox: this.addedPropsMapInSandbox,
                    modifiedPropsOriginalValueMapInSandbox: this.modifiedPropsOriginalValueMapInSandbox,
                    currentUpdatedPropsValueMap: this.currentUpdatedPropsValueMap
                });
            },
            set: (target, prop, value, receiver) => {
                return this.proxyHandler.set(target, prop, value, receiver, {
                    addedPropsMapInSandbox: this.addedPropsMapInSandbox,
                    modifiedPropsOriginalValueMapInSandbox: this.modifiedPropsOriginalValueMapInSandbox,
                    currentUpdatedPropsValueMap: this.currentUpdatedPropsValueMap
                });
            },
            has: (target, prop) => {
                return this.proxyHandler.has(target, prop);
            },
            deleteProperty: (target, prop) => {
                return this.proxyHandler.deleteProperty(target, prop, {
                    addedPropsMapInSandbox: this.addedPropsMapInSandbox,
                    modifiedPropsOriginalValueMapInSandbox: this.modifiedPropsOriginalValueMapInSandbox,
                    currentUpdatedPropsValueMap: this.currentUpdatedPropsValueMap
                });
            },
            ownKeys: (target) => {
                return this.proxyHandler.ownKeys(target);
            },
            getOwnPropertyDescriptor: (target, prop) => {
                return this.proxyHandler.getOwnPropertyDescriptor(target, prop);
            },
            defineProperty: (target, prop, descriptor) => {
                return this.proxyHandler.defineProperty(target, prop, descriptor);
            }
        });

        console.log(`[ProxySandbox] ${this.appName} 沙箱已初始化`);
    }

    /**
     * 激活沙箱
     */
    activate(): void {
        if (this.active) {
            return;
        }

        this.active = true;

        // 恢复沙箱环境
        this.currentUpdatedPropsValueMap.forEach((value, prop) => {
            this.setWindowProp(prop, value);
        });

        console.log(`[ProxySandbox] ${this.appName} 沙箱已激活`);
    }

    /**
     * 停用沙箱
     */
    deactivate(): void {
        if (!this.active) {
            return;
        }

        this.active = false;

        // 恢复全局环境
        this.addedPropsMapInSandbox.forEach((_, prop) => {
            this.deleteWindowProp(prop);
        });

        this.modifiedPropsOriginalValueMapInSandbox.forEach((value, prop) => {
            this.setWindowProp(prop, value);
        });

        console.log(`[ProxySandbox] ${this.appName} 沙箱已停用`);
    }

    /**
     * 销毁沙箱
     */
    destroy(): void {
        this.deactivate();

        // 清理所有映射
        this.addedPropsMapInSandbox.clear();
        this.modifiedPropsOriginalValueMapInSandbox.clear();
        this.currentUpdatedPropsValueMap.clear();

        console.log(`[ProxySandbox] ${this.appName} 沙箱已销毁`);
    }

    /**
     * 获取代理窗口
     */
    getProxyWindow(): WindowProxy {
        return this.proxyWindow;
    }

    /**
     * 检查沙箱是否激活
     */
    isActive(): boolean {
        return this.active;
    }

    /**
     * 获取应用名称
     */
    getAppName(): string {
        return this.appName;
    }

    /**
     * 执行脚本
     */
    execScript(script: string): any {
        if (!this.active) {
            throw new Error(`沙箱 ${this.appName} 未激活，无法执行脚本`);
        }

        try {
            // 在沙箱环境中执行脚本
            const func = new Function('window', 'self', 'globalThis', script);
            return func.call(this.proxyWindow, this.proxyWindow, this.proxyWindow, this.proxyWindow);
        } catch (error) {
            console.error(`[ProxySandbox] ${this.appName} 脚本执行失败:`, error);
            throw error;
        }
    }

    /**
     * 设置窗口属性
     */
    private setWindowProp(prop: PropertyKey, value: any): void {
        try {
            (window as any)[prop] = value;
        } catch (error) {
            console.warn(`[ProxySandbox] 设置窗口属性失败: ${String(prop)}`, error);
        }
    }

    /**
     * 删除窗口属性
     */
    private deleteWindowProp(prop: PropertyKey): void {
        try {
            delete (window as any)[prop];
        } catch (error) {
            console.warn(`[ProxySandbox] 删除窗口属性失败: ${String(prop)}`, error);
        }
    }

    /**
     * 获取沙箱统计信息
     */
    getStats(): {
        appName: string;
        active: boolean;
        addedProps: number;
        modifiedProps: number;
        currentProps: number;
    } {
        return {
            appName: this.appName,
            active: this.active,
            addedProps: this.addedPropsMapInSandbox.size,
            modifiedProps: this.modifiedPropsOriginalValueMapInSandbox.size,
            currentProps: this.currentUpdatedPropsValueMap.size
        };
    }

    /**
     * 获取沙箱快照
     */
    getSnapshot(): {
        addedProps: Array<[PropertyKey, any]>;
        modifiedProps: Array<[PropertyKey, any]>;
        currentProps: Array<[PropertyKey, any]>;
    } {
        return {
            addedProps: Array.from(this.addedPropsMapInSandbox.entries()),
            modifiedProps: Array.from(this.modifiedPropsOriginalValueMapInSandbox.entries()),
            currentProps: Array.from(this.currentUpdatedPropsValueMap.entries())
        };
    }

    /**
     * 恢复沙箱快照
     */
    restoreSnapshot(snapshot: {
        addedProps: Array<[PropertyKey, any]>;
        modifiedProps: Array<[PropertyKey, any]>;
        currentProps: Array<[PropertyKey, any]>;
    }): void {
        this.addedPropsMapInSandbox.clear();
        this.modifiedPropsOriginalValueMapInSandbox.clear();
        this.currentUpdatedPropsValueMap.clear();

        snapshot.addedProps.forEach(([prop, value]) => {
            this.addedPropsMapInSandbox.set(prop, value);
        });

        snapshot.modifiedProps.forEach(([prop, value]) => {
            this.modifiedPropsOriginalValueMapInSandbox.set(prop, value);
        });

        snapshot.currentProps.forEach(([prop, value]) => {
            this.currentUpdatedPropsValueMap.set(prop, value);
        });

        console.log(`[ProxySandbox] ${this.appName} 沙箱快照已恢复`);
    }
}