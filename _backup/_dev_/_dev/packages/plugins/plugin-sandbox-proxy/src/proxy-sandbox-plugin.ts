/**
 * @fileoverview 代理沙箱插件 - 提供基于Proxy的应用隔离环境
 * <AUTHOR> <<EMAIL>>
 */

import type { AppInfo, MicroCoreKernel, Plugin } from '@micro-core/core';
import { ProxyHandler } from './proxy-handler';
import { ProxySandbox } from './proxy-sandbox';

/**
 * 代理沙箱插件配置
 */
export interface ProxySandboxPluginOptions {
    enableStyleIsolation?: boolean;
    enableScriptIsolation?: boolean;
    enableEventIsolation?: boolean;
    enableStorageIsolation?: boolean;
    enableHistoryIsolation?: boolean;
    strictMode?: boolean;
    whiteList?: string[];
    blackList?: string[];
    customProxyHandler?: Partial<ProxyHandler<any>>;
    onSandboxCreate?: (appName: string, sandbox: ProxySandbox) => void;
    onSandboxDestroy?: (appName: string, sandbox: ProxySandbox) => void;
    onSandboxError?: (appName: string, error: Error) => void;
}

/**
 * 代理沙箱插件
 * 提供基于Proxy的应用隔离环境
 */
export class ProxySandboxPlugin implements Plugin {
    public readonly name = 'proxy-sandbox';
    public readonly version = '1.0.0';

    private kernel?: MicroCoreKernel;
    private sandboxes: Map<string, ProxySandbox> = new Map();
    private options: ProxySandboxPluginOptions;

    constructor(options: ProxySandboxPluginOptions = {}) {
        this.options = {
            enableStyleIsolation: true,
            enableScriptIsolation: true,
            enableEventIsolation: true,
            enableStorageIsolation: false,
            enableHistoryIsolation: false,
            strictMode: false,
            whiteList: [],
            blackList: [],
            ...options
        };
    }

    /**
     * 安装插件
     */
    async install(kernel: MicroCoreKernel): Promise<void> {
        this.kernel = kernel;

        // 注册生命周期钩子
        this.registerHooks();

        // 注册沙箱创建器
        this.registerSandboxCreator();

        console.log('[ProxySandboxPlugin] 代理沙箱插件已安装');
    }

    /**
     * 卸载插件
     */
    async uninstall(): Promise<void> {
        // 销毁所有沙箱
        for (const [appName, sandbox] of this.sandboxes) {
            try {
                await this.destroySandbox(appName);
            } catch (error) {
                console.error(`[ProxySandboxPlugin] 销毁沙箱失败: ${appName}`, error);
            }
        }

        this.sandboxes.clear();
        this.kernel = undefined;

        console.log('[ProxySandboxPlugin] 代理沙箱插件已卸载');
    }

    /**
     * 创建沙箱
     */
    async createSandbox(appName: string, appInfo: AppInfo): Promise<ProxySandbox> {
        try {
            // 检查是否已存在沙箱
            if (this.sandboxes.has(appName)) {
                console.warn(`[ProxySandboxPlugin] 沙箱已存在: ${appName}`);
                return this.sandboxes.get(appName)!;
            }

            // 创建代理处理器
            const proxyHandler = new ProxyHandler({
                appName,
                strictMode: this.options.strictMode,
                whiteList: this.options.whiteList,
                blackList: this.options.blackList,
                ...this.options.customProxyHandler
            });

            // 创建沙箱实例
            const sandbox = new ProxySandbox({
                appName,
                proxyHandler,
                enableStyleIsolation: this.options.enableStyleIsolation,
                enableScriptIsolation: this.options.enableScriptIsolation,
                enableEventIsolation: this.options.enableEventIsolation,
                enableStorageIsolation: this.options.enableStorageIsolation,
                enableHistoryIsolation: this.options.enableHistoryIsolation
            });

            // 初始化沙箱
            await sandbox.initialize();

            // 保存沙箱实例
            this.sandboxes.set(appName, sandbox);

            // 调用创建回调
            if (this.options.onSandboxCreate) {
                this.options.onSandboxCreate(appName, sandbox);
            }

            // 发射沙箱创建事件
            this.kernel?.getEventBus().emit('sandbox:created', {
                appName,
                sandbox,
                type: 'proxy'
            });

            console.log(`[ProxySandboxPlugin] 沙箱创建成功: ${appName}`);
            return sandbox;
        } catch (error) {
            console.error(`[ProxySandboxPlugin] 沙箱创建失败: ${appName}`, error);

            // 调用错误回调
            if (this.options.onSandboxError) {
                this.options.onSandboxError(appName, error as Error);
            }

            // 发射沙箱错误事件
            this.kernel?.getEventBus().emit('sandbox:error', {
                appName,
                error,
                type: 'proxy'
            });

            throw error;
        }
    }

    /**
     * 销毁沙箱
     */
    async destroySandbox(appName: string): Promise<void> {
        try {
            const sandbox = this.sandboxes.get(appName);
            if (!sandbox) {
                console.warn(`[ProxySandboxPlugin] 沙箱不存在: ${appName}`);
                return;
            }

            // 销毁沙箱
            await sandbox.destroy();

            // 移除沙箱实例
            this.sandboxes.delete(appName);

            // 调用销毁回调
            if (this.options.onSandboxDestroy) {
                this.options.onSandboxDestroy(appName, sandbox);
            }

            // 发射沙箱销毁事件
            this.kernel?.getEventBus().emit('sandbox:destroyed', {
                appName,
                sandbox,
                type: 'proxy'
            });

            console.log(`[ProxySandboxPlugin] 沙箱销毁成功: ${appName}`);
        } catch (error) {
            console.error(`[ProxySandboxPlugin] 沙箱销毁失败: ${appName}`, error);

            // 调用错误回调
            if (this.options.onSandboxError) {
                this.options.onSandboxError(appName, error as Error);
            }

            throw error;
        }
    }

    /**
     * 获取沙箱
     */
    getSandbox(appName: string): ProxySandbox | undefined {
        return this.sandboxes.get(appName);
    }

    /**
     * 获取所有沙箱
     */
    getAllSandboxes(): Map<string, ProxySandbox> {
        return new Map(this.sandboxes);
    }

    /**
     * 激活沙箱
     */
    async activateSandbox(appName: string): Promise<void> {
        const sandbox = this.sandboxes.get(appName);
        if (!sandbox) {
            throw new Error(`[ProxySandboxPlugin] 沙箱不存在: ${appName}`);
        }

        try {
            await sandbox.activate();

            // 发射沙箱激活事件
            this.kernel?.getEventBus().emit('sandbox:activated', {
                appName,
                sandbox,
                type: 'proxy'
            });

            console.log(`[ProxySandboxPlugin] 沙箱激活成功: ${appName}`);
        } catch (error) {
            console.error(`[ProxySandboxPlugin] 沙箱激活失败: ${appName}`, error);

            // 调用错误回调
            if (this.options.onSandboxError) {
                this.options.onSandboxError(appName, error as Error);
            }

            throw error;
        }
    }

    /**
     * 停用沙箱
     */
    async deactivateSandbox(appName: string): Promise<void> {
        const sandbox = this.sandboxes.get(appName);
        if (!sandbox) {
            console.warn(`[ProxySandboxPlugin] 沙箱不存在: ${appName}`);
            return;
        }

        try {
            await sandbox.deactivate();

            // 发射沙箱停用事件
            this.kernel?.getEventBus().emit('sandbox:deactivated', {
                appName,
                sandbox,
                type: 'proxy'
            });

            console.log(`[ProxySandboxPlugin] 沙箱停用成功: ${appName}`);
        } catch (error) {
            console.error(`[ProxySandboxPlugin] 沙箱停用失败: ${appName}`, error);

            // 调用错误回调
            if (this.options.onSandboxError) {
                this.options.onSandboxError(appName, error as Error);
            }

            throw error;
        }
    }

    /**
     * 检查沙箱状态
     */
    isSandboxActive(appName: string): boolean {
        const sandbox = this.sandboxes.get(appName);
        return sandbox ? sandbox.isActive() : false;
    }

    /**
     * 获取沙箱统计信息
     */
    getSandboxStats(): {
        total: number;
        active: number;
        inactive: number;
        sandboxes: Array<{
            appName: string;
            active: boolean;
            memoryUsage?: number;
            createdAt: number;
        }>;
    } {
        const sandboxes: Array<{
            appName: string;
            active: boolean;
            memoryUsage?: number;
            createdAt: number;
        }> = [];

        let active = 0;
        let inactive = 0;

        for (const [appName, sandbox] of this.sandboxes) {
            const isActive = sandbox.isActive();

            sandboxes.push({
                appName,
                active: isActive,
                memoryUsage: sandbox.getMemoryUsage?.(),
                createdAt: sandbox.getCreatedAt?.() || Date.now()
            });

            if (isActive) {
                active++;
            } else {
                inactive++;
            }
        }

        return {
            total: this.sandboxes.size,
            active,
            inactive,
            sandboxes
        };
    }

    /**
     * 注册生命周期钩子
     */
    private registerHooks(): void {
        if (!this.kernel) {
            return;
        }

        // 应用加载前创建沙箱
        this.kernel.registerHook('beforeAppLoad', async (appInfo: AppInfo) => {
            if (appInfo.sandbox?.type === 'proxy' || (!appInfo.sandbox?.type && this.options.enableScriptIsolation)) {
                await this.createSandbox(appInfo.name, appInfo);
            }
        });

        // 应用挂载前激活沙箱
        this.kernel.registerHook('beforeAppMount', async (appInfo: AppInfo) => {
            if (this.sandboxes.has(appInfo.name)) {
                await this.activateSandbox(appInfo.name);
            }
        });

        // 应用卸载后停用沙箱
        this.kernel.registerHook('afterAppUnmount', async (appInfo: AppInfo) => {
            if (this.sandboxes.has(appInfo.name)) {
                await this.deactivateSandbox(appInfo.name);
            }
        });

        // 应用注销时销毁沙箱
        this.kernel.registerHook('afterAppUnregister', async (appInfo: AppInfo) => {
            if (this.sandboxes.has(appInfo.name)) {
                await this.destroySandbox(appInfo.name);
            }
        });
    }

    /**
     * 注册沙箱创建器
     */
    private registerSandboxCreator(): void {
        if (!this.kernel) {
            return;
        }

        // 在内核上注册沙箱创建器
        (this.kernel as any).createProxySandbox = this.createSandbox.bind(this);
        (this.kernel as any).destroyProxySandbox = this.destroySandbox.bind(this);
        (this.kernel as any).getProxySandbox = this.getSandbox.bind(this);
        (this.kernel as any).activateProxySandbox = this.activateSandbox.bind(this);
        (this.kernel as any).deactivateProxySandbox = this.deactivateSandbox.bind(this);
        (this.kernel as any).getProxySandboxStats = this.getSandboxStats.bind(this);
    }
}

/**
 * 创建代理沙箱插件实例
 */
export function createProxySandboxPlugin(options?: ProxySandboxPluginOptions): ProxySandboxPlugin {
    return new ProxySandboxPlugin(options);
}