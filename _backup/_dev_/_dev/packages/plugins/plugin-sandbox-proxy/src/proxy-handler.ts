/**
 * Proxy 处理器配置 (get, set, has 等)
 */

export interface ProxyHandlerOptions {
    enableStyleIsolation?: boolean;
    enableScriptIsolation?: boolean;
    strictMode?: boolean;
    whiteList?: string[];
    blackList?: string[];
}

export interface SandboxContext {
    addedPropsMapInSandbox: Map<PropertyKey, any>;
    modifiedPropsOriginalValueMapInSandbox: Map<PropertyKey, any>;
    currentUpdatedPropsValueMap: Map<PropertyKey, any>;
}

export class ProxyHandler {
    private appName: string;
    private options: ProxyHandlerOptions;
    private unscopables = Symbol.for('unscopables');

    // 不可配置的属性列表
    private readonly nativeGlobalProperties = new Set([
        'window',
        'self',
        'globalThis',
        'document',
        'location',
        'top',
        'parent',
        'frames',
        'history',
        'navigator',
        'screen'
    ]);

    // 需要绑定原始上下文的属性
    private readonly boundProperties = new Set([
        'setTimeout',
        'setInterval',
        'clearTimeout',
        'clearInterval',
        'requestAnimationFrame',
        'cancelAnimationFrame',
        'requestIdleCallback',
        'cancelIdleCallback'
    ]);

    constructor(appName: string, options: ProxyHandlerOptions = {}) {
        this.appName = appName;
        this.options = options;
    }

    /**
     * get 陷阱
     */
    get(target: any, prop: PropertyKey, receiver: any, context: SandboxContext): any {
        // 处理 Symbol.unscopables
        if (prop === this.unscopables) {
            return undefined;
        }

        // 优先从沙箱中获取
        if (prop in target) {
            return target[prop];
        }

        // 检查黑名单
        if (this.isInBlackList(prop)) {
            return undefined;
        }

        // 从全局对象获取
        const value = (window as any)[prop];

        // 绑定原始上下文
        if (this.boundProperties.has(prop as string) && typeof value === 'function') {
            return value.bind(window);
        }

        // 特殊处理 document
        if (prop === 'document') {
            return this.wrapDocument(value);
        }

        // 特殊处理 location
        if (prop === 'location') {
            return this.wrapLocation(value);
        }

        return value;
    }

    /**
     * set 陷阱
     */
    set(target: any, prop: PropertyKey, value: any, receiver: any, context: SandboxContext): boolean {
        // 检查黑名单
        if (this.isInBlackList(prop)) {
            console.warn(`[ProxyHandler] ${this.appName} 尝试设置黑名单属性: ${String(prop)}`);
            return false;
        }

        // 检查是否为不可配置属性
        if (this.nativeGlobalProperties.has(prop as string)) {
            console.warn(`[ProxyHandler] ${this.appName} 尝试设置不可配置属性: ${String(prop)}`);
            return false;
        }

        const originalValue = (window as any)[prop];
        const hasOriginalValue = prop in window;

        // 记录变更
        if (!context.addedPropsMapInSandbox.has(prop) && !context.modifiedPropsOriginalValueMapInSandbox.has(prop)) {
            if (hasOriginalValue) {
                context.modifiedPropsOriginalValueMapInSandbox.set(prop, originalValue);
            } else {
                context.addedPropsMapInSandbox.set(prop, value);
            }
        }

        context.currentUpdatedPropsValueMap.set(prop, value);

        // 设置到沙箱和全局
        target[prop] = value;

        if (!this.options.strictMode) {
            try {
                (window as any)[prop] = value;
            } catch (error) {
                console.warn(`[ProxyHandler] 设置全局属性失败: ${String(prop)}`, error);
            }
        }

        return true;
    }

    /**
     * has 陷阱
     */
    has(target: any, prop: PropertyKey): boolean {
        // 检查黑名单
        if (this.isInBlackList(prop)) {
            return false;
        }

        return prop in target || prop in window;
    }

    /**
     * deleteProperty 陷阱
     */
    deleteProperty(target: any, prop: PropertyKey, context: SandboxContext): boolean {
        // 检查是否为不可配置属性
        if (this.nativeGlobalProperties.has(prop as string)) {
            return false;
        }

        if (prop in target) {
            delete target[prop];

            // 更新记录
            context.currentUpdatedPropsValueMap.delete(prop);

            // 如果是新增属性，从记录中移除
            if (context.addedPropsMapInSandbox.has(prop)) {
                context.addedPropsMapInSandbox.delete(prop);
            }
        }

        return true;
    }

    /**
     * ownKeys 陷阱
     */
    ownKeys(target: any): ArrayLike<string | symbol> {
        const targetKeys = Object.getOwnPropertyNames(target);
        const windowKeys = Object.getOwnPropertyNames(window);

        // 合并并去重
        const allKeys = Array.from(new Set([...targetKeys, ...windowKeys]));

        // 过滤黑名单
        return allKeys.filter(key => !this.isInBlackList(key));
    }

    /**
     * getOwnPropertyDescriptor 陷阱
     */
    getOwnPropertyDescriptor(target: any, prop: PropertyKey): PropertyDescriptor | undefined {
        if (prop in target) {
            return Object.getOwnPropertyDescriptor(target, prop);
        }

        if (prop in window) {
            const descriptor = Object.getOwnPropertyDescriptor(window, prop);
            // 确保属性可配置
            if (descriptor && !this.nativeGlobalProperties.has(prop as string)) {
                descriptor.configurable = true;
            }
            return descriptor;
        }

        return undefined;
    }

    /**
     * defineProperty 陷阱
     */
    defineProperty(target: any, prop: PropertyKey, descriptor: PropertyDescriptor): boolean {
        // 检查是否为不可配置属性
        if (this.nativeGlobalProperties.has(prop as string)) {
            return false;
        }

        return Object.defineProperty(target, prop, descriptor);
    }

    /**
     * 检查是否在黑名单中
     */
    private isInBlackList(prop: PropertyKey): boolean {
        const propStr = String(prop);
        return this.options.blackList?.includes(propStr) || false;
    }

    /**
     * 检查是否在白名单中
     */
    private isInWhiteList(prop: PropertyKey): boolean {
        const propStr = String(prop);
        return this.options.whiteList?.includes(propStr) || false;
    }

    /**
     * 包装 document 对象
     */
    private wrapDocument(originalDocument: Document): Document {
        if (!this.options.enableStyleIsolation) {
            return originalDocument;
        }

        // 创建 document 代理，拦截样式相关操作
        return new Proxy(originalDocument, {
            get: (target, prop) => {
                const value = (target as any)[prop];

                // 拦截 createElement
                if (prop === 'createElement') {
                    return (tagName: string, options?: ElementCreationOptions) => {
                        const element = target.createElement(tagName, options);

                        // 为 style 和 link 元素添加作用域
                        if (tagName.toLowerCase() === 'style') {
                            this.addStyleScope(element as HTMLStyleElement);
                        } else if (tagName.toLowerCase() === 'link') {
                            this.addLinkScope(element as HTMLLinkElement);
                        }

                        return element;
                    };
                }

                return typeof value === 'function' ? value.bind(target) : value;
            }
        });
    }

    /**
     * 包装 location 对象
     */
    private wrapLocation(originalLocation: Location): Location {
        // 简单返回原始 location，可以根据需要进行扩展
        return originalLocation;
    }

    /**
     * 为 style 元素添加作用域
     */
    private addStyleScope(styleElement: HTMLStyleElement): void {
        const originalTextContent = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent')!;

        Object.defineProperty(styleElement, 'textContent', {
            get: originalTextContent.get,
            set: (content: string) => {
                const scopedContent = this.addCSSScope(content);
                originalTextContent.set!.call(styleElement, scopedContent);
            },
            configurable: true
        });
    }

    /**
     * 为 link 元素添加作用域
     */
    private addLinkScope(linkElement: HTMLLinkElement): void {
        // 可以在这里添加 CSS 作用域逻辑
        // 例如拦截 href 设置，动态加载并处理 CSS
    }

    /**
     * 为 CSS 添加作用域
     */
    private addCSSScope(css: string): string {
        if (!css || !this.options.enableStyleIsolation) {
            return css;
        }

        const scopeId = `micro-app-${this.appName}`;

        // 简单的 CSS 作用域处理
        // 实际项目中可能需要更复杂的 CSS 解析和处理
        return css.replace(/([^{}]+){/g, (match, selector) => {
            // 跳过 @规则
            if (selector.trim().startsWith('@')) {
                return match;
            }

            // 为选择器添加作用域
            const scopedSelector = selector
                .split(',')
                .map((s: string) => `[${scopeId}] ${s.trim()}`)
                .join(', ');

            return `${scopedSelector} {`;
        });
    }
}