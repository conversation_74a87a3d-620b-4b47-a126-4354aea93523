/**
 * Proxy 沙箱插件
 * 提供基于 Proxy 的 JavaScript 沙箱隔离功能
 */

import type { MicroCoreKernel, Plugin } from '@micro-core/core';

/**
 * Proxy 沙箱配置选项
 */
export interface ProxySandboxOptions {
    /** 是否启用严格模式 */
    strict?: boolean;
    /** 白名单属性，允许直接访问 */
    whitelist?: string[];
    /** 黑名单属性，禁止访问 */
    blacklist?: string[];
    /** 是否记录访问日志 */
    enableLogging?: boolean;
}

/**
 * Proxy 沙箱实例
 */
export class ProxySandbox {
    private sandbox: Record<string, any> = {};
    private originalWindow: Window;
    private proxyWindow: WindowProxy;
    private options: ProxySandboxOptions;
    private isActive = false;

    constructor(appName: string, options: ProxySandboxOptions = {}) {
        this.originalWindow = window;
        this.options = {
            strict: true,
            whitelist: ['console', 'location', 'history', 'document'],
            blacklist: [],
            enableLogging: false,
            ...options
        };

        this.proxyWindow = this.createProxyWindow(appName);
    }

    /**
     * 创建代理 window 对象
     */
    private createProxyWindow(appName: string): WindowProxy {
        const { whitelist = [], blacklist = [], enableLogging = false } = this.options;

        return new Proxy(this.originalWindow, {
            get: (target: Window, key: string | symbol) => {
                if (typeof key === 'symbol') {
                    return target[key];
                }

                // 检查黑名单
                if (blacklist.includes(key)) {
                    if (enableLogging) {
                        console.warn(`[ProxySandbox:${appName}] 访问被禁止的属性: ${key}`);
                    }
                    return undefined;
                }

                // 优先从沙箱中获取
                if (this.sandbox.hasOwnProperty(key)) {
                    return this.sandbox[key];
                }

                // 白名单属性直接从原始 window 获取
                if (whitelist.includes(key)) {
                    return target[key];
                }

                // 其他属性从沙箱获取
                return this.sandbox[key] || target[key];
            },

            set: (target: Window, key: string | symbol, value: any) => {
                if (typeof key === 'symbol') {
                    target[key] = value;
                    return true;
                }

                // 检查黑名单
                if (blacklist.includes(key)) {
                    if (enableLogging) {
                        console.warn(`[ProxySandbox:${appName}] 设置被禁止的属性: ${key}`);
                    }
                    return false;
                }

                // 设置到沙箱中
                this.sandbox[key] = value;

                if (enableLogging) {
                    console.log(`[ProxySandbox:${appName}] 设置属性: ${key}`, value);
                }

                return true;
            },

            has: (target: Window, key: string | symbol) => {
                if (typeof key === 'symbol') {
                    return key in target;
                }

                return key in this.sandbox || key in target;
            },

            deleteProperty: (target, key: string | symbol) => {
                if (typeof key === 'symbol') {
                    return delete target[key];
                }

                if (this.sandbox.hasOwnProperty(key)) {
                    delete this.sandbox[key];
                    return true;
                }

                return true;
            }
        });
    }

    /**
     * 激活沙箱
     */
    activate(): void {
        if (this.isActive) {
            return;
        }

        this.isActive = true;

        // 保存当前全局状态快照
        this.takeSnapshot();
    }

    /**
     * 停用沙箱
     */
    deactivate(): void {
        if (!this.isActive) {
            return;
        }

        this.isActive = false;

        // 清理沙箱状态
        this.cleanup();
    }

    /**
     * 获取代理 window
     */
    getProxyWindow(): WindowProxy {
        return this.proxyWindow;
    }

    /**
     * 执行代码
     */
    execScript(code: string): any {
        if (!this.isActive) {
            throw new Error('沙箱未激活，无法执行代码');
        }

        try {
            // 在沙箱环境中执行代码
            const func = new Function('window', code);
            return func.call(this.proxyWindow, this.proxyWindow);
        } catch (error) {
            console.error('[ProxySandbox] 代码执行错误:', error);
            throw error;
        }
    }

    /**
     * 拍摄快照
     */
    private takeSnapshot(): void {
        // 记录当前全局变量状态
        // 这里可以根据需要实现更复杂的快照逻辑
    }

    /**
     * 清理沙箱
     */
    private cleanup(): void {
        // 清空沙箱变量
        this.sandbox = {};

        // 恢复全局状态
        // 这里可以根据需要实现状态恢复逻辑
    }

    /**
     * 销毁沙箱
     */
    destroy(): void {
        this.deactivate();
        this.sandbox = {};
    }
}

/**
 * Proxy 沙箱插件
 */
export class ProxySandboxPlugin implements Plugin {
    name = 'sandbox-proxy';
    version = '1.0.0';

    private sandboxes = new Map<string, ProxySandbox>();
    private options: ProxySandboxOptions;

    constructor(options: ProxySandboxOptions = {}) {
        this.options = options;
    }

    /**
     * 安装插件
     */
    install(kernel: MicroCoreKernel): void {
        // 注册沙箱创建器
        kernel.registerSandboxCreator?.('proxy', (appName: string, config: any) => {
            return this.createSandbox(appName, config);
        });

        console.log('[ProxySandboxPlugin] Proxy 沙箱插件已安装');
    }

    /**
     * 卸载插件
     */
    uninstall(kernel: MicroCoreKernel): void {
        // 销毁所有沙箱
        for (const [appName, sandbox] of this.sandboxes) {
            sandbox.destroy();
        }
        this.sandboxes.clear();

        // 注销沙箱创建器
        kernel.unregisterSandboxCreator?.('proxy');

        console.log('[ProxySandboxPlugin] Proxy 沙箱插件已卸载');
    }

    /**
     * 创建沙箱
     */
    createSandbox(appName: string, config: any): ProxySandbox {
        if (this.sandboxes.has(appName)) {
            return this.sandboxes.get(appName)!;
        }

        const sandbox = new ProxySandbox(appName, {
            ...this.options,
            ...config.sandboxOptions
        });

        this.sandboxes.set(appName, sandbox);
        return sandbox;
    }

    /**
     * 获取沙箱
     */
    getSandbox(appName: string): ProxySandbox | undefined {
        return this.sandboxes.get(appName);
    }

    /**
     * 销毁沙箱
     */
    destroySandbox(appName: string): void {
        const sandbox = this.sandboxes.get(appName);
        if (sandbox) {
            sandbox.destroy();
            this.sandboxes.delete(appName);
        }
    }
}

// 导出工厂函数
export function createProxySandboxPlugin(options?: ProxySandboxOptions): ProxySandboxPlugin {
    return new ProxySandboxPlugin(options);
}

// 默认导出
export default ProxySandboxPlugin;

// 导出类型
export type { ProxySandboxOptions };
