/**
 * Enhanced ProxySandbox - Advanced Proxy-based sandbox implementation
 * Migrated from @micro-core/core for enhanced functionality
 * @packageDocumentation
 */

import { SandboxOptions } from '@micro-core/core';

/**
 * Enhanced Proxy sandbox options
 */
export interface EnhancedProxySandboxOptions extends SandboxOptions {
    /**
     * Whether to use strict isolation
     */
    strictIsolation?: boolean;

    /**
     * List of global variables to allow
     */
    allowList?: string[];

    /**
     * List of global variables to deny
     */
    denyList?: string[];

    /**
     * Whether to enable snapshot mode
     */
    enableSnapshot?: boolean;

    /**
     * Whether to enable property tracking
     */
    enablePropertyTracking?: boolean;
}

/**
 * Property descriptor with additional metadata
 */
interface TrackedPropertyDescriptor extends PropertyDescriptor {
    originalDescriptor?: PropertyDescriptor;
    isModified?: boolean;
    modifiedAt?: Date;
}

/**
 * Enhanced ProxySandbox - Advanced Proxy-based sandbox implementation
 */
export class EnhancedProxySandbox {
    /**
     * Sandbox name
     */
    name: string;

    /**
     * Sandbox type
     */
    type: string = 'proxy';

    /**
     * Sandbox options
     */
    private options: EnhancedProxySandboxOptions;

    /**
     * Whether the sandbox is active
     */
    private isActivated: boolean = false;

    /**
     * Proxy window object
     */
    private proxyWindow: Window | null = null;

    /**
     * Original window snapshot
     */
    private windowSnapshot: Record<string, any> = {};

    /**
     * Modified properties
     */
    private modifiedProps: Set<string> = new Set();

    /**
     * Added properties
     */
    private addedProps: Set<string> = new Set();

    /**
     * Property descriptors
     */
    private propertyDescriptors: Map<string, TrackedPropertyDescriptor> = new Map();

    /**
     * Allow list set
     */
    private allowListSet: Set<string>;

    /**
     * Deny list set
     */
    private denyListSet: Set<string>;

    /**
     * Creates a new EnhancedProxySandbox
     * @param name - Sandbox name
     * @param options - Sandbox options
     */
    constructor(name: string, options: EnhancedProxySandboxOptions = {}) {
        this.name = name;
        this.options = {
            strictIsolation: true,
            enableSnapshot: true,
            enablePropertyTracking: true,
            allowList: [],
            denyList: [],
            ...options
        };

        // Convert arrays to sets for faster lookup
        this.allowListSet = new Set(this.options.allowList || []);
        this.denyListSet = new Set(this.options.denyList || []);

        // Add default allowed properties
        this.addDefaultAllowedProperties();

        // Create proxy window
        this.createProxyWindow();
    }

    /**
     * Add default allowed properties
     */
    private addDefaultAllowedProperties(): void {
        const defaultAllowed = [
            'console',
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'requestAnimationFrame',
            'cancelAnimationFrame',
            'fetch',
            'XMLHttpRequest',
            'Promise',
            'Array',
            'Object',
            'String',
            'Number',
            'Boolean',
            'Date',
            'RegExp',
            'JSON',
            'Math',
            'parseInt',
            'parseFloat',
            'isNaN',
            'isFinite',
            'encodeURIComponent',
            'decodeURIComponent',
            'encodeURI',
            'decodeURI'
        ];

        defaultAllowed.forEach(prop => this.allowListSet.add(prop));
    }

    /**
     * Create proxy window
     */
    private createProxyWindow(): void {
        const rawWindow = window;
        const fakeWindow = Object.create(null);

        // Create proxy handler
        const proxyHandler: ProxyHandler<Window> = {
            get: (target, prop, receiver) => {
                return this.getProperty(target, prop as string, receiver);
            },

            set: (target, prop, value, receiver) => {
                return this.setProperty(target, prop as string, value, receiver);
            },

            has: (target, prop) => {
                return this.hasProperty(target, prop as string);
            },

            deleteProperty: (target, prop) => {
                return this.deleteProperty(target, prop as string);
            },

            defineProperty: (target, prop, descriptor) => {
                return this.defineProperty(target, prop as string, descriptor);
            },

            getOwnPropertyDescriptor: (target, prop) => {
                return this.getOwnPropertyDescriptor(target, prop as string);
            },

            ownKeys: (target) => {
                return this.ownKeys(target);
            }
        };

        this.proxyWindow = new Proxy(fakeWindow, proxyHandler) as Window;
    }

    /**
     * Get property from sandbox
     */
    private getProperty(target: Window, prop: string, receiver: any): any {
        // Check deny list first
        if (this.denyListSet.has(prop)) {
            if (this.options.strictIsolation) {
                throw new Error(`Access to property '${prop}' is denied`);
            }
            return undefined;
        }

        // Check if property exists in fake window
        if (prop in target) {
            return Reflect.get(target, prop, receiver);
        }

        // Check allow list for global properties
        if (this.allowListSet.has(prop)) {
            return Reflect.get(window, prop);
        }

        // For strict isolation, return undefined for unknown properties
        if (this.options.strictIsolation) {
            return undefined;
        }

        // Otherwise, return from global window
        return Reflect.get(window, prop);
    }

    /**
     * Set property in sandbox
     */
    private setProperty(target: Window, prop: string, value: any, receiver: any): boolean {
        // Check deny list
        if (this.denyListSet.has(prop)) {
            if (this.options.strictIsolation) {
                throw new Error(`Setting property '${prop}' is denied`);
            }
            return false;
        }

        // Track property changes
        if (this.options.enablePropertyTracking) {
            this.trackPropertyChange(prop, value);
        }

        // Set property in fake window
        return Reflect.set(target, prop, value, receiver);
    }

    /**
     * Check if property exists
     */
    private hasProperty(target: Window, prop: string): boolean {
        if (this.denyListSet.has(prop)) {
            return false;
        }

        return Reflect.has(target, prop) ||
            (this.allowListSet.has(prop) && Reflect.has(window, prop));
    }

    /**
     * Delete property from sandbox
     */
    private deleteProperty(target: Window, prop: string): boolean {
        if (this.denyListSet.has(prop)) {
            return false;
        }

        if (this.options.enablePropertyTracking) {
            this.trackPropertyDeletion(prop);
        }

        return Reflect.deleteProperty(target, prop);
    }

    /**
     * Define property in sandbox
     */
    private defineProperty(target: Window, prop: string, descriptor: PropertyDescriptor): boolean {
        if (this.denyListSet.has(prop)) {
            return false;
        }

        if (this.options.enablePropertyTracking) {
            this.trackPropertyDefinition(prop, descriptor);
        }

        return Reflect.defineProperty(target, prop, descriptor);
    }

    /**
     * Get own property descriptor
     */
    private getOwnPropertyDescriptor(target: Window, prop: string): PropertyDescriptor | undefined {
        if (this.denyListSet.has(prop)) {
            return undefined;
        }

        return Reflect.getOwnPropertyDescriptor(target, prop);
    }

    /**
     * Get own keys
     */
    private ownKeys(target: Window): ArrayLike<string | symbol> {
        const keys = Reflect.ownKeys(target);
        return keys.filter(key => !this.denyListSet.has(key as string));
    }

    /**
     * Track property change
     */
    private trackPropertyChange(prop: string, value: any): void {
        if (!this.propertyDescriptors.has(prop)) {
            const originalDescriptor = Object.getOwnPropertyDescriptor(window, prop);
            this.propertyDescriptors.set(prop, {
                originalDescriptor,
                isModified: false,
                modifiedAt: new Date()
            });
        }

        const descriptor = this.propertyDescriptors.get(prop)!;
        descriptor.isModified = true;
        descriptor.modifiedAt = new Date();

        this.modifiedProps.add(prop);
    }

    /**
     * Track property deletion
     */
    private trackPropertyDeletion(prop: string): void {
        this.modifiedProps.add(prop);
        this.addedProps.delete(prop);
    }

    /**
     * Track property definition
     */
    private trackPropertyDefinition(prop: string, descriptor: PropertyDescriptor): void {
        this.addedProps.add(prop);

        if (this.options.enablePropertyTracking) {
            this.propertyDescriptors.set(prop, {
                originalDescriptor: descriptor,
                isModified: false,
                modifiedAt: new Date()
            });
        }
    }

    /**
     * Activate the sandbox
     */
    activate(): void {
        if (this.isActivated) {
            return;
        }

        this.isActivated = true;

        // Take snapshot if enabled
        if (this.options.enableSnapshot) {
            this.takeSnapshot();
        }

        console.log(`[EnhancedProxySandbox] ${this.name} sandbox activated`);
    }

    /**
     * Deactivate the sandbox
     */
    deactivate(): void {
        if (!this.isActivated) {
            return;
        }

        this.isActivated = false;

        // Restore snapshot if enabled
        if (this.options.enableSnapshot) {
            this.restoreSnapshot();
        }

        console.log(`[EnhancedProxySandbox] ${this.name} sandbox deactivated`);
    }

    /**
     * Destroy the sandbox
     */
    destroy(): void {
        this.deactivate();

        // Clear all tracking data
        this.modifiedProps.clear();
        this.addedProps.clear();
        this.propertyDescriptors.clear();
        this.windowSnapshot = {};

        this.proxyWindow = null;

        console.log(`[EnhancedProxySandbox] ${this.name} sandbox destroyed`);
    }

    /**
     * Get the proxy window
     */
    getProxyWindow(): Window | null {
        return this.proxyWindow;
    }

    /**
     * Check if sandbox is active
     */
    isActive(): boolean {
        return this.isActivated;
    }

    /**
     * Execute script in sandbox
     */
    execScript(script: string): any {
        if (!this.isActivated) {
            throw new Error(`Sandbox ${this.name} is not activated`);
        }

        if (!this.proxyWindow) {
            throw new Error(`Sandbox ${this.name} proxy window is not available`);
        }

        try {
            // Execute script in sandbox context
            const func = new Function('window', 'self', 'globalThis', script);
            return func.call(this.proxyWindow, this.proxyWindow, this.proxyWindow, this.proxyWindow);
        } catch (error) {
            console.error(`[EnhancedProxySandbox] ${this.name} script execution failed:`, error);
            throw error;
        }
    }

    /**
     * Take snapshot of current window state
     */
    private takeSnapshot(): void {
        if (!this.options.enableSnapshot) {
            return;
        }

        this.windowSnapshot = {};

        // Snapshot modified properties
        this.modifiedProps.forEach(prop => {
            try {
                this.windowSnapshot[prop] = (window as any)[prop];
            } catch (error) {
                // Ignore properties that cannot be accessed
            }
        });
    }

    /**
     * Restore snapshot
     */
    private restoreSnapshot(): void {
        if (!this.options.enableSnapshot) {
            return;
        }

        // Restore modified properties
        Object.keys(this.windowSnapshot).forEach(prop => {
            try {
                (window as any)[prop] = this.windowSnapshot[prop];
            } catch (error) {
                // Ignore properties that cannot be set
            }
        });

        // Remove added properties
        this.addedProps.forEach(prop => {
            try {
                delete (window as any)[prop];
            } catch (error) {
                // Ignore properties that cannot be deleted
            }
        });
    }

    /**
     * Get sandbox statistics
     */
    getStats(): {
        name: string;
        type: string;
        active: boolean;
        modifiedProps: number;
        addedProps: number;
        trackedDescriptors: number;
    } {
        return {
            name: this.name,
            type: this.type,
            active: this.isActivated,
            modifiedProps: this.modifiedProps.size,
            addedProps: this.addedProps.size,
            trackedDescriptors: this.propertyDescriptors.size
        };
    }

    /**
     * Get detailed sandbox information
     */
    getInfo(): {
        name: string;
        type: string;
        active: boolean;
        options: EnhancedProxySandboxOptions;
        modifiedProps: string[];
        addedProps: string[];
        allowList: string[];
        denyList: string[];
    } {
        return {
            name: this.name,
            type: this.type,
            active: this.isActivated,
            options: { ...this.options },
            modifiedProps: Array.from(this.modifiedProps),
            addedProps: Array.from(this.addedProps),
            allowList: Array.from(this.allowListSet),
            denyList: Array.from(this.denyListSet)
        };
    }
}
