/**
 * 插件注册表实现
 * 
 * @description 插件注册和发现机制
 * <AUTHOR> <<EMAIL>>
 */

import { createError, createLogger } from '@micro-core/shared';
import { BasePlugin } from './base-plugin';
import type {
    PluginConfig,
    PluginDependency,
    PluginMetadata,
    PluginRegistryEntry
} from './types';

/**
 * 插件注册表
 * 负责插件的注册、发现和元数据管理
 */
export class PluginRegistry {
    private readonly logger = createLogger('PluginRegistry');
    private readonly entries = new Map<string, PluginRegistryEntry>();
    private readonly metadataCache = new Map<string, PluginMetadata>();
    private readonly dependencyGraph = new Map<string, Set<string>>();

    constructor() {
        this.logger.info('插件注册表初始化完成');
    }

    /**
     * 注册插件
     */
    register(
        pluginId: string,
        pluginClass: typeof BasePlugin,
        config: PluginConfig = {},
        source?: string
    ): void {
        try {
            this.validatePluginId(pluginId);
            this.validatePluginClass(pluginClass);

            if (this.entries.has(pluginId)) {
                throw createError('PLUGIN_ALREADY_REGISTERED', `插件已注册: ${pluginId}`);
            }

            // 创建注册表项
            const entry: PluginRegistryEntry = {
                id: pluginId,
                pluginClass,
                config,
                registeredAt: Date.now(),
                source
            };

            // 获取插件元数据
            const tempInstance = new pluginClass(config);
            const metadata = tempInstance.getMetadata();

            // 验证元数据
            this.validateMetadata(metadata);

            // 注册插件
            this.entries.set(pluginId, entry);
            this.metadataCache.set(pluginId, metadata);

            // 更新依赖图
            if (metadata.dependencies) {
                this.updateDependencyGraph(pluginId, metadata.dependencies);
            }

            this.logger.debug('插件注册成功', {
                pluginId,
                name: metadata.name,
                version: metadata.version,
                source
            });
        } catch (error: any) {
            this.logger.error('插件注册失败', error, { pluginId });
            throw createError('PLUGIN_REGISTRATION_FAILED', `插件注册失败: ${error.message}`, error);
        }
    }

    /**
     * 注销插件
     */
    unregister(pluginId: string): boolean {
        try {
            if (!this.entries.has(pluginId)) {
                return false;
            }

            // 检查是否有其他插件依赖此插件
            const dependents = this.getDependents(pluginId);
            if (dependents.length > 0) {
                throw createError('PLUGIN_HAS_DEPENDENTS',
                    `插件有依赖者，无法注销: ${dependents.join(', ')}`);
            }

            // 移除注册表项
            this.entries.delete(pluginId);
            this.metadataCache.delete(pluginId);

            // 从依赖图中移除
            this.dependencyGraph.delete(pluginId);
            for (const deps of this.dependencyGraph.values()) {
                deps.delete(pluginId);
            }

            this.logger.debug('插件注销成功', { pluginId });
            return true;
        } catch (error: any) {
            this.logger.error('插件注销失败', error, { pluginId });
            throw createError('PLUGIN_UNREGISTRATION_FAILED', `插件注销失败: ${error.message}`, error);
        }
    }

    /**
     * 获取插件类
     */
    getPluginClass(pluginId: string): typeof BasePlugin | undefined {
        const entry = this.entries.get(pluginId);
        return entry?.pluginClass;
    }

    /**
     * 获取插件配置
     */
    getPluginConfig(pluginId: string): PluginConfig | undefined {
        const entry = this.entries.get(pluginId);
        return entry?.config;
    }

    /**
     * 获取插件元数据
     */
    getPluginMetadata(pluginId: string): PluginMetadata | undefined {
        return this.metadataCache.get(pluginId);
    }

    /**
     * 获取注册表项
     */
    getEntry(pluginId: string): PluginRegistryEntry | undefined {
        return this.entries.get(pluginId);
    }

    /**
     * 检查插件是否已注册
     */
    isRegistered(pluginId: string): boolean {
        return this.entries.has(pluginId);
    }

    /**
     * 获取所有已注册的插件ID
     */
    getRegisteredPluginIds(): string[] {
        return Array.from(this.entries.keys());
    }

    /**
     * 获取所有注册表项
     */
    getAllEntries(): PluginRegistryEntry[] {
        return Array.from(this.entries.values());
    }

    /**
     * 按名称搜索插件
     */
    searchByName(name: string): PluginRegistryEntry[] {
        const results: PluginRegistryEntry[] = [];
        const searchTerm = name.toLowerCase();

        for (const entry of this.entries.values()) {
            const metadata = this.metadataCache.get(entry.id);
            if (metadata && metadata.name.toLowerCase().includes(searchTerm)) {
                results.push(entry);
            }
        }

        return results;
    }

    /**
     * 按作者搜索插件
     */
    searchByAuthor(author: string): PluginRegistryEntry[] {
        const results: PluginRegistryEntry[] = [];
        const searchTerm = author.toLowerCase();

        for (const entry of this.entries.values()) {
            const metadata = this.metadataCache.get(entry.id);
            if (metadata && metadata.author?.toLowerCase().includes(searchTerm)) {
                results.push(entry);
            }
        }

        return results;
    }

    /**
     * 按关键词搜索插件
     */
    searchByKeywords(keywords: string[]): PluginRegistryEntry[] {
        const results: PluginRegistryEntry[] = [];
        const searchTerms = keywords.map(k => k.toLowerCase());

        for (const entry of this.entries.values()) {
            const metadata = this.metadataCache.get(entry.id);
            if (metadata && metadata.keywords) {
                const pluginKeywords = metadata.keywords.map(k => k.toLowerCase());
                const hasMatch = searchTerms.some(term =>
                    pluginKeywords.some(keyword => keyword.includes(term))
                );
                if (hasMatch) {
                    results.push(entry);
                }
            }
        }

        return results;
    }

    /**
     * 获取插件依赖
     */
    getDependencies(pluginId: string): string[] {
        const deps = this.dependencyGraph.get(pluginId);
        return deps ? Array.from(deps) : [];
    }

    /**
     * 获取插件依赖者
     */
    getDependents(pluginId: string): string[] {
        const dependents: string[] = [];
        for (const [id, deps] of this.dependencyGraph) {
            if (deps.has(pluginId)) {
                dependents.push(id);
            }
        }
        return dependents;
    }

    /**
     * 检查依赖关系
     */
    checkDependencies(pluginId: string): { missing: string[], circular: string[] } {
        const missing: string[] = [];
        const circular: string[] = [];

        const dependencies = this.getDependencies(pluginId);

        // 检查缺失依赖
        for (const dep of dependencies) {
            if (!this.entries.has(dep)) {
                missing.push(dep);
            }
        }

        // 检查循环依赖
        const visited = new Set<string>();
        const visiting = new Set<string>();

        const checkCircular = (id: string): boolean => {
            if (visiting.has(id)) {
                circular.push(id);
                return true;
            }
            if (visited.has(id)) {
                return false;
            }

            visiting.add(id);
            const deps = this.getDependencies(id);
            for (const dep of deps) {
                if (checkCircular(dep)) {
                    return true;
                }
            }
            visiting.delete(id);
            visited.add(id);
            return false;
        };

        checkCircular(pluginId);

        return { missing, circular };
    }

    /**
     * 拓扑排序
     */
    topologicalSort(pluginIds?: string[]): string[] {
        const ids = pluginIds || this.getRegisteredPluginIds();
        const visited = new Set<string>();
        const visiting = new Set<string>();
        const result: string[] = [];

        const visit = (id: string) => {
            if (visiting.has(id)) {
                throw createError('CIRCULAR_DEPENDENCY', `检测到循环依赖: ${id}`);
            }
            if (visited.has(id)) {
                return;
            }

            visiting.add(id);
            const deps = this.getDependencies(id);
            for (const dep of deps) {
                if (ids.includes(dep)) {
                    visit(dep);
                }
            }
            visiting.delete(id);
            visited.add(id);
            result.push(id);
        };

        for (const id of ids) {
            if (!visited.has(id)) {
                visit(id);
            }
        }

        return result;
    }

    /**
     * 获取统计信息
     */
    getStats(): {
        totalPlugins: number;
        pluginsBySource: Record<string, number>;
        dependencyCount: number;
        averageDependencies: number;
    } {
        const totalPlugins = this.entries.size;
        const pluginsBySource: Record<string, number> = {};
        let dependencyCount = 0;

        for (const entry of this.entries.values()) {
            const source = entry.source || 'unknown';
            pluginsBySource[source] = (pluginsBySource[source] || 0) + 1;

            const deps = this.getDependencies(entry.id);
            dependencyCount += deps.length;
        }

        const averageDependencies = totalPlugins > 0 ? dependencyCount / totalPlugins : 0;

        return {
            totalPlugins,
            pluginsBySource,
            dependencyCount,
            averageDependencies
        };
    }

    /**
     * 清空注册表
     */
    clear(): void {
        this.entries.clear();
        this.metadataCache.clear();
        this.dependencyGraph.clear();
        this.logger.debug('插件注册表已清空');
    }

    /**
     * 导出注册表数据
     */
    export(): {
        entries: PluginRegistryEntry[];
        metadata: Record<string, PluginMetadata>;
        dependencies: Record<string, string[]>;
    } {
        const entries = Array.from(this.entries.values());
        const metadata: Record<string, PluginMetadata> = {};
        const dependencies: Record<string, string[]> = {};

        for (const [id, meta] of this.metadataCache) {
            metadata[id] = meta;
        }

        for (const [id, deps] of this.dependencyGraph) {
            dependencies[id] = Array.from(deps);
        }

        return { entries, metadata, dependencies };
    }

    /**
     * 导入注册表数据
     */
    import(data: {
        entries: PluginRegistryEntry[];
        metadata: Record<string, PluginMetadata>;
        dependencies: Record<string, string[]>;
    }): void {
        try {
            // 清空现有数据
            this.clear();

            // 导入条目
            for (const entry of data.entries) {
                this.entries.set(entry.id, entry);
            }

            // 导入元数据
            for (const [id, metadata] of Object.entries(data.metadata)) {
                this.metadataCache.set(id, metadata);
            }

            // 导入依赖关系
            for (const [id, deps] of Object.entries(data.dependencies)) {
                this.dependencyGraph.set(id, new Set(deps));
            }

            this.logger.debug('插件注册表导入完成', {
                totalEntries: data.entries.length
            });
        } catch (error: any) {
            this.logger.error('插件注册表导入失败', error);
            throw createError('REGISTRY_IMPORT_FAILED', `注册表导入失败: ${error.message}`, error);
        }
    }

    /**
     * 验证插件ID
     */
    private validatePluginId(pluginId: string): void {
        if (!pluginId || typeof pluginId !== 'string') {
            throw createError('INVALID_PLUGIN_ID', '插件ID必须是非空字符串');
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(pluginId)) {
            throw createError('INVALID_PLUGIN_ID', '插件ID只能包含字母、数字、下划线和连字符');
        }
    }

    /**
     * 验证插件类
     */
    private validatePluginClass(pluginClass: typeof BasePlugin): void {
        if (!pluginClass || typeof pluginClass !== 'function') {
            throw createError('INVALID_PLUGIN_CLASS', '插件类必须是构造函数');
        }

        // 检查是否继承自BasePlugin
        if (!(pluginClass.prototype instanceof BasePlugin)) {
            throw createError('INVALID_PLUGIN_CLASS', '插件类必须继承自BasePlugin');
        }
    }

    /**
     * 验证元数据
     */
    private validateMetadata(metadata: PluginMetadata): void {
        if (!metadata.name || !metadata.version) {
            throw createError('INVALID_PLUGIN_METADATA', '插件元数据必须包含名称和版本');
        }

        // 验证版本格式
        if (!/^\d+\.\d+\.\d+/.test(metadata.version)) {
            throw createError('INVALID_PLUGIN_VERSION', '插件版本必须符合语义化版本格式');
        }
    }

    /**
     * 更新依赖图
     */
    private updateDependencyGraph(pluginId: string, dependencies: PluginDependency[]): void {
        const deps = new Set<string>();
        for (const dep of dependencies) {
            if (!dep.optional) {
                deps.add(dep.id);
            }
        }
        this.dependencyGraph.set(pluginId, deps);
    }
}