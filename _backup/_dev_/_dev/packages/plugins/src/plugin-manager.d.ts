/**
 * 插件管理器实现
 *
 * @description 统一的插件注册、加载和生命周期管理
 * <AUTHOR> <<EMAIL>>
 */
import type { PluginConfig, PluginLifecycleHook, PluginManagerOptions, PluginState } from '@micro-core/shared';
import { BasePlugin } from './base-plugin';
/**
 * 插件管理器
 * 负责插件的注册、加载、卸载和生命周期管理
 */
export declare class PluginManager {
    private readonly logger;
    private readonly options;
    private readonly plugins;
    private readonly pluginStates;
    private readonly lifecycleHooks;
    private readonly dependencyGraph;
    private isInitialized;
    constructor(options?: PluginManagerOptions);
    /**
     * 初始化插件管理器
     */
    initialize(): Promise<void>;
    /**
     * 注册插件
     */
    registerPlugin(pluginId: string, pluginClass: typeof BasePlugin, config?: PluginConfig): Promise<void>;
    /**
     * 加载插件
     */
    loadPlugin(pluginId: string): Promise<void>;
    /**
     * 激活插件
     */
    activatePlugin(pluginId: string): Promise<void>;
    /**
     * 停用插件
     */
    deactivatePlugin(pluginId: string): Promise<void>;
    /**
     * 卸载插件
     */
    unloadPlugin(pluginId: string): Promise<void>;
    /**
     * 注销插件
     */
    unregisterPlugin(pluginId: string): Promise<void>;
    /**
     * 获取插件实例
     */
    getPlugin(pluginId: string): BasePlugin | undefined;
    /**
     * 获取插件状态
     */
    getPluginState(pluginId: string): PluginState | undefined;
    /**
     * 获取所有插件ID
     */
    getPluginIds(): string[];
    /**
     * 获取指定状态的插件
     */
    getPluginsByState(state: PluginState): string[];
    /**
     * 批量加载插件
     */
    loadPlugins(pluginIds: string[]): Promise<void>;
    /**
     * 批量激活插件
     */
    activatePlugins(pluginIds: string[]): Promise<void>;
    /**
     * 注册生命周期钩子
     */
    onLifecycleHook(hook: PluginLifecycleHook, callback: Function): void;
    /**
     * 移除生命周期钩子
     */
    offLifecycleHook(hook: PluginLifecycleHook, callback: Function): boolean;
    /**
     * 销毁插件管理器
     */
    destroy(): Promise<void>;
    /**
     * 验证插件ID
     */
    private validatePluginId;
    /**
     * 验证插件元数据
     */
    private validatePluginMetadata;
    /**
     * 获取插件实例
     */
    private getPluginInstance;
    /**
     * 解析依赖关系
     */
    private resolveDependencies;
    /**
     * 加载依赖插件
     */
    private loadDependencies;
    /**
     * 更新依赖图
     */
    private updateDependencyGraph;
    /**
     * 获取依赖者
     */
    private getDependents;
    /**
     * 拓扑排序
     */
    private topologicalSort;
    /**
     * 数组分块
     */
    private chunkArray;
    /**
     * 版本兼容性检查
     */
    private isVersionCompatible;
    /**
     * 设置内部钩子
     */
    private setupInternalHooks;
    /**
     * 执行生命周期钩子
     */
    private executeLifecycleHook;
}
//# sourceMappingURL=plugin-manager.d.ts.map