/**
 * @fileoverview 基础插件类
 * @description 提供插件的基础功能和抽象接口
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createError, createLogger } from '@micro-core/shared';

/**
 * 插件接口
 */
export interface PluginInterface {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件依赖 */
    dependencies?: string[];
    /** 插件安装方法 */
    install(core: any): Promise<void> | void;
    /** 插件初始化方法 */
    initialize?(core: any): Promise<void> | void;
    /** 插件启动方法 */
    start?(core: any): Promise<void> | void;
    /** 插件停止方法 */
    stop?(core: any): Promise<void> | void;
    /** 插件销毁方法 */
    destroy?(core: any): Promise<void> | void;
}

/**
 * 插件状态枚举
 */
export enum PluginStatus {
    CREATED = 'created',
    INSTALLING = 'installing',
    INSTALLED = 'installed',
    ENABLING = 'enabling',
    ENABLED = 'enabled',
    DISABLING = 'disabling',
    UNINSTALLING = 'uninstalling',
    ERROR = 'error'
}

/**
 * 插件配置接口
 */
export interface BasePluginConfig {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件作者 */
    author?: string;
    /** 插件依赖 */
    dependencies?: string[];
    /** 是否默认启用 */
    enabled?: boolean;
    /** 自定义配置 */
    [key: string]: any;
}

/**
 * 基础插件抽象类
 * 定义插件的基本生命周期和通用功能
 */
export abstract class BasePlugin implements PluginInterface {
    /** 插件名称 */
    public readonly name: string;

    /** 插件版本 */
    public readonly version: string;

    /** 插件描述 */
    public readonly description?: string;

    /** 插件依赖 */
    public readonly dependencies?: string[];

    /** 插件配置 */
    protected readonly config: BasePluginConfig;

    /** 插件状态 */
    protected status: PluginStatus = PluginStatus.CREATED;

    /** 微内核实例 */
    protected core: any = null;

    /** 创建时间 */
    protected readonly createdAt: number;

    /** 日志记录器 */
    protected logger: ReturnType<typeof createLogger>;

    /** 性能统计 */
    protected stats = {
        installCount: 0,
        uninstallCount: 0,
        enableCount: 0,
        disableCount: 0,
        lastInstallTime: 0,
        lastUninstallTime: 0
    };

    constructor(config: BasePluginConfig) {
        this.name = config.name;
        this.version = config.version;
        this.description = config.description;
        this.dependencies = config.dependencies;
        this.config = {
            enabled: true,
            ...config
        };
        this.createdAt = Date.now();
        this.logger = createLogger(`Plugin:${this.name}`);

        this.logger.debug(`插件 ${this.name} v${this.version} 已创建`);
    }

    /**
     * 安装插件
     */
    async install(core: any): Promise<void> {
        if (this.status === PluginStatus.INSTALLED || this.status === PluginStatus.ENABLED) {
            this.logger.warn(`插件 ${this.name} 已安装`);
            return;
        }

        try {
            this.status = PluginStatus.INSTALLING;
            this.logger.debug(`正在安装插件 ${this.name}`);

            // 检查依赖
            await this.checkDependencies(core);

            // 保存内核引用
            this.core = core;

            // 执行安装逻辑
            await this.doInstall(core);

            this.status = PluginStatus.INSTALLED;
            this.stats.installCount++;
            this.stats.lastInstallTime = Date.now();

            // 如果配置为启用，则自动启用
            if (this.config.enabled) {
                await this.enable();
            }

            this.logger.info(`插件 ${this.name} 安装成功`);
        } catch (error) {
            this.status = PluginStatus.ERROR;
            const pluginError = createError('PLUGIN_INSTALL_FAILED',
                `插件 ${this.name} 安装失败: ${(error as Error).message}`);
            this.logger.error('插件安装失败:', pluginError);
            throw pluginError;
        }
    }

    /**
     * 初始化插件
     */
    async initialize?(core: any): Promise<void> {
        if (this.status !== PluginStatus.INSTALLED && this.status !== PluginStatus.ENABLED) {
            throw createError('PLUGIN_NOT_INSTALLED', `插件 ${this.name} 未安装，无法初始化`);
        }

        try {
            this.logger.debug(`正在初始化插件 ${this.name}`);

            // 执行初始化逻辑
            await this.doInitialize?.(core);

            this.logger.info(`插件 ${this.name} 初始化成功`);
        } catch (error) {
            this.status = PluginStatus.ERROR;
            const pluginError = createError('PLUGIN_INITIALIZE_FAILED',
                `插件 ${this.name} 初始化失败: ${(error as Error).message}`);
            this.logger.error('插件初始化失败:', pluginError);
            throw pluginError;
        }
    }

    /**
     * 启动插件
     */
    async start?(core: any): Promise<void> {
        if (this.status !== PluginStatus.INSTALLED && this.status !== PluginStatus.ENABLED) {
            throw createError('PLUGIN_NOT_INSTALLED', `插件 ${this.name} 未安装，无法启动`);
        }

        try {
            this.logger.debug(`正在启动插件 ${this.name}`);

            // 执行启动逻辑
            await this.doStart?.(core);

            this.status = PluginStatus.ENABLED;
            this.stats.enableCount++;

            this.logger.info(`插件 ${this.name} 启动成功`);
        } catch (error) {
            this.status = PluginStatus.ERROR;
            const pluginError = createError('PLUGIN_START_FAILED',
                `插件 ${this.name} 启动失败: ${(error as Error).message}`);
            this.logger.error('插件启动失败:', pluginError);
            throw pluginError;
        }
    }

    /**
     * 停止插件
     */
    async stop?(core: any): Promise<void> {
        if (this.status !== PluginStatus.ENABLED) {
            this.logger.warn(`插件 ${this.name} 未启用`);
            return;
        }

        try {
            this.logger.debug(`正在停止插件 ${this.name}`);

            // 执行停止逻辑
            await this.doStop?.(core);

            this.status = PluginStatus.INSTALLED;
            this.stats.disableCount++;

            this.logger.info(`插件 ${this.name} 停止成功`);
        } catch (error) {
            this.status = PluginStatus.ERROR;
            const pluginError = createError('PLUGIN_STOP_FAILED',
                `插件 ${this.name} 停止失败: ${(error as Error).message}`, error);
            this.logger.error('插件停止失败:', pluginError);
            throw pluginError;
        }
    }

    /**
     * 销毁插件
     */
    async destroy?(core: any): Promise<void> {
        try {
            this.logger.debug(`正在销毁插件 ${this.name}`);

            // 如果插件已启用，先停止
            if (this.status === PluginStatus.ENABLED) {
                await this.stop?.(core);
            }

            // 执行销毁逻辑
            await this.doDestroy?.(core);

            this.status = PluginStatus.CREATED;
            this.core = null;
            this.stats.uninstallCount++;
            this.stats.lastUninstallTime = Date.now();

            this.logger.info(`插件 ${this.name} 销毁成功`);
        } catch (error) {
            this.status = PluginStatus.ERROR;
            const pluginError = createError('PLUGIN_DESTROY_FAILED',
                `插件 ${this.name} 销毁失败: ${(error as Error).message}`, error);
            this.logger.error('插件销毁失败:', pluginError);
            throw pluginError;
        }
    }

    /**
     * 启用插件
     */
    async enable(): Promise<void> {
        if (this.status !== PluginStatus.INSTALLED) {
            throw createError('PLUGIN_NOT_INSTALLED', `插件 ${this.name} 未安装，无法启用`);
        }

        if (this.status === PluginStatus.ENABLED) {
            this.logger.warn(`插件 ${this.name} 已启用`);
            return;
        }

        try {
            this.status = PluginStatus.ENABLING;
            this.logger.debug(`正在启用插件 ${this.name}`);

            // 执行启用逻辑
            await this.doEnable?.();

            this.status = PluginStatus.ENABLED;
            this.stats.enableCount++;

            this.logger.info(`插件 ${this.name} 启用成功`);
        } catch (error) {
            this.status = PluginStatus.ERROR;
            const pluginError = createError('PLUGIN_ENABLE_FAILED',
                `插件 ${this.name} 启用失败: ${(error as Error).message}`, error);
            this.logger.error('插件启用失败:', pluginError);
            throw pluginError;
        }
    }

    /**
     * 禁用插件
     */
    async disable(): Promise<void> {
        if (this.status !== PluginStatus.ENABLED) {
            this.logger.warn(`插件 ${this.name} 未启用`);
            return;
        }

        try {
            this.status = PluginStatus.DISABLING;
            this.logger.debug(`正在禁用插件 ${this.name}`);

            // 执行禁用逻辑
            await this.doDisable?.();

            this.status = PluginStatus.INSTALLED;
            this.stats.disableCount++;

            this.logger.info(`插件 ${this.name} 禁用成功`);
        } catch (error) {
            this.status = PluginStatus.ERROR;
            const pluginError = createError('PLUGIN_DISABLE_FAILED',
                `插件 ${this.name} 禁用失败: ${(error as Error).message}`, error);
            this.logger.error('插件禁用失败:', pluginError);
            throw pluginError;
        }
    }

    /**
     * 获取插件状态
     */
    getStatus(): PluginStatus {
        return this.status;
    }

    /**
     * 检查插件是否已安装
     */
    isInstalled(): boolean {
        return this.status === PluginStatus.INSTALLED ||
            this.status === PluginStatus.ENABLED;
    }

    /**
     * 检查插件是否已启用
     */
    isEnabled(): boolean {
        return this.status === PluginStatus.ENABLED;
    }

    /**
     * 获取插件配置
     */
    getConfig(): BasePluginConfig {
        return { ...this.config };
    }

    /**
     * 获取插件统计信息
     */
    getStats(): Record<string, any> {
        return {
            name: this.name,
            version: this.version,
            status: this.status,
            createdAt: this.createdAt,
            dependencies: this.dependencies ? [...this.dependencies] : [],
            ...this.stats
        };
    }

    /**
     * 获取插件描述信息
     */
    getDescription(): string {
        return this.description || `${this.name} 插件`;
    }

    // ============= 抽象方法 - 子类必须实现 =============

    /**
     * 执行安装逻辑（子类实现）
     */
    protected abstract doInstall(core: any): Promise<void>;

    // ============= 可选的钩子方法 - 子类可重写 =============

    /**
     * 执行初始化逻辑（子类可重写）
     */
    protected async doInitialize?(core: any): Promise<void> {
        // 默认实现：什么都不做
    }

    /**
     * 执行启动逻辑（子类可重写）
     */
    protected async doStart?(core: any): Promise<void> {
        // 默认实现：什么都不做
    }

    /**
     * 执行停止逻辑（子类可重写）
     */
    protected async doStop?(core: any): Promise<void> {
        // 默认实现：什么都不做
    }

    /**
     * 执行销毁逻辑（子类可重写）
     */
    protected async doDestroy?(core: any): Promise<void> {
        // 默认实现：清理资源
        await this.cleanup();
    }

    /**
     * 执行启用逻辑（子类可重写）
     */
    protected async doEnable?(): Promise<void> {
        // 默认实现：什么都不做
    }

    /**
     * 执行禁用逻辑（子类可重写）
     */
    protected async doDisable?(): Promise<void> {
        // 默认实现：什么都不做
    }

    /**
     * 检查依赖（子类可重写）
     */
    protected async checkDependencies(core: any): Promise<void> {
        if (!this.dependencies || this.dependencies.length === 0) {
            return;
        }

        // 通过核心系统检查插件依赖
        for (const dep of this.dependencies) {
            // 这里可以通过 core 实例检查依赖插件是否已安装
            // 具体实现取决于核心系统的 API
            this.logger.debug(`检查依赖插件: ${dep}`);
        }

        this.logger.debug(`插件 ${this.name} 依赖检查通过`);
    }

    /**
     * 清理资源（子类可重写）
     */
    protected async cleanup(): Promise<void> {
        // 默认实现：清理统计数据
        this.stats = {
            installCount: 0,
            uninstallCount: 0,
            enableCount: 0,
            disableCount: 0,
            lastInstallTime: 0,
            lastUninstallTime: 0
        };
    }
}