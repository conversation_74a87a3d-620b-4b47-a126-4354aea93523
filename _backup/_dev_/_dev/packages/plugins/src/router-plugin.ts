/**
 * @fileoverview 路由插件
 * @description 提供微前端应用的路由管理功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import {
    generateId,
    type PluginConfig
} from '@micro-core/shared';
import { BasePlugin } from './base-plugin';

// 本地错误创建函数
function createError(code: string, message: string, cause?: Error): Error {
    const error = new Error(message);
    error.name = code;
    if (cause) {
        (error as any).cause = cause;
    }
    return error;
}

// 本地类型定义
interface MicroCoreKernel {
    registerAPI(name: string, api: any): void;
    getEventBus(): any;
}

interface NavigationOptions {
    replace?: boolean;
    params?: Record<string, any>;
    state?: any;
    cache?: boolean;
    fromBrowser?: boolean;
}

interface RouteConfig {
    path: string;
    component?: any;
    meta?: Record<string, any>;
    children?: RouteConfig[];
}

interface RouteGuard {
    name?: string;
    canNavigate(from: string, to: string, options: NavigationOptions): Promise<boolean | { allowed: boolean; reason?: string }> | boolean | { allowed: boolean; reason?: string };
}

/**
 * 路由插件配置接口
 */
interface RouterPluginConfig extends PluginConfig {
    /** 路由模式 */
    mode?: 'hash' | 'history';
    /** 基础路径 */
    base?: string;
    /** 默认路由 */
    defaultRoute?: string;
    /** 是否启用路由守卫 */
    enableGuards?: boolean;
    /** 路由切换动画 */
    transition?: {
        enter?: string;
        leave?: string;
        duration?: number;
    };
    /** 是否启用路由缓存 */
    enableCache?: boolean;
    /** 缓存大小限制 */
    cacheSize?: number;
}

/**
 * 路由记录接口
 */
interface RouteRecord {
    /** 路由ID */
    id: string;
    /** 路由路径 */
    path: string;
    /** 应用名称 */
    appName: string;
    /** 路由配置 */
    config: RouteConfig;
    /** 创建时间 */
    createdAt: number;
    /** 最后访问时间 */
    lastAccessTime?: number;
    /** 访问次数 */
    accessCount: number;
}

/**
 * 导航历史记录
 */
interface NavigationHistory {
    /** 历史记录ID */
    id: string;
    /** 来源路径 */
    from: string;
    /** 目标路径 */
    to: string;
    /** 导航时间 */
    timestamp: number;
    /** 导航参数 */
    params?: Record<string, any>;
    /** 导航状态 */
    state?: any;
}

/**
 * 路由插件类
 * 提供微前端应用的路由管理、导航控制和路由守卫功能
 */
export class RouterPlugin extends BasePlugin {
    /** 内核实例 */
    protected kernel?: MicroCoreKernel;

    /** 路由插件配置 */
    private routerConfig: Required<RouterPluginConfig>;

    /** 路由注册表 */
    private routes = new Map<string, RouteRecord>();

    /** 路径到路由的映射 */
    private pathRouteMap = new Map<string, string>();

    /** 当前激活的路由 */
    private currentRoute: RouteRecord | null = null;

    /** 路由守卫列表 */
    private guards: RouteGuard[] = [];

    /** 导航历史记录 */
    private history: NavigationHistory[] = [];

    /** 路由缓存 */
    private routeCache = new Map<string, any>();

    /** 是否正在导航 */
    private isNavigating = false;

    /** 导航队列 */
    private navigationQueue: Array<() => Promise<void>> = [];

    constructor(config: RouterPluginConfig = {}) {
        super({
            name: 'router',
            version: '0.1.0',
            description: '微前端路由管理插件',
            enabled: true,
            ...config
        });

        this.routerConfig = {
            name: 'router',
            version: '0.1.0',
            description: '微前端路由管理插件',
            enabled: true,
            mode: 'hash',
            base: '/',
            defaultRoute: '/',
            enableGuards: true,
            enableCache: true,
            cacheSize: 50,
            transition: {
                enter: 'fade-in',
                leave: 'fade-out',
                duration: 300
            },
            ...config
        } as Required<RouterPluginConfig>;
    }

    /**
     * 执行安装逻辑
     */
    protected async doInstall(kernel: MicroCoreKernel): Promise<void> {
        this.kernel = kernel;

        // 注册路由API
        kernel.registerAPI('router', {
            // 路由注册
            register: this.registerRoute.bind(this),
            unregister: this.unregisterRoute.bind(this),

            // 路由导航
            navigate: this.navigate.bind(this),
            replace: this.replace.bind(this),
            back: this.back.bind(this),
            forward: this.forward.bind(this),

            // 路由信息
            getCurrentRoute: this.getCurrentRoute.bind(this),
            getRoutes: this.getRoutes.bind(this),
            getHistory: this.getHistory.bind(this),

            // 路由守卫
            addGuard: this.addGuard.bind(this),
            removeGuard: this.removeGuard.bind(this),

            // 路由缓存
            clearCache: this.clearCache.bind(this),
            getCacheSize: this.getCacheSize.bind(this)
        });

        // 设置路由监听
        this.setupRouteListener();

        // 初始化路由
        await this.initializeRouter();

        this.logger.info('路由插件安装完成');
    }

    /**
     * 执行卸载逻辑
     */
    protected async doUninstall(): Promise<void> {
        // 清理路由监听
        this.cleanupRouteListener();

        // 清理数据
        this.routes.clear();
        this.pathRouteMap.clear();
        this.guards = [];
        this.history = [];
        this.routeCache.clear();
        this.currentRoute = null;

        this.logger.info('路由插件卸载完成');
    }

    /**
     * 注册路由
     */
    async registerRoute(appName: string, config: RouteConfig): Promise<string> {
        try {
            const routeId = generateId('route');
            const route: RouteRecord = {
                id: routeId,
                path: config.path,
                appName,
                config,
                createdAt: Date.now(),
                accessCount: 0
            };

            // 检查路径冲突
            if (this.pathRouteMap.has(config.path)) {
                const existingRouteId = this.pathRouteMap.get(config.path)!;
                const existingRoute = this.routes.get(existingRouteId);

                if (existingRoute && existingRoute.appName !== appName) {
                    throw createError(
                        'ROUTE_INVALID_CONFIG',
                        `路径 ${config.path} 已被应用 ${existingRoute.appName} 注册`
                    );
                }
            }

            // 注册路由
            this.routes.set(routeId, route);
            this.pathRouteMap.set(config.path, routeId);

            this.logger.debug(`应用 ${appName} 注册路由: ${config.path}`);

            // 触发路由注册事件
            this.kernel?.getEventBus()?.emit('route:registered', {
                routeId,
                appName,
                path: config.path,
                config
            });

            return routeId;
        } catch (error) {
            this.logger.error(`注册路由失败:`, error);
            throw error;
        }
    }

    /**
     * 取消注册路由
     */
    async unregisterRoute(routeId: string): Promise<void> {
        try {
            const route = this.routes.get(routeId);
            if (!route) {
                this.logger.warn(`路由 ${routeId} 不存在`);
                return;
            }

            // 如果是当前路由，需要导航到默认路由
            if (this.currentRoute?.id === routeId) {
                await this.navigate(this.routerConfig.defaultRoute);
            }

            // 移除路由
            this.routes.delete(routeId);
            this.pathRouteMap.delete(route.path);

            // 清理缓存
            this.routeCache.delete(route.path);

            this.logger.debug(`取消注册路由: ${route.path}`);

            // 触发路由取消注册事件
            this.kernel?.getEventBus()?.emit('route:unregistered', {
                routeId,
                appName: route.appName,
                path: route.path
            });
        } catch (error) {
            this.logger.error(`取消注册路由失败:`, error);
            throw error;
        }
    }

    /**
     * 导航到指定路径
     */
    async navigate(path: string, options: NavigationOptions = {}): Promise<void> {
        // 如果正在导航，加入队列
        if (this.isNavigating) {
            return new Promise((resolve, reject) => {
                this.navigationQueue.push(async () => {
                    try {
                        await this.doNavigate(path, options);
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                });
            });
        }

        return this.doNavigate(path, options);
    }

    /**
     * 执行导航
     */
    private async doNavigate(path: string, options: NavigationOptions = {}): Promise<void> {
        this.isNavigating = true;

        try {
            const fromPath = this.getCurrentPath();

            // 查找目标路由
            const routeId = this.pathRouteMap.get(path);
            if (!routeId) {
                throw createError(
                    'ROUTE_NOT_FOUND',
                    `路由 ${path} 未找到`
                );
            }

            const targetRoute = this.routes.get(routeId);
            if (!targetRoute) {
                throw createError(
                    'ROUTE_NOT_FOUND',
                    `路由记录 ${routeId} 不存在`
                );
            }

            // 执行路由守卫
            if (this.routerConfig.enableGuards) {
                const guardResult = await this.executeGuards(fromPath, path, options);
                if (!guardResult.allowed) {
                    throw createError(
                        'ROUTE_GUARD_REJECTED',
                        guardResult.reason || '路由守卫拒绝导航'
                    );
                }
            }

            // 更新浏览器历史
            this.updateBrowserHistory(path, options);

            // 更新当前路由
            const previousRoute = this.currentRoute;
            this.currentRoute = targetRoute;
            this.currentRoute.lastAccessTime = Date.now();
            this.currentRoute.accessCount++;

            // 添加导航历史
            this.addNavigationHistory(fromPath, path, options);

            // 缓存路由数据
            if (this.routerConfig.enableCache && options.cache !== false) {
                this.cacheRoute(path, options.state);
            }

            // 触发路由变化事件
            this.kernel?.getEventBus()?.emit('route:changed', {
                from: fromPath,
                to: path,
                route: targetRoute,
                previousRoute,
                options
            });

            this.logger.debug(`导航成功: ${fromPath} -> ${path}`);
        } catch (error) {
            this.logger.error(`导航失败: ${path}`, error);
            throw error;
        } finally {
            this.isNavigating = false;

            // 处理导航队列
            if (this.navigationQueue.length > 0) {
                const nextNavigation = this.navigationQueue.shift();
                if (nextNavigation) {
                    setTimeout(() => nextNavigation(), 0);
                }
            }
        }
    }

    /**
     * 替换当前路由
     */
    async replace(path: string, options: NavigationOptions = {}): Promise<void> {
        return this.navigate(path, { ...options, replace: true });
    }

    /**
     * 后退
     */
    async back(): Promise<void> {
        if (this.history.length < 2) {
            this.logger.warn('没有可后退的历史记录');
            return;
        }

        const current = this.history[this.history.length - 1];
        const previous = this.history[this.history.length - 2];

        await this.navigate(previous.from);
    }

    /**
     * 前进
     */
    async forward(): Promise<void> {
        // 这里需要实现前进逻辑，通常需要维护一个更复杂的历史栈
        this.logger.warn('前进功能暂未实现');
    }

    /**
     * 获取当前路由
     */
    getCurrentRoute(): RouteRecord | null {
        return this.currentRoute ? { ...this.currentRoute } : null;
    }

    /**
     * 获取当前路径
     */
    getCurrentPath(): string {
        if (this.routerConfig.mode === 'hash') {
            return window.location.hash.slice(1) || this.routerConfig.defaultRoute;
        } else {
            return window.location.pathname || this.routerConfig.defaultRoute;
        }
    }

    /**
     * 获取所有路由
     */
    getRoutes(): RouteRecord[] {
        return Array.from(this.routes.values()).map(route => ({ ...route }));
    }

    /**
     * 获取导航历史
     */
    getHistory(): NavigationHistory[] {
        return [...this.history];
    }

    /**
     * 添加路由守卫
     */
    addGuard(guard: RouteGuard): void {
        this.guards.push(guard);
        this.logger.debug(`添加路由守卫: ${guard.name || 'anonymous'}`);
    }

    /**
     * 移除路由守卫
     */
    removeGuard(guard: RouteGuard | string): void {
        const guardName = typeof guard === 'string' ? guard : guard.name;
        const index = this.guards.findIndex(g => g.name === guardName);

        if (index > -1) {
            this.guards.splice(index, 1);
            this.logger.debug(`移除路由守卫: ${guardName}`);
        }
    }

    /**
     * 清理路由缓存
     */
    clearCache(path?: string): void {
        if (path) {
            this.routeCache.delete(path);
            this.logger.debug(`清理路由缓存: ${path}`);
        } else {
            this.routeCache.clear();
            this.logger.debug('清理所有路由缓存');
        }
    }

    /**
     * 获取缓存大小
     */
    getCacheSize(): number {
        return this.routeCache.size;
    }

    /**
     * 设置路由监听
     */
    private setupRouteListener(): void {
        if (this.routerConfig.mode === 'hash') {
            window.addEventListener('hashchange', this.handleRouteChange.bind(this));
        } else {
            window.addEventListener('popstate', this.handleRouteChange.bind(this));
        }
    }

    /**
     * 清理路由监听
     */
    private cleanupRouteListener(): void {
        if (this.routerConfig.mode === 'hash') {
            window.removeEventListener('hashchange', this.handleRouteChange.bind(this));
        } else {
            window.removeEventListener('popstate', this.handleRouteChange.bind(this));
        }
    }

    /**
     * 处理路由变化
     */
    private async handleRouteChange(): Promise<void> {
        const currentPath = this.getCurrentPath();

        try {
            await this.navigate(currentPath, { fromBrowser: true });
        } catch (error) {
            this.logger.error('处理路由变化失败:', error);

            // 导航到默认路由
            try {
                await this.navigate(this.routerConfig.defaultRoute);
            } catch (fallbackError) {
                this.logger.error('导航到默认路由失败:', fallbackError);
            }
        }
    }

    /**
     * 初始化路由
     */
    private async initializeRouter(): Promise<void> {
        const currentPath = this.getCurrentPath();

        // 如果当前路径不是默认路径且没有对应的路由，导航到默认路由
        if (currentPath !== this.routerConfig.defaultRoute && !this.pathRouteMap.has(currentPath)) {
            await this.navigate(this.routerConfig.defaultRoute);
        } else if (this.pathRouteMap.has(currentPath)) {
            // 如果有对应的路由，激活它
            const routeId = this.pathRouteMap.get(currentPath)!;
            const route = this.routes.get(routeId);
            if (route) {
                this.currentRoute = route;
                this.currentRoute.lastAccessTime = Date.now();
                this.currentRoute.accessCount++;
            }
        }
    }

    /**
     * 执行路由守卫
     */
    private async executeGuards(
        from: string,
        to: string,
        options: NavigationOptions
    ): Promise<{ allowed: boolean; reason?: string }> {
        for (const guard of this.guards) {
            try {
                const result = await guard.canNavigate(from, to, options);

                if (typeof result === 'boolean') {
                    if (!result) {
                        return { allowed: false, reason: `守卫 ${guard.name} 拒绝导航` };
                    }
                } else if (result && !result.allowed) {
                    return result;
                }
            } catch (error) {
                this.logger.error(`路由守卫 ${guard.name} 执行失败:`, error);
                return { allowed: false, reason: `守卫执行失败: ${(error as Error).message}` };
            }
        }

        return { allowed: true };
    }

    /**
     * 更新浏览器历史
     */
    private updateBrowserHistory(path: string, options: NavigationOptions): void {
        const url = this.routerConfig.mode === 'hash' ? `#${path}` : path;

        if (options.replace) {
            window.history.replaceState(options.state || null, '', url);
        } else if (!options.fromBrowser) {
            window.history.pushState(options.state || null, '', url);
        }
    }

    /**
     * 添加导航历史
     */
    private addNavigationHistory(from: string, to: string, options: NavigationOptions): void {
        const historyRecord: NavigationHistory = {
            id: generateId('nav'),
            from,
            to,
            timestamp: Date.now(),
            params: options.params,
            state: options.state
        };

        this.history.push(historyRecord);

        // 限制历史记录数量
        if (this.history.length > 100) {
            this.history.shift();
        }
    }

    /**
     * 缓存路由数据
     */
    private cacheRoute(path: string, data: any): void {
        if (this.routeCache.size >= this.routerConfig.cacheSize) {
            // 删除最旧的缓存
            const firstKey = this.routeCache.keys().next().value;
            if (firstKey) {
                this.routeCache.delete(firstKey);
            }
        }

        this.routeCache.set(path, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 获取插件统计信息
     */
    override getStats(): Record<string, any> {
        const baseStats = super.getStats();

        return {
            ...baseStats,
            routesCount: this.routes.size,
            guardsCount: this.guards.length,
            historyCount: this.history.length,
            cacheSize: this.routeCache.size,
            currentRoute: this.currentRoute?.path,
            mode: this.routerConfig.mode,
            navigationQueueSize: this.navigationQueue.length
        };
    }
}