/**
 * @fileoverview 基础插件类
 * @description 提供插件的基础功能和抽象接口
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import { createLogger } from '@micro-core/shared';
/**
 * 插件接口
 */
export interface PluginInterface {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件依赖 */
    dependencies?: string[];
    /** 插件安装方法 */
    install(core: any): Promise<void> | void;
    /** 插件初始化方法 */
    initialize?(core: any): Promise<void> | void;
    /** 插件启动方法 */
    start?(core: any): Promise<void> | void;
    /** 插件停止方法 */
    stop?(core: any): Promise<void> | void;
    /** 插件销毁方法 */
    destroy?(core: any): Promise<void> | void;
}
/**
 * 插件状态枚举
 */
export declare enum PluginStatus {
    CREATED = "created",
    INSTALLING = "installing",
    INSTALLED = "installed",
    ENABLING = "enabling",
    ENABLED = "enabled",
    DISABLING = "disabling",
    UNINSTALLING = "uninstalling",
    ERROR = "error"
}
/**
 * 插件配置接口
 */
export interface BasePluginConfig {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件作者 */
    author?: string;
    /** 插件依赖 */
    dependencies?: string[];
    /** 是否默认启用 */
    enabled?: boolean;
    /** 自定义配置 */
    [key: string]: any;
}
/**
 * 基础插件抽象类
 * 定义插件的基本生命周期和通用功能
 */
export declare abstract class BasePlugin implements PluginInterface {
    /** 插件名称 */
    readonly name: string;
    /** 插件版本 */
    readonly version: string;
    /** 插件描述 */
    readonly description?: string;
    /** 插件依赖 */
    readonly dependencies?: string[];
    /** 插件配置 */
    protected readonly config: BasePluginConfig;
    /** 插件状态 */
    protected status: PluginStatus;
    /** 微内核实例 */
    protected core: any;
    /** 创建时间 */
    protected readonly createdAt: number;
    /** 日志记录器 */
    protected logger: ReturnType<typeof createLogger>;
    /** 性能统计 */
    protected stats: {
        installCount: number;
        uninstallCount: number;
        enableCount: number;
        disableCount: number;
        lastInstallTime: number;
        lastUninstallTime: number;
    };
    constructor(config: BasePluginConfig);
    /**
     * 安装插件
     */
    install(core: any): Promise<void>;
    /**
     * 初始化插件
     */
    initialize?(core: any): Promise<void>;
    /**
     * 启动插件
     */
    start?(core: any): Promise<void>;
    /**
     * 停止插件
     */
    stop?(core: any): Promise<void>;
    /**
     * 销毁插件
     */
    destroy?(core: any): Promise<void>;
    /**
     * 启用插件
     */
    enable(): Promise<void>;
    /**
     * 禁用插件
     */
    disable(): Promise<void>;
    /**
     * 获取插件状态
     */
    getStatus(): PluginStatus;
    /**
     * 检查插件是否已安装
     */
    isInstalled(): boolean;
    /**
     * 检查插件是否已启用
     */
    isEnabled(): boolean;
    /**
     * 获取插件配置
     */
    getConfig(): BasePluginConfig;
    /**
     * 获取插件统计信息
     */
    getStats(): Record<string, any>;
    /**
     * 获取插件描述信息
     */
    getDescription(): string;
    /**
     * 执行安装逻辑（子类实现）
     */
    protected abstract doInstall(core: any): Promise<void>;
    /**
     * 执行初始化逻辑（子类可重写）
     */
    protected doInitialize?(core: any): Promise<void>;
    /**
     * 执行启动逻辑（子类可重写）
     */
    protected doStart?(core: any): Promise<void>;
    /**
     * 执行停止逻辑（子类可重写）
     */
    protected doStop?(core: any): Promise<void>;
    /**
     * 执行销毁逻辑（子类可重写）
     */
    protected doDestroy?(core: any): Promise<void>;
    /**
     * 执行启用逻辑（子类可重写）
     */
    protected doEnable?(): Promise<void>;
    /**
     * 执行禁用逻辑（子类可重写）
     */
    protected doDisable?(): Promise<void>;
    /**
     * 检查依赖（子类可重写）
     */
    protected checkDependencies(core: any): Promise<void>;
    /**
     * 清理资源（子类可重写）
     */
    protected cleanup(): Promise<void>;
}
//# sourceMappingURL=base-plugin.d.ts.map