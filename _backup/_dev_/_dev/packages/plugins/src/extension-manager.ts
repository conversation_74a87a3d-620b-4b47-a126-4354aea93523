/**
 * 插件扩展点管理器
 * 
 * @description 管理插件的扩展点和扩展实现
 * <AUTHOR> <<EMAIL>>
 */

import { createError, EventEmitter, Logger } from '@micro-core/shared';
import type {
    PluginEvent,
    PluginExtension,
    PluginExtensionPoint
} from './types';

/**
 * 扩展点注册信息
 */
interface ExtensionPointRegistry {
    /** 扩展点定义 */
    definition: PluginExtensionPoint;
    /** 注册时间 */
    registeredAt: number;
    /** 注册者 */
    registeredBy: string;
    /** 扩展实现列表 */
    extensions: Map<string, PluginExtension>;
}

/**
 * 扩展实现注册信息
 */
interface ExtensionRegistry {
    /** 扩展定义 */
    definition: PluginExtension;
    /** 注册时间 */
    registeredAt: number;
    /** 注册者 */
    registeredBy: string;
    /** 是否已激活 */
    active: boolean;
}

/**
 * 插件扩展点管理器
 */
export class ExtensionManager extends EventEmitter {
    private readonly logger: Logger;
    private readonly extensionPoints = new Map<string, ExtensionPointRegistry>();
    private readonly extensions = new Map<string, ExtensionRegistry>();
    private readonly providerCache = new Map<string, any[]>();

    constructor() {
        super();
        this.logger = new Logger('ExtensionManager');
        this.logger.info('扩展点管理器初始化完成');
    }

    /**
     * 注册扩展点
     */
    registerExtensionPoint(
        extensionPoint: PluginExtensionPoint,
        registeredBy: string
    ): void {
        try {
            this.validateExtensionPoint(extensionPoint);

            if (this.extensionPoints.has(extensionPoint.id)) {
                throw createError('EXTENSION_POINT_ALREADY_EXISTS',
                    `扩展点已存在: ${extensionPoint.id}`);
            }

            const registry: ExtensionPointRegistry = {
                definition: extensionPoint,
                registeredAt: Date.now(),
                registeredBy,
                extensions: new Map()
            };

            this.extensionPoints.set(extensionPoint.id, registry);

            this.emit('extensionPointRegistered', {
                type: 'extensionPointRegistered',
                pluginId: registeredBy,
                data: extensionPoint,
                timestamp: Date.now()
            } as PluginEvent);

            this.logger.debug('扩展点注册成功', {
                extensionPointId: extensionPoint.id,
                registeredBy
            });
        } catch (error) {
            this.logger.error('扩展点注册失败', error, {
                extensionPointId: extensionPoint.id,
                registeredBy
            });
            throw createError('EXTENSION_POINT_REGISTRATION_FAILED',
                `扩展点注册失败: ${error.message}`, error);
        }
    }

    /**
     * 注销扩展点
     */
    unregisterExtensionPoint(extensionPointId: string): boolean {
        try {
            const registry = this.extensionPoints.get(extensionPointId);
            if (!registry) {
                return false;
            }

            // 先注销所有相关扩展
            const extensionIds = Array.from(registry.extensions.keys());
            for (const extensionId of extensionIds) {
                this.unregisterExtension(extensionId);
            }

            // 移除扩展点
            this.extensionPoints.delete(extensionPointId);
            this.providerCache.delete(extensionPointId);

            this.emit('extensionPointUnregistered', {
                type: 'extensionPointUnregistered',
                pluginId: registry.registeredBy,
                data: { extensionPointId },
                timestamp: Date.now()
            } as PluginEvent);

            this.logger.debug('扩展点注销成功', { extensionPointId });
            return true;
        } catch (error) {
            this.logger.error('扩展点注销失败', error, { extensionPointId });
            throw createError('EXTENSION_POINT_UNREGISTRATION_FAILED',
                `扩展点注销失败: ${error.message}`, error);
        }
    }

    /**
     * 注册扩展实现
     */
    registerExtension(
        extension: PluginExtension,
        registeredBy: string
    ): void {
        try {
            this.validateExtension(extension);

            // 检查扩展点是否存在
            const extensionPointRegistry = this.extensionPoints.get(extension.extensionPointId);
            if (!extensionPointRegistry) {
                throw createError('EXTENSION_POINT_NOT_FOUND',
                    `扩展点不存在: ${extension.extensionPointId}`);
            }

            if (this.extensions.has(extension.id)) {
                throw createError('EXTENSION_ALREADY_EXISTS',
                    `扩展已存在: ${extension.id}`);
            }

            // 验证扩展实现
            this.validateExtensionImplementation(extension, extensionPointRegistry.definition);

            const registry: ExtensionRegistry = {
                definition: extension,
                registeredAt: Date.now(),
                registeredBy,
                active: true
            };

            this.extensions.set(extension.id, registry);
            extensionPointRegistry.extensions.set(extension.id, extension);

            // 清除缓存
            this.providerCache.delete(extension.extensionPointId);

            this.emit('extensionRegistered', {
                type: 'extensionRegistered',
                pluginId: registeredBy,
                data: extension,
                timestamp: Date.now()
            } as PluginEvent);

            this.logger.debug('扩展注册成功', {
                extensionId: extension.id,
                extensionPointId: extension.extensionPointId,
                registeredBy
            });
        } catch (error) {
            this.logger.error('扩展注册失败', error, {
                extensionId: extension.id,
                registeredBy
            });
            throw createError('EXTENSION_REGISTRATION_FAILED',
                `扩展注册失败: ${error.message}`, error);
        }
    }

    /**
     * 注销扩展实现
     */
    unregisterExtension(extensionId: string): boolean {
        try {
            const registry = this.extensions.get(extensionId);
            if (!registry) {
                return false;
            }

            const extension = registry.definition;
            const extensionPointRegistry = this.extensionPoints.get(extension.extensionPointId);

            // 从扩展点中移除
            if (extensionPointRegistry) {
                extensionPointRegistry.extensions.delete(extensionId);
            }

            // 移除扩展
            this.extensions.delete(extensionId);

            // 清除缓存
            this.providerCache.delete(extension.extensionPointId);

            this.emit('extensionUnregistered', {
                type: 'extensionUnregistered',
                pluginId: registry.registeredBy,
                data: { extensionId },
                timestamp: Date.now()
            } as PluginEvent);

            this.logger.debug('扩展注销成功', { extensionId });
            return true;
        } catch (error) {
            this.logger.error('扩展注销失败', error, { extensionId });
            throw createError('EXTENSION_UNREGISTRATION_FAILED',
                `扩展注销失败: ${error.message}`, error);
        }
    }

    /**
     * 获取扩展点定义
     */
    getExtensionPoint(extensionPointId: string): PluginExtensionPoint | undefined {
        const registry = this.extensionPoints.get(extensionPointId);
        return registry?.definition;
    }

    /**
     * 获取扩展实现
     */
    getExtension(extensionId: string): PluginExtension | undefined {
        const registry = this.extensions.get(extensionId);
        return registry?.definition;
    }

    /**
     * 获取扩展点的所有扩展实现
     */
    getExtensions(extensionPointId: string): PluginExtension[] {
        const registry = this.extensionPoints.get(extensionPointId);
        if (!registry) {
            return [];
        }

        return Array.from(registry.extensions.values())
            .filter(ext => {
                const extRegistry = this.extensions.get(ext.id);
                return extRegistry?.active;
            })
            .sort((a, b) => (b.priority || 0) - (a.priority || 0));
    }

    /**
     * 获取提供者实现（缓存版本）
     */
    getProviders<T = any>(extensionPointId: string): T[] {
        // 检查缓存
        if (this.providerCache.has(extensionPointId)) {
            return this.providerCache.get(extensionPointId)!;
        }

        const extensions = this.getExtensions(extensionPointId);
        const providers = extensions.map(ext => ext.implementation);

        // 缓存结果
        this.providerCache.set(extensionPointId, providers);

        return providers;
    }

    /**
     * 获取单个提供者实现
     */
    getProvider<T = any>(extensionPointId: string): T | undefined {
        const providers = this.getProviders<T>(extensionPointId);
        return providers[0];
    }

    /**
     * 执行钩子扩展点
     */
    async executeHook(extensionPointId: string, ...args: any[]): Promise<any[]> {
        const providers = this.getProviders(extensionPointId);
        const results: any[] = [];

        for (const provider of providers) {
            try {
                if (typeof provider === 'function') {
                    const result = await provider(...args);
                    results.push(result);
                } else if (provider && typeof provider.execute === 'function') {
                    const result = await provider.execute(...args);
                    results.push(result);
                }
            } catch (error) {
                this.logger.error('钩子执行失败', error, { extensionPointId });
                results.push(error);
            }
        }

        return results;
    }

    /**
     * 检查扩展点是否存在
     */
    hasExtensionPoint(extensionPointId: string): boolean {
        return this.extensionPoints.has(extensionPointId);
    }

    /**
     * 检查扩展是否存在
     */
    hasExtension(extensionId: string): boolean {
        return this.extensions.has(extensionId);
    }

    /**
     * 激活扩展
     */
    activateExtension(extensionId: string): boolean {
        const registry = this.extensions.get(extensionId);
        if (!registry) {
            return false;
        }

        if (!registry.active) {
            registry.active = true;

            // 清除缓存
            this.providerCache.delete(registry.definition.extensionPointId);

            this.emit('extensionActivated', {
                type: 'extensionActivated',
                pluginId: registry.registeredBy,
                data: { extensionId },
                timestamp: Date.now()
            } as PluginEvent);

            this.logger.debug('扩展激活成功', { extensionId });
        }

        return true;
    }

    /**
     * 停用扩展
     */
    deactivateExtension(extensionId: string): boolean {
        const registry = this.extensions.get(extensionId);
        if (!registry) {
            return false;
        }

        if (registry.active) {
            registry.active = false;

            // 清除缓存
            this.providerCache.delete(registry.definition.extensionPointId);

            this.emit('extensionDeactivated', {
                type: 'extensionDeactivated',
                pluginId: registry.registeredBy,
                data: { extensionId },
                timestamp: Date.now()
            } as PluginEvent);

            this.logger.debug('扩展停用成功', { extensionId });
        }

        return true;
    }

    /**
     * 获取所有扩展点
     */
    getAllExtensionPoints(): PluginExtensionPoint[] {
        return Array.from(this.extensionPoints.values())
            .map(registry => registry.definition);
    }

    /**
     * 获取所有扩展实现
     */
    getAllExtensions(): PluginExtension[] {
        return Array.from(this.extensions.values())
            .map(registry => registry.definition);
    }

    /**
     * 获取插件的扩展点
     */
    getPluginExtensionPoints(pluginId: string): PluginExtensionPoint[] {
        const extensionPoints: PluginExtensionPoint[] = [];
        for (const registry of this.extensionPoints.values()) {
            if (registry.registeredBy === pluginId) {
                extensionPoints.push(registry.definition);
            }
        }
        return extensionPoints;
    }

    /**
     * 获取插件的扩展实现
     */
    getPluginExtensions(pluginId: string): PluginExtension[] {
        const extensions: PluginExtension[] = [];
        for (const registry of this.extensions.values()) {
            if (registry.registeredBy === pluginId) {
                extensions.push(registry.definition);
            }
        }
        return extensions;
    }

    /**
     * 清理插件的所有扩展点和扩展
     */
    cleanupPlugin(pluginId: string): void {
        // 清理扩展实现
        const extensionIds = Array.from(this.extensions.keys());
        for (const extensionId of extensionIds) {
            const registry = this.extensions.get(extensionId);
            if (registry && registry.registeredBy === pluginId) {
                this.unregisterExtension(extensionId);
            }
        }

        // 清理扩展点
        const extensionPointIds = Array.from(this.extensionPoints.keys());
        for (const extensionPointId of extensionPointIds) {
            const registry = this.extensionPoints.get(extensionPointId);
            if (registry && registry.registeredBy === pluginId) {
                this.unregisterExtensionPoint(extensionPointId);
            }
        }

        this.logger.debug('插件扩展清理完成', { pluginId });
    }

    /**
     * 获取统计信息
     */
    getStats(): {
        totalExtensionPoints: number;
        totalExtensions: number;
        activeExtensions: number;
        extensionPointsByType: Record<string, number>;
        averageExtensionsPerPoint: number;
    } {
        const totalExtensionPoints = this.extensionPoints.size;
        const totalExtensions = this.extensions.size;
        let activeExtensions = 0;
        const extensionPointsByType: Record<string, number> = {};

        // 统计活跃扩展
        for (const registry of this.extensions.values()) {
            if (registry.active) {
                activeExtensions++;
            }
        }

        // 统计扩展点类型
        for (const registry of this.extensionPoints.values()) {
            const type = registry.definition.type;
            extensionPointsByType[type] = (extensionPointsByType[type] || 0) + 1;
        }

        const averageExtensionsPerPoint = totalExtensionPoints > 0
            ? totalExtensions / totalExtensionPoints
            : 0;

        return {
            totalExtensionPoints,
            totalExtensions,
            activeExtensions,
            extensionPointsByType,
            averageExtensionsPerPoint
        };
    }

    /**
     * 清空所有扩展点和扩展
     */
    clear(): void {
        this.extensionPoints.clear();
        this.extensions.clear();
        this.providerCache.clear();
        this.logger.debug('扩展管理器已清空');
    }

    /**
     * 验证扩展点定义
     */
    private validateExtensionPoint(extensionPoint: PluginExtensionPoint): void {
        if (!extensionPoint.id || !extensionPoint.name) {
            throw createError('INVALID_EXTENSION_POINT', '扩展点必须包含ID和名称');
        }

        if (!['hook', 'provider', 'consumer'].includes(extensionPoint.type)) {
            throw createError('INVALID_EXTENSION_POINT_TYPE',
                '扩展点类型必须是 hook、provider 或 consumer');
        }
    }

    /**
     * 验证扩展实现
     */
    private validateExtension(extension: PluginExtension): void {
        if (!extension.id || !extension.extensionPointId) {
            throw createError('INVALID_EXTENSION', '扩展必须包含ID和扩展点ID');
        }

        if (!extension.implementation) {
            throw createError('INVALID_EXTENSION_IMPLEMENTATION', '扩展必须包含实现');
        }
    }

    /**
     * 验证扩展实现与扩展点的兼容性
     */
    private validateExtensionImplementation(
        extension: PluginExtension,
        extensionPoint: PluginExtensionPoint
    ): void {
        // 基本类型检查
        if (extensionPoint.type === 'hook' && typeof extension.implementation !== 'function') {
            throw createError('INVALID_HOOK_IMPLEMENTATION',
                '钩子扩展的实现必须是函数');
        }

        // 接口检查（如果定义了接口）
        if (extensionPoint.interface) {
            // 这里可以添加更复杂的接口验证逻辑
            // 例如检查方法签名、属性等
        }
    }
}
