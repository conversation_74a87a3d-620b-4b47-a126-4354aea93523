/**
 * @fileoverview 通信插件
 * @description 提供微前端应用间的通信功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import {
    ERROR_CODES,
    generateId,
    type PluginConfig
} from '@micro-core/shared';
import { BasePlugin } from './base-plugin';

// 本地错误创建函数
function createError(code: string, message: string, cause?: Error): Error {
    const error = new Error(message);
    error.name = code;
    if (cause) {
        (error as any).cause = cause;
    }
    return error;
}

// 本地类型定义
interface CommunicationMessage {
    id: string;
    type: string;
    data: any;
    sender: string;
    target?: string;
    timestamp: number;
    encrypted?: boolean;
    compressed?: boolean;
    priority?: number;
    timeout?: number;
}

interface MessageHandler {
    (message: CommunicationMessage): Promise<void> | void;
}

interface MessageFilter {
    (message: CommunicationMessage): boolean;
}

interface MicroCoreKernel {
    registerAPI(name: string, api: any): void;
    getEventBus(): any;
}

/**
 * 通信插件配置接口
 */
interface CommunicationPluginConfig extends PluginConfig {
    /** 消息超时时间（毫秒） */
    timeout?: number;
    /** 最大消息队列长度 */
    maxQueueSize?: number;
    /** 是否启用消息持久化 */
    enablePersistence?: boolean;
    /** 是否启用消息加密 */
    enableEncryption?: boolean;
    /** 加密密钥 */
    encryptionKey?: string;
    /** 是否启用消息压缩 */
    enableCompression?: boolean;
    /** 消息重试次数 */
    retryCount?: number;
    /** 重试间隔（毫秒） */
    retryInterval?: number;
}

/**
 * 消息记录接口
 */
interface MessageRecord {
    /** 消息ID */
    id: string;
    /** 消息内容 */
    message: CommunicationMessage;
    /** 发送时间 */
    timestamp: number;
    /** 发送者 */
    sender: string;
    /** 接收者 */
    receiver?: string;
    /** 消息状态 */
    status: 'pending' | 'sent' | 'delivered' | 'failed';
    /** 重试次数 */
    retryCount: number;
    /** 错误信息 */
    error?: string;
}

/**
 * 消息处理器记录
 */
interface HandlerRecord {
    /** 处理器ID */
    id: string;
    /** 消息类型 */
    type: string;
    /** 处理器函数 */
    handler: MessageHandler;
    /** 注册的应用 */
    appName: string;
    /** 过滤器 */
    filter?: MessageFilter;
    /** 优先级 */
    priority: number;
    /** 创建时间 */
    createdAt: number;
}

/**
 * 通信通道接口
 */
interface CommunicationChannel {
    /** 通道名称 */
    name: string;
    /** 通道类型 */
    type: 'broadcast' | 'unicast' | 'multicast';
    /** 参与的应用 */
    participants: Set<string>;
    /** 消息历史 */
    history: MessageRecord[];
    /** 创建时间 */
    createdAt: number;
}

/**
 * 通信插件类
 * 提供微前端应用间的消息传递、事件通信和数据共享功能
 */
export class CommunicationPlugin extends BasePlugin {
    /** 内核实例 */
    protected kernel?: MicroCoreKernel;

    /** 通信插件配置 */
    private commConfig: Required<CommunicationPluginConfig>;

    /** 消息处理器注册表 */
    private handlers = new Map<string, HandlerRecord[]>();

    /** 消息队列 */
    private messageQueue: MessageRecord[] = [];

    /** 通信通道 */
    private channels = new Map<string, CommunicationChannel>();

    /** 消息历史 */
    private messageHistory: MessageRecord[] = [];

    /** 待确认的消息 */
    private pendingMessages = new Map<string, MessageRecord>();

    /** 消息过滤器 */
    private globalFilters: MessageFilter[] = [];

    constructor(config: CommunicationPluginConfig = {}) {
        super({
            name: 'communication',
            version: '0.1.0',
            description: '微前端应用间通信插件',
            enabled: true,
            ...config
        });

        this.commConfig = {
            name: 'communication',
            version: '0.1.0',
            description: '微前端应用间通信插件',
            enabled: true,
            timeout: 5000,
            maxQueueSize: 1000,
            enablePersistence: false,
            enableEncryption: false,
            encryptionKey: '',
            enableCompression: false,
            retryCount: 3,
            retryInterval: 1000,
            ...config
        } as Required<CommunicationPluginConfig>;
    }

    /**
     * 执行安装逻辑
     */
    protected async doInstall(kernel: MicroCoreKernel): Promise<void> {
        this.kernel = kernel;

        // 注册通信API
        kernel.registerAPI('communication', {
            // 消息发送
            send: this.sendMessage.bind(this),
            broadcast: this.broadcastMessage.bind(this),

            // 消息监听
            on: this.addMessageHandler.bind(this),
            off: this.removeMessageHandler.bind(this),
            once: this.addOnceHandler.bind(this),

            // 通道管理
            createChannel: this.createChannel.bind(this),
            joinChannel: this.joinChannel.bind(this),
            leaveChannel: this.leaveChannel.bind(this),
            getChannels: this.getChannels.bind(this),

            // 消息管理
            getMessageHistory: this.getMessageHistory.bind(this),
            clearHistory: this.clearHistory.bind(this),

            // 过滤器管理
            addFilter: this.addGlobalFilter.bind(this),
            removeFilter: this.removeGlobalFilter.bind(this)
        });

        // 设置消息处理循环
        this.startMessageProcessing();

        // 如果启用持久化，恢复消息历史
        if (this.commConfig.enablePersistence) {
            await this.restoreMessageHistory();
        }

        this.logger.info('通信插件安装完成');
    }

    /**
     * 执行卸载逻辑
     */
    protected async doUninstall(): Promise<void> {
        // 停止消息处理
        this.stopMessageProcessing();

        // 如果启用持久化，保存消息历史
        if (this.commConfig.enablePersistence) {
            await this.saveMessageHistory();
        }

        // 清理数据
        this.handlers.clear();
        this.messageQueue = [];
        this.channels.clear();
        this.messageHistory = [];
        this.pendingMessages.clear();
        this.globalFilters = [];

        this.logger.info('通信插件卸载完成');
    }

    /**
     * 发送消息
     */
    async sendMessage(
        type: string,
        data: any,
        target?: string,
        options: {
            timeout?: number;
            priority?: number;
            persistent?: boolean;
            encrypted?: boolean;
        } = {}
    ): Promise<string> {
        try {
            const message: CommunicationMessage = {
                id: generateId('msg'),
                type,
                data,
                sender: this.getCurrentAppName(),
                target,
                timestamp: Date.now(),
                ...options
            };

            // 创建消息记录
            const record: MessageRecord = {
                id: message.id,
                message,
                timestamp: Date.now(),
                sender: message.sender,
                receiver: target,
                status: 'pending',
                retryCount: 0
            };

            // 应用全局过滤器
            if (!this.applyGlobalFilters(message)) {
                throw createCommunicationError(
                    ERROR_CODES.COMMUNICATION_INVALID_MESSAGE,
                    type,
                    '消息被全局过滤器拒绝'
                );
            }

            // 加密消息（如果启用）
            if (this.commConfig.enableEncryption || options.encrypted) {
                message.data = await this.encryptData(message.data);
                message.encrypted = true;
            }

            // 压缩消息（如果启用）
            if (this.commConfig.enableCompression) {
                message.data = await this.compressData(message.data);
                message.compressed = true;
            }

            // 添加到消息队列
            this.addToQueue(record);

            // 如果是同步发送，等待处理完成
            if (options.timeout !== 0) {
                await this.waitForMessageDelivery(message.id, options.timeout || this.commConfig.timeout);
            }

            this.logger.debug(`发送消息: ${type} -> ${target || 'broadcast'}`);
            return message.id;
        } catch (error) {
            this.logger.error(`发送消息失败:`, error);
            throw error;
        }
    }

    /**
     * 广播消息
     */
    async broadcastMessage(type: string, data: any, options: any = {}): Promise<string> {
        return this.sendMessage(type, data, undefined, options);
    }

    /**
     * 添加消息处理器
     */
    addMessageHandler(
        type: string,
        handler: MessageHandler,
        options: {
            appName?: string;
            filter?: MessageFilter;
            priority?: number;
        } = {}
    ): string {
        const handlerRecord: HandlerRecord = {
            id: generateId('handler'),
            type,
            handler,
            appName: options.appName || this.getCurrentAppName(),
            filter: options.filter,
            priority: options.priority || 0,
            createdAt: Date.now()
        };

        // 获取或创建处理器列表
        if (!this.handlers.has(type)) {
            this.handlers.set(type, []);
        }

        const handlerList = this.handlers.get(type)!;
        handlerList.push(handlerRecord);

        // 按优先级排序
        handlerList.sort((a, b) => b.priority - a.priority);

        this.logger.debug(`添加消息处理器: ${type} (${handlerRecord.appName})`);
        return handlerRecord.id;
    }

    /**
     * 移除消息处理器
     */
    removeMessageHandler(handlerId: string): boolean {
        for (const [type, handlerList] of this.handlers.entries()) {
            const index = handlerList.findIndex(h => h.id === handlerId);
            if (index > -1) {
                const handler = handlerList[index];
                handlerList.splice(index, 1);

                // 如果列表为空，删除类型
                if (handlerList.length === 0) {
                    this.handlers.delete(type);
                }

                this.logger.debug(`移除消息处理器: ${type} (${handler.appName})`);
                return true;
            }
        }
        return false;
    }

    /**
     * 添加一次性处理器
     */
    addOnceHandler(
        type: string,
        handler: MessageHandler,
        options: any = {}
    ): string {
        const onceHandler: MessageHandler = async (message) => {
            try {
                await handler(message);
            } finally {
                this.removeMessageHandler(handlerId);
            }
        };

        const handlerId = this.addMessageHandler(type, onceHandler, options);
        return handlerId;
    }

    /**
     * 创建通信通道
     */
    createChannel(
        name: string,
        type: 'broadcast' | 'unicast' | 'multicast' = 'broadcast'
    ): string {
        if (this.channels.has(name)) {
            throw createCommunicationError(
                ERROR_CODES.COMMUNICATION_INVALID_MESSAGE,
                name,
                `通道 ${name} 已存在`
            );
        }

        const channel: CommunicationChannel = {
            name,
            type,
            participants: new Set(),
            history: [],
            createdAt: Date.now()
        };

        this.channels.set(name, channel);
        this.logger.debug(`创建通信通道: ${name} (${type})`);

        return name;
    }

    /**
     * 加入通道
     */
    joinChannel(channelName: string, appName?: string): boolean {
        const channel = this.channels.get(channelName);
        if (!channel) {
            this.logger.warn(`通道 ${channelName} 不存在`);
            return false;
        }

        const participant = appName || this.getCurrentAppName();
        channel.participants.add(participant);

        this.logger.debug(`应用 ${participant} 加入通道 ${channelName}`);

        // 触发通道事件
        this.kernel?.getEventBus()?.emit('channel:joined', {
            channelName,
            participant,
            participantCount: channel.participants.size
        });

        return true;
    }

    /**
     * 离开通道
     */
    leaveChannel(channelName: string, appName?: string): boolean {
        const channel = this.channels.get(channelName);
        if (!channel) {
            return false;
        }

        const participant = appName || this.getCurrentAppName();
        const removed = channel.participants.delete(participant);

        if (removed) {
            this.logger.debug(`应用 ${participant} 离开通道 ${channelName}`);

            // 触发通道事件
            this.kernel?.getEventBus()?.emit('channel:left', {
                channelName,
                participant,
                participantCount: channel.participants.size
            });
        }

        return removed;
    }

    /**
     * 获取所有通道
     */
    getChannels(): Array<{
        name: string;
        type: string;
        participantCount: number;
        createdAt: number;
    }> {
        return Array.from(this.channels.values()).map(channel => ({
            name: channel.name,
            type: channel.type,
            participantCount: channel.participants.size,
            createdAt: channel.createdAt
        }));
    }

    /**
     * 获取消息历史
     */
    getMessageHistory(limit?: number): MessageRecord[] {
        const history = [...this.messageHistory];
        return limit ? history.slice(-limit) : history;
    }

    /**
     * 清理消息历史
     */
    clearHistory(): void {
        this.messageHistory = [];
        this.logger.debug('清理消息历史');
    }

    /**
     * 添加全局过滤器
     */
    addGlobalFilter(filter: MessageFilter): void {
        this.globalFilters.push(filter);
        this.logger.debug('添加全局消息过滤器');
    }

    /**
     * 移除全局过滤器
     */
    removeGlobalFilter(filter: MessageFilter): boolean {
        const index = this.globalFilters.indexOf(filter);
        if (index > -1) {
            this.globalFilters.splice(index, 1);
            this.logger.debug('移除全局消息过滤器');
            return true;
        }
        return false;
    }

    /**
     * 开始消息处理循环
     */
    private startMessageProcessing(): void {
        this.processMessageQueue();
    }

    /**
     * 停止消息处理
     */
    private stopMessageProcessing(): void {
        // 清空消息队列
        this.messageQueue = [];
    }

    /**
     * 处理消息队列
     */
    private async processMessageQueue(): Promise<void> {
        while (this.messageQueue.length > 0) {
            const record = this.messageQueue.shift();
            if (record) {
                await this.processMessage(record);
            }
        }

        // 继续处理队列
        setTimeout(() => this.processMessageQueue(), 100);
    }

    /**
     * 处理单个消息
     */
    private async processMessage(record: MessageRecord): Promise<void> {
        try {
            record.status = 'sent';

            const { message } = record;
            const handlers = this.handlers.get(message.type) || [];

            if (handlers.length === 0) {
                this.logger.warn(`没有找到消息类型 ${message.type} 的处理器`);
                record.status = 'failed';
                record.error = '没有处理器';
                return;
            }

            // 解密消息（如果需要）
            if (message.encrypted) {
                message.data = await this.decryptData(message.data);
            }

            // 解压消息（如果需要）
            if (message.compressed) {
                message.data = await this.decompressData(message.data);
            }

            // 执行处理器
            const promises = handlers.map(async (handlerRecord) => {
                try {
                    // 应用过滤器
                    if (handlerRecord.filter && !handlerRecord.filter(message)) {
                        return;
                    }

                    // 检查目标应用
                    if (message.target && message.target !== handlerRecord.appName) {
                        return;
                    }

                    await handlerRecord.handler(message);
                } catch (error) {
                    this.logger.error(`消息处理器执行失败:`, error);
                }
            });

            await Promise.all(promises);

            record.status = 'delivered';
            this.addToHistory(record);

        } catch (error) {
            record.status = 'failed';
            record.error = error.message;
            record.retryCount++;

            // 重试逻辑
            if (record.retryCount < this.commConfig.retryCount) {
                setTimeout(() => {
                    this.addToQueue(record);
                }, this.commConfig.retryInterval);
            } else {
                this.logger.error(`消息处理失败，已达最大重试次数:`, error);
                this.addToHistory(record);
            }
        } finally {
            // 从待确认消息中移除
            this.pendingMessages.delete(record.id);
        }
    }

    /**
     * 添加到消息队列
     */
    private addToQueue(record: MessageRecord): void {
        // 检查队列大小限制
        if (this.messageQueue.length >= this.commConfig.maxQueueSize) {
            this.messageQueue.shift(); // 移除最旧的消息
            this.logger.warn('消息队列已满，移除最旧的消息');
        }

        this.messageQueue.push(record);
        this.pendingMessages.set(record.id, record);
    }

    /**
     * 添加到历史记录
     */
    private addToHistory(record: MessageRecord): void {
        this.messageHistory.push(record);

        // 限制历史记录数量
        if (this.messageHistory.length > 1000) {
            this.messageHistory.shift();
        }
    }

    /**
     * 等待消息投递完成
     */
    private async waitForMessageDelivery(messageId: string, timeout: number): Promise<void> {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkStatus = () => {
                const record = this.pendingMessages.get(messageId);

                if (!record) {
                    resolve();
                    return;
                }

                if (record.status === 'delivered') {
                    resolve();
                    return;
                }

                if (record.status === 'failed') {
                    reject(new Error(record.error || '消息发送失败'));
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(createError(
                        'COMMUNICATION_TIMEOUT',
                        '消息发送超时'
                    ));
                    return;
                }

                setTimeout(checkStatus, 50);
            };

            checkStatus();
        });
    }

    /**
     * 应用全局过滤器
     */
    private applyGlobalFilters(message: CommunicationMessage): boolean {
        for (const filter of this.globalFilters) {
            if (!filter(message)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 加密数据
     */
    private async encryptData(data: any): Promise<any> {
        // 这里应该实现真正的加密逻辑
        // 为了简化，这里只是返回原数据
        return data;
    }

    /**
     * 解密数据
     */
    private async decryptData(data: any): Promise<any> {
        // 这里应该实现真正的解密逻辑
        // 为了简化，这里只是返回原数据
        return data;
    }

    /**
     * 压缩数据
     */
    private async compressData(data: any): Promise<any> {
        // 这里应该实现真正的压缩逻辑
        // 为了简化，这里只是返回原数据
        return data;
    }

    /**
     * 解压数据
     */
    private async decompressData(data: any): Promise<any> {
        // 这里应该实现真正的解压逻辑
        // 为了简化，这里只是返回原数据
        return data;
    }

    /**
     * 获取当前应用名称
     */
    private getCurrentAppName(): string {
        // 这里应该从内核获取当前应用名称
        return 'unknown';
    }

    /**
     * 恢复消息历史
     */
    private async restoreMessageHistory(): Promise<void> {
        try {
            const stored = localStorage.getItem('micro-core-message-history');
            if (stored) {
                this.messageHistory = JSON.parse(stored);
                this.logger.debug('恢复消息历史成功');
            }
        } catch (error) {
            this.logger.error('恢复消息历史失败:', error);
        }
    }

    /**
     * 保存消息历史
     */
    private async saveMessageHistory(): Promise<void> {
        try {
            localStorage.setItem('micro-core-message-history', JSON.stringify(this.messageHistory));
            this.logger.debug('保存消息历史成功');
        } catch (error) {
            this.logger.error('保存消息历史失败:', error);
        }
    }

    /**
     * 获取插件统计信息
     */
    override getStats(): Record<string, any> {
        const baseStats = super.getStats();

        return {
            ...baseStats,
            handlersCount: Array.from(this.handlers.values()).reduce((sum, list) => sum + list.length, 0),
            messageQueueSize: this.messageQueue.length,
            channelsCount: this.channels.size,
            messageHistorySize: this.messageHistory.length,
            pendingMessagesCount: this.pendingMessages.size,
            globalFiltersCount: this.globalFilters.length
        };
    }
}
