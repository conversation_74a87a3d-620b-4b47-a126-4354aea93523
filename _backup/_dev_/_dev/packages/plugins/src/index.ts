/**
 * @fileoverview 插件模块入口
 * @description 导出所有插件相关的类和接口
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// ============= 基础插件类 =============
export { BasePlugin } from './base-plugin';

// ============= 核心插件 =============
export { CommunicationPlugin } from './communication-plugin';
export { RouterPlugin } from './router-plugin';

// ============= 类型定义 =============
export { PluginStatus } from './base-plugin';
export type { BasePluginConfig, PluginInterface } from './base-plugin';

