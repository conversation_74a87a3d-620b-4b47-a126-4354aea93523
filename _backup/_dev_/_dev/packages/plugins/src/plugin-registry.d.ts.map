{"version": 3, "file": "plugin-registry.d.ts", "sourceRoot": "", "sources": ["plugin-registry.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,KAAK,EACR,YAAY,EAEZ,cAAc,EACd,mBAAmB,EACtB,MAAM,SAAS,CAAC;AAEjB;;;GAGG;AACH,qBAAa,cAAc;IACvB,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAkC;IACzD,OAAO,CAAC,QAAQ,CAAC,OAAO,CAA0C;IAClE,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAqC;IACnE,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAkC;;IAMlE;;OAEG;IACH,QAAQ,CACJ,QAAQ,EAAE,MAAM,EAChB,WAAW,EAAE,OAAO,UAAU,EAC9B,MAAM,GAAE,YAAiB,EACzB,MAAM,CAAC,EAAE,MAAM,GAChB,IAAI;IA8CP;;OAEG;IACH,UAAU,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IA+BrC;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,UAAU,GAAG,SAAS;IAK/D;;OAEG;IACH,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,YAAY,GAAG,SAAS;IAK3D;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,GAAG,cAAc,GAAG,SAAS;IAI/D;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;IAI3D;;OAEG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAIvC;;OAEG;IACH,sBAAsB,IAAI,MAAM,EAAE;IAIlC;;OAEG;IACH,aAAa,IAAI,mBAAmB,EAAE;IAItC;;OAEG;IACH,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,mBAAmB,EAAE;IAcjD;;OAEG;IACH,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,mBAAmB,EAAE;IAcrD;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,mBAAmB,EAAE;IAoB3D;;OAEG;IACH,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAE;IAK3C;;OAEG;IACH,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAE;IAUzC;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,GAAG;QAAE,OAAO,EAAE,MAAM,EAAE,CAAC;QAAC,QAAQ,EAAE,MAAM,EAAE,CAAA;KAAE;IA2C9E;;OAEG;IACH,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE;IAmC/C;;OAEG;IACH,QAAQ,IAAI;QACR,YAAY,EAAE,MAAM,CAAC;QACrB,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,eAAe,EAAE,MAAM,CAAC;QACxB,mBAAmB,EAAE,MAAM,CAAC;KAC/B;IAuBD;;OAEG;IACH,KAAK,IAAI,IAAI;IAOb;;OAEG;IACH,MAAM,IAAI;QACN,OAAO,EAAE,mBAAmB,EAAE,CAAC;QAC/B,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QACzC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;KAC1C;IAgBD;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE;QACT,OAAO,EAAE,mBAAmB,EAAE,CAAC;QAC/B,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QACzC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;KAC1C,GAAG,IAAI;IA6BR;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAUxB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAW3B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAWxB;;OAEG;IACH,OAAO,CAAC,qBAAqB;CAShC"}