/**
 * @fileoverview 插件注册表测试
 * @description 测试插件注册表的功能
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { PluginRegistry } from '../plugin-registry'
import { PluginType } from '../types'

describe('PluginRegistry', () => {
    let pluginRegistry: PluginRegistry

    beforeEach(() => {
        pluginRegistry = new PluginRegistry()
    })

    describe('插件注册', () => {
        it('应该能够注册Router插件', () => {
            const plugin = {
                name: 'router-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)
            expect(pluginRegistry.has('router-plugin')).toBe(true)
        })

        it('应该能够注册Communication插件', () => {
            const plugin = {
                name: 'communication-plugin',
                version: '1.0.0',
                type: PluginType.COMMUNICATION,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)
            expect(pluginRegistry.has('communication-plugin')).toBe(true)
        })

        it('应该能够注册Auth插件', () => {
            const plugin = {
                name: 'auth-plugin',
                version: '1.0.0',
                type: PluginType.AUTH,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)
            expect(pluginRegistry.has('auth-plugin')).toBe(true)
        })

        it('应该能够注册DevTools插件', () => {
            const plugin = {
                name: 'devtools-plugin',
                version: '1.0.0',
                type: PluginType.DEVTOOLS,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)
            expect(pluginRegistry.has('devtools-plugin')).toBe(true)
        })
    })

    describe('插件管理', () => {
        it('应该能够获取插件', () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)
            const retrieved = pluginRegistry.get('test-plugin')
            expect(retrieved).toBe(plugin)
        })

        it('应该能够卸载插件', async () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)
            await pluginRegistry.install('test-plugin')
            await pluginRegistry.uninstall('test-plugin')

            expect(plugin.uninstall).toHaveBeenCalled()
        })

        it('应该能够获取所有插件', () => {
            const plugin1 = {
                name: 'plugin1',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            const plugin2 = {
                name: 'plugin2',
                version: '1.0.0',
                type: PluginType.AUTH,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin1)
            pluginRegistry.register(plugin2)

            const plugins = pluginRegistry.getAll()
            expect(plugins).toHaveLength(2)
        })

        it('应该能够按类型获取插件', () => {
            const routerPlugin = {
                name: 'router-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            const authPlugin = {
                name: 'auth-plugin',
                version: '1.0.0',
                type: PluginType.AUTH,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(routerPlugin)
            pluginRegistry.register(authPlugin)

            const routerPlugins = pluginRegistry.getByType(PluginType.ROUTER)
            expect(routerPlugins).toHaveLength(1)
            expect(routerPlugins[0]).toBe(routerPlugin)
        })
    })

    describe('插件生命周期', () => {
        it('应该能够安装插件', async () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)
            await pluginRegistry.install('test-plugin')

            expect(plugin.install).toHaveBeenCalled()
            expect(pluginRegistry.isInstalled('test-plugin')).toBe(true)
        })

        it('应该能够卸载插件', async () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn().mockResolvedValue(undefined)
            }

            pluginRegistry.register(plugin)
            await pluginRegistry.install('test-plugin')
            await pluginRegistry.uninstall('test-plugin')

            expect(plugin.uninstall).toHaveBeenCalled()
            expect(pluginRegistry.isInstalled('test-plugin')).toBe(false)
        })

        it('应该能够批量安装插件', async () => {
            const plugin1 = {
                name: 'plugin1',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn()
            }

            const plugin2 = {
                name: 'plugin2',
                version: '1.0.0',
                type: PluginType.AUTH,
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin1)
            pluginRegistry.register(plugin2)

            await pluginRegistry.installAll()

            expect(plugin1.install).toHaveBeenCalled()
            expect(plugin2.install).toHaveBeenCalled()
        })
    })

    describe('错误处理', () => {
        it('应该在注册重复插件时抛出错误', () => {
            const plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)

            expect(() => {
                pluginRegistry.register(plugin)
            }).toThrow('插件 test-plugin 已存在')
        })

        it('应该在安装不存在的插件时抛出错误', async () => {
            await expect(pluginRegistry.install('nonexistent')).rejects.toThrow('插件 nonexistent 未注册')
        })

        it('应该处理插件安装失败', async () => {
            const plugin = {
                name: 'failing-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn().mockRejectedValue(new Error('安装失败')),
                uninstall: vi.fn()
            }

            pluginRegistry.register(plugin)

            await expect(pluginRegistry.install('failing-plugin')).rejects.toThrow('安装失败')
        })
    })

    describe('依赖管理', () => {
        it('应该能够处理插件依赖', async () => {
            const basePlugin = {
                name: 'base-plugin',
                version: '1.0.0',
                type: PluginType.ROUTER,
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn()
            }

            const dependentPlugin = {
                name: 'dependent-plugin',
                version: '1.0.0',
                type: PluginType.AUTH,
                dependencies: ['base-plugin'],
                install: vi.fn().mockResolvedValue(undefined),
                uninstall: vi.fn()
            }

            pluginRegistry.register(basePlugin)
            pluginRegistry.register(dependentPlugin)

            await pluginRegistry.install('dependent-plugin')

            expect(basePlugin.install).toHaveBeenCalled()
            expect(dependentPlugin.install).toHaveBeenCalled()
        })

        it('应该在依赖缺失时抛出错误', async () => {
            const dependentPlugin = {
                name: 'dependent-plugin',
                version: '1.0.0',
                type: PluginType.AUTH,
                dependencies: ['missing-plugin'],
                install: vi.fn(),
                uninstall: vi.fn()
            }

            pluginRegistry.register(dependentPlugin)

            await expect(pluginRegistry.install('dependent-plugin')).rejects.toThrow('依赖插件 missing-plugin 未注册')
        })
    })
})