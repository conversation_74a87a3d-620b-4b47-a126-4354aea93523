/**
 * 插件生命周期管理器
 * 
 * @description 管理插件的完整生命周期
 * <AUTHOR> <<EMAIL>>
 */

import { createError, EventEmitter, Logger } from '@micro-core/shared';
import { BasePlugin } from './base-plugin';
import type {
    PluginConfig,
    PluginLifecycleHook,
    PluginMetrics,
    PluginState
} from './types';

/**
 * 插件实例信息
 */
interface PluginInstance {
    /** 插件ID */
    id: string;
    /** 插件实例 */
    instance: BasePlugin;
    /** 插件状态 */
    state: PluginState;
    /** 插件配置 */
    config: PluginConfig;
    /** 创建时间 */
    createdAt: number;
    /** 最后活动时间 */
    lastActivity: number;
    /** 性能指标 */
    metrics: PluginMetrics;
    /** 错误信息 */
    lastError?: Error;
}

/**
 * 生命周期钩子函数
 */
type LifecycleHookFunction = (pluginId: string, instance: BasePlugin) => Promise<void> | void;

/**
 * 插件生命周期管理器
 */
export class PluginLifecycleManager extends EventEmitter {
    private readonly logger: Logger;
    private readonly instances = new Map<string, PluginInstance>();
    private readonly hooks = new Map<PluginLifecycleHook, Set<LifecycleHookFunction>>();
    private readonly stateTransitions = new Map<string, PluginState[]>();

    constructor() {
        super();
        this.logger = new Logger('PluginLifecycleManager');
        this.initializeHooks();
        this.logger.info('插件生命周期管理器初始化完成');
    }

    /**
     * 创建插件实例
     */
    async createInstance(
        pluginId: string,
        pluginClass: typeof BasePlugin,
        config: PluginConfig = {}
    ): Promise<BasePlugin> {
        try {
            if (this.instances.has(pluginId)) {
                throw createError('PLUGIN_ALREADY_EXISTS', `插件实例已存在: ${pluginId}`);
            }

            // 执行创建前钩子
            await this.executeHooks('beforeInit', pluginId, null);

            // 创建插件实例
            const instance = new pluginClass(config);
            const now = Date.now();

            // 创建实例信息
            const instanceInfo: PluginInstance = {
                id: pluginId,
                instance,
                state: 'registered',
                config,
                createdAt: now,
                lastActivity: now,
                metrics: {
                    pluginId,
                    loadTime: 0,
                    activationTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    errorCount: 0,
                    lastActivity: now
                }
            };

            this.instances.set(pluginId, instanceInfo);
            this.stateTransitions.set(pluginId, ['registered']);

            // 执行创建后钩子
            await this.executeHooks('afterInit', pluginId, instance);

            this.emit('instanceCreated', { pluginId, instance });
            this.logger.debug('插件实例创建成功', { pluginId });

            return instance;
        } catch (error) {
            this.logger.error('插件实例创建失败', error, { pluginId });
            throw createError('PLUGIN_INSTANCE_CREATION_FAILED',
                `插件实例创建失败: ${error.message}`, error);
        }
    }

    /**
     * 加载插件
     */
    async loadPlugin(pluginId: string): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(pluginId);
            if (instanceInfo.state !== 'registered') {
                throw createError('INVALID_PLUGIN_STATE',
                    `插件状态无效，无法加载: ${instanceInfo.state}`);
            }

            const startTime = Date.now();

            // 执行加载前钩子
            await this.executeHooks('beforeLoad', pluginId, instanceInfo.instance);

            // 更新状态
            this.updateState(pluginId, 'loaded');

            // 调用插件的加载方法
            if (typeof instanceInfo.instance.load === 'function') {
                await instanceInfo.instance.load();
            }

            // 更新指标
            const loadTime = Date.now() - startTime;
            instanceInfo.metrics.loadTime = loadTime;
            instanceInfo.lastActivity = Date.now();

            // 执行加载后钩子
            await this.executeHooks('afterLoad', pluginId, instanceInfo.instance);

            this.emit('pluginLoaded', { pluginId, loadTime });
            this.logger.debug('插件加载成功', { pluginId, loadTime });
        } catch (error) {
            this.handlePluginError(pluginId, error);
            throw createError('PLUGIN_LOAD_FAILED', `插件加载失败: ${error.message}`, error);
        }
    }

    /**
     * 激活插件
     */
    async activatePlugin(pluginId: string): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(pluginId);
            if (instanceInfo.state !== 'loaded') {
                throw createError('INVALID_PLUGIN_STATE',
                    `插件状态无效，无法激活: ${instanceInfo.state}`);
            }

            const startTime = Date.now();

            // 执行激活前钩子
            await this.executeHooks('beforeActivate', pluginId, instanceInfo.instance);

            // 更新状态
            this.updateState(pluginId, 'active');

            // 调用插件的激活方法
            if (typeof instanceInfo.instance.activate === 'function') {
                await instanceInfo.instance.activate();
            }

            // 更新指标
            const activationTime = Date.now() - startTime;
            instanceInfo.metrics.activationTime = activationTime;
            instanceInfo.lastActivity = Date.now();

            // 执行激活后钩子
            await this.executeHooks('afterActivate', pluginId, instanceInfo.instance);

            this.emit('pluginActivated', { pluginId, activationTime });
            this.logger.debug('插件激活成功', { pluginId, activationTime });
        } catch (error) {
            this.handlePluginError(pluginId, error);
            throw createError('PLUGIN_ACTIVATION_FAILED', `插件激活失败: ${error.message}`, error);
        }
    }

    /**
     * 停用插件
     */
    async deactivatePlugin(pluginId: string): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(pluginId);
            if (instanceInfo.state !== 'active') {
                throw createError('INVALID_PLUGIN_STATE',
                    `插件状态无效，无法停用: ${instanceInfo.state}`);
            }

            // 执行停用前钩子
            await this.executeHooks('beforeDeactivate', pluginId, instanceInfo.instance);

            // 更新状态
            this.updateState(pluginId, 'inactive');

            // 调用插件的停用方法
            if (typeof instanceInfo.instance.deactivate === 'function') {
                await instanceInfo.instance.deactivate();
            }

            instanceInfo.lastActivity = Date.now();

            // 执行停用后钩子
            await this.executeHooks('afterDeactivate', pluginId, instanceInfo.instance);

            this.emit('pluginDeactivated', { pluginId });
            this.logger.debug('插件停用成功', { pluginId });
        } catch (error) {
            this.handlePluginError(pluginId, error);
            throw createError('PLUGIN_DEACTIVATION_FAILED', `插件停用失败: ${error.message}`, error);
        }
    }

    /**
     * 卸载插件
     */
    async unloadPlugin(pluginId: string): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(pluginId);
            if (!['loaded', 'inactive'].includes(instanceInfo.state)) {
                throw createError('INVALID_PLUGIN_STATE',
                    `插件状态无效，无法卸载: ${instanceInfo.state}`);
            }

            // 执行卸载前钩子
            await this.executeHooks('beforeUnload', pluginId, instanceInfo.instance);

            // 更新状态
            this.updateState(pluginId, 'unloaded');

            // 调用插件的卸载方法
            if (typeof instanceInfo.instance.unload === 'function') {
                await instanceInfo.instance.unload();
            }

            instanceInfo.lastActivity = Date.now();

            // 执行卸载后钩子
            await this.executeHooks('afterUnload', pluginId, instanceInfo.instance);

            this.emit('pluginUnloaded', { pluginId });
            this.logger.debug('插件卸载成功', { pluginId });
        } catch (error) {
            this.handlePluginError(pluginId, error);
            throw createError('PLUGIN_UNLOAD_FAILED', `插件卸载失败: ${error.message}`, error);
        }
    }

    /**
     * 销毁插件实例
     */
    async destroyInstance(pluginId: string): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(pluginId);

            // 执行销毁前钩子
            await this.executeHooks('beforeDestroy', pluginId, instanceInfo.instance);

            // 调用插件的销毁方法
            if (typeof instanceInfo.instance.destroy === 'function') {
                await instanceInfo.instance.destroy();
            }

            // 移除实例
            this.instances.delete(pluginId);
            this.stateTransitions.delete(pluginId);

            // 执行销毁后钩子
            await this.executeHooks('afterDestroy', pluginId, instanceInfo.instance);

            this.emit('instanceDestroyed', { pluginId });
            this.logger.debug('插件实例销毁成功', { pluginId });
        } catch (error) {
            this.logger.error('插件实例销毁失败', error, { pluginId });
            throw createError('PLUGIN_INSTANCE_DESTRUCTION_FAILED',
                `插件实例销毁失败: ${error.message}`, error);
        }
    }

    /**
     * 获取插件实例
     */
    getInstance(pluginId: string): BasePlugin | undefined {
        const instanceInfo = this.instances.get(pluginId);
        return instanceInfo?.instance;
    }

    /**
     * 获取插件状态
     */
    getState(pluginId: string): PluginState | undefined {
        const instanceInfo = this.instances.get(pluginId);
        return instanceInfo?.state;
    }

    /**
     * 获取插件指标
     */
    getMetrics(pluginId: string): PluginMetrics | undefined {
        const instanceInfo = this.instances.get(pluginId);
        return instanceInfo?.metrics;
    }

    /**
     * 获取所有插件状态
     */
    getAllStates(): Record<string, PluginState> {
        const states: Record<string, PluginState> = {};
        for (const [id, info] of this.instances) {
            states[id] = info.state;
        }
        return states;
    }

    /**
     * 获取指定状态的插件
     */
    getPluginsByState(state: PluginState): string[] {
        const plugins: string[] = [];
        for (const [id, info] of this.instances) {
            if (info.state === state) {
                plugins.push(id);
            }
        }
        return plugins;
    }

    /**
     * 检查插件是否存在
     */
    hasInstance(pluginId: string): boolean {
        return this.instances.has(pluginId);
    }

    /**
     * 添加生命周期钩子
     */
    addHook(hook: PluginLifecycleHook, fn: LifecycleHookFunction): void {
        if (!this.hooks.has(hook)) {
            this.hooks.set(hook, new Set());
        }
        this.hooks.get(hook)!.add(fn);
        this.logger.debug('生命周期钩子添加成功', { hook });
    }

    /**
     * 移除生命周期钩子
     */
    removeHook(hook: PluginLifecycleHook, fn: LifecycleHookFunction): boolean {
        const hookSet = this.hooks.get(hook);
        if (hookSet) {
            const removed = hookSet.delete(fn);
            if (removed) {
                this.logger.debug('生命周期钩子移除成功', { hook });
            }
            return removed;
        }
        return false;
    }

    /**
     * 更新插件指标
     */
    updateMetrics(pluginId: string, metrics: Partial<PluginMetrics>): void {
        const instanceInfo = this.instances.get(pluginId);
        if (instanceInfo) {
            Object.assign(instanceInfo.metrics, metrics);
            instanceInfo.lastActivity = Date.now();
            this.emit('metricsUpdated', { pluginId, metrics: instanceInfo.metrics });
        }
    }

    /**
     * 获取状态转换历史
     */
    getStateTransitions(pluginId: string): PluginState[] {
        return this.stateTransitions.get(pluginId) || [];
    }

    /**
     * 获取所有实例信息
     */
    getAllInstances(): Record<string, PluginInstance> {
        const instances: Record<string, PluginInstance> = {};
        for (const [id, info] of this.instances) {
            instances[id] = { ...info };
        }
        return instances;
    }

    /**
     * 清理所有实例
     */
    async cleanup(): Promise<void> {
        const pluginIds = Array.from(this.instances.keys());

        for (const pluginId of pluginIds) {
            try {
                const state = this.getState(pluginId);

                // 按状态顺序清理
                if (state === 'active') {
                    await this.deactivatePlugin(pluginId);
                }
                if (['loaded', 'inactive'].includes(state!)) {
                    await this.unloadPlugin(pluginId);
                }
                await this.destroyInstance(pluginId);
            } catch (error) {
                this.logger.error('插件清理失败', error, { pluginId });
            }
        }

        this.hooks.clear();
        this.logger.info('插件生命周期管理器清理完成');
    }

    /**
     * 获取实例信息
     */
    private getInstanceInfo(pluginId: string): PluginInstance {
        const instanceInfo = this.instances.get(pluginId);
        if (!instanceInfo) {
            throw createError('PLUGIN_INSTANCE_NOT_FOUND', `插件实例不存在: ${pluginId}`);
        }
        return instanceInfo;
    }

    /**
     * 更新插件状态
     */
    private updateState(pluginId: string, newState: PluginState): void {
        const instanceInfo = this.getInstanceInfo(pluginId);
        const oldState = instanceInfo.state;
        instanceInfo.state = newState;

        // 记录状态转换
        const transitions = this.stateTransitions.get(pluginId) || [];
        transitions.push(newState);
        this.stateTransitions.set(pluginId, transitions);

        this.emit('stateChanged', { pluginId, oldState, newState });
        this.logger.debug('插件状态更新', { pluginId, oldState, newState });
    }

    /**
     * 执行生命周期钩子
     */
    private async executeHooks(
        hook: PluginLifecycleHook,
        pluginId: string,
        instance: BasePlugin | null
    ): Promise<void> {
        const hookSet = this.hooks.get(hook);
        if (!hookSet || hookSet.size === 0) {
            return;
        }

        const promises: Promise<void>[] = [];
        for (const fn of hookSet) {
            try {
                const result = fn(pluginId, instance!);
                if (result instanceof Promise) {
                    promises.push(result);
                }
            } catch (error) {
                this.logger.error('生命周期钩子执行失败', error, { hook, pluginId });
            }
        }

        if (promises.length > 0) {
            await Promise.all(promises);
        }
    }

    /**
     * 处理插件错误
     */
    private handlePluginError(pluginId: string, error: Error): void {
        const instanceInfo = this.instances.get(pluginId);
        if (instanceInfo) {
            instanceInfo.lastError = error;
            instanceInfo.metrics.errorCount++;
            instanceInfo.state = 'error';
            this.emit('pluginError', { pluginId, error });
        }
        this.logger.error('插件错误', error, { pluginId });
    }

    /**
     * 初始化钩子
     */
    private initializeHooks(): void {
        const hooks: PluginLifecycleHook[] = [
            'beforeInit', 'afterInit',
            'beforeRegister', 'afterRegister',
            'beforeLoad', 'afterLoad',
            'beforeActivate', 'afterActivate',
            'beforeDeactivate', 'afterDeactivate',
            'beforeUnload', 'afterUnload',
            'beforeUnregister', 'afterUnregister',
            'beforeDestroy', 'afterDestroy'
        ];

        for (const hook of hooks) {
            this.hooks.set(hook, new Set());
        }
    }
}