{"version": 3, "file": "base-plugin.d.ts", "sourceRoot": "", "sources": ["base-plugin.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAe,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAE/D;;GAEG;AACH,MAAM,WAAW,eAAe;IAC5B,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW;IACX,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW;IACX,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IACxB,aAAa;IACb,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACzC,cAAc;IACd,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAC7C,aAAa;IACb,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACxC,aAAa;IACb,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACvC,aAAa;IACb,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;CAC7C;AAED;;GAEG;AACH,oBAAY,YAAY;IACpB,OAAO,YAAY;IACnB,UAAU,eAAe;IACzB,SAAS,cAAc;IACvB,QAAQ,aAAa;IACrB,OAAO,YAAY;IACnB,SAAS,cAAc;IACvB,YAAY,iBAAiB;IAC7B,KAAK,UAAU;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC7B,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW;IACX,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW;IACX,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW;IACX,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IACxB,aAAa;IACb,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,YAAY;IACZ,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;CACtB;AAED;;;GAGG;AACH,8BAAsB,UAAW,YAAW,eAAe;IACvD,WAAW;IACX,SAAgB,IAAI,EAAE,MAAM,CAAC;IAE7B,WAAW;IACX,SAAgB,OAAO,EAAE,MAAM,CAAC;IAEhC,WAAW;IACX,SAAgB,WAAW,CAAC,EAAE,MAAM,CAAC;IAErC,WAAW;IACX,SAAgB,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IAExC,WAAW;IACX,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,gBAAgB,CAAC;IAE5C,WAAW;IACX,SAAS,CAAC,MAAM,EAAE,YAAY,CAAwB;IAEtD,YAAY;IACZ,SAAS,CAAC,IAAI,EAAE,GAAG,CAAQ;IAE3B,WAAW;IACX,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;IAErC,YAAY;IACZ,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,OAAO,YAAY,CAAC,CAAC;IAElD,WAAW;IACX,SAAS,CAAC,KAAK;;;;;;;MAOb;gBAEU,MAAM,EAAE,gBAAgB;IAepC;;OAEG;IACG,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAsCvC;;OAEG;IACG,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAqB3C;;OAEG;IACG,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAwBtC;;OAEG;IACG,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAyBrC;;OAEG;IACG,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IA2BxC;;OAEG;IACG,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IA8B7B;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IA0B9B;;OAEG;IACH,SAAS,IAAI,YAAY;IAIzB;;OAEG;IACH,WAAW,IAAI,OAAO;IAKtB;;OAEG;IACH,SAAS,IAAI,OAAO;IAIpB;;OAEG;IACH,SAAS,IAAI,gBAAgB;IAI7B;;OAEG;IACH,QAAQ,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAW/B;;OAEG;IACH,cAAc,IAAI,MAAM;IAMxB;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAItD;;OAEG;cACa,YAAY,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAIvD;;OAEG;cACa,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAIlD;;OAEG;cACa,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAIjD;;OAEG;cACa,SAAS,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAKpD;;OAEG;cACa,QAAQ,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC;IAI1C;;OAEG;cACa,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC;IAI3C;;OAEG;cACa,iBAAiB,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAe3D;;OAEG;cACa,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;CAW3C"}