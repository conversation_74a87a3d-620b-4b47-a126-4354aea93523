/**
 * 插件管理器实现
 * 
 * @description 统一的插件注册、加载和生命周期管理
 * <AUTHOR> <<EMAIL>>
 */

import type {
    PluginConfig,
    PluginDependency,
    PluginLifecycleHook,
    PluginManagerOptions,
    PluginMetadata,
    PluginState
} from '@micro-core/shared';
import { createError, ERROR_CODES, Logger } from '@micro-core/shared';
import { BasePlugin } from './base-plugin';

/**
 * 插件管理器
 * 负责插件的注册、加载、卸载和生命周期管理
 */
export class PluginManager {
    private readonly logger: Logger;
    private readonly options: PluginManagerOptions;
    private readonly plugins = new Map<string, PluginInstance>();
    private readonly pluginStates = new Map<string, PluginState>();
    private readonly lifecycleHooks = new Map<PluginLifecycleHook, Set<Function>>();
    private readonly dependencyGraph = new Map<string, Set<string>>();
    private isInitialized = false;

    constructor(options: PluginManagerOptions = {}) {
        this.logger = new Logger('PluginManager');
        this.options = {
            enableLazyLoading: true,
            enableDependencyResolution: true,
            enableHotReload: false,
            maxConcurrentLoads: 5,
            loadTimeout: 30000,
            ...options
        };

        this.logger.info('插件管理器初始化完成', { options: this.options });
    }

    /**
     * 初始化插件管理器
     */
    async initialize(): Promise<void> {
        try {
            if (this.isInitialized) {
                this.logger.warn('插件管理器已经初始化');
                return;
            }

            // 触发初始化钩子
            await this.executeLifecycleHook('beforeInit');

            // 初始化内部状态
            this.setupInternalHooks();

            this.isInitialized = true;

            // 触发初始化完成钩子
            await this.executeLifecycleHook('afterInit');

            this.logger.info('插件管理器初始化成功');
        } catch (error) {
            this.logger.error('插件管理器初始化失败', error);
            throw createError(ERROR_CODES.PLUGIN_MANAGER_INIT_FAILED, `插件管理器初始化失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 注册插件
     */
    async registerPlugin(
        pluginId: string,
        pluginClass: typeof BasePlugin,
        config: PluginConfig = {}
    ): Promise<void> {
        try {
            this.validatePluginId(pluginId);

            if (this.plugins.has(pluginId)) {
                throw createError(ERROR_CODES.PLUGIN_ALREADY_REGISTERED, `插件已注册: ${pluginId}`);
            }

            // 创建插件实例
            const plugin = new pluginClass(config);
            const metadata = plugin.getMetadata();

            // 验证插件元数据
            this.validatePluginMetadata(metadata);

            // 检查依赖关系
            if (this.options.enableDependencyResolution && metadata.dependencies) {
                await this.resolveDependencies(pluginId, metadata.dependencies);
            }

            // 注册插件实例
            const pluginInstance: PluginInstance = {
                id: pluginId,
                plugin,
                metadata,
                config,
                state: 'registered',
                loadedAt: null,
                activatedAt: null
            };

            this.plugins.set(pluginId, pluginInstance);
            this.pluginStates.set(pluginId, 'registered');

            // 更新依赖图
            if (metadata.dependencies) {
                this.updateDependencyGraph(pluginId, metadata.dependencies);
            }

            this.logger.debug('插件注册成功', {
                pluginId,
                name: metadata.name,
                version: metadata.version
            });

            // 触发注册钩子
            await this.executeLifecycleHook('afterRegister', { pluginId, plugin });
        } catch (error) {
            this.logger.error('插件注册失败', error, { pluginId });
            throw createError(ERROR_CODES.PLUGIN_REGISTRATION_FAILED, `插件注册失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 加载插件
     */
    async loadPlugin(pluginId: string): Promise<void> {
        try {
            const pluginInstance = this.getPluginInstance(pluginId);

            if (pluginInstance.state === 'loaded' || pluginInstance.state === 'active') {
                this.logger.debug('插件已加载', { pluginId });
                return;
            }

            // 检查依赖是否已加载
            if (this.options.enableDependencyResolution) {
                await this.loadDependencies(pluginId);
            }

            // 触发加载前钩子
            await this.executeLifecycleHook('beforeLoad', { pluginId, plugin: pluginInstance.plugin });

            // 执行插件加载
            await pluginInstance.plugin.install(this);

            // 更新状态
            pluginInstance.state = 'loaded';
            pluginInstance.loadedAt = Date.now();
            this.pluginStates.set(pluginId, 'loaded');

            this.logger.debug('插件加载成功', { pluginId });

            // 触发加载后钩子
            await this.executeLifecycleHook('afterLoad', { pluginId, plugin: pluginInstance.plugin });
        } catch (error) {
            this.logger.error('插件加载失败', error, { pluginId });
            this.pluginStates.set(pluginId, 'error');
            throw createError(ERROR_CODES.PLUGIN_LOAD_FAILED, `插件加载失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 激活插件
     */
    async activatePlugin(pluginId: string): Promise<void> {
        try {
            const pluginInstance = this.getPluginInstance(pluginId);

            if (pluginInstance.state === 'active') {
                this.logger.debug('插件已激活', { pluginId });
                return;
            }

            // 确保插件已加载
            if (pluginInstance.state !== 'loaded') {
                await this.loadPlugin(pluginId);
            }

            // 触发激活前钩子
            await this.executeLifecycleHook('beforeActivate', { pluginId, plugin: pluginInstance.plugin });

            // 执行插件激活
            await pluginInstance.plugin.start?.(this);

            // 更新状态
            pluginInstance.state = 'active';
            pluginInstance.activatedAt = Date.now();
            this.pluginStates.set(pluginId, 'active');

            this.logger.debug('插件激活成功', { pluginId });

            // 触发激活后钩子
            await this.executeLifecycleHook('afterActivate', { pluginId, plugin: pluginInstance.plugin });
        } catch (error) {
            this.logger.error('插件激活失败', error, { pluginId });
            this.pluginStates.set(pluginId, 'error');
            throw createError(ERROR_CODES.PLUGIN_ACTIVATION_FAILED, `插件激活失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 停用插件
     */
    async deactivatePlugin(pluginId: string): Promise<void> {
        try {
            const pluginInstance = this.getPluginInstance(pluginId);

            if (pluginInstance.state !== 'active') {
                this.logger.debug('插件未激活', { pluginId });
                return;
            }

            // 检查是否有其他插件依赖此插件
            const dependents = this.getDependents(pluginId);
            if (dependents.length > 0) {
                const activeDependents = dependents.filter(id => this.pluginStates.get(id) === 'active');
                if (activeDependents.length > 0) {
                    throw createError(ERROR_CODES.PLUGIN_HAS_ACTIVE_DEPENDENTS,
                        `插件有活跃的依赖者，无法停用: ${activeDependents.join(', ')}`);
                }
            }

            // 触发停用前钩子
            await this.executeLifecycleHook('beforeDeactivate', { pluginId, plugin: pluginInstance.plugin });

            // 执行插件停用
            await pluginInstance.plugin.stop?.(this);

            // 更新状态
            pluginInstance.state = 'loaded';
            pluginInstance.activatedAt = null;
            this.pluginStates.set(pluginId, 'loaded');

            this.logger.debug('插件停用成功', { pluginId });

            // 触发停用后钩子
            await this.executeLifecycleHook('afterDeactivate', { pluginId, plugin: pluginInstance.plugin });
        } catch (error) {
            this.logger.error('插件停用失败', error, { pluginId });
            throw createError(ERROR_CODES.PLUGIN_DEACTIVATION_FAILED, `插件停用失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 卸载插件
     */
    async unloadPlugin(pluginId: string): Promise<void> {
        try {
            const pluginInstance = this.getPluginInstance(pluginId);

            // 如果插件已激活，先停用
            if (pluginInstance.state === 'active') {
                await this.deactivatePlugin(pluginId);
            }

            if (pluginInstance.state === 'unloaded') {
                this.logger.debug('插件已卸载', { pluginId });
                return;
            }

            // 触发卸载前钩子
            await this.executeLifecycleHook('beforeUnload', { pluginId, plugin: pluginInstance.plugin });

            // 执行插件卸载
            await pluginInstance.plugin.destroy?.(this);

            // 更新状态
            pluginInstance.state = 'unloaded';
            pluginInstance.loadedAt = null;
            pluginInstance.activatedAt = null;
            this.pluginStates.set(pluginId, 'unloaded');

            this.logger.debug('插件卸载成功', { pluginId });

            // 触发卸载后钩子
            await this.executeLifecycleHook('afterUnload', { pluginId, plugin: pluginInstance.plugin });
        } catch (error) {
            this.logger.error('插件卸载失败', error, { pluginId });
            throw createError(ERROR_CODES.PLUGIN_UNLOAD_FAILED, `插件卸载失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 注销插件
     */
    async unregisterPlugin(pluginId: string): Promise<void> {
        try {
            const pluginInstance = this.getPluginInstance(pluginId);

            // 如果插件已加载，先卸载
            if (pluginInstance.state !== 'unloaded' && pluginInstance.state !== 'registered') {
                await this.unloadPlugin(pluginId);
            }

            // 触发注销前钩子
            await this.executeLifecycleHook('beforeUnregister', { pluginId, plugin: pluginInstance.plugin });

            // 从依赖图中移除
            this.dependencyGraph.delete(pluginId);
            for (const deps of this.dependencyGraph.values()) {
                deps.delete(pluginId);
            }

            // 移除插件实例
            this.plugins.delete(pluginId);
            this.pluginStates.delete(pluginId);

            this.logger.debug('插件注销成功', { pluginId });

            // 触发注销后钩子
            await this.executeLifecycleHook('afterUnregister', { pluginId });
        } catch (error) {
            this.logger.error('插件注销失败', error, { pluginId });
            throw createError(ERROR_CODES.PLUGIN_UNREGISTRATION_FAILED, `插件注销失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 获取插件实例
     */
    getPlugin(pluginId: string): BasePlugin | undefined {
        const pluginInstance = this.plugins.get(pluginId);
        return pluginInstance?.plugin;
    }

    /**
     * 获取插件状态
     */
    getPluginState(pluginId: string): PluginState | undefined {
        return this.pluginStates.get(pluginId);
    }

    /**
     * 获取所有插件ID
     */
    getPluginIds(): string[] {
        return Array.from(this.plugins.keys());
    }

    /**
     * 获取指定状态的插件
     */
    getPluginsByState(state: PluginState): string[] {
        const result: string[] = [];
        for (const [pluginId, pluginState] of this.pluginStates) {
            if (pluginState === state) {
                result.push(pluginId);
            }
        }
        return result;
    }

    /**
     * 批量加载插件
     */
    async loadPlugins(pluginIds: string[]): Promise<void> {
        try {
            // 根据依赖关系排序
            const sortedIds = this.options.enableDependencyResolution
                ? this.topologicalSort(pluginIds)
                : pluginIds;

            // 并发加载（受限于最大并发数）
            const chunks = this.chunkArray(sortedIds, this.options.maxConcurrentLoads!);

            for (const chunk of chunks) {
                await Promise.all(chunk.map(id => this.loadPlugin(id)));
            }

            this.logger.debug('批量加载插件完成', {
                totalPlugins: pluginIds.length,
                loadedPlugins: sortedIds.length
            });
        } catch (error) {
            this.logger.error('批量加载插件失败', error);
            throw createError(ERROR_CODES.BATCH_PLUGIN_LOAD_FAILED, `批量加载插件失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 批量激活插件
     */
    async activatePlugins(pluginIds: string[]): Promise<void> {
        try {
            // 根据依赖关系排序
            const sortedIds = this.options.enableDependencyResolution
                ? this.topologicalSort(pluginIds)
                : pluginIds;

            // 并发激活（受限于最大并发数）
            const chunks = this.chunkArray(sortedIds, this.options.maxConcurrentLoads!);

            for (const chunk of chunks) {
                await Promise.all(chunk.map(id => this.activatePlugin(id)));
            }

            this.logger.debug('批量激活插件完成', {
                totalPlugins: pluginIds.length,
                activatedPlugins: sortedIds.length
            });
        } catch (error) {
            this.logger.error('批量激活插件失败', error);
            throw createError(ERROR_CODES.BATCH_PLUGIN_ACTIVATION_FAILED, `批量激活插件失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 注册生命周期钩子
     */
    onLifecycleHook(hook: PluginLifecycleHook, callback: Function): void {
        if (!this.lifecycleHooks.has(hook)) {
            this.lifecycleHooks.set(hook, new Set());
        }
        this.lifecycleHooks.get(hook)!.add(callback);
    }

    /**
     * 移除生命周期钩子
     */
    offLifecycleHook(hook: PluginLifecycleHook, callback: Function): boolean {
        const hooks = this.lifecycleHooks.get(hook);
        return hooks ? hooks.delete(callback) : false;
    }

    /**
     * 销毁插件管理器
     */
    async destroy(): Promise<void> {
        try {
            // 触发销毁前钩子
            await this.executeLifecycleHook('beforeDestroy');

            // 停用所有活跃插件
            const activePlugins = this.getPluginsByState('active');
            for (const pluginId of activePlugins) {
                try {
                    await this.deactivatePlugin(pluginId);
                } catch (error) {
                    this.logger.error('停用插件失败', error, { pluginId });
                }
            }

            // 卸载所有已加载插件
            const loadedPlugins = this.getPluginsByState('loaded');
            for (const pluginId of loadedPlugins) {
                try {
                    await this.unloadPlugin(pluginId);
                } catch (error) {
                    this.logger.error('卸载插件失败', error, { pluginId });
                }
            }

            // 清理内部状态
            this.plugins.clear();
            this.pluginStates.clear();
            this.lifecycleHooks.clear();
            this.dependencyGraph.clear();
            this.isInitialized = false;

            // 触发销毁后钩子
            await this.executeLifecycleHook('afterDestroy');

            this.logger.info('插件管理器销毁完成');
        } catch (error) {
            this.logger.error('插件管理器销毁失败', error);
            throw createError(ERROR_CODES.PLUGIN_MANAGER_DESTROY_FAILED, `插件管理器销毁失败: ${(error as Error).message}`, error);
        }
    }

    /**
     * 验证插件ID
     */
    private validatePluginId(pluginId: string): void {
        if (!pluginId || typeof pluginId !== 'string') {
            throw createError(ERROR_CODES.INVALID_PLUGIN_ID, '插件ID必须是非空字符串');
        }
    }

    /**
     * 验证插件元数据
     */
    private validatePluginMetadata(metadata: PluginMetadata): void {
        if (!metadata.name || !metadata.version) {
            throw createError(ERROR_CODES.INVALID_PLUGIN_METADATA, '插件元数据必须包含名称和版本');
        }
    }

    /**
     * 获取插件实例
     */
    private getPluginInstance(pluginId: string): PluginInstance {
        const pluginInstance = this.plugins.get(pluginId);
        if (!pluginInstance) {
            throw createError(ERROR_CODES.PLUGIN_NOT_FOUND, `插件未找到: ${pluginId}`);
        }
        return pluginInstance;
    }

    /**
     * 解析依赖关系
     */
    private async resolveDependencies(pluginId: string, dependencies: PluginDependency[]): Promise<void> {
        for (const dep of dependencies) {
            if (!this.plugins.has(dep.name)) {
                if (dep.optional) {
                    this.logger.warn('可选依赖插件未找到', { pluginId, dependencyId: dep.name });
                    continue;
                }
                throw createError(ERROR_CODES.DEPENDENCY_NOT_FOUND, `依赖插件未找到: ${dep.name}`);
            }

            // 检查版本兼容性
            if (dep.version) {
                const depPlugin = this.plugins.get(dep.name)!;
                if (!this.isVersionCompatible(depPlugin.metadata.version, dep.version)) {
                    throw createError(ERROR_CODES.DEPENDENCY_VERSION_MISMATCH,
                        `依赖插件版本不兼容: ${dep.name} 需要 ${dep.version}，实际 ${depPlugin.metadata.version}`);
                }
            }
        }
    }

    /**
     * 加载依赖插件
     */
    private async loadDependencies(pluginId: string): Promise<void> {
        const pluginInstance = this.getPluginInstance(pluginId);
        const dependencies = pluginInstance.metadata.dependencies || [];

        for (const dep of dependencies) {
            if (this.plugins.has(dep.name)) {
                const depState = this.pluginStates.get(dep.name);
                if (depState === 'registered') {
                    await this.loadPlugin(dep.name);
                }
            }
        }
    }

    /**
     * 更新依赖图
     */
    private updateDependencyGraph(pluginId: string, dependencies: PluginDependency[]): void {
        const deps = new Set<string>();
        for (const dep of dependencies) {
            if (!dep.optional) {
                deps.add(dep.name);
            }
        }
        this.dependencyGraph.set(pluginId, deps);
    }

    /**
     * 获取依赖者
     */
    private getDependents(pluginId: string): string[] {
        const dependents: string[] = [];
        for (const [id, deps] of this.dependencyGraph) {
            if (deps.has(pluginId)) {
                dependents.push(id);
            }
        }
        return dependents;
    }

    /**
     * 拓扑排序
     */
    private topologicalSort(pluginIds: string[]): string[] {
        const visited = new Set<string>();
        const visiting = new Set<string>();
        const result: string[] = [];

        const visit = (pluginId: string) => {
            if (visiting.has(pluginId)) {
                throw createError(ERROR_CODES.CIRCULAR_DEPENDENCY, `检测到循环依赖: ${pluginId}`);
            }
            if (visited.has(pluginId)) {
                return;
            }

            visiting.add(pluginId);
            const deps = this.dependencyGraph.get(pluginId) || new Set();
            for (const dep of deps) {
                if (pluginIds.includes(dep)) {
                    visit(dep);
                }
            }
            visiting.delete(pluginId);
            visited.add(pluginId);
            result.push(pluginId);
        };

        for (const pluginId of pluginIds) {
            if (!visited.has(pluginId)) {
                visit(pluginId);
            }
        }

        return result;
    }

    /**
     * 数组分块
     */
    private chunkArray<T>(array: T[], chunkSize: number): T[][] {
        const chunks: T[][] = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    /**
     * 版本兼容性检查
     */
    private isVersionCompatible(actual: string, required: string): boolean {
        // 简单的版本兼容性检查（实际应用中应使用更复杂的语义版本比较）
        return actual >= required;
    }

    /**
     * 设置内部钩子
     */
    private setupInternalHooks(): void {
        // 可以在这里设置一些内部的生命周期钩子
    }

    /**
     * 执行生命周期钩子
     */
    private async executeLifecycleHook(hook: PluginLifecycleHook, data?: any): Promise<void> {
        const hooks = this.lifecycleHooks.get(hook);
        if (!hooks || hooks.size === 0) {
            return;
        }

        const promises = Array.from(hooks).map(callback => {
            try {
                return Promise.resolve(callback(data));
            } catch (error) {
                this.logger.error('生命周期钩子执行失败', error, { hook });
                return Promise.resolve();
            }
        });

        await Promise.allSettled(promises);
    }
}

/**
 * 插件实例接口
 */
interface PluginInstance {
    id: string;
    plugin: BasePlugin;
    metadata: PluginMetadata;
    config: PluginConfig;
    state: PluginState;
    loadedAt: number | null;
    activatedAt: number | null;
}