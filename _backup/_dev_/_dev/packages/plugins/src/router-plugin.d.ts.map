{"version": 3, "file": "router-plugin.d.ts", "sourceRoot": "", "sources": ["router-plugin.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAEH,KAAK,YAAY,EACpB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAa3C,UAAU,eAAe;IACrB,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;IAC1C,WAAW,IAAI,GAAG,CAAC;CACtB;AAED,UAAU,iBAAiB;IACvB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,KAAK,CAAC,EAAE,GAAG,CAAC;IACZ,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,WAAW,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,UAAU,WAAW;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,CAAC,EAAE,GAAG,CAAC;IAChB,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC3B,QAAQ,CAAC,EAAE,WAAW,EAAE,CAAC;CAC5B;AAED,UAAU,UAAU;IAChB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,OAAO,GAAG;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC,GAAG,OAAO,GAAG;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;CACjL;AAED;;GAEG;AACH,UAAU,kBAAmB,SAAQ,YAAY;IAC7C,WAAW;IACX,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,WAAW;IACX,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW;IACX,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,eAAe;IACf,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,aAAa;IACb,UAAU,CAAC,EAAE;QACT,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,QAAQ,CAAC,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,eAAe;IACf,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,aAAa;IACb,SAAS,CAAC,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,UAAU,WAAW;IACjB,WAAW;IACX,EAAE,EAAE,MAAM,CAAC;IACX,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW;IACX,MAAM,EAAE,WAAW,CAAC;IACpB,WAAW;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa;IACb,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,WAAW;IACX,WAAW,EAAE,MAAM,CAAC;CACvB;AAED;;GAEG;AACH,UAAU,iBAAiB;IACvB,aAAa;IACb,EAAE,EAAE,MAAM,CAAC;IACX,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,EAAE,EAAE,MAAM,CAAC;IACX,WAAW;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW;IACX,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,WAAW;IACX,KAAK,CAAC,EAAE,GAAG,CAAC;CACf;AAED;;;GAGG;AACH,qBAAa,YAAa,SAAQ,UAAU;IACxC,WAAW;IACX,SAAS,CAAC,MAAM,CAAC,EAAE,eAAe,CAAC;IAEnC,aAAa;IACb,OAAO,CAAC,YAAY,CAA+B;IAEnD,YAAY;IACZ,OAAO,CAAC,MAAM,CAAkC;IAEhD,eAAe;IACf,OAAO,CAAC,YAAY,CAA6B;IAEjD,cAAc;IACd,OAAO,CAAC,YAAY,CAA4B;IAEhD,aAAa;IACb,OAAO,CAAC,MAAM,CAAoB;IAElC,aAAa;IACb,OAAO,CAAC,OAAO,CAA2B;IAE1C,WAAW;IACX,OAAO,CAAC,UAAU,CAA0B;IAE5C,aAAa;IACb,OAAO,CAAC,YAAY,CAAS;IAE7B,WAAW;IACX,OAAO,CAAC,eAAe,CAAkC;gBAE7C,MAAM,GAAE,kBAAuB;IA6B3C;;OAEG;cACa,SAAS,CAAC,MAAM,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IAsCjE;;OAEG;cACa,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;IAe5C;;OAEG;IACG,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;IA8C1E;;OAEG;IACG,eAAe,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAkCrD;;OAEG;IACG,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAE,iBAAsB,GAAG,OAAO,CAAC,IAAI,CAAC;IAkB5E;;OAEG;YACW,UAAU;IA6ExB;;OAEG;IACG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAE,iBAAsB,GAAG,OAAO,CAAC,IAAI,CAAC;IAI3E;;OAEG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAY3B;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAK9B;;OAEG;IACH,eAAe,IAAI,WAAW,GAAG,IAAI;IAIrC;;OAEG;IACH,cAAc,IAAI,MAAM;IAQxB;;OAEG;IACH,SAAS,IAAI,WAAW,EAAE;IAI1B;;OAEG;IACH,UAAU,IAAI,iBAAiB,EAAE;IAIjC;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,UAAU,GAAG,IAAI;IAKjC;;OAEG;IACH,WAAW,CAAC,KAAK,EAAE,UAAU,GAAG,MAAM,GAAG,IAAI;IAU7C;;OAEG;IACH,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IAU/B;;OAEG;IACH,YAAY,IAAI,MAAM;IAItB;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAQ1B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAQ5B;;OAEG;YACW,iBAAiB;IAiB/B;;OAEG;YACW,gBAAgB;IAkB9B;;OAEG;YACW,aAAa;IAyB3B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAU5B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAkB5B;;OAEG;IACH,OAAO,CAAC,UAAU;IAelB;;OAEG;IACM,QAAQ,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAc3C"}