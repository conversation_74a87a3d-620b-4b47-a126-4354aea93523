/**
 * @micro-core/plugin-auth - 权限管理插件
 * 提供统一的身份认证和权限管理功能
 */

export { AuthGuard } from './auth-guard';
export { AuthPlugin } from './auth-plugin';
export { PermissionChecker } from './permission-checker';
export { TokenManager } from './token-manager';

// 导出类型定义
export type {
    AuthConfig, AuthGuardConfig,
    AuthGuardContext, AuthToken, AuthUser, Permission,
    Role,
    TokenConfig
} from './types';

// 默认导出
export { AuthPlugin as default } from './auth-plugin';
