{"name": "@micro-core/plugin-auth", "version": "0.1.0", "description": "Micro-Core 权限管理插件", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build && tsc --emitDeclarationOnly", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "test:watch": "vitest", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "micro-core", "plugin", "auth", "authentication", "authorization"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-auth"}, "dependencies": {"@micro-core/core": "workspace:*"}, "devDependencies": {"typescript": "^5.3.3", "vite": "^7.0.6", "vitest": "^3.2.4"}, "peerDependencies": {"@micro-core/core": "^0.1.0"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}