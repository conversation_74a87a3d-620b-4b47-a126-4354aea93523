/**
 * Vue 3 Adapter Implementation
 * @description Vue 3 微前端适配器，基于新的 BaseAdapter 基础设施
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import {
  BaseAdapter
} from '@micro-core/shared/utils';
import { App, createApp } from 'vue';
import type {
  Vue3AdapterConfig,
  Vue3AppInstance
} from './types';
import {
  cleanupVue3Container,
  extractVue3Component,
  getVue3Version,
  isVue3App,
  isVue3VersionCompatible,
  validateVue3Config
} from './utils';

// 添加BaseAdapter导入

/**
 * Vue 3 适配器类
 * @description 实现 Vue 3 微前端应用的加载、挂载、卸载等生命周期管理
 */
export class Vue3Adapter extends BaseAdapter {
  private vueApp: App | null = null;
  private appInstance: Vue3AppInstance | null = null;
  private component: any = null;

  constructor(config: Vue3AdapterConfig) {
    super({
      name: (config as any).name || 'vue3-app',
      framework: 'vue3'
    });
  }

  /**
   * 检查是否能处理指定的应用配置
   * @param appConfig 应用配置
   * @returns 是否能处理
   */
  canHandle(appConfig: any): boolean {
    return isVue3App(appConfig);
  }

  /**
   * 加载 Vue 3 应用
   * @param config 应用配置
   */
  protected async doLoadApp(config: any): Promise<any> {
    // 验证配置
    validateVue3Config(config);

    // 准备组件
    this.component = await this.prepareComponent(config);

    // 创建应用实例
    this.appInstance = {
      name: config.name,
      app: null as any, // 将在挂载时创建
      config,
      lifecycleHooks: config.lifecycleHooks || {},
      status: 'loaded',
      createdAt: Date.now(),
      updatedAt: Date.now()
    } as Vue3AppInstance;

    return this.appInstance;
  }

  /**
   * 挂载 Vue 3 应用
   */
  protected async doMountApp(app: any): Promise<void> {
    if (!this.component) {
      throw new Error('组件未准备就绪');
    }

    // 准备容器
    app.container = this.prepareContainer();

    // 创建 Vue 3 应用
    this.vueApp = createApp(this.component, app.config.props || {});

    // 配置应用
    await this.configureApp(this.vueApp, app.config);

    // 挂载应用
    const instance = this.vueApp.mount(app.container);
    app.instance = instance;
    app.status = 'mounted';
    app.updatedAt = Date.now();
  }

  /**
   * 卸载 Vue 3 应用
   */
  protected async doUnmountApp(app: any): Promise<void> {
    if (this.vueApp) {
      this.vueApp.unmount();
      this.vueApp = null;
    }

    // 清理容器
    if (app.container) {
      cleanupVue3Container(app.container);
    }

    app.instance = undefined;
    app.container = undefined;
    app.status = 'unmounted';
    app.updatedAt = Date.now();
  }

  /**
   * 更新 Vue 3 应用属性
   */
  protected async doUpdateApp(app: any, props?: Record<string, any>): Promise<void> {
    if (!props) return;

    // 更新配置中的 props
    app.config.props = { ...app.config.props, ...props };

    // 如果应用已挂载，重新挂载以应用新属性
    if (app.status === 'mounted' && this.vueApp) {
      await this.doUnmountApp(app);
      await this.doMountApp(app);
    }

    app.updatedAt = Date.now();
  }

  /**
   * 获取应用实例
   * @returns 应用实例
   */
  getAppInstance(): Vue3AppInstance | null {
    return this.appInstance;
  }

  /**
   * 准备 Vue 3 组件
   * @param config 应用配置
   * @returns Vue 3 组件
   */
  private async prepareComponent(config: any): Promise<any> {
    if ((config as any).component?.rootComponent) {
      return (config as any).component.rootComponent;
    }

    if (config.entry) {
      // 从入口加载组件
      const module = await this.loadModule(config.entry);
      return extractVue3Component(module);
    }

    throw new Error('必须提供 component.rootComponent 或 entry');
  }

  /**
   * 加载模块
   * @param entry 入口地址
   * @returns 模块对象
   */
  private async loadModule(entry: string | any): Promise<any> {
    try {
      const entryUrl = typeof entry === 'string' ? entry : entry.url || entry.src;

      if (entryUrl.startsWith('http')) {
        // 远程模块
        return await this.loadRemoteModule(entryUrl);
      } else {
        // 本地模块
        return await import(entryUrl);
      }
    } catch (error) {
      throw new Error(`从 ${entry} 加载 Vue 3 模块失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 加载远程模块
   * @param url 远程模块地址
   * @returns 模块对象
   */
  private async loadRemoteModule(url: string): Promise<any> {
    // 实现远程 Vue 3 模块加载
    const response = await fetch(url);
    const code = await response.text();

    // 创建沙箱并执行模块
    const moduleFunction = new Function('exports', 'require', 'module', 'Vue', code);
    const module = { exports: {} };
    moduleFunction(module.exports, require, module, { createApp });

    return module.exports;
  }

  /**
   * 准备容器元素
   * @returns 容器元素
   */
  protected prepareContainer(): HTMLElement {
    // 创建容器元素
    const container = document.createElement('div');
    container.id = `vue3-app-${this.config.name}`;
    container.className = 'vue3-app-container';
    container.setAttribute('data-framework', 'vue3');

    // 检查 Vue 3 版本兼容性
    const vue3Config = (this.config as any).vue3;
    if (vue3Config?.vueVersion) {
      this.checkVersionCompatibility(vue3Config.vueVersion);
    }

    return container;
  }

  /**
   * 检查 Vue 3 版本兼容性
   * @param requiredVersion 要求的版本
   * @returns 是否兼容
   */
  private checkVersionCompatibility(requiredVersion?: string): boolean {
    if (!requiredVersion) return true;

    const currentVersion = getVue3Version();
    if (!currentVersion) {
      console.warn('无法检测 Vue 3 版本');
      return true; // 无法检测时假设兼容
    }

    const compatible = isVue3VersionCompatible(currentVersion, requiredVersion);
    if (!compatible) {
      console.warn(
        `Vue 3 版本不匹配。当前: ${currentVersion}, 要求: ${requiredVersion}`
      );
    }

    return compatible;
  }

  /**
   * 配置 Vue 3 应用
   * @param app Vue 3 应用实例
   * @param config 应用配置
   */
  private async configureApp(app: App, config: any): Promise<void> {
    // 配置错误处理
    app.config.errorHandler = (error, instance, info) => {
      const err = error instanceof Error ? error : new Error(String(error));
      const errorMessage = `Vue 3 应用错误: ${err.message}`;
      console.error(errorMessage, {
        error: err,
        instance,
        info,
        appName: config.name
      });
    };

    // 配置全局属性
    if (config.globalProperties) {
      Object.assign(app.config.globalProperties, config.globalProperties);
    }

    // 注册全局组件
    if (config.components) {
      Object.entries(config.components).forEach(([name, component]) => {
        app.component(name, component as any);
      });
    }

    // 安装插件
    if (config.plugins) {
      config.plugins.forEach((plugin: any) => {
        if (typeof plugin === 'function') {
          app.use(plugin);
        } else if (plugin.plugin) {
          app.use(plugin.plugin, plugin.options);
        }
      });
    }
  }
}

/**
 * 创建 Vue 3 适配器实例
 * @param config 应用配置
 * @returns Vue 3 适配器实例
 */
export function createVue3Adapter(config: Vue3AdapterConfig): Vue3Adapter {
  return new Vue3Adapter(config);
}

/**
 * Vue 3 适配器工厂函数
 * @description 用于适配器注册表的工厂函数
 */
export const Vue3AdapterFactory = {
  type: 'vue3',
  create: createVue3Adapter,
  canHandle: isVue3App,
  metadata: {
    name: 'Vue 3 Adapter',
    version: '2.0.0',
    description: 'Vue 3 微前端适配器',
    supportedVersions: ['3.0+', '3.x'],
    author: 'Echo <<EMAIL>>'
  }
};

export default Vue3Adapter;
