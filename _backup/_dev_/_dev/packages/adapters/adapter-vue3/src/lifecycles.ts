/**
 * Vue 3 生命周期管理
 */

import type { App, Component } from 'vue';
import { createApp } from 'vue';
import type { Vue3AdapterOptions } from './index';

export interface Vue3AppConfig {
    /** 根组件 */
    rootComponent?: Component;
    /** 应用配置 */
    appOptions?: any;
    /** 路由配置 */
    router?: any;
    /** 状态管理 */
    store?: any;
    /** 插件列表 */
    plugins?: Array<{
        plugin: any;
        options?: any;
    }>;
}

export class Vue3Lifecycles {
    private appName: string;
    private appConfig: Vue3AppConfig;
    private options: Vue3AdapterOptions;
    private vueApp: App | null = null;
    private container: HTMLElement | null = null;

    constructor(appName: string, appConfig: Vue3AppConfig, options: Vue3AdapterOptions) {
        this.appName = appName;
        this.appConfig = appConfig;
        this.options = options;
    }

    /**
     * 获取生命周期函数
     */
    getLifecycles() {
        return {
            bootstrap: this.bootstrap.bind(this),
            mount: this.mount.bind(this),
            unmount: this.unmount.bind(this),
            update: this.update.bind(this)
        };
    }

    /**
     * 启动阶段
     */
    async bootstrap(props: any): Promise<void> {
        console.log(`[Vue3Lifecycles:${this.appName}] 启动阶段`, props);

        // 创建 Vue 应用实例
        if (!this.vueApp && this.appConfig.rootComponent) {
            this.vueApp = createApp(this.appConfig.rootComponent, props);

            // 配置全局属性
            if (this.options.globalProperties) {
                Object.assign(this.vueApp.config.globalProperties, this.options.globalProperties);
            }

            // 配置错误处理
            if (this.options.errorHandler) {
                this.vueApp.config.errorHandler = this.options.errorHandler;
            }

            // 安装插件
            if (this.appConfig.plugins) {
                this.appConfig.plugins.forEach(({ plugin, options }) => {
                    this.vueApp!.use(plugin, options);
                });
            }

            // 安装路由
            if (this.appConfig.router) {
                this.vueApp.use(this.appConfig.router);
            }

            // 安装状态管理
            if (this.appConfig.store) {
                this.vueApp.use(this.appConfig.store);
            }
        }
    }

    /**
     * 挂载阶段
     */
    async mount(props: any): Promise<void> {
        console.log(`[Vue3Lifecycles:${this.appName}] 挂载阶段`, props);

        if (!this.vueApp) {
            await this.bootstrap(props);
        }

        // 获取容器元素
        this.container = this.getContainer(props);

        if (this.container && this.vueApp) {
            // 挂载 Vue 应用
            this.vueApp.mount(this.container);
            console.log(`[Vue3Lifecycles:${this.appName}] Vue 3 应用已挂载`);
        } else {
            throw new Error(`[Vue3Lifecycles:${this.appName}] 无法找到挂载容器或 Vue 应用未创建`);
        }
    }

    /**
     * 卸载阶段
     */
    async unmount(props: any): Promise<void> {
        console.log(`[Vue3Lifecycles:${this.appName}] 卸载阶段`, props);

        if (this.vueApp) {
            // 卸载 Vue 应用
            this.vueApp.unmount();
            console.log(`[Vue3Lifecycles:${this.appName}] Vue 3 应用已卸载`);
        }

        // 清理容器
        if (this.container) {
            this.container.innerHTML = '';
            this.container = null;
        }
    }

    /**
     * 更新阶段
     */
    async update(props: any): Promise<void> {
        console.log(`[Vue3Lifecycles:${this.appName}] 更新阶段`, props);

        // Vue 3 应用的更新通常通过响应式数据自动处理
        // 这里可以处理一些特殊的更新逻辑
        if (this.vueApp && props) {
            // 更新全局属性
            Object.assign(this.vueApp.config.globalProperties, props);
        }
    }

    /**
     * 获取挂载容器
     */
    private getContainer(props: any): HTMLElement {
        const { container } = props;

        if (typeof container === 'string') {
            const element = document.querySelector(container);
            if (!element) {
                throw new Error(`[Vue3Lifecycles:${this.appName}] 无法找到容器: ${container}`);
            }
            return element as HTMLElement;
        }

        if (container instanceof HTMLElement) {
            return container;
        }

        // 默认容器
        const defaultContainer = document.getElementById(`micro-app-${this.appName}`) ||
            document.getElementById('root') ||
            document.body;

        return defaultContainer as HTMLElement;
    }

    /**
     * 销毁生命周期
     */
    destroy(): void {
        if (this.vueApp) {
            this.vueApp.unmount();
            this.vueApp = null;
        }

        if (this.container) {
            this.container.innerHTML = '';
            this.container = null;
        }

        console.log(`[Vue3Lifecycles:${this.appName}] 生命周期已销毁`);
    }

    /**
     * 获取 Vue 应用实例
     */
    getVueApp(): App | null {
        return this.vueApp;
    }
}