/**
 * @fileoverview Vue 3 Adapter Types
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import type { App, Component, ComponentPublicInstance, VNode } from 'vue';
import type {
  BaseAdapter,
  BaseAdapterConfig,
  BaseLifecycleHooks,
  BaseSandboxConfig,
  BaseAppInstance,
  BaseErrorInfo
} from '@micro-core/types';

/**
 * Vue 3 适配器配置接口
 */
export interface Vue3AdapterConfig extends BaseAdapterConfig {
  /** Vue 3 应用实例配置 */
  app?: {
    /** 全局属性 */
    globalProperties?: Record<string, any>;
    /** 全局组件 */
    components?: Record<string, Component>;
    /** 全局指令 */
    directives?: Record<string, any>;
    /** 插件列表 */
    plugins?: Array<{
      plugin: any;
      options?: any;
    }>;
    /** 混入 */
    mixins?: any[];
    /** 提供者 */
    provide?: Record<string | symbol, any>;
  };

  /** 组件配置 */
  component?: {
    /** 根组件 */
    rootComponent?: Component;
    /** 组件属性 */
    props?: Record<string, any>;
    /** 组件事件 */
    emits?: string[] | Record<string, any>;
    /** 插槽 */
    slots?: Record<string, any>;
  };

  /** 渲染配置 */
  render?: {
    /** 是否启用 SSR */
    ssr?: boolean;
    /** 水合配置 */
    hydration?: {
      /** 是否启用水合 */
      enabled?: boolean;
      /** 水合选项 */
      options?: any;
    };
  };

  /** 开发工具配置 */
  devtools?: {
    /** 是否启用 Vue DevTools */
    enabled?: boolean;
    /** DevTools 配置 */
    config?: any;
  };
}

/**
 * Vue 3 生命周期钩子接口
 */
export interface Vue3LifecycleHooks extends BaseLifecycleHooks {
  /** Vue 3 应用创建前 */
  beforeAppCreate?: (config: Vue3AdapterConfig) => void | Promise<void>;
  
  /** Vue 3 应用创建后 */
  afterAppCreate?: (app: App, config: Vue3AdapterConfig) => void | Promise<void>;
  
  /** Vue 3 应用挂载前 */
  beforeAppMount?: (app: App, container: Element) => void | Promise<void>;
  
  /** Vue 3 应用挂载后 */
  afterAppMount?: (app: App, instance: ComponentPublicInstance) => void | Promise<void>;
  
  /** Vue 3 应用卸载前 */
  beforeAppUnmount?: (app: App, instance: ComponentPublicInstance) => void | Promise<void>;
  
  /** Vue 3 应用卸载后 */
  afterAppUnmount?: (app: App) => void | Promise<void>;
  
  /** Vue 3 应用更新前 */
  beforeAppUpdate?: (app: App, newConfig: Vue3AdapterConfig) => void | Promise<void>;
  
  /** Vue 3 应用更新后 */
  afterAppUpdate?: (app: App, oldConfig: Vue3AdapterConfig) => void | Promise<void>;
}

/**
 * Vue 3 组件选项接口
 */
export interface Vue3ComponentOptions {
  /** 组件名称 */
  name?: string;
  /** 组件属性 */
  props?: Record<string, any>;
  /** 组件事件 */
  emits?: string[] | Record<string, any>;
  /** 组件数据 */
  data?: () => Record<string, any>;
  /** 计算属性 */
  computed?: Record<string, any>;
  /** 方法 */
  methods?: Record<string, Function>;
  /** 监听器 */
  watch?: Record<string, any>;
  /** 生命周期钩子 */
  beforeCreate?: () => void;
  created?: () => void;
  beforeMount?: () => void;
  mounted?: () => void;
  beforeUpdate?: () => void;
  updated?: () => void;
  beforeUnmount?: () => void;
  unmounted?: () => void;
  /** 渲染函数 */
  render?: () => VNode;
  /** 模板 */
  template?: string;
  /** 组件 */
  components?: Record<string, Component>;
  /** 指令 */
  directives?: Record<string, any>;
  /** 混入 */
  mixins?: any[];
  /** 继承 */
  extends?: any;
  /** 提供者 */
  provide?: Record<string | symbol, any> | (() => Record<string | symbol, any>);
  /** 注入 */
  inject?: string[] | Record<string, any>;
}

/**
 * Vue 3 错误信息接口
 */
export interface Vue3ErrorInfo extends BaseErrorInfo {
  /** Vue 3 应用实例 */
  app?: App;
  /** Vue 3 组件实例 */
  instance?: ComponentPublicInstance;
  /** Vue 3 虚拟节点 */
  vnode?: VNode;
  /** Vue 3 错误类型 */
  type?: 'setup' | 'render' | 'watch' | 'lifecycle' | 'handler' | 'scheduler';
}

/**
 * Vue 3 沙箱配置接口
 */
export interface Vue3SandboxConfig extends BaseSandboxConfig {
  /** Vue 3 特定配置 */
  vue3?: {
    /** 是否隔离 Vue 3 全局配置 */
    isolateGlobalConfig?: boolean;
    /** 是否隔离 Vue 3 全局属性 */
    isolateGlobalProperties?: boolean;
    /** 是否隔离 Vue 3 组件 */
    isolateComponents?: boolean;
    /** 是否隔离 Vue 3 指令 */
    isolateDirectives?: boolean;
    /** 是否隔离 Vue 3 插件 */
    isolatePlugins?: boolean;
  };
}

/**
 * Vue 3 应用实例接口
 */
export interface Vue3AppInstance extends BaseAppInstance {
  /** Vue 3 应用实例 */
  app: App;
  /** Vue 3 根组件实例 */
  instance?: ComponentPublicInstance;
  /** Vue 3 适配器配置 */
  config: Vue3AdapterConfig;
  /** Vue 3 生命周期钩子 */
  lifecycleHooks: Vue3LifecycleHooks;
}

/**
 * Vue 3 适配器接口
 */
export interface IVue3Adapter extends BaseAdapter<Vue3AdapterConfig, Vue3AppInstance> {
  /** 创建 Vue 3 应用 */
  createApp(config: Vue3AdapterConfig): Promise<App>;
  
  /** 配置 Vue 3 应用 */
  configureApp(app: App, config: Vue3AdapterConfig): Promise<void>;
  
  /** 挂载 Vue 3 应用 */
  mountApp(app: App, container: Element): Promise<ComponentPublicInstance>;
  
  /** 卸载 Vue 3 应用 */
  unmountApp(app: App): Promise<void>;
  
  /** 获取 Vue 3 应用实例 */
  getAppInstance(appId: string): Vue3AppInstance | undefined;
  
  /** 更新 Vue 3 应用配置 */
  updateAppConfig(appId: string, config: Partial<Vue3AdapterConfig>): Promise<void>;
}

/**
 * Vue 3 插件选项接口
 */
export interface Vue3PluginOptions {
  /** 插件名称 */
  name?: string;
  /** 插件版本 */
  version?: string;
  /** 插件配置 */
  config?: Record<string, any>;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * Vue 3 上下文接口
 */
export interface Vue3Context {
  /** Vue 3 应用实例 */
  app: App;
  /** Vue 3 组件实例 */
  instance?: ComponentPublicInstance;
  /** Vue 3 适配器实例 */
  adapter: IVue3Adapter;
  /** Vue 3 配置 */
  config: Vue3AdapterConfig;
  /** 容器元素 */
  container?: Element;
}

/**
 * Vue 3 事件类型
 */
export type Vue3EventType = 
  | 'app:created'
  | 'app:mounted'
  | 'app:unmounted'
  | 'app:updated'
  | 'app:error'
  | 'component:created'
  | 'component:mounted'
  | 'component:unmounted'
  | 'component:updated'
  | 'component:error';

/**
 * Vue 3 事件数据接口
 */
export interface Vue3EventData {
  /** 事件类型 */
  type: Vue3EventType;
  /** 应用 ID */
  appId: string;
  /** Vue 3 应用实例 */
  app?: App;
  /** Vue 3 组件实例 */
  instance?: ComponentPublicInstance;
  /** 错误信息 */
  error?: Vue3ErrorInfo;
  /** 额外数据 */
  data?: any;
  /** 时间戳 */
  timestamp: number;
}

/**
 * Vue 3 路由配置接口
 */
export interface Vue3RouteConfig {
  /** 路由路径 */
  path: string;
  /** 路由组件 */
  component: Component;
  /** 路由名称 */
  name?: string;
  /** 路由元信息 */
  meta?: Record<string, any>;
  /** 子路由 */
  children?: Vue3RouteConfig[];
}

/**
 * Vue 3 状态管理配置接口
 */
export interface Vue3StoreConfig {
  /** 状态 */
  state?: Record<string, any>;
  /** 获取器 */
  getters?: Record<string, Function>;
  /** 变更 */
  mutations?: Record<string, Function>;
  /** 动作 */
  actions?: Record<string, Function>;
  /** 模块 */
  modules?: Record<string, any>;
}

/**
 * Vue 3 工具函数类型
 */
export type Vue3Utils = {
  /** 创建 Vue 3 适配器 */
  createVue3Adapter: (config?: Partial<Vue3AdapterConfig>) => IVue3Adapter;
  
  /** 检测是否为 Vue 3 应用 */
  isVue3App: (app: any) => app is App;
  
  /** 验证 Vue 3 配置 */
  validateVue3Config: (config: Vue3AdapterConfig) => boolean;
  
  /** 格式化 Vue 3 错误 */
  formatVue3Error: (error: any, context?: Vue3Context) => Vue3ErrorInfo;
  
  /** 提取 Vue 3 组件 */
  extractVue3Component: (app: App) => Component | null;
  
  /** 创建 Vue 3 容器 */
  createVue3Container: (selector?: string) => Element;
  
  /** 清理 Vue 3 容器 */
  cleanupVue3Container: (container: Element) => void;
};
