/**
 * Vue3 适配器工具函数
 * 使用 shared 包的通用工具函数
 */

import {
  cleanupContainer,
  createEnhancedContainer,
  createVue3ErrorInfo,
  formatVue3Error,
  mergeConfigs
} from '@micro-core/shared/utils';
import type { App } from 'vue';
import type { Vue3AdapterConfig, Vue3ComponentType } from './types';

// Vue3 适配器创建函数
export function createVue3Adapter(
  config?: Partial<Vue3AdapterConfig>,
  dependencies?: {
    lifecycleManager?: any;
    sandboxManager?: any;
    communicationManager?: any;
    errorHandler?: any;
  }
): any {
  if (!dependencies || !dependencies.lifecycleManager || !dependencies.sandboxManager ||
    !dependencies.communicationManager || !dependencies.errorHandler) {
    throw new Error('All dependencies are required to create Vue3Adapter');
  }

  // 动态导入 Vue3Adapter 类以避免循环依赖
  const { Vue3Adapter } = require('./vue3-adapter');
  return new Vue3Adapter(config, dependencies);
}

// Vue3 应用检测函数
export function isVue3App(config: any): boolean {
  if (!config || typeof config !== 'object') return false;

  return !!(
    config.vue3 ||
    config.vueOptions ||
    (config.component && isVue3Component(config.component)) ||
    (config.entry && isVue3Entry(config.entry))
  );
}

// Vue3 入口文件检测
export function isVue3Entry(entry: string): boolean {
  if (!entry || typeof entry !== 'string') return false;

  const vue3Patterns = [
    /vue3?-app\./,
    /\.vue$/,
    /main-vue3?\./,
    /app\.vue3?\./
  ];

  return vue3Patterns.some(pattern => pattern.test(entry));
}

// 获取 Vue3 版本
export function getVue3Version(): string | null {
  try {
    // 尝试从 Vue 包获取版本
    const vue = require('vue');
    return vue.version || null;
  } catch {
    // 尝试从 window.Vue 获取版本
    if (typeof window !== 'undefined' && (window as any).Vue) {
      return (window as any).Vue.version || null;
    }
    return null;
  }
}

// Vue3 版本兼容性检查
export function isVue3VersionCompatible(currentVersion: string, minVersion: string = '3.0.0'): boolean {
  if (!currentVersion || !minVersion) return false;

  try {
    const parseVersion = (version: string) => {
      const parts = version.split('.').map(Number);
      return parts[0] * 10000 + (parts[1] || 0) * 100 + (parts[2] || 0);
    };

    return parseVersion(currentVersion) >= parseVersion(minVersion);
  } catch {
    return false;
  }
}

// Vue3 配置验证
export function validateVue3Config(config: any): void {
  if (!config.name || typeof config.name !== 'string') {
    throw new Error('Vue 3 app name is required');
  }

  if (!config.component && !config.vueOptions && !config.entry) {
    throw new Error('Either component, vueOptions, or entry must be specified');
  }
}

// 创建默认 Vue3 配置
export function createDefaultVue3Config(overrides: any = {}): any {
  const defaultConfig = {
    name: 'vue3-app',
    framework: 'vue3',
    sandbox: {
      type: 'proxy',
      isolateGlobals: true,
      isolateStyles: true
    },
    vue3: {
      vueVersion: '3.0',
      enableDevTools: process.env.NODE_ENV === 'development'
    }
  };

  return mergeConfigs(defaultConfig, overrides);
}

// Vue3 组件提取函数 - 拆分为多个小函数提高可测试性

function checkPreferredVue3Component(module: any, preferredName?: string): Vue3ComponentType | null {
  if (!preferredName || !module[preferredName]) return null;

  const component = module[preferredName];
  return isVue3Component(component) ? component : null;
}

function checkDefaultVue3Component(module: any): Vue3ComponentType | null {
  if (!module.default) return null;

  const component = module.default;
  return isVue3Component(component) ? component : null;
}

function getNamedVue3Components(module: any): Record<string, Vue3ComponentType> {
  const components: Record<string, Vue3ComponentType> = {};

  for (const [key, value] of Object.entries(module)) {
    if (key !== 'default' && isVue3Component(value)) {
      components[key] = value as Vue3ComponentType;
    }
  }

  return components;
}

function selectBestVue3Component(components: Record<string, Vue3ComponentType>): Vue3ComponentType {
  const priorityNames = ['App', 'Component', 'Main', 'Index'];

  for (const name of priorityNames) {
    if (components[name]) {
      return components[name];
    }
  }

  // 返回第一个找到的组件
  const componentNames = Object.keys(components);
  if (componentNames.length > 0) {
    return components[componentNames[0]];
  }

  throw new Error('No Vue 3 component found in module');
}

export function extractVue3Component(
  module: any,
  options: {
    preferredName?: string;
    allowMultiple?: boolean;
  } = {}
): Vue3ComponentType | Record<string, Vue3ComponentType> {
  if (!module) {
    throw new Error('Module is required for component extraction');
  }

  // 如果直接传入组件选项对象
  if (isVue3Component(module)) {
    return module;
  }

  // 检查首选名称的组件
  const preferredComponent = checkPreferredVue3Component(module, options.preferredName);
  if (preferredComponent) {
    return preferredComponent;
  }

  // 检查默认导出
  const defaultComponent = checkDefaultVue3Component(module);
  if (defaultComponent) {
    return defaultComponent;
  }

  // 获取所有命名导出的组件
  const namedComponents = getNamedVue3Components(module);

  if (options.allowMultiple && Object.keys(namedComponents).length > 1) {
    return namedComponents;
  }

  if (Object.keys(namedComponents).length === 0) {
    throw new Error('No Vue 3 component found in module');
  }

  return selectBestVue3Component(namedComponents);
}

// Vue3 组件检测函数
export function isVue3Component(component: any): boolean {
  if (!component || typeof component !== 'object') return false;

  // 检查 Vue3 组件选项对象
  const hasVue3Options = !!(
    component.setup ||
    component.template ||
    component.render ||
    component.data ||
    component.computed ||
    component.methods ||
    component.props ||
    component.components ||
    component.emits ||
    component.expose
  );

  if (hasVue3Options) return true;

  // 检查 Vue3 函数式组件
  if (typeof component === 'function') {
    // 检查是否是 defineComponent 创建的组件
    if (component.__vccOpts || component.render) return true;

    // 检查函数参数，Vue3 函数式组件接受 props 和 context
    const funcStr = component.toString();
    if (funcStr.includes('props') && funcStr.includes('context')) return true;
  }

  // 检查 Vue3 应用实例
  if (component._component || component._context) return true;

  return false;
}

// Vue3 组件增强检测
export function isVue3ComponentEnhanced(component: any): boolean {
  if (isVue3Component(component)) return true;

  if (!component || typeof component !== 'object') return false;

  // 检查更多 Vue3 特有属性
  const hasVue3EnhancedOptions = !!(
    component.watch ||
    component.mixins ||
    component.directives ||
    component.provide ||
    component.inject ||
    component.beforeCreate ||
    component.created ||
    component.beforeMount ||
    component.mounted ||
    component.beforeUpdate ||
    component.updated ||
    component.beforeUnmount ||
    component.unmounted ||
    component.errorCaptured ||
    component.renderTracked ||
    component.renderTriggered
  );

  if (hasVue3EnhancedOptions) return true;

  // 检查 Composition API 相关
  if (typeof component === 'function') {
    const funcStr = component.toString();
    const compositionApiPatterns = [
      /\bref\b/,
      /\breactive\b/,
      /\bcomputed\b/,
      /\bwatch\b/,
      /\bonMounted\b/,
      /\bonUnmounted\b/,
      /\buseRouter\b/,
      /\buseStore\b/
    ];

    if (compositionApiPatterns.some(pattern => pattern.test(funcStr))) {
      return true;
    }
  }

  // 检查异步组件
  if (typeof component === 'function' && component.length === 0) {
    // 可能是异步组件工厂函数
    return true;
  }

  return false;
}

// Vue3 容器管理
export function createVue3Container(appName: string, parentElement?: Element): Element {
  return createEnhancedContainer(appName, 'vue3', parentElement, {
    className: 'vue3-app-container'
  });
}

export function cleanupVue3Container(appName: string): void {
  cleanupContainer(appName);
}

export function getVue3Container(appName: string): Element | null {
  return document.getElementById(`micro-app-${appName}`);
}

// Vue3 开发工具
export function isVue3DevToolsAvailable(): boolean {
  return typeof window !== 'undefined' && !!(window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__;
}

export function enableVue3DevTools(appName: string): void {
  if (isVue3DevToolsAvailable()) {
    console.log(`Vue 3 DevTools enabled for app: ${appName}`);
    // 可以在这里添加更多 DevTools 集成逻辑
  }
}

// Vue3 错误处理
export { createVue3ErrorInfo, formatVue3Error };

// Vue3 配置合并
export function mergeVue3Configs(base: any, override: any): any {
  return mergeConfigs(base, override);
}

// Vue3 微应用集成工具
export function createVue3MicroAppPlugin(context: any) {
  return {
    install(app: App) {
      // 注入微应用上下文
      app.provide('microAppContext', context);

      // 添加全局属性
      app.config.globalProperties.$microApp = context;

      // 添加全局方法
      app.config.globalProperties.$emitToParent = (event: string, data?: any) => {
        context.communication?.emitToParent?.(event, data);
      };

      app.config.globalProperties.$sendToApp = (targetApp: string, message: any) => {
        context.communication?.sendToApp?.(targetApp, message);
      };
    }
  };
}

// 导出工具函数集合
export const Vue3AdapterUtils = {
  // 组件相关
  extractVue3Component,
  isVue3Component,
  isVue3ComponentEnhanced,

  // 应用检测
  isVue3App,
  isVue3Entry,

  // 版本管理
  getVue3Version,
  isVue3VersionCompatible,

  // 配置管理
  validateVue3Config,
  createDefaultVue3Config,
  mergeVue3Configs,

  // 容器管理
  createVue3Container,
  cleanupVue3Container,
  getVue3Container,

  // 错误处理
  formatVue3Error,
  createVue3ErrorInfo,

  // 开发工具
  isVue3DevToolsAvailable,
  enableVue3DevTools,

  // 适配器创建
  createVue3Adapter,

  // 微应用集成
  createVue3MicroAppPlugin
};