{"extends": "../../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "composite": true, "jsx": "preserve", "jsxImportSource": "solid-js", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@micro-core/core": ["../../../packages/core/src"], "@micro-core/shared": ["../../../packages/shared"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}