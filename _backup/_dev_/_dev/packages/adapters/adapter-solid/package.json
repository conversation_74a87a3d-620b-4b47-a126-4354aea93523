{"name": "@micro-core/adapter-solid", "version": "0.1.0", "description": "Solid.js 框架适配器，用于将 Solid.js 应用集成到微前端架构中", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest"}, "keywords": ["micro-frontend", "solid", "solidjs", "adapter", "微前端"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"solid-js": "^1.8.0", "typescript": "^5.3.3", "vitest": "^3.2.4"}, "peerDependencies": {"solid-js": "^1.8.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters/adapter-solid"}, "publishConfig": {"access": "public"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}