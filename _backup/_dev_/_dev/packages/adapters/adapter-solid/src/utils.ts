/**
 * Solid 适配器工具函数
 * 使用 shared 包的通用工具函数实现
 */

import {
    cleanupContainer,
    createAdapterErrorInfo,
    createEnhancedContainer,
    formatAdapterError,
    isFunction,
    isObject,
    isString,
    mergeConfigs
} from '@micro-core/shared/utils';
import type {
    SolidAppInstance,
    SolidConfig,
    SolidDisposer
} from './types';

/**
 * 创建 Solid 适配器
 */
export function createSolidAdapter(config: SolidConfig): SolidAppInstance {
    const finalConfig = createDefaultSolidConfig(config);

    return {
        name: finalConfig.name,
        config: finalConfig,
        mount: async (element: HTMLElement) => {
            try {
                const container = createSolidContainer(finalConfig.name, element);
                const disposer = await mountSolidComponent(finalConfig, container);
                return { container, disposer };
            } catch (error) {
                throw formatSolidError(error, 'mount', finalConfig.name);
            }
        },
        unmount: async (instance: any) => {
            try {
                if (instance?.disposer) {
                    instance.disposer();
                }
                if (instance?.container) {
                    cleanupContainer(instance.container);
                }
            } catch (error) {
                throw formatSolidError(error, 'unmount', finalConfig.name);
            }
        }
    };
}

/**
 * 检查是否为 Solid 应用
 */
export function isSolidApp(app: any): boolean {
    if (!isObject(app)) return false;

    // 检查 Solid 组件特征
    if (app.render || app.Component) return true;

    // 检查 Solid 配置特征
    if (app.solidConfig) return true;

    // 检查 Solid JSX 特征
    if (app.$$typeof || app.type) return true;

    return false;
}

/**
 * 检查是否为 Solid 入口
 */
export function isSolidEntry(entry: any): boolean {
    if (!isObject(entry)) return false;

    // 检查是否有 Solid 组件导出
    if (entry.App || entry.Component) return true;

    // 检查是否有 Solid 配置导出
    if (entry.solidConfig) return true;

    // 检查默认导出是否为 Solid 组件
    if (entry.default && isSolidComponent(entry.default)) return true;

    return false;
}

/**
 * 获取 Solid 版本
 */
export function getSolidVersion(): string | null {
    try {
        // 尝试从全局对象获取
        if (typeof window !== 'undefined' && (window as any).solid) {
            return (window as any).solid.VERSION || null;
        }

        // 尝试从 Solid 包获取
        const solid = require('solid-js/package.json');
        return solid.version || null;
    } catch {
        return null;
    }
}

/**
 * 检查 Solid 版本兼容性
 */
export function isSolidVersionCompatible(version: string, minVersion = '1.0.0'): boolean {
    if (!version) return false;

    const parseVersion = (v: string) => {
        const parts = v.split('.').map(Number);
        return parts[0] * 10000 + (parts[1] || 0) * 100 + (parts[2] || 0);
    };

    return parseVersion(version) >= parseVersion(minVersion);
}

/**
 * 验证 Solid 配置
 */
export function validateSolidConfig(config: Partial<SolidConfig>): string[] {
    const errors: string[] = [];

    if (!config.name || !isString(config.name)) {
        errors.push('配置中缺少有效的应用名称');
    }

    if (!config.component) {
        errors.push('配置中缺少 Solid 组件');
    }

    if (config.solid?.dev !== undefined && typeof config.solid.dev !== 'boolean') {
        errors.push('solid.dev 必须是布尔值');
    }

    if (config.solid?.ssr !== undefined && typeof config.solid.ssr !== 'boolean') {
        errors.push('solid.ssr 必须是布尔值');
    }

    return errors;
}

/**
 * 创建默认 Solid 配置
 */
export function createDefaultSolidConfig(config: Partial<SolidConfig>): SolidConfig {
    const defaultConfig: SolidConfig = {
        name: '',
        component: null,
        props: {},
        solid: {
            dev: process.env.NODE_ENV === 'development',
            ssr: false,
            hydratable: false
        },
        container: {
            className: 'solid-app-container',
            style: {}
        },
        lifecycle: {}
    };

    return mergeConfigs(defaultConfig, config);
}

/**
 * 提取 Solid 组件
 * 重构为多个简单函数以提高可测试性
 */
export function extractSolidComponent(moduleExports: any, preferredName?: string): any {
    if (!isObject(moduleExports)) {
        throw new Error('模块导出必须是对象');
    }

    // 1. 检查首选组件
    const preferredComponent = checkPreferredSolidComponent(moduleExports, preferredName);
    if (preferredComponent) return preferredComponent;

    // 2. 检查默认导出
    const defaultComponent = checkDefaultSolidComponent(moduleExports);
    if (defaultComponent) return defaultComponent;

    // 3. 获取命名导出
    const namedComponents = getNamedSolidComponents(moduleExports);
    if (namedComponents.length > 0) {
        return selectBestSolidComponent(namedComponents);
    }

    throw new Error('未找到有效的 Solid 组件');
}

/**
 * 检查首选 Solid 组件
 */
function checkPreferredSolidComponent(moduleExports: any, preferredName?: string): any {
    if (!preferredName) return null;

    const component = moduleExports[preferredName];
    if (component && isSolidComponent(component)) {
        return component;
    }

    return null;
}

/**
 * 检查默认 Solid 组件
 */
function checkDefaultSolidComponent(moduleExports: any): any {
    const defaultExport = moduleExports.default;
    if (defaultExport && isSolidComponent(defaultExport)) {
        return defaultExport;
    }

    return null;
}

/**
 * 获取命名 Solid 组件
 */
function getNamedSolidComponents(moduleExports: any): any[] {
    const components: any[] = [];

    for (const [key, value] of Object.entries(moduleExports)) {
        if (key !== 'default' && isSolidComponent(value)) {
            components.push(value);
        }
    }

    return components;
}

/**
 * 选择最佳 Solid 组件
 */
function selectBestSolidComponent(components: any[]): any {
    // 优先选择函数组件
    const functionComponents = components.filter(comp => isFunction(comp));
    if (functionComponents.length > 0) return functionComponents[0];

    // 返回第一个组件
    return components[0];
}

/**
 * 检查是否为 Solid 组件
 */
export function isSolidComponent(component: any): boolean {
    if (!component) return false;

    // 检查函数组件
    if (isFunction(component)) return true;

    // 检查 JSX 元素
    if (isObject(component) && (component.$$typeof || component.type)) return true;

    return false;
}

/**
 * 检查是否为增强的 Solid 组件
 */
export function isSolidComponentEnhanced(component: any): boolean {
    if (!isSolidComponent(component)) return false;

    // 检查是否有微应用增强功能
    return !!(component.microAppConfig || component.microAppHooks);
}

/**
 * 创建 Solid 容器
 */
export function createSolidContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
    return createEnhancedContainer(appName, 'solid', parentElement, {
        className: 'solid-app-container',
        attributes: {
            'data-framework': 'solid',
            'data-app': appName
        }
    });
}

/**
 * 挂载 Solid 组件
 */
async function mountSolidComponent(config: SolidConfig, container: HTMLElement): Promise<SolidDisposer> {
    const { render } = await import('solid-js/web');
    const Component = config.component;

    if (!Component) {
        throw new Error('Solid 组件不能为空');
    }

    // 渲染组件
    const disposer = render(() => {
        if (isFunction(Component)) {
            return Component(config.props || {});
        } else {
            return Component;
        }
    }, container);

    return disposer;
}

/**
 * 设置 Solid 开发工具
 */
export function setupSolidDevTools(appName: string, disposer: SolidDisposer): void {
    if (typeof window === 'undefined') return;

    try {
        // 注册到全局 Solid 开发工具
        if (!(window as any).solid) {
            (window as any).solid = {};
        }

        (window as any).solid[appName] = {
            disposer,
            getComponent: () => disposer,
            dispose: () => disposer()
        };
    } catch (error) {
        console.warn(`设置 Solid 开发工具失败 (${appName}):`, error);
    }
}

/**
 * 格式化 Solid 错误
 */
export function formatSolidError(error: any, operation: string, appName: string): Error {
    return formatAdapterError(error, 'Solid', operation, appName);
}

/**
 * 创建 Solid 错误信息
 */
export function createSolidErrorInfo(error: any, context: any): any {
    return createAdapterErrorInfo(error, 'Solid', context);
}

/**
 * 合并 Solid 配置
 */
export function mergeSolidConfigs(base: Partial<SolidConfig>, override: Partial<SolidConfig>): SolidConfig {
    return mergeConfigs(base, override);
}

/**
 * Solid 微应用集成工具
 */
export class SolidMicroAppIntegration {
    private disposer: SolidDisposer | null = null;
    private container: HTMLElement | null = null;

    constructor(private config: SolidConfig) { }

    async mount(element: HTMLElement): Promise<void> {
        try {
            this.container = createSolidContainer(this.config.name, element);
            this.disposer = await mountSolidComponent(this.config, this.container);

            // 设置开发工具
            if (this.config.solid?.dev) {
                setupSolidDevTools(this.config.name, this.disposer);
            }

            // 触发生命周期钩子
            await this.config.lifecycle?.mounted?.(this.disposer);
        } catch (error) {
            throw formatSolidError(error, 'mount', this.config.name);
        }
    }

    async unmount(): Promise<void> {
        try {
            // 触发生命周期钩子
            await this.config.lifecycle?.beforeUnmount?.(this.disposer);

            // 销毁组件
            if (this.disposer) {
                this.disposer();
                this.disposer = null;
            }

            // 清理容器
            if (this.container) {
                cleanupContainer(this.container);
                this.container = null;
            }

            // 触发生命周期钩子
            await this.config.lifecycle?.unmounted?.();
        } catch (error) {
            throw formatSolidError(error, 'unmount', this.config.name);
        }
    }

    getDisposer(): SolidDisposer | null {
        return this.disposer;
    }

    getContainer(): HTMLElement | null {
        return this.container;
    }

    async updateComponent(component: any, props?: Record<string, any>): Promise<void> {
        if (!this.container) {
            throw new Error('应用未挂载，无法更新组件');
        }

        try {
            // 销毁旧组件
            if (this.disposer) {
                this.disposer();
            }

            // 更新配置
            this.config.component = component;
            if (props) {
                this.config.props = { ...this.config.props, ...props };
            }

            // 重新挂载组件
            this.disposer = await mountSolidComponent(this.config, this.container);

            // 触发生命周期钩子
            await this.config.lifecycle?.updated?.(this.disposer);
        } catch (error) {
            throw formatSolidError(error, 'update', this.config.name);
        }
    }
}