import type { LifecycleFn } from '@micro-core/core';
import { render } from 'solid-js/web';

/**
 * 创建 Solid.js 应用的标准生命周期函数
 */
export function createSolidLifecycles(AppComponent: any) {
    let dispose: (() => void) | undefined;

    const bootstrap: LifecycleFn = async (props) => {
        console.log('[Solid] 应用启动', props);
    };

    const mount: LifecycleFn = async (props) => {
        console.log('[Solid] 应用挂载', props);

        const { container } = props;
        const mountPoint = container.querySelector('#solid-app-root') || container;

        // 渲染 Solid.js 应用
        dispose = render(() => AppComponent(props), mountPoint);
    };

    const unmount: LifecycleFn = async (props) => {
        console.log('[Solid] 应用卸载', props);

        if (dispose) {
            dispose();
            dispose = undefined;
        }

        // 清理 DOM
        const { container } = props;
        if (container) {
            container.innerHTML = '';
        }
    };

    return {
        bootstrap,
        mount,
        unmount
    };
}