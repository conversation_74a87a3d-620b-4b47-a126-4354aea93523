/**
 * Angular 适配器工具函数
 * 使用 shared 包的通用工具函数实现
 */

import {
    cleanupContainer,
    createAdapterErrorInfo,
    createEnhancedContainer,
    formatAdapterError,
    isFunction,
    isObject,
    isString,
    mergeConfigs
} from '@micro-core/shared/utils';
import type {
    AngularAppInstance,
    AngularConfig,
    AngularModuleRef
} from './types';

/**
 * 创建 Angular 适配器
 */
export function createAngularAdapter(config: AngularConfig): AngularAppInstance {
    const finalConfig = createDefaultAngularConfig(config);

    return {
        name: finalConfig.name,
        config: finalConfig,
        mount: async (element: HTMLElement) => {
            try {
                const container = createAngularContainer(finalConfig.name, element);
                const moduleRef = await bootstrapAngularModule(finalConfig, container);
                return { container, moduleRef };
            } catch (error) {
                throw formatAngularError(error, 'mount', finalConfig.name);
            }
        },
        unmount: async (instance: any) => {
            try {
                if (instance?.moduleRef) {
                    instance.moduleRef.destroy();
                }
                if (instance?.container) {
                    cleanupContainer(instance.container);
                }
            } catch (error) {
                throw formatAngularError(error, 'unmount', finalConfig.name);
            }
        }
    };
}

/**
 * 检查是否为 Angular 应用
 */
export function isAngularApp(app: any): boolean {
    if (!isObject(app)) return false;

    // 检查 Angular 模块特征
    if (app.ngModule || app.moduleRef) return true;

    // 检查 Angular 组件特征
    if (app.component && isFunction(app.component)) return true;

    // 检查 Angular 平台特征
    if (app.platformBrowserDynamic || app.platformBrowser) return true;

    return false;
}

/**
 * 检查是否为 Angular 入口
 */
export function isAngularEntry(entry: any): boolean {
    if (!isObject(entry)) return false;

    // 检查是否有 Angular 模块导出
    if (entry.AppModule || entry.NgModule) return true;

    // 检查是否有 Angular 组件导出
    if (entry.AppComponent || entry.Component) return true;

    // 检查是否有 Angular 平台导出
    if (entry.platformBrowserDynamic) return true;

    return false;
}

/**
 * 获取 Angular 版本
 */
export function getAngularVersion(): string | null {
    try {
        // 尝试从全局对象获取
        if (typeof window !== 'undefined' && (window as any).ng) {
            return (window as any).ng.version?.full || null;
        }

        // 尝试从 Angular 核心包获取
        const ngCore = require('@angular/core');
        return ngCore.VERSION?.full || null;
    } catch {
        return null;
    }
}

/**
 * 检查 Angular 版本兼容性
 */
export function isAngularVersionCompatible(version: string, minVersion = '12.0.0'): boolean {
    if (!version) return false;

    const parseVersion = (v: string) => {
        const parts = v.split('.').map(Number);
        return parts[0] * 10000 + (parts[1] || 0) * 100 + (parts[2] || 0);
    };

    return parseVersion(version) >= parseVersion(minVersion);
}

/**
 * 验证 Angular 配置
 */
export function validateAngularConfig(config: Partial<AngularConfig>): string[] {
    const errors: string[] = [];

    if (!config.name || !isString(config.name)) {
        errors.push('配置中缺少有效的应用名称');
    }

    if (!config.entry) {
        errors.push('配置中缺少入口模块或组件');
    }

    if (config.angular?.enableProdMode !== undefined && typeof config.angular.enableProdMode !== 'boolean') {
        errors.push('angular.enableProdMode 必须是布尔值');
    }

    if (config.angular?.preserveWhitespaces !== undefined && typeof config.angular.preserveWhitespaces !== 'boolean') {
        errors.push('angular.preserveWhitespaces 必须是布尔值');
    }

    return errors;
}

/**
 * 创建默认 Angular 配置
 */
export function createDefaultAngularConfig(config: Partial<AngularConfig>): AngularConfig {
    const defaultConfig: AngularConfig = {
        name: '',
        entry: null,
        angular: {
            enableProdMode: false,
            preserveWhitespaces: false,
            enableIvy: true,
            strictTemplates: false,
            enableDevTools: process.env.NODE_ENV === 'development'
        },
        container: {
            className: 'angular-app-container',
            style: {}
        },
        lifecycle: {}
    };

    return mergeConfigs(defaultConfig, config);
}

/**
 * 提取 Angular 组件或模块
 * 重构为多个简单函数以提高可测试性
 */
export function extractAngularComponent(moduleExports: any, preferredName?: string): any {
    if (!isObject(moduleExports)) {
        throw new Error('模块导出必须是对象');
    }

    // 1. 检查首选组件
    const preferredComponent = checkPreferredAngularComponent(moduleExports, preferredName);
    if (preferredComponent) return preferredComponent;

    // 2. 检查默认导出
    const defaultComponent = checkDefaultAngularComponent(moduleExports);
    if (defaultComponent) return defaultComponent;

    // 3. 获取命名导出
    const namedComponents = getNamedAngularComponents(moduleExports);
    if (namedComponents.length > 0) {
        return selectBestAngularComponent(namedComponents);
    }

    throw new Error('未找到有效的 Angular 组件或模块');
}

/**
 * 检查首选 Angular 组件
 */
function checkPreferredAngularComponent(moduleExports: any, preferredName?: string): any {
    if (!preferredName) return null;

    const component = moduleExports[preferredName];
    if (component && isAngularComponent(component)) {
        return component;
    }

    return null;
}

/**
 * 检查默认 Angular 组件
 */
function checkDefaultAngularComponent(moduleExports: any): any {
    const defaultExport = moduleExports.default;
    if (defaultExport && isAngularComponent(defaultExport)) {
        return defaultExport;
    }

    return null;
}

/**
 * 获取命名 Angular 组件
 */
function getNamedAngularComponents(moduleExports: any): any[] {
    const components: any[] = [];

    for (const [key, value] of Object.entries(moduleExports)) {
        if (key !== 'default' && isAngularComponent(value)) {
            components.push(value);
        }
    }

    return components;
}

/**
 * 选择最佳 Angular 组件
 */
function selectBestAngularComponent(components: any[]): any {
    // 优先选择模块
    const modules = components.filter(comp => isAngularModule(comp));
    if (modules.length > 0) return modules[0];

    // 其次选择组件
    const comps = components.filter(comp => isAngularComponentOnly(comp));
    if (comps.length > 0) return comps[0];

    // 最后返回第一个
    return components[0];
}

/**
 * 检查是否为 Angular 组件
 */
export function isAngularComponent(component: any): boolean {
    if (!isObject(component) && !isFunction(component)) return false;

    // 检查 Angular 模块
    if (isAngularModule(component)) return true;

    // 检查 Angular 组件
    if (isAngularComponentOnly(component)) return true;

    return false;
}

/**
 * 检查是否为 Angular 模块
 */
function isAngularModule(module: any): boolean {
    if (!isObject(module) && !isFunction(module)) return false;

    // 检查模块装饰器元数据
    if (module.ɵmod || module.ngModule) return true;

    // 检查模块配置
    if (module.declarations || module.imports || module.providers || module.bootstrap) return true;

    return false;
}

/**
 * 检查是否为 Angular 组件（非模块）
 */
function isAngularComponentOnly(component: any): boolean {
    if (!isObject(component) && !isFunction(component)) return false;

    // 检查组件装饰器元数据
    if (component.ɵcmp || component.ngComponent) return true;

    // 检查组件配置
    if (component.selector || component.template || component.templateUrl) return true;

    return false;
}

/**
 * 检查是否为增强的 Angular 组件
 */
export function isAngularComponentEnhanced(component: any): boolean {
    if (!isAngularComponent(component)) return false;

    // 检查是否有微应用增强功能
    return !!(component.microAppConfig || component.microAppHooks);
}

/**
 * 创建 Angular 容器
 */
export function createAngularContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
    return createEnhancedContainer(appName, 'angular', parentElement, {
        className: 'angular-app-container',
        attributes: {
            'data-framework': 'angular',
            'data-app': appName
        }
    });
}

/**
 * 启动 Angular 模块
 */
async function bootstrapAngularModule(config: AngularConfig, container: HTMLElement): Promise<AngularModuleRef> {
    const { platformBrowserDynamic } = await import('@angular/platform-browser-dynamic');

    // 配置 Angular 环境
    if (config.angular?.enableProdMode) {
        const { enableProdMode } = await import('@angular/core');
        enableProdMode();
    }

    // 启动模块
    const platform = platformBrowserDynamic();
    const moduleRef = await platform.bootstrapModule(config.entry);

    return moduleRef;
}

/**
 * 设置 Angular 开发工具
 */
export function setupAngularDevTools(appName: string, moduleRef: AngularModuleRef): void {
    if (typeof window === 'undefined') return;

    try {
        // 注册到全局 Angular 开发工具
        if (!(window as any).ng) {
            (window as any).ng = {};
        }

        (window as any).ng[appName] = {
            moduleRef,
            getComponent: (selector: string) => {
                // 获取组件实例的辅助函数
                const debugElement = moduleRef.injector.get('DebugElement', null);
                return debugElement?.query(selector);
            }
        };
    } catch (error) {
        console.warn(`设置 Angular 开发工具失败 (${appName}):`, error);
    }
}

/**
 * 格式化 Angular 错误
 */
export function formatAngularError(error: any, operation: string, appName: string): Error {
    return formatAdapterError(error, 'Angular', operation, appName);
}

/**
 * 创建 Angular 错误信息
 */
export function createAngularErrorInfo(error: any, context: any): any {
    return createAdapterErrorInfo(error, 'Angular', context);
}

/**
 * 合并 Angular 配置
 */
export function mergeAngularConfigs(base: Partial<AngularConfig>, override: Partial<AngularConfig>): AngularConfig {
    return mergeConfigs(base, override);
}

/**
 * Angular 微应用集成工具
 */
export class AngularMicroAppIntegration {
    private moduleRef: AngularModuleRef | null = null;
    private container: HTMLElement | null = null;

    constructor(private config: AngularConfig) { }

    async mount(element: HTMLElement): Promise<void> {
        try {
            this.container = createAngularContainer(this.config.name, element);
            this.moduleRef = await bootstrapAngularModule(this.config, this.container);

            // 设置开发工具
            if (this.config.angular?.enableDevTools) {
                setupAngularDevTools(this.config.name, this.moduleRef);
            }

            // 触发生命周期钩子
            await this.config.lifecycle?.mounted?.(this.moduleRef);
        } catch (error) {
            throw formatAngularError(error, 'mount', this.config.name);
        }
    }

    async unmount(): Promise<void> {
        try {
            // 触发生命周期钩子
            await this.config.lifecycle?.beforeUnmount?.(this.moduleRef);

            // 销毁模块
            if (this.moduleRef) {
                this.moduleRef.destroy();
                this.moduleRef = null;
            }

            // 清理容器
            if (this.container) {
                cleanupContainer(this.container);
                this.container = null;
            }

            // 触发生命周期钩子
            await this.config.lifecycle?.unmounted?.();
        } catch (error) {
            throw formatAngularError(error, 'unmount', this.config.name);
        }
    }

    getModuleRef(): AngularModuleRef | null {
        return this.moduleRef;
    }

    getContainer(): HTMLElement | null {
        return this.container;
    }
}