/**
 * Svelte 适配器 - 主入口文件
 */

import type { MicroCoreKernel } from '@micro-core/core';
import { BaseAdapter, type BaseAdapterOptions, type LifecycleManager } from '../../shared/base-adapter';

/**
 * Svelte 适配器选项接口
 */
export interface SvelteAdapterOptions extends BaseAdapterOptions {
    /** 是否为开发模式 */
    dev?: boolean;
    /** 是否启用水合 */
    hydrate?: boolean;
    /** 自定义编译选项 */
    compilerOptions?: {
        generate?: 'dom' | 'ssr';
        hydratable?: boolean;
        legacy?: boolean;
    };
    /** 是否启用 HMR */
    enableHMR?: boolean;
    /** 是否启用开发者工具 */
    enableDevtools?: boolean;
    /** Svelte 组件类 */
    component?: any;
    /** 挂载目标选择器 */
    target?: string | HTMLElement;
    /** 组件属性 */
    props?: Record<string, any>;
}

/**
 * 生命周期函数接口
 */
export interface SvelteLifecycles {
    bootstrap?(): Promise<void>;
    mount?(): Promise<any>;
    unmount?(): Promise<void>;
    update?(props: any): Promise<void>;
}

/**
 * Svelte 生命周期管理器
 */
class SvelteLifecycleManager implements LifecycleManager {
    private app: any = null;
    private options: SvelteAdapterOptions;

    constructor(private appName: string, private appConfig: any, options: SvelteAdapterOptions) {
        this.options = { ...options, ...appConfig };
    }

    async bootstrap(): Promise<void> {
        console.log(`Svelte 应用 ${this.appName} 启动中...`);
        
        if (this.options.dev) {
            console.log('Svelte 应用运行在开发模式');
        }
    }

    async mount(container?: HTMLElement): Promise<any> {
        console.log(`Svelte 应用 ${this.appName} 挂载中...`);

        if (!this.options.component) {
            throw new Error('Svelte 组件未定义');
        }

        try {
            // 确定挂载目标
            let target: HTMLElement;

            if (container) {
                target = container;
            } else if (typeof this.options.target === 'string') {
                const element = document.querySelector(this.options.target);
                if (!element) {
                    throw new Error(`找不到目标元素: ${this.options.target}`);
                }
                target = element as HTMLElement;
            } else if (this.options.target instanceof HTMLElement) {
                target = this.options.target;
            } else {
                throw new Error('未指定有效的挂载目标');
            }

            // 创建 Svelte 应用实例
            const SvelteComponent = this.options.component;
            this.app = new SvelteComponent({
                target,
                props: this.options.props || {},
                hydrate: this.options.hydrate || false
            });

            console.log(`Svelte 应用 ${this.appName} 挂载成功`);
            return this.app;
        } catch (error) {
            console.error(`Svelte 应用 ${this.appName} 挂载失败:`, error);
            throw error;
        }
    }

    async unmount(): Promise<void> {
        console.log(`Svelte 应用 ${this.appName} 卸载中...`);

        if (this.app) {
            try {
                this.app.$destroy();
                this.app = null;
                console.log(`Svelte 应用 ${this.appName} 卸载成功`);
            } catch (error) {
                console.error(`Svelte 应用 ${this.appName} 卸载失败:`, error);
                throw error;
            }
        }
    }

    async update(props: any): Promise<void> {
        console.log(`Svelte 应用 ${this.appName} 更新中...`, props);

        if (this.app && this.app.$set) {
            try {
                this.app.$set(props);
                console.log(`Svelte 应用 ${this.appName} 更新成功`);
            } catch (error) {
                console.error(`Svelte 应用 ${this.appName} 更新失败:`, error);
                throw error;
            }
        }
    }

    destroy(): void {
        if (this.app) {
            this.app.$destroy();
            this.app = null;
        }
    }

    getLifecycles(): SvelteLifecycles {
        return {
            bootstrap: this.bootstrap.bind(this),
            mount: this.mount.bind(this),
            unmount: this.unmount.bind(this),
            update: this.update.bind(this)
        };
    }
}

/**
 * Svelte 适配器插件
 */
export class SvelteAdapter extends BaseAdapter {
    public readonly name = 'adapter-svelte';
    public readonly version = '0.1.0';

    constructor(options: SvelteAdapterOptions = {}) {
        super({
            dev: process.env.NODE_ENV !== 'production',
            hydrate: false,
            enableHMR: process.env.NODE_ENV === 'development',
            enableDevtools: process.env.NODE_ENV === 'development',
            compilerOptions: {
                generate: 'dom',
                hydratable: false,
                legacy: false
            },
            ...options
        });
    }

    getAdapterType(): string {
        return 'svelte';
    }

    createLifecycleManager(appName: string, appConfig: any): LifecycleManager {
        return new SvelteLifecycleManager(appName, appConfig, this.options as SvelteAdapterOptions);
    }

}

/**
 * 创建 Svelte 适配器插件实例
 */
export function createSvelteAdapter(options: SvelteAdapterOptions = {}): SvelteAdapter {
    return new SvelteAdapter(options);
}

/**
 * 创建 Svelte 应用生命周期函数的便捷方法
 */
export function createSvelteLifecycles(
    component: any,
    options: SvelteAdapterOptions = {}
): SvelteLifecycles {
    const adapter = new SvelteAdapter(options);
    const manager = adapter.createLifecycleManager('default', { component, ...options });
    return manager.getLifecycles();
}

/**
 * 默认导出适配器插件类
 */
export default SvelteAdapter;