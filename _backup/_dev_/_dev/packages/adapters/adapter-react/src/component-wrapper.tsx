/**
 * React Component Wrapper
 * Provides context and lifecycle management for React micro-apps
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import type { ReactComponentProps, ReactHookContext } from './types';

// Create React context for micro-app
const MicroAppContext = createContext<ReactHookContext | null>(null);

export const ReactComponentWrapper: React.FC<ReactComponentProps> = ({
  config,
  hooks,
  adapter,
  children
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Create context value
  const contextValue: ReactHookContext = {
    config,
    lifecycle: adapter.lifecycleManager,
    communication: adapter.communicationManager,
    sandbox: adapter.sandboxManager
  };

  useEffect(() => {
    let isMounted = true;

    const initializeApp = async () => {
      try {
        // Execute load hook if present
        if (hooks?.load) {
          await hooks.load();
        }

        if (isMounted) {
          setIsLoaded(true);
        }
      } catch (err) {
        if (isMounted) {
          setError(err as Error);
          if (hooks?.onError) {
            hooks.onError(err as Error, {
              componentStack: '',
              errorBoundary: 'ReactComponentWrapper'
            });
          }
        }
      }
    };

    initializeApp();

    return () => {
      isMounted = false;
      // Execute unload hook if present
      if (hooks?.unload) {
        hooks.unload().catch(console.error);
      }
    };
  }, [hooks]);

  if (error) {
    return (
      <div className="micro-app-error">
        <h3>Micro App Error</h3>
        <p>{error.message}</p>
        <button onClick={() => setError(null)}>Retry</button>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div className="micro-app-loading">
        <p>Loading micro app...</p>
      </div>
    );
  }

  return (
    <MicroAppContext.Provider value={contextValue}>
      <div className={`micro-app micro-app-${config.name}`}>
        {children}
      </div>
    </MicroAppContext.Provider>
  );
};

// Hook to access micro-app context
export const useMicroApp = (): ReactHookContext => {
  const context = useContext(MicroAppContext);
  if (!context) {
    throw new Error('useMicroApp must be used within a ReactComponentWrapper');
  }
  return context;
};

// Hook to access micro-app configuration
export const useMicroAppConfig = () => {
  const { config } = useMicroApp();
  return config;
};

// Hook to access communication manager
export const useMicroAppCommunication = () => {
  const { communication } = useMicroApp();
  return communication;
};

// Hook to access lifecycle manager
export const useMicroAppLifecycle = () => {
  const { lifecycle } = useMicroApp();
  return lifecycle;
};
