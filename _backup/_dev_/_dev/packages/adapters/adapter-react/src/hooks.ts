/**
 * React Hooks for Micro-Core Integration
 * Custom hooks for React micro-applications
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import type { ReactHookContext, ReactAppConfig } from './types';

/**
 * Hook to access micro-app communication
 */
export function useMicroAppCommunication() {
  const context = useMicroApp();
  return context.communication;
}

/**
 * Hook to subscribe to global state changes
 */
export function useGlobalState<T = any>(key: string, defaultValue?: T) {
  const communication = useMicroAppCommunication();
  const [value, setValue] = useState<T>(() => {
    return communication.getGlobalState(key) ?? defaultValue;
  });

  useEffect(() => {
    const unsubscribe = communication.onGlobalStateChange((state: any) => {
      if (key in state) {
        setValue(state[key]);
      }
    });

    return unsubscribe;
  }, [communication, key]);

  const updateValue = useCallback((newValue: T) => {
    communication.setGlobalState({ [key]: newValue });
  }, [communication, key]);

  return [value, updateValue] as const;
}

/**
 * Hook to emit and listen to events
 */
export function useEventBus() {
  const communication = useMicroAppCommunication();

  const emit = useCallback((event: string, data?: any) => {
    communication.emit(event, data);
  }, [communication]);

  const on = useCallback((event: string, handler: (data: any) => void) => {
    return communication.on(event, handler);
  }, [communication]);

  const off = useCallback((event: string, handler: (data: any) => void) => {
    communication.off(event, handler);
  }, [communication]);

  return { emit, on, off };
}

/**
 * Hook to listen to specific events
 */
export function useEvent(event: string, handler: (data: any) => void, deps: any[] = []) {
  const { on, off } = useEventBus();

  useEffect(() => {
    const unsubscribe = on(event, handler);
    return unsubscribe;
  }, [event, on, off, ...deps]);
}

/**
 * Hook to access app lifecycle
 */
export function useLifecycle() {
  const context = useMicroApp();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    const lifecycle = context.lifecycle;
    
    const handleLoad = () => setIsLoaded(true);
    const handleMount = () => setIsMounted(true);
    const handleUnmount = () => setIsMounted(false);
    const handleUnload = () => setIsLoaded(false);

    lifecycle.on('load', handleLoad);
    lifecycle.on('mount', handleMount);
    lifecycle.on('unmount', handleUnmount);
    lifecycle.on('unload', handleUnload);

    return () => {
      lifecycle.off('load', handleLoad);
      lifecycle.off('mount', handleMount);
      lifecycle.off('unmount', handleUnmount);
      lifecycle.off('unload', handleUnload);
    };
  }, [context.lifecycle]);

  return { isLoaded, isMounted };
}

/**
 * Hook to access sandbox information
 */
export function useSandbox() {
  const context = useMicroApp();
  const [sandboxInfo, setSandboxInfo] = useState<any>(null);

  useEffect(() => {
    const sandbox = context.sandbox;
    const appName = context.config.name;
    
    const info = sandbox.getSandboxInfo(appName);
    setSandboxInfo(info);
  }, [context.sandbox, context.config.name]);

  return sandboxInfo;
}

/**
 * Hook for inter-app communication
 */
export function useInterAppCommunication() {
  const communication = useMicroAppCommunication();

  const sendToApp = useCallback((targetApp: string, message: any) => {
    communication.sendToApp(targetApp, message);
  }, [communication]);

  const onMessageFromApp = useCallback((sourceApp: string, handler: (message: any) => void) => {
    return communication.onMessageFromApp(sourceApp, handler);
  }, [communication]);

  return { sendToApp, onMessageFromApp };
}

/**
 * Hook for app navigation
 */
export function useNavigation() {
  const context = useMicroApp();
  const [currentRoute, setCurrentRoute] = useState<string>('');

  const navigate = useCallback((path: string, options?: any) => {
    // Implementation would depend on the router manager
    context.lifecycle.navigate?.(path, options);
  }, [context.lifecycle]);

  const goBack = useCallback(() => {
    window.history.back();
  }, []);

  const goForward = useCallback(() => {
    window.history.forward();
  }, []);

  useEffect(() => {
    const updateRoute = () => {
      setCurrentRoute(window.location.pathname);
    };

    window.addEventListener('popstate', updateRoute);
    updateRoute();

    return () => {
      window.removeEventListener('popstate', updateRoute);
    };
  }, []);

  return { currentRoute, navigate, goBack, goForward };
}

/**
 * Hook for resource loading
 */
export function useResourceLoader() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadResource = useCallback(async (url: string, type: 'script' | 'style' = 'script') => {
    setLoading(true);
    setError(null);

    try {
      if (type === 'script') {
        await loadScript(url);
      } else {
        await loadStyle(url);
      }
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, []);

  return { loadResource, loading, error };
}

/**
 * Hook for app configuration
 */
export function useAppConfig(): ReactAppConfig {
  const context = useMicroApp();
  return context.config;
}

/**
 * Hook for performance monitoring
 */
export function usePerformance() {
  const [metrics, setMetrics] = useState<any>({});

  const measureRender = useCallback((name: string) => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setMetrics(prev => ({
        ...prev,
        [name]: duration
      }));
    };
  }, []);

  const measureAsync = useCallback(async (name: string, asyncFn: () => Promise<any>) => {
    const startTime = performance.now();
    
    try {
      const result = await asyncFn();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setMetrics(prev => ({
        ...prev,
        [name]: duration
      }));
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setMetrics(prev => ({
        ...prev,
        [`${name}_error`]: duration
      }));
      
      throw error;
    }
  }, []);

  return { metrics, measureRender, measureAsync };
}

/**
 * Hook for error handling
 */
export function useErrorHandler() {
  const [errors, setErrors] = useState<Error[]>([]);

  const handleError = useCallback((error: Error) => {
    setErrors(prev => [...prev, error]);
    console.error('React micro-app error:', error);
  }, []);

  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const clearError = useCallback((index: number) => {
    setErrors(prev => prev.filter((_, i) => i !== index));
  }, []);

  return { errors, handleError, clearErrors, clearError };
}

// Helper functions
function loadScript(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = url;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
    document.head.appendChild(script);
  });
}

function loadStyle(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = url;
    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to load style: ${url}`));
    document.head.appendChild(link);
  });
}

// Re-export from component-wrapper for convenience
export { useMicroApp, useMicroAppConfig } from './component-wrapper';
