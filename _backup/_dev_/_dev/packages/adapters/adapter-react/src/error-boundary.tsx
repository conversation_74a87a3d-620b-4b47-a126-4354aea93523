/**
 * React Error Boundary for Micro-Apps
 * Catches and handles errors in React micro-applications
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import type { ReactErrorBoundaryProps, ReactErrorBoundaryState, ReactErrorInfo } from './types';

export class ReactErrorBoundary extends Component<ReactErrorBoundaryProps, ReactErrorBoundaryState> {
  constructor(props: ReactErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ReactErrorBoundaryState {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const reactErrorInfo: ReactErrorInfo = {
      ...errorInfo,
      errorBoundary: 'ReactErrorBoundary',
      errorBoundaryStack: new Error().stack
    };

    this.setState({
      hasError: true,
      error,
      errorInfo: reactErrorInfo
    });

    // Call the error handler if provided
    if (this.props.onError) {
      this.props.onError(error, reactErrorInfo);
    }

    // Log error for debugging
    console.error('React Error Boundary caught an error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // Custom fallback component
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent {...this.state} />;
      }

      // Default error UI
      return (
        <div className="micro-app-error-boundary">
          <div className="error-container">
            <h2>🚫 Micro App Error</h2>
            <p className="error-message">
              Something went wrong in this micro application.
            </p>
            <details className="error-details">
              <summary>Error Details</summary>
              <div className="error-info">
                <h4>Error:</h4>
                <pre>{this.state.error?.message}</pre>
                {this.state.error?.stack && (
                  <>
                    <h4>Stack Trace:</h4>
                    <pre>{this.state.error.stack}</pre>
                  </>
                )}
                {this.state.errorInfo?.componentStack && (
                  <>
                    <h4>Component Stack:</h4>
                    <pre>{this.state.errorInfo.componentStack}</pre>
                  </>
                )}
              </div>
            </details>
            <div className="error-actions">
              <button 
                onClick={this.handleRetry}
                className="retry-button"
              >
                🔄 Retry
              </button>
              <button 
                onClick={() => window.location.reload()}
                className="reload-button"
              >
                🔃 Reload Page
              </button>
            </div>
          </div>
          <style jsx>{`
            .micro-app-error-boundary {
              display: flex;
              align-items: center;
              justify-content: center;
              min-height: 200px;
              padding: 20px;
              background-color: #fef2f2;
              border: 1px solid #fecaca;
              border-radius: 8px;
              margin: 10px;
            }
            .error-container {
              max-width: 600px;
              text-align: center;
            }
            .error-container h2 {
              color: #dc2626;
              margin-bottom: 10px;
            }
            .error-message {
              color: #7f1d1d;
              margin-bottom: 20px;
            }
            .error-details {
              text-align: left;
              margin: 20px 0;
              background-color: #fff;
              border: 1px solid #e5e7eb;
              border-radius: 4px;
              padding: 10px;
            }
            .error-details summary {
              cursor: pointer;
              font-weight: bold;
              color: #374151;
            }
            .error-info {
              margin-top: 10px;
            }
            .error-info h4 {
              margin: 10px 0 5px 0;
              color: #374151;
            }
            .error-info pre {
              background-color: #f9fafb;
              border: 1px solid #e5e7eb;
              border-radius: 4px;
              padding: 10px;
              overflow-x: auto;
              font-size: 12px;
              color: #1f2937;
            }
            .error-actions {
              display: flex;
              gap: 10px;
              justify-content: center;
              margin-top: 20px;
            }
            .retry-button, .reload-button {
              padding: 8px 16px;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              font-weight: bold;
              transition: background-color 0.2s;
            }
            .retry-button {
              background-color: #3b82f6;
              color: white;
            }
            .retry-button:hover {
              background-color: #2563eb;
            }
            .reload-button {
              background-color: #6b7280;
              color: white;
            }
            .reload-button:hover {
              background-color: #4b5563;
            }
          `}</style>
        </div>
      );
    }

    return this.props.children;
  }
}

// Default fallback component
export const DefaultErrorFallback: React.FC<ReactErrorBoundaryState> = ({ 
  error, 
  errorInfo 
}) => (
  <div className="default-error-fallback">
    <h3>Application Error</h3>
    <p>{error?.message || 'An unexpected error occurred'}</p>
    {process.env.NODE_ENV === 'development' && errorInfo && (
      <details>
        <summary>Debug Information</summary>
        <pre>{errorInfo.componentStack}</pre>
      </details>
    )}
  </div>
);

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Partial<ReactErrorBoundaryProps>
) {
  const WrappedComponent = (props: P) => (
    <ReactErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ReactErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
