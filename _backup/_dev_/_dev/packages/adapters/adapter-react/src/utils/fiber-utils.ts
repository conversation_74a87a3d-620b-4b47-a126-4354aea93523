/**
 * @fileoverview React Fiber Utility Functions
 * 提供 React Fiber 架构相关的工具函数
 */

import type { ReactElement, ComponentType } from 'react';

/**
 * Fiber 节点类型定义
 */
interface FiberNode {
  type: any;
  key: string | null;
  ref: any;
  props: any;
  stateNode: any;
  return: FiberNode | null;
  child: FiberNode | null;
  sibling: FiberNode | null;
  index: number;
  tag: number;
  elementType: any;
  pendingProps: any;
  memoizedProps: any;
  memoizedState: any;
  updateQueue: any;
  flags: number;
  subtreeFlags: number;
  deletions: FiberNode[] | null;
  lanes: number;
  childLanes: number;
  alternate: FiberNode | null;
}

/**
 * React Fiber 工具类
 */
export class ReactFiberUtils {
  /**
   * 获取组件的 Fiber 节点
   */
  static getFiberNode(element: HTMLElement): FiberNode | null {
    try {
      // 尝试从多个可能的属性中获取 Fiber 节点
      const fiberKey = Object.keys(element).find(key => 
        key.startsWith('__reactFiber') || 
        key.startsWith('__reactInternalFiber')
      );
      
      if (fiberKey) {
        return (element as any)[fiberKey];
      }

      // 备用方法：通过 _reactInternalFiber 属性
      return (element as any)._reactInternalFiber || null;
    } catch (error) {
      console.warn('[FiberUtils] Failed to get fiber node:', error);
      return null;
    }
  }

  /**
   * 遍历 Fiber 树
   */
  static traverseFiberTree(
    fiber: FiberNode | null,
    callback: (fiber: FiberNode) => boolean | void
  ): void {
    if (!fiber) return;

    const shouldContinue = callback(fiber);
    if (shouldContinue === false) return;

    // 遍历子节点
    this.traverseFiberTree(fiber.child, callback);
    
    // 遍历兄弟节点
    this.traverseFiberTree(fiber.sibling, callback);
  }

  /**
   * 查找特定类型的 Fiber 节点
   */
  static findFiberByType(
    rootFiber: FiberNode | null,
    targetType: ComponentType<any> | string
  ): FiberNode[] {
    const results: FiberNode[] = [];

    this.traverseFiberTree(rootFiber, (fiber) => {
      if (fiber.type === targetType || fiber.elementType === targetType) {
        results.push(fiber);
      }
    });

    return results;
  }

  /**
   * 查找带有特定 props 的 Fiber 节点
   */
  static findFiberByProps(
    rootFiber: FiberNode | null,
    propMatcher: (props: any) => boolean
  ): FiberNode[] {
    const results: FiberNode[] = [];

    this.traverseFiberTree(rootFiber, (fiber) => {
      if (fiber.props && propMatcher(fiber.props)) {
        results.push(fiber);
      }
    });

    return results;
  }

  /**
   * 获取组件实例
   */
  static getComponentInstance(fiber: FiberNode): any {
    if (!fiber) return null;

    // 类组件
    if (fiber.stateNode && typeof fiber.stateNode === 'object') {
      return fiber.stateNode;
    }

    // 函数组件的 hooks
    if (fiber.memoizedState) {
      return fiber.memoizedState;
    }

    return null;
  }

  /**
   * 获取组件的 props
   */
  static getComponentProps(fiber: FiberNode): any {
    return fiber?.memoizedProps || fiber?.pendingProps || {};
  }

  /**
   * 获取组件的 state
   */
  static getComponentState(fiber: FiberNode): any {
    const instance = this.getComponentInstance(fiber);
    return instance?.state || fiber?.memoizedState || {};
  }

  /**
   * 检查组件是否已挂载
   */
  static isComponentMounted(fiber: FiberNode): boolean {
    if (!fiber) return false;

    // 检查 Fiber 节点是否在当前树中
    let current = fiber;
    while (current.return) {
      current = current.return;
    }

    // 如果能找到根节点，说明组件已挂载
    return current.tag === 3; // HostRoot tag
  }

  /**
   * 获取组件的显示名称
   */
  static getComponentDisplayName(fiber: FiberNode): string {
    if (!fiber) return 'Unknown';

    const type = fiber.type || fiber.elementType;
    
    if (typeof type === 'string') {
      return type;
    }

    if (typeof type === 'function') {
      return type.displayName || type.name || 'Anonymous';
    }

    if (type && typeof type === 'object') {
      return type.displayName || type.name || 'Component';
    }

    return 'Unknown';
  }
}

/**
 * React Fiber 性能监控工具
 */
export class ReactFiberPerformanceMonitor {
  private static performanceData = new Map<string, {
    renderCount: number;
    totalRenderTime: number;
    averageRenderTime: number;
    lastRenderTime: number;
  }>();

  /**
   * 开始性能监控
   */
  static startMonitoring(fiber: FiberNode): string {
    const componentName = ReactFiberUtils.getComponentDisplayName(fiber);
    const startTime = performance.now();
    
    return `${componentName}_${startTime}`;
  }

  /**
   * 结束性能监控
   */
  static endMonitoring(monitorId: string, fiber: FiberNode): void {
    const endTime = performance.now();
    const [componentName, startTimeStr] = monitorId.split('_');
    const startTime = parseFloat(startTimeStr);
    const renderTime = endTime - startTime;

    let data = this.performanceData.get(componentName);
    if (!data) {
      data = {
        renderCount: 0,
        totalRenderTime: 0,
        averageRenderTime: 0,
        lastRenderTime: 0
      };
      this.performanceData.set(componentName, data);
    }

    data.renderCount++;
    data.totalRenderTime += renderTime;
    data.averageRenderTime = data.totalRenderTime / data.renderCount;
    data.lastRenderTime = renderTime;

    // 警告慢渲染
    if (renderTime > 16) { // > 1 frame at 60fps
      console.warn(`[FiberPerformance] Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms`);
    }
  }

  /**
   * 获取性能报告
   */
  static getPerformanceReport(): Record<string, any> {
    const report: Record<string, any> = {};
    
    this.performanceData.forEach((data, componentName) => {
      report[componentName] = {
        ...data,
        averageRenderTime: parseFloat(data.averageRenderTime.toFixed(2)),
        lastRenderTime: parseFloat(data.lastRenderTime.toFixed(2))
      };
    });

    return report;
  }

  /**
   * 清除性能数据
   */
  static clearPerformanceData(): void {
    this.performanceData.clear();
  }
}

/**
 * React Fiber 调试工具
 */
export class ReactFiberDebugger {
  /**
   * 打印 Fiber 树结构
   */
  static printFiberTree(fiber: FiberNode | null, depth: number = 0): void {
    if (!fiber) return;

    const indent = '  '.repeat(depth);
    const componentName = ReactFiberUtils.getComponentDisplayName(fiber);
    const props = ReactFiberUtils.getComponentProps(fiber);
    const state = ReactFiberUtils.getComponentState(fiber);

    console.log(`${indent}${componentName}`, {
      props: Object.keys(props).length > 0 ? props : undefined,
      state: Object.keys(state).length > 0 ? state : undefined,
      tag: fiber.tag,
      key: fiber.key
    });

    // 递归打印子节点
    this.printFiberTree(fiber.child, depth + 1);
    
    // 递归打印兄弟节点
    if (depth === 0) { // 只在根级别打印兄弟节点
      this.printFiberTree(fiber.sibling, depth);
    }
  }

  /**
   * 获取组件层次结构
   */
  static getComponentHierarchy(fiber: FiberNode | null): string[] {
    const hierarchy: string[] = [];
    let current = fiber;

    while (current) {
      const componentName = ReactFiberUtils.getComponentDisplayName(current);
      if (componentName !== 'Unknown') {
        hierarchy.unshift(componentName);
      }
      current = current.return;
    }

    return hierarchy;
  }

  /**
   * 检查组件更新原因
   */
  static analyzeUpdateReason(fiber: FiberNode): {
    propsChanged: boolean;
    stateChanged: boolean;
    contextChanged: boolean;
    forceUpdate: boolean;
  } {
    const current = fiber.alternate;
    
    if (!current) {
      return {
        propsChanged: false,
        stateChanged: false,
        contextChanged: false,
        forceUpdate: false
      };
    }

    const propsChanged = fiber.memoizedProps !== current.memoizedProps;
    const stateChanged = fiber.memoizedState !== current.memoizedState;
    const contextChanged = fiber.dependencies !== current.dependencies;
    const forceUpdate = (fiber.flags & 4) !== 0; // ForceUpdate flag

    return {
      propsChanged,
      stateChanged,
      contextChanged,
      forceUpdate
    };
  }
}

/**
 * React Fiber 内存管理工具
 */
export class ReactFiberMemoryManager {
  private static fiberRefs = new WeakSet<FiberNode>();

  /**
   * 跟踪 Fiber 节点引用
   */
  static trackFiberNode(fiber: FiberNode): void {
    this.fiberRefs.add(fiber);
  }

  /**
   * 清理 Fiber 节点引用
   */
  static cleanupFiberNode(fiber: FiberNode): void {
    // WeakSet 会自动清理不再被引用的对象
    // 这里主要是为了显式标记清理意图
    if (this.fiberRefs.has(fiber)) {
      // 执行清理逻辑
      this.clearFiberNodeData(fiber);
    }
  }

  /**
   * 清理 Fiber 节点数据
   */
  private static clearFiberNodeData(fiber: FiberNode): void {
    try {
      // 清理可能的循环引用
      if (fiber.alternate) {
        fiber.alternate = null;
      }
      
      // 清理子节点引用
      if (fiber.child) {
        this.clearFiberNodeData(fiber.child);
        fiber.child = null;
      }
      
      // 清理兄弟节点引用
      if (fiber.sibling) {
        this.clearFiberNodeData(fiber.sibling);
        fiber.sibling = null;
      }
      
      // 清理父节点引用
      fiber.return = null;
      
      // 清理状态节点
      fiber.stateNode = null;
      
      // 清理更新队列
      fiber.updateQueue = null;
      
      // 清理删除列表
      fiber.deletions = null;
    } catch (error) {
      console.warn('[FiberMemoryManager] Error cleaning fiber node:', error);
    }
  }

  /**
   * 检查内存泄漏
   */
  static checkMemoryLeaks(): {
    suspiciousNodes: number;
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    let suspiciousNodes = 0;

    // 这里只是一个示例，实际的内存泄漏检测会更复杂
    if (ReactFiberPerformanceMonitor.getPerformanceReport) {
      const report = ReactFiberPerformanceMonitor.getPerformanceReport();
      
      Object.entries(report).forEach(([componentName, data]) => {
        if ((data as any).renderCount > 1000) {
          suspiciousNodes++;
          recommendations.push(`${componentName} has rendered ${(data as any).renderCount} times - consider optimization`);
        }
      });
    }

    return {
      suspiciousNodes,
      recommendations
    };
  }
}

/**
 * 导出所有工具类
 */
export {
  ReactFiberUtils,
  ReactFiberPerformanceMonitor,
  ReactFiberDebugger,
  ReactFiberMemoryManager
};

/**
 * 默认导出工具集合
 */
export default {
  ReactFiberUtils,
  ReactFiberPerformanceMonitor,
  ReactFiberDebugger,
  ReactFiberMemoryManager
};
