/**
 * React Lifecycle Adapter
 * Manages React-specific lifecycle events and hooks
 */

import { BaseLifecycleAdapter } from '@micro-core/core';
import type { ReactAppConfig, ReactLifecycleHooks, ReactAppInstance } from './types';

export class ReactLifecycleAdapter extends BaseLifecycleAdapter {
  private appInstances = new Map<string, ReactAppInstance>();

  /**
   * Register React app instance
   */
  registerApp(name: string, instance: ReactAppInstance): void {
    this.appInstances.set(name, instance);
  }

  /**
   * Unregister React app instance
   */
  unregisterApp(name: string): void {
    this.appInstances.delete(name);
  }

  /**
   * Get React app instance
   */
  getApp(name: string): ReactAppInstance | undefined {
    return this.appInstances.get(name);
  }

  /**
   * Execute React-specific load lifecycle
   */
  async executeLoad(appConfig: ReactAppConfig): Promise<void> {
    const hooks = appConfig.hooks as ReactLifecycleHooks;
    
    // Execute standard load hook
    if (hooks?.load) {
      await hooks.load();
    }

    // React-specific initialization
    await this.initializeReactEnvironment(appConfig);
  }

  /**
   * Execute React-specific mount lifecycle
   */
  async executeMount(appName: string): Promise<void> {
    const instance = this.appInstances.get(appName);
    if (!instance) {
      throw new Error(`React app instance not found: ${appName}`);
    }

    const hooks = instance.hooks;

    // Execute before mount hook
    if (hooks.beforeMount) {
      await hooks.beforeMount(instance.config.props);
    }

    // Execute standard mount hook
    if (hooks.mount) {
      await hooks.mount();
    }

    // Mount React component
    await instance.mount();

    // Execute after mount hook
    if (hooks.afterMount) {
      await hooks.afterMount(instance);
    }
  }

  /**
   * Execute React-specific unmount lifecycle
   */
  async executeUnmount(appName: string): Promise<void> {
    const instance = this.appInstances.get(appName);
    if (!instance) {
      return; // Already unmounted
    }

    const hooks = instance.hooks;

    // Execute before unmount hook
    if (hooks.beforeUnmount) {
      await hooks.beforeUnmount(instance);
    }

    // Execute standard unmount hook
    if (hooks.unmount) {
      await hooks.unmount();
    }

    // Unmount React component
    await instance.unmount();

    // Execute after unmount hook
    if (hooks.afterUnmount) {
      await hooks.afterUnmount();
    }
  }

  /**
   * Execute React-specific unload lifecycle
   */
  async executeUnload(appName: string): Promise<void> {
    const instance = this.appInstances.get(appName);
    if (!instance) {
      return;
    }

    const hooks = instance.hooks;

    // Execute standard unload hook
    if (hooks.unload) {
      await hooks.unload();
    }

    // Clean up React environment
    await this.cleanupReactEnvironment(appName);

    // Remove instance
    this.unregisterApp(appName);
  }

  /**
   * Handle React component updates
   */
  async executeUpdate(appName: string, props: any): Promise<void> {
    const instance = this.appInstances.get(appName);
    if (!instance) {
      throw new Error(`React app instance not found: ${appName}`);
    }

    const hooks = instance.hooks;
    const prevProps = instance.config.props;

    // Execute update hook
    if (hooks.onUpdate) {
      await hooks.onUpdate(prevProps, props);
    }

    // Update React component
    await instance.update(props);
  }

  /**
   * Handle React errors
   */
  async executeError(appName: string, error: Error, errorInfo?: any): Promise<void> {
    const instance = this.appInstances.get(appName);
    if (!instance) {
      return;
    }

    const hooks = instance.hooks;

    // Execute error hook
    if (hooks.onError) {
      await hooks.onError(error, errorInfo);
    }

    // Log error
    console.error(`React app error in ${appName}:`, error);
    if (errorInfo) {
      console.error('Error info:', errorInfo);
    }
  }

  /**
   * Initialize React environment for the app
   */
  private async initializeReactEnvironment(appConfig: ReactAppConfig): Promise<void> {
    // Setup React DevTools if enabled
    if (appConfig.react?.enableDevTools) {
      this.setupReactDevTools(appConfig.name);
    }

    // Setup React Strict Mode if enabled
    if (appConfig.react?.strictMode) {
      this.setupStrictMode(appConfig.name);
    }

    // Initialize React context providers
    if (appConfig.react?.sandbox?.contextProviders) {
      this.setupContextProviders(appConfig.name, appConfig.react.sandbox.contextProviders);
    }
  }

  /**
   * Clean up React environment
   */
  private async cleanupReactEnvironment(appName: string): Promise<void> {
    // Clean up React DevTools
    this.cleanupReactDevTools(appName);

    // Clean up context providers
    this.cleanupContextProviders(appName);

    // Clean up any React-specific resources
    this.cleanupReactResources(appName);
  }

  /**
   * Setup React DevTools for the app
   */
  private setupReactDevTools(appName: string): void {
    if (typeof window !== 'undefined' && (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      // React DevTools is available
      console.log(`React DevTools enabled for app: ${appName}`);
    }
  }

  /**
   * Clean up React DevTools
   */
  private cleanupReactDevTools(appName: string): void {
    // Clean up DevTools references if needed
    console.log(`React DevTools cleaned up for app: ${appName}`);
  }

  /**
   * Setup React Strict Mode
   */
  private setupStrictMode(appName: string): void {
    console.log(`React Strict Mode enabled for app: ${appName}`);
  }

  /**
   * Setup React context providers
   */
  private setupContextProviders(appName: string, providers: any[]): void {
    console.log(`React context providers setup for app: ${appName}`, providers);
  }

  /**
   * Clean up React context providers
   */
  private cleanupContextProviders(appName: string): void {
    console.log(`React context providers cleaned up for app: ${appName}`);
  }

  /**
   * Clean up React-specific resources
   */
  private cleanupReactResources(appName: string): void {
    // Clean up any React-specific resources, event listeners, etc.
    console.log(`React resources cleaned up for app: ${appName}`);
  }

  /**
   * Get all active React app instances
   */
  getActiveApps(): string[] {
    return Array.from(this.appInstances.keys());
  }

  /**
   * Check if React app is mounted
   */
  isAppMounted(appName: string): boolean {
    const instance = this.appInstances.get(appName);
    return !!instance;
  }

  /**
   * Get React app configuration
   */
  getAppConfig(appName: string): ReactAppConfig | undefined {
    const instance = this.appInstances.get(appName);
    return instance?.config;
  }
}
