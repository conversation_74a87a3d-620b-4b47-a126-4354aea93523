/**
 * React Adapter Types
 */

import type { ComponentType, ErrorInfo, ReactNode } from 'react';
import type { AppConfig, LifecycleHooks, SandboxConfig } from '@micro-core/core';

export interface ReactAdapterConfig {
  /** React version compatibility */
  reactVersion?: string;
  /** Enable React DevTools integration */
  enableDevTools?: boolean;
  /** Enable React Strict Mode */
  strictMode?: boolean;
  /** Custom error boundary */
  errorBoundary?: ComponentType<ReactErrorBoundaryProps>;
  /** React-specific sandbox configuration */
  sandbox?: ReactSandboxConfig;
}

export interface ReactAppConfig extends AppConfig {
  /** React adapter specific configuration */
  react?: ReactAdapterConfig;
  /** React component to render */
  component?: ComponentType<any>;
  /** Props to pass to the React component */
  props?: Record<string, any>;
  /** React DOM container selector or element */
  container?: string | HTMLElement;
}

export interface ReactLifecycleHooks extends LifecycleHooks {
  /** Called before React component mounts */
  beforeMount?: (props: any) => Promise<void> | void;
  /** Called after React component mounts */
  afterMount?: (instance: any) => Promise<void> | void;
  /** Called before React component unmounts */
  beforeUnmount?: (instance: any) => Promise<void> | void;
  /** Called after React component unmounts */
  afterUnmount?: () => Promise<void> | void;
  /** Called when React component updates */
  onUpdate?: (prevProps: any, nextProps: any) => Promise<void> | void;
  /** Called when React component encounters an error */
  onError?: (error: Error, errorInfo: ReactErrorInfo) => Promise<void> | void;
}

export interface ReactComponentProps {
  /** App configuration */
  config: ReactAppConfig;
  /** App lifecycle hooks */
  hooks?: ReactLifecycleHooks;
  /** Children components */
  children?: ReactNode;
  /** Additional props */
  [key: string]: any;
}

export interface ReactErrorInfo extends ErrorInfo {
  /** Component stack trace */
  componentStack: string;
  /** Error boundary info */
  errorBoundary?: string;
  /** Error boundary stack */
  errorBoundaryStack?: string;
}

export interface ReactErrorBoundaryProps {
  /** Children to render */
  children: ReactNode;
  /** Fallback component to render on error */
  fallback?: ComponentType<ReactErrorBoundaryState>;
  /** Error handler callback */
  onError?: (error: Error, errorInfo: ReactErrorInfo) => void;
}

export interface ReactErrorBoundaryState {
  /** Whether an error has occurred */
  hasError: boolean;
  /** The error that occurred */
  error?: Error;
  /** Error information */
  errorInfo?: ReactErrorInfo;
}

export interface ReactSandboxConfig extends SandboxConfig {
  /** Isolate React context */
  isolateContext?: boolean;
  /** Preserve React DevTools */
  preserveDevTools?: boolean;
  /** Custom React context providers */
  contextProviders?: ComponentType<any>[];
}

export interface ReactHookContext {
  /** App configuration */
  config: ReactAppConfig;
  /** Lifecycle manager */
  lifecycle: any;
  /** Communication manager */
  communication: any;
  /** Sandbox manager */
  sandbox: any;
}

export type ReactAppInstance = {
  /** React component instance */
  component: ComponentType<any>;
  /** DOM container */
  container: HTMLElement;
  /** App configuration */
  config: ReactAppConfig;
  /** Lifecycle hooks */
  hooks: ReactLifecycleHooks;
  /** Mount the React app */
  mount: () => Promise<void>;
  /** Unmount the React app */
  unmount: () => Promise<void>;
  /** Update the React app */
  update: (props: any) => Promise<void>;
};
