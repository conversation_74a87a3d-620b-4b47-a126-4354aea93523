{"extends": "../../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "composite": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@micro-core/core": ["../../../packages/core/src"], "@micro-core/shared": ["../../../packages/shared"], "@micro-core/adapter-shared": ["../adapter-shared/src"]}}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}