/**
 * @micro-core/adapters
 * 微前端适配器系统
 * 
 * @description 提供统一的多框架适配器管理
 * @version 0.1.0
 * <AUTHOR> <<EMAIL>>
 */

// 核心管理器
export { AdapterManager } from './adapter-manager';

// 从 shared 包导入类型定义
export type {
    AdapterConfig,
    AdapterInstance,
    AdapterStatus,
    FrameworkType
} from '@micro-core/shared';

/**
 * 创建适配器管理器实例
 */
export function createAdapterManager(): AdapterManager {
    return new AdapterManager();
}

/**
 * 默认适配器管理器实例
 */
export const defaultAdapterManager = createAdapterManager();

/**
 * 版本信息
 */
export const VERSION = '0.1.0';

/**
 * 默认导出
 */
export default {
    VERSION,
    AdapterManager,
    createAdapterManager,
    defaultAdapterManager
};