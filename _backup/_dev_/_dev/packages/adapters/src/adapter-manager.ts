/**
 * 适配器管理器
 * 
 * @description 统一管理所有框架适配器
 * <AUTHOR> <<EMAIL>>
 */

import { createError, Logger } from '@micro-core/shared';
import { BaseAdapter } from '../shared/src/base-adapter';
import type {
    AdapterConfig,
    AdapterFactory,
    AdapterRegistry,
    AdapterState,
    FrameworkType
} from '../shared/src/types';

/**
 * 简单的事件发射器
 */
class EventEmitter {
    private listeners = new Map<string, Set<Function>>();

    emit(event: string, data?: any): void {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            eventListeners.forEach(listener => {
                try {
                    listener(data);
                } catch (error) {
                    console.error('Event listener error:', error);
                }
            });
        }
    }

    on(event: string, listener: Function): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }
        this.listeners.get(event)!.add(listener);
    }

    off(event: string, listener: Function): void {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            eventListeners.delete(listener);
        }
    }
}

/**
 * 适配器实例信息
 */
interface AdapterInstance {
    /** 适配器ID */
    id: string;
    /** 适配器实例 */
    instance: BaseAdapter;
    /** 适配器状态 */
    state: AdapterState;
    /** 适配器配置 */
    config: AdapterConfig;
    /** 创建时间 */
    createdAt: number;
    /** 最后活动时间 */
    lastActivity: number;
    /** 错误信息 */
    lastError?: Error;
}

/**
 * 适配器管理器
 */
export class AdapterManager extends EventEmitter {
    private readonly logger: Logger;
    private readonly adapters = new Map<string, AdapterInstance>();
    private readonly factories = new Map<FrameworkType, AdapterFactory>();
    private readonly registry = new Map<string, AdapterRegistry>();

    constructor() {
        super();
        this.logger = Logger.create('AdapterManager');
        this.initializeBuiltinAdapters();
        this.logger.info('适配器管理器初始化完成');
    }

    /**
     * 注册适配器工厂
     */
    registerFactory(
        frameworkType: FrameworkType,
        factory: AdapterFactory
    ): void {
        try {
            this.validateFactory(factory);

            if (this.factories.has(frameworkType)) {
                this.logger.warn('适配器工厂已存在，将被覆盖', { frameworkType });
            }

            this.factories.set(frameworkType, factory);

            this.emit('factoryRegistered', {
                frameworkType,
                factory: factory.name
            });

            this.logger.debug('适配器工厂注册成功', {
                frameworkType,
                factoryName: factory.name
            });
        } catch (error) {
            this.logger.error('适配器工厂注册失败', error, { frameworkType });
            throw createError('ADAPTER_FACTORY_REGISTRATION_FAILED',
                `适配器工厂注册失败: ${error.message}`, error);
        }
    }

    /**
     * 注销适配器工厂
     */
    unregisterFactory(frameworkType: FrameworkType): boolean {
        const removed = this.factories.delete(frameworkType);
        if (removed) {
            this.emit('factoryUnregistered', { frameworkType });
            this.logger.debug('适配器工厂注销成功', { frameworkType });
        }
        return removed;
    }

    /**
     * 创建适配器实例
     */
    async createAdapter(
        adapterId: string,
        frameworkType: FrameworkType,
        config: AdapterConfig = {}
    ): Promise<BaseAdapter> {
        try {
            if (this.adapters.has(adapterId)) {
                throw createError('ADAPTER_ALREADY_EXISTS',
                    `适配器实例已存在: ${adapterId}`);
            }

            const factory = this.factories.get(frameworkType);
            if (!factory) {
                throw createError('ADAPTER_FACTORY_NOT_FOUND',
                    `未找到适配器工厂: ${frameworkType}`);
            }

            // 创建适配器实例
            const instance = await factory.create(config);
            const now = Date.now();

            // 创建实例信息
            const instanceInfo: AdapterInstance = {
                id: adapterId,
                instance,
                state: 'created',
                config,
                createdAt: now,
                lastActivity: now
            };

            this.adapters.set(adapterId, instanceInfo);

            this.emit('adapterCreated', {
                adapterId,
                frameworkType,
                instance
            });

            this.logger.debug('适配器实例创建成功', {
                adapterId,
                frameworkType
            });

            return instance;
        } catch (error) {
            this.logger.error('适配器实例创建失败', error, {
                adapterId,
                frameworkType
            });
            throw createError('ADAPTER_CREATION_FAILED',
                `适配器实例创建失败: ${error.message}`, error);
        }
    }

    /**
     * 初始化适配器
     */
    async initializeAdapter(adapterId: string): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(adapterId);
            if (instanceInfo.state !== 'created') {
                throw createError('INVALID_ADAPTER_STATE',
                    `适配器状态无效，无法初始化: ${instanceInfo.state}`);
            }

            this.updateState(adapterId, 'initializing');

            await instanceInfo.instance.initialize();

            this.updateState(adapterId, 'initialized');
            instanceInfo.lastActivity = Date.now();

            this.emit('adapterInitialized', { adapterId });
            this.logger.debug('适配器初始化成功', { adapterId });
        } catch (error) {
            this.handleAdapterError(adapterId, error);
            throw createError('ADAPTER_INITIALIZATION_FAILED',
                `适配器初始化失败: ${error.message}`, error);
        }
    }

    /**
     * 挂载适配器
     */
    async mountAdapter(
        adapterId: string,
        container: HTMLElement,
        props?: any
    ): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(adapterId);
            if (instanceInfo.state !== 'initialized') {
                throw createError('INVALID_ADAPTER_STATE',
                    `适配器状态无效，无法挂载: ${instanceInfo.state}`);
            }

            this.updateState(adapterId, 'mounting');

            await instanceInfo.instance.mount(container, props);

            this.updateState(adapterId, 'mounted');
            instanceInfo.lastActivity = Date.now();

            this.emit('adapterMounted', { adapterId, container });
            this.logger.debug('适配器挂载成功', { adapterId });
        } catch (error) {
            this.handleAdapterError(adapterId, error);
            throw createError('ADAPTER_MOUNT_FAILED',
                `适配器挂载失败: ${error.message}`, error);
        }
    }

    /**
     * 卸载适配器
     */
    async unmountAdapter(adapterId: string): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(adapterId);
            if (instanceInfo.state !== 'mounted') {
                throw createError('INVALID_ADAPTER_STATE',
                    `适配器状态无效，无法卸载: ${instanceInfo.state}`);
            }

            this.updateState(adapterId, 'unmounting');

            await instanceInfo.instance.unmount();

            this.updateState(adapterId, 'unmounted');
            instanceInfo.lastActivity = Date.now();

            this.emit('adapterUnmounted', { adapterId });
            this.logger.debug('适配器卸载成功', { adapterId });
        } catch (error) {
            this.handleAdapterError(adapterId, error);
            throw createError('ADAPTER_UNMOUNT_FAILED',
                `适配器卸载失败: ${error.message}`, error);
        }
    }

    /**
     * 销毁适配器
     */
    async destroyAdapter(adapterId: string): Promise<void> {
        try {
            const instanceInfo = this.getInstanceInfo(adapterId);

            this.updateState(adapterId, 'destroying');

            await instanceInfo.instance.destroy();

            this.adapters.delete(adapterId);

            this.emit('adapterDestroyed', { adapterId });
            this.logger.debug('适配器销毁成功', { adapterId });
        } catch (error) {
            this.logger.error('适配器销毁失败', error, { adapterId });
            throw createError('ADAPTER_DESTRUCTION_FAILED',
                `适配器销毁失败: ${error.message}`, error);
        }
    }

    /**
     * 获取适配器实例
     */
    getAdapter(adapterId: string): BaseAdapter | undefined {
        const instanceInfo = this.adapters.get(adapterId);
        return instanceInfo?.instance;
    }

    /**
     * 获取适配器状态
     */
    getAdapterState(adapterId: string): AdapterState | undefined {
        const instanceInfo = this.adapters.get(adapterId);
        return instanceInfo?.state;
    }

    /**
     * 获取适配器配置
     */
    getAdapterConfig(adapterId: string): AdapterConfig | undefined {
        const instanceInfo = this.adapters.get(adapterId);
        return instanceInfo?.config;
    }

    /**
     * 检查适配器是否存在
     */
    hasAdapter(adapterId: string): boolean {
        return this.adapters.has(adapterId);
    }

    /**
     * 获取所有适配器ID
     */
    getAdapterIds(): string[] {
        return Array.from(this.adapters.keys());
    }

    /**
     * 获取指定状态的适配器
     */
    getAdaptersByState(state: AdapterState): string[] {
        const adapters: string[] = [];
        for (const [id, info] of this.adapters) {
            if (info.state === state) {
                adapters.push(id);
            }
        }
        return adapters;
    }

    /**
     * 获取支持的框架类型
     */
    getSupportedFrameworks(): FrameworkType[] {
        return Array.from(this.factories.keys());
    }

    /**
     * 检查框架是否支持
     */
    isFrameworkSupported(frameworkType: FrameworkType): boolean {
        return this.factories.has(frameworkType);
    }

    /**
     * 自动检测框架类型
     */
    detectFramework(container: HTMLElement): FrameworkType | null {
        try {
            // React 检测
            if (this.detectReact(container)) {
                return 'react';
            }

            // Vue 检测
            const vueVersion = this.detectVue(container);
            if (vueVersion) {
                return vueVersion === 2 ? 'vue2' : 'vue3';
            }

            // Angular 检测
            if (this.detectAngular(container)) {
                return 'angular';
            }

            // Svelte 检测
            if (this.detectSvelte(container)) {
                return 'svelte';
            }

            // Solid 检测
            if (this.detectSolid(container)) {
                return 'solid';
            }

            // 默认返回 HTML
            return 'html';
        } catch (error) {
            this.logger.error('框架检测失败', error);
            return null;
        }
    }

    /**
     * 批量创建适配器
     */
    async createAdapters(
        configs: Array<{
            id: string;
            frameworkType: FrameworkType;
            config?: AdapterConfig;
        }>
    ): Promise<BaseAdapter[]> {
        const adapters: BaseAdapter[] = [];
        const errors: Error[] = [];

        for (const { id, frameworkType, config } of configs) {
            try {
                const adapter = await this.createAdapter(id, frameworkType, config);
                adapters.push(adapter);
            } catch (error) {
                errors.push(error as Error);
                this.logger.error('批量创建适配器失败', error, { id, frameworkType });
            }
        }

        if (errors.length > 0) {
            this.logger.warn('部分适配器创建失败', { errorCount: errors.length });
        }

        return adapters;
    }

    /**
     * 获取适配器统计信息
     */
    getStats(): {
        totalAdapters: number;
        adaptersByState: Record<AdapterState, number>;
        adaptersByFramework: Record<FrameworkType, number>;
        supportedFrameworks: number;
        averageLifetime: number;
    } {
        const totalAdapters = this.adapters.size;
        const adaptersByState: Record<AdapterState, number> = {} as any;
        const adaptersByFramework: Record<FrameworkType, number> = {} as any;
        let totalLifetime = 0;

        for (const instanceInfo of this.adapters.values()) {
            // 统计状态
            const state = instanceInfo.state;
            adaptersByState[state] = (adaptersByState[state] || 0) + 1;

            // 统计生命周期
            totalLifetime += Date.now() - instanceInfo.createdAt;
        }

        // 统计框架类型
        for (const frameworkType of this.factories.keys()) {
            adaptersByFramework[frameworkType] = 0;
        }

        const averageLifetime = totalAdapters > 0 ? totalLifetime / totalAdapters : 0;

        return {
            totalAdapters,
            adaptersByState,
            adaptersByFramework,
            supportedFrameworks: this.factories.size,
            averageLifetime
        };
    }

    /**
     * 清理所有适配器
     */
    async cleanup(): Promise<void> {
        const adapterIds = Array.from(this.adapters.keys());

        for (const adapterId of adapterIds) {
            try {
                const state = this.getAdapterState(adapterId);

                // 按状态顺序清理
                if (state === 'mounted') {
                    await this.unmountAdapter(adapterId);
                }
                if (['initialized', 'unmounted'].includes(state!)) {
                    await this.destroyAdapter(adapterId);
                }
            } catch (error) {
                this.logger.error('适配器清理失败', error, { adapterId });
            }
        }

        this.factories.clear();
        this.registry.clear();
        this.logger.info('适配器管理器清理完成');
    }

    /**
     * 获取实例信息
     */
    private getInstanceInfo(adapterId: string): AdapterInstance {
        const instanceInfo = this.adapters.get(adapterId);
        if (!instanceInfo) {
            throw createError('ADAPTER_NOT_FOUND', `适配器实例不存在: ${adapterId}`);
        }
        return instanceInfo;
    }

    /**
     * 更新适配器状态
     */
    private updateState(adapterId: string, newState: AdapterState): void {
        const instanceInfo = this.getInstanceInfo(adapterId);
        const oldState = instanceInfo.state;
        instanceInfo.state = newState;

        this.emit('stateChanged', { adapterId, oldState, newState });
        this.logger.debug('适配器状态更新', { adapterId, oldState, newState });
    }

    /**
     * 处理适配器错误
     */
    private handleAdapterError(adapterId: string, error: Error): void {
        const instanceInfo = this.adapters.get(adapterId);
        if (instanceInfo) {
            instanceInfo.lastError = error;
            instanceInfo.state = 'error';
            this.emit('adapterError', { adapterId, error });
        }
        this.logger.error('适配器错误', error, { adapterId });
    }

    /**
     * 验证适配器工厂
     */
    private validateFactory(factory: AdapterFactory): void {
        if (!factory || typeof factory !== 'object') {
            throw createError('INVALID_ADAPTER_FACTORY', '适配器工厂必须是对象');
        }

        if (!factory.name || typeof factory.name !== 'string') {
            throw createError('INVALID_ADAPTER_FACTORY', '适配器工厂必须包含名称');
        }

        if (!factory.create || typeof factory.create !== 'function') {
            throw createError('INVALID_ADAPTER_FACTORY', '适配器工厂必须包含create方法');
        }
    }

    /**
     * 初始化内置适配器
     */
    private initializeBuiltinAdapters(): void {
        // 这里可以注册内置的适配器工厂
        // 实际的工厂实现会在各自的适配器包中定义
        this.logger.debug('内置适配器初始化完成');
    }

    /**
     * 检测 React
     */
    private detectReact(container: HTMLElement): boolean {
        // 检查全局 React 对象
        if (typeof (window as any).React !== 'undefined') {
            return true;
        }

        // 检查 React Fiber 节点
        const reactFiberKey = Object.keys(container).find(key =>
            key.startsWith('__reactFiber') || key.startsWith('__reactInternalInstance')
        );

        return !!reactFiberKey;
    }

    /**
     * 检测 Vue
     */
    private detectVue(container: HTMLElement): number | null {
        // 检查全局 Vue 对象
        const vue = (window as any).Vue;
        if (vue) {
            // Vue 2.x
            if (vue.version && vue.version.startsWith('2.')) {
                return 2;
            }
            // Vue 3.x
            if (vue.version && vue.version.startsWith('3.')) {
                return 3;
            }
        }

        // 检查 Vue 实例
        const vueKey = Object.keys(container).find(key =>
            key.startsWith('__vue__') || key.startsWith('__vueParentComponent')
        );

        if (vueKey) {
            const vueInstance = (container as any)[vueKey];
            if (vueInstance && vueInstance.$options) {
                return 2; // Vue 2.x
            }
            if (vueInstance && vueInstance.component) {
                return 3; // Vue 3.x
            }
        }

        return null;
    }

    /**
     * 检测 Angular
     */
    private detectAngular(container: HTMLElement): boolean {
        // 检查全局 ng 对象
        if (typeof (window as any).ng !== 'undefined') {
            return true;
        }

        // 检查 Angular 属性
        const hasAngularAttributes = container.hasAttribute('ng-app') ||
            container.hasAttribute('ng-controller') ||
            container.querySelector('[ng-app]') !== null ||
            container.querySelector('[ng-controller]') !== null;

        return hasAngularAttributes;
    }

    /**
     * 检测 Svelte
     */
    private detectSvelte(container: HTMLElement): boolean {
        // 检查 Svelte 特有的属性
        const hasSvelteClass = container.className.includes('svelte-') ||
            container.querySelector('[class*="svelte-"]') !== null;

        return hasSvelteClass;
    }

    /**
     * 检测 Solid
     */
    private detectSolid(container: HTMLElement): boolean {
        // 检查全局 Solid 对象
        if (typeof (window as any).Solid !== 'undefined') {
            return true;
        }

        // 检查 Solid 特有的属性
        const hasSolidData = container.hasAttribute('data-solid') ||
            container.querySelector('[data-solid]') !== null;

        return hasSolidData;
    }
}