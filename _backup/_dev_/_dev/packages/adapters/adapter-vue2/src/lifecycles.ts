/**
 * Vue 2 生命周期管理
 */

import type { Vue2AdapterOptions } from './index';

export interface Vue2AppConfig {
    /** 根组件 */
    rootComponent?: any;
    /** 应用配置 */
    appOptions?: any;
    /** 路由配置 */
    router?: any;
    /** 状态管理 */
    store?: any;
    /** 插件列表 */
    plugins?: Array<{
        plugin: any;
        options?: any;
    }>;
}

export class Vue2Lifecycles {
    private appName: string;
    private appConfig: Vue2AppConfig;
    private options: Vue2AdapterOptions;
    private vueInstance: any = null;
    private container: HTMLElement | null = null;

    constructor(appName: string, appConfig: Vue2AppConfig, options: Vue2AdapterOptions) {
        this.appName = appName;
        this.appConfig = appConfig;
        this.options = options;
    }

    /**
     * 获取生命周期函数
     */
    getLifecycles() {
        return {
            bootstrap: this.bootstrap.bind(this),
            mount: this.mount.bind(this),
            unmount: this.unmount.bind(this),
            update: this.update.bind(this)
        };
    }

    /**
     * 启动阶段
     */
    async bootstrap(props: any): Promise<void> {
        console.log(`[Vue2Lifecycles:${this.appName}] 启动阶段`, props);

        // Vue 2 的初始化逻辑
        if (this.options.enableDevtools !== undefined) {
            // Vue.config.devtools = this.options.enableDevtools;
        }

        if (this.options.enableProductionTip !== undefined) {
            // Vue.config.productionTip = this.options.enableProductionTip;
        }

        if (this.options.errorHandler) {
            // Vue.config.errorHandler = this.options.errorHandler;
        }
    }

    /**
     * 挂载阶段
     */
    async mount(props: any): Promise<void> {
        console.log(`[Vue2Lifecycles:${this.appName}] 挂载阶段`, props);

        // 获取容器元素
        this.container = this.getContainer(props);

        if (this.container && this.appConfig.rootComponent) {
            // 创建 Vue 2 实例配置
            const vueConfig: any = {
                ...this.appConfig.appOptions,
                el: this.container,
                render: (h: any) => h(this.appConfig.rootComponent, { props })
            };

            // 添加路由
            if (this.appConfig.router) {
                vueConfig.router = this.appConfig.router;
            }

            // 添加状态管理
            if (this.appConfig.store) {
                vueConfig.store = this.appConfig.store;
            }

            // 创建 Vue 实例（这里需要实际的 Vue 2 依赖）
            // this.vueInstance = new Vue(vueConfig);

            console.log(`[Vue2Lifecycles:${this.appName}] Vue 2 应用已挂载`);
        } else {
            throw new Error(`[Vue2Lifecycles:${this.appName}] 无法找到挂载容器或根组件未定义`);
        }
    }

    /**
     * 卸载阶段
     */
    async unmount(props: any): Promise<void> {
        console.log(`[Vue2Lifecycles:${this.appName}] 卸载阶段`, props);

        if (this.vueInstance) {
            // 销毁 Vue 实例
            this.vueInstance.$destroy();
            this.vueInstance = null;
            console.log(`[Vue2Lifecycles:${this.appName}] Vue 2 应用已卸载`);
        }

        // 清理容器
        if (this.container) {
            this.container.innerHTML = '';
            this.container = null;
        }
    }

    /**
     * 更新阶段
     */
    async update(props: any): Promise<void> {
        console.log(`[Vue2Lifecycles:${this.appName}] 更新阶段`, props);

        // Vue 2 应用的更新逻辑
        if (this.vueInstance && props) {
            // 更新实例数据
            Object.assign(this.vueInstance.$data, props);
        }
    }

    /**
     * 获取挂载容器
     */
    private getContainer(props: any): HTMLElement {
        const { container } = props;

        if (typeof container === 'string') {
            const element = document.querySelector(container);
            if (!element) {
                throw new Error(`[Vue2Lifecycles:${this.appName}] 无法找到容器: ${container}`);
            }
            return element as HTMLElement;
        }

        if (container instanceof HTMLElement) {
            return container;
        }

        // 默认容器
        const defaultContainer = document.getElementById(`micro-app-${this.appName}`) ||
            document.getElementById('root') ||
            document.body;

        return defaultContainer as HTMLElement;
    }

    /**
     * 销毁生命周期
     */
    destroy(): void {
        if (this.vueInstance) {
            this.vueInstance.$destroy();
            this.vueInstance = null;
        }

        if (this.container) {
            this.container.innerHTML = '';
            this.container = null;
        }

        console.log(`[Vue2Lifecycles:${this.appName}] 生命周期已销毁`);
    }

    /**
     * 获取 Vue 实例
     */
    getVueInstance(): any {
        return this.vueInstance;
    }
}