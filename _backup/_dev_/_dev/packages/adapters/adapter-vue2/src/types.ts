/**
 * Vue 2 Adapter Types
 */

import type Vue from 'vue';
import type { AppConfig, LifecycleHooks, SandboxConfig } from '@micro-core/core';

export interface Vue2AdapterConfig {
  /** Vue 2 version compatibility */
  vueVersion?: string;
  /** Enable Vue DevTools integration */
  enableDevTools?: boolean;
  /** Global Vue configuration */
  globalConfig?: Partial<Vue.Config>;
  /** Custom error handler */
  errorHandler?: (error: Error, vm: Vue, info: string) => void;
  /** Vue 2-specific sandbox configuration */
  sandbox?: Vue2SandboxConfig;
}

export interface Vue2AppConfig extends AppConfig {
  /** Vue 2 adapter specific configuration */
  vue2?: Vue2AdapterConfig;
  /** Vue component options */
  component?: Vue2ComponentOptions;
  /** Vue instance options */
  vueOptions?: Vue2ComponentOptions;
  /** Props to pass to the Vue component */
  props?: Record<string, any>;
  /** Vue DOM container selector or element */
  container?: string | HTMLElement;
}

export interface Vue2LifecycleHooks extends LifecycleHooks {
  /** Called before Vue instance is created */
  beforeCreate?: () => Promise<void> | void;
  /** Called after Vue instance is created */
  created?: () => Promise<void> | void;
  /** Called before Vue component mounts */
  beforeMount?: () => Promise<void> | void;
  /** Called after Vue component mounts */
  mounted?: () => Promise<void> | void;
  /** Called before Vue component updates */
  beforeUpdate?: () => Promise<void> | void;
  /** Called after Vue component updates */
  updated?: () => Promise<void> | void;
  /** Called before Vue component unmounts */
  beforeDestroy?: () => Promise<void> | void;
  /** Called after Vue component unmounts */
  destroyed?: () => Promise<void> | void;
  /** Called when Vue component encounters an error */
  errorCaptured?: (error: Error, vm: Vue, info: string) => Promise<boolean | void> | boolean | void;
}

export interface Vue2ComponentOptions {
  /** Component template */
  template?: string;
  /** Render function */
  render?: (h: Vue.CreateElement) => Vue.VNode;
  /** Component data */
  data?: () => any;
  /** Component props */
  props?: string[] | Record<string, any>;
  /** Component computed properties */
  computed?: Record<string, any>;
  /** Component methods */
  methods?: Record<string, any>;
  /** Component watchers */
  watch?: Record<string, any>;
  /** Component lifecycle hooks */
  beforeCreate?: () => void;
  created?: () => void;
  beforeMount?: () => void;
  mounted?: () => void;
  beforeUpdate?: () => void;
  updated?: () => void;
  beforeDestroy?: () => void;
  destroyed?: () => void;
  errorCaptured?: (error: Error, vm: Vue, info: string) => boolean | void;
  /** Component mixins */
  mixins?: any[];
  /** Component directives */
  directives?: Record<string, any>;
  /** Component filters */
  filters?: Record<string, any>;
  /** Component components */
  components?: Record<string, any>;
}

export interface Vue2ErrorInfo {
  /** Vue instance */
  vm: Vue;
  /** Error information */
  info: string;
  /** Component name */
  componentName?: string;
  /** Component hierarchy */
  componentHierarchy?: string[];
}

export interface Vue2SandboxConfig extends SandboxConfig {
  /** Isolate Vue global */
  isolateVueGlobal?: boolean;
  /** Preserve Vue DevTools */
  preserveDevTools?: boolean;
  /** Custom Vue plugins */
  plugins?: any[];
  /** Custom Vue mixins */
  mixins?: any[];
}

export interface Vue2HookContext {
  /** App configuration */
  config: Vue2AppConfig;
  /** Vue instance */
  vue: Vue;
  /** Lifecycle manager */
  lifecycle: any;
  /** Communication manager */
  communication: any;
  /** Sandbox manager */
  sandbox: any;
}

export type Vue2AppInstance = {
  /** Vue instance */
  vue: Vue;
  /** DOM container */
  container: HTMLElement;
  /** App configuration */
  config: Vue2AppConfig;
  /** Lifecycle hooks */
  hooks: Vue2LifecycleHooks;
  /** Mount the Vue app */
  mount: () => Promise<void>;
  /** Unmount the Vue app */
  unmount: () => Promise<void>;
  /** Update the Vue app */
  update: (props: any) => Promise<void>;
};
