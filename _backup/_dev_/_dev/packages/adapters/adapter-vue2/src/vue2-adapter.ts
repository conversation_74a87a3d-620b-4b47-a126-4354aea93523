/**
 * Vue 2 Adapter Implementation
 * @description Vue 2 微前端适配器，基于新的 BaseAdapter 基础设施
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import {
  AdapterDependencies,
  BaseAdapter
} from '@micro-core/shared/utils';
import Vue from 'vue';
import type {
  Vue2AppConfig,
  Vue2AppInstance,
  Vue2ComponentOptions,
  Vue2LifecycleHooks
} from './types';
import {
  isVue2App,
  validateVue2Config
} from './utils';

/**
 * Vue 2 适配器类
 * @description 实现 Vue 2 微前端应用的加载、挂载、卸载等生命周期管理
 */
export class Vue2Adapter extends BaseAdapter<Vue2AppConfig> {
  private vueInstance: Vue | null = null;
  private appInstance: Vue2AppInstance | null = null;
  private component: Vue2ComponentOptions | null = null;

  constructor(
    config: Vue2AppConfig,
    dependencies: AdapterDependencies
  ) {
    super(config, dependencies);
  }

  /**
   * 检查是否能处理指定的应用配置
   * @param appConfig 应用配置
   * @returns 是否能处理
   */
  canHandle(appConfig: any): boolean {
    return isVue2App(appConfig);
  }

  /**
   * 加载 Vue 2 应用
   * @param appConfig 应用配置
   */
  protected async doLoadApp(appConfig: Vue2AppConfig): Promise<Vue2AppInstance> {
    // 验证配置
    validateVue2Config(appConfig);

    // 准备组件
    this.component = await this.prepareComponent(appConfig);

    // 创建应用实例
    this.appInstance = {
      vue: null as any, // 将在挂载时设置
      container: null as any, // 将在挂载时设置
      config: appConfig,
      hooks: (appConfig as any).hooks as Vue2LifecycleHooks || {},
      mount: () => this.doMountApp(this.appInstance!),
      unmount: () => this.doUnmountApp(this.appInstance!),
      update: (props: any) => this.doUpdateApp(this.appInstance!, props)
    };

    return this.appInstance;
  }

  /**
   * 挂载 Vue 2 应用
   */
  protected async doMountApp(app: Vue2AppInstance): Promise<void> {
    if (!this.component) {
      throw new Error('组件未准备就绪');
    }

    // 准备容器
    app.container = this.prepareContainer();

    // 执行生命周期钩子
    await this.executeLifecycleHook(app.config.hooks?.beforeMount, app);

    // 创建 Vue 实例
    this.vueInstance = new Vue({
      ...this.component,
      el: app.container,
      data: () => ({
        ...this.component!.data?.() || {},
        microAppProps: app.config.props || {}
      }),
      beforeCreate: () => {
        // 执行 beforeCreate 钩子
        if (app.hooks.beforeCreate) {
          app.hooks.beforeCreate();
        }
      },
      created: () => {
        // 执行 created 钩子
        if (app.hooks.created) {
          app.hooks.created();
        }
      },
      mounted: () => {
        // 执行 mounted 钩子
        if (app.hooks.mounted) {
          app.hooks.mounted();
        }
      },
      errorCaptured: (error: Error, vm: Vue, info: string) => {
        // 执行错误捕获钩子
        if (app.hooks.errorCaptured) {
          return app.hooks.errorCaptured(error, vm, info);
        }

        const formattedError = formatVue2Error(error, vm, info);
        console.error(formattedError);

        this.communicationManager.emit('app:error', {
          appName: app.config.name,
          error: formattedError
        });

        return false;
      }
    });

    app.vue = this.vueInstance;

    // 执行生命周期钩子
    await this.executeLifecycleHook((app.config as any).hooks?.afterMount, app);
  }

  /**
   * 卸载 Vue 2 应用
   */
  protected async doUnmountApp(app: Vue2AppInstance): Promise<void> {
    if (!this.vueInstance) {
      return;
    }

    // 执行生命周期钩子
    await this.executeLifecycleHook((app.config as any).hooks?.beforeUnmount, app);

    // 销毁 Vue 实例
    this.vueInstance.$destroy();
    this.vueInstance = null;

    // 清理容器
    if (app.container) {
      cleanupVue2Container(app.container);
    }

    // 销毁沙箱
    if (app.config.sandbox) {
      await this.sandboxManager.destroySandbox(app.config.name);
    }

    // 清理应用实例
    this.appInstance = null;
    this.component = null;

    // 执行生命周期钩子
    await this.executeLifecycleHook((app.config as any).hooks?.afterUnmount, app);
  }

  /**
   * 更新 Vue 2 应用属性
   * @param app 应用实例
   * @param props 新的属性
   */
  protected async doUpdateApp(app: Vue2AppInstance, props?: Record<string, any>): Promise<void> {
    if (!this.vueInstance) {
      throw new Error('应用未挂载');
    }

    if (!props) return;

    // 更新配置中的 props
    app.config.props = { ...app.config.props, ...props };

    // 更新 Vue 实例数据
    Object.assign(this.vueInstance.$data.microAppProps, props);

    // 执行更新钩子
    if (app.hooks.updated) {
      await app.hooks.updated();
    }

    // 触发通信事件
    this.communicationManager.emit('app:props-updated', {
      appName: app.config.name,
      props: app.config.props
    });
  }

  /**
   * 获取应用实例
   * @returns 应用实例
   */
  getAppInstance(): Vue2AppInstance | null {
    return this.appInstance;
  }

  /**
   * 准备 Vue 2 组件
   * @param appConfig 应用配置
   * @returns Vue 2 组件选项
   */
  private async prepareComponent(appConfig: Vue2AppConfig): Promise<Vue2ComponentOptions> {
    if ((appConfig as any).component) {
      // 直接使用提供的组件
      return (appConfig as any).component;
    }

    if ((appConfig as any).vueOptions) {
      return (appConfig as any).vueOptions;
    }

    if (appConfig.entry) {
      // 从入口加载组件
      const module = await this.loadModule(appConfig.entry);
      return extractVue2Component(module);
    }

    throw new Error('必须提供 component、vueOptions 或 entry');
  }

  /**
   * 加载模块
   * @param entry 入口地址
   * @returns 模块对象
   */
  private async loadModule(entry: string | any): Promise<any> {
    try {
      const entryUrl = typeof entry === 'string' ? entry : entry.url || entry.src;

      if (entryUrl.startsWith('http')) {
        // 远程模块
        return await this.loadRemoteModule(entryUrl);
      } else {
        // 本地模块
        return await import(entryUrl);
      }
    } catch (error) {
      throw new Error(`从 ${entry} 加载 Vue 模块失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 准备容器元素
   * @returns 容器元素
   */
  protected prepareContainer(): HTMLElement {
    const container = super.prepareContainer();

    // 添加 Vue 2 特定的属性
    container.className += ' vue2-app-container';
    container.setAttribute('data-framework', 'vue2');

    // 检查 Vue 2 版本兼容性
    const vue2Config = (this.config as any).vue2;
    if (vue2Config?.vueVersion) {
      this.checkVersionCompatibility(vue2Config.vueVersion);
    }

    return container;
  }

  /**
   * 检查 Vue 2 版本兼容性
   * @param requiredVersion 要求的版本
   * @returns 是否兼容
   */
  private checkVersionCompatibility(requiredVersion?: string): boolean {
    if (!requiredVersion) return true;

    const currentVersion = getVue2Version();
    if (!currentVersion) {
      console.warn('无法检测 Vue 2 版本');
      return true; // 无法检测时假设兼容
    }

    const compatible = isVue2VersionCompatible(currentVersion, requiredVersion);
    if (!compatible) {
      console.warn(
        `Vue 2 版本不匹配。当前: ${currentVersion}, 要求: ${requiredVersion}`
      );
    }

    return compatible;
  }

  /**
   * 加载远程模块
   * @param url 远程模块地址
   * @returns 模块对象
   */
  private async loadRemoteModule(url: string): Promise<any> {
    // 实现远程 Vue 模块加载
    const response = await fetch(url);
    const code = await response.text();

    // 创建沙箱并执行模块
    const moduleFunction = new Function('exports', 'require', 'module', 'Vue', code);
    const module = { exports: {} };
    moduleFunction(module.exports, require, module, Vue);

    return module.exports;
  }
}

/**
 * 创建 Vue 2 适配器实例
 * @param config 应用配置
 * @param dependencies 依赖注入
 * @returns Vue 2 适配器实例
 */
export function createVue2Adapter(
  config: Vue2AppConfig,
  dependencies: AdapterDependencies
): Vue2Adapter {
  return new Vue2Adapter(config, dependencies);
}

/**
 * Vue 2 适配器工厂函数
 * @description 用于适配器注册表的工厂函数
 */
export const Vue2AdapterFactory = {
  type: 'vue2',
  create: createVue2Adapter,
  canHandle: isVue2App,
  metadata: {
    name: 'Vue 2 Adapter',
    version: '2.0.0',
    description: 'Vue 2 微前端适配器',
    supportedVersions: ['2.6+', '2.7.x'],
    author: 'Echo <<EMAIL>>'
  }
};

export default Vue2Adapter;
