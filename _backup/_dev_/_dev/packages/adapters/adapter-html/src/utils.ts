/**
 * HTML 适配器工具函数
 * 使用 shared 包的通用工具函数实现
 */

import {
    cleanupContainer,
    createAdapterErrorInfo,
    createEnhancedContainer,
    formatAdapterError,
    isObject,
    isString,
    mergeConfigs
} from '@micro-core/shared/utils';
import type {
    HtmlAppInstance,
    HtmlConfig
} from './types';

/**
 * 创建 HTML 适配器
 */
export function createHtmlAdapter(config: HtmlConfig): HtmlAppInstance {
    const finalConfig = createDefaultHtmlConfig(config);

    return {
        name: finalConfig.name,
        config: finalConfig,
        mount: async (element: HTMLElement) => {
            try {
                const container = createHtmlContainer(finalConfig.name, element);
                await loadHtmlContent(finalConfig, container);
                return { container };
            } catch (error) {
                throw formatHtmlError(error, 'mount', finalConfig.name);
            }
        },
        unmount: async (instance: any) => {
            try {
                if (instance?.container) {
                    cleanupContainer(instance.container);
                }
            } catch (error) {
                throw formatHtmlError(error, 'unmount', finalConfig.name);
            }
        }
    };
}

/**
 * 检查是否为 HTML 应用
 */
export function isHtmlApp(app: any): boolean {
    if (!isObject(app)) return false;

    // 检查 HTML 内容特征
    if (app.html || app.template) return true;

    // 检查 HTML 文件特征
    if (app.htmlFile || app.templateFile) return true;

    // 检查 HTML 配置特征
    if (app.htmlConfig) return true;

    return false;
}

/**
 * 检查是否为 HTML 入口
 */
export function isHtmlEntry(entry: any): boolean {
    if (!isObject(entry)) return false;

    // 检查是否有 HTML 导出
    if (entry.html || entry.template) return true;

    // 检查是否有 HTML 文件导出
    if (entry.htmlFile || entry.templateFile) return true;

    // 检查是否有 HTML 配置导出
    if (entry.htmlConfig) return true;

    return false;
}

/**
 * 验证 HTML 配置
 */
export function validateHtmlConfig(config: Partial<HtmlConfig>): string[] {
    const errors: string[] = [];

    if (!config.name || !isString(config.name)) {
        errors.push('配置中缺少有效的应用名称');
    }

    if (!config.html && !config.htmlFile) {
        errors.push('配置中缺少 HTML 内容或文件路径');
    }

    if (config.html && config.htmlFile) {
        errors.push('不能同时指定 HTML 内容和文件路径');
    }

    if (config.sandbox !== undefined && typeof config.sandbox !== 'boolean') {
        errors.push('sandbox 必须是布尔值');
    }

    return errors;
}

/**
 * 创建默认 HTML 配置
 */
export function createDefaultHtmlConfig(config: Partial<HtmlConfig>): HtmlConfig {
    const defaultConfig: HtmlConfig = {
        name: '',
        html: '',
        htmlFile: '',
        sandbox: false,
        allowScripts: true,
        allowStyles: true,
        container: {
            className: 'html-app-container',
            style: {}
        },
        lifecycle: {}
    };

    return mergeConfigs(defaultConfig, config);
}

/**
 * 提取 HTML 内容
 * 重构为多个简单函数以提高可测试性
 */
export function extractHtmlContent(moduleExports: any, preferredName?: string): string {
    if (!isObject(moduleExports)) {
        throw new Error('模块导出必须是对象');
    }

    // 1. 检查首选内容
    const preferredContent = checkPreferredHtmlContent(moduleExports, preferredName);
    if (preferredContent) return preferredContent;

    // 2. 检查默认导出
    const defaultContent = checkDefaultHtmlContent(moduleExports);
    if (defaultContent) return defaultContent;

    // 3. 获取命名导出
    const namedContents = getNamedHtmlContents(moduleExports);
    if (namedContents.length > 0) {
        return selectBestHtmlContent(namedContents);
    }

    throw new Error('未找到有效的 HTML 内容');
}

/**
 * 检查首选 HTML 内容
 */
function checkPreferredHtmlContent(moduleExports: any, preferredName?: string): string | null {
    if (!preferredName) return null;

    const content = moduleExports[preferredName];
    if (content && isHtmlContent(content)) {
        return content;
    }

    return null;
}

/**
 * 检查默认 HTML 内容
 */
function checkDefaultHtmlContent(moduleExports: any): string | null {
    const defaultExport = moduleExports.default;
    if (defaultExport && isHtmlContent(defaultExport)) {
        return defaultExport;
    }

    return null;
}

/**
 * 获取命名 HTML 内容
 */
function getNamedHtmlContents(moduleExports: any): string[] {
    const contents: string[] = [];

    for (const [key, value] of Object.entries(moduleExports)) {
        if (key !== 'default' && isHtmlContent(value)) {
            contents.push(value as string);
        }
    }

    return contents;
}

/**
 * 选择最佳 HTML 内容
 */
function selectBestHtmlContent(contents: string[]): string {
    // 优先选择包含更多标签的内容
    return contents.sort((a, b) => {
        const aTagCount = (a.match(/<[^>]+>/g) || []).length;
        const bTagCount = (b.match(/<[^>]+>/g) || []).length;
        return bTagCount - aTagCount;
    })[0];
}

/**
 * 检查是否为 HTML 内容
 */
export function isHtmlContent(content: any): boolean {
    if (!isString(content)) return false;

    // 检查是否包含 HTML 标签
    const htmlTagRegex = /<[^>]+>/;
    return htmlTagRegex.test(content);
}

/**
 * 检查是否为增强的 HTML 内容
 */
export function isHtmlContentEnhanced(content: any): boolean {
    if (!isHtmlContent(content)) return false;

    // 检查是否包含微应用特定标记
    const microAppRegex = /data-micro-app|micro-app-/i;
    return microAppRegex.test(content);
}

/**
 * 创建 HTML 容器
 */
export function createHtmlContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
    return createEnhancedContainer(appName, 'html', parentElement, {
        className: 'html-app-container',
        attributes: {
            'data-framework': 'html',
            'data-app': appName
        }
    });
}

/**
 * 加载 HTML 内容
 */
async function loadHtmlContent(config: HtmlConfig, container: HTMLElement): Promise<void> {
    let htmlContent = '';

    if (config.html) {
        htmlContent = config.html;
    } else if (config.htmlFile) {
        htmlContent = await fetchHtmlFile(config.htmlFile);
    }

    if (!htmlContent) {
        throw new Error('未找到有效的 HTML 内容');
    }

    // 处理沙箱模式
    if (config.sandbox) {
        htmlContent = sanitizeHtmlContent(htmlContent, config);
    }

    // 插入 HTML 内容
    container.innerHTML = htmlContent;

    // 执行脚本（如果允许）
    if (config.allowScripts) {
        executeScripts(container);
    }
}

/**
 * 获取 HTML 文件内容
 */
async function fetchHtmlFile(filePath: string): Promise<string> {
    try {
        const response = await fetch(filePath);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.text();
    } catch (error) {
        throw new Error(`加载 HTML 文件失败: ${error.message}`);
    }
}

/**
 * 清理 HTML 内容（沙箱模式）
 */
function sanitizeHtmlContent(html: string, config: HtmlConfig): string {
    let sanitized = html;

    // 移除脚本标签（如果不允许脚本）
    if (!config.allowScripts) {
        sanitized = sanitized.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
        sanitized = sanitized.replace(/on\w+\s*=\s*["'][^"']*["']/gi, '');
    }

    // 移除样式标签（如果不允许样式）
    if (!config.allowStyles) {
        sanitized = sanitized.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
        sanitized = sanitized.replace(/style\s*=\s*["'][^"']*["']/gi, '');
    }

    return sanitized;
}

/**
 * 执行容器中的脚本
 */
function executeScripts(container: HTMLElement): void {
    const scripts = container.querySelectorAll('script');
    scripts.forEach(script => {
        const newScript = document.createElement('script');

        // 复制属性
        Array.from(script.attributes).forEach(attr => {
            newScript.setAttribute(attr.name, attr.value);
        });

        // 复制内容
        if (script.src) {
            newScript.src = script.src;
        } else {
            newScript.textContent = script.textContent;
        }

        // 替换原脚本
        script.parentNode?.replaceChild(newScript, script);
    });
}

/**
 * 格式化 HTML 错误
 */
export function formatHtmlError(error: any, operation: string, appName: string): Error {
    return formatAdapterError(error, 'HTML', operation, appName);
}

/**
 * 创建 HTML 错误信息
 */
export function createHtmlErrorInfo(error: any, context: any): any {
    return createAdapterErrorInfo(error, 'HTML', context);
}

/**
 * 合并 HTML 配置
 */
export function mergeHtmlConfigs(base: Partial<HtmlConfig>, override: Partial<HtmlConfig>): HtmlConfig {
    return mergeConfigs(base, override);
}

/**
 * HTML 微应用集成工具
 */
export class HtmlMicroAppIntegration {
    private container: HTMLElement | null = null;

    constructor(private config: HtmlConfig) { }

    async mount(element: HTMLElement): Promise<void> {
        try {
            this.container = createHtmlContainer(this.config.name, element);
            await loadHtmlContent(this.config, this.container);

            // 触发生命周期钩子
            await this.config.lifecycle?.mounted?.(this.container);
        } catch (error) {
            throw formatHtmlError(error, 'mount', this.config.name);
        }
    }

    async unmount(): Promise<void> {
        try {
            // 触发生命周期钩子
            await this.config.lifecycle?.beforeUnmount?.(this.container);

            // 清理容器
            if (this.container) {
                cleanupContainer(this.container);
                this.container = null;
            }

            // 触发生命周期钩子
            await this.config.lifecycle?.unmounted?.();
        } catch (error) {
            throw formatHtmlError(error, 'unmount', this.config.name);
        }
    }

    getContainer(): HTMLElement | null {
        return this.container;
    }

    async updateContent(html: string): Promise<void> {
        if (!this.container) {
            throw new Error('应用未挂载，无法更新内容');
        }

        try {
            // 更新配置
            this.config.html = html;

            // 重新加载内容
            await loadHtmlContent(this.config, this.container);

            // 触发生命周期钩子
            await this.config.lifecycle?.updated?.(this.container);
        } catch (error) {
            throw formatHtmlError(error, 'update', this.config.name);
        }
    }
}