name: 部署文档

on:
  push:
    branches: [main]
    paths: ['docs/**', 'packages/**']
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: pages
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'pnpm'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: 设置 Pages
        uses: actions/configure-pages@v3

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 构建包
        run: pnpm build

      - name: 构建文档
        run: pnpm docs:build

      - name: 上传构建产物
        uses: actions/upload-pages-artifact@v2
        with:
          path: docs/.vitepress/dist

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: 部署到 GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v2