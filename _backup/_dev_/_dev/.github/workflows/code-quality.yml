name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行
    - cron: '0 2 * * *'

jobs:
  # 代码质量分析
  sonarcloud:
    name: SonarCloud 分析
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 运行测试和覆盖率
        run: pnpm run test:coverage

      - name: SonarCloud 扫描
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # 依赖安全检查
  dependency-check:
    name: 依赖安全检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 运行 npm audit
        run: pnpm audit --audit-level moderate

      - name: 检查过期依赖
        run: pnpm outdated

      - name: 运行 Snyk 安全检查
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # 许可证检查
  license-check:
    name: 许可证检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 安装 license-checker
        run: npm install -g license-checker

      - name: 检查许可证
        run: |
          license-checker --onlyAllow 'MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC' --excludePrivatePackages

      - name: 生成许可证报告
        run: |
          license-checker --csv --out licenses.csv
          license-checker --json --out licenses.json

      - name: 上传许可证报告
        uses: actions/upload-artifact@v3
        with:
          name: license-report
          path: |
            licenses.csv
            licenses.json

  # 代码复杂度分析
  complexity-analysis:
    name: 代码复杂度分析
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 安装复杂度分析工具
        run: npm install -g complexity-report

      - name: 运行复杂度分析
        run: |
          find packages -name "*.ts" -not -path "*/node_modules/*" -not -path "*/dist/*" | \
          xargs complexity-report --format json --output complexity-report.json

      - name: 上传复杂度报告
        uses: actions/upload-artifact@v3
        with:
          name: complexity-report
          path: complexity-report.json

  # 性能回归测试
  performance-regression:
    name: 性能回归测试
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 构建项目
        run: pnpm run build

      - name: 运行性能基准测试
        run: pnpm run test:performance

      - name: 比较性能结果
        run: |
          # 这里可以添加与基准版本的性能对比逻辑
          echo "性能测试完成，结果已保存"

      - name: 上传性能报告
        uses: actions/upload-artifact@v3
        with:
          name: performance-regression-report
          path: performance-report/

  # 文档链接检查
  docs-link-check:
    name: 文档链接检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 构建文档
        run: pnpm run docs:build

      - name: 检查文档链接
        uses: lycheeverse/lychee-action@v1.8.0
        with:
          args: --verbose --no-progress 'docs/**/*.md'
          fail: true

  # 代码覆盖率趋势
  coverage-trend:
    name: 代码覆盖率趋势
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 运行测试覆盖率
        run: pnpm run test:coverage

      - name: 上传到 Coveralls
        uses: coverallsapp/github-action@v2
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: 覆盖率评论
        uses: romeovs/lcov-reporter-action@v0.3.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          lcov-file: ./coverage/lcov.info