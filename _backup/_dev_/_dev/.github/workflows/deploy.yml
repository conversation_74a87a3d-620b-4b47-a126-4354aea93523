name: Deploy

on:
  push:
    branches: [ main ]
  release:
    types: [ published ]
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # 部署到预发布环境
  deploy-staging:
    name: 部署到预发布环境
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment:
      name: staging
      url: https://staging.micro-core.dev
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 构建项目
        run: pnpm run build
        env:
          NODE_ENV: production
          VITE_API_BASE_URL: ${{ secrets.STAGING_API_URL }}

      - name: 构建文档
        run: pnpm run docs:build
        env:
          NODE_ENV: production
          VITE_BASE_URL: https://staging.micro-core.dev

      - name: 部署到 Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          scope: ${{ secrets.VERCEL_ORG_ID }}
          alias-domains: staging.micro-core.dev

      - name: 运行冒烟测试
        run: |
          sleep 30
          curl -f https://staging.micro-core.dev/health || exit 1
          curl -f https://staging.micro-core.dev/api/status || exit 1

      - name: 通知部署成功
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: |
            🚀 预发布环境部署成功！
            🔗 URL: https://staging.micro-core.dev
            📝 提交: ${{ github.sha }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    if: github.event_name == 'release' || github.event.inputs.environment == 'production'
    environment:
      name: production
      url: https://micro-core.dev
    needs: [deploy-staging]
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 构建项目
        run: pnpm run build
        env:
          NODE_ENV: production
          VITE_API_BASE_URL: ${{ secrets.PRODUCTION_API_URL }}

      - name: 构建文档
        run: pnpm run docs:build
        env:
          NODE_ENV: production
          VITE_BASE_URL: https://micro-core.dev

      - name: 部署到 Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./
          scope: ${{ secrets.VERCEL_ORG_ID }}
          alias-domains: micro-core.dev

      - name: 部署到 CDN
        run: |
          # 上传构建产物到 CDN
          aws s3 sync ./packages/core/dist s3://${{ secrets.CDN_BUCKET }}/core/ --delete
          aws s3 sync ./packages/plugins/dist s3://${{ secrets.CDN_BUCKET }}/plugins/ --delete
          aws s3 sync ./packages/adapters/dist s3://${{ secrets.CDN_BUCKET }}/adapters/ --delete
          aws s3 sync ./packages/builders/dist s3://${{ secrets.CDN_BUCKET }}/builders/ --delete
          aws s3 sync ./packages/shared/dist s3://${{ secrets.CDN_BUCKET }}/shared/ --delete
          aws s3 sync ./packages/sidecar/dist s3://${{ secrets.CDN_BUCKET }}/sidecar/ --delete
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: 清除 CDN 缓存
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: 运行生产环境健康检查
        run: |
          sleep 60
          curl -f https://micro-core.dev/health || exit 1
          curl -f https://micro-core.dev/api/status || exit 1
          curl -f https://cdn.micro-core.dev/core/index.js || exit 1

      - name: 运行生产环境冒烟测试
        run: pnpm run test:smoke:production
        env:
          TEST_BASE_URL: https://micro-core.dev

      - name: 通知部署成功
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: |
            🎉 生产环境部署成功！
            🔗 主站: https://micro-core.dev
            📚 文档: https://micro-core.dev/docs
            📦 CDN: https://cdn.micro-core.dev
            🏷️ 版本: ${{ github.event.release.tag_name || github.sha }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Docker 镜像构建和推送
  docker-build:
    name: 构建 Docker 镜像
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: 登录到 Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: 登录到 GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            micro-core/core
            ghcr.io/${{ github.repository }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}

      - name: 构建和推送 Docker 镜像
        uses: docker/build-push-action@v4
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 部署监控和告警
  setup-monitoring:
    name: 设置监控和告警
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.event_name == 'release'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置监控规则
        run: |
          # 这里可以配置 Prometheus、Grafana 等监控规则
          echo "设置生产环境监控规则"

      - name: 配置告警规则
        run: |
          # 配置 AlertManager 告警规则
          echo "配置生产环境告警规则"

      - name: 发送部署完成通知
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: |
            ✅ micro-core 完整部署流程已完成！
            
            📊 监控面板: https://monitoring.micro-core.dev
            🚨 告警配置: 已启用
            📈 性能指标: 正在收集
            
            🔗 相关链接:
            • 主站: https://micro-core.dev
            • 文档: https://micro-core.dev/docs
            • CDN: https://cdn.micro-core.dev
            • 监控: https://monitoring.micro-core.dev
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # 回滚机制
  rollback:
    name: 回滚部署
    runs-on: ubuntu-latest
    if: failure() && github.event_name == 'release'
    needs: [deploy-production]
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 回滚 Vercel 部署
        run: |
          # 回滚到上一个稳定版本
          echo "执行 Vercel 部署回滚"

      - name: 回滚 CDN 内容
        run: |
          # 恢复 CDN 上的上一个版本
          echo "执行 CDN 内容回滚"

      - name: 发送回滚通知
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: |
            ⚠️ 生产环境部署失败，已执行自动回滚！
            
            🔄 回滚状态: 已完成
            📝 失败原因: 请查看 GitHub Actions 日志
            🔗 日志链接: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
            
            请及时检查并修复问题。
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
