/**
 * 插件加载器
 * 负责插件的动态加载、卸载和模块管理
 */

import type { 
  Plugin, 
  PluginInstance, 
  PluginLoaderConfig,
  PluginLoadOptions,
  PluginModule 
} from './types'
import { PluginState, PluginType } from './enums'
import { PluginLoadError, PluginModuleError } from './errors'
import { DEFAULT_LOADER_CONFIG } from './constants'

/**
 * 插件加载器实现
 */
export class PluginLoader {
  private readonly config: Required<PluginLoaderConfig>
  private loadedModules: Map<string, PluginModule> = new Map()
  private moduleCache: Map<string, any> = new Map()
  private initialized = false

  constructor(config: PluginLoaderConfig = {}) {
    this.config = { ...DEFAULT_LOADER_CONFIG, ...config }
  }

  /**
   * 初始化加载器
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // 预加载核心模块
      if (this.config.preloadCoreModules) {
        await this.preloadCoreModules()
      }

      this.initialized = true
    } catch (error) {
      throw new PluginLoadError(`插件加载器初始化失败: ${error}`)
    }
  }

  /**
   * 销毁加载器
   */
  async destroy(): Promise<void> {
    if (!this.initialized) {
      return
    }

    try {
      // 卸载所有模块
      await this.unloadAllModules()

      this.loadedModules.clear()
      this.moduleCache.clear()
      this.initialized = false
    } catch (error) {
      throw new PluginLoadError(`插件加载器销毁失败: ${error}`)
    }
  }

  /**
   * 加载插件
   */
  async load(instance: PluginInstance, options: PluginLoadOptions = {}): Promise<void> {
    if (!this.initialized) {
      throw new PluginLoadError('插件加载器未初始化')
    }

    const { plugin } = instance
    const startTime = Date.now()

    try {
      // 检查是否已加载
      if (this.loadedModules.has(plugin.id)) {
        return
      }

      // 加载插件模块
      const module = await this.loadModule(plugin, options)

      // 验证模块
      this.validateModule(module, plugin)

      // 初始化插件
      if (module.initialize) {
        await module.initialize(instance.config)
      }

      // 存储模块
      this.loadedModules.set(plugin.id, module)

      // 更新实例状态
      instance.status = PluginState.LOADED
      instance.loadTime = Date.now() - startTime

    } catch (error) {
      throw new PluginLoadError(`插件 ${plugin.id} 加载失败: ${error}`)
    }
  }

  /**
   * 卸载插件
   */
  async unload(instance: PluginInstance): Promise<void> {
    const { plugin } = instance

    try {
      const module = this.loadedModules.get(plugin.id)
      if (!module) {
        return
      }

      // 销毁插件
      if (module.destroy) {
        await module.destroy()
      }

      // 移除模块
      this.loadedModules.delete(plugin.id)

      // 清除缓存
      if (this.config.enableCache) {
        this.moduleCache.delete(plugin.id)
      }

      // 更新实例状态
      instance.status = PluginState.UNLOADED

    } catch (error) {
      throw new PluginLoadError(`插件 ${plugin.id} 卸载失败: ${error}`)
    }
  }

  /**
   * 重新加载插件
   */
  async reload(instance: PluginInstance, options: PluginLoadOptions = {}): Promise<void> {
    try {
      await this.unload(instance)
      await this.load(instance, { ...options, forceReload: true })
    } catch (error) {
      throw new PluginLoadError(`插件 ${instance.plugin.id} 重新加载失败: ${error}`)
    }
  }

  /**
   * 获取已加载的模块
   */
  getModule(pluginId: string): PluginModule | undefined {
    return this.loadedModules.get(pluginId)
  }

  /**
   * 获取所有已加载的模块
   */
  getAllModules(): Map<string, PluginModule> {
    return new Map(this.loadedModules)
  }

  /**
   * 检查模块是否已加载
   */
  isLoaded(pluginId: string): boolean {
    return this.loadedModules.has(pluginId)
  }

  /**
   * 清除模块缓存
   */
  clearCache(pluginId?: string): void {
    if (pluginId) {
      this.moduleCache.delete(pluginId)
    } else {
      this.moduleCache.clear()
    }
  }

  /**
   * 获取加载统计信息
   */
  getStats(): {
    loadedCount: number
    cachedCount: number
    totalLoadTime: number
  } {
    let totalLoadTime = 0
    
    // 这里需要从插件实例中获取加载时间，暂时返回0
    
    return {
      loadedCount: this.loadedModules.size,
      cachedCount: this.moduleCache.size,
      totalLoadTime
    }
  }

  /**
   * 加载插件模块
   */
  private async loadModule(plugin: Plugin, options: PluginLoadOptions): Promise<PluginModule> {
    const { id, entry, type } = plugin

    // 检查缓存
    if (this.config.enableCache && !options.forceReload) {
      const cached = this.moduleCache.get(id)
      if (cached) {
        return cached
      }
    }

    let module: PluginModule

    try {
      switch (type) {
        case PluginType.ES_MODULE:
          module = await this.loadESModule(entry)
          break
        case PluginType.UMD:
          module = await this.loadUMDModule(entry)
          break
        case PluginType.SYSTEM_JS:
          module = await this.loadSystemJSModule(entry)
          break
        case PluginType.INLINE:
          module = await this.loadInlineModule(plugin)
          break
        default:
          throw new PluginModuleError(`不支持的插件类型: ${type}`)
      }

      // 缓存模块
      if (this.config.enableCache) {
        this.moduleCache.set(id, module)
      }

      return module

    } catch (error) {
      throw new PluginModuleError(`加载插件模块失败: ${error}`)
    }
  }

  /**
   * 加载 ES 模块
   */
  private async loadESModule(entry: string): Promise<PluginModule> {
    try {
      const module = await import(entry)
      return module.default || module
    } catch (error) {
      throw new PluginModuleError(`ES 模块加载失败: ${error}`)
    }
  }

  /**
   * 加载 UMD 模块
   */
  private async loadUMDModule(entry: string): Promise<PluginModule> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = entry
      script.type = 'text/javascript'

      script.onload = () => {
        try {
          // 假设 UMD 模块会在全局对象上注册
          const globalName = this.extractGlobalName(entry)
          const module = (window as any)[globalName]
          
          if (!module) {
            throw new Error(`UMD 模块未找到全局变量: ${globalName}`)
          }

          resolve(module)
        } catch (error) {
          reject(error)
        } finally {
          document.head.removeChild(script)
        }
      }

      script.onerror = () => {
        document.head.removeChild(script)
        reject(new Error(`UMD 模块加载失败: ${entry}`))
      }

      document.head.appendChild(script)
    })
  }

  /**
   * 加载 SystemJS 模块
   */
  private async loadSystemJSModule(entry: string): Promise<PluginModule> {
    try {
      // 检查 SystemJS 是否可用
      if (!(window as any).System) {
        throw new Error('SystemJS 未加载')
      }

      const module = await (window as any).System.import(entry)
      return module.default || module
    } catch (error) {
      throw new PluginModuleError(`SystemJS 模块加载失败: ${error}`)
    }
  }

  /**
   * 加载内联模块
   */
  private async loadInlineModule(plugin: Plugin): Promise<PluginModule> {
    try {
      if (!plugin.code) {
        throw new Error('内联插件缺少代码')
      }

      // 创建模块函数
      const moduleFunction = new Function(
        'exports', 
        'require', 
        'module', 
        '__filename', 
        '__dirname',
        plugin.code
      )

      // 创建模块上下文
      const moduleContext = {
        exports: {},
        require: this.createRequireFunction(),
        module: { exports: {} },
        __filename: `${plugin.id}.js`,
        __dirname: '/'
      }

      // 执行模块
      moduleFunction.call(
        moduleContext.exports,
        moduleContext.exports,
        moduleContext.require,
        moduleContext.module,
        moduleContext.__filename,
        moduleContext.__dirname
      )

      return moduleContext.module.exports || moduleContext.exports
    } catch (error) {
      throw new PluginModuleError(`内联模块执行失败: ${error}`)
    }
  }

  /**
   * 验证模块
   */
  private validateModule(module: PluginModule, plugin: Plugin): void {
    if (!module) {
      throw new PluginModuleError('模块不能为空')
    }

    // 检查必需的方法
    const requiredMethods = ['initialize']
    for (const method of requiredMethods) {
      if (typeof module[method] !== 'function') {
        console.warn(`插件 ${plugin.id} 缺少方法: ${method}`)
      }
    }

    // 检查可选的方法
    const optionalMethods = ['destroy', 'enable', 'disable', 'onConfigUpdate']
    for (const method of optionalMethods) {
      if (module[method] && typeof module[method] !== 'function') {
        throw new PluginModuleError(`插件 ${plugin.id} 的 ${method} 不是函数`)
      }
    }
  }

  /**
   * 预加载核心模块
   */
  private async preloadCoreModules(): Promise<void> {
    const coreModules = this.config.coreModules || []
    
    for (const moduleEntry of coreModules) {
      try {
        await this.loadESModule(moduleEntry)
      } catch (error) {
        console.warn(`预加载核心模块失败: ${moduleEntry}`, error)
      }
    }
  }

  /**
   * 卸载所有模块
   */
  private async unloadAllModules(): Promise<void> {
    const moduleIds = Array.from(this.loadedModules.keys())
    
    for (const moduleId of moduleIds) {
      try {
        const module = this.loadedModules.get(moduleId)
        if (module && module.destroy) {
          await module.destroy()
        }
      } catch (error) {
        console.warn(`卸载模块 ${moduleId} 失败:`, error)
      }
    }
  }

  /**
   * 提取全局变量名
   */
  private extractGlobalName(entry: string): string {
    // 从文件名提取全局变量名
    const filename = entry.split('/').pop() || ''
    const name = filename.replace(/\.(js|min\.js)$/, '')
    
    // 转换为驼峰命名
    return name.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
  }

  /**
   * 创建 require 函数
   */
  private createRequireFunction(): (id: string) => any {
    return (id: string) => {
      // 简单的 require 实现
      switch (id) {
        case 'events':
          return { EventEmitter: class EventEmitter {} }
        case 'util':
          return { 
            inherits: (ctor: any, superCtor: any) => {
              ctor.prototype = Object.create(superCtor.prototype)
              ctor.prototype.constructor = ctor
            }
          }
        default:
          throw new Error(`模块 ${id} 未找到`)
      }
    }
  }
}