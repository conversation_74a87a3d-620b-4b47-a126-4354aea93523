/**
 * 插件验证器
 * 负责插件的安全性、完整性和兼容性验证
 */

import type { 
  Plugin, 
  PluginValidatorConfig,
  PluginValidationResult,
  PluginSecurityPolicy,
  PluginCompatibilityCheck 
} from './types'
import { PluginType, PluginState } from './enums'
import { PluginValidationError, PluginSecurityError } from './errors'
import { DEFAULT_VALIDATOR_CONFIG } from './constants'

/**
 * 插件验证器实现
 */
export class PluginValidator {
  private readonly config: Required<PluginValidatorConfig>
  private securityPolicies: Map<string, PluginSecurityPolicy> = new Map()
  private initialized = false

  constructor(config: PluginValidatorConfig = {}) {
    this.config = { ...DEFAULT_VALIDATOR_CONFIG, ...config }
  }

  /**
   * 初始化验证器
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // 加载安全策略
      await this.loadSecurityPolicies()

      this.initialized = true
    } catch (error) {
      throw new PluginValidationError(`插件验证器初始化失败: ${error}`)
    }
  }

  /**
   * 销毁验证器
   */
  async destroy(): Promise<void> {
    if (!this.initialized) {
      return
    }

    this.securityPolicies.clear()
    this.initialized = false
  }

  /**
   * 验证插件
   */
  async validate(plugin: Plugin): Promise<PluginValidationResult> {
    if (!this.initialized) {
      throw new PluginValidationError('插件验证器未初始化')
    }

    const result: PluginValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
      securityIssues: [],
      compatibilityIssues: []
    }

    try {
      // 基础验证
      await this.validateBasic(plugin, result)

      // 安全验证
      if (this.config.enableSecurityCheck) {
        await this.validateSecurity(plugin, result)
      }

      // 兼容性验证
      if (this.config.enableCompatibilityCheck) {
        await this.validateCompatibility(plugin, result)
      }

      // 依赖验证
      if (this.config.enableDependencyCheck) {
        await this.validateDependencies(plugin, result)
      }

      // 代码验证
      if (this.config.enableCodeValidation) {
        await this.validateCode(plugin, result)
      }

      // 元数据验证
      if (this.config.enableMetadataValidation) {
        await this.validateMetadata(plugin, result)
      }

      // 确定最终验证结果
      result.valid = result.errors.length === 0

    } catch (error) {
      result.valid = false
      result.errors.push(`验证过程出错: ${error}`)
    }

    return result
  }

  /**
   * 快速验证（仅基础检查）
   */
  async quickValidate(plugin: Plugin): Promise<boolean> {
    try {
      const result: PluginValidationResult = {
        valid: true,
        errors: [],
        warnings: [],
        securityIssues: [],
        compatibilityIssues: []
      }

      await this.validateBasic(plugin, result)
      return result.errors.length === 0
    } catch {
      return false
    }
  }

  /**
   * 验证插件签名
   */
  async validateSignature(plugin: Plugin): Promise<boolean> {
    if (!this.config.requireSignature || !plugin.signature) {
      return !this.config.requireSignature
    }

    try {
      // 这里应该实现真正的数字签名验证
      // 暂时返回简单的检查结果
      return plugin.signature.length > 0
    } catch {
      return false
    }
  }

  /**
   * 添加安全策略
   */
  addSecurityPolicy(name: string, policy: PluginSecurityPolicy): void {
    this.securityPolicies.set(name, policy)
  }

  /**
   * 移除安全策略
   */
  removeSecurityPolicy(name: string): void {
    this.securityPolicies.delete(name)
  }

  /**
   * 获取安全策略
   */
  getSecurityPolicy(name: string): PluginSecurityPolicy | undefined {
    return this.securityPolicies.get(name)
  }

  /**
   * 基础验证
   */
  private async validateBasic(plugin: Plugin, result: PluginValidationResult): Promise<void> {
    // 验证必需字段
    if (!plugin.id) {
      result.errors.push('插件ID不能为空')
    }

    if (!plugin.name) {
      result.errors.push('插件名称不能为空')
    }

    if (!plugin.version) {
      result.errors.push('插件版本不能为空')
    }

    if (!plugin.type) {
      result.errors.push('插件类型不能为空')
    }

    // 验证ID格式
    if (plugin.id && !/^[a-zA-Z0-9\-_]+$/.test(plugin.id)) {
      result.errors.push('插件ID格式不正确，只能包含字母、数字、连字符和下划线')
    }

    // 验证版本格式
    if (plugin.version && !/^\d+\.\d+\.\d+/.test(plugin.version)) {
      result.errors.push('插件版本格式不正确，应为 x.y.z 格式')
    }

    // 验证类型
    if (plugin.type && !Object.values(PluginType).includes(plugin.type)) {
      result.errors.push(`不支持的插件类型: ${plugin.type}`)
    }

    // 验证入口文件
    if (plugin.type !== PluginType.INLINE && !plugin.entry) {
      result.errors.push('非内联插件必须指定入口文件')
    }

    if (plugin.type === PluginType.INLINE && !plugin.code) {
      result.errors.push('内联插件必须提供代码')
    }

    // 验证描述长度
    if (plugin.description && plugin.description.length > this.config.maxDescriptionLength) {
      result.warnings.push(`插件描述过长，建议不超过 ${this.config.maxDescriptionLength} 字符`)
    }

    // 验证标签数量
    if (plugin.tags && plugin.tags.length > this.config.maxTagsCount) {
      result.warnings.push(`插件标签过多，建议不超过 ${this.config.maxTagsCount} 个`)
    }
  }

  /**
   * 安全验证
   */
  private async validateSecurity(plugin: Plugin, result: PluginValidationResult): Promise<void> {
    // 验证签名
    if (this.config.requireSignature) {
      const hasValidSignature = await this.validateSignature(plugin)
      if (!hasValidSignature) {
        result.securityIssues.push('插件缺少有效的数字签名')
      }
    }

    // 验证来源
    if (this.config.trustedSources && this.config.trustedSources.length > 0) {
      const isTrustedSource = this.config.trustedSources.some(source => 
        plugin.entry?.startsWith(source) || plugin.author === source
      )
      
      if (!isTrustedSource) {
        result.securityIssues.push('插件来源不在可信列表中')
      }
    }

    // 验证权限
    if (plugin.permissions && plugin.permissions.length > 0) {
      for (const permission of plugin.permissions) {
        if (this.config.forbiddenPermissions?.includes(permission)) {
          result.securityIssues.push(`插件请求了被禁止的权限: ${permission}`)
        }
      }
    }

    // 代码安全检查
    if (plugin.code) {
      await this.validateCodeSecurity(plugin.code, result)
    }

    // 应用安全策略
    for (const [name, policy] of this.securityPolicies) {
      try {
        const policyResult = await policy.validate(plugin)
        if (!policyResult.passed) {
          result.securityIssues.push(`安全策略 ${name} 验证失败: ${policyResult.reason}`)
        }
      } catch (error) {
        result.warnings.push(`安全策略 ${name} 执行失败: ${error}`)
      }
    }
  }

  /**
   * 兼容性验证
   */
  private async validateCompatibility(plugin: Plugin, result: PluginValidationResult): Promise<void> {
    // 验证引擎版本
    if (plugin.engines) {
      const currentVersion = this.config.currentEngineVersion
      
      if (plugin.engines.microCore) {
        const compatible = this.checkVersionCompatibility(
          currentVersion,
          plugin.engines.microCore
        )
        
        if (!compatible) {
          result.compatibilityIssues.push(
            `插件要求 micro-core 版本 ${plugin.engines.microCore}，当前版本 ${currentVersion}`
          )
        }
      }

      // 验证浏览器兼容性
      if (plugin.engines.browsers) {
        const browserCompatible = this.checkBrowserCompatibility(plugin.engines.browsers)
        if (!browserCompatible) {
          result.compatibilityIssues.push('插件与当前浏览器不兼容')
        }
      }
    }

    // 验证 API 兼容性
    if (plugin.requiredAPIs) {
      for (const api of plugin.requiredAPIs) {
        if (!this.isAPIAvailable(api)) {
          result.compatibilityIssues.push(`插件需要的 API ${api} 不可用`)
        }
      }
    }

    // 验证特性支持
    if (plugin.requiredFeatures) {
      for (const feature of plugin.requiredFeatures) {
        if (!this.isFeatureSupported(feature)) {
          result.compatibilityIssues.push(`插件需要的特性 ${feature} 不支持`)
        }
      }
    }
  }

  /**
   * 依赖验证
   */
  private async validateDependencies(plugin: Plugin, result: PluginValidationResult): Promise<void> {
    if (!plugin.dependencies || plugin.dependencies.length === 0) {
      return
    }

    // 检查循环依赖
    const visited = new Set<string>()
    const visiting = new Set<string>()
    
    if (this.hasCircularDependency(plugin.id, plugin.dependencies, visited, visiting)) {
      result.errors.push('检测到循环依赖')
    }

    // 验证依赖格式
    for (const dep of plugin.dependencies) {
      if (!dep.id) {
        result.errors.push('依赖插件ID不能为空')
      }

      if (dep.version && !/^\d+\.\d+\.\d+/.test(dep.version)) {
        result.errors.push(`依赖插件 ${dep.id} 版本格式不正确`)
      }

      // 检查依赖深度
      if (this.getDependencyDepth(plugin.dependencies) > this.config.maxDependencyDepth) {
        result.warnings.push('依赖层级过深，可能影响加载性能')
      }
    }
  }

  /**
   * 代码验证
   */
  private async validateCode(plugin: Plugin, result: PluginValidationResult): Promise<void> {
    if (!plugin.code) {
      return
    }

    // 检查代码长度
    if (plugin.code.length > this.config.maxCodeSize) {
      result.errors.push(`插件代码过大，超过 ${this.config.maxCodeSize} 字符限制`)
    }

    // 语法检查
    try {
      new Function(plugin.code)
    } catch (error) {
      result.errors.push(`插件代码语法错误: ${error}`)
    }

    // 检查禁用的API
    if (this.config.forbiddenAPIs) {
      for (const api of this.config.forbiddenAPIs) {
        if (plugin.code.includes(api)) {
          result.securityIssues.push(`插件代码使用了被禁止的API: ${api}`)
        }
      }
    }
  }

  /**
   * 元数据验证
   */
  private async validateMetadata(plugin: Plugin, result: PluginValidationResult): Promise<void> {
    if (!plugin.metadata) {
      return
    }

    // 验证元数据大小
    const metadataSize = JSON.stringify(plugin.metadata).length
    if (metadataSize > this.config.maxMetadataSize) {
      result.warnings.push(`插件元数据过大，建议不超过 ${this.config.maxMetadataSize} 字符`)
    }

    // 验证必需的元数据字段
    const requiredFields = ['author', 'license']
    for (const field of requiredFields) {
      if (!plugin.metadata[field]) {
        result.warnings.push(`建议提供元数据字段: ${field}`)
      }
    }
  }

  /**
   * 代码安全检查
   */
  private async validateCodeSecurity(code: string, result: PluginValidationResult): Promise<void> {
    // 检查危险函数调用
    const dangerousFunctions = [
      'eval',
      'Function',
      'setTimeout',
      'setInterval',
      'document.write',
      'innerHTML',
      'outerHTML'
    ]

    for (const func of dangerousFunctions) {
      if (code.includes(func)) {
        result.securityIssues.push(`代码包含潜在危险函数: ${func}`)
      }
    }

    // 检查网络请求
    const networkPatterns = [
      /fetch\s*\(/,
      /XMLHttpRequest/,
      /\.ajax\s*\(/,
      /import\s*\(/
    ]

    for (const pattern of networkPatterns) {
      if (pattern.test(code)) {
        result.securityIssues.push('代码包含网络请求，可能存在安全风险')
        break
      }
    }

    // 检查文件系统访问
    if (code.includes('require(') && code.includes('fs')) {
      result.securityIssues.push('代码尝试访问文件系统')
    }
  }

  /**
   * 检查版本兼容性
   */
  private checkVersionCompatibility(current: string, required: string): boolean {
    // 简单的版本比较实现
    const currentParts = current.split('.').map(Number)
    const requiredParts = required.replace(/[^\d.]/g, '').split('.').map(Number)

    for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
      const currentPart = currentParts[i] || 0
      const requiredPart = requiredParts[i] || 0

      if (currentPart > requiredPart) return true
      if (currentPart < requiredPart) return false
    }

    return true
  }

  /**
   * 检查浏览器兼容性
   */
  private checkBrowserCompatibility(browsers: string[]): boolean {
    // 简单的浏览器检查实现
    const userAgent = navigator.userAgent.toLowerCase()
    
    return browsers.some(browser => {
      const browserName = browser.toLowerCase()
      return userAgent.includes(browserName)
    })
  }

  /**
   * 检查API是否可用
   */
  private isAPIAvailable(api: string): boolean {
    try {
      // 检查全局对象上是否存在该API
      const parts = api.split('.')
      let obj: any = window
      
      for (const part of parts) {
        if (!(part in obj)) {
          return false
        }
        obj = obj[part]
      }
      
      return true
    } catch {
      return false
    }
  }

  /**
   * 检查特性是否支持
   */
  private isFeatureSupported(feature: string): boolean {
    switch (feature) {
      case 'es6':
        return typeof Symbol !== 'undefined'
      case 'webworker':
        return typeof Worker !== 'undefined'
      case 'websocket':
        return typeof WebSocket !== 'undefined'
      case 'indexeddb':
        return typeof indexedDB !== 'undefined'
      case 'serviceworker':
        return 'serviceWorker' in navigator
      default:
        return true
    }
  }

  /**
   * 检查循环依赖
   */
  private hasCircularDependency(
    pluginId: string,
    dependencies: any[],
    visited: Set<string>,
    visiting: Set<string>
  ): boolean {
    if (visiting.has(pluginId)) {
      return true
    }

    if (visited.has(pluginId)) {
      return false
    }

    visiting.add(pluginId)

    for (const dep of dependencies) {
      if (this.hasCircularDependency(dep.id, dep.dependencies || [], visited, visiting)) {
        return true
      }
    }

    visiting.delete(pluginId)
    visited.add(pluginId)

    return false
  }

  /**
   * 获取依赖深度
   */
  private getDependencyDepth(dependencies: any[], depth = 0): number {
    if (!dependencies || dependencies.length === 0) {
      return depth
    }

    let maxDepth = depth
    for (const dep of dependencies) {
      const depDepth = this.getDependencyDepth(dep.dependencies || [], depth + 1)
      maxDepth = Math.max(maxDepth, depDepth)
    }

    return maxDepth
  }

  /**
   * 加载安全策略
   */
  private async loadSecurityPolicies(): Promise<void> {
    // 默认安全策略
    this.addSecurityPolicy('default', {
      name: 'default',
      description: '默认安全策略',
      validate: async (plugin: Plugin) => {
        return {
          passed: true,
          reason: ''
        }
      }
    })

    // 严格安全策略
    this.addSecurityPolicy('strict', {
      name: 'strict',
      description: '严格安全策略',
      validate: async (plugin: Plugin) => {
        if (!plugin.signature) {
          return {
            passed: false,
            reason: '缺少数字签名'
          }
        }

        if (plugin.permissions && plugin.permissions.length > 0) {
          return {
            passed: false,
            reason: '不允许请求权限'
          }
        }

        return {
          passed: true,
          reason: ''
        }
      }
    })
  }
}