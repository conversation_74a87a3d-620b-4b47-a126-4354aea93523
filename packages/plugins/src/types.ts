/**
 * 插件系统类型定义
 */

import type { PluginType, PluginState } from './enums'

/**
 * 插件基础接口
 */
export interface Plugin {
  /** 插件唯一标识 */
  id: string
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 插件描述 */
  description?: string
  /** 插件类型 */
  type: PluginType
  /** 插件入口文件 */
  entry?: string
  /** 插件代码（内联插件使用） */
  code?: string
  /** 插件作者 */
  author?: string
  /** 插件许可证 */
  license?: string
  /** 插件主页 */
  homepage?: string
  /** 插件仓库地址 */
  repository?: string
  /** 插件标签 */
  tags?: string[]
  /** 插件关键词 */
  keywords?: string[]
  /** 插件依赖 */
  dependencies?: PluginDependency[]
  /** 插件配置 */
  config?: Record<string, any>
  /** 插件权限 */
  permissions?: string[]
  /** 插件引擎要求 */
  engines?: PluginEngines
  /** 插件所需API */
  requiredAPIs?: string[]
  /** 插件所需特性 */
  requiredFeatures?: string[]
  /** 插件数字签名 */
  signature?: string
  /** 插件元数据 */
  metadata?: PluginMetadata
  /** 插件资源 */
  resources?: PluginResource[]
  /** 插件生命周期钩子 */
  hooks?: PluginHooks
}

/**
 * 插件依赖
 */
export interface PluginDependency {
  /** 依赖插件ID */
  id: string
  /** 依赖版本 */
  version?: string
  /** 是否可选依赖 */
  optional?: boolean
  /** 依赖描述 */
  description?: string
}

/**
 * 插件引擎要求
 */
export interface PluginEngines {
  /** micro-core 版本要求 */
  microCore?: string
  /** Node.js 版本要求 */
  node?: string
  /** 浏览器要求 */
  browsers?: string[]
}

/**
 * 插件元数据
 */
export interface PluginMetadata {
  /** 创建时间 */
  createdAt?: number
  /** 更新时间 */
  updatedAt?: number
  /** 注册时间 */
  registeredAt?: number
  /** 注册表版本 */
  registryVersion?: string
  /** 下载次数 */
  downloads?: number
  /** 评分 */
  rating?: number
  /** 文件大小 */
  size?: number
  /** 文件哈希 */
  hash?: string
  /** 自定义元数据 */
  [key: string]: any
}

/**
 * 插件资源
 */
export interface PluginResource {
  /** 资源类型 */
  type: 'css' | 'js' | 'image' | 'font' | 'other'
  /** 资源URL */
  url: string
  /** 资源描述 */
  description?: string
  /** 是否必需 */
  required?: boolean
}

/**
 * 插件生命周期钩子
 */
export interface PluginHooks {
  /** 安装前钩子 */
  beforeInstall?: string
  /** 安装后钩子 */
  afterInstall?: string
  /** 启用前钩子 */
  beforeEnable?: string
  /** 启用后钩子 */
  afterEnable?: string
  /** 禁用前钩子 */
  beforeDisable?: string
  /** 禁用后钩子 */
  afterDisable?: string
  /** 卸载前钩子 */
  beforeUninstall?: string
  /** 卸载后钩子 */
  afterUninstall?: string
}

/**
 * 插件实例
 */
export interface PluginInstance {
  /** 插件定义 */
  plugin: Plugin
  /** 插件状态 */
  status: PluginState
  /** 插件配置 */
  config: Record<string, any>
  /** 加载时间 */
  loadTime?: number
  /** 启用时间 */
  enableTime?: number
  /** 错误信息 */
  error?: string
  /** 实例创建时间 */
  createdAt: number
  /** 实例更新时间 */
  updatedAt: number
}

/**
 * 插件模块接口
 */
export interface PluginModule {
  /** 初始化方法 */
  initialize?: (config?: Record<string, any>) => Promise<void> | void
  /** 销毁方法 */
  destroy?: () => Promise<void> | void
  /** 启用方法 */
  enable?: () => Promise<void> | void
  /** 禁用方法 */
  disable?: () => Promise<void> | void
  /** 配置更新方法 */
  onConfigUpdate?: (config: Record<string, any>) => Promise<void> | void
  /** 其他方法 */
  [key: string]: any
}

/**
 * 插件管理器配置
 */
export interface PluginManagerConfig {
  /** 是否启用自动加载 */
  enableAutoLoad?: boolean
  /** 是否启用热重载 */
  enableHotReload?: boolean
  /** 是否启用依赖解析 */
  enableDependencyResolution?: boolean
  /** 是否启用沙箱隔离 */
  enableSandbox?: boolean
  /** 最大并发加载数 */
  maxConcurrentLoads?: number
  /** 加载超时时间 */
  loadTimeout?: number
  /** 插件目录 */
  pluginDirectory?: string
  /** 默认配置 */
  defaultConfig?: Record<string, any>
}

/**
 * 插件注册表配置
 */
export interface PluginRegistryConfig {
  /** 注册表版本 */
  version?: string
  /** 是否允许覆盖 */
  allowOverride?: boolean
  /** 是否启用持久化 */
  enablePersistence?: boolean
  /** 存储前缀 */
  storagePrefix?: string
}

/**
 * 插件加载器配置
 */
export interface PluginLoaderConfig {
  /** 是否启用缓存 */
  enableCache?: boolean
  /** 是否预加载核心模块 */
  preloadCoreModules?: boolean
  /** 核心模块列表 */
  coreModules?: string[]
  /** 加载超时时间 */
  timeout?: number
}

/**
 * 插件验证器配置
 */
export interface PluginValidatorConfig {
  /** 是否启用安全检查 */
  enableSecurityCheck?: boolean
  /** 是否启用兼容性检查 */
  enableCompatibilityCheck?: boolean
  /** 是否启用依赖检查 */
  enableDependencyCheck?: boolean
  /** 是否启用代码验证 */
  enableCodeValidation?: boolean
  /** 是否启用元数据验证 */
  enableMetadataValidation?: boolean
  /** 是否要求签名 */
  requireSignature?: boolean
  /** 可信来源列表 */
  trustedSources?: string[]
  /** 禁止的权限 */
  forbiddenPermissions?: string[]
  /** 禁止的API */
  forbiddenAPIs?: string[]
  /** 当前引擎版本 */
  currentEngineVersion?: string
  /** 最大描述长度 */
  maxDescriptionLength?: number
  /** 最大标签数量 */
  maxTagsCount?: number
  /** 最大代码大小 */
  maxCodeSize?: number
  /** 最大元数据大小 */
  maxMetadataSize?: number
  /** 最大依赖深度 */
  maxDependencyDepth?: number
}

/**
 * 插件加载选项
 */
export interface PluginLoadOptions {
  /** 是否强制重新加载 */
  forceReload?: boolean
  /** 加载超时时间 */
  timeout?: number
  /** 是否启用沙箱 */
  enableSandbox?: boolean
  /** 沙箱配置 */
  sandboxConfig?: Record<string, any>
}

/**
 * 插件验证结果
 */
export interface PluginValidationResult {
  /** 是否验证通过 */
  valid: boolean
  /** 错误列表 */
  errors: string[]
  /** 警告列表 */
  warnings: string[]
  /** 安全问题列表 */
  securityIssues: string[]
  /** 兼容性问题列表 */
  compatibilityIssues: string[]
}

/**
 * 插件安全策略
 */
export interface PluginSecurityPolicy {
  /** 策略名称 */
  name: string
  /** 策略描述 */
  description: string
  /** 验证方法 */
  validate: (plugin: Plugin) => Promise<PluginSecurityPolicyResult>
}

/**
 * 插件安全策略结果
 */
export interface PluginSecurityPolicyResult {
  /** 是否通过 */
  passed: boolean
  /** 失败原因 */
  reason: string
}

/**
 * 插件兼容性检查
 */
export interface PluginCompatibilityCheck {
  /** 检查名称 */
  name: string
  /** 检查方法 */
  check: (plugin: Plugin) => Promise<boolean>
}

/**
 * 插件过滤器
 */
export interface PluginFilter {
  /** 按类型过滤 */
  type?: PluginType
  /** 按状态过滤 */
  enabled?: boolean
  /** 按是否有配置过滤 */
  hasConfig?: boolean
  /** 按是否有依赖过滤 */
  hasDependencies?: boolean
}

/**
 * 插件搜索选项
 */
export interface PluginSearchOptions {
  /** 按名称搜索 */
  name?: string
  /** 按类型过滤 */
  type?: PluginType
  /** 按版本过滤 */
  version?: string
  /** 按作者过滤 */
  author?: string
  /** 按标签过滤 */
  tags?: string[]
  /** 按关键词搜索 */
  keywords?: string[]
}

/**
 * 插件事件
 */
export interface PluginEvent {
  /** 事件类型 */
  type: string
  /** 插件ID */
  pluginId: string
  /** 事件数据 */
  data?: any
  /** 事件时间戳 */
  timestamp: number
}

/**
 * 插件市场配置
 */
export interface PluginMarketConfig {
  /** 市场URL */
  marketUrl?: string
  /** API密钥 */
  apiKey?: string
  /** 是否启用自动更新检查 */
  enableAutoUpdateCheck?: boolean
  /** 更新检查间隔 */
  updateCheckInterval?: number
}

/**
 * 插件市场信息
 */
export interface PluginMarketInfo {
  /** 插件ID */
  id: string
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 插件描述 */
  description: string
  /** 插件作者 */
  author: string
  /** 下载次数 */
  downloads: number
  /** 评分 */
  rating: number
  /** 更新时间 */
  updatedAt: number
  /** 下载URL */
  downloadUrl: string
}

/**
 * 插件更新信息
 */
export interface PluginUpdateInfo {
  /** 插件ID */
  pluginId: string
  /** 当前版本 */
  currentVersion: string
  /** 最新版本 */
  latestVersion: string
  /** 更新描述 */
  updateDescription?: string
  /** 是否有重大更新 */
  hasBreakingChanges?: boolean
}

/**
 * 插件统计信息
 */
export interface PluginStats {
  /** 总插件数 */
  total: number
  /** 已启用插件数 */
  enabled: number
  /** 已禁用插件数 */
  disabled: number
  /** 按类型分组统计 */
  byType: Record<string, number>
  /** 按状态分组统计 */
  byStatus: Record<string, number>
}

/**
 * 插件配置模式
 */
export interface PluginConfigSchema {
  /** 配置项类型 */
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  /** 配置项标题 */
  title?: string
  /** 配置项描述 */
  description?: string
  /** 默认值 */
  default?: any
  /** 是否必需 */
  required?: boolean
  /** 枚举值 */
  enum?: any[]
  /** 最小值 */
  minimum?: number
  /** 最大值 */
  maximum?: number
  /** 最小长度 */
  minLength?: number
  /** 最大长度 */
  maxLength?: number
  /** 正则表达式 */
  pattern?: string
  /** 子配置项 */
  properties?: Record<string, PluginConfigSchema>
  /** 数组项配置 */
  items?: PluginConfigSchema
}