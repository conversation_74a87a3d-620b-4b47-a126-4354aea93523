/**
 * 插件系统枚举定义
 */

/**
 * 插件类型
 */
export enum PluginType {
  /** ES模块 */
  ES_MODULE = 'es-module',
  /** UMD模块 */
  UMD = 'umd',
  /** SystemJS模块 */
  SYSTEM_JS = 'systemjs',
  /** 内联插件 */
  INLINE = 'inline'
}

/**
 * 插件状态
 */
export enum PluginState {
  /** 未安装 */
  UNINSTALLED = 'uninstalled',
  /** 已安装 */
  INSTALLED = 'installed',
  /** 未加载 */
  UNLOADED = 'unloaded',
  /** 已加载 */
  LOADED = 'loaded',
  /** 已启用 */
  ENABLED = 'enabled',
  /** 已禁用 */
  DISABLED = 'disabled',
  /** 加载中 */
  LOADING = 'loading',
  /** 启用中 */
  ENABLING = 'enabling',
  /** 禁用中 */
  DISABLING = 'disabling',
  /** 卸载中 */
  UNLOADING = 'unloading',
  /** 错误状态 */
  ERROR = 'error'
}

/**
 * 插件事件类型
 */
export enum PluginEventType {
  /** 插件注册 */
  REGISTERED = 'plugin:registered',
  /** 插件取消注册 */
  UNREGISTERED = 'plugin:unregistered',
  /** 插件安装 */
  INSTALLED = 'plugin:installed',
  /** 插件卸载 */
  UNINSTALLED = 'plugin:uninstalled',
  /** 插件加载 */
  LOADED = 'plugin:loaded',
  /** 插件卸载 */
  UNLOADED = 'plugin:unloaded',
  /** 插件启用 */
  ENABLED = 'plugin:enabled',
  /** 插件禁用 */
  DISABLED = 'plugin:disabled',
  /** 插件配置更新 */
  CONFIG_UPDATED = 'plugin:config-updated',
  /** 插件错误 */
  ERROR = 'plugin:error'
}

/**
 * 插件权限类型
 */
export enum PluginPermission {
  /** 读取DOM */
  DOM_READ = 'dom:read',
  /** 修改DOM */
  DOM_WRITE = 'dom:write',
  /** 网络请求 */
  NETWORK = 'network',
  /** 存储访问 */
  STORAGE = 'storage',
  /** 文件系统访问 */
  FILE_SYSTEM = 'file-system',
  /** 摄像头访问 */
  CAMERA = 'camera',
  /** 麦克风访问 */
  MICROPHONE = 'microphone',
  /** 地理位置 */
  GEOLOCATION = 'geolocation',
  /** 通知 */
  NOTIFICATIONS = 'notifications',
  /** 剪贴板 */
  CLIPBOARD = 'clipboard'
}

/**
 * 插件优先级
 */
export enum PluginPriority {
  /** 最高优先级 */
  HIGHEST = 1000,
  /** 高优先级 */
  HIGH = 800,
  /** 正常优先级 */
  NORMAL = 500,
  /** 低优先级 */
  LOW = 200,
  /** 最低优先级 */
  LOWEST = 0
}

/**
 * 插件分类
 */
export enum PluginCategory {
  /** 核心插件 */
  CORE = 'core',
  /** UI组件 */
  UI = 'ui',
  /** 工具插件 */
  UTILITY = 'utility',
  /** 主题插件 */
  THEME = 'theme',
  /** 语言包 */
  LANGUAGE = 'language',
  /** 集成插件 */
  INTEGRATION = 'integration',
  /** 开发工具 */
  DEVELOPMENT = 'development',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 插件加载策略
 */
export enum PluginLoadStrategy {
  /** 立即加载 */
  IMMEDIATE = 'immediate',
  /** 延迟加载 */
  LAZY = 'lazy',
  /** 按需加载 */
  ON_DEMAND = 'on-demand',
  /** 预加载 */
  PRELOAD = 'preload'
}

/**
 * 插件验证级别
 */
export enum PluginValidationLevel {
  /** 无验证 */
  NONE = 'none',
  /** 基础验证 */
  BASIC = 'basic',
  /** 标准验证 */
  STANDARD = 'standard',
  /** 严格验证 */
  STRICT = 'strict'
}

/**
 * 插件更新策略
 */
export enum PluginUpdateStrategy {
  /** 手动更新 */
  MANUAL = 'manual',
  /** 自动更新 */
  AUTO = 'auto',
  /** 提示更新 */
  PROMPT = 'prompt',
  /** 静默更新 */
  SILENT = 'silent'
}