/**
 * 插件管理器
 * 负责插件的注册、加载、卸载、生命周期管理等核心功能
 */

import type { 
  Plugin, 
  PluginConfig, 
  PluginMetadata, 
  PluginInstance,
  PluginManagerConfig,
  PluginLifecycleHooks,
  PluginDependency,
  PluginStatus
} from './types'
import { PluginRegistry } from './registry'
import { PluginLoader } from './loader'
import { PluginValidator } from './validator'
import { PluginLifecycle } from './lifecycle'
import { PluginHooks } from './hooks'
import { PluginDependencyManager } from './dependency'
import { PluginConfigManager } from './config'
import { PluginEventBus } from './events'
import { PluginSandbox } from './sandbox'
import { PluginState, PluginType, PluginPriority } from './enums'
import { 
  PluginError, 
  PluginLoadError, 
  PluginValidationError,
  PluginDependencyError,
  PluginLifecycleError 
} from './errors'
import { 
  DEFAULT_PLUGIN_CONFIG,
  PLUGIN_LIFECYCLE_TIMEOUT,
  MAX_PLUGIN_LOAD_TIME 
} from './constants'

/**
 * 插件管理器实现
 */
export class PluginManager {
  private readonly config: Required<PluginManagerConfig>
  private readonly registry: PluginRegistry
  private readonly loader: PluginLoader
  private readonly validator: PluginValidator
  private readonly lifecycle: PluginLifecycle
  private readonly hooks: PluginHooks
  private readonly dependencyManager: PluginDependencyManager
  private readonly configManager: PluginConfigManager
  private readonly eventBus: PluginEventBus
  private readonly sandbox: PluginSandbox
  
  private plugins: Map<string, PluginInstance> = new Map()
  private loadingPlugins: Set<string> = new Set()
  private initialized = false

  constructor(config: PluginManagerConfig = {}) {
    this.config = { ...DEFAULT_PLUGIN_CONFIG, ...config }
    
    // 初始化各个组件
    this.registry = new PluginRegistry(this.config.registry)
    this.loader = new PluginLoader(this.config.loader)
    this.validator = new PluginValidator(this.config.validator)
    this.lifecycle = new PluginLifecycle(this.config.lifecycle)
    this.hooks = new PluginHooks(this.config.hooks)
    this.dependencyManager = new PluginDependencyManager(this.config.dependency)
    this.configManager = new PluginConfigManager(this.config.config)
    this.eventBus = new PluginEventBus(this.config.events)
    this.sandbox = new PluginSandbox(this.config.sandbox)
  }

  /**
   * 初始化插件管理器
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // 初始化各个组件
      await Promise.all([
        this.registry.initialize(),
        this.loader.initialize(),
        this.validator.initialize(),
        this.lifecycle.initialize(),
        this.hooks.initialize(),
        this.dependencyManager.initialize(),
        this.configManager.initialize(),
        this.eventBus.initialize(),
        this.sandbox.initialize()
      ])

      // 设置事件监听
      this.setupEventListeners()

      // 加载预配置的插件
      await this.loadPreConfiguredPlugins()

      this.initialized = true
      this.eventBus.emit('manager:initialized')

    } catch (error) {
      throw new PluginError(`插件管理器初始化失败: ${error}`)
    }
  }

  /**
   * 销毁插件管理器
   */
  async destroy(): Promise<void> {
    if (!this.initialized) {
      return
    }

    try {
      // 卸载所有插件
      await this.unloadAllPlugins()

      // 销毁各个组件
      await Promise.all([
        this.sandbox.destroy(),
        this.eventBus.destroy(),
        this.configManager.destroy(),
        this.dependencyManager.destroy(),
        this.hooks.destroy(),
        this.lifecycle.destroy(),
        this.validator.destroy(),
        this.loader.destroy(),
        this.registry.destroy()
      ])

      this.plugins.clear()
      this.loadingPlugins.clear()
      this.initialized = false

      this.eventBus.emit('manager:destroyed')

    } catch (error) {
      throw new PluginError(`插件管理器销毁失败: ${error}`)
    }
  }

  /**
   * 注册插件
   */
  async register(plugin: Plugin): Promise<void> {
    try {
      // 验证插件
      await this.validator.validate(plugin)

      // 注册到注册表
      await this.registry.register(plugin)

      // 触发注册事件
      this.eventBus.emit('plugin:registered', plugin)

    } catch (error) {
      throw new PluginError(`插件注册失败: ${error}`)
    }
  }

  /**
   * 取消注册插件
   */
  async unregister(pluginId: string): Promise<void> {
    try {
      // 如果插件已加载，先卸载
      if (this.plugins.has(pluginId)) {
        await this.unload(pluginId)
      }

      // 从注册表移除
      await this.registry.unregister(pluginId)

      // 触发取消注册事件
      this.eventBus.emit('plugin:unregistered', pluginId)

    } catch (error) {
      throw new PluginError(`插件取消注册失败: ${error}`)
    }
  }

  /**
   * 加载插件
   */
  async load(pluginId: string, config?: PluginConfig): Promise<PluginInstance> {
    if (this.plugins.has(pluginId)) {
      return this.plugins.get(pluginId)!
    }

    if (this.loadingPlugins.has(pluginId)) {
      throw new PluginLoadError(`插件 ${pluginId} 正在加载中`)
    }

    this.loadingPlugins.add(pluginId)

    try {
      // 获取插件定义
      const plugin = await this.registry.get(pluginId)
      if (!plugin) {
        throw new PluginLoadError(`插件 ${pluginId} 未找到`)
      }

      // 检查依赖
      await this.checkDependencies(plugin)

      // 加载依赖插件
      await this.loadDependencies(plugin)

      // 合并配置
      const finalConfig = await this.configManager.merge(plugin.config, config)

      // 创建插件实例
      const instance = await this.createPluginInstance(plugin, finalConfig)

      // 执行生命周期钩子
      await this.lifecycle.execute('beforeLoad', instance)

      // 加载插件
      await this.loader.load(instance)

      // 执行生命周期钩子
      await this.lifecycle.execute('afterLoad', instance)

      // 存储插件实例
      this.plugins.set(pluginId, instance)

      // 触发加载事件
      this.eventBus.emit('plugin:loaded', instance)

      return instance

    } catch (error) {
      throw new PluginLoadError(`插件 ${pluginId} 加载失败: ${error}`)
    } finally {
      this.loadingPlugins.delete(pluginId)
    }
  }

  /**
   * 卸载插件
   */
  async unload(pluginId: string): Promise<void> {
    const instance = this.plugins.get(pluginId)
    if (!instance) {
      return
    }

    try {
      // 检查依赖关系
      await this.checkUnloadDependencies(pluginId)

      // 执行生命周期钩子
      await this.lifecycle.execute('beforeUnload', instance)

      // 卸载插件
      await this.loader.unload(instance)

      // 执行生命周期钩子
      await this.lifecycle.execute('afterUnload', instance)

      // 从存储中移除
      this.plugins.delete(pluginId)

      // 触发卸载事件
      this.eventBus.emit('plugin:unloaded', instance)

    } catch (error) {
      throw new PluginError(`插件 ${pluginId} 卸载失败: ${error}`)
    }
  }

  /**
   * 重新加载插件
   */
  async reload(pluginId: string, config?: PluginConfig): Promise<PluginInstance> {
    try {
      // 先卸载
      await this.unload(pluginId)

      // 重新加载
      return await this.load(pluginId, config)

    } catch (error) {
      throw new PluginError(`插件 ${pluginId} 重新加载失败: ${error}`)
    }
  }

  /**
   * 启用插件
   */
  async enable(pluginId: string): Promise<void> {
    const instance = this.plugins.get(pluginId)
    if (!instance) {
      throw new PluginError(`插件 ${pluginId} 未加载`)
    }

    if (instance.status === PluginState.ENABLED) {
      return
    }

    try {
      // 执行生命周期钩子
      await this.lifecycle.execute('beforeEnable', instance)

      // 启用插件
      if (instance.plugin.enable) {
        await instance.plugin.enable()
      }

      // 更新状态
      instance.status = PluginState.ENABLED

      // 执行生命周期钩子
      await this.lifecycle.execute('afterEnable', instance)

      // 触发启用事件
      this.eventBus.emit('plugin:enabled', instance)

    } catch (error) {
      throw new PluginLifecycleError(`插件 ${pluginId} 启用失败: ${error}`)
    }
  }

  /**
   * 禁用插件
   */
  async disable(pluginId: string): Promise<void> {
    const instance = this.plugins.get(pluginId)
    if (!instance) {
      throw new PluginError(`插件 ${pluginId} 未加载`)
    }

    if (instance.status === PluginState.DISABLED) {
      return
    }

    try {
      // 执行生命周期钩子
      await this.lifecycle.execute('beforeDisable', instance)

      // 禁用插件
      if (instance.plugin.disable) {
        await instance.plugin.disable()
      }

      // 更新状态
      instance.status = PluginState.DISABLED

      // 执行生命周期钩子
      await this.lifecycle.execute('afterDisable', instance)

      // 触发禁用事件
      this.eventBus.emit('plugin:disabled', instance)

    } catch (error) {
      throw new PluginLifecycleError(`插件 ${pluginId} 禁用失败: ${error}`)
    }
  }

  /**
   * 获取插件实例
   */
  get(pluginId: string): PluginInstance | undefined {
    return this.plugins.get(pluginId)
  }

  /**
   * 获取所有插件实例
   */
  getAll(): PluginInstance[] {
    return Array.from(this.plugins.values())
  }

  /**
   * 获取已加载的插件列表
   */
  getLoaded(): PluginInstance[] {
    return this.getAll().filter(instance => 
      instance.status !== PluginState.UNLOADED
    )
  }

  /**
   * 获取已启用的插件列表
   */
  getEnabled(): PluginInstance[] {
    return this.getAll().filter(instance => 
      instance.status === PluginState.ENABLED
    )
  }

  /**
   * 检查插件是否已加载
   */
  isLoaded(pluginId: string): boolean {
    return this.plugins.has(pluginId)
  }

  /**
   * 检查插件是否已启用
   */
  isEnabled(pluginId: string): boolean {
    const instance = this.plugins.get(pluginId)
    return instance?.status === PluginState.ENABLED
  }

  /**
   * 获取插件状态
   */
  getStatus(pluginId: string): PluginStatus | undefined {
    const instance = this.plugins.get(pluginId)
    return instance?.status
  }

  /**
   * 获取插件配置
   */
  getConfig(pluginId: string): PluginConfig | undefined {
    const instance = this.plugins.get(pluginId)
    return instance?.config
  }

  /**
   * 更新插件配置
   */
  async updateConfig(pluginId: string, config: Partial<PluginConfig>): Promise<void> {
    const instance = this.plugins.get(pluginId)
    if (!instance) {
      throw new PluginError(`插件 ${pluginId} 未加载`)
    }

    try {
      // 合并配置
      const newConfig = await this.configManager.merge(instance.config, config)

      // 执行配置更新钩子
      if (instance.plugin.onConfigUpdate) {
        await instance.plugin.onConfigUpdate(newConfig, instance.config)
      }

      // 更新配置
      instance.config = newConfig

      // 触发配置更新事件
      this.eventBus.emit('plugin:configUpdated', instance, config)

    } catch (error) {
      throw new PluginError(`插件 ${pluginId} 配置更新失败: ${error}`)
    }
  }

  /**
   * 获取插件钩子系统
   */
  getHooks(): PluginHooks {
    return this.hooks
  }

  /**
   * 获取插件事件总线
   */
  getEventBus(): PluginEventBus {
    return this.eventBus
  }

  /**
   * 获取插件注册表
   */
  getRegistry(): PluginRegistry {
    return this.registry
  }

  /**
   * 卸载所有插件
   */
  private async unloadAllPlugins(): Promise<void> {
    const pluginIds = Array.from(this.plugins.keys())
    
    // 按依赖关系逆序卸载
    const sortedIds = await this.dependencyManager.sortForUnload(pluginIds)
    
    for (const pluginId of sortedIds) {
      try {
        await this.unload(pluginId)
      } catch (error) {
        console.warn(`卸载插件 ${pluginId} 失败:`, error)
      }
    }
  }

  /**
   * 加载预配置的插件
   */
  private async loadPreConfiguredPlugins(): Promise<void> {
    if (!this.config.autoLoad || !this.config.preloadPlugins) {
      return
    }

    for (const pluginConfig of this.config.preloadPlugins) {
      try {
        await this.load(pluginConfig.id, pluginConfig.config)
        
        if (pluginConfig.autoEnable) {
          await this.enable(pluginConfig.id)
        }
      } catch (error) {
        console.warn(`预加载插件 ${pluginConfig.id} 失败:`, error)
      }
    }
  }

  /**
   * 检查插件依赖
   */
  private async checkDependencies(plugin: Plugin): Promise<void> {
    if (!plugin.dependencies || plugin.dependencies.length === 0) {
      return
    }

    const missingDeps = await this.dependencyManager.checkMissing(plugin.dependencies)
    if (missingDeps.length > 0) {
      throw new PluginDependencyError(
        `插件 ${plugin.id} 缺少依赖: ${missingDeps.join(', ')}`
      )
    }
  }

  /**
   * 加载依赖插件
   */
  private async loadDependencies(plugin: Plugin): Promise<void> {
    if (!plugin.dependencies || plugin.dependencies.length === 0) {
      return
    }

    for (const dep of plugin.dependencies) {
      if (!this.plugins.has(dep.id)) {
        await this.load(dep.id)
      }
    }
  }

  /**
   * 检查卸载依赖关系
   */
  private async checkUnloadDependencies(pluginId: string): Promise<void> {
    const dependents = await this.dependencyManager.getDependents(pluginId)
    const loadedDependents = dependents.filter(id => this.plugins.has(id))
    
    if (loadedDependents.length > 0) {
      throw new PluginDependencyError(
        `无法卸载插件 ${pluginId}，以下插件依赖它: ${loadedDependents.join(', ')}`
      )
    }
  }

  /**
   * 创建插件实例
   */
  private async createPluginInstance(
    plugin: Plugin, 
    config: PluginConfig
  ): Promise<PluginInstance> {
    return {
      id: plugin.id,
      plugin,
      config,
      status: PluginState.LOADED,
      loadTime: Date.now(),
      metadata: {
        ...plugin.metadata,
        loadTime: Date.now()
      }
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听插件错误
    this.eventBus.on('plugin:error', (error: Error, pluginId: string) => {
      console.error(`插件 ${pluginId} 发生错误:`, error)
    })

    // 监听插件生命周期事件
    this.lifecycle.on('timeout', (hookName: string, pluginId: string) => {
      console.warn(`插件 ${pluginId} 生命周期钩子 ${hookName} 超时`)
    })
  }
}