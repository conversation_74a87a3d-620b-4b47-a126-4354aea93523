/**
 * 插件注册表
 * 负责插件的注册、查找、管理等功能
 */

import type { 
  Plugin, 
  PluginMetadata, 
  PluginRegistryConfig,
  PluginFilter,
  PluginSearchOptions 
} from './types'
import { PluginType, PluginState } from './enums'
import { PluginValidationError, PluginRegistryError } from './errors'
import { DEFAULT_REGISTRY_CONFIG } from './constants'

/**
 * 插件注册表实现
 */
export class PluginRegistry {
  private readonly config: Required<PluginRegistryConfig>
  private plugins: Map<string, Plugin> = new Map()
  private metadata: Map<string, PluginMetadata> = new Map()
  private initialized = false

  constructor(config: PluginRegistryConfig = {}) {
    this.config = { ...DEFAULT_REGISTRY_CONFIG, ...config }
  }

  /**
   * 初始化注册表
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // 加载持久化的插件信息
      if (this.config.enablePersistence) {
        await this.loadPersistedPlugins()
      }

      this.initialized = true
    } catch (error) {
      throw new PluginRegistryError(`插件注册表初始化失败: ${error}`)
    }
  }

  /**
   * 销毁注册表
   */
  async destroy(): Promise<void> {
    if (!this.initialized) {
      return
    }

    try {
      // 保存插件信息
      if (this.config.enablePersistence) {
        await this.persistPlugins()
      }

      this.plugins.clear()
      this.metadata.clear()
      this.initialized = false
    } catch (error) {
      throw new PluginRegistryError(`插件注册表销毁失败: ${error}`)
    }
  }

  /**
   * 注册插件
   */
  async register(plugin: Plugin): Promise<void> {
    if (!this.initialized) {
      throw new PluginRegistryError('插件注册表未初始化')
    }

    try {
      // 验证插件
      this.validatePlugin(plugin)

      // 检查是否已存在
      if (this.plugins.has(plugin.id)) {
        if (!this.config.allowOverride) {
          throw new PluginRegistryError(`插件 ${plugin.id} 已存在`)
        }
      }

      // 注册插件
      this.plugins.set(plugin.id, plugin)
      this.metadata.set(plugin.id, {
        ...plugin.metadata,
        registeredAt: Date.now(),
        registryVersion: this.config.version
      })

      // 持久化
      if (this.config.enablePersistence) {
        await this.persistPlugin(plugin)
      }

    } catch (error) {
      throw new PluginRegistryError(`插件 ${plugin.id} 注册失败: ${error}`)
    }
  }

  /**
   * 取消注册插件
   */
  async unregister(pluginId: string): Promise<void> {
    if (!this.initialized) {
      throw new PluginRegistryError('插件注册表未初始化')
    }

    if (!this.plugins.has(pluginId)) {
      return
    }

    try {
      // 移除插件
      this.plugins.delete(pluginId)
      this.metadata.delete(pluginId)

      // 从持久化存储中移除
      if (this.config.enablePersistence) {
        await this.removePersistedPlugin(pluginId)
      }

    } catch (error) {
      throw new PluginRegistryError(`插件 ${pluginId} 取消注册失败: ${error}`)
    }
  }

  /**
   * 获取插件
   */
  async get(pluginId: string): Promise<Plugin | undefined> {
    return this.plugins.get(pluginId)
  }

  /**
   * 获取所有插件
   */
  async getAll(): Promise<Plugin[]> {
    return Array.from(this.plugins.values())
  }

  /**
   * 搜索插件
   */
  async search(options: PluginSearchOptions): Promise<Plugin[]> {
    const plugins = Array.from(this.plugins.values())
    
    return plugins.filter(plugin => {
      // 按名称搜索
      if (options.name && !plugin.name.toLowerCase().includes(options.name.toLowerCase())) {
        return false
      }

      // 按类型过滤
      if (options.type && plugin.type !== options.type) {
        return false
      }

      // 按版本过滤
      if (options.version && plugin.version !== options.version) {
        return false
      }

      // 按作者过滤
      if (options.author && plugin.author !== options.author) {
        return false
      }

      // 按标签过滤
      if (options.tags && options.tags.length > 0) {
        const pluginTags = plugin.tags || []
        if (!options.tags.some(tag => pluginTags.includes(tag))) {
          return false
        }
      }

      // 按关键词搜索
      if (options.keywords && options.keywords.length > 0) {
        const searchText = `${plugin.name} ${plugin.description} ${plugin.tags?.join(' ') || ''}`.toLowerCase()
        if (!options.keywords.some(keyword => searchText.includes(keyword.toLowerCase()))) {
          return false
        }
      }

      return true
    })
  }

  /**
   * 过滤插件
   */
  async filter(filter: PluginFilter): Promise<Plugin[]> {
    const plugins = Array.from(this.plugins.values())
    
    return plugins.filter(plugin => {
      if (filter.type && plugin.type !== filter.type) {
        return false
      }

      if (filter.enabled !== undefined) {
        // 这里需要从插件管理器获取状态，暂时跳过
      }

      if (filter.hasConfig !== undefined) {
        const hasConfig = plugin.config && Object.keys(plugin.config).length > 0
        if (filter.hasConfig !== hasConfig) {
          return false
        }
      }

      if (filter.hasDependencies !== undefined) {
        const hasDeps = plugin.dependencies && plugin.dependencies.length > 0
        if (filter.hasDependencies !== hasDeps) {
          return false
        }
      }

      return true
    })
  }

  /**
   * 检查插件是否存在
   */
  has(pluginId: string): boolean {
    return this.plugins.has(pluginId)
  }

  /**
   * 获取插件数量
   */
  size(): number {
    return this.plugins.size
  }

  /**
   * 获取插件元数据
   */
  getMetadata(pluginId: string): PluginMetadata | undefined {
    return this.metadata.get(pluginId)
  }

  /**
   * 获取所有插件元数据
   */
  getAllMetadata(): Map<string, PluginMetadata> {
    return new Map(this.metadata)
  }

  /**
   * 按类型获取插件
   */
  getByType(type: PluginType): Plugin[] {
    return Array.from(this.plugins.values()).filter(plugin => plugin.type === type)
  }

  /**
   * 按作者获取插件
   */
  getByAuthor(author: string): Plugin[] {
    return Array.from(this.plugins.values()).filter(plugin => plugin.author === author)
  }

  /**
   * 按标签获取插件
   */
  getByTag(tag: string): Plugin[] {
    return Array.from(this.plugins.values()).filter(plugin => 
      plugin.tags && plugin.tags.includes(tag)
    )
  }

  /**
   * 获取插件统计信息
   */
  getStats(): {
    total: number
    byType: Record<string, number>
    byAuthor: Record<string, number>
  } {
    const plugins = Array.from(this.plugins.values())
    
    const byType: Record<string, number> = {}
    const byAuthor: Record<string, number> = {}

    plugins.forEach(plugin => {
      // 按类型统计
      byType[plugin.type] = (byType[plugin.type] || 0) + 1

      // 按作者统计
      if (plugin.author) {
        byAuthor[plugin.author] = (byAuthor[plugin.author] || 0) + 1
      }
    })

    return {
      total: plugins.length,
      byType,
      byAuthor
    }
  }

  /**
   * 清空注册表
   */
  async clear(): Promise<void> {
    this.plugins.clear()
    this.metadata.clear()

    if (this.config.enablePersistence) {
      await this.clearPersistedPlugins()
    }
  }

  /**
   * 验证插件
   */
  private validatePlugin(plugin: Plugin): void {
    if (!plugin.id) {
      throw new PluginValidationError('插件ID不能为空')
    }

    if (!plugin.name) {
      throw new PluginValidationError('插件名称不能为空')
    }

    if (!plugin.version) {
      throw new PluginValidationError('插件版本不能为空')
    }

    if (!plugin.type) {
      throw new PluginValidationError('插件类型不能为空')
    }

    // 验证版本格式
    if (!/^\d+\.\d+\.\d+/.test(plugin.version)) {
      throw new PluginValidationError('插件版本格式不正确')
    }

    // 验证依赖关系
    if (plugin.dependencies) {
      for (const dep of plugin.dependencies) {
        if (!dep.id) {
          throw new PluginValidationError('依赖插件ID不能为空')
        }
      }
    }
  }

  /**
   * 加载持久化的插件
   */
  private async loadPersistedPlugins(): Promise<void> {
    try {
      const storageKey = `${this.config.storagePrefix}plugins`
      const stored = localStorage.getItem(storageKey)
      
      if (stored) {
        const data = JSON.parse(stored)
        
        if (data.plugins && Array.isArray(data.plugins)) {
          for (const pluginData of data.plugins) {
            this.plugins.set(pluginData.id, pluginData)
          }
        }

        if (data.metadata && typeof data.metadata === 'object') {
          for (const [id, meta] of Object.entries(data.metadata)) {
            this.metadata.set(id, meta as PluginMetadata)
          }
        }
      }
    } catch (error) {
      console.warn('加载持久化插件失败:', error)
    }
  }

  /**
   * 持久化插件
   */
  private async persistPlugins(): Promise<void> {
    try {
      const storageKey = `${this.config.storagePrefix}plugins`
      const data = {
        plugins: Array.from(this.plugins.values()),
        metadata: Object.fromEntries(this.metadata.entries()),
        version: this.config.version,
        timestamp: Date.now()
      }

      localStorage.setItem(storageKey, JSON.stringify(data))
    } catch (error) {
      console.warn('持久化插件失败:', error)
    }
  }

  /**
   * 持久化单个插件
   */
  private async persistPlugin(plugin: Plugin): Promise<void> {
    // 重新持久化所有插件
    await this.persistPlugins()
  }

  /**
   * 移除持久化的插件
   */
  private async removePersistedPlugin(pluginId: string): Promise<void> {
    // 重新持久化所有插件
    await this.persistPlugins()
  }

  /**
   * 清空持久化的插件
   */
  private async clearPersistedPlugins(): Promise<void> {
    try {
      const storageKey = `${this.config.storagePrefix}plugins`
      localStorage.removeItem(storageKey)
    } catch (error) {
      console.warn('清空持久化插件失败:', error)
    }
  }
}