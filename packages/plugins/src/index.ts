/**
 * 插件系统主入口
 * 提供插件管理、生命周期、钩子系统等核心功能
 */

// 插件管理器
export { PluginManager } from './manager'

// 插件工厂
export { PluginFactory } from './factory'

// 插件基类
export { BasePlugin } from './base'

// 插件注册器
export { PluginRegistry } from './registry'

// 插件加载器
export { PluginLoader } from './loader'

// 插件验证器
export { PluginValidator } from './validator'

// 插件生命周期管理
export { PluginLifecycle } from './lifecycle'

// 插件钩子系统
export { PluginHooks } from './hooks'

// 插件依赖管理
export { PluginDependencyManager } from './dependency'

// 插件配置管理
export { PluginConfigManager } from './config'

// 插件事件系统
export { PluginEventBus } from './events'

// 插件沙箱
export { PluginSandbox } from './sandbox'

// 插件工具函数
export * from './utils'

// 插件类型定义
export * from './types'

// 插件枚举
export * from './enums'

// 插件常量
export * from './constants'

// 插件错误类
export * from './errors'

// 默认导出插件管理器
export { PluginManager as default } from './manager'