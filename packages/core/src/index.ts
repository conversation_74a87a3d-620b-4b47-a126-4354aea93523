/**
 * @micro-core/core 主入口文件
 * 微前端核心运行时包
 */

// 导出核心功能
export { LifecycleScheduler } from './kernel/lifecycle'
export { TaskScheduler } from './kernel/scheduler'
export { ApplicationRegistry } from './kernel/registry'
export { ResourceLoader } from './kernel/loader'

// 导出插件系统
export { PluginManager } from './plugin/manager'

// 导出所有类型
export type * from './types'

// 导出工具
export { logger } from './utils/logger'

/**
 * 微前端核心类
 * 整合所有核心功能的主要接口
 */
export class MicroCore {
  private readonly lifecycleScheduler: LifecycleScheduler
  private readonly taskScheduler: TaskScheduler
  private readonly applicationRegistry: ApplicationRegistry
  private readonly resourceLoader: ResourceLoader
  private readonly pluginManager: PluginManager
  
  private isInitialized = false

  constructor() {
    // 初始化核心组件
    this.lifecycleScheduler = new LifecycleScheduler()
    this.taskScheduler = new TaskScheduler()
    this.applicationRegistry = new ApplicationRegistry()
    this.resourceLoader = new ResourceLoader()
    this.pluginManager = new PluginManager(this)
  }

  /**
   * 初始化微前端核心
   */
  async init(options: {
    plugins?: any[]
    enableRouter?: boolean
    enableCache?: boolean
  } = {}): Promise<void> {
    if (this.isInitialized) {
      console.warn('[MicroCore] 已经初始化，跳过重复初始化')
      return
    }

    try {
      console.log('[MicroCore] 开始初始化微前端核心...')

      // 启动任务调度器
      this.taskScheduler.start()

      // 加载默认插件
      if (options.plugins) {
        for (const plugin of options.plugins) {
          this.pluginManager.register(plugin)
          await this.pluginManager.load(plugin.name)
        }
      }

      this.isInitialized = true
      console.log('[MicroCore] 微前端核心初始化完成')

    } catch (error) {
      console.error('[MicroCore] 初始化失败:', error)
      throw error
    }
  }

  /**
   * 注册应用
   */
  registerApp(config: any): void {
    this.applicationRegistry.register(config)
  }

  /**
   * 启动应用
   */
  async startApp(appName: string, props?: any): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('MicroCore 尚未初始化，请先调用 init() 方法')
    }

    const app = this.applicationRegistry.getApp(appName)
    if (!app) {
      throw new Error(`应用 ${appName} 未注册`)
    }

    try {
      // 加载应用资源
      await this.resourceLoader.loadApp(app)
      
      // 启动应用
      await this.lifecycleScheduler.bootstrap(app)
      await this.lifecycleScheduler.mount(app)

      console.log(`[MicroCore] 应用已启动: ${appName}`)
    } catch (error) {
      console.error(`[MicroCore] 启动应用失败: ${appName}`, error)
      throw error
    }
  }

  /**
   * 停止应用
   */
  async stopApp(appName: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('MicroCore 尚未初始化，请先调用 init() 方法')
    }

    const app = this.applicationRegistry.getApp(appName)
    if (!app) {
      return
    }

    try {
      await this.lifecycleScheduler.unmount(app)
      console.log(`[MicroCore] 应用已停止: ${appName}`)
    } catch (error) {
      console.error(`[MicroCore] 停止应用失败: ${appName}`, error)
      throw error
    }
  }

  /**
   * 卸载应用
   */
  async unloadApp(appName: string): Promise<void> {
    const app = this.applicationRegistry.getApp(appName)
    if (!app) {
      return
    }

    try {
      await this.lifecycleScheduler.unload(app)
      this.applicationRegistry.unregister(appName)
      console.log(`[MicroCore] 应用已卸载: ${appName}`)
    } catch (error) {
      console.error(`[MicroCore] 卸载应用失败: ${appName}`, error)
      throw error
    }
  }

  /**
   * 获取应用注册表
   */
  getApplicationRegistry(): ApplicationRegistry {
    return this.applicationRegistry
  }

  /**
   * 获取插件管理器
   */
  getPluginManager(): PluginManager {
    return this.pluginManager
  }

  /**
   * 获取资源加载器
   */
  getResourceLoader(): ResourceLoader {
    return this.resourceLoader
  }

  /**
   * 获取生命周期调度器
   */
  getLifecycleScheduler(): LifecycleScheduler {
    return this.lifecycleScheduler
  }

  /**
   * 获取任务调度器
   */
  getTaskScheduler(): TaskScheduler {
    return this.taskScheduler
  }

  /**
   * 获取系统统计信息
   */
  getStats() {
    return {
      applications: this.applicationRegistry.getStats(),
      plugins: this.pluginManager.getStats(),
      tasks: this.taskScheduler.getQueueStatus(),
      cache: this.resourceLoader.getCacheStats()
    }
  }

  /**
   * 销毁微前端核心
   */
  async destroy(): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    console.log('[MicroCore] 开始销毁微前端核心...')

    // 停止所有应用
    const activeApps = this.applicationRegistry.getActiveApps()
    for (const app of activeApps) {
      await this.stopApp(app.name)
    }

    // 停止任务调度器
    this.taskScheduler.stop()

    // 清空插件
    await this.pluginManager.clear()

    // 清空注册表
    this.applicationRegistry.clear()

    // 清空缓存
    this.resourceLoader.clearCache()

    this.isInitialized = false
    console.log('[MicroCore] 微前端核心已销毁')
  }
}

// 默认导出
export default MicroCore

// 创建默认实例
export const microCore = new MicroCore()

// 便捷方法
export const init = microCore.init.bind(microCore)
export const registerApp = microCore.registerApp.bind(microCore)
export const startApp = microCore.startApp.bind(microCore)
export const stopApp = microCore.stopApp.bind(microCore)
export const unloadApp = microCore.unloadApp.bind(microCore)