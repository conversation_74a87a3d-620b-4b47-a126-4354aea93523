/**
 * 通用类型定义
 */

export interface MicroCoreConfig {
  /** 应用配置列表 */
  apps?: AppConfig[]
  /** 全局配置 */
  global?: GlobalConfig
  /** 插件配置 */
  plugins?: PluginConfig[]
  /** 沙箱配置 */
  sandbox?: SandboxConfig
  /** 开发模式配置 */
  dev?: DevConfig
}

export interface GlobalConfig {
  /** 基础路径 */
  basePath?: string
  /** 是否启用单实例模式 */
  singular?: boolean
  /** 预加载策略 */
  prefetch?: boolean | 'all' | string[]
  /** 资源加载超时时间 */
  timeout?: number
  /** 错误处理策略 */
  errorHandling?: 'throw' | 'warn' | 'ignore'
  /** 性能监控配置 */
  performance?: PerformanceConfig
}

export interface DevConfig {
  /** 是否启用开发模式 */
  enabled?: boolean
  /** 开发工具配置 */
  devtools?: boolean
  /** 热更新配置 */
  hotReload?: boolean
  /** 调试配置 */
  debug?: boolean | DebugConfig
}

export interface DebugConfig {
  /** 日志级别 */
  logLevel?: 'debug' | 'info' | 'warn' | 'error'
  /** 是否显示性能信息 */
  showPerformance?: boolean
  /** 是否显示生命周期信息 */
  showLifecycle?: boolean
}

export interface PerformanceConfig {
  /** 是否启用性能监控 */
  enabled?: boolean
  /** 性能指标收集间隔 */
  interval?: number
  /** 性能数据上报地址 */
  reportUrl?: string
  /** 性能阈值配置 */
  thresholds?: PerformanceThresholds
}

export interface PerformanceThresholds {
  /** 应用加载时间阈值（毫秒） */
  loadTime?: number
  /** 应用挂载时间阈值（毫秒） */
  mountTime?: number
  /** 内存使用阈值（MB） */
  memoryUsage?: number
  /** 包大小阈值（KB） */
  bundleSize?: number
}

export interface EventBus {
  /** 订阅事件 */
  on(event: string, handler: EventHandler): void
  /** 取消订阅 */
  off(event: string, handler?: EventHandler): void
  /** 触发事件 */
  emit(event: string, data?: any): void
  /** 一次性订阅 */
  once(event: string, handler: EventHandler): void
  /** 清空所有事件 */
  clear(): void
}

export type EventHandler = (data?: any) => void

export interface Logger {
  /** 调试日志 */
  debug(message: string, ...args: any[]): void
  /** 信息日志 */
  info(message: string, ...args: any[]): void
  /** 警告日志 */
  warn(message: string, ...args: any[]): void
  /** 错误日志 */
  error(message: string, ...args: any[]): void
  /** 设置日志级别 */
  setLevel(level: 'debug' | 'info' | 'warn' | 'error'): void
}

export interface LifecycleEvent {
  /** 事件类型 */
  type: 'bootstrap' | 'mount' | 'unmount' | 'unload' | 'error'
  /** 应用名称 */
  appName: string
  /** 事件时间戳 */
  timestamp: number
  /** 事件数据 */
  data?: any
  /** 错误信息 */
  error?: Error
}

export interface RouteInfo {
  /** 路径 */
  path: string
  /** 查询参数 */
  query?: Record<string, string>
  /** 哈希值 */
  hash?: string
  /** 状态 */
  state?: any
}

export interface AppInfo {
  /** 应用名称 */
  name: string
  /** 应用状态 */
  status: string
  /** 应用版本 */
  version?: string
  /** 应用描述 */
  description?: string
  /** 应用入口 */
  entry: string
  /** 激活规则 */
  activeRule: any
  /** 应用属性 */
  props: Record<string, any>
}

// 重新导入其他类型以避免循环依赖
import type { AppConfig } from './app'
import type { PluginConfig } from './plugin'
import type { SandboxConfig } from './sandbox'