/**
 * 注册表相关类型定义
 */

export interface RegistryConfig {
  /** 最大应用数量 */
  maxApps?: number
  /** 是否启用缓存 */
  enableCache?: boolean
  /** 是否启用应用状态持久化 */
  enablePersistence?: boolean
  /** 应用配置验证规则 */
  validationRules?: ValidationRule[]
}

export interface ValidationRule {
  /** 规则名称 */
  name: string
  /** 验证函数 */
  validate: (config: any) => boolean | string
  /** 错误消息 */
  message?: string
}

export interface RegistryStats {
  /** 应用总数 */
  total: number
  /** 按状态分组的应用数量 */
  byStatus: Record<string, number>
  /** 最早注册的应用 */
  oldestApp?: string
  /** 最新注册的应用 */
  newestApp?: string
  /** 内存使用情况 */
  memoryUsage?: {
    apps: number
    cache: number
    total: number
  }
}

export interface AppQuery {
  /** 应用名称模式 */
  namePattern?: string | RegExp
  /** 应用状态过滤 */
  status?: string[]
  /** 路由匹配 */
  route?: string
  /** 自定义过滤函数 */
  filter?: (app: any) => boolean
}