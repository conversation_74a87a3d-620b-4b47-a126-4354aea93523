/**
 * 调度器相关类型定义
 */

export type TaskPriority = 'high' | 'normal' | 'low'

export interface Task {
  /** 任务ID */
  id: string
  /** 任务执行函数 */
  execute: () => Promise<void> | void
  /** 任务优先级 */
  priority: TaskPriority
  /** 重试次数 */
  retryCount: number
  /** 最大重试次数 */
  maxRetries: number
  /** 成功回调 */
  onSuccess?: () => void
  /** 错误回调 */
  onError?: (error: Error) => void
  /** 任务描述 */
  description?: string
  /** 任务创建时间 */
  createdAt?: number
}

export interface SchedulerConfig {
  /** 最大并发数 */
  maxConcurrency?: number
  /** 任务超时时间 */
  timeout?: number
  /** 默认重试次数 */
  retryCount?: number
  /** 是否启用任务队列持久化 */
  enablePersistence?: boolean
}

export interface TaskResult {
  /** 任务ID */
  taskId: string
  /** 执行状态 */
  status: 'success' | 'error' | 'timeout'
  /** 执行时间 */
  duration: number
  /** 错误信息 */
  error?: Error
  /** 执行结果 */
  result?: any
}