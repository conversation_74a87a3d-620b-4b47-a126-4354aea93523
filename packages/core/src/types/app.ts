/**
 * 应用相关类型定义
 */

export type AppStatus = 
  | 'not_loaded'
  | 'loading_source_code'
  | 'not_bootstrapped'
  | 'bootstrapping'
  | 'not_mounted'
  | 'mounting'
  | 'mounted'
  | 'unmounting'
  | 'unloading'
  | 'load_error'
  | 'skip_because_broken'

export interface AppConfig {
  /** 应用名称 */
  name: string
  /** 应用入口 */
  entry: string | AppEntry
  /** 应用容器 */
  container?: string | HTMLElement
  /** 激活规则 */
  activeRule: string | RegExp | ((pathname: string) => boolean) | (string | RegExp)[]
  /** 应用属性 */
  props?: Record<string, any>
  /** 自定义属性 */
  customProps?: Record<string, any>
  /** 资源加载器配置 */
  loader?: LoaderConfig
  /** 沙箱配置 */
  sandbox?: SandboxConfig
  /** 样式文件 */
  styles?: string[]
  /** 脚本文件 */
  scripts?: string[]
}

export interface AppEntry {
  /** HTML 内容 */
  html?: string
  /** 脚本文件列表 */
  scripts?: string[]
  /** 样式文件列表 */
  styles?: string[]
}

export interface MicroApp {
  /** 应用名称 */
  name: string
  /** 应用入口 */
  entry: string | AppEntry
  /** 应用容器 */
  container?: string | HTMLElement
  /** 激活规则 */
  activeRule: string | RegExp | ((pathname: string) => boolean) | (string | RegExp)[]
  /** 应用属性 */
  props: Record<string, any>
  /** 应用状态 */
  status: AppStatus
  /** 加载Promise */
  loadPromise: Promise<any> | null
  /** 挂载Promise */
  mountPromise: Promise<any> | null
  /** 卸载Promise */
  unmountPromise: Promise<any> | null
  /** 自定义属性 */
  customProps: Record<string, any>
  /** 资源加载器配置 */
  loader?: LoaderConfig
  /** 沙箱配置 */
  sandbox?: SandboxConfig
  /** 样式文件 */
  styles: string[]
  /** 脚本文件 */
  scripts: string[]
  /** 注册时间 */
  registeredAt: number
}

export interface LoaderConfig {
  /** 请求超时时间 */
  timeout?: number
  /** 是否启用缓存 */
  enableCache?: boolean
  /** 重试次数 */
  retryCount?: number
  /** 跨域设置 */
  crossOrigin?: 'anonymous' | 'use-credentials'
}

export interface SandboxConfig {
  /** 沙箱类型 */
  type?: 'proxy' | 'iframe' | 'web-component' | 'namespace' | 'federation'
  /** 是否启用JavaScript隔离 */
  enableJsIsolation?: boolean
  /** 是否启用CSS隔离 */
  enableCssIsolation?: boolean
  /** 是否启用全局变量隔离 */
  enableGlobalIsolation?: boolean
}