/**
 * 插件相关类型定义
 */

export interface Plugin {
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 插件描述 */
  description?: string
  /** 插件作者 */
  author?: string
  /** 插件依赖 */
  dependencies?: string[]
  /** 插件初始化函数 */
  install: (core: any, options?: any) => void | Promise<void>
  /** 插件卸载函数 */
  uninstall?: () => void | Promise<void>
  /** 插件配置 */
  options?: Record<string, any>
}

export interface PluginConfig {
  /** 插件名称 */
  name: string
  /** 插件选项 */
  options?: Record<string, any>
  /** 是否启用 */
  enabled?: boolean
  /** 加载优先级 */
  priority?: number
}

export interface PluginManager {
  /** 注册插件 */
  register(plugin: Plugin): void
  /** 加载插件 */
  load(pluginName: string): Promise<Plugin>
  /** 卸载插件 */
  unload(pluginName: string): Promise<void>
  /** 获取插件 */
  getPlugin(name: string): Plugin | undefined
  /** 获取所有插件 */
  getAllPlugins(): Plugin[]
  /** 检查插件是否已加载 */
  isLoaded(name: string): boolean
}

export interface PluginHooks {
  /** 应用加载前 */
  beforeLoad?: (app: any) => void | Promise<void>
  /** 应用加载后 */
  afterLoad?: (app: any) => void | Promise<void>
  /** 应用挂载前 */
  beforeMount?: (app: any) => void | Promise<void>
  /** 应用挂载后 */
  afterMount?: (app: any) => void | Promise<void>
  /** 应用卸载前 */
  beforeUnmount?: (app: any) => void | Promise<void>
  /** 应用卸载后 */
  afterUnmount?: (app: any) => void | Promise<void>
  /** 路由变化 */
  onRouteChange?: (from: string, to: string) => void | Promise<void>
  /** 错误处理 */
  onError?: (error: Error, app?: any) => void | Promise<void>
}