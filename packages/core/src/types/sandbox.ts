/**
 * 沙箱相关类型定义
 */

export type SandboxType = 'proxy' | 'define-property' | 'iframe' | 'web-component' | 'namespace' | 'federation'

export interface SandboxConfig {
  /** 沙箱类型 */
  type?: SandboxType
  /** 是否启用JavaScript隔离 */
  enableJsIsolation?: boolean
  /** 是否启用CSS隔离 */
  enableCssIsolation?: boolean
  /** 是否启用全局变量隔离 */
  enableGlobalIsolation?: boolean
  /** 沙箱名称 */
  name?: string
  /** 自定义配置 */
  customConfig?: Record<string, any>
}

export interface SandboxStrategy {
  /** 策略名称 */
  name: string
  /** 创建沙箱 */
  createSandbox(config: SandboxConfig): Sandbox
  /** 检查是否支持当前环境 */
  isSupported(): boolean
  /** 获取策略优先级 */
  getPriority(): number
}

export interface Sandbox {
  /** 沙箱名称 */
  name: string
  /** 沙箱类型 */
  type: SandboxType
  /** 激活沙箱 */
  activate(): void
  /** 停用沙箱 */
  deactivate(): void
  /** 执行代码 */
  execScript(script: string): any
  /** 设置全局变量 */
  setGlobal(key: string, value: any): void
  /** 获取全局变量 */
  getGlobal(key: string): any
  /** 清理沙箱 */
  destroy(): void
  /** 检查是否激活 */
  isActive(): boolean
}

export interface IsolationConfig {
  /** JavaScript隔离配置 */
  javascript?: {
    /** 白名单全局变量 */
    whitelist?: string[]
    /** 黑名单全局变量 */
    blacklist?: string[]
    /** 是否启用严格模式 */
    strictMode?: boolean
  }
  /** CSS隔离配置 */
  css?: {
    /** CSS前缀 */
    prefix?: string
    /** 是否启用Shadow DOM */
    enableShadowDOM?: boolean
    /** 样式隔离策略 */
    strategy?: 'prefix' | 'shadow' | 'scoped'
  }
  /** 全局变量隔离配置 */
  global?: {
    /** 共享的全局变量 */
    shared?: string[]
    /** 只读的全局变量 */
    readonly?: string[]
  }
}

export interface SandboxFactory {
  /** 创建沙箱 */
  createSandbox(strategy: SandboxType, config?: SandboxConfig): Sandbox
  /** 选择最优策略 */
  selectOptimalStrategy(env?: Environment): SandboxType
  /** 注册策略 */
  registerStrategy(strategy: SandboxStrategy): void
  /** 获取所有策略 */
  getAllStrategies(): SandboxStrategy[]
}

export interface Environment {
  /** 浏览器类型 */
  browser?: string
  /** 浏览器版本 */
  version?: string
  /** 是否支持Proxy */
  supportsProxy?: boolean
  /** 是否支持Shadow DOM */
  supportsShadowDOM?: boolean
  /** 是否支持Web Components */
  supportsWebComponents?: boolean
}