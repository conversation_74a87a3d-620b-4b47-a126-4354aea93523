/**
 * 资源加载器相关类型定义
 */

export type ResourceType = 'html' | 'script' | 'style' | 'json' | 'image'

export interface LoadResult {
  /** HTML 内容 */
  html?: string
  /** 脚本内容列表 */
  scripts?: string[]
  /** 样式内容列表 */
  styles?: string[]
  /** 加载时间戳 */
  loadedAt: number
  /** 资源大小 */
  size?: number
  /** 加载耗时 */
  duration?: number
}

export interface ResourceInfo {
  /** 资源URL */
  url: string
  /** 资源类型 */
  type: ResourceType
  /** 资源内容 */
  content: string
  /** 加载时间 */
  loadedAt: number
  /** 资源大小 */
  size: number
  /** 是否来自缓存 */
  fromCache: boolean
}

export interface LoaderConfig {
  /** 请求超时时间（毫秒） */
  timeout?: number
  /** 是否启用缓存 */
  enableCache?: boolean
  /** 重试次数 */
  retryCount?: number
  /** 跨域设置 */
  crossOrigin?: 'anonymous' | 'use-credentials'
  /** 自定义请求头 */
  headers?: Record<string, string>
  /** 缓存策略 */
  cacheStrategy?: 'memory' | 'localStorage' | 'sessionStorage'
  /** 缓存过期时间（毫秒） */
  cacheExpiry?: number
}

export interface LoadOptions {
  /** 是否强制重新加载 */
  forceReload?: boolean
  /** 自定义请求头 */
  headers?: Record<string, string>
  /** 超时时间 */
  timeout?: number
  /** 进度回调 */
  onProgress?: (loaded: number, total: number) => void
}

export interface LoaderStats {
  /** 缓存命中率 */
  cacheHitRate: number
  /** 总请求数 */
  totalRequests: number
  /** 缓存命中数 */
  cacheHits: number
  /** 平均加载时间 */
  averageLoadTime: number
  /** 失败请求数 */
  failedRequests: number
  /** 缓存大小 */
  cacheSize: number
}