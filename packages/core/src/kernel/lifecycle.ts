/**
 * 生命周期调度器
 * 负责管理微前端应用的完整生命周期
 */

import type { MicroApp, LifecycleHooks, AppStatus } from '../types'
import { MicroCoreError } from '@micro-core/shared'
import { logger } from '../utils/logger'

export class LifecycleScheduler {
  private hooks: Map<string, LifecycleHooks> = new Map()
  private appStatus: Map<string, AppStatus> = new Map()

  /**
   * 注册应用生命周期钩子
   */
  registerHooks(appName: string, hooks: LifecycleHooks): void {
    this.hooks.set(appName, hooks)
    logger.debug(`已注册应用 ${appName} 的生命周期钩子`)
  }

  /**
   * 启动应用
   */
  async bootstrap(app: MicroApp): Promise<void> {
    try {
      logger.info(`开始启动应用: ${app.name}`)
      this.setAppStatus(app.name, 'bootstrapping')

      const hooks = this.hooks.get(app.name)
      if (hooks?.bootstrap) {
        await hooks.bootstrap(app.props)
      }

      this.setAppStatus(app.name, 'not_mounted')
      logger.info(`应用 ${app.name} 启动完成`)
    } catch (error) {
      this.setAppStatus(app.name, 'load_error')
      throw new MicroCoreError(
        `应用 ${app.name} 启动失败: ${error.message}`,
        'BOOTSTRAP_ERROR',
        { appName: app.name, error }
      )
    }
  }

  /**
   * 挂载应用
   */
  async mount(app: MicroApp): Promise<void> {
    try {
      logger.info(`开始挂载应用: ${app.name}`)
      this.setAppStatus(app.name, 'mounting')

      const hooks = this.hooks.get(app.name)
      if (hooks?.mount) {
        await hooks.mount(app.props)
      }

      this.setAppStatus(app.name, 'mounted')
      logger.info(`应用 ${app.name} 挂载完成`)
    } catch (error) {
      this.setAppStatus(app.name, 'skip_because_broken')
      throw new MicroCoreError(
        `应用 ${app.name} 挂载失败: ${error.message}`,
        'MOUNT_ERROR',
        { appName: app.name, error }
      )
    }
  }

  /**
   * 卸载应用
   */
  async unmount(app: MicroApp): Promise<void> {
    try {
      logger.info(`开始卸载应用: ${app.name}`)
      this.setAppStatus(app.name, 'unmounting')

      const hooks = this.hooks.get(app.name)
      if (hooks?.unmount) {
        await hooks.unmount(app.props)
      }

      this.setAppStatus(app.name, 'not_mounted')
      logger.info(`应用 ${app.name} 卸载完成`)
    } catch (error) {
      this.setAppStatus(app.name, 'skip_because_broken')
      throw new MicroCoreError(
        `应用 ${app.name} 卸载失败: ${error.message}`,
        'UNMOUNT_ERROR',
        { appName: app.name, error }
      )
    }
  }

  /**
   * 卸载并清理应用
   */
  async unload(app: MicroApp): Promise<void> {
    try {
      logger.info(`开始卸载并清理应用: ${app.name}`)
      
      // 先卸载应用
      if (this.getAppStatus(app.name) === 'mounted') {
        await this.unmount(app)
      }

      const hooks = this.hooks.get(app.name)
      if (hooks?.unload) {
        await hooks.unload(app.props)
      }

      // 清理状态和钩子
      this.appStatus.delete(app.name)
      this.hooks.delete(app.name)
      
      logger.info(`应用 ${app.name} 卸载并清理完成`)
    } catch (error) {
      throw new MicroCoreError(
        `应用 ${app.name} 卸载清理失败: ${error.message}`,
        'UNLOAD_ERROR',
        { appName: app.name, error }
      )
    }
  }

  /**
   * 获取应用状态
   */
  getAppStatus(appName: string): AppStatus {
    return this.appStatus.get(appName) || 'not_loaded'
  }

  /**
   * 设置应用状态
   */
  private setAppStatus(appName: string, status: AppStatus): void {
    this.appStatus.set(appName, status)
    logger.debug(`应用 ${appName} 状态变更为: ${status}`)
  }

  /**
   * 获取所有应用状态
   */
  getAllAppStatus(): Record<string, AppStatus> {
    const result: Record<string, AppStatus> = {}
    this.appStatus.forEach((status, name) => {
      result[name] = status
    })
    return result
  }
}