/**
 * 资源加载器
 * 负责加载微前端应用的静态资源和代码
 */

import type { MicroApp, LoaderConfig, ResourceType, LoadResult } from '../types'
import { MicroCoreError } from '@micro-core/shared'
import { logger } from '../utils/logger'

export class ResourceLoader {
  private config: LoaderConfig
  private cache: Map<string, LoadResult> = new Map()
  private loadingPromises: Map<string, Promise<LoadResult>> = new Map()

  constructor(config: LoaderConfig = {}) {
    this.config = {
      timeout: 30000,
      enableCache: true,
      retryCount: 3,
      crossOrigin: 'anonymous',
      ...config
    }
  }

  /**
   * 加载应用资源
   */
  async loadApp(app: MicroApp): Promise<LoadResult> {
    const cacheKey = `${app.name}:${app.entry}`
    
    // 检查缓存
    if (this.config.enableCache && this.cache.has(cacheKey)) {
      logger.debug(`从缓存加载应用资源: ${app.name}`)
      return this.cache.get(cacheKey)!
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(cacheKey)) {
      logger.debug(`应用 ${app.name} 正在加载中，等待完成`)
      return this.loadingPromises.get(cacheKey)!
    }

    // 开始加载
    const loadPromise = this.doLoadApp(app)
    this.loadingPromises.set(cacheKey, loadPromise)

    try {
      const result = await loadPromise
      
      // 缓存结果
      if (this.config.enableCache) {
        this.cache.set(cacheKey, result)
      }
      
      return result
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  /**
   * 执行应用加载
   */
  private async doLoadApp(app: MicroApp): Promise<LoadResult> {
    logger.info(`开始加载应用资源: ${app.name}`)
    
    try {
      let html: string
      let scripts: string[] = []
      let styles: string[] = []

      if (typeof app.entry === 'string') {
        // 加载HTML入口
        html = await this.loadResource(app.entry, 'html')
        
        // 解析HTML中的资源
        const resources = this.parseHtmlResources(html, app.entry)
        
        // 加载脚本
        scripts = await Promise.all(
          resources.scripts.map(url => this.loadResource(url, 'script'))
        )
        
        // 加载样式
        styles = await Promise.all(
          resources.styles.map(url => this.loadResource(url, 'style'))
        )
      } else {
        // 直接指定的资源配置
        html = app.entry.html || ''
        
        if (app.entry.scripts) {
          scripts = await Promise.all(
            app.entry.scripts.map(url => this.loadResource(url, 'script'))
          )
        }
        
        if (app.entry.styles) {
          styles = await Promise.all(
            app.entry.styles.map(url => this.loadResource(url, 'style'))
          )
        }
      }

      const result: LoadResult = {
        html,
        scripts,
        styles,
        loadedAt: Date.now()
      }

      logger.info(`应用 ${app.name} 资源加载完成`)
      return result

    } catch (error) {
      logger.error(`应用 ${app.name} 资源加载失败:`, error)
      throw new MicroCoreError(
        `应用 ${app.name} 资源加载失败: ${error.message}`,
        'RESOURCE_LOAD_ERROR',
        { appName: app.name, error }
      )
    }
  }

  /**
   * 加载单个资源
   */
  private async loadResource(url: string, type: ResourceType): Promise<string> {
    const cacheKey = `${type}:${url}`
    
    // 检查缓存
    if (this.config.enableCache && this.cache.has(cacheKey)) {
      return (this.cache.get(cacheKey) as any).content || ''
    }

    let retryCount = 0
    const maxRetries = this.config.retryCount

    while (retryCount <= maxRetries) {
      try {
        const content = await this.fetchResource(url, type)
        
        // 缓存内容
        if (this.config.enableCache) {
          this.cache.set(cacheKey, { content } as any)
        }
        
        return content
      } catch (error) {
        retryCount++
        if (retryCount > maxRetries) {
          throw error
        }
        
        logger.warn(`资源加载失败，准备重试 (${retryCount}/${maxRetries}): ${url}`)
        await this.sleep(1000 * retryCount) // 递增延时重试
      }
    }

    throw new Error(`资源加载失败: ${url}`)
  }

  /**
   * 获取资源内容
   */
  private async fetchResource(url: string, type: ResourceType): Promise<string> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`资源加载超时: ${url}`))
      }, this.config.timeout)

      if (type === 'html' || type === 'script' || type === 'style') {
        // 使用 fetch 加载文本资源
        fetch(url, {
          mode: 'cors',
          credentials: 'omit'
        })
          .then(response => {
            if (!response.ok) {
              throw new Error(`HTTP ${response.status}: ${response.statusText}`)
            }
            return response.text()
          })
          .then(content => {
            clearTimeout(timeoutId)
            resolve(content)
          })
          .catch(error => {
            clearTimeout(timeoutId)
            reject(error)
          })
      } else {
        clearTimeout(timeoutId)
        reject(new Error(`不支持的资源类型: ${type}`))
      }
    })
  }

  /**
   * 解析HTML中的资源链接
   */
  private parseHtmlResources(html: string, baseUrl: string): {
    scripts: string[]
    styles: string[]
  } {
    const scripts: string[] = []
    const styles: string[] = []

    // 解析script标签
    const scriptRegex = /<script[^>]*src=["']([^"']+)["'][^>]*><\/script>/gi
    let scriptMatch
    while ((scriptMatch = scriptRegex.exec(html)) !== null) {
      const src = this.resolveUrl(scriptMatch[1], baseUrl)
      scripts.push(src)
    }

    // 解析link标签中的CSS
    const linkRegex = /<link[^>]*rel=["']stylesheet["'][^>]*href=["']([^"']+)["'][^>]*>/gi
    let linkMatch
    while ((linkMatch = linkRegex.exec(html)) !== null) {
      const href = this.resolveUrl(linkMatch[1], baseUrl)
      styles.push(href)
    }

    // 解析style标签
    const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi
    let styleMatch
    while ((styleMatch = styleRegex.exec(html)) !== null) {
      styles.push(styleMatch[1])
    }

    return { scripts, styles }
  }

  /**
   * 解析相对URL为绝对URL
   */
  private resolveUrl(url: string, baseUrl: string): string {
    if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//')) {
      return url
    }
    
    try {
      return new URL(url, baseUrl).href
    } catch {
      return url
    }
  }

  /**
   * 预加载应用资源
   */
  async preloadApp(app: MicroApp): Promise<void> {
    try {
      await this.loadApp(app)
      logger.info(`应用 ${app.name} 预加载完成`)
    } catch (error) {
      logger.warn(`应用 ${app.name} 预加载失败:`, error)
    }
  }

  /**
   * 清除缓存
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern)
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key)
        }
      }
      logger.info(`已清除匹配模式 ${pattern} 的缓存`)
    } else {
      this.cache.clear()
      logger.info('已清除所有缓存')
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    size: number
    keys: string[]
    totalSize: number
  } {
    const keys = Array.from(this.cache.keys())
    let totalSize = 0
    
    this.cache.forEach(value => {
      if (typeof value === 'object' && value.content) {
        totalSize += value.content.length
      } else if (typeof value === 'string') {
        totalSize += value.length
      }
    })

    return {
      size: this.cache.size,
      keys,
      totalSize
    }
  }

  /**
   * 工具方法：延时
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}