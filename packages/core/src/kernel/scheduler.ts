/**
 * 任务调度器
 * 负责管理微前端应用的任务调度和执行
 */

import type { SchedulerConfig, Task, TaskPriority } from '../types'
import { MicroCoreError } from '@micro-core/shared'
import { logger } from '../utils/logger'

export class TaskScheduler {
  private config: SchedulerConfig
  private taskQueue: Task[] = []
  private runningTasks: Set<string> = new Set()
  private isRunning = false

  constructor(config: SchedulerConfig = {}) {
    this.config = {
      maxConcurrency: 3,
      timeout: 30000,
      retryCount: 3,
      ...config
    }
  }

  /**
   * 添加任务到调度队列
   */
  addTask(task: Task): void {
    // 验证任务
    if (!task.id || !task.execute) {
      throw new MicroCoreError('任务必须包含 id 和 execute 方法', 'INVALID_TASK')
    }

    // 检查是否已存在相同任务
    if (this.taskQueue.some(t => t.id === task.id) || this.runningTasks.has(task.id)) {
      logger.warn(`任务 ${task.id} 已存在，跳过添加`)
      return
    }

    // 设置默认优先级
    task.priority = task.priority || 'normal'
    task.retryCount = task.retryCount || 0
    task.maxRetries = task.maxRetries || this.config.retryCount

    this.taskQueue.push(task)
    this.sortTaskQueue()
    
    logger.debug(`任务 ${task.id} 已添加到调度队列`)

    // 如果调度器未运行，启动它
    if (!this.isRunning) {
      this.start()
    }
  }

  /**
   * 启动任务调度器
   */
  start(): void {
    if (this.isRunning) {
      return
    }

    this.isRunning = true
    logger.info('任务调度器已启动')
    this.processQueue()
  }

  /**
   * 停止任务调度器
   */
  stop(): void {
    this.isRunning = false
    logger.info('任务调度器已停止')
  }

  /**
   * 处理任务队列
   */
  private async processQueue(): Promise<void> {
    while (this.isRunning && (this.taskQueue.length > 0 || this.runningTasks.size > 0)) {
      // 检查是否可以执行新任务
      if (this.runningTasks.size < this.config.maxConcurrency && this.taskQueue.length > 0) {
        const task = this.taskQueue.shift()!
        this.executeTask(task)
      }

      // 等待一段时间再检查
      await this.sleep(100)
    }

    this.isRunning = false
    logger.info('任务队列处理完成，调度器已停止')
  }

  /**
   * 执行单个任务
   */
  private async executeTask(task: Task): Promise<void> {
    this.runningTasks.add(task.id)
    logger.debug(`开始执行任务: ${task.id}`)

    try {
      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(`任务 ${task.id} 执行超时`))
        }, this.config.timeout)
      })

      // 执行任务
      await Promise.race([
        task.execute(),
        timeoutPromise
      ])

      logger.info(`任务 ${task.id} 执行成功`)
      
      // 执行成功回调
      if (task.onSuccess) {
        task.onSuccess()
      }

    } catch (error) {
      logger.error(`任务 ${task.id} 执行失败:`, error)

      // 检查是否需要重试
      if (task.retryCount < task.maxRetries) {
        task.retryCount++
        logger.info(`任务 ${task.id} 准备重试，第 ${task.retryCount} 次`)
        
        // 重新添加到队列
        this.taskQueue.unshift(task)
        this.sortTaskQueue()
      } else {
        logger.error(`任务 ${task.id} 重试次数已达上限，执行失败`)
        
        // 执行失败回调
        if (task.onError) {
          task.onError(error)
        }
      }
    } finally {
      this.runningTasks.delete(task.id)
    }
  }

  /**
   * 按优先级排序任务队列
   */
  private sortTaskQueue(): void {
    const priorityOrder: Record<TaskPriority, number> = {
      high: 3,
      normal: 2,
      low: 1
    }

    this.taskQueue.sort((a, b) => {
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    pending: number
    running: number
    isRunning: boolean
  } {
    return {
      pending: this.taskQueue.length,
      running: this.runningTasks.size,
      isRunning: this.isRunning
    }
  }

  /**
   * 清空任务队列
   */
  clearQueue(): void {
    this.taskQueue = []
    logger.info('任务队列已清空')
  }

  /**
   * 工具方法：延时
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}