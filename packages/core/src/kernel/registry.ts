/**
 * 应用注册表
 * 负责管理微前端应用的注册、查找和状态管理
 */

import type { MicroApp, AppConfig, RegistryConfig, AppStatus } from '../types'
import { MicroCoreError } from '@micro-core/shared'
import { logger } from '../utils/logger'

export class ApplicationRegistry {
  private apps: Map<string, MicroApp> = new Map()
  private config: RegistryConfig

  constructor(config: RegistryConfig = {}) {
    this.config = {
      maxApps: 50,
      enableCache: true,
      ...config
    }
  }

  /**
   * 注册应用
   */
  register(appConfig: AppConfig): void {
    // 验证应用配置
    this.validateAppConfig(appConfig)

    // 检查应用数量限制
    if (this.apps.size >= this.config.maxApps) {
      throw new MicroCoreError(
        `应用注册数量已达上限: ${this.config.maxApps}`,
        'MAX_APPS_EXCEEDED'
      )
    }

    // 检查应用是否已存在
    if (this.apps.has(appConfig.name)) {
      logger.warn(`应用 ${appConfig.name} 已存在，将覆盖原有配置`)
    }

    // 创建微应用实例
    const microApp: MicroApp = {
      name: appConfig.name,
      entry: appConfig.entry,
      container: appConfig.container,
      activeRule: appConfig.activeRule,
      props: appConfig.props || {},
      status: 'not_loaded',
      loadPromise: null,
      mountPromise: null,
      unmountPromise: null,
      customProps: appConfig.customProps || {},
      loader: appConfig.loader,
      sandbox: appConfig.sandbox,
      styles: appConfig.styles || [],
      scripts: appConfig.scripts || [],
      registeredAt: Date.now()
    }

    this.apps.set(appConfig.name, microApp)
    logger.info(`应用 ${appConfig.name} 注册成功`)
  }

  /**
   * 获取应用
   */
  getApp(name: string): MicroApp | undefined {
    const app = this.apps.get(name)
    if (!app) {
      logger.warn(`应用 ${name} 未找到`)
    }
    return app
  }

  /**
   * 获取所有应用
   */
  getAllApps(): MicroApp[] {
    return Array.from(this.apps.values())
  }

  /**
   * 获取活跃应用
   */
  getActiveApps(): MicroApp[] {
    return this.getAllApps().filter(app => 
      app.status === 'mounted' || app.status === 'mounting'
    )
  }

  /**
   * 根据路由规则获取匹配的应用
   */
  getAppsByRoute(pathname: string): MicroApp[] {
    return this.getAllApps().filter(app => {
      if (typeof app.activeRule === 'string') {
        return pathname.startsWith(app.activeRule)
      }
      if (typeof app.activeRule === 'function') {
        return app.activeRule(pathname)
      }
      if (app.activeRule instanceof RegExp) {
        return app.activeRule.test(pathname)
      }
      return false
    })
  }

  /**
   * 注销应用
   */
  unregister(name: string): boolean {
    const app = this.apps.get(name)
    if (!app) {
      logger.warn(`尝试注销不存在的应用: ${name}`)
      return false
    }

    // 检查应用状态
    if (app.status === 'mounted' || app.status === 'mounting') {
      throw new MicroCoreError(
        `无法注销正在运行的应用: ${name}`,
        'APP_STILL_ACTIVE'
      )
    }

    this.apps.delete(name)
    logger.info(`应用 ${name} 注销成功`)
    return true
  }

  /**
   * 更新应用状态
   */
  updateAppStatus(name: string, status: AppStatus): void {
    const app = this.apps.get(name)
    if (!app) {
      throw new MicroCoreError(`应用 ${name} 不存在`, 'APP_NOT_FOUND')
    }

    const oldStatus = app.status
    app.status = status
    logger.debug(`应用 ${name} 状态从 ${oldStatus} 变更为 ${status}`)
  }

  /**
   * 更新应用属性
   */
  updateAppProps(name: string, props: Record<string, any>): void {
    const app = this.apps.get(name)
    if (!app) {
      throw new MicroCoreError(`应用 ${name} 不存在`, 'APP_NOT_FOUND')
    }

    app.props = { ...app.props, ...props }
    logger.debug(`应用 ${name} 属性已更新`)
  }

  /**
   * 获取注册表统计信息
   */
  getStats(): {
    total: number
    byStatus: Record<AppStatus, number>
    oldestApp?: string
    newestApp?: string
  } {
    const apps = this.getAllApps()
    const stats = {
      total: apps.length,
      byStatus: {} as Record<AppStatus, number>,
      oldestApp: undefined as string | undefined,
      newestApp: undefined as string | undefined
    }

    // 统计各状态应用数量
    const statusCounts: Record<AppStatus, number> = {
      not_loaded: 0,
      loading_source_code: 0,
      not_bootstrapped: 0,
      bootstrapping: 0,
      not_mounted: 0,
      mounting: 0,
      mounted: 0,
      unmounting: 0,
      unloading: 0,
      load_error: 0,
      skip_because_broken: 0
    }

    let oldestTime = Infinity
    let newestTime = 0

    apps.forEach(app => {
      statusCounts[app.status]++
      
      if (app.registeredAt < oldestTime) {
        oldestTime = app.registeredAt
        stats.oldestApp = app.name
      }
      
      if (app.registeredAt > newestTime) {
        newestTime = app.registeredAt
        stats.newestApp = app.name
      }
    })

    stats.byStatus = statusCounts
    return stats
  }

  /**
   * 清空注册表
   */
  clear(): void {
    const activeApps = this.getActiveApps()
    if (activeApps.length > 0) {
      throw new MicroCoreError(
        `无法清空注册表，存在活跃应用: ${activeApps.map(app => app.name).join(', ')}`,
        'ACTIVE_APPS_EXIST'
      )
    }

    this.apps.clear()
    logger.info('应用注册表已清空')
  }

  /**
   * 验证应用配置
   */
  private validateAppConfig(config: AppConfig): void {
    if (!config.name) {
      throw new MicroCoreError('应用名称不能为空', 'INVALID_APP_NAME')
    }

    if (!config.entry) {
      throw new MicroCoreError('应用入口不能为空', 'INVALID_APP_ENTRY')
    }

    if (!config.activeRule) {
      throw new MicroCoreError('应用激活规则不能为空', 'INVALID_ACTIVE_RULE')
    }

    // 验证应用名称格式
    if (!/^[a-zA-Z][a-zA-Z0-9-_]*$/.test(config.name)) {
      throw new MicroCoreError(
        '应用名称只能包含字母、数字、连字符和下划线，且必须以字母开头',
        'INVALID_APP_NAME_FORMAT'
      )
    }

    // 验证入口地址格式
    if (typeof config.entry === 'string') {
      try {
        new URL(config.entry)
      } catch {
        throw new MicroCoreError(
          `应用入口地址格式无效: ${config.entry}`,
          'INVALID_ENTRY_URL'
        )
      }
    }
  }

  /**
   * 导出注册表配置
   */
  exportConfig(): AppConfig[] {
    return this.getAllApps().map(app => ({
      name: app.name,
      entry: app.entry,
      container: app.container,
      activeRule: app.activeRule,
      props: app.props,
      customProps: app.customProps,
      loader: app.loader,
      sandbox: app.sandbox,
      styles: app.styles,
      scripts: app.scripts
    }))
  }

  /**
   * 批量注册应用
   */
  registerBatch(configs: AppConfig[]): void {
    const errors: string[] = []

    configs.forEach(config => {
      try {
        this.register(config)
      } catch (error) {
        errors.push(`${config.name}: ${error.message}`)
      }
    })

    if (errors.length > 0) {
      throw new MicroCoreError(
        `批量注册失败:\n${errors.join('\n')}`,
        'BATCH_REGISTER_ERROR'
      )
    }

    logger.info(`批量注册 ${configs.length} 个应用成功`)
  }
}