/**
 * 日志工具
 * 提供统一的日志记录功能
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

export interface LoggerConfig {
  /** 日志级别 */
  level?: LogLevel
  /** 是否启用控制台输出 */
  enableConsole?: boolean
  /** 日志前缀 */
  prefix?: string
  /** 是否显示时间戳 */
  showTimestamp?: boolean
}

class Logger {
  private config: Required<LoggerConfig>
  private levels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3
  }

  constructor(config: LoggerConfig = {}) {
    this.config = {
      level: 'info',
      enableConsole: true,
      prefix: '[MicroCore]',
      showTimestamp: true,
      ...config
    }
  }

  /**
   * 调试日志
   */
  debug(message: string, ...args: any[]): void {
    this.log('debug', message, ...args)
  }

  /**
   * 信息日志
   */
  info(message: string, ...args: any[]): void {
    this.log('info', message, ...args)
  }

  /**
   * 警告日志
   */
  warn(message: string, ...args: any[]): void {
    this.log('warn', message, ...args)
  }

  /**
   * 错误日志
   */
  error(message: string, ...args: any[]): void {
    this.log('error', message, ...args)
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel): void {
    this.config.level = level
  }

  /**
   * 获取当前日志级别
   */
  getLevel(): LogLevel {
    return this.config.level
  }

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, ...args: any[]): void {
    if (!this.shouldLog(level)) {
      return
    }

    if (!this.config.enableConsole) {
      return
    }

    const timestamp = this.config.showTimestamp ? new Date().toISOString() : ''
    const prefix = this.config.prefix
    const logMessage = `${timestamp} ${prefix} [${level.toUpperCase()}] ${message}`

    switch (level) {
      case 'debug':
        console.debug(logMessage, ...args)
        break
      case 'info':
        console.info(logMessage, ...args)
        break
      case 'warn':
        console.warn(logMessage, ...args)
        break
      case 'error':
        console.error(logMessage, ...args)
        break
    }
  }

  /**
   * 检查是否应该记录日志
   */
  private shouldLog(level: LogLevel): boolean {
    return this.levels[level] >= this.levels[this.config.level]
  }
}

// 创建默认日志实例
export const logger = new Logger()

// 导出Logger类供自定义使用
export { Logger }