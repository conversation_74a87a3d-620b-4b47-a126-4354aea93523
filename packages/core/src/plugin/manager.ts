/**
 * 插件管理器
 * 负责插件的注册、加载、卸载和生命周期管理
 */

import type { Plugin, PluginConfig, PluginHooks } from '../types'
import { MicroCoreError } from '@micro-core/shared'
import { logger } from '../utils/logger'

export class PluginManager {
  private plugins: Map<string, Plugin> = new Map()
  private loadedPlugins: Set<string> = new Set()
  private hooks: Map<string, PluginHooks> = new Map()
  private core: any

  constructor(core: any) {
    this.core = core
  }

  /**
   * 注册插件
   */
  register(plugin: Plugin): void {
    // 验证插件
    this.validatePlugin(plugin)

    // 检查插件是否已存在
    if (this.plugins.has(plugin.name)) {
      logger.warn(`插件 ${plugin.name} 已存在，将覆盖原有插件`)
    }

    this.plugins.set(plugin.name, plugin)
    logger.info(`插件 ${plugin.name} 注册成功`)
  }

  /**
   * 加载插件
   */
  async load(pluginName: string): Promise<Plugin> {
    const plugin = this.plugins.get(pluginName)
    if (!plugin) {
      throw new MicroCoreError(`插件 ${pluginName} 未找到`, 'PLUGIN_NOT_FOUND')
    }

    if (this.loadedPlugins.has(pluginName)) {
      logger.debug(`插件 ${pluginName} 已加载`)
      return plugin
    }

    try {
      logger.info(`开始加载插件: ${pluginName}`)

      // 检查依赖
      await this.checkDependencies(plugin)

      // 执行插件安装
      await plugin.install(this.core, plugin.options)

      this.loadedPlugins.add(pluginName)
      logger.info(`插件 ${pluginName} 加载完成`)

      return plugin
    } catch (error) {
      logger.error(`插件 ${pluginName} 加载失败:`, error)
      throw new MicroCoreError(
        `插件 ${pluginName} 加载失败: ${error.message}`,
        'PLUGIN_LOAD_ERROR',
        { pluginName, error }
      )
    }
  }

  /**
   * 卸载插件
   */
  async unload(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName)
    if (!plugin) {
      throw new MicroCoreError(`插件 ${pluginName} 未找到`, 'PLUGIN_NOT_FOUND')
    }

    if (!this.loadedPlugins.has(pluginName)) {
      logger.debug(`插件 ${pluginName} 未加载`)
      return
    }

    try {
      logger.info(`开始卸载插件: ${pluginName}`)

      // 执行插件卸载
      if (plugin.uninstall) {
        await plugin.uninstall()
      }

      this.loadedPlugins.delete(pluginName)
      this.hooks.delete(pluginName)
      
      logger.info(`插件 ${pluginName} 卸载完成`)
    } catch (error) {
      logger.error(`插件 ${pluginName} 卸载失败:`, error)
      throw new MicroCoreError(
        `插件 ${pluginName} 卸载失败: ${error.message}`,
        'PLUGIN_UNLOAD_ERROR',
        { pluginName, error }
      )
    }
  }

  /**
   * 获取插件
   */
  getPlugin(name: string): Plugin | undefined {
    return this.plugins.get(name)
  }

  /**
   * 获取所有插件
   */
  getAllPlugins(): Plugin[] {
    return Array.from(this.plugins.values())
  }

  /**
   * 获取已加载的插件
   */
  getLoadedPlugins(): Plugin[] {
    return Array.from(this.loadedPlugins).map(name => this.plugins.get(name)!).filter(Boolean)
  }

  /**
   * 检查插件是否已加载
   */
  isLoaded(name: string): boolean {
    return this.loadedPlugins.has(name)
  }

  /**
   * 批量加载插件
   */
  async loadPlugins(configs: PluginConfig[]): Promise<void> {
    // 按优先级排序
    const sortedConfigs = configs
      .filter(config => config.enabled !== false)
      .sort((a, b) => (b.priority || 0) - (a.priority || 0))

    const errors: string[] = []

    for (const config of sortedConfigs) {
      try {
        // 设置插件选项
        const plugin = this.plugins.get(config.name)
        if (plugin && config.options) {
          plugin.options = { ...plugin.options, ...config.options }
        }

        await this.load(config.name)
      } catch (error) {
        errors.push(`${config.name}: ${error.message}`)
      }
    }

    if (errors.length > 0) {
      logger.warn(`部分插件加载失败:\n${errors.join('\n')}`)
    }
  }

  /**
   * 注册插件钩子
   */
  registerHooks(pluginName: string, hooks: PluginHooks): void {
    this.hooks.set(pluginName, hooks)
    logger.debug(`插件 ${pluginName} 钩子已注册`)
  }

  /**
   * 执行钩子
   */
  async executeHook(hookName: keyof PluginHooks, ...args: any[]): Promise<void> {
    const promises: Promise<void>[] = []

    this.hooks.forEach((hooks, pluginName) => {
      const hook = hooks[hookName]
      if (hook) {
        try {
          const result = hook(...args)
          if (result instanceof Promise) {
            promises.push(result)
          }
        } catch (error) {
          logger.error(`插件 ${pluginName} 钩子 ${hookName} 执行失败:`, error)
        }
      }
    })

    if (promises.length > 0) {
      await Promise.all(promises)
    }
  }

  /**
   * 获取插件统计信息
   */
  getStats(): {
    total: number
    loaded: number
    unloaded: number
    withHooks: number
  } {
    return {
      total: this.plugins.size,
      loaded: this.loadedPlugins.size,
      unloaded: this.plugins.size - this.loadedPlugins.size,
      withHooks: this.hooks.size
    }
  }

  /**
   * 清空所有插件
   */
  async clear(): Promise<void> {
    const loadedPlugins = Array.from(this.loadedPlugins)
    
    for (const pluginName of loadedPlugins) {
      try {
        await this.unload(pluginName)
      } catch (error) {
        logger.error(`清空插件时卸载 ${pluginName} 失败:`, error)
      }
    }

    this.plugins.clear()
    this.loadedPlugins.clear()
    this.hooks.clear()
    
    logger.info('所有插件已清空')
  }

  /**
   * 验证插件
   */
  private validatePlugin(plugin: Plugin): void {
    if (!plugin.name) {
      throw new MicroCoreError('插件名称不能为空', 'INVALID_PLUGIN_NAME')
    }

    if (!plugin.version) {
      throw new MicroCoreError('插件版本不能为空', 'INVALID_PLUGIN_VERSION')
    }

    if (typeof plugin.install !== 'function') {
      throw new MicroCoreError('插件必须提供 install 方法', 'INVALID_PLUGIN_INSTALL')
    }

    // 验证插件名称格式
    if (!/^[a-zA-Z][a-zA-Z0-9-_]*$/.test(plugin.name)) {
      throw new MicroCoreError(
        '插件名称只能包含字母、数字、连字符和下划线，且必须以字母开头',
        'INVALID_PLUGIN_NAME_FORMAT'
      )
    }

    // 验证版本格式
    if (!/^\d+\.\d+\.\d+/.test(plugin.version)) {
      throw new MicroCoreError(
        '插件版本必须符合语义化版本格式 (x.y.z)',
        'INVALID_PLUGIN_VERSION_FORMAT'
      )
    }
  }

  /**
   * 检查插件依赖
   */
  private async checkDependencies(plugin: Plugin): Promise<void> {
    if (!plugin.dependencies || plugin.dependencies.length === 0) {
      return
    }

    const missingDeps: string[] = []

    for (const dep of plugin.dependencies) {
      if (!this.plugins.has(dep)) {
        missingDeps.push(dep)
      } else if (!this.loadedPlugins.has(dep)) {
        // 自动加载依赖
        try {
          await this.load(dep)
        } catch (error) {
          missingDeps.push(dep)
        }
      }
    }

    if (missingDeps.length > 0) {
      throw new MicroCoreError(
        `插件 ${plugin.name} 缺少依赖: ${missingDeps.join(', ')}`,
        'PLUGIN_DEPENDENCIES_MISSING'
      )
    }
  }
}