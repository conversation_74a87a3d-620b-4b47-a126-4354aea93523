/**
 * 沙箱管理器
 * 负责管理多个沙箱实例的生命周期
 */

import type {
  SandboxStrategy,
  SandboxConfig,
  SandboxManagerConfig,
  SandboxCreateOptions,
  SandboxQueryOptions,
  SandboxStats,
  SandboxBatchOptions,
  SandboxMetrics,
  SandboxEvent,
  SandboxEventListener
} from './types'
import { SandboxStrategyFactory } from './factory'
import { SandboxState } from '@micro-core/shared'
import { SANDBOX_EVENTS, DEFAULT_SANDBOX_CONFIG } from './constants'

export class SandboxManager {
  private sandboxes: Map<string, SandboxStrategy> = new Map()
  private factory: SandboxStrategyFactory
  private config: SandboxManagerConfig
  private metrics: Map<string, SandboxMetrics> = new Map()
  private eventListeners: Map<string, SandboxEventListener[]> = new Map()
  private cleanupTimer: number | null = null

  constructor(config: SandboxManagerConfig = {}) {
    this.config = {
      defaultStrategy: 'proxy',
      maxSandboxes: 50,
      enablePerformanceMonitoring: true,
      enableAutoCleanup: true,
      autoCleanupInterval: 300000, // 5分钟
      sandboxTimeout: 30000,
      ...config
    }

    this.factory = new SandboxStrategyFactory({
      defaultStrategy: this.config.defaultStrategy,
      autoSelectStrategy: true
    })

    // 启动自动清理
    if (this.config.enableAutoCleanup) {
      this.startAutoCleanup()
    }

    // 注册全局错误处理器
    if (this.config.globalErrorHandler) {
      this.on('error', (event) => {
        this.config.globalErrorHandler!(event.error!, event.sandboxName)
      })
    }

    // 注册配置中的事件监听器
    if (this.config.eventListeners) {
      Object.entries(this.config.eventListeners).forEach(([eventType, listener]) => {
        this.on(eventType, listener)
      })
    }
  }

  /**
   * 创建沙箱
   */
  create(options: SandboxCreateOptions): SandboxStrategy {
    const { name, strategy, config = {}, autoActivate = false } = options

    // 检查沙箱数量限制
    if (this.sandboxes.size >= this.config.maxSandboxes) {
      throw new Error(`沙箱数量已达上限: ${this.config.maxSandboxes}`)
    }

    // 检查名称冲突
    if (this.sandboxes.has(name)) {
      throw new Error(`沙箱 ${name} 已存在`)
    }

    try {
      // 合并配置
      const finalConfig: SandboxConfig = {
        ...DEFAULT_SANDBOX_CONFIG,
        ...config,
        name
      }

      // 创建沙箱实例
      const sandbox = this.factory.createStrategy(name, strategy, finalConfig)

      // 注册沙箱
      this.sandboxes.set(name, sandbox)

      // 初始化性能指标
      if (this.config.enablePerformanceMonitoring) {
        this.initializeMetrics(name)
      }

      // 触发创建事件
      this.emitEvent('create', name, { strategy, config: finalConfig })

      // 自动激活
      if (autoActivate) {
        this.activate(name)
      }

      return sandbox
    } catch (error) {
      this.emitEvent('error', name, undefined, error as Error)
      throw error
    }
  }

  /**
   * 获取沙箱
   */
  get(name: string): SandboxStrategy | undefined {
    return this.sandboxes.get(name)
  }

  /**
   * 检查沙箱是否存在
   */
  has(name: string): boolean {
    return this.sandboxes.has(name)
  }

  /**
   * 激活沙箱
   */
  activate(name: string): void {
    const sandbox = this.sandboxes.get(name)
    if (!sandbox) {
      throw new Error(`沙箱 ${name} 不存在`)
    }

    try {
      this.emitEvent('activate', name)
      sandbox.activate()
      
      // 更新指标
      if (this.config.enablePerformanceMonitoring) {
        const metrics = this.metrics.get(name)
        if (metrics) {
          metrics.activateTime = Date.now()
        }
      }
    } catch (error) {
      this.emitEvent('error', name, undefined, error as Error)
      throw error
    }
  }

  /**
   * 停用沙箱
   */
  deactivate(name: string): void {
    const sandbox = this.sandboxes.get(name)
    if (!sandbox) {
      throw new Error(`沙箱 ${name} 不存在`)
    }

    try {
      this.emitEvent('deactivate', name)
      sandbox.deactivate()
    } catch (error) {
      this.emitEvent('error', name, undefined, error as Error)
      throw error
    }
  }

  /**
   * 销毁沙箱
   */
  destroy(name: string): void {
    const sandbox = this.sandboxes.get(name)
    if (!sandbox) {
      throw new Error(`沙箱 ${name} 不存在`)
    }

    try {
      this.emitEvent('destroy', name)
      sandbox.destroy()
      
      // 清理资源
      this.sandboxes.delete(name)
      this.metrics.delete(name)
    } catch (error) {
      this.emitEvent('error', name, undefined, error as Error)
      throw error
    }
  }

  /**
   * 执行代码
   */
  execute(name: string, code: string): any {
    const sandbox = this.sandboxes.get(name)
    if (!sandbox) {
      throw new Error(`沙箱 ${name} 不存在`)
    }

    const startTime = Date.now()
    
    try {
      this.emitEvent('execute', name, { code })
      const result = sandbox.execute(code)
      
      // 更新性能指标
      if (this.config.enablePerformanceMonitoring) {
        this.updateExecutionMetrics(name, Date.now() - startTime, true)
      }
      
      return result
    } catch (error) {
      // 更新错误指标
      if (this.config.enablePerformanceMonitoring) {
        this.updateExecutionMetrics(name, Date.now() - startTime, false)
      }
      
      this.emitEvent('error', name, { code }, error as Error)
      throw error
    }
  }

  /**
   * 批量操作
   */
  batch(operation: 'activate' | 'deactivate' | 'destroy', options: SandboxBatchOptions): void {
    const targetSandboxes = this.selectSandboxes(options)
    
    const concurrency = options.concurrency || 5
    const chunks = this.chunkArray(targetSandboxes, concurrency)
    
    for (const chunk of chunks) {
      const promises = chunk.map(name => {
        return new Promise<void>((resolve, reject) => {
          try {
            switch (operation) {
              case 'activate':
                this.activate(name)
                break
              case 'deactivate':
                this.deactivate(name)
                break
              case 'destroy':
                this.destroy(name)
                break
            }
            resolve()
          } catch (error) {
            reject(error)
          }
        })
      })
      
      // 等待当前批次完成
      Promise.allSettled(promises)
    }
  }

  /**
   * 查询沙箱
   */
  query(options: SandboxQueryOptions = {}): SandboxStrategy[] {
    let results = Array.from(this.sandboxes.values())
    
    // 按状态过滤
    if (options.state) {
      results = results.filter(sandbox => sandbox.getState() === options.state)
    }
    
    // 按名称模式过滤
    if (options.namePattern) {
      const pattern = new RegExp(options.namePattern)
      results = results.filter(sandbox => pattern.test(sandbox.getName()))
    }
    
    // 排序
    if (options.sortBy) {
      results.sort((a, b) => {
        let aValue: any, bValue: any
        
        switch (options.sortBy) {
          case 'name':
            aValue = a.getName()
            bValue = b.getName()
            break
          case 'createTime':
            aValue = this.metrics.get(a.getName())?.createTime || 0
            bValue = this.metrics.get(b.getName())?.createTime || 0
            break
          case 'executeCount':
            aValue = this.metrics.get(a.getName())?.executeCount || 0
            bValue = this.metrics.get(b.getName())?.executeCount || 0
            break
          default:
            return 0
        }
        
        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0
        return options.sortOrder === 'desc' ? -comparison : comparison
      })
    }
    
    // 限制数量
    if (options.limit) {
      results = results.slice(0, options.limit)
    }
    
    return results
  }

  /**
   * 获取统计信息
   */
  getStats(): SandboxStats {
    const sandboxes = Array.from(this.sandboxes.values())
    const byStrategy: Record<string, number> = {}
    
    let active = 0
    let inactive = 0
    let destroyed = 0
    let totalExecutions = 0
    let totalErrors = 0
    let totalExecutionTime = 0
    
    sandboxes.forEach(sandbox => {
      const state = sandbox.getState()
      const metrics = this.metrics.get(sandbox.getName())
      
      // 统计状态
      switch (state) {
        case SandboxState.ACTIVE:
          active++
          break
        case SandboxState.INACTIVE:
          inactive++
          break
        case SandboxState.DESTROYED:
          destroyed++
          break
      }
      
      // 统计指标
      if (metrics) {
        totalExecutions += metrics.executeCount
        totalErrors += metrics.errorCount
        totalExecutionTime += metrics.totalExecuteTime
      }
    })
    
    return {
      total: sandboxes.length,
      active,
      inactive,
      destroyed,
      byStrategy,
      totalExecutions,
      totalErrors,
      averageExecutionTime: totalExecutions > 0 ? totalExecutionTime / totalExecutions : 0
    }
  }

  /**
   * 获取沙箱指标
   */
  getMetrics(name: string): SandboxMetrics | undefined {
    return this.metrics.get(name)
  }

  /**
   * 获取所有指标
   */
  getAllMetrics(): Record<string, SandboxMetrics> {
    const result: Record<string, SandboxMetrics> = {}
    this.metrics.forEach((metrics, name) => {
      result[name] = { ...metrics }
    })
    return result
  }

  /**
   * 清理无用的沙箱
   */
  cleanup(): void {
    const now = Date.now()
    const timeout = this.config.sandboxTimeout!
    
    this.sandboxes.forEach((sandbox, name) => {
      const metrics = this.metrics.get(name)
      if (metrics && metrics.lastExecuteTime) {
        const idleTime = now - metrics.lastExecuteTime
        if (idleTime > timeout && sandbox.getState() === SandboxState.INACTIVE) {
          console.log(`清理闲置沙箱: ${name}`)
          this.destroy(name)
        }
      }
    })
  }

  /**
   * 事件监听
   */
  on(eventType: string, listener: SandboxEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, [])
    }
    this.eventListeners.get(eventType)!.push(listener)
  }

  /**
   * 移除事件监听
   */
  off(eventType: string, listener: SandboxEventListener): void {
    const listeners = this.eventListeners.get(eventType)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 销毁管理器
   */
  dispose(): void {
    // 停止自动清理
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    
    // 销毁所有沙箱
    this.sandboxes.forEach((_, name) => {
      this.destroy(name)
    })
    
    // 清理资源
    this.sandboxes.clear()
    this.metrics.clear()
    this.eventListeners.clear()
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(name: string): void {
    this.metrics.set(name, {
      name,
      createTime: Date.now(),
      executeCount: 0,
      totalExecuteTime: 0,
      averageExecuteTime: 0,
      memoryUsage: 0,
      errorCount: 0
    })
  }

  /**
   * 更新执行指标
   */
  private updateExecutionMetrics(name: string, executionTime: number, success: boolean): void {
    const metrics = this.metrics.get(name)
    if (!metrics) return
    
    metrics.executeCount++
    metrics.totalExecuteTime += executionTime
    metrics.averageExecuteTime = metrics.totalExecuteTime / metrics.executeCount
    metrics.lastExecuteTime = Date.now()
    
    if (!success) {
      metrics.errorCount++
    }
  }

  /**
   * 触发事件
   */
  private emitEvent(type: string, sandboxName: string, data?: any, error?: Error): void {
    const event: SandboxEvent = {
      type: type as any,
      sandboxName,
      timestamp: Date.now(),
      data,
      error
    }
    
    const listeners = this.eventListeners.get(type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event)
        } catch (error) {
          console.error('事件监听器执行失败:', error)
        }
      })
    }
  }

  /**
   * 启动自动清理
   */
  private startAutoCleanup(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanup()
    }, this.config.autoCleanupInterval!)
  }

  /**
   * 选择目标沙箱
   */
  private selectSandboxes(options: SandboxBatchOptions): string[] {
    let targets: string[] = []
    
    if (options.names) {
      targets = options.names.filter(name => this.sandboxes.has(name))
    } else {
      targets = Array.from(this.sandboxes.keys())
    }
    
    // 按状态过滤
    if (options.state) {
      targets = targets.filter(name => {
        const sandbox = this.sandboxes.get(name)
        return sandbox && sandbox.getState() === options.state
      })
    }
    
    // 排除指定沙箱
    if (options.exclude) {
      targets = targets.filter(name => !options.exclude!.includes(name))
    }
    
    return targets
  }

  /**
   * 数组分块
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }
}