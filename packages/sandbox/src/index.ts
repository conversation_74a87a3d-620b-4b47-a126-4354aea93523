/**
 * 沙箱包主入口
 * 提供多种沙箱策略实现
 */

// 导出沙箱管理器
export { SandboxManager } from './manager'

// 导出所有沙箱策略
export { ProxySandbox } from './strategies/proxy'
export { SnapshotSandbox } from './strategies/snapshot'
export { IframeSandbox } from './strategies/iframe'
export { WebWorkerSandbox } from './strategies/webworker'
export { ShadowDOMSandbox } from './strategies/shadow-dom'
export { VMSandbox } from './strategies/vm'

// 导出策略工厂
export { SandboxStrategyFactory } from './factory'

// 导出类型定义
export type {
  SandboxStrategy,
  SandboxContext,
  SandboxConfig,
  SandboxManagerConfig,
  SandboxInfo,
  SandboxMetrics
} from './types'

// 导出工具函数
export {
  createSandbox,
  getSandboxInfo,
  isSandboxSupported,
  getBestSandboxStrategy
} from './utils'

// 导出常量
export {
  SANDBOX_STRATEGIES,
  DEFAULT_SANDBOX_CONFIG,
  SANDBOX_EVENTS
} from './constants'