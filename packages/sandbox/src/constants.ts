/**
 * 沙箱常量定义
 */

import type { SandboxStrategyType } from './types'

/**
 * 沙箱策略列表
 */
export const SANDBOX_STRATEGIES: SandboxStrategyType[] = [
  'proxy',
  'snapshot',
  'iframe',
  'webworker',
  'shadow-dom',
  'vm'
]

/**
 * 默认沙箱配置
 */
export const DEFAULT_SANDBOX_CONFIG = {
  strict: false,
  timeout: 10000,
  memoryLimit: 128,
  enablePerformanceMonitoring: false,
  allowedApis: [],
  blockedApis: [],
  context: {}
}

/**
 * 沙箱事件类型
 */
export const SANDBOX_EVENTS = {
  CREATE: 'create',
  ACTIVATE: 'activate',
  DEACTIVATE: 'deactivate',
  DESTROY: 'destroy',
  EXECUTE: 'execute',
  ERROR: 'error'
} as const

/**
 * 沙箱状态常量
 */
export const SANDBOX_STATES = {
  INACTIVE: 'inactive',
  ACTIVE: 'active',
  DESTROYED: 'destroyed'
} as const

/**
 * 性能等级
 */
export const PERFORMANCE_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
} as const

/**
 * 兼容性等级
 */
export const COMPATIBILITY_LEVELS = {
  LEGACY: 'legacy',
  MODERN: 'modern',
  ALL: 'all'
} as const

/**
 * 隔离等级
 */
export const ISOLATION_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
} as const

/**
 * 默认策略优先级
 */
export const DEFAULT_STRATEGY_PRIORITY: SandboxStrategyType[] = [
  'proxy',
  'snapshot',
  'shadow-dom',
  'vm',
  'iframe',
  'webworker'
]

/**
 * 策略能力映射
 */
export const STRATEGY_CAPABILITIES = {
  proxy: {
    domIsolation: false,
    styleIsolation: false,
    jsIsolation: true,
    networkIsolation: false,
    storageIsolation: false,
    eventIsolation: false,
    performanceOverhead: 'low' as const,
    compatibility: 'modern' as const
  },
  snapshot: {
    domIsolation: false,
    styleIsolation: false,
    jsIsolation: true,
    networkIsolation: false,
    storageIsolation: false,
    eventIsolation: false,
    performanceOverhead: 'medium' as const,
    compatibility: 'all' as const
  },
  iframe: {
    domIsolation: true,
    styleIsolation: true,
    jsIsolation: true,
    networkIsolation: true,
    storageIsolation: true,
    eventIsolation: true,
    performanceOverhead: 'high' as const,
    compatibility: 'all' as const
  },
  webworker: {
    domIsolation: true,
    styleIsolation: true,
    jsIsolation: true,
    networkIsolation: true,
    storageIsolation: true,
    eventIsolation: true,
    performanceOverhead: 'medium' as const,
    compatibility: 'modern' as const
  },
  shadowdom: {
    domIsolation: true,
    styleIsolation: true,
    jsIsolation: false,
    networkIsolation: false,
    storageIsolation: false,
    eventIsolation: true,
    performanceOverhead: 'low' as const,
    compatibility: 'modern' as const
  },
  vm: {
    domIsolation: false,
    styleIsolation: false,
    jsIsolation: true,
    networkIsolation: false,
    storageIsolation: false,
    eventIsolation: false,
    performanceOverhead: 'medium' as const,
    compatibility: 'all' as const
  }
}

/**
 * 安全 API 白名单
 */
export const SAFE_APIS = [
  'console.log',
  'console.warn',
  'console.error',
  'console.info',
  'console.debug',
  'JSON.parse',
  'JSON.stringify',
  'Math',
  'Date',
  'Array',
  'Object',
  'String',
  'Number',
  'Boolean',
  'RegExp',
  'Promise',
  'setTimeout',
  'clearTimeout',
  'setInterval',
  'clearInterval'
]

/**
 * 危险 API 黑名单
 */
export const DANGEROUS_APIS = [
  'eval',
  'Function',
  'XMLHttpRequest',
  'fetch',
  'WebSocket',
  'localStorage',
  'sessionStorage',
  'indexedDB',
  'navigator.geolocation',
  'navigator.camera',
  'navigator.microphone',
  'document.write',
  'document.writeln',
  'document.open',
  'document.close',
  'window.open',
  'window.close',
  'location.href',
  'location.replace',
  'location.assign'
]

/**
 * 默认资源限制
 */
export const DEFAULT_RESOURCE_LIMITS = {
  maxMemory: 128, // MB
  maxExecutionTime: 10000, // ms
  maxCallStackSize: 1000,
  maxLoopIterations: 1000000
}

/**
 * 沙箱错误类型
 */
export const SANDBOX_ERROR_TYPES = {
  CREATION_FAILED: 'CREATION_FAILED',
  ACTIVATION_FAILED: 'ACTIVATION_FAILED',
  EXECUTION_FAILED: 'EXECUTION_FAILED',
  TIMEOUT: 'TIMEOUT',
  MEMORY_LIMIT_EXCEEDED: 'MEMORY_LIMIT_EXCEEDED',
  SECURITY_VIOLATION: 'SECURITY_VIOLATION',
  UNSUPPORTED_STRATEGY: 'UNSUPPORTED_STRATEGY',
  INVALID_CONFIG: 'INVALID_CONFIG'
} as const

/**
 * 沙箱监控指标
 */
export const MONITORING_METRICS = {
  EXECUTION_TIME: 'executionTime',
  MEMORY_USAGE: 'memoryUsage',
  ERROR_COUNT: 'errorCount',
  SUCCESS_RATE: 'successRate',
  THROUGHPUT: 'throughput'
} as const

/**
 * 沙箱生命周期钩子
 */
export const LIFECYCLE_HOOKS = {
  BEFORE_CREATE: 'beforeCreate',
  AFTER_CREATE: 'afterCreate',
  BEFORE_ACTIVATE: 'beforeActivate',
  AFTER_ACTIVATE: 'afterActivate',
  BEFORE_EXECUTE: 'beforeExecute',
  AFTER_EXECUTE: 'afterExecute',
  BEFORE_DEACTIVATE: 'beforeDeactivate',
  AFTER_DEACTIVATE: 'afterDeactivate',
  BEFORE_DESTROY: 'beforeDestroy',
  AFTER_DESTROY: 'afterDestroy',
  ON_ERROR: 'onError'
} as const