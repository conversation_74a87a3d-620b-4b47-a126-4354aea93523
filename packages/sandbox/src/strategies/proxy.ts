/**
 * Proxy 沙箱策略
 * 使用 ES6 Proxy 实现的沙箱隔离
 */

import type { SandboxStrategy, SandboxContext, SandboxConfig } from '../types'
import { SandboxState } from '@micro-core/shared'

export class ProxySandbox implements SandboxStrategy {
  private name: string
  private state: SandboxState = SandboxState.INACTIVE
  private context: SandboxContext
  private proxyWindow: WindowProxy
  private addedProperties: Set<string> = new Set()
  private modifiedProperties: Map<string, any> = new Map()
  private originalValues: Map<string, any> = new Map()

  constructor(name: string, config: SandboxConfig = {}) {
    this.name = name
    this.context = {
      window: {} as any,
      document: document,
      location: window.location,
      history: window.history,
      ...config.context
    }
    
    this.createProxyWindow()
  }

  /**
   * 创建代理窗口对象
   */
  private createProxyWindow(): void {
    const fakeWindow = Object.create(null)
    
    // 复制 window 的基本属性
    const windowProperties = [
      'console', 'setTimeout', 'clearTimeout', 'setInterval', 'clearInterval',
      'requestAnimationFrame', 'cancelAnimationFrame', 'fetch', 'XMLHttpRequest',
      'Promise', 'Array', 'Object', 'String', 'Number', 'Boolean', 'Date',
      'RegExp', 'Error', 'JSON', 'Math', 'parseInt', 'parseFloat', 'isNaN',
      'isFinite', 'encodeURIComponent', 'decodeURIComponent', 'btoa', 'atob'
    ]

    windowProperties.forEach(prop => {
      if (prop in window) {
        fakeWindow[prop] = (window as any)[prop]
      }
    })

    this.proxyWindow = new Proxy(fakeWindow, {
      get: (target, prop, receiver) => {
        // 优先从沙箱上下文获取
        if (prop in this.context) {
          return this.context[prop as keyof SandboxContext]
        }

        // 从代理目标获取
        if (prop in target) {
          return Reflect.get(target, prop, receiver)
        }

        // 从原始 window 获取
        if (typeof prop === 'string' && prop in window) {
          const value = (window as any)[prop]
          
          // 如果是函数，绑定到原始 window
          if (typeof value === 'function') {
            return value.bind(window)
          }
          
          return value
        }

        return undefined
      },

      set: (target, prop, value, receiver) => {
        // 记录属性变更
        if (typeof prop === 'string') {
          if (!(prop in target) && !(prop in window)) {
            this.addedProperties.add(prop)
          } else if (prop in window) {
            if (!this.originalValues.has(prop)) {
              this.originalValues.set(prop, (window as any)[prop])
            }
            this.modifiedProperties.set(prop, value)
          }
        }

        return Reflect.set(target, prop, value, receiver)
      },

      has: (target, prop) => {
        return prop in target || prop in window
      },

      ownKeys: (target) => {
        return [...Reflect.ownKeys(target), ...Reflect.ownKeys(window)]
      },

      getOwnPropertyDescriptor: (target, prop) => {
        const descriptor = Reflect.getOwnPropertyDescriptor(target, prop)
        if (descriptor) {
          return descriptor
        }
        
        if (typeof prop === 'string' && prop in window) {
          return Reflect.getOwnPropertyDescriptor(window, prop)
        }
        
        return undefined
      }
    })

    // 设置循环引用
    fakeWindow.window = this.proxyWindow
    fakeWindow.self = this.proxyWindow
    fakeWindow.globalThis = this.proxyWindow
  }

  /**
   * 激活沙箱
   */
  activate(): void {
    if (this.state === SandboxState.ACTIVE) {
      return
    }

    this.state = SandboxState.ACTIVE
    this.context.window = this.proxyWindow
  }

  /**
   * 停用沙箱
   */
  deactivate(): void {
    if (this.state !== SandboxState.ACTIVE) {
      return
    }

    this.state = SandboxState.INACTIVE
    
    // 恢复被修改的全局属性
    this.modifiedProperties.forEach((value, prop) => {
      if (this.originalValues.has(prop)) {
        ;(window as any)[prop] = this.originalValues.get(prop)
      }
    })

    // 删除新增的全局属性
    this.addedProperties.forEach(prop => {
      if (prop in window) {
        delete (window as any)[prop]
      }
    })
  }

  /**
   * 销毁沙箱
   */
  destroy(): void {
    this.deactivate()
    this.state = SandboxState.DESTROYED
    
    // 清理资源
    this.addedProperties.clear()
    this.modifiedProperties.clear()
    this.originalValues.clear()
    this.context = {} as any
  }

  /**
   * 执行代码
   */
  execute(code: string): any {
    if (this.state !== SandboxState.ACTIVE) {
      throw new Error(`沙箱 ${this.name} 未激活`)
    }

    try {
      // 创建函数并在沙箱上下文中执行
      const func = new Function('window', 'self', 'globalThis', code)
      return func.call(this.proxyWindow, this.proxyWindow, this.proxyWindow, this.proxyWindow)
    } catch (error) {
      console.error(`沙箱 ${this.name} 代码执行失败:`, error)
      throw error
    }
  }

  /**
   * 获取沙箱名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取沙箱状态
   */
  getState(): SandboxState {
    return this.state
  }

  /**
   * 获取沙箱上下文
   */
  getContext(): SandboxContext {
    return this.context
  }

  /**
   * 设置上下文属性
   */
  setContext(key: string, value: any): void {
    this.context[key as keyof SandboxContext] = value
  }

  /**
   * 获取上下文属性
   */
  getContextValue(key: string): any {
    return this.context[key as keyof SandboxContext]
  }

  /**
   * 检查是否支持该策略
   */
  static isSupported(): boolean {
    return typeof Proxy !== 'undefined'
  }

  /**
   * 获取策略信息
   */
  static getInfo() {
    return {
      name: 'proxy',
      description: 'Proxy 沙箱策略',
      supported: ProxySandbox.isSupported(),
      performance: 'high',
      isolation: 'medium',
      compatibility: 'modern'
    }
  }
}