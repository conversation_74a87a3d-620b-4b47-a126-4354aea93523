/**
 * VM 沙箱策略
 * 使用虚拟机实现代码隔离执行
 */

import type { SandboxStrategy, SandboxContext, SandboxConfig } from '../types'
import { SandboxState } from '@micro-core/shared'

export class VMSandbox implements SandboxStrategy {
  private name: string
  private state: SandboxState = SandboxState.INACTIVE
  private context: SandboxContext
  private globalContext: Record<string, any> = {}
  private moduleCache: Map<string, any> = new Map()

  constructor(name: string, config: SandboxConfig = {}) {
    this.name = name
    this.context = {
      window: {} as any,
      document: document,
      location: window.location,
      history: window.history,
      ...config.context
    }
    
    this.setupGlobalContext()
  }

  /**
   * 设置全局上下文
   */
  private setupGlobalContext(): void {
    // 创建安全的全局对象
    this.globalContext = {
      // 基础对象
      Object: Object,
      Array: Array,
      String: String,
      Number: Number,
      Boolean: <PERSON>olean,
      Date: Date,
      RegExp: RegExp,
      Error: Error,
      TypeError: TypeError,
      ReferenceError: ReferenceError,
      SyntaxError: SyntaxError,
      
      // 工具函数
      JSON: JSON,
      Math: Math,
      parseInt: parseInt,
      parseFloat: parseFloat,
      isNaN: isNaN,
      isFinite: isFinite,
      encodeURIComponent: encodeURIComponent,
      decodeURIComponent: decodeURIComponent,
      
      // 异步相关
      Promise: Promise,
      setTimeout: this.createSafeTimeout(),
      clearTimeout: clearTimeout,
      setInterval: this.createSafeInterval(),
      clearInterval: clearInterval,
      
      // 控制台
      console: this.createSafeConsole(),
      
      // 沙箱特定
      __SANDBOX_NAME__: this.name,
      __SANDBOX_TYPE__: 'vm',
      
      // 模块系统
      require: this.createRequireFunction(),
      module: { exports: {} },
      exports: {}
    }
  }

  /**
   * 创建安全的 setTimeout
   */
  private createSafeTimeout(): typeof setTimeout {
    return (callback: Function, delay?: number, ...args: any[]) => {
      // 限制最小延迟
      const safeDelay = Math.max(delay || 0, 4)
      
      return setTimeout(() => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`沙箱 ${this.name} setTimeout 回调执行失败:`, error)
        }
      }, safeDelay)
    }
  }

  /**
   * 创建安全的 setInterval
   */
  private createSafeInterval(): typeof setInterval {
    return (callback: Function, delay?: number, ...args: any[]) => {
      // 限制最小延迟
      const safeDelay = Math.max(delay || 0, 4)
      
      return setInterval(() => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`沙箱 ${this.name} setInterval 回调执行失败:`, error)
        }
      }, safeDelay)
    }
  }

  /**
   * 创建安全的控制台
   */
  private createSafeConsole(): Console {
    const prefix = `[${this.name}]`
    
    return {
      log: (...args: any[]) => console.log(prefix, ...args),
      warn: (...args: any[]) => console.warn(prefix, ...args),
      error: (...args: any[]) => console.error(prefix, ...args),
      info: (...args: any[]) => console.info(prefix, ...args),
      debug: (...args: any[]) => console.debug(prefix, ...args),
      trace: (...args: any[]) => console.trace(prefix, ...args),
      group: (...args: any[]) => console.group(prefix, ...args),
      groupCollapsed: (...args: any[]) => console.groupCollapsed(prefix, ...args),
      groupEnd: () => console.groupEnd(),
      time: (label?: string) => console.time(`${prefix} ${label || 'default'}`),
      timeEnd: (label?: string) => console.timeEnd(`${prefix} ${label || 'default'}`),
      count: (label?: string) => console.count(`${prefix} ${label || 'default'}`),
      countReset: (label?: string) => console.countReset(`${prefix} ${label || 'default'}`),
      clear: () => console.clear(),
      table: (data?: any) => console.table(data),
      dir: (obj?: any) => console.dir(obj),
      dirxml: (...args: any[]) => console.dirxml(...args),
      assert: (condition?: boolean, ...args: any[]) => console.assert(condition, prefix, ...args)
    } as Console
  }

  /**
   * 创建 require 函数
   */
  private createRequireFunction(): (id: string) => any {
    return (id: string) => {
      // 检查模块缓存
      if (this.moduleCache.has(id)) {
        return this.moduleCache.get(id)
      }

      // 内置模块
      const builtinModules: Record<string, any> = {
        'events': this.createEventsModule(),
        'util': this.createUtilModule(),
        'path': this.createPathModule(),
        'url': this.createUrlModule()
      }

      if (builtinModules[id]) {
        const module = builtinModules[id]
        this.moduleCache.set(id, module)
        return module
      }

      throw new Error(`模块 '${id}' 未找到`)
    }
  }

  /**
   * 创建 events 模块
   */
  private createEventsModule(): any {
    class EventEmitter {
      private events: Record<string, Function[]> = {}

      on(event: string, listener: Function): this {
        if (!this.events[event]) {
          this.events[event] = []
        }
        this.events[event].push(listener)
        return this
      }

      off(event: string, listener: Function): this {
        if (this.events[event]) {
          const index = this.events[event].indexOf(listener)
          if (index > -1) {
            this.events[event].splice(index, 1)
          }
        }
        return this
      }

      emit(event: string, ...args: any[]): boolean {
        if (this.events[event]) {
          this.events[event].forEach(listener => {
            try {
              listener(...args)
            } catch (error) {
              console.error(`事件监听器执行失败:`, error)
            }
          })
          return true
        }
        return false
      }

      once(event: string, listener: Function): this {
        const onceListener = (...args: any[]) => {
          listener(...args)
          this.off(event, onceListener)
        }
        return this.on(event, onceListener)
      }

      removeAllListeners(event?: string): this {
        if (event) {
          delete this.events[event]
        } else {
          this.events = {}
        }
        return this
      }

      listenerCount(event: string): number {
        return this.events[event]?.length || 0
      }
    }

    return { EventEmitter }
  }

  /**
   * 创建 util 模块
   */
  private createUtilModule(): any {
    return {
      isArray: Array.isArray,
      isDate: (obj: any) => obj instanceof Date,
      isError: (obj: any) => obj instanceof Error,
      isFunction: (obj: any) => typeof obj === 'function',
      isNull: (obj: any) => obj === null,
      isNumber: (obj: any) => typeof obj === 'number',
      isObject: (obj: any) => typeof obj === 'object' && obj !== null,
      isString: (obj: any) => typeof obj === 'string',
      isUndefined: (obj: any) => obj === undefined,
      format: (f: string, ...args: any[]) => {
        let i = 0
        return f.replace(/%[sdj%]/g, (x) => {
          if (x === '%%') return x
          if (i >= args.length) return x
          switch (x) {
            case '%s': return String(args[i++])
            case '%d': return Number(args[i++])
            case '%j':
              try {
                return JSON.stringify(args[i++])
              } catch (_) {
                return '[Circular]'
              }
            default:
              return x
          }
        })
      }
    }
  }

  /**
   * 创建 path 模块
   */
  private createPathModule(): any {
    return {
      join: (...paths: string[]) => {
        return paths.filter(Boolean).join('/').replace(/\/+/g, '/')
      },
      resolve: (...paths: string[]) => {
        return '/' + paths.filter(Boolean).join('/').replace(/\/+/g, '/')
      },
      dirname: (path: string) => {
        const parts = path.split('/')
        return parts.slice(0, -1).join('/') || '/'
      },
      basename: (path: string, ext?: string) => {
        const name = path.split('/').pop() || ''
        if (ext && name.endsWith(ext)) {
          return name.slice(0, -ext.length)
        }
        return name
      },
      extname: (path: string) => {
        const name = path.split('/').pop() || ''
        const dotIndex = name.lastIndexOf('.')
        return dotIndex > 0 ? name.slice(dotIndex) : ''
      }
    }
  }

  /**
   * 创建 url 模块
   */
  private createUrlModule(): any {
    return {
      parse: (urlString: string) => {
        try {
          const url = new URL(urlString)
          return {
            protocol: url.protocol,
            hostname: url.hostname,
            port: url.port,
            pathname: url.pathname,
            search: url.search,
            hash: url.hash,
            host: url.host,
            origin: url.origin
          }
        } catch (error) {
          throw new Error(`无效的 URL: ${urlString}`)
        }
      },
      format: (urlObject: any) => {
        const { protocol = '', hostname = '', port = '', pathname = '', search = '', hash = '' } = urlObject
        let url = protocol + '//' + hostname
        if (port) url += ':' + port
        url += pathname + search + hash
        return url
      }
    }
  }

  /**
   * 执行代码
   */
  private executeInContext(code: string, context: Record<string, any>): any {
    // 创建参数列表
    const contextKeys = Object.keys(context)
    const contextValues = Object.values(context)

    try {
      // 使用 Function 构造器创建函数
      const func = new Function(...contextKeys, `
        "use strict";
        ${code}
      `)

      // 执行函数
      return func.apply(null, contextValues)
    } catch (error) {
      // 包装错误信息
      if (error instanceof Error) {
        error.message = `沙箱执行错误: ${error.message}`
      }
      throw error
    }
  }

  /**
   * 激活沙箱
   */
  activate(): void {
    if (this.state === SandboxState.ACTIVE) {
      return
    }

    // 重置模块系统
    this.globalContext.module = { exports: {} }
    this.globalContext.exports = this.globalContext.module.exports

    this.state = SandboxState.ACTIVE
  }

  /**
   * 停用沙箱
   */
  deactivate(): void {
    if (this.state !== SandboxState.ACTIVE) {
      return
    }

    this.state = SandboxState.INACTIVE
  }

  /**
   * 销毁沙箱
   */
  destroy(): void {
    this.deactivate()
    this.state = SandboxState.DESTROYED
    
    // 清理资源
    this.globalContext = {}
    this.moduleCache.clear()
    this.context = {} as any
  }

  /**
   * 执行代码
   */
  execute(code: string): any {
    if (this.state !== SandboxState.ACTIVE) {
      throw new Error(`沙箱 ${this.name} 未激活`)
    }

    try {
      // 合并上下文
      const executionContext = {
        ...this.globalContext,
        ...this.context
      }

      return this.executeInContext(code, executionContext)
    } catch (error) {
      console.error(`沙箱 ${this.name} 代码执行失败:`, error)
      throw error
    }
  }

  /**
   * 执行模块代码
   */
  executeModule(code: string, filename?: string): any {
    if (this.state !== SandboxState.ACTIVE) {
      throw new Error(`沙箱 ${this.name} 未激活`)
    }

    try {
      // 创建模块上下文
      const moduleContext = {
        ...this.globalContext,
        ...this.context,
        __filename: filename || 'unknown',
        __dirname: filename ? filename.split('/').slice(0, -1).join('/') : '/',
        module: { exports: {} },
        exports: {}
      }

      // 确保 exports 指向 module.exports
      moduleContext.exports = moduleContext.module.exports

      // 执行模块代码
      this.executeInContext(code, moduleContext)

      // 返回模块导出
      return moduleContext.module.exports
    } catch (error) {
      console.error(`沙箱 ${this.name} 模块执行失败:`, error)
      throw error
    }
  }

  /**
   * 获取沙箱名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取沙箱状态
   */
  getState(): SandboxState {
    return this.state
  }

  /**
   * 获取沙箱上下文
   */
  getContext(): SandboxContext {
    return this.context
  }

  /**
   * 设置上下文属性
   */
  setContext(key: string, value: any): void {
    this.context[key as keyof SandboxContext] = value
    this.globalContext[key] = value
  }

  /**
   * 获取上下文属性
   */
  getContextValue(key: string): any {
    return this.context[key as keyof SandboxContext]
  }

  /**
   * 添加模块到缓存
   */
  addModule(id: string, module: any): void {
    this.moduleCache.set(id, module)
  }

  /**
   * 移除模块缓存
   */
  removeModule(id: string): void {
    this.moduleCache.delete(id)
  }

  /**
   * 清空模块缓存
   */
  clearModuleCache(): void {
    this.moduleCache.clear()
  }

  /**
   * 获取模块列表
   */
  getModules(): string[] {
    return Array.from(this.moduleCache.keys())
  }

  /**
   * 检查是否支持该策略
   */
  static isSupported(): boolean {
    return typeof Function !== 'undefined'
  }

  /**
   * 获取策略信息
   */
  static getInfo() {
    return {
      name: 'vm',
      description: 'VM 沙箱策略',
      supported: VMSandbox.isSupported(),
      performance: 'medium',
      isolation: 'high',
      compatibility: 'all'
    }
  }
}