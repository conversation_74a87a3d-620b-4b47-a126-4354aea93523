/**
 * 快照沙箱策略
 * 通过快照机制实现沙箱隔离
 */

import type { SandboxStrategy, SandboxContext, SandboxConfig } from '../types'
import { SandboxState } from '@micro-core/shared'

export class SnapshotSandbox implements SandboxStrategy {
  private name: string
  private state: SandboxState = SandboxState.INACTIVE
  private context: SandboxContext
  private windowSnapshot: Record<string, any> = {}
  private modifiedProperties: Record<string, any> = {}

  constructor(name: string, config: SandboxConfig = {}) {
    this.name = name
    this.context = {
      window: window as any,
      document: document,
      location: window.location,
      history: window.history,
      ...config.context
    }
  }

  /**
   * 创建 window 快照
   */
  private createSnapshot(): void {
    this.windowSnapshot = {}
    
    // 遍历 window 对象的所有属性
    for (const prop in window) {
      try {
        this.windowSnapshot[prop] = (window as any)[prop]
      } catch (error) {
        // 忽略无法访问的属性
        console.warn(`无法访问属性 ${prop}:`, error)
      }
    }
  }

  /**
   * 恢复 window 快照
   */
  private restoreSnapshot(): void {
    // 恢复被修改的属性
    Object.keys(this.modifiedProperties).forEach(prop => {
      if (this.windowSnapshot.hasOwnProperty(prop)) {
        ;(window as any)[prop] = this.windowSnapshot[prop]
      } else {
        // 删除新增的属性
        delete (window as any)[prop]
      }
    })

    // 清空修改记录
    this.modifiedProperties = {}
  }

  /**
   * 记录属性变更
   */
  private recordChanges(): void {
    this.modifiedProperties = {}
    
    for (const prop in window) {
      try {
        const currentValue = (window as any)[prop]
        const snapshotValue = this.windowSnapshot[prop]
        
        // 检查属性是否被修改
        if (currentValue !== snapshotValue) {
          this.modifiedProperties[prop] = currentValue
        }
      } catch (error) {
        // 忽略无法访问的属性
        console.warn(`无法检查属性 ${prop}:`, error)
      }
    }

    // 检查新增的属性
    for (const prop in window) {
      if (!this.windowSnapshot.hasOwnProperty(prop)) {
        try {
          this.modifiedProperties[prop] = (window as any)[prop]
        } catch (error) {
          console.warn(`无法记录新属性 ${prop}:`, error)
        }
      }
    }
  }

  /**
   * 激活沙箱
   */
  activate(): void {
    if (this.state === SandboxState.ACTIVE) {
      return
    }

    // 创建快照
    this.createSnapshot()
    
    // 恢复之前的修改
    Object.keys(this.modifiedProperties).forEach(prop => {
      try {
        ;(window as any)[prop] = this.modifiedProperties[prop]
      } catch (error) {
        console.warn(`无法恢复属性 ${prop}:`, error)
      }
    })

    this.state = SandboxState.ACTIVE
  }

  /**
   * 停用沙箱
   */
  deactivate(): void {
    if (this.state !== SandboxState.ACTIVE) {
      return
    }

    // 记录变更
    this.recordChanges()
    
    // 恢复快照
    this.restoreSnapshot()

    this.state = SandboxState.INACTIVE
  }

  /**
   * 销毁沙箱
   */
  destroy(): void {
    this.deactivate()
    this.state = SandboxState.DESTROYED
    
    // 清理资源
    this.windowSnapshot = {}
    this.modifiedProperties = {}
    this.context = {} as any
  }

  /**
   * 执行代码
   */
  execute(code: string): any {
    if (this.state !== SandboxState.ACTIVE) {
      throw new Error(`沙箱 ${this.name} 未激活`)
    }

    try {
      // 直接在全局作用域执行代码
      return (0, eval)(code)
    } catch (error) {
      console.error(`沙箱 ${this.name} 代码执行失败:`, error)
      throw error
    }
  }

  /**
   * 获取沙箱名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取沙箱状态
   */
  getState(): SandboxState {
    return this.state
  }

  /**
   * 获取沙箱上下文
   */
  getContext(): SandboxContext {
    return this.context
  }

  /**
   * 设置上下文属性
   */
  setContext(key: string, value: any): void {
    this.context[key as keyof SandboxContext] = value
  }

  /**
   * 获取上下文属性
   */
  getContextValue(key: string): any {
    return this.context[key as keyof SandboxContext]
  }

  /**
   * 获取快照信息
   */
  getSnapshotInfo() {
    return {
      snapshotSize: Object.keys(this.windowSnapshot).length,
      modifiedSize: Object.keys(this.modifiedProperties).length,
      modifiedProperties: Object.keys(this.modifiedProperties)
    }
  }

  /**
   * 检查是否支持该策略
   */
  static isSupported(): boolean {
    return true // 快照策略总是支持的
  }

  /**
   * 获取策略信息
   */
  static getInfo() {
    return {
      name: 'snapshot',
      description: '快照沙箱策略',
      supported: SnapshotSandbox.isSupported(),
      performance: 'low',
      isolation: 'low',
      compatibility: 'all'
    }
  }
}