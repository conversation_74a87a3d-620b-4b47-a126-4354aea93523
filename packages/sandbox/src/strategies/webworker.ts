/**
 * WebWorker 沙箱策略
 * 使用 Web Worker 实现隔离的沙箱环境
 */

import type { SandboxStrategy, SandboxContext, SandboxConfig } from '../types'
import { SandboxState } from '@micro-core/shared'

export class WebWorkerSandbox implements SandboxStrategy {
  private name: string
  private state: SandboxState = SandboxState.INACTIVE
  private context: SandboxContext
  private worker: Worker | null = null
  private messageHandlers: Map<string, Function> = new Map()
  private pendingTasks: Map<string, { resolve: Function; reject: Function }> = new Map()
  private taskId = 0

  constructor(name: string, config: SandboxConfig = {}) {
    this.name = name
    this.context = {
      window: {} as any,
      document: {} as any,
      location: {} as any,
      history: {} as any,
      ...config.context
    }
  }

  /**
   * 创建 Worker
   */
  private createWorker(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 创建 Worker 脚本
        const workerScript = this.generateWorkerScript()
        const blob = new Blob([workerScript], { type: 'application/javascript' })
        const workerUrl = URL.createObjectURL(blob)

        this.worker = new Worker(workerUrl)

        // 设置消息处理
        this.worker.onmessage = (event) => {
          this.handleWorkerMessage(event.data)
        }

        this.worker.onerror = (error) => {
          console.error(`Worker 错误:`, error)
          reject(error)
        }

        // 清理 URL
        URL.revokeObjectURL(workerUrl)
        
        resolve()
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 生成 Worker 脚本
   */
  private generateWorkerScript(): string {
    return `
      // Worker 沙箱环境
      const sandbox = {
        name: '${this.name}',
        type: 'webworker',
        context: {},
        modules: new Map()
      };

      // 消息处理
      self.onmessage = function(event) {
        const { type, id, data } = event.data;
        
        try {
          switch (type) {
            case 'execute':
              const result = executeCode(data.code);
              postMessage({ type: 'result', id, data: result });
              break;
              
            case 'setContext':
              sandbox.context[data.key] = data.value;
              postMessage({ type: 'success', id });
              break;
              
            case 'getContext':
              const value = sandbox.context[data.key];
              postMessage({ type: 'result', id, data: value });
              break;
              
            default:
              postMessage({ type: 'error', id, error: 'Unknown message type' });
          }
        } catch (error) {
          postMessage({ 
            type: 'error', 
            id, 
            error: error.message || 'Unknown error' 
          });
        }
      };

      // 代码执行函数
      function executeCode(code) {
        // 创建受限的全局环境
        const globalContext = {
          console: {
            log: (...args) => postMessage({ type: 'console', level: 'log', args }),
            warn: (...args) => postMessage({ type: 'console', level: 'warn', args }),
            error: (...args) => postMessage({ type: 'console', level: 'error', args })
          },
          setTimeout: self.setTimeout.bind(self),
          clearTimeout: self.clearTimeout.bind(self),
          setInterval: self.setInterval.bind(self),
          clearInterval: self.clearInterval.bind(self),
          Promise: Promise,
          Array: Array,
          Object: Object,
          String: String,
          Number: Number,
          Boolean: Boolean,
          Date: Date,
          RegExp: RegExp,
          Error: Error,
          JSON: JSON,
          Math: Math,
          parseInt: parseInt,
          parseFloat: parseFloat,
          isNaN: isNaN,
          isFinite: isFinite,
          encodeURIComponent: encodeURIComponent,
          decodeURIComponent: decodeURIComponent,
          ...sandbox.context
        };

        // 使用 Function 构造器执行代码
        const func = new Function(...Object.keys(globalContext), code);
        return func(...Object.values(globalContext));
      }
    `
  }

  /**
   * 处理 Worker 消息
   */
  private handleWorkerMessage(message: any): void {
    const { type, id, data, error } = message

    switch (type) {
      case 'result':
        const resultTask = this.pendingTasks.get(id)
        if (resultTask) {
          resultTask.resolve(data)
          this.pendingTasks.delete(id)
        }
        break

      case 'error':
        const errorTask = this.pendingTasks.get(id)
        if (errorTask) {
          errorTask.reject(new Error(error))
          this.pendingTasks.delete(id)
        }
        break

      case 'success':
        const successTask = this.pendingTasks.get(id)
        if (successTask) {
          successTask.resolve(true)
          this.pendingTasks.delete(id)
        }
        break

      case 'console':
        // 转发控制台输出
        const { level, args } = message
        ;(console as any)[level](`[${this.name}]`, ...args)
        break

      default:
        const handler = this.messageHandlers.get(type)
        if (handler) {
          handler(data)
        }
    }
  }

  /**
   * 发送消息到 Worker
   */
  private postMessage(type: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.worker) {
        reject(new Error('Worker 未初始化'))
        return
      }

      const id = (++this.taskId).toString()
      this.pendingTasks.set(id, { resolve, reject })

      this.worker.postMessage({ type, id, data })

      // 设置超时
      setTimeout(() => {
        if (this.pendingTasks.has(id)) {
          this.pendingTasks.delete(id)
          reject(new Error('Worker 操作超时'))
        }
      }, 10000) // 10秒超时
    })
  }

  /**
   * 激活沙箱
   */
  async activate(): Promise<void> {
    if (this.state === SandboxState.ACTIVE) {
      return
    }

    if (!this.worker) {
      await this.createWorker()
    }

    this.state = SandboxState.ACTIVE
  }

  /**
   * 停用沙箱
   */
  deactivate(): void {
    if (this.state !== SandboxState.ACTIVE) {
      return
    }

    this.state = SandboxState.INACTIVE
  }

  /**
   * 销毁沙箱
   */
  destroy(): void {
    this.deactivate()
    this.state = SandboxState.DESTROYED
    
    // 终止 Worker
    if (this.worker) {
      this.worker.terminate()
      this.worker = null
    }
    
    // 清理资源
    this.pendingTasks.clear()
    this.messageHandlers.clear()
    this.context = {} as any
  }

  /**
   * 执行代码
   */
  async execute(code: string): Promise<any> {
    if (this.state !== SandboxState.ACTIVE) {
      throw new Error(`沙箱 ${this.name} 未激活`)
    }

    try {
      return await this.postMessage('execute', { code })
    } catch (error) {
      console.error(`沙箱 ${this.name} 代码执行失败:`, error)
      throw error
    }
  }

  /**
   * 获取沙箱名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取沙箱状态
   */
  getState(): SandboxState {
    return this.state
  }

  /**
   * 获取沙箱上下文
   */
  getContext(): SandboxContext {
    return this.context
  }

  /**
   * 设置上下文属性
   */
  async setContext(key: string, value: any): Promise<void> {
    this.context[key as keyof SandboxContext] = value
    
    if (this.state === SandboxState.ACTIVE) {
      await this.postMessage('setContext', { key, value })
    }
  }

  /**
   * 获取上下文属性
   */
  async getContextValue(key: string): Promise<any> {
    if (this.state === SandboxState.ACTIVE) {
      return await this.postMessage('getContext', { key })
    }
    
    return this.context[key as keyof SandboxContext]
  }

  /**
   * 添加消息处理器
   */
  onMessage(type: string, handler: Function): void {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 移除消息处理器
   */
  offMessage(type: string): void {
    this.messageHandlers.delete(type)
  }

  /**
   * 检查是否支持该策略
   */
  static isSupported(): boolean {
    return typeof Worker !== 'undefined'
  }

  /**
   * 获取策略信息
   */
  static getInfo() {
    return {
      name: 'webworker',
      description: 'WebWorker 沙箱策略',
      supported: WebWorkerSandbox.isSupported(),
      performance: 'high',
      isolation: 'high',
      compatibility: 'modern'
    }
  }
}