/**
 * ShadowDOM 沙箱策略
 * 使用 Shadow DOM 实现样式和 DOM 隔离
 */

import type { SandboxStrategy, SandboxContext, SandboxConfig } from '../types'
import { SandboxState } from '@micro-core/shared'

export class ShadowDOMSandbox implements SandboxStrategy {
  private name: string
  private state: SandboxState = SandboxState.INACTIVE
  private context: SandboxContext
  private hostElement: HTMLElement | null = null
  private shadowRoot: ShadowRoot | null = null
  private styleSheets: CSSStyleSheet[] = []

  constructor(name: string, config: SandboxConfig = {}) {
    this.name = name
    this.context = {
      window: window,
      document: {} as any,
      location: window.location,
      history: window.history,
      ...config.context
    }
  }

  /**
   * 创建 Shadow DOM
   */
  private createShadowDOM(container?: HTMLElement): void {
    // 创建宿主元素
    this.hostElement = container || document.createElement('div')
    this.hostElement.id = `micro-sandbox-${this.name}`
    
    if (!container) {
      this.hostElement.style.display = 'none'
      document.body.appendChild(this.hostElement)
    }

    // 创建 Shadow Root
    this.shadowRoot = this.hostElement.attachShadow({ 
      mode: 'closed',
      delegatesFocus: true
    })

    // 设置基础样式
    this.setupBaseStyles()
    
    // 更新上下文
    this.updateContext()
  }

  /**
   * 设置基础样式
   */
  private setupBaseStyles(): void {
    if (!this.shadowRoot) {
      return
    }

    const style = document.createElement('style')
    style.textContent = `
      :host {
        display: block;
        position: relative;
        width: 100%;
        height: 100%;
      }
      
      * {
        box-sizing: border-box;
      }
      
      .sandbox-container {
        width: 100%;
        height: 100%;
        overflow: auto;
      }
    `
    
    this.shadowRoot.appendChild(style)

    // 创建容器
    const container = document.createElement('div')
    container.className = 'sandbox-container'
    this.shadowRoot.appendChild(container)
  }

  /**
   * 更新沙箱上下文
   */
  private updateContext(): void {
    if (!this.shadowRoot) {
      return
    }

    // 创建受限的 document 对象
    const sandboxDocument = {
      // 基本属性
      nodeType: 9,
      nodeName: '#document',
      
      // 查询方法 - 限制在 Shadow DOM 内
      querySelector: (selector: string) => this.shadowRoot!.querySelector(selector),
      querySelectorAll: (selector: string) => this.shadowRoot!.querySelectorAll(selector),
      getElementById: (id: string) => this.shadowRoot!.querySelector(`#${id}`),
      getElementsByClassName: (className: string) => this.shadowRoot!.querySelectorAll(`.${className}`),
      getElementsByTagName: (tagName: string) => this.shadowRoot!.querySelectorAll(tagName),
      
      // 创建方法
      createElement: (tagName: string) => document.createElement(tagName),
      createTextNode: (text: string) => document.createTextNode(text),
      createDocumentFragment: () => document.createDocumentFragment(),
      
      // 事件方法
      addEventListener: (type: string, listener: EventListener, options?: boolean | AddEventListenerOptions) => {
        this.shadowRoot!.addEventListener(type, listener, options)
      },
      removeEventListener: (type: string, listener: EventListener, options?: boolean | EventListenerOptions) => {
        this.shadowRoot!.removeEventListener(type, listener, options)
      },
      
      // 样式相关
      adoptedStyleSheets: this.shadowRoot.adoptedStyleSheets,
      
      // 其他属性
      head: null,
      body: this.shadowRoot.querySelector('.sandbox-container'),
      documentElement: this.shadowRoot.host,
      
      // 兼容性方法
      write: () => {
        console.warn('document.write 在 Shadow DOM 沙箱中不可用')
      },
      writeln: () => {
        console.warn('document.writeln 在 Shadow DOM 沙箱中不可用')
      }
    }

    this.context.document = sandboxDocument as any
  }

  /**
   * 添加样式表
   */
  addStyleSheet(css: string): void {
    if (!this.shadowRoot) {
      return
    }

    const style = document.createElement('style')
    style.textContent = css
    this.shadowRoot.appendChild(style)
  }

  /**
   * 添加可构造样式表
   */
  addConstructableStyleSheet(css: string): void {
    if (!this.shadowRoot || !('adoptedStyleSheets' in this.shadowRoot)) {
      // 降级到普通样式表
      this.addStyleSheet(css)
      return
    }

    try {
      const sheet = new CSSStyleSheet()
      sheet.replaceSync(css)
      this.styleSheets.push(sheet)
      this.shadowRoot.adoptedStyleSheets = [...this.shadowRoot.adoptedStyleSheets, sheet]
    } catch (error) {
      console.warn('无法创建可构造样式表，降级到普通样式表:', error)
      this.addStyleSheet(css)
    }
  }

  /**
   * 激活沙箱
   */
  activate(): void {
    if (this.state === SandboxState.ACTIVE) {
      return
    }

    if (!this.shadowRoot) {
      this.createShadowDOM()
    }

    this.state = SandboxState.ACTIVE
  }

  /**
   * 停用沙箱
   */
  deactivate(): void {
    if (this.state !== SandboxState.ACTIVE) {
      return
    }

    this.state = SandboxState.INACTIVE
  }

  /**
   * 销毁沙箱
   */
  destroy(): void {
    this.deactivate()
    this.state = SandboxState.DESTROYED
    
    // 清理 Shadow DOM
    if (this.hostElement && this.hostElement.parentNode) {
      this.hostElement.parentNode.removeChild(this.hostElement)
    }
    
    // 清理资源
    this.hostElement = null
    this.shadowRoot = null
    this.styleSheets = []
    this.context = {} as any
  }

  /**
   * 执行代码
   */
  execute(code: string): any {
    if (this.state !== SandboxState.ACTIVE) {
      throw new Error(`沙箱 ${this.name} 未激活`)
    }

    try {
      // 创建函数并在沙箱上下文中执行
      const func = new Function('document', 'window', code)
      return func.call(this.context.window, this.context.document, this.context.window)
    } catch (error) {
      console.error(`沙箱 ${this.name} 代码执行失败:`, error)
      throw error
    }
  }

  /**
   * 渲染内容到 Shadow DOM
   */
  render(html: string, css?: string): void {
    if (!this.shadowRoot) {
      throw new Error('Shadow DOM 未初始化')
    }

    const container = this.shadowRoot.querySelector('.sandbox-container')
    if (container) {
      container.innerHTML = html
    }

    if (css) {
      this.addStyleSheet(css)
    }
  }

  /**
   * 获取沙箱名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取沙箱状态
   */
  getState(): SandboxState {
    return this.state
  }

  /**
   * 获取沙箱上下文
   */
  getContext(): SandboxContext {
    return this.context
  }

  /**
   * 设置上下文属性
   */
  setContext(key: string, value: any): void {
    this.context[key as keyof SandboxContext] = value
  }

  /**
   * 获取上下文属性
   */
  getContextValue(key: string): any {
    return this.context[key as keyof SandboxContext]
  }

  /**
   * 获取 Shadow Root
   */
  getShadowRoot(): ShadowRoot | null {
    return this.shadowRoot
  }

  /**
   * 获取宿主元素
   */
  getHostElement(): HTMLElement | null {
    return this.hostElement
  }

  /**
   * 设置宿主元素
   */
  setHostElement(element: HTMLElement): void {
    if (this.state === SandboxState.ACTIVE) {
      throw new Error('无法在激活状态下更改宿主元素')
    }

    this.hostElement = element
  }

  /**
   * 检查是否支持该策略
   */
  static isSupported(): boolean {
    return 'attachShadow' in Element.prototype
  }

  /**
   * 获取策略信息
   */
  static getInfo() {
    return {
      name: 'shadow-dom',
      description: 'ShadowDOM 沙箱策略',
      supported: ShadowDOMSandbox.isSupported(),
      performance: 'high',
      isolation: 'medium',
      compatibility: 'modern'
    }
  }
}