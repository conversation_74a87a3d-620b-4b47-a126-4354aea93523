/**
 * Iframe 沙箱策略
 * 使用 iframe 实现完全隔离的沙箱环境
 */

import type { SandboxStrategy, SandboxContext, SandboxConfig } from '../types'
import { SandboxState } from '@micro-core/shared'

export class IframeSandbox implements SandboxStrategy {
  private name: string
  private state: SandboxState = SandboxState.INACTIVE
  private context: SandboxContext
  private iframe: HTMLIFrameElement | null = null
  private iframeWindow: Window | null = null
  private messageHandlers: Map<string, Function> = new Map()

  constructor(name: string, config: SandboxConfig = {}) {
    this.name = name
    this.context = {
      window: {} as any,
      document: {} as any,
      location: {} as any,
      history: {} as any,
      ...config.context
    }
  }

  /**
   * 创建 iframe
   */
  private createIframe(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.iframe = document.createElement('iframe')
      
      // 设置 iframe 属性
      this.iframe.style.display = 'none'
      this.iframe.src = 'about:blank'
      this.iframe.sandbox.add(
        'allow-scripts',
        'allow-same-origin',
        'allow-forms',
        'allow-modals'
      )

      // 监听加载完成
      this.iframe.onload = () => {
        try {
          this.iframeWindow = this.iframe!.contentWindow
          if (!this.iframeWindow) {
            reject(new Error('无法获取 iframe window'))
            return
          }

          // 设置上下文
          this.setupContext()
          
          // 设置消息通信
          this.setupMessageCommunication()
          
          resolve()
        } catch (error) {
          reject(error)
        }
      }

      this.iframe.onerror = () => {
        reject(new Error('iframe 加载失败'))
      }

      // 添加到文档
      document.body.appendChild(this.iframe)
    })
  }

  /**
   * 设置沙箱上下文
   */
  private setupContext(): void {
    if (!this.iframeWindow) {
      return
    }

    // 设置上下文对象
    this.context.window = this.iframeWindow
    this.context.document = this.iframeWindow.document
    this.context.location = this.iframeWindow.location
    this.context.history = this.iframeWindow.history

    // 注入全局对象
    const iframeGlobal = this.iframeWindow as any
    iframeGlobal.__MICRO_SANDBOX__ = {
      name: this.name,
      type: 'iframe',
      postMessage: (data: any) => {
        window.postMessage({
          type: 'sandbox-message',
          sandbox: this.name,
          data
        }, '*')
      }
    }
  }

  /**
   * 设置消息通信
   */
  private setupMessageCommunication(): void {
    const messageHandler = (event: MessageEvent) => {
      if (event.source === this.iframeWindow) {
        const { type, data } = event.data
        const handler = this.messageHandlers.get(type)
        if (handler) {
          handler(data)
        }
      }
    }

    window.addEventListener('message', messageHandler)
  }

  /**
   * 激活沙箱
   */
  async activate(): Promise<void> {
    if (this.state === SandboxState.ACTIVE) {
      return
    }

    if (!this.iframe) {
      await this.createIframe()
    }

    this.state = SandboxState.ACTIVE
  }

  /**
   * 停用沙箱
   */
  deactivate(): void {
    if (this.state !== SandboxState.ACTIVE) {
      return
    }

    this.state = SandboxState.INACTIVE
  }

  /**
   * 销毁沙箱
   */
  destroy(): void {
    this.deactivate()
    this.state = SandboxState.DESTROYED
    
    // 清理 iframe
    if (this.iframe && this.iframe.parentNode) {
      this.iframe.parentNode.removeChild(this.iframe)
    }
    
    // 清理资源
    this.iframe = null
    this.iframeWindow = null
    this.messageHandlers.clear()
    this.context = {} as any
  }

  /**
   * 执行代码
   */
  execute(code: string): any {
    if (this.state !== SandboxState.ACTIVE || !this.iframeWindow) {
      throw new Error(`沙箱 ${this.name} 未激活或 iframe 不可用`)
    }

    try {
      // 在 iframe 中执行代码
      return this.iframeWindow.eval(code)
    } catch (error) {
      console.error(`沙箱 ${this.name} 代码执行失败:`, error)
      throw error
    }
  }

  /**
   * 获取沙箱名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取沙箱状态
   */
  getState(): SandboxState {
    return this.state
  }

  /**
   * 获取沙箱上下文
   */
  getContext(): SandboxContext {
    return this.context
  }

  /**
   * 设置上下文属性
   */
  setContext(key: string, value: any): void {
    this.context[key as keyof SandboxContext] = value
  }

  /**
   * 获取上下文属性
   */
  getContextValue(key: string): any {
    return this.context[key as keyof SandboxContext]
  }

  /**
   * 添加消息处理器
   */
  onMessage(type: string, handler: Function): void {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 移除消息处理器
   */
  offMessage(type: string): void {
    this.messageHandlers.delete(type)
  }

  /**
   * 发送消息到沙箱
   */
  postMessage(type: string, data: any): void {
    if (this.iframeWindow) {
      this.iframeWindow.postMessage({ type, data }, '*')
    }
  }

  /**
   * 检查是否支持该策略
   */
  static isSupported(): boolean {
    return typeof HTMLIFrameElement !== 'undefined'
  }

  /**
   * 获取策略信息
   */
  static getInfo() {
    return {
      name: 'iframe',
      description: 'Iframe 沙箱策略',
      supported: IframeSandbox.isSupported(),
      performance: 'medium',
      isolation: 'high',
      compatibility: 'all'
    }
  }
}