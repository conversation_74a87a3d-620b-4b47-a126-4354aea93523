/**
 * 沙箱工具函数
 */

import type { 
  SandboxStrategy, 
  SandboxConfig, 
  SandboxInfo,
  SandboxStrategyType,
  StrategySelectionCriteria
} from './types'
import { SandboxStrategyFactory } from './factory'

// 全局工厂实例
let globalFactory: SandboxStrategyFactory | null = null

/**
 * 获取全局工厂实例
 */
function getGlobalFactory(): SandboxStrategyFactory {
  if (!globalFactory) {
    globalFactory = new SandboxStrategyFactory()
  }
  return globalFactory
}

/**
 * 创建沙箱实例
 */
export function createSandbox(
  name: string,
  strategy?: SandboxStrategyType,
  config?: SandboxConfig
): SandboxStrategy {
  const factory = getGlobalFactory()
  return factory.createStrategy(name, strategy, config)
}

/**
 * 获取沙箱策略信息
 */
export function getSandboxInfo(strategy: SandboxStrategyType): any {
  const factory = getGlobalFactory()
  return factory.getStrategyInfo(strategy)
}

/**
 * 检查沙箱策略是否受支持
 */
export function isSandboxSupported(strategy: SandboxStrategyType): boolean {
  const factory = getGlobalFactory()
  return factory.isStrategySupported(strategy)
}

/**
 * 获取最佳沙箱策略
 */
export function getBestSandboxStrategy(criteria?: StrategySelectionCriteria): SandboxStrategyType {
  const factory = getGlobalFactory()
  return factory.selectBestStrategy(criteria)
}

/**
 * 获取所有可用的沙箱策略
 */
export function getAvailableSandboxStrategies(): SandboxStrategyType[] {
  const factory = getGlobalFactory()
  return factory.getAvailableStrategies()
}

/**
 * 评估沙箱策略
 */
export function evaluateSandboxStrategies(criteria?: StrategySelectionCriteria) {
  const factory = getGlobalFactory()
  return factory.evaluateStrategies(criteria)
}

/**
 * 获取策略能力信息
 */
export function getStrategyCapabilities(strategy: SandboxStrategyType) {
  const factory = getGlobalFactory()
  return factory.getStrategyCapabilities(strategy)
}

/**
 * 检测浏览器环境能力
 */
export function detectBrowserCapabilities() {
  return {
    proxy: typeof Proxy !== 'undefined',
    shadowDOM: 'attachShadow' in Element.prototype,
    webWorker: typeof Worker !== 'undefined',
    iframe: typeof HTMLIFrameElement !== 'undefined',
    constructableStylesheets: 'adoptedStyleSheets' in Document.prototype,
    es6: (() => {
      try {
        new Function('(a = 0) => a')
        return true
      } catch (e) {
        return false
      }
    })(),
    modules: 'noModule' in HTMLScriptElement.prototype,
    customElements: 'customElements' in window,
    intersectionObserver: 'IntersectionObserver' in window,
    mutationObserver: 'MutationObserver' in window
  }
}

/**
 * 获取推荐的沙箱策略
 */
export function getRecommendedStrategy(): {
  strategy: SandboxStrategyType
  reason: string
  alternatives: SandboxStrategyType[]
} {
  const capabilities = detectBrowserCapabilities()
  
  // 现代浏览器优先使用 Proxy
  if (capabilities.proxy) {
    return {
      strategy: 'proxy',
      reason: '现代浏览器，支持 Proxy，性能最佳',
      alternatives: ['snapshot', 'vm']
    }
  }
  
  // 支持 Shadow DOM 的浏览器
  if (capabilities.shadowDOM) {
    return {
      strategy: 'shadow-dom',
      reason: '支持 Shadow DOM，提供样式隔离',
      alternatives: ['vm', 'snapshot']
    }
  }
  
  // 支持 Web Worker 的浏览器
  if (capabilities.webWorker) {
    return {
      strategy: 'webworker',
      reason: '支持 Web Worker，提供完全隔离',
      alternatives: ['vm', 'iframe']
    }
  }
  
  // 降级到 VM 策略
  return {
    strategy: 'vm',
    reason: '兼容性最佳，适用于所有环境',
    alternatives: ['snapshot']
  }
}

/**
 * 验证沙箱配置
 */
export function validateSandboxConfig(config: SandboxConfig): {
  valid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []
  
  // 检查名称
  if (config.name && !/^[a-zA-Z0-9_-]+$/.test(config.name)) {
    errors.push('沙箱名称只能包含字母、数字、下划线和连字符')
  }
  
  // 检查超时时间
  if (config.timeout !== undefined) {
    if (typeof config.timeout !== 'number' || config.timeout < 0) {
      errors.push('超时时间必须是非负数')
    } else if (config.timeout > 30000) {
      warnings.push('超时时间过长，可能影响用户体验')
    }
  }
  
  // 检查内存限制
  if (config.memoryLimit !== undefined) {
    if (typeof config.memoryLimit !== 'number' || config.memoryLimit <= 0) {
      errors.push('内存限制必须是正数')
    } else if (config.memoryLimit > 1024) {
      warnings.push('内存限制过高，可能影响系统性能')
    }
  }
  
  // 检查 API 列表
  if (config.allowedApis && !Array.isArray(config.allowedApis)) {
    errors.push('allowedApis 必须是数组')
  }
  
  if (config.blockedApis && !Array.isArray(config.blockedApis)) {
    errors.push('blockedApis 必须是数组')
  }
  
  // 检查冲突的 API 配置
  if (config.allowedApis && config.blockedApis) {
    const intersection = config.allowedApis.filter(api => 
      config.blockedApis!.includes(api)
    )
    if (intersection.length > 0) {
      warnings.push(`以下 API 同时出现在允许和禁止列表中: ${intersection.join(', ')}`)
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 创建安全的沙箱配置
 */
export function createSecureSandboxConfig(overrides?: Partial<SandboxConfig>): SandboxConfig {
  const secureConfig: SandboxConfig = {
    strict: true,
    timeout: 5000,
    memoryLimit: 64,
    enablePerformanceMonitoring: true,
    blockedApis: [
      'eval',
      'Function',
      'setTimeout',
      'setInterval',
      'XMLHttpRequest',
      'fetch',
      'WebSocket',
      'localStorage',
      'sessionStorage',
      'indexedDB',
      'navigator.geolocation',
      'navigator.camera',
      'navigator.microphone'
    ],
    allowedApis: [
      'console.log',
      'console.warn',
      'console.error',
      'JSON.parse',
      'JSON.stringify',
      'Math',
      'Date',
      'Array',
      'Object',
      'String',
      'Number',
      'Boolean'
    ],
    errorHandler: (error: Error) => {
      console.error('沙箱执行错误:', error)
    }
  }
  
  return { ...secureConfig, ...overrides }
}

/**
 * 性能基准测试
 */
export async function benchmarkSandboxStrategies(
  testCode: string = 'let sum = 0; for(let i = 0; i < 10000; i++) sum += i; return sum;',
  iterations: number = 100
): Promise<Record<SandboxStrategyType, { avgTime: number; minTime: number; maxTime: number; errors: number }>> {
  const results: Record<string, any> = {}
  const availableStrategies = getAvailableSandboxStrategies()
  
  for (const strategy of availableStrategies) {
    const times: number[] = []
    let errors = 0
    
    for (let i = 0; i < iterations; i++) {
      try {
        const sandbox = createSandbox(`benchmark-${strategy}-${i}`, strategy)
        sandbox.activate()
        
        const startTime = performance.now()
        sandbox.execute(testCode)
        const endTime = performance.now()
        
        times.push(endTime - startTime)
        sandbox.destroy()
      } catch (error) {
        errors++
      }
    }
    
    if (times.length > 0) {
      results[strategy] = {
        avgTime: times.reduce((a, b) => a + b, 0) / times.length,
        minTime: Math.min(...times),
        maxTime: Math.max(...times),
        errors
      }
    } else {
      results[strategy] = {
        avgTime: 0,
        minTime: 0,
        maxTime: 0,
        errors
      }
    }
  }
  
  return results
}

/**
 * 内存使用情况监控
 */
export function monitorSandboxMemory(sandbox: SandboxStrategy): {
  start: () => void
  stop: () => { peak: number; current: number }
} {
  let monitoring = false
  let peakMemory = 0
  let intervalId: number | null = null
  
  const start = () => {
    if (monitoring) return
    
    monitoring = true
    peakMemory = 0
    
    // 如果支持 performance.memory
    if ('memory' in performance) {
      intervalId = window.setInterval(() => {
        const memory = (performance as any).memory
        const current = memory.usedJSHeapSize / 1024 / 1024 // MB
        peakMemory = Math.max(peakMemory, current)
      }, 100)
    }
  }
  
  const stop = () => {
    monitoring = false
    if (intervalId) {
      clearInterval(intervalId)
      intervalId = null
    }
    
    let current = 0
    if ('memory' in performance) {
      const memory = (performance as any).memory
      current = memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    
    return { peak: peakMemory, current }
  }
  
  return { start, stop }
}

/**
 * 沙箱健康检查
 */
export function healthCheckSandbox(sandbox: SandboxStrategy): {
  healthy: boolean
  issues: string[]
  recommendations: string[]
} {
  const issues: string[] = []
  const recommendations: string[] = []
  
  try {
    // 检查基本功能
    const testResult = sandbox.execute('return 42;')
    if (testResult !== 42) {
      issues.push('基本代码执行异常')
    }
  } catch (error) {
    issues.push('无法执行基本代码')
  }
  
  // 检查状态
  const state = sandbox.getState()
  if (state === 'destroyed') {
    issues.push('沙箱已被销毁')
  } else if (state === 'inactive') {
    recommendations.push('建议激活沙箱以获得最佳性能')
  }
  
  // 检查上下文
  const context = sandbox.getContext()
  if (!context || Object.keys(context).length === 0) {
    issues.push('沙箱上下文为空')
  }
  
  return {
    healthy: issues.length === 0,
    issues,
    recommendations
  }
}