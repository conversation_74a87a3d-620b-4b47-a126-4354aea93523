/**
 * 沙箱策略工厂
 * 负责创建和管理不同类型的沙箱策略
 */

import type { 
  SandboxStrategy, 
  SandboxConfig,
  SandboxFactoryConfig,
  SandboxStrategyType,
  SandboxCapabilities,
  StrategySelectionCriteria,
  StrategyEvaluationResult
} from './types'

import { ProxySandbox } from './strategies/proxy'
import { SnapshotSandbox } from './strategies/snapshot'
import { IframeSandbox } from './strategies/iframe'
import { WebWorkerSandbox } from './strategies/webworker'
import { ShadowDOMSandbox } from './strategies/shadow-dom'
import { VMSandbox } from './strategies/vm'

export class SandboxStrategyFactory {
  private config: SandboxFactoryConfig
  private strategyConstructors: Map<string, new (name: string, config?: SandboxConfig) => SandboxStrategy>
  private capabilitiesCache: Map<string, SandboxCapabilities> = new Map()

  constructor(config: SandboxFactoryConfig = {}) {
    this.config = {
      defaultStrategy: 'proxy',
      autoSelectStrategy: true,
      enableCompatibilityCheck: true,
      strategyPriority: ['proxy', 'snapshot', 'shadow-dom', 'vm', 'iframe', 'webworker'],
      ...config
    }

    this.strategyConstructors = new Map([
      ['proxy', ProxySandbox],
      ['snapshot', SnapshotSandbox],
      ['iframe', IframeSandbox],
      ['webworker', WebWorkerSandbox],
      ['shadow-dom', ShadowDOMSandbox],
      ['vm', VMSandbox]
    ])

    // 添加自定义策略
    if (this.config.customStrategies) {
      Object.entries(this.config.customStrategies).forEach(([name, constructor]) => {
        this.strategyConstructors.set(name, constructor)
      })
    }

    // 初始化能力缓存
    this.initializeCapabilitiesCache()
  }

  /**
   * 初始化能力缓存
   */
  private initializeCapabilitiesCache(): void {
    const capabilities: Record<string, SandboxCapabilities> = {
      proxy: {
        domIsolation: false,
        styleIsolation: false,
        jsIsolation: true,
        networkIsolation: false,
        storageIsolation: false,
        eventIsolation: false,
        performanceOverhead: 'low',
        compatibility: 'modern'
      },
      snapshot: {
        domIsolation: false,
        styleIsolation: false,
        jsIsolation: true,
        networkIsolation: false,
        storageIsolation: false,
        eventIsolation: false,
        performanceOverhead: 'medium',
        compatibility: 'all'
      },
      iframe: {
        domIsolation: true,
        styleIsolation: true,
        jsIsolation: true,
        networkIsolation: true,
        storageIsolation: true,
        eventIsolation: true,
        performanceOverhead: 'high',
        compatibility: 'all'
      },
      webworker: {
        domIsolation: true,
        styleIsolation: true,
        jsIsolation: true,
        networkIsolation: true,
        storageIsolation: true,
        eventIsolation: true,
        performanceOverhead: 'medium',
        compatibility: 'modern'
      },
      'shadow-dom': {
        domIsolation: true,
        styleIsolation: true,
        jsIsolation: false,
        networkIsolation: false,
        storageIsolation: false,
        eventIsolation: true,
        performanceOverhead: 'low',
        compatibility: 'modern'
      },
      vm: {
        domIsolation: false,
        styleIsolation: false,
        jsIsolation: true,
        networkIsolation: false,
        storageIsolation: false,
        eventIsolation: false,
        performanceOverhead: 'medium',
        compatibility: 'all'
      }
    }

    Object.entries(capabilities).forEach(([strategy, caps]) => {
      this.capabilitiesCache.set(strategy, caps)
    })
  }

  /**
   * 创建沙箱策略
   */
  createStrategy(
    name: string,
    strategyType?: SandboxStrategyType,
    config?: SandboxConfig
  ): SandboxStrategy {
    // 自动选择策略
    if (!strategyType && this.config.autoSelectStrategy) {
      strategyType = this.selectBestStrategy()
    }

    // 使用默认策略
    if (!strategyType) {
      strategyType = this.config.defaultStrategy!
    }

    // 获取策略构造器
    const StrategyConstructor = this.strategyConstructors.get(strategyType)
    if (!StrategyConstructor) {
      throw new Error(`未知的沙箱策略: ${strategyType}`)
    }

    // 兼容性检查
    if (this.config.enableCompatibilityCheck && !this.isStrategySupported(strategyType)) {
      console.warn(`策略 ${strategyType} 在当前环境中不受支持，尝试使用备选策略`)
      
      // 寻找备选策略
      const fallbackStrategy = this.findFallbackStrategy(strategyType)
      if (fallbackStrategy) {
        const FallbackConstructor = this.strategyConstructors.get(fallbackStrategy)!
        return new FallbackConstructor(name, config)
      }
      
      throw new Error(`策略 ${strategyType} 不受支持且无可用的备选策略`)
    }

    return new StrategyConstructor(name, config)
  }

  /**
   * 选择最佳策略
   */
  selectBestStrategy(criteria?: StrategySelectionCriteria): SandboxStrategyType {
    const evaluationResults = this.evaluateStrategies(criteria)
    
    if (evaluationResults.length === 0) {
      return this.config.defaultStrategy!
    }

    // 按评分排序
    evaluationResults.sort((a, b) => b.score - a.score)
    
    return evaluationResults[0].strategy
  }

  /**
   * 评估所有策略
   */
  evaluateStrategies(criteria?: StrategySelectionCriteria): StrategyEvaluationResult[] {
    const results: StrategyEvaluationResult[] = []
    
    for (const [strategyName] of this.strategyConstructors) {
      const result = this.evaluateStrategy(strategyName as SandboxStrategyType, criteria)
      if (result.supported) {
        results.push(result)
      }
    }

    return results
  }

  /**
   * 评估单个策略
   */
  evaluateStrategy(
    strategy: SandboxStrategyType,
    criteria?: StrategySelectionCriteria
  ): StrategyEvaluationResult {
    const capabilities = this.getStrategyCapabilities(strategy)
    const supported = this.isStrategySupported(strategy)
    
    let score = 0
    let capabilityMatch = 0
    let performanceMatch = 0
    let compatibilityMatch = 0
    const details: string[] = []

    if (!supported) {
      return {
        strategy,
        score: 0,
        supported: false,
        capabilityMatch: 0,
        performanceMatch: 0,
        compatibilityMatch: 0,
        details: ['策略在当前环境中不受支持']
      }
    }

    // 基础分数
    score = 50

    // 能力匹配评估
    if (criteria?.requiredCapabilities) {
      const required = criteria.requiredCapabilities
      let matchedCapabilities = 0
      let totalCapabilities = 0

      Object.entries(required).forEach(([key, value]) => {
        totalCapabilities++
        const capKey = key as keyof SandboxCapabilities
        
        if (typeof value === 'boolean') {
          if (capabilities[capKey] === value) {
            matchedCapabilities++
            score += 10
          } else {
            score -= 5
            details.push(`能力 ${key} 不匹配`)
          }
        } else if (typeof value === 'string') {
          if (capabilities[capKey] === value) {
            matchedCapabilities++
            score += 10
          } else {
            score -= 5
            details.push(`能力 ${key} 不匹配`)
          }
        }
      })

      capabilityMatch = totalCapabilities > 0 ? matchedCapabilities / totalCapabilities : 1
    } else {
      capabilityMatch = 1
    }

    // 性能匹配评估
    if (criteria?.performanceRequirement) {
      const performanceLevels = { low: 1, medium: 2, high: 3 }
      const requiredLevel = performanceLevels[criteria.performanceRequirement]
      const actualLevel = performanceLevels[capabilities.performanceOverhead]
      
      if (actualLevel <= requiredLevel) {
        performanceMatch = 1
        score += 15
        details.push('性能要求满足')
      } else {
        performanceMatch = requiredLevel / actualLevel
        score -= 10
        details.push('性能开销过高')
      }
    } else {
      performanceMatch = 1
    }

    // 兼容性匹配评估
    if (criteria?.compatibilityRequirement) {
      const compatibilityLevels = { legacy: 3, all: 2, modern: 1 }
      const requiredLevel = compatibilityLevels[criteria.compatibilityRequirement]
      const actualLevel = compatibilityLevels[capabilities.compatibility]
      
      if (actualLevel >= requiredLevel) {
        compatibilityMatch = 1
        score += 10
        details.push('兼容性要求满足')
      } else {
        compatibilityMatch = actualLevel / requiredLevel
        score -= 15
        details.push('兼容性不足')
      }
    } else {
      compatibilityMatch = 1
    }

    // 排除策略检查
    if (criteria?.excludeStrategies?.includes(strategy)) {
      score = 0
      details.push('策略被明确排除')
    }

    // 首选策略加分
    if (criteria?.preferredStrategies?.includes(strategy)) {
      score += 20
      details.push('首选策略')
    }

    // 策略优先级加分
    const priorityIndex = this.config.strategyPriority?.indexOf(strategy) ?? -1
    if (priorityIndex >= 0) {
      score += (this.config.strategyPriority!.length - priorityIndex) * 2
    }

    return {
      strategy,
      score: Math.max(0, score),
      supported,
      capabilityMatch,
      performanceMatch,
      compatibilityMatch,
      details
    }
  }

  /**
   * 检查策略是否受支持
   */
  isStrategySupported(strategy: SandboxStrategyType): boolean {
    switch (strategy) {
      case 'proxy':
        return typeof Proxy !== 'undefined'
      case 'snapshot':
        return true
      case 'iframe':
        return typeof HTMLIFrameElement !== 'undefined'
      case 'webworker':
        return typeof Worker !== 'undefined'
      case 'shadow-dom':
        return 'attachShadow' in Element.prototype
      case 'vm':
        return typeof Function !== 'undefined'
      default:
        return false
    }
  }

  /**
   * 获取策略能力
   */
  getStrategyCapabilities(strategy: SandboxStrategyType): SandboxCapabilities {
    return this.capabilitiesCache.get(strategy) || {
      domIsolation: false,
      styleIsolation: false,
      jsIsolation: false,
      networkIsolation: false,
      storageIsolation: false,
      eventIsolation: false,
      performanceOverhead: 'high',
      compatibility: 'legacy'
    }
  }

  /**
   * 寻找备选策略
   */
  findFallbackStrategy(strategy: SandboxStrategyType): SandboxStrategyType | null {
    const fallbackMap: Record<SandboxStrategyType, SandboxStrategyType[]> = {
      'proxy': ['snapshot', 'vm'],
      'snapshot': ['vm', 'proxy'],
      'iframe': ['shadow-dom', 'vm'],
      'webworker': ['vm', 'iframe'],
      'shadow-dom': ['vm', 'snapshot'],
      'vm': ['snapshot', 'proxy']
    }

    const fallbacks = fallbackMap[strategy] || []
    
    for (const fallback of fallbacks) {
      if (this.isStrategySupported(fallback)) {
        return fallback
      }
    }

    return null
  }

  /**
   * 获取所有可用策略
   */
  getAvailableStrategies(): SandboxStrategyType[] {
    const strategies: SandboxStrategyType[] = []
    
    for (const [strategyName] of this.strategyConstructors) {
      if (this.isStrategySupported(strategyName as SandboxStrategyType)) {
        strategies.push(strategyName as SandboxStrategyType)
      }
    }

    return strategies
  }

  /**
   * 获取策略信息
   */
  getStrategyInfo(strategy: SandboxStrategyType): any {
    const StrategyConstructor = this.strategyConstructors.get(strategy)
    if (!StrategyConstructor) {
      return null
    }

    // 尝试获取静态信息方法
    if ('getInfo' in StrategyConstructor) {
      return (StrategyConstructor as any).getInfo()
    }

    return {
      name: strategy,
      description: `${strategy} 沙箱策略`,
      supported: this.isStrategySupported(strategy),
      capabilities: this.getStrategyCapabilities(strategy)
    }
  }

  /**
   * 注册自定义策略
   */
  registerStrategy(
    name: string,
    constructor: new (name: string, config?: SandboxConfig) => SandboxStrategy,
    capabilities?: SandboxCapabilities
  ): void {
    this.strategyConstructors.set(name, constructor)
    
    if (capabilities) {
      this.capabilitiesCache.set(name, capabilities)
    }
  }

  /**
   * 注销策略
   */
  unregisterStrategy(name: string): void {
    this.strategyConstructors.delete(name)
    this.capabilitiesCache.delete(name)
  }

  /**
   * 获取工厂配置
   */
  getConfig(): SandboxFactoryConfig {
    return { ...this.config }
  }

  /**
   * 更新工厂配置
   */
  updateConfig(config: Partial<SandboxFactoryConfig>): void {
    this.config = { ...this.config, ...config }
  }
}