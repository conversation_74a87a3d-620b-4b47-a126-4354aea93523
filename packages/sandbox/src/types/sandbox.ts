/**
 * 沙箱策略类型定义
 */

import type { SandboxState } from '@micro-core/shared'

/**
 * 沙箱上下文接口
 */
export interface SandboxContext {
  /** 窗口对象 */
  window: Window | Record<string, any>
  /** 文档对象 */
  document: Document | Record<string, any>
  /** 位置对象 */
  location: Location | Record<string, any>
  /** 历史对象 */
  history: History | Record<string, any>
  /** 其他自定义属性 */
  [key: string]: any
}

/**
 * 沙箱配置接口
 */
export interface SandboxConfig {
  /** 沙箱名称 */
  name?: string
  /** 是否启用严格模式 */
  strict?: boolean
  /** 超时时间（毫秒） */
  timeout?: number
  /** 内存限制（MB） */
  memoryLimit?: number
  /** 自定义上下文 */
  context?: Partial<SandboxContext>
  /** 白名单API */
  allowedApis?: string[]
  /** 黑名单API */
  blockedApis?: string[]
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean
  /** 错误处理器 */
  errorHandler?: (error: Error) => void
}

/**
 * 沙箱策略接口
 */
export interface SandboxStrategy {
  /** 获取沙箱名称 */
  getName(): string
  
  /** 获取沙箱状态 */
  getState(): SandboxState
  
  /** 获取沙箱上下文 */
  getContext(): SandboxContext
  
  /** 激活沙箱 */
  activate(): void
  
  /** 停用沙箱 */
  deactivate(): void
  
  /** 销毁沙箱 */
  destroy(): void
  
  /** 执行代码 */
  execute(code: string): any
  
  /** 设置上下文属性 */
  setContext(key: string, value: any): void
  
  /** 获取上下文属性 */
  getContextValue(key: string): any
}

/**
 * 沙箱信息接口
 */
export interface SandboxInfo {
  /** 策略名称 */
  name: string
  /** 策略描述 */
  description: string
  /** 是否支持 */
  supported: boolean
  /** 性能等级 */
  performance: 'low' | 'medium' | 'high'
  /** 隔离等级 */
  isolation: 'low' | 'medium' | 'high'
  /** 兼容性 */
  compatibility: 'legacy' | 'modern' | 'all'
}

/**
 * 沙箱性能指标接口
 */
export interface SandboxMetrics {
  /** 沙箱名称 */
  name: string
  /** 创建时间 */
  createTime: number
  /** 激活时间 */
  activateTime?: number
  /** 执行次数 */
  executeCount: number
  /** 总执行时间 */
  totalExecuteTime: number
  /** 平均执行时间 */
  averageExecuteTime: number
  /** 内存使用量 */
  memoryUsage: number
  /** 错误次数 */
  errorCount: number
  /** 最后执行时间 */
  lastExecuteTime?: number
}

/**
 * 沙箱事件类型
 */
export type SandboxEventType = 
  | 'create'
  | 'activate'
  | 'deactivate'
  | 'destroy'
  | 'execute'
  | 'error'

/**
 * 沙箱事件接口
 */
export interface SandboxEvent {
  /** 事件类型 */
  type: SandboxEventType
  /** 沙箱名称 */
  sandboxName: string
  /** 时间戳 */
  timestamp: number
  /** 事件数据 */
  data?: any
  /** 错误信息 */
  error?: Error
}

/**
 * 沙箱事件监听器
 */
export type SandboxEventListener = (event: SandboxEvent) => void

/**
 * 沙箱执行选项
 */
export interface SandboxExecuteOptions {
  /** 超时时间 */
  timeout?: number
  /** 是否异步执行 */
  async?: boolean
  /** 执行上下文 */
  context?: Record<string, any>
  /** 错误处理 */
  onError?: (error: Error) => void
  /** 成功回调 */
  onSuccess?: (result: any) => void
}

/**
 * 沙箱资源限制
 */
export interface SandboxResourceLimits {
  /** 最大内存使用量（MB） */
  maxMemory?: number
  /** 最大执行时间（毫秒） */
  maxExecutionTime?: number
  /** 最大调用栈深度 */
  maxCallStackSize?: number
  /** 最大循环次数 */
  maxLoopIterations?: number
}

/**
 * 沙箱安全策略
 */
export interface SandboxSecurityPolicy {
  /** 允许的全局变量 */
  allowedGlobals?: string[]
  /** 禁止的全局变量 */
  blockedGlobals?: string[]
  /** 允许的函数 */
  allowedFunctions?: string[]
  /** 禁止的函数 */
  blockedFunctions?: string[]
  /** 是否允许eval */
  allowEval?: boolean
  /** 是否允许Function构造器 */
  allowFunction?: boolean
  /** 是否允许动态导入 */
  allowDynamicImport?: boolean
}