/**
 * 沙箱类型定义主入口
 */

export type {
  SandboxStrategy,
  SandboxContext,
  SandboxConfig,
  SandboxInfo,
  SandboxMetrics
} from './sandbox'

export type {
  SandboxManagerConfig,
  SandboxManagerOptions,
  SandboxCreateOptions
} from './manager'

export type {
  SandboxFactoryConfig,
  SandboxStrategyType,
  SandboxCapabilities
} from './factory'

// 重新导出共享类型
export type {
  SandboxState,
  SandboxType,
  SandboxIsolationLevel
} from '@micro-core/shared'