/**
 * 沙箱管理器类型定义
 */

import type { SandboxStrategy, SandboxConfig, SandboxMetrics, SandboxEvent } from './sandbox'

/**
 * 沙箱管理器配置
 */
export interface SandboxManagerConfig {
  /** 默认沙箱策略 */
  defaultStrategy?: string
  /** 最大沙箱数量 */
  maxSandboxes?: number
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean
  /** 是否启用自动清理 */
  enableAutoCleanup?: boolean
  /** 自动清理间隔（毫秒） */
  autoCleanupInterval?: number
  /** 沙箱超时时间（毫秒） */
  sandboxTimeout?: number
  /** 全局错误处理器 */
  globalErrorHandler?: (error: Error, sandboxName: string) => void
  /** 事件监听器 */
  eventListeners?: Record<string, (event: SandboxEvent) => void>
}

/**
 * 沙箱管理器选项
 */
export interface SandboxManagerOptions {
  /** 配置 */
  config?: SandboxManagerConfig
  /** 自定义策略 */
  customStrategies?: Record<string, new (name: string, config?: SandboxConfig) => SandboxStrategy>
}

/**
 * 沙箱创建选项
 */
export interface SandboxCreateOptions {
  /** 沙箱名称 */
  name: string
  /** 策略类型 */
  strategy?: string
  /** 沙箱配置 */
  config?: SandboxConfig
  /** 是否立即激活 */
  autoActivate?: boolean
}

/**
 * 沙箱查询选项
 */
export interface SandboxQueryOptions {
  /** 按状态过滤 */
  state?: string
  /** 按策略过滤 */
  strategy?: string
  /** 按名称模式过滤 */
  namePattern?: string
  /** 排序字段 */
  sortBy?: 'name' | 'createTime' | 'executeCount'
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
  /** 限制数量 */
  limit?: number
}

/**
 * 沙箱统计信息
 */
export interface SandboxStats {
  /** 总沙箱数量 */
  total: number
  /** 活跃沙箱数量 */
  active: number
  /** 非活跃沙箱数量 */
  inactive: number
  /** 已销毁沙箱数量 */
  destroyed: number
  /** 按策略分组的统计 */
  byStrategy: Record<string, number>
  /** 总执行次数 */
  totalExecutions: number
  /** 总错误次数 */
  totalErrors: number
  /** 平均执行时间 */
  averageExecutionTime: number
}

/**
 * 沙箱批量操作选项
 */
export interface SandboxBatchOptions {
  /** 沙箱名称列表 */
  names?: string[]
  /** 按状态选择 */
  state?: string
  /** 按策略选择 */
  strategy?: string
  /** 排除的沙箱 */
  exclude?: string[]
  /** 并发限制 */
  concurrency?: number
}

/**
 * 沙箱导出选项
 */
export interface SandboxExportOptions {
  /** 包含配置 */
  includeConfig?: boolean
  /** 包含状态 */
  includeState?: boolean
  /** 包含指标 */
  includeMetrics?: boolean
  /** 包含上下文 */
  includeContext?: boolean
  /** 格式化输出 */
  format?: 'json' | 'yaml' | 'xml'
}

/**
 * 沙箱导入选项
 */
export interface SandboxImportOptions {
  /** 覆盖已存在的沙箱 */
  overwrite?: boolean
  /** 验证配置 */
  validate?: boolean
  /** 自动激活 */
  autoActivate?: boolean
  /** 错误处理策略 */
  errorStrategy?: 'skip' | 'stop' | 'continue'
}