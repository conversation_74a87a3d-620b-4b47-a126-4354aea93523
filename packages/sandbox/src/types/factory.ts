/**
 * 沙箱工厂类型定义
 */

import type { SandboxStrategy, SandboxConfig, SandboxInfo } from './sandbox'

/**
 * 沙箱策略类型
 */
export type SandboxStrategyType = 
  | 'proxy'
  | 'snapshot'
  | 'iframe'
  | 'webworker'
  | 'shadow-dom'
  | 'vm'

/**
 * 沙箱工厂配置
 */
export interface SandboxFactoryConfig {
  /** 默认策略 */
  defaultStrategy?: SandboxStrategyType
  /** 策略优先级 */
  strategyPriority?: SandboxStrategyType[]
  /** 自动选择策略 */
  autoSelectStrategy?: boolean
  /** 策略兼容性检查 */
  enableCompatibilityCheck?: boolean
  /** 自定义策略构造器 */
  customStrategies?: Record<string, new (name: string, config?: SandboxConfig) => SandboxStrategy>
}

/**
 * 沙箱能力接口
 */
export interface SandboxCapabilities {
  /** 是否支持DOM隔离 */
  domIsolation: boolean
  /** 是否支持样式隔离 */
  styleIsolation: boolean
  /** 是否支持JavaScript隔离 */
  jsIsolation: boolean
  /** 是否支持网络隔离 */
  networkIsolation: boolean
  /** 是否支持存储隔离 */
  storageIsolation: boolean
  /** 是否支持事件隔离 */
  eventIsolation: boolean
  /** 性能开销等级 */
  performanceOverhead: 'low' | 'medium' | 'high'
  /** 兼容性等级 */
  compatibility: 'legacy' | 'modern' | 'all'
}

/**
 * 策略选择条件
 */
export interface StrategySelectionCriteria {
  /** 所需能力 */
  requiredCapabilities?: Partial<SandboxCapabilities>
  /** 性能要求 */
  performanceRequirement?: 'low' | 'medium' | 'high'
  /** 兼容性要求 */
  compatibilityRequirement?: 'legacy' | 'modern' | 'all'
  /** 排除的策略 */
  excludeStrategies?: SandboxStrategyType[]
  /** 首选策略 */
  preferredStrategies?: SandboxStrategyType[]
}

/**
 * 策略评估结果
 */
export interface StrategyEvaluationResult {
  /** 策略类型 */
  strategy: SandboxStrategyType
  /** 评分 */
  score: number
  /** 是否支持 */
  supported: boolean
  /** 能力匹配度 */
  capabilityMatch: number
  /** 性能匹配度 */
  performanceMatch: number
  /** 兼容性匹配度 */
  compatibilityMatch: number
  /** 评估详情 */
  details: string[]
}