/**
 * 应用相关枚举定义
 */

/**
 * 应用状态枚举
 */
export enum AppStatus {
  /** 未加载 */
  NOT_LOADED = 'NOT_LOADED',
  /** 加载中 */
  LOADING = 'LOADING',
  /** 已加载 */
  LOADED = 'LOADED',
  /** 启动中 */
  BOOTSTRAPPING = 'BOOTSTRAPPING',
  /** 已启动 */
  BOOTSTRAPPED = 'BOOTSTRAPPED',
  /** 挂载中 */
  MOUNTING = 'MOUNTING',
  /** 已挂载 */
  MOUNTED = 'MOUNTED',
  /** 卸载中 */
  UNMOUNTING = 'UNMOUNTING',
  /** 已卸载 */
  UNMOUNTED = 'UNMOUNTED',
  /** 更新中 */
  UPDATING = 'UPDATING',
  /** 已更新 */
  UPDATED = 'UPDATED',
  /** 错误状态 */
  ERROR = 'ERROR',
  /** 跳过状态 */
  SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN'
}

/**
 * 应用类型枚举
 */
export enum AppType {
  /** 主应用 */
  MAIN = 'main',
  /** 子应用 */
  SUB = 'sub',
  /** 插件应用 */
  PLUGIN = 'plugin',
  /** 工具应用 */
  UTILITY = 'utility',
  /** 库应用 */
  LIBRARY = 'library',
  /** 组件应用 */
  COMPONENT = 'component'
}

/**
 * 应用模式枚举
 */
export enum AppMode {
  /** 开发模式 */
  DEVELOPMENT = 'development',
  /** 生产模式 */
  PRODUCTION = 'production',
  /** 测试模式 */
  TEST = 'test',
  /** 预览模式 */
  PREVIEW = 'preview',
  /** 调试模式 */
  DEBUG = 'debug'
}

/**
 * 应用优先级枚举
 */
export enum AppPriority {
  /** 紧急 */
  URGENT = 'urgent',
  /** 高优先级 */
  HIGH = 'high',
  /** 普通优先级 */
  NORMAL = 'normal',
  /** 低优先级 */
  LOW = 'low',
  /** 空闲时处理 */
  IDLE = 'idle'
}

/**
 * 应用加载策略枚举
 */
export enum LoadStrategy {
  /** 预加载 */
  PRELOAD = 'preload',
  /** 懒加载 */
  LAZY = 'lazy',
  /** 按需加载 */
  ON_DEMAND = 'onDemand',
  /** 立即加载 */
  IMMEDIATE = 'immediate',
  /** 延迟加载 */
  DELAYED = 'delayed'
}

/**
 * 应用缓存策略枚举
 */
export enum CacheStrategy {
  /** 无缓存 */
  NO_CACHE = 'noCache',
  /** 内存缓存 */
  MEMORY = 'memory',
  /** 磁盘缓存 */
  DISK = 'disk',
  /** 混合缓存 */
  HYBRID = 'hybrid',
  /** 自定义缓存 */
  CUSTOM = 'custom'
}

/**
 * 应用路由模式枚举
 */
export enum RouterMode {
  /** Hash模式 */
  HASH = 'hash',
  /** History模式 */
  HISTORY = 'history',
  /** Memory模式 */
  MEMORY = 'memory',
  /** 抽象模式 */
  ABSTRACT = 'abstract'
}

/**
 * 应用渲染模式枚举
 */
export enum RenderMode {
  /** 客户端渲染 */
  CSR = 'csr',
  /** 服务端渲染 */
  SSR = 'ssr',
  /** 静态生成 */
  SSG = 'ssg',
  /** 增量静态再生 */
  ISR = 'isr',
  /** 混合渲染 */
  HYBRID = 'hybrid'
}

/**
 * 应用框架类型枚举
 */
export enum FrameworkType {
  /** React */
  REACT = 'react',
  /** Vue */
  VUE = 'vue',
  /** Angular */
  ANGULAR = 'angular',
  /** Svelte */
  SVELTE = 'svelte',
  /** Solid */
  SOLID = 'solid',
  /** Preact */
  PREACT = 'preact',
  /** Lit */
  LIT = 'lit',
  /** 原生JavaScript */
  VANILLA = 'vanilla',
  /** jQuery */
  JQUERY = 'jquery',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 应用构建工具枚举
 */
export enum BuildTool {
  /** Webpack */
  WEBPACK = 'webpack',
  /** Vite */
  VITE = 'vite',
  /** Rollup */
  ROLLUP = 'rollup',
  /** Parcel */
  PARCEL = 'parcel',
  /** esbuild */
  ESBUILD = 'esbuild',
  /** SWC */
  SWC = 'swc',
  /** Turbopack */
  TURBOPACK = 'turbopack',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 应用部署环境枚举
 */
export enum DeployEnvironment {
  /** 本地开发 */
  LOCAL = 'local',
  /** 开发环境 */
  DEVELOPMENT = 'development',
  /** 测试环境 */
  TESTING = 'testing',
  /** 预发布环境 */
  STAGING = 'staging',
  /** 生产环境 */
  PRODUCTION = 'production',
  /** 灾备环境 */
  DISASTER_RECOVERY = 'disasterRecovery'
}

/**
 * 应用版本策略枚举
 */
export enum VersionStrategy {
  /** 语义化版本 */
  SEMANTIC = 'semantic',
  /** 时间戳版本 */
  TIMESTAMP = 'timestamp',
  /** Git提交版本 */
  GIT_COMMIT = 'gitCommit',
  /** 构建号版本 */
  BUILD_NUMBER = 'buildNumber',
  /** 自定义版本 */
  CUSTOM = 'custom'
}

/**
 * 应用监控级别枚举
 */
export enum MonitoringLevel {
  /** 无监控 */
  NONE = 'none',
  /** 基础监控 */
  BASIC = 'basic',
  /** 标准监控 */
  STANDARD = 'standard',
  /** 详细监控 */
  DETAILED = 'detailed',
  /** 完整监控 */
  COMPREHENSIVE = 'comprehensive'
}

/**
 * 应用错误处理策略枚举
 */
export enum ErrorHandlingStrategy {
  /** 忽略错误 */
  IGNORE = 'ignore',
  /** 记录错误 */
  LOG = 'log',
  /** 重试 */
  RETRY = 'retry',
  /** 降级 */
  FALLBACK = 'fallback',
  /** 停止应用 */
  STOP = 'stop',
  /** 重启应用 */
  RESTART = 'restart'
}

/**
 * 应用资源类型枚举
 */
export enum ResourceType {
  /** JavaScript */
  JAVASCRIPT = 'javascript',
  /** CSS */
  CSS = 'css',
  /** HTML */
  HTML = 'html',
  /** 图片 */
  IMAGE = 'image',
  /** 字体 */
  FONT = 'font',
  /** 音频 */
  AUDIO = 'audio',
  /** 视频 */
  VIDEO = 'video',
  /** 数据 */
  DATA = 'data',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 应用权限级别枚举
 */
export enum PermissionLevel {
  /** 无权限 */
  NONE = 'none',
  /** 只读权限 */
  READ_ONLY = 'readOnly',
  /** 读写权限 */
  READ_WRITE = 'readWrite',
  /** 管理员权限 */
  ADMIN = 'admin',
  /** 超级管理员权限 */
  SUPER_ADMIN = 'superAdmin'
}

/**
 * 应用安全级别枚举
 */
export enum SecurityLevel {
  /** 无安全 */
  NONE = 'none',
  /** 基础安全 */
  BASIC = 'basic',
  /** 标准安全 */
  STANDARD = 'standard',
  /** 高级安全 */
  ADVANCED = 'advanced',
  /** 企业级安全 */
  ENTERPRISE = 'enterprise'
}

/**
 * 应用性能级别枚举
 */
export enum PerformanceLevel {
  /** 低性能 */
  LOW = 'low',
  /** 普通性能 */
  NORMAL = 'normal',
  /** 高性能 */
  HIGH = 'high',
  /** 极高性能 */
  EXTREME = 'extreme',
  /** 自定义性能 */
  CUSTOM = 'custom'
}

/**
 * 应用兼容性级别枚举
 */
export enum CompatibilityLevel {
  /** 现代浏览器 */
  MODERN = 'modern',
  /** 标准浏览器 */
  STANDARD = 'standard',
  /** 传统浏览器 */
  LEGACY = 'legacy',
  /** 全兼容 */
  UNIVERSAL = 'universal',
  /** 自定义兼容 */
  CUSTOM = 'custom'
}

/**
 * 应用测试类型枚举
 */
export enum TestType {
  /** 单元测试 */
  UNIT = 'unit',
  /** 集成测试 */
  INTEGRATION = 'integration',
  /** 端到端测试 */
  E2E = 'e2e',
  /** 性能测试 */
  PERFORMANCE = 'performance',
  /** 安全测试 */
  SECURITY = 'security',
  /** 兼容性测试 */
  COMPATIBILITY = 'compatibility',
  /** 压力测试 */
  STRESS = 'stress',
  /** 负载测试 */
  LOAD = 'load'
}

/**
 * 应用文档类型枚举
 */
export enum DocumentationType {
  /** API文档 */
  API = 'api',
  /** 用户指南 */
  USER_GUIDE = 'userGuide',
  /** 开发指南 */
  DEVELOPER_GUIDE = 'developerGuide',
  /** 部署指南 */
  DEPLOYMENT_GUIDE = 'deploymentGuide',
  /** 故障排除 */
  TROUBLESHOOTING = 'troubleshooting',
  /** 更新日志 */
  CHANGELOG = 'changelog',
  /** 许可证 */
  LICENSE = 'license',
  /** 贡献指南 */
  CONTRIBUTING = 'contributing'
}

/**
 * 应用国际化策略枚举
 */
export enum I18nStrategy {
  /** 无国际化 */
  NONE = 'none',
  /** 静态国际化 */
  STATIC = 'static',
  /** 动态国际化 */
  DYNAMIC = 'dynamic',
  /** 懒加载国际化 */
  LAZY = 'lazy',
  /** 服务端国际化 */
  SERVER_SIDE = 'serverSide',
  /** 混合国际化 */
  HYBRID = 'hybrid'
}

/**
 * 应用主题策略枚举
 */
export enum ThemeStrategy {
  /** 无主题 */
  NONE = 'none',
  /** 静态主题 */
  STATIC = 'static',
  /** 动态主题 */
  DYNAMIC = 'dynamic',
  /** 自适应主题 */
  ADAPTIVE = 'adaptive',
  /** 用户自定义主题 */
  USER_CUSTOM = 'userCustom',
  /** 系统主题 */
  SYSTEM = 'system'
}

/**
 * 应用数据流策略枚举
 */
export enum DataFlowStrategy {
  /** 单向数据流 */
  UNIDIRECTIONAL = 'unidirectional',
  /** 双向数据流 */
  BIDIRECTIONAL = 'bidirectional',
  /** 事件驱动 */
  EVENT_DRIVEN = 'eventDriven',
  /** 状态管理 */
  STATE_MANAGEMENT = 'stateManagement',
  /** 响应式 */
  REACTIVE = 'reactive',
  /** 函数式 */
  FUNCTIONAL = 'functional'
}

/**
 * 应用存储策略枚举
 */
export enum StorageStrategy {
  /** 无存储 */
  NONE = 'none',
  /** 内存存储 */
  MEMORY = 'memory',
  /** 本地存储 */
  LOCAL_STORAGE = 'localStorage',
  /** 会话存储 */
  SESSION_STORAGE = 'sessionStorage',
  /** IndexedDB */
  INDEXED_DB = 'indexedDB',
  /** WebSQL */
  WEB_SQL = 'webSQL',
  /** 服务端存储 */
  SERVER_SIDE = 'serverSide',
  /** 混合存储 */
  HYBRID = 'hybrid'
}