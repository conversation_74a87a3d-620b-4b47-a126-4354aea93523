/**
 * 沙箱相关枚举定义
 */

/**
 * 沙箱类型枚举
 */
export enum SandboxType {
  /** Proxy沙箱 */
  PROXY = 'proxy',
  /** DefineProperty沙箱 */
  DEFINE_PROPERTY = 'defineProperty',
  /** iframe沙箱 */
  IFRAME = 'iframe',
  /** WebComponent沙箱 */
  WEB_COMPONENT = 'webComponent',
  /** Namespace沙箱 */
  NAMESPACE = 'namespace',
  /** Federation沙箱 */
  FEDERATION = 'federation'
}

/**
 * 沙箱状态枚举
 */
export enum SandboxStatus {
  /** 未创建 */
  NOT_CREATED = 'NOT_CREATED',
  /** 创建中 */
  CREATING = 'CREATING',
  /** 已创建 */
  CREATED = 'CREATED',
  /** 激活中 */
  ACTIVATING = 'ACTIVATING',
  /** 已激活 */
  ACTIVE = 'ACTIVE',
  /** 失活中 */
  DEACTIVATING = 'DEACTIVATING',
  /** 已失活 */
  INACTIVE = 'INACTIVE',
  /** 销毁中 */
  DESTROYING = 'DESTROYING',
  /** 已销毁 */
  DESTROYED = 'DESTROYED',
  /** 错误状态 */
  ERROR = 'ERROR'
}

/**
 * 隔离级别枚举
 */
export enum IsolationLevel {
  /** 无隔离 */
  NONE = 'none',
  /** 基础隔离 */
  BASIC = 'basic',
  /** 标准隔离 */
  STANDARD = 'standard',
  /** 严格隔离 */
  STRICT = 'strict',
  /** 完全隔离 */
  COMPLETE = 'complete'
}

/**
 * 沙箱策略枚举
 */
export enum SandboxStrategy {
  /** 单实例策略 */
  SINGLETON = 'singleton',
  /** 多实例策略 */
  MULTIPLE = 'multiple',
  /** 共享策略 */
  SHARED = 'shared',
  /** 独立策略 */
  ISOLATED = 'isolated'
}

/**
 * CSS隔离策略枚举
 */
export enum CSSIsolationStrategy {
  /** 无隔离 */
  NONE = 'none',
  /** 作用域隔离 */
  SCOPED = 'scoped',
  /** Shadow DOM隔离 */
  SHADOW_DOM = 'shadowDOM',
  /** 命名空间隔离 */
  NAMESPACE = 'namespace',
  /** 前缀隔离 */
  PREFIX = 'prefix'
}

/**
 * JavaScript隔离策略枚举
 */
export enum JSIsolationStrategy {
  /** 无隔离 */
  NONE = 'none',
  /** Proxy隔离 */
  PROXY = 'proxy',
  /** 快照隔离 */
  SNAPSHOT = 'snapshot',
  /** iframe隔离 */
  IFRAME = 'iframe',
  /** Web Worker隔离 */
  WEB_WORKER = 'webWorker'
}

/**
 * 沙箱权限枚举
 */
export enum SandboxPermission {
  /** DOM访问权限 */
  DOM_ACCESS = 'dom:access',
  /** DOM修改权限 */
  DOM_MODIFY = 'dom:modify',
  /** 网络请求权限 */
  NETWORK_REQUEST = 'network:request',
  /** 存储访问权限 */
  STORAGE_ACCESS = 'storage:access',
  /** 存储修改权限 */
  STORAGE_MODIFY = 'storage:modify',
  /** 定时器权限 */
  TIMER = 'timer',
  /** 事件监听权限 */
  EVENT_LISTENER = 'event:listener',
  /** 全局变量访问权限 */
  GLOBAL_ACCESS = 'global:access',
  /** 全局变量修改权限 */
  GLOBAL_MODIFY = 'global:modify',
  /** 脚本执行权限 */
  SCRIPT_EXECUTION = 'script:execution',
  /** 模块加载权限 */
  MODULE_LOADING = 'module:loading'
}

/**
 * 沙箱错误类型枚举
 */
export enum SandboxErrorType {
  /** 创建失败 */
  CREATE_FAILED = 'CREATE_FAILED',
  /** 激活失败 */
  ACTIVATE_FAILED = 'ACTIVATE_FAILED',
  /** 失活失败 */
  DEACTIVATE_FAILED = 'DEACTIVATE_FAILED',
  /** 销毁失败 */
  DESTROY_FAILED = 'DESTROY_FAILED',
  /** 权限违规 */
  PERMISSION_VIOLATION = 'PERMISSION_VIOLATION',
  /** 资源超限 */
  RESOURCE_EXCEEDED = 'RESOURCE_EXCEEDED',
  /** 内存泄漏 */
  MEMORY_LEAK = 'MEMORY_LEAK',
  /** 执行超时 */
  EXECUTION_TIMEOUT = 'EXECUTION_TIMEOUT',
  /** 隔离失败 */
  ISOLATION_FAILED = 'ISOLATION_FAILED',
  /** 不支持的操作 */
  UNSUPPORTED_OPERATION = 'UNSUPPORTED_OPERATION'
}

/**
 * 沙箱监控指标枚举
 */
export enum SandboxMetric {
  /** 内存使用量 */
  MEMORY_USAGE = 'memoryUsage',
  /** CPU使用率 */
  CPU_USAGE = 'cpuUsage',
  /** 网络请求数 */
  NETWORK_REQUESTS = 'networkRequests',
  /** DOM操作数 */
  DOM_OPERATIONS = 'domOperations',
  /** 事件触发数 */
  EVENT_TRIGGERS = 'eventTriggers',
  /** 定时器数量 */
  TIMER_COUNT = 'timerCount',
  /** 存储使用量 */
  STORAGE_USAGE = 'storageUsage',
  /** 错误数量 */
  ERROR_COUNT = 'errorCount',
  /** 警告数量 */
  WARNING_COUNT = 'warningCount',
  /** 执行时间 */
  EXECUTION_TIME = 'executionTime'
}

/**
 * 沙箱清理策略枚举
 */
export enum SandboxCleanupStrategy {
  /** 立即清理 */
  IMMEDIATE = 'immediate',
  /** 延迟清理 */
  DELAYED = 'delayed',
  /** 批量清理 */
  BATCH = 'batch',
  /** 按需清理 */
  ON_DEMAND = 'onDemand',
  /** 自动清理 */
  AUTO = 'auto'
}

/**
 * 沙箱通信类型枚举
 */
export enum SandboxCommunicationType {
  /** 主通道 */
  MAIN = 'main',
  /** 数据通道 */
  DATA = 'data',
  /** 事件通道 */
  EVENT = 'event',
  /** 调试通道 */
  DEBUG = 'debug'
}

/**
 * 沙箱消息类型枚举
 */
export enum SandboxMessageType {
  /** 初始化 */
  INIT = 'init',
  /** 执行 */
  EXECUTE = 'execute',
  /** 响应 */
  RESPONSE = 'response',
  /** 错误 */
  ERROR = 'error',
  /** 销毁 */
  DESTROY = 'destroy'
}

/**
 * 沙箱消息优先级枚举
 */
export enum SandboxMessagePriority {
  /** 高优先级 */
  HIGH = 'high',
  /** 普通优先级 */
  NORMAL = 'normal',
  /** 低优先级 */
  LOW = 'low'
}

/**
 * 沙箱安全策略枚举
 */
export enum SandboxSecurityPolicy {
  /** 内容安全策略 */
  CSP = 'csp',
  /** 特性策略 */
  FEATURE_POLICY = 'featurePolicy',
  /** 权限策略 */
  PERMISSIONS_POLICY = 'permissionsPolicy',
  /** 同源策略 */
  SAME_ORIGIN_POLICY = 'sameOriginPolicy',
  /** 跨域资源共享 */
  CORS = 'cors'
}

/**
 * 沙箱兼容性级别枚举
 */
export enum SandboxCompatibilityLevel {
  /** 现代浏览器 */
  MODERN = 'modern',
  /** 标准浏览器 */
  STANDARD = 'standard',
  /** 传统浏览器 */
  LEGACY = 'legacy',
  /** 全兼容 */
  UNIVERSAL = 'universal'
}

/**
 * 沙箱性能级别枚举
 */
export enum SandboxPerformanceLevel {
  /** 低性能 */
  LOW = 'low',
  /** 普通性能 */
  NORMAL = 'normal',
  /** 高性能 */
  HIGH = 'high',
  /** 极高性能 */
  EXTREME = 'extreme'
}

/**
 * 沙箱调试级别枚举
 */
export enum SandboxDebugLevel {
  /** 无调试 */
  NONE = 'none',
  /** 基础调试 */
  BASIC = 'basic',
  /** 详细调试 */
  VERBOSE = 'verbose',
  /** 完整调试 */
  FULL = 'full'
}

/**
 * 沙箱资源类型枚举
 */
export enum SandboxResourceType {
  /** JavaScript */
  JAVASCRIPT = 'javascript',
  /** CSS */
  CSS = 'css',
  /** HTML */
  HTML = 'html',
  /** 图片 */
  IMAGE = 'image',
  /** 字体 */
  FONT = 'font',
  /** 数据 */
  DATA = 'data',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 沙箱生命周期阶段枚举
 */
export enum SandboxLifecyclePhase {
  /** 初始化前 */
  BEFORE_INIT = 'beforeInit',
  /** 初始化后 */
  AFTER_INIT = 'afterInit',
  /** 激活前 */
  BEFORE_ACTIVATE = 'beforeActivate',
  /** 激活后 */
  AFTER_ACTIVATE = 'afterActivate',
  /** 失活前 */
  BEFORE_DEACTIVATE = 'beforeDeactivate',
  /** 失活后 */
  AFTER_DEACTIVATE = 'afterDeactivate',
  /** 销毁前 */
  BEFORE_DESTROY = 'beforeDestroy',
  /** 销毁后 */
  AFTER_DESTROY = 'afterDestroy'
}

/**
 * 沙箱事件类型枚举
 */
export enum SandboxEventType {
  /** 沙箱创建 */
  CREATED = 'sandbox:created',
  /** 沙箱激活 */
  ACTIVATED = 'sandbox:activated',
  /** 沙箱失活 */
  DEACTIVATED = 'sandbox:deactivated',
  /** 沙箱销毁 */
  DESTROYED = 'sandbox:destroyed',
  /** 沙箱错误 */
  ERROR = 'sandbox:error',
  /** 全局变量访问 */
  GLOBAL_ACCESS = 'sandbox:globalAccess',
  /** 全局变量修改 */
  GLOBAL_MODIFY = 'sandbox:globalModify',
  /** 内存泄漏检测 */
  MEMORY_LEAK = 'sandbox:memoryLeak',
  /** 性能警告 */
  PERFORMANCE_WARNING = 'sandbox:performanceWarning'
}

/**
 * 沙箱模式枚举
 */
export enum SandboxMode {
  /** 开发模式 */
  DEVELOPMENT = 'development',
  /** 生产模式 */
  PRODUCTION = 'production',
  /** 测试模式 */
  TEST = 'test',
  /** 调试模式 */
  DEBUG = 'debug'
}

/**
 * 沙箱环境枚举
 */
export enum SandboxEnvironment {
  /** 浏览器环境 */
  BROWSER = 'browser',
  /** Node.js环境 */
  NODE = 'node',
  /** Web Worker环境 */
  WEB_WORKER = 'webWorker',
  /** Service Worker环境 */
  SERVICE_WORKER = 'serviceWorker',
  /** 混合环境 */
  HYBRID = 'hybrid'
}

/**
 * 沙箱版本策略枚举
 */
export enum SandboxVersionStrategy {
  /** 固定版本 */
  FIXED = 'fixed',
  /** 最新版本 */
  LATEST = 'latest',
  /** 兼容版本 */
  COMPATIBLE = 'compatible',
  /** 自动更新 */
  AUTO_UPDATE = 'autoUpdate'
}

/**
 * 沙箱更新策略枚举
 */
export enum SandboxUpdateStrategy {
  /** 热更新 */
  HOT_UPDATE = 'hotUpdate',
  /** 冷更新 */
  COLD_UPDATE = 'coldUpdate',
  /** 增量更新 */
  INCREMENTAL_UPDATE = 'incrementalUpdate',
  /** 全量更新 */
  FULL_UPDATE = 'fullUpdate',
  /** 按需更新 */
  ON_DEMAND_UPDATE = 'onDemandUpdate'
}

/**
 * 沙箱回滚策略枚举
 */
export enum SandboxRollbackStrategy {
  /** 自动回滚 */
  AUTO_ROLLBACK = 'autoRollback',
  /** 手动回滚 */
  MANUAL_ROLLBACK = 'manualRollback',
  /** 快照回滚 */
  SNAPSHOT_ROLLBACK = 'snapshotRollback',
  /** 版本回滚 */
  VERSION_ROLLBACK = 'versionRollback'
}

/**
 * 沙箱备份策略枚举
 */
export enum SandboxBackupStrategy {
  /** 无备份 */
  NO_BACKUP = 'noBackup',
  /** 自动备份 */
  AUTO_BACKUP = 'autoBackup',
  /** 手动备份 */
  MANUAL_BACKUP = 'manualBackup',
  /** 增量备份 */
  INCREMENTAL_BACKUP = 'incrementalBackup',
  /** 全量备份 */
  FULL_BACKUP = 'fullBackup'
}

/**
 * 沙箱恢复策略枚举
 */
export enum SandboxRecoveryStrategy {
  /** 自动恢复 */
  AUTO_RECOVERY = 'autoRecovery',
  /** 手动恢复 */
  MANUAL_RECOVERY = 'manualRecovery',
  /** 快速恢复 */
  FAST_RECOVERY = 'fastRecovery',
  /** 完整恢复 */
  FULL_RECOVERY = 'fullRecovery'
}

/**
 * 沙箱测试类型枚举
 */
export enum SandboxTestType {
  /** 单元测试 */
  UNIT = 'unit',
  /** 集成测试 */
  INTEGRATION = 'integration',
  /** 性能测试 */
  PERFORMANCE = 'performance',
  /** 安全测试 */
  SECURITY = 'security',
  /** 兼容性测试 */
  COMPATIBILITY = 'compatibility',
  /** 压力测试 */
  STRESS = 'stress'
}

/**
 * 沙箱日志级别枚举
 */
export enum SandboxLogLevel {
  /** 调试 */
  DEBUG = 'debug',
  /** 信息 */
  INFO = 'info',
  /** 警告 */
  WARN = 'warn',
  /** 错误 */
  ERROR = 'error',
  /** 致命错误 */
  FATAL = 'fatal'
}

/**
 * 沙箱配置类型枚举
 */
export enum SandboxConfigType {
  /** 默认配置 */
  DEFAULT = 'default',
  /** 自定义配置 */
  CUSTOM = 'custom',
  /** 继承配置 */
  INHERITED = 'inherited',
  /** 合并配置 */
  MERGED = 'merged'
}

/**
 * 沙箱插件类型枚举
 */
export enum SandboxPluginType {
  /** 核心插件 */
  CORE = 'core',
  /** 扩展插件 */
  EXTENSION = 'extension',
  /** 第三方插件 */
  THIRD_PARTY = 'thirdParty',
  /** 自定义插件 */
  CUSTOM = 'custom'
}

/**
 * 沙箱中间件类型枚举
 */
export enum SandboxMiddlewareType {
  /** 请求中间件 */
  REQUEST = 'request',
  /** 响应中间件 */
  RESPONSE = 'response',
  /** 错误中间件 */
  ERROR = 'error',
  /** 日志中间件 */
  LOGGING = 'logging',
  /** 监控中间件 */
  MONITORING = 'monitoring'
}

/**
 * 沙箱拦截器类型枚举
 */
export enum SandboxInterceptorType {
  /** 全局拦截器 */
  GLOBAL = 'global',
  /** 局部拦截器 */
  LOCAL = 'local',
  /** 条件拦截器 */
  CONDITIONAL = 'conditional',
  /** 动态拦截器 */
  DYNAMIC = 'dynamic'
}

/**
 * 沙箱适配器类型枚举
 */
export enum SandboxAdapterType {
  /** 框架适配器 */
  FRAMEWORK = 'framework',
  /** 构建工具适配器 */
  BUILD_TOOL = 'buildTool',
  /** 运行时适配器 */
  RUNTIME = 'runtime',
  /** 环境适配器 */
  ENVIRONMENT = 'environment'
}