/**
 * 微前端核心错误基类
 */

/**
 * 微前端核心错误基类
 */
export class MicroCoreError extends Error {
  /** 错误代码 */
  public readonly code: string
  
  /** 错误上下文 */
  public readonly context?: Record<string, any>
  
  /** 错误时间戳 */
  public readonly timestamp: number
  
  /** 错误堆栈信息 */
  public readonly stack?: string
  
  /** 错误来源 */
  public readonly source?: string
  
  /** 错误级别 */
  public readonly level: 'info' | 'warn' | 'error' | 'fatal'
  
  /** 是否可恢复 */
  public readonly recoverable: boolean
  
  /** 重试次数 */
  public retryCount: number = 0
  
  /** 最大重试次数 */
  public readonly maxRetries: number
  
  /** 原始错误 */
  public readonly originalError?: Error

  constructor(
    message: string,
    code: string,
    options: {
      context?: Record<string, any>
      source?: string
      level?: 'info' | 'warn' | 'error' | 'fatal'
      recoverable?: boolean
      maxRetries?: number
      originalError?: Error
    } = {}
  ) {
    super(message)
    
    this.name = this.constructor.name
    this.code = code
    this.context = options.context || undefined
    this.timestamp = Date.now()
    this.source = options.source || undefined
    this.level = options.level || 'error'
    this.recoverable = options.recoverable ?? false
    this.maxRetries = options.maxRetries || 0
    this.originalError = options.originalError || undefined
    
    // 确保错误堆栈正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor)
    }
  }

  /**
   * 转换为JSON格式
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      context: this.context,
      timestamp: this.timestamp,
      source: this.source,
      level: this.level,
      recoverable: this.recoverable,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      stack: this.stack,
      originalError: this.originalError ? {
        name: this.originalError.name,
        message: this.originalError.message,
        stack: this.originalError.stack
      } : undefined
    }
  }

  /**
   * 转换为字符串格式
   */
  toString(): string {
    let result = `${this.name}: ${this.message} (${this.code})`
    
    if (this.source) {
      result += ` [来源: ${this.source}]`
    }
    
    if (this.context && Object.keys(this.context).length > 0) {
      result += ` [上下文: ${JSON.stringify(this.context)}]`
    }
    
    return result
  }

  /**
   * 增加重试次数
   */
  incrementRetry(): void {
    this.retryCount++
  }

  /**
   * 是否可以重试
   */
  canRetry(): boolean {
    return this.recoverable && this.retryCount < this.maxRetries
  }

  /**
   * 获取错误详情
   */
  getDetails(): string {
    const details = [
      `错误名称: ${this.name}`,
      `错误消息: ${this.message}`,
      `错误代码: ${this.code}`,
      `错误级别: ${this.level}`,
      `发生时间: ${new Date(this.timestamp).toISOString()}`,
      `是否可恢复: ${this.recoverable ? '是' : '否'}`,
      `重试次数: ${this.retryCount}/${this.maxRetries}`
    ]
    
    if (this.source) {
      details.push(`错误来源: ${this.source}`)
    }
    
    if (this.context && Object.keys(this.context).length > 0) {
      details.push(`错误上下文: ${JSON.stringify(this.context, null, 2)}`)
    }
    
    if (this.originalError) {
      details.push(`原始错误: ${this.originalError.message}`)
    }
    
    if (this.stack) {
      details.push(`错误堆栈:\n${this.stack}`)
    }
    
    return details.join('\n')
  }

  /**
   * 克隆错误对象
   */
  clone(): MicroCoreError {
    const options: any = {
      level: this.level,
      recoverable: this.recoverable,
      maxRetries: this.maxRetries
    }
    
    if (this.context) {
      options.context = { ...this.context }
    }
    
    if (this.source) {
      options.source = this.source
    }
    
    if (this.originalError) {
      options.originalError = this.originalError
    }
    
    const cloned = new MicroCoreError(this.message, this.code, options)
    cloned.retryCount = this.retryCount
    
    return cloned
  }

  /**
   * 检查是否为指定类型的错误
   */
  isType(errorType: string): boolean {
    return this.code === errorType || this.name === errorType
  }

  /**
   * 检查是否包含指定上下文
   */
  hasContext(key: string): boolean {
    return this.context ? key in this.context : false
  }

  /**
   * 获取上下文值
   */
  getContext<T = any>(key: string): T | undefined {
    return this.context ? this.context[key] : undefined
  }

  /**
   * 设置上下文值
   */
  setContext(key: string, value: any): void {
    if (!this.context) {
      (this as any).context = {}
    }
    this.context![key] = value
  }

  /**
   * 检查错误级别
   */
  isLevel(level: 'info' | 'warn' | 'error' | 'fatal'): boolean {
    return this.level === level
  }

  /**
   * 检查是否为致命错误
   */
  isFatal(): boolean {
    return this.level === 'fatal'
  }

  /**
   * 检查是否为警告
   */
  isWarning(): boolean {
    return this.level === 'warn'
  }

  /**
   * 检查是否为信息
   */
  isInfo(): boolean {
    return this.level === 'info'
  }

  /**
   * 获取错误摘要
   */
  getSummary(): string {
    return `[${this.level.toUpperCase()}] ${this.code}: ${this.message}`
  }

  /**
   * 获取错误标识符
   */
  getId(): string {
    return `${this.code}_${this.timestamp}`
  }

  /**
   * 比较两个错误是否相同
   */
  equals(other: MicroCoreError): boolean {
    return (
      this.code === other.code &&
      this.message === other.message &&
      this.source === other.source &&
      JSON.stringify(this.context) === JSON.stringify(other.context)
    )
  }

  /**
   * 获取错误哈希值
   */
  getHash(): string {
    const content = `${this.code}:${this.message}:${this.source || ''}:${JSON.stringify(this.context || {})}`
    return btoa(content).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16)
  }

  /**
   * 检查是否为网络错误
   */
  isNetworkError(): boolean {
    return this.code.includes('NETWORK') || this.code.includes('CONNECTION')
  }

  /**
   * 检查是否为超时错误
   */
  isTimeoutError(): boolean {
    return this.code.includes('TIMEOUT') || this.code.includes('EXPIRED')
  }

  /**
   * 检查是否为权限错误
   */
  isPermissionError(): boolean {
    return this.code.includes('PERMISSION') || this.code.includes('UNAUTHORIZED') || this.code.includes('FORBIDDEN')
  }

  /**
   * 检查是否为配置错误
   */
  isConfigError(): boolean {
    return this.code.includes('CONFIG') || this.code.includes('INVALID_PARAMETER')
  }

  /**
   * 检查是否为资源错误
   */
  isResourceError(): boolean {
    return this.code.includes('RESOURCE') || this.code.includes('NOT_FOUND') || this.code.includes('LOAD_FAILED')
  }

  /**
   * 获取建议的解决方案
   */
  getSuggestion(): string {
    if (this.isNetworkError()) {
      return '请检查网络连接是否正常，或稍后重试'
    }
    
    if (this.isTimeoutError()) {
      return '请求超时，请检查网络状况或增加超时时间'
    }
    
    if (this.isPermissionError()) {
      return '权限不足，请检查用户权限配置'
    }
    
    if (this.isConfigError()) {
      return '配置错误，请检查相关配置参数'
    }
    
    if (this.isResourceError()) {
      return '资源加载失败，请检查资源路径是否正确'
    }
    
    return '请查看错误详情并联系技术支持'
  }

  /**
   * 获取错误分类
   */
  getCategory(): string {
    if (this.isNetworkError()) return '网络错误'
    if (this.isTimeoutError()) return '超时错误'
    if (this.isPermissionError()) return '权限错误'
    if (this.isConfigError()) return '配置错误'
    if (this.isResourceError()) return '资源错误'
    return '未知错误'
  }

  /**
   * 创建子错误
   */
  createChildError(message: string, code: string, context?: Record<string, any>): MicroCoreError {
    const options: any = {
      level: this.level,
      recoverable: this.recoverable,
      maxRetries: this.maxRetries,
      originalError: this
    }
    
    if (this.context || context) {
      options.context = { ...this.context, ...context }
    }
    
    if (this.source) {
      options.source = this.source
    }
    
    return new MicroCoreError(message, code, options)
  }

  /**
   * 包装原始错误
   */
  static wrap(error: Error, code: string, context?: Record<string, any>): MicroCoreError {
    if (error instanceof MicroCoreError) {
      return error
    }
    
    const options: any = {
      originalError: error,
      recoverable: true,
      maxRetries: 3
    }
    
    if (context) {
      options.context = context
    }
    
    return new MicroCoreError(error.message, code, options)
  }

  /**
   * 从JSON创建错误对象
   */
  static fromJSON(json: Record<string, any>): MicroCoreError {
    const options: any = {
      level: json.level,
      recoverable: json.recoverable,
      maxRetries: json.maxRetries
    }
    
    if (json.context) {
      options.context = json.context
    }
    
    if (json.source) {
      options.source = json.source
    }
    
    if (json.originalError) {
      options.originalError = new Error(json.originalError.message)
    }
    
    const error = new MicroCoreError(json.message, json.code, options)
    error.retryCount = json.retryCount || 0
    
    return error
  }

  /**
   * 检查是否为MicroCoreError实例
   */
  static isMicroCoreError(error: any): error is MicroCoreError {
    return error instanceof MicroCoreError
  }

  /**
   * 获取错误的严重程度分数
   */
  getSeverityScore(): number {
    const levelScores = {
      info: 1,
      warn: 2,
      error: 3,
      fatal: 4
    }
    
    let score = levelScores[this.level]
    
    // 根据错误类型调整分数
    if (this.isFatal()) score += 2
    if (this.isNetworkError()) score += 1
    if (this.isPermissionError()) score += 1
    if (!this.recoverable) score += 1
    
    return Math.min(score, 10) // 最高分数为10
  }

  /**
   * 检查是否需要立即处理
   */
  requiresImmediateAttention(): boolean {
    return this.isFatal() || this.getSeverityScore() >= 7
  }

  /**
   * 获取错误的用户友好消息
   */
  getUserFriendlyMessage(): string {
    // 根据错误类型返回用户友好的消息
    const friendlyMessages: Record<string, string> = {
      'NETWORK_ERROR': '网络连接异常，请检查网络设置',
      'TIMEOUT_ERROR': '请求超时，请稍后重试',
      'PERMISSION_DENIED': '权限不足，请联系管理员',
      'RESOURCE_NOT_FOUND': '资源未找到，请检查配置',
      'INVALID_CONFIG': '配置错误，请检查设置',
      'LOAD_FAILED': '加载失败，请刷新页面重试'
    }
    
    return friendlyMessages[this.code] || this.message
  }
}

/**
 * 错误构建器类
 */
export class ErrorBuilder {
  private message: string = ''
  private code: string = ''
  private context?: Record<string, any>
  private source?: string
  private level: 'info' | 'warn' | 'error' | 'fatal' = 'error'
  private recoverable: boolean = false
  private maxRetries: number = 0
  private originalError?: Error

  /**
   * 设置错误消息
   */
  setMessage(message: string): ErrorBuilder {
    this.message = message
    return this
  }

  /**
   * 设置错误代码
   */
  setCode(code: string): ErrorBuilder {
    this.code = code
    return this
  }

  /**
   * 设置错误上下文
   */
  setContext(context: Record<string, any>): ErrorBuilder {
    this.context = context
    return this
  }

  /**
   * 添加上下文项
   */
  addContext(key: string, value: any): ErrorBuilder {
    if (!this.context) {
      this.context = {}
    }
    this.context[key] = value
    return this
  }

  /**
   * 设置错误来源
   */
  setSource(source: string): ErrorBuilder {
    this.source = source
    return this
  }

  /**
   * 设置错误级别
   */
  setLevel(level: 'info' | 'warn' | 'error' | 'fatal'): ErrorBuilder {
    this.level = level
    return this
  }

  /**
   * 设置是否可恢复
   */
  setRecoverable(recoverable: boolean): ErrorBuilder {
    this.recoverable = recoverable
    return this
  }

  /**
   * 设置最大重试次数
   */
  setMaxRetries(maxRetries: number): ErrorBuilder {
    this.maxRetries = maxRetries
    return this
  }

  /**
   * 设置原始错误
   */
  setOriginalError(originalError: Error): ErrorBuilder {
    this.originalError = originalError
    return this
  }

  /**
   * 构建错误对象
   */
  build(): MicroCoreError {
    if (!this.message || !this.code) {
      throw new Error('错误消息和错误代码是必需的')
    }

    return new MicroCoreError(this.message, this.code, {
      context: this.context,
      source: this.source,
      level: this.level,
      recoverable: this.recoverable,
      maxRetries: this.maxRetries,
      originalError: this.originalError
    })
  }

  /**
   * 重置构建器
   */
  reset(): ErrorBuilder {
    this.message = ''
    this.code = ''
    this.context = undefined
    this.source = undefined
    this.level = 'error'
    this.recoverable = false
    this.maxRetries = 0
    this.originalError = undefined
    return this
  }
}