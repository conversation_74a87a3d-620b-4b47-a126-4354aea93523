/**
 * 错误处理相关类型定义
 */

/**
 * 错误级别
 */
export type ErrorLevel = 'info' | 'warn' | 'error' | 'fatal'

/**
 * 错误代码枚举
 */
export const ERROR_CODES = {
  // 系统错误
  SYSTEM_ERROR: 'system_error',
  UNKNOWN: 'unknown_error',
  INTERNAL_ERROR: 'internal_error',
  
  // 应用错误
  APPLICATION_ERROR: 'application_error',
  APPLICATION: {
    LOAD_FAILED: 'app_load_failed',
    MOUNT_FAILED: 'app_mount_failed',
    UNMOUNT_FAILED: 'app_unmount_failed',
    NOT_FOUND: 'app_not_found',
    ALREADY_EXISTS: 'app_already_exists',
    INVALID_CONFIG: 'app_invalid_config',
    LIFECYCLE_ERROR: 'app_lifecycle_error'
  },
  
  // 沙箱错误
  SANDBOX_ERROR: 'sandbox_error',
  SANDBOX: {
    CREATE_FAILED: 'sandbox_create_failed',
    DESTROY_FAILED: 'sandbox_destroy_failed',
    ISOLATION_FAILED: 'sandbox_isolation_failed',
    PROXY_ERROR: 'sandbox_proxy_error',
    IFRAME_ERROR: 'sandbox_iframe_error',
    WEB_COMPONENT_ERROR: 'sandbox_web_component_error'
  },
  
  // 插件错误
  PLUGIN_ERROR: 'plugin_error',
  PLUGIN: {
    LOAD_FAILED: 'plugin_load_failed',
    REGISTER_FAILED: 'plugin_register_failed',
    EXECUTE_FAILED: 'plugin_execute_failed',
    NOT_FOUND: 'plugin_not_found',
    INVALID_PLUGIN: 'plugin_invalid',
    DEPENDENCY_ERROR: 'plugin_dependency_error'
  },
  
  // 通信错误
  COMMUNICATION_ERROR: 'communication_error',
  COMMUNICATION: {
    SEND_FAILED: 'comm_send_failed',
    RECEIVE_FAILED: 'comm_receive_failed',
    CHANNEL_ERROR: 'comm_channel_error',
    MESSAGE_ERROR: 'comm_message_error',
    TIMEOUT: 'comm_timeout',
    CONNECTION_LOST: 'comm_connection_lost'
  },
  
  // 路由错误
  ROUTER_ERROR: 'router_error',
  ROUTER: {
    NAVIGATION_FAILED: 'router_navigation_failed',
    ROUTE_NOT_FOUND: 'router_route_not_found',
    INVALID_ROUTE: 'router_invalid_route',
    GUARD_ERROR: 'router_guard_error'
  },
  
  // 资源错误
  RESOURCE_ERROR: 'resource_error',
  RESOURCE: {
    LOAD_FAILED: 'resource_load_failed',
    NOT_FOUND: 'resource_not_found',
    INVALID_URL: 'resource_invalid_url',
    CACHE_ERROR: 'resource_cache_error',
    VERSION_MISMATCH: 'resource_version_mismatch'
  },
  
  // 网络错误
  NETWORK_ERROR: 'network_error',
  NETWORK: {
    CONNECTION_FAILED: 'network_connection_failed',
    TIMEOUT: 'network_timeout',
    OFFLINE: 'network_offline',
    DNS_ERROR: 'network_dns_error',
    SSL_ERROR: 'network_ssl_error'
  },
  
  // 配置错误
  CONFIG_ERROR: 'config_error',
  CONFIG: {
    INVALID_CONFIG: 'config_invalid',
    MISSING_CONFIG: 'config_missing',
    PARSE_ERROR: 'config_parse_error',
    VALIDATION_ERROR: 'config_validation_error'
  },
  
  // 权限错误
  PERMISSION_ERROR: 'permission_error',
  PERMISSION: {
    ACCESS_DENIED: 'permission_access_denied',
    UNAUTHORIZED: 'permission_unauthorized',
    FORBIDDEN: 'permission_forbidden',
    TOKEN_EXPIRED: 'permission_token_expired',
    INVALID_TOKEN: 'permission_invalid_token'
  },
  
  // 超时错误
  TIMEOUT_ERROR: 'timeout_error',
  TIMEOUT: {
    REQUEST_TIMEOUT: 'timeout_request',
    RESPONSE_TIMEOUT: 'timeout_response',
    CONNECTION_TIMEOUT: 'timeout_connection',
    OPERATION_TIMEOUT: 'timeout_operation'
  },
  
  // 验证错误
  VALIDATION_ERROR: 'validation_error',
  VALIDATION: {
    INVALID_PARAMETER: 'validation_invalid_parameter',
    MISSING_PARAMETER: 'validation_missing_parameter',
    TYPE_ERROR: 'validation_type_error',
    FORMAT_ERROR: 'validation_format_error',
    RANGE_ERROR: 'validation_range_error'
  },
  
  // 性能错误
  PERFORMANCE_ERROR: 'performance_error',
  PERFORMANCE: {
    MEMORY_LEAK: 'performance_memory_leak',
    CPU_OVERLOAD: 'performance_cpu_overload',
    SLOW_RESPONSE: 'performance_slow_response',
    RESOURCE_EXHAUSTED: 'performance_resource_exhausted'
  },
  
  // 安全错误
  SECURITY_ERROR: 'security_error',
  SECURITY: {
    XSS_DETECTED: 'security_xss_detected',
    CSRF_DETECTED: 'security_csrf_detected',
    INJECTION_DETECTED: 'security_injection_detected',
    UNSAFE_CONTENT: 'security_unsafe_content',
    MALICIOUS_CODE: 'security_malicious_code'
  }
} as const

/**
 * 错误代码类型
 */
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES] | string

/**
 * 错误上下文接口
 */
export interface ErrorContext {
  /** 应用名称 */
  appName?: string
  /** 组件名称 */
  componentName?: string
  /** 操作类型 */
  operation?: string
  /** 用户ID */
  userId?: string
  /** 会话ID */
  sessionId?: string
  /** 请求ID */
  requestId?: string
  /** URL */
  url?: string
  /** 用户代理 */
  userAgent?: string
  /** 时间戳 */
  timestamp?: number
  /** 额外数据 */
  [key: string]: any
}

/**
 * 错误选项接口
 */
export interface ErrorOptions {
  /** 错误上下文 */
  context?: ErrorContext
  /** 错误来源 */
  source?: string
  /** 错误级别 */
  level?: ErrorLevel
  /** 是否可恢复 */
  recoverable?: boolean
  /** 最大重试次数 */
  maxRetries?: number
  /** 原始错误 */
  originalError?: Error
}

/**
 * 错误处理器接口
 */
export interface ErrorHandler {
  /** 处理错误 */
  handle(error: Error): void | Promise<void>
  /** 是否可以处理该错误 */
  canHandle(error: Error): boolean
  /** 处理器优先级 */
  priority: number
}

/**
 * 错误报告接口
 */
export interface ErrorReport {
  /** 错误ID */
  id: string
  /** 错误名称 */
  name: string
  /** 错误消息 */
  message: string
  /** 错误代码 */
  code: string
  /** 错误级别 */
  level: ErrorLevel
  /** 错误时间戳 */
  timestamp: number
  /** 错误来源 */
  source?: string
  /** 错误上下文 */
  context?: ErrorContext
  /** 错误堆栈 */
  stack?: string
  /** 是否可恢复 */
  recoverable: boolean
  /** 重试次数 */
  retryCount: number
  /** 最大重试次数 */
  maxRetries: number
  /** 原始错误 */
  originalError?: {
    name: string
    message: string
    stack?: string
  }
}

/**
 * 错误统计接口
 */
export interface ErrorStats {
  /** 总错误数 */
  total: number
  /** 按级别统计 */
  byLevel: Record<ErrorLevel, number>
  /** 按代码统计 */
  byCode: Record<string, number>
  /** 按来源统计 */
  bySource: Record<string, number>
  /** 最近错误 */
  recent: ErrorReport[]
  /** 统计时间范围 */
  timeRange: {
    start: number
    end: number
  }
}

/**
 * 错误监听器接口
 */
export interface ErrorListener {
  /** 监听器ID */
  id: string
  /** 错误过滤器 */
  filter?: (error: Error) => boolean
  /** 错误处理函数 */
  handler: (error: Error) => void | Promise<void>
  /** 是否只执行一次 */
  once?: boolean
}

/**
 * 错误配置接口
 */
export interface ErrorConfig {
  /** 是否启用错误收集 */
  enabled: boolean
  /** 最大错误数量 */
  maxErrors: number
  /** 错误过期时间（毫秒） */
  expireTime: number
  /** 是否自动重试 */
  autoRetry: boolean
  /** 默认重试次数 */
  defaultRetries: number
  /** 是否上报错误 */
  reportErrors: boolean
  /** 上报URL */
  reportUrl?: string
  /** 忽略的错误代码 */
  ignoredCodes: string[]
  /** 错误处理器 */
  handlers: ErrorHandler[]
}

/**
 * 错误分类枚举
 */
export enum ErrorCategory {
  /** 系统错误 */
  SYSTEM = 'system',
  /** 应用错误 */
  APPLICATION = 'application',
  /** 网络错误 */
  NETWORK = 'network',
  /** 用户错误 */
  USER = 'user',
  /** 配置错误 */
  CONFIG = 'config',
  /** 权限错误 */
  PERMISSION = 'permission',
  /** 资源错误 */
  RESOURCE = 'resource',
  /** 未知错误 */
  UNKNOWN = 'unknown'
}

/**
 * 错误严重程度枚举
 */
export enum ErrorSeverity {
  /** 低 */
  LOW = 1,
  /** 中 */
  MEDIUM = 2,
  /** 高 */
  HIGH = 3,
  /** 严重 */
  CRITICAL = 4,
  /** 致命 */
  FATAL = 5
}

/**
 * 错误状态枚举
 */
export enum ErrorStatus {
  /** 新建 */
  NEW = 'new',
  /** 处理中 */
  PROCESSING = 'processing',
  /** 已解决 */
  RESOLVED = 'resolved',
  /** 已忽略 */
  IGNORED = 'ignored',
  /** 重试中 */
  RETRYING = 'retrying',
  /** 失败 */
  FAILED = 'failed'
}

/**
 * 错误操作枚举
 */
export enum ErrorAction {
  /** 重试 */
  RETRY = 'retry',
  /** 忽略 */
  IGNORE = 'ignore',
  /** 上报 */
  REPORT = 'report',
  /** 回退 */
  FALLBACK = 'fallback',
  /** 重新加载 */
  RELOAD = 'reload',
  /** 重定向 */
  REDIRECT = 'redirect'
}

/**
 * 错误恢复策略接口
 */
export interface ErrorRecoveryStrategy {
  /** 策略名称 */
  name: string
  /** 是否可以恢复该错误 */
  canRecover(error: Error): boolean
  /** 执行恢复 */
  recover(error: Error): Promise<boolean>
  /** 策略优先级 */
  priority: number
}

/**
 * 错误通知接口
 */
export interface ErrorNotification {
  /** 通知ID */
  id: string
  /** 错误信息 */
  error: ErrorReport
  /** 通知类型 */
  type: 'toast' | 'modal' | 'console' | 'email' | 'webhook'
  /** 通知配置 */
  config?: Record<string, any>
  /** 发送时间 */
  sentAt?: number
  /** 是否已发送 */
  sent: boolean
}

/**
 * 错误过滤器接口
 */
export interface ErrorFilter {
  /** 过滤器名称 */
  name: string
  /** 过滤函数 */
  filter: (error: Error) => boolean
  /** 是否启用 */
  enabled: boolean
}

/**
 * 错误转换器接口
 */
export interface ErrorTransformer {
  /** 转换器名称 */
  name: string
  /** 转换函数 */
  transform: (error: Error) => Error
  /** 是否启用 */
  enabled: boolean
}

/**
 * 错误聚合器接口
 */
export interface ErrorAggregator {
  /** 聚合器名称 */
  name: string
  /** 聚合函数 */
  aggregate: (errors: Error[]) => ErrorStats
  /** 聚合间隔（毫秒） */
  interval: number
}

/**
 * 错误队列接口
 */
export interface ErrorQueue {
  /** 队列名称 */
  name: string
  /** 最大队列长度 */
  maxSize: number
  /** 添加错误 */
  enqueue(error: Error): void
  /** 获取错误 */
  dequeue(): Error | undefined
  /** 获取队列大小 */
  size(): number
  /** 清空队列 */
  clear(): void
  /** 是否为空 */
  isEmpty(): boolean
  /** 是否已满 */
  isFull(): boolean
}

/**
 * 错误缓存接口
 */
export interface ErrorCache {
  /** 缓存名称 */
  name: string
  /** 设置错误 */
  set(key: string, error: Error, ttl?: number): void
  /** 获取错误 */
  get(key: string): Error | undefined
  /** 删除错误 */
  delete(key: string): boolean
  /** 清空缓存 */
  clear(): void
  /** 获取所有键 */
  keys(): string[]
  /** 获取缓存大小 */
  size(): number
}

/**
 * 错误日志接口
 */
export interface ErrorLogger {
  /** 记录错误 */
  log(error: Error): void
  /** 记录信息 */
  info(message: string, context?: ErrorContext): void
  /** 记录警告 */
  warn(message: string, context?: ErrorContext): void
  /** 记录错误 */
  error(message: string, context?: ErrorContext): void
  /** 记录致命错误 */
  fatal(message: string, context?: ErrorContext): void
}

/**
 * 错误监控接口
 */
export interface ErrorMonitor {
  /** 开始监控 */
  start(): void
  /** 停止监控 */
  stop(): void
  /** 是否正在监控 */
  isMonitoring(): boolean
  /** 获取错误统计 */
  getStats(): ErrorStats
  /** 重置统计 */
  resetStats(): void
}

/**
 * 错误分析器接口
 */
export interface ErrorAnalyzer {
  /** 分析错误 */
  analyze(error: Error): ErrorReport
  /** 分析错误趋势 */
  analyzeTrend(errors: Error[]): {
    increasing: boolean
    pattern: string
    suggestion: string
  }
  /** 获取错误建议 */
  getSuggestion(error: Error): string
}

/**
 * 错误预测器接口
 */
export interface ErrorPredictor {
  /** 预测错误 */
  predict(context: ErrorContext): {
    probability: number
    possibleErrors: string[]
    preventionSuggestions: string[]
  }
  /** 训练模型 */
  train(errors: Error[]): void
}