/**
 * 通信相关常量定义
 */

/**
 * 通信类型常量
 */
export const COMMUNICATION_TYPE = {
  /** 事件总线 */
  EVENT_BUS: 'eventBus',
  /** 消息通道 */
  MESSAGE_CHANNEL: 'messageChannel',
  /** 共享状态 */
  SHARED_STATE: 'sharedState',
  /** 直接调用 */
  DIRECT_CALL: 'directCall',
  /** WebSocket */
  WEBSOCKET: 'websocket',
  /** PostMessage */
  POST_MESSAGE: 'postMessage'
} as const

/**
 * 消息类型常量
 */
export const MESSAGE_TYPE = {
  /** 请求 */
  REQUEST: 'request',
  /** 响应 */
  RESPONSE: 'response',
  /** 通知 */
  NOTIFICATION: 'notification',
  /** 事件 */
  EVENT: 'event',
  /** 广播 */
  BROADCAST: 'broadcast',
  /** 错误 */
  ERROR: 'error'
} as const

/**
 * 通信状态常量
 */
export const COMMUNICATION_STATUS = {
  /** 未连接 */
  DISCONNECTED: 'DISCONNECTED',
  /** 连接中 */
  CONNECTING: 'CONNECTING',
  /** 已连接 */
  CONNECTED: 'CONNECTED',
  /** 重连中 */
  RECONNECTING: 'RECONNECTING',
  /** 错误状态 */
  ERROR: 'ERROR',
  /** 已关闭 */
  CLOSED: 'CLOSED'
} as const

/**
 * 事件优先级常量
 */
export const EVENT_PRIORITY = {
  /** 紧急 */
  URGENT: 'urgent',
  /** 高优先级 */
  HIGH: 'high',
  /** 普通优先级 */
  NORMAL: 'normal',
  /** 低优先级 */
  LOW: 'low',
  /** 空闲时处理 */
  IDLE: 'idle'
} as const

/**
 * 通信协议常量
 */
export const COMMUNICATION_PROTOCOL = {
  /** HTTP */
  HTTP: 'http',
  /** HTTPS */
  HTTPS: 'https',
  /** WebSocket */
  WS: 'ws',
  /** 安全WebSocket */
  WSS: 'wss',
  /** 自定义协议 */
  CUSTOM: 'custom'
} as const

/**
 * 序列化类型常量
 */
export const SERIALIZATION_TYPE = {
  /** JSON */
  JSON: 'json',
  /** MessagePack */
  MESSAGE_PACK: 'messagePack',
  /** Protocol Buffers */
  PROTOBUF: 'protobuf',
  /** 二进制 */
  BINARY: 'binary',
  /** 字符串 */
  STRING: 'string'
} as const

/**
 * 通信模式常量
 */
export const COMMUNICATION_MODE = {
  /** 同步 */
  SYNC: 'sync',
  /** 异步 */
  ASYNC: 'async',
  /** 单向 */
  ONE_WAY: 'oneWay',
  /** 双向 */
  TWO_WAY: 'twoWay',
  /** 广播 */
  BROADCAST: 'broadcast',
  /** 多播 */
  MULTICAST: 'multicast'
} as const

/**
 * 通信安全级别常量
 */
export const SECURITY_LEVEL = {
  /** 无安全 */
  NONE: 'none',
  /** 基础安全 */
  BASIC: 'basic',
  /** 标准安全 */
  STANDARD: 'standard',
  /** 高级安全 */
  ADVANCED: 'advanced',
  /** 企业级安全 */
  ENTERPRISE: 'enterprise'
} as const

/**
 * 通信错误类型常量
 */
export const COMMUNICATION_ERROR_TYPE = {
  /** 连接失败 */
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  /** 超时 */
  TIMEOUT: 'TIMEOUT',
  /** 序列化失败 */
  SERIALIZATION_FAILED: 'SERIALIZATION_FAILED',
  /** 反序列化失败 */
  DESERIALIZATION_FAILED: 'DESERIALIZATION_FAILED',
  /** 权限拒绝 */
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  /** 消息格式错误 */
  INVALID_MESSAGE_FORMAT: 'INVALID_MESSAGE_FORMAT',
  /** 目标不存在 */
  TARGET_NOT_FOUND: 'TARGET_NOT_FOUND',
  /** 网络错误 */
  NETWORK_ERROR: 'NETWORK_ERROR',
  /** 协议错误 */
  PROTOCOL_ERROR: 'PROTOCOL_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const

/**
 * 通信事件常量
 */
export const COMMUNICATION_EVENTS = {
  /** 连接建立 */
  CONNECTED: 'communication:connected',
  /** 连接断开 */
  DISCONNECTED: 'communication:disconnected',
  /** 消息发送 */
  MESSAGE_SENT: 'communication:messageSent',
  /** 消息接收 */
  MESSAGE_RECEIVED: 'communication:messageReceived',
  /** 错误发生 */
  ERROR: 'communication:error',
  /** 重连开始 */
  RECONNECT_START: 'communication:reconnectStart',
  /** 重连成功 */
  RECONNECT_SUCCESS: 'communication:reconnectSuccess',
  /** 重连失败 */
  RECONNECT_FAILED: 'communication:reconnectFailed',
  /** 性能警告 */
  PERFORMANCE_WARNING: 'communication:performanceWarning'
} as const

/**
 * 通信配置默认值
 */
export const COMMUNICATION_CONFIG_DEFAULTS = {
  /** 默认超时时间（毫秒） */
  TIMEOUT: 5000,
  /** 默认重试次数 */
  RETRY_COUNT: 3,
  /** 默认重试间隔（毫秒） */
  RETRY_INTERVAL: 1000,
  /** 默认心跳间隔（毫秒） */
  HEARTBEAT_INTERVAL: 30000,
  /** 默认最大消息大小（字节） */
  MAX_MESSAGE_SIZE: 1024 * 1024, // 1MB
  /** 默认最大连接数 */
  MAX_CONNECTIONS: 100,
  /** 默认缓冲区大小 */
  BUFFER_SIZE: 1024,
  /** 默认序列化类型 */
  SERIALIZATION: SERIALIZATION_TYPE.JSON,
  /** 默认通信模式 */
  MODE: COMMUNICATION_MODE.ASYNC,
  /** 默认安全级别 */
  SECURITY: SECURITY_LEVEL.STANDARD,
  /** 默认启用压缩 */
  COMPRESSION: false,
  /** 默认启用加密 */
  ENCRYPTION: false
} as const

/**
 * 消息头常量
 */
export const MESSAGE_HEADERS = {
  /** 消息ID */
  MESSAGE_ID: 'messageId',
  /** 消息类型 */
  MESSAGE_TYPE: 'messageType',
  /** 发送者 */
  SENDER: 'sender',
  /** 接收者 */
  RECEIVER: 'receiver',
  /** 时间戳 */
  TIMESTAMP: 'timestamp',
  /** 优先级 */
  PRIORITY: 'priority',
  /** 过期时间 */
  EXPIRES_AT: 'expiresAt',
  /** 重试次数 */
  RETRY_COUNT: 'retryCount',
  /** 内容类型 */
  CONTENT_TYPE: 'contentType',
  /** 内容长度 */
  CONTENT_LENGTH: 'contentLength',
  /** 压缩类型 */
  COMPRESSION: 'compression',
  /** 加密类型 */
  ENCRYPTION: 'encryption',
  /** 签名 */
  SIGNATURE: 'signature',
  /** 版本 */
  VERSION: 'version'
} as const

/**
 * 通信通道常量
 */
export const COMMUNICATION_CHANNELS = {
  /** 默认通道 */
  DEFAULT: 'default',
  /** 系统通道 */
  SYSTEM: 'system',
  /** 用户通道 */
  USER: 'user',
  /** 数据通道 */
  DATA: 'data',
  /** 事件通道 */
  EVENT: 'event',
  /** 控制通道 */
  CONTROL: 'control',
  /** 调试通道 */
  DEBUG: 'debug',
  /** 监控通道 */
  MONITORING: 'monitoring',
  /** 日志通道 */
  LOGGING: 'logging',
  /** 错误通道 */
  ERROR: 'error'
} as const

/**
 * 通信中间件类型常量
 */
export const MIDDLEWARE_TYPE = {
  /** 认证中间件 */
  AUTH: 'auth',
  /** 授权中间件 */
  AUTHORIZATION: 'authorization',
  /** 日志中间件 */
  LOGGING: 'logging',
  /** 监控中间件 */
  MONITORING: 'monitoring',
  /** 缓存中间件 */
  CACHE: 'cache',
  /** 压缩中间件 */
  COMPRESSION: 'compression',
  /** 加密中间件 */
  ENCRYPTION: 'encryption',
  /** 限流中间件 */
  RATE_LIMITING: 'rateLimiting',
  /** 验证中间件 */
  VALIDATION: 'validation',
  /** 转换中间件 */
  TRANSFORMATION: 'transformation'
} as const

/**
 * 通信拦截器类型常量
 */
export const INTERCEPTOR_TYPE = {
  /** 请求拦截器 */
  REQUEST: 'request',
  /** 响应拦截器 */
  RESPONSE: 'response',
  /** 错误拦截器 */
  ERROR: 'error',
  /** 重试拦截器 */
  RETRY: 'retry',
  /** 缓存拦截器 */
  CACHE: 'cache',
  /** 转换拦截器 */
  TRANSFORM: 'transform'
} as const

/**
 * 通信适配器类型常量
 */
export const ADAPTER_TYPE = {
  /** HTTP适配器 */
  HTTP: 'http',
  /** WebSocket适配器 */
  WEBSOCKET: 'websocket',
  /** EventSource适配器 */
  EVENT_SOURCE: 'eventSource',
  /** PostMessage适配器 */
  POST_MESSAGE: 'postMessage',
  /** BroadcastChannel适配器 */
  BROADCAST_CHANNEL: 'broadcastChannel',
  /** SharedWorker适配器 */
  SHARED_WORKER: 'sharedWorker',
  /** ServiceWorker适配器 */
  SERVICE_WORKER: 'serviceWorker',
  /** 自定义适配器 */
  CUSTOM: 'custom'
} as const

/**
 * 通信负载均衡策略常量
 */
export const LOAD_BALANCE_STRATEGY = {
  /** 轮询 */
  ROUND_ROBIN: 'roundRobin',
  /** 随机 */
  RANDOM: 'random',
  /** 最少连接 */
  LEAST_CONNECTIONS: 'leastConnections',
  /** 加权轮询 */
  WEIGHTED_ROUND_ROBIN: 'weightedRoundRobin',
  /** 加权随机 */
  WEIGHTED_RANDOM: 'weightedRandom',
  /** IP哈希 */
  IP_HASH: 'ipHash',
  /** 一致性哈希 */
  CONSISTENT_HASH: 'consistentHash'
} as const

/**
 * 通信重试策略常量
 */
export const RETRY_STRATEGY = {
  /** 固定间隔 */
  FIXED_INTERVAL: 'fixedInterval',
  /** 指数退避 */
  EXPONENTIAL_BACKOFF: 'exponentialBackoff',
  /** 线性退避 */
  LINEAR_BACKOFF: 'linearBackoff',
  /** 随机退避 */
  RANDOM_BACKOFF: 'randomBackoff',
  /** 自定义退避 */
  CUSTOM_BACKOFF: 'customBackoff'
} as const

/**
 * 通信缓存策略常量
 */
export const CACHE_STRATEGY = {
  /** 无缓存 */
  NO_CACHE: 'noCache',
  /** 内存缓存 */
  MEMORY_CACHE: 'memoryCache',
  /** 本地存储缓存 */
  LOCAL_STORAGE_CACHE: 'localStorageCache',
  /** 会话存储缓存 */
  SESSION_STORAGE_CACHE: 'sessionStorageCache',
  /** IndexedDB缓存 */
  INDEXED_DB_CACHE: 'indexedDBCache',
  /** 自定义缓存 */
  CUSTOM_CACHE: 'customCache'
} as const

/**
 * 通信压缩算法常量
 */
export const COMPRESSION_ALGORITHM = {
  /** 无压缩 */
  NONE: 'none',
  /** GZIP */
  GZIP: 'gzip',
  /** Deflate */
  DEFLATE: 'deflate',
  /** Brotli */
  BROTLI: 'brotli',
  /** LZ4 */
  LZ4: 'lz4',
  /** Snappy */
  SNAPPY: 'snappy'
} as const

/**
 * 通信加密算法常量
 */
export const ENCRYPTION_ALGORITHM = {
  /** 无加密 */
  NONE: 'none',
  /** AES */
  AES: 'aes',
  /** RSA */
  RSA: 'rsa',
  /** ChaCha20 */
  CHACHA20: 'chacha20',
  /** Salsa20 */
  SALSA20: 'salsa20'
} as const

/**
 * 通信监控指标常量
 */
export const COMMUNICATION_METRICS = {
  /** 连接数 */
  CONNECTION_COUNT: 'connectionCount',
  /** 消息发送数 */
  MESSAGES_SENT: 'messagesSent',
  /** 消息接收数 */
  MESSAGES_RECEIVED: 'messagesReceived',
  /** 错误数 */
  ERROR_COUNT: 'errorCount',
  /** 平均响应时间 */
  AVERAGE_RESPONSE_TIME: 'averageResponseTime',
  /** 吞吐量 */
  THROUGHPUT: 'throughput',
  /** 带宽使用 */
  BANDWIDTH_USAGE: 'bandwidthUsage',
  /** 重连次数 */
  RECONNECT_COUNT: 'reconnectCount',
  /** 超时次数 */
  TIMEOUT_COUNT: 'timeoutCount',
  /** 成功率 */
  SUCCESS_RATE: 'successRate'
} as const

/**
 * 通信性能阈值常量
 */
export const COMMUNICATION_PERFORMANCE_THRESHOLDS = {
  /** 响应时间警告阈值（毫秒） */
  RESPONSE_TIME_WARNING: 1000,
  /** 响应时间错误阈值（毫秒） */
  RESPONSE_TIME_ERROR: 5000,
  /** 错误率警告阈值（百分比） */
  ERROR_RATE_WARNING: 5,
  /** 错误率错误阈值（百分比） */
  ERROR_RATE_ERROR: 10,
  /** 连接数警告阈值 */
  CONNECTION_WARNING: 80,
  /** 连接数错误阈值 */
  CONNECTION_ERROR: 100,
  /** 带宽使用警告阈值（MB/s） */
  BANDWIDTH_WARNING: 10,
  /** 带宽使用错误阈值（MB/s） */
  BANDWIDTH_ERROR: 50,
  /** 消息队列长度警告阈值 */
  QUEUE_LENGTH_WARNING: 1000,
  /** 消息队列长度错误阈值 */
  QUEUE_LENGTH_ERROR: 5000
} as const

/**
 * 通信调试常量
 */
export const COMMUNICATION_DEBUG = {
  /** 调试模式环境变量 */
  ENV_VAR: 'MICRO_COMMUNICATION_DEBUG',
  /** 调试日志前缀 */
  LOG_PREFIX: '[MicroCommunication]',
  /** 调试面板ID */
  PANEL_ID: 'micro-communication-debug-panel',
  /** 调试工具类名 */
  TOOLS_CLASS: 'micro-communication-debug-tools',
  /** 调试事件前缀 */
  EVENT_PREFIX: 'micro-communication:debug:'
} as const

/**
 * 通信兼容性常量
 */
export const COMMUNICATION_COMPATIBILITY = {
  /** 支持的浏览器 */
  SUPPORTED_BROWSERS: {
    CHROME: 50,
    FIREFOX: 45,
    SAFARI: 10,
    EDGE: 79,
    IE: 11
  },
  /** 必需的特性 */
  REQUIRED_FEATURES: [
    'Promise',
    'fetch',
    'WebSocket',
    'postMessage',
    'addEventListener'
  ],
  /** 可选的特性 */
  OPTIONAL_FEATURES: [
    'BroadcastChannel',
    'SharedWorker',
    'ServiceWorker',
    'EventSource',
    'MessageChannel'
  ]
} as const

/**
 * 通信限制常量
 */
export const COMMUNICATION_LIMITS = {
  /** 最大消息大小（字节） */
  MAX_MESSAGE_SIZE: 10 * 1024 * 1024, // 10MB
  /** 最大连接数 */
  MAX_CONNECTIONS: 1000,
  /** 最大队列长度 */
  MAX_QUEUE_LENGTH: 10000,
  /** 最大重试次数 */
  MAX_RETRY_COUNT: 10,
  /** 最大超时时间（毫秒） */
  MAX_TIMEOUT: 60000,
  /** 最大心跳间隔（毫秒） */
  MAX_HEARTBEAT_INTERVAL: 300000,
  /** 最大缓存大小（字节） */
  MAX_CACHE_SIZE: 100 * 1024 * 1024, // 100MB
  /** 最大并发请求数 */
  MAX_CONCURRENT_REQUESTS: 100
} as const