/**
 * 沙箱相关常量定义
 */

/**
 * 沙箱类型常量
 */
export const SANDBOX_TYPE = {
  /** Proxy沙箱 */
  PROXY: 'proxy',
  /** DefineProperty沙箱 */
  DEFINE_PROPERTY: 'defineProperty',
  /** iframe沙箱 */
  IFRAME: 'iframe',
  /** WebComponent沙箱 */
  WEB_COMPONENT: 'webComponent',
  /** Namespace沙箱 */
  NAMESPACE: 'namespace',
  /** Federation沙箱 */
  FEDERATION: 'federation'
} as const

/**
 * 沙箱状态常量
 */
export const SANDBOX_STATUS = {
  /** 未创建 */
  NOT_CREATED: 'NOT_CREATED',
  /** 创建中 */
  CREATING: 'CREATING',
  /** 已创建 */
  CREATED: 'CREATED',
  /** 激活中 */
  ACTIVATING: 'ACTIVATING',
  /** 已激活 */
  ACTIVE: 'ACTIVE',
  /** 失活中 */
  DEACTIVATING: 'DEACTIVATING',
  /** 已失活 */
  INACTIVE: 'INACTIVE',
  /** 销毁中 */
  DESTROYING: 'DESTROYING',
  /** 已销毁 */
  DESTROYED: 'DESTROYED',
  /** 错误状态 */
  ERROR: 'ERROR'
} as const

/**
 * 隔离级别常量
 */
export const ISOLATION_LEVEL = {
  /** 无隔离 */
  NONE: 'none',
  /** 基础隔离 */
  BASIC: 'basic',
  /** 标准隔离 */
  STANDARD: 'standard',
  /** 严格隔离 */
  STRICT: 'strict',
  /** 完全隔离 */
  COMPLETE: 'complete'
} as const

/**
 * 沙箱策略常量
 */
export const SANDBOX_STRATEGY = {
  /** 单实例策略 */
  SINGLETON: 'singleton',
  /** 多实例策略 */
  MULTIPLE: 'multiple',
  /** 共享策略 */
  SHARED: 'shared',
  /** 独立策略 */
  ISOLATED: 'isolated'
} as const

/**
 * 全局变量白名单
 */
export const GLOBAL_WHITELIST = [
  // 基础对象
  'Object',
  'Array',
  'String',
  'Number',
  'Boolean',
  'Date',
  'RegExp',
  'Error',
  'Promise',
  'Symbol',
  'Map',
  'Set',
  'WeakMap',
  'WeakSet',
  'Proxy',
  'Reflect',
  
  // 函数对象
  'Function',
  'parseInt',
  'parseFloat',
  'isNaN',
  'isFinite',
  'encodeURI',
  'encodeURIComponent',
  'decodeURI',
  'decodeURIComponent',
  
  // JSON对象
  'JSON',
  
  // 数学对象
  'Math',
  
  // 控制台对象
  'console',
  
  // 定时器
  'setTimeout',
  'clearTimeout',
  'setInterval',
  'clearInterval',
  'requestAnimationFrame',
  'cancelAnimationFrame',
  
  // 浏览器API
  'fetch',
  'XMLHttpRequest',
  'WebSocket',
  'EventSource',
  'URL',
  'URLSearchParams',
  'Blob',
  'File',
  'FileReader',
  'FormData',
  'Headers',
  'Request',
  'Response',
  
  // 存储API
  'localStorage',
  'sessionStorage',
  
  // 事件相关
  'Event',
  'CustomEvent',
  'EventTarget',
  'addEventListener',
  'removeEventListener',
  'dispatchEvent',
  
  // DOM相关（在浏览器环境中）
  'document',
  'window',
  'navigator',
  'location',
  'history',
  'screen',
  
  // 微前端相关
  '__MICRO_APP__',
  '__MICRO_CORE__'
] as const

/**
 * 全局变量黑名单
 */
export const GLOBAL_BLACKLIST = [
  // 危险的全局方法
  'eval',
  'Function',
  
  // 系统相关
  'process',
  'global',
  'Buffer',
  'require',
  'module',
  'exports',
  '__dirname',
  '__filename',
  
  // Node.js相关
  'fs',
  'path',
  'os',
  'crypto',
  'child_process',
  
  // 浏览器敏感API
  'alert',
  'confirm',
  'prompt',
  'open',
  'close',
  'print',
  
  // 调试相关
  'debugger'
] as const

/**
 * CSS隔离策略常量
 */
export const CSS_ISOLATION_STRATEGY = {
  /** 无隔离 */
  NONE: 'none',
  /** 作用域隔离 */
  SCOPED: 'scoped',
  /** Shadow DOM隔离 */
  SHADOW_DOM: 'shadowDOM',
  /** 命名空间隔离 */
  NAMESPACE: 'namespace',
  /** 前缀隔离 */
  PREFIX: 'prefix'
} as const

/**
 * JavaScript隔离策略常量
 */
export const JS_ISOLATION_STRATEGY = {
  /** 无隔离 */
  NONE: 'none',
  /** Proxy隔离 */
  PROXY: 'proxy',
  /** 快照隔离 */
  SNAPSHOT: 'snapshot',
  /** iframe隔离 */
  IFRAME: 'iframe',
  /** Web Worker隔离 */
  WEB_WORKER: 'webWorker'
} as const

/**
 * 沙箱事件常量
 */
export const SANDBOX_EVENTS = {
  /** 沙箱创建 */
  CREATED: 'sandbox:created',
  /** 沙箱激活 */
  ACTIVATED: 'sandbox:activated',
  /** 沙箱失活 */
  DEACTIVATED: 'sandbox:deactivated',
  /** 沙箱销毁 */
  DESTROYED: 'sandbox:destroyed',
  /** 沙箱错误 */
  ERROR: 'sandbox:error',
  /** 全局变量访问 */
  GLOBAL_ACCESS: 'sandbox:globalAccess',
  /** 全局变量修改 */
  GLOBAL_MODIFY: 'sandbox:globalModify',
  /** 内存泄漏检测 */
  MEMORY_LEAK: 'sandbox:memoryLeak',
  /** 性能警告 */
  PERFORMANCE_WARNING: 'sandbox:performanceWarning'
} as const

/**
 * 沙箱配置默认值
 */
export const SANDBOX_CONFIG_DEFAULTS = {
  /** 默认沙箱类型 */
  TYPE: SANDBOX_TYPE.PROXY,
  /** 默认隔离级别 */
  ISOLATION_LEVEL: ISOLATION_LEVEL.STANDARD,
  /** 默认CSS隔离策略 */
  CSS_ISOLATION: CSS_ISOLATION_STRATEGY.SCOPED,
  /** 默认JS隔离策略 */
  JS_ISOLATION: JS_ISOLATION_STRATEGY.PROXY,
  /** 默认启用严格模式 */
  STRICT_MODE: true,
  /** 默认启用内存泄漏检测 */
  MEMORY_LEAK_DETECTION: true,
  /** 默认启用性能监控 */
  PERFORMANCE_MONITORING: true,
  /** 默认超时时间（毫秒） */
  TIMEOUT: 30000,
  /** 默认最大内存使用量（MB） */
  MAX_MEMORY_USAGE: 100,
  /** 默认最大执行时间（毫秒） */
  MAX_EXECUTION_TIME: 5000
} as const

/**
 * 沙箱权限常量
 */
export const SANDBOX_PERMISSIONS = {
  /** DOM访问权限 */
  DOM_ACCESS: 'dom:access',
  /** DOM修改权限 */
  DOM_MODIFY: 'dom:modify',
  /** 网络请求权限 */
  NETWORK_REQUEST: 'network:request',
  /** 存储访问权限 */
  STORAGE_ACCESS: 'storage:access',
  /** 存储修改权限 */
  STORAGE_MODIFY: 'storage:modify',
  /** 定时器权限 */
  TIMER: 'timer',
  /** 事件监听权限 */
  EVENT_LISTENER: 'event:listener',
  /** 全局变量访问权限 */
  GLOBAL_ACCESS: 'global:access',
  /** 全局变量修改权限 */
  GLOBAL_MODIFY: 'global:modify',
  /** 脚本执行权限 */
  SCRIPT_EXECUTION: 'script:execution',
  /** 模块加载权限 */
  MODULE_LOADING: 'module:loading'
} as const

/**
 * 沙箱资源限制常量
 */
export const SANDBOX_LIMITS = {
  /** 最大内存使用量（字节） */
  MAX_MEMORY: 100 * 1024 * 1024, // 100MB
  /** 最大CPU使用时间（毫秒） */
  MAX_CPU_TIME: 5000,
  /** 最大网络请求数 */
  MAX_NETWORK_REQUESTS: 100,
  /** 最大DOM节点数 */
  MAX_DOM_NODES: 10000,
  /** 最大事件监听器数 */
  MAX_EVENT_LISTENERS: 1000,
  /** 最大定时器数 */
  MAX_TIMERS: 100,
  /** 最大存储大小（字节） */
  MAX_STORAGE_SIZE: 10 * 1024 * 1024, // 10MB
  /** 最大文件大小（字节） */
  MAX_FILE_SIZE: 50 * 1024 * 1024 // 50MB
} as const

/**
 * 沙箱监控指标常量
 */
export const SANDBOX_METRICS = {
  /** 内存使用量 */
  MEMORY_USAGE: 'memoryUsage',
  /** CPU使用率 */
  CPU_USAGE: 'cpuUsage',
  /** 网络请求数 */
  NETWORK_REQUESTS: 'networkRequests',
  /** DOM操作数 */
  DOM_OPERATIONS: 'domOperations',
  /** 事件触发数 */
  EVENT_TRIGGERS: 'eventTriggers',
  /** 定时器数量 */
  TIMER_COUNT: 'timerCount',
  /** 存储使用量 */
  STORAGE_USAGE: 'storageUsage',
  /** 错误数量 */
  ERROR_COUNT: 'errorCount',
  /** 警告数量 */
  WARNING_COUNT: 'warningCount',
  /** 执行时间 */
  EXECUTION_TIME: 'executionTime'
} as const

/**
 * 沙箱错误类型常量
 */
export const SANDBOX_ERROR_TYPES = {
  /** 创建失败 */
  CREATE_FAILED: 'CREATE_FAILED',
  /** 激活失败 */
  ACTIVATE_FAILED: 'ACTIVATE_FAILED',
  /** 失活失败 */
  DEACTIVATE_FAILED: 'DEACTIVATE_FAILED',
  /** 销毁失败 */
  DESTROY_FAILED: 'DESTROY_FAILED',
  /** 权限违规 */
  PERMISSION_VIOLATION: 'PERMISSION_VIOLATION',
  /** 资源超限 */
  RESOURCE_EXCEEDED: 'RESOURCE_EXCEEDED',
  /** 内存泄漏 */
  MEMORY_LEAK: 'MEMORY_LEAK',
  /** 执行超时 */
  EXECUTION_TIMEOUT: 'EXECUTION_TIMEOUT',
  /** 隔离失败 */
  ISOLATION_FAILED: 'ISOLATION_FAILED',
  /** 不支持的操作 */
  UNSUPPORTED_OPERATION: 'UNSUPPORTED_OPERATION'
} as const

/**
 * 沙箱调试常量
 */
export const SANDBOX_DEBUG = {
  /** 调试模式环境变量 */
  ENV_VAR: 'MICRO_SANDBOX_DEBUG',
  /** 调试日志前缀 */
  LOG_PREFIX: '[MicroSandbox]',
  /** 调试面板ID */
  PANEL_ID: 'micro-sandbox-debug-panel',
  /** 调试工具类名 */
  TOOLS_CLASS: 'micro-sandbox-debug-tools',
  /** 调试事件前缀 */
  EVENT_PREFIX: 'micro-sandbox:debug:'
} as const

/**
 * 沙箱性能阈值常量
 */
export const SANDBOX_PERFORMANCE_THRESHOLDS = {
  /** 内存使用警告阈值（字节） */
  MEMORY_WARNING: 50 * 1024 * 1024, // 50MB
  /** 内存使用错误阈值（字节） */
  MEMORY_ERROR: 80 * 1024 * 1024, // 80MB
  /** CPU使用警告阈值（毫秒） */
  CPU_WARNING: 3000,
  /** CPU使用错误阈值（毫秒） */
  CPU_ERROR: 5000,
  /** 网络请求警告阈值 */
  NETWORK_WARNING: 50,
  /** 网络请求错误阈值 */
  NETWORK_ERROR: 100,
  /** DOM操作警告阈值 */
  DOM_WARNING: 1000,
  /** DOM操作错误阈值 */
  DOM_ERROR: 5000
} as const

/**
 * 沙箱清理策略常量
 */
export const SANDBOX_CLEANUP_STRATEGY = {
  /** 立即清理 */
  IMMEDIATE: 'immediate',
  /** 延迟清理 */
  DELAYED: 'delayed',
  /** 批量清理 */
  BATCH: 'batch',
  /** 按需清理 */
  ON_DEMAND: 'onDemand',
  /** 自动清理 */
  AUTO: 'auto'
} as const

/**
 * 沙箱兼容性常量
 */
export const SANDBOX_COMPATIBILITY = {
  /** 支持的浏览器 */
  SUPPORTED_BROWSERS: {
    CHROME: 60,
    FIREFOX: 55,
    SAFARI: 12,
    EDGE: 79,
    IE: 11
  },
  /** 必需的特性 */
  REQUIRED_FEATURES: [
    'Proxy',
    'WeakMap',
    'WeakSet',
    'Symbol',
    'Promise',
    'MutationObserver'
  ],
  /** 可选的特性 */
  OPTIONAL_FEATURES: [
    'ShadowDOM',
    'CustomElements',
    'WebComponents',
    'ServiceWorker',
    'WebWorker'
  ]
} as const

/**
 * 沙箱安全策略常量
 */
export const SANDBOX_SECURITY = {
  /** 内容安全策略 */
  CSP: {
    /** 默认源 */
    DEFAULT_SRC: "'self'",
    /** 脚本源 */
    SCRIPT_SRC: "'self' 'unsafe-inline'",
    /** 样式源 */
    STYLE_SRC: "'self' 'unsafe-inline'",
    /** 对象源 */
    OBJECT_SRC: "'none'",
    /** 基础URI */
    BASE_URI: "'self'",
    /** 表单动作 */
    FORM_ACTION: "'self'"
  },
  /** 特性策略 */
  FEATURE_POLICY: {
    /** 相机 */
    CAMERA: "'none'",
    /** 麦克风 */
    MICROPHONE: "'none'",
    /** 地理位置 */
    GEOLOCATION: "'none'",
    /** 通知 */
    NOTIFICATIONS: "'none'",
    /** 支付 */
    PAYMENT: "'none'"
  },
  /** 权限策略 */
  PERMISSIONS_POLICY: {
    /** 相机 */
    CAMERA: '()',
    /** 麦克风 */
    MICROPHONE: '()',
    /** 地理位置 */
    GEOLOCATION: '()',
    /** 全屏 */
    FULLSCREEN: '(self)'
  }
} as const

/**
 * 沙箱通信协议常量
 */
export const SANDBOX_COMMUNICATION = {
  /** 消息类型 */
  MESSAGE_TYPES: {
    /** 初始化 */
    INIT: 'init',
    /** 执行 */
    EXECUTE: 'execute',
    /** 响应 */
    RESPONSE: 'response',
    /** 错误 */
    ERROR: 'error',
    /** 销毁 */
    DESTROY: 'destroy'
  },
  /** 通信通道 */
  CHANNELS: {
    /** 主通道 */
    MAIN: 'main',
    /** 数据通道 */
    DATA: 'data',
    /** 事件通道 */
    EVENT: 'event',
    /** 调试通道 */
    DEBUG: 'debug'
  },
  /** 消息优先级 */
  PRIORITY: {
    /** 高优先级 */
    HIGH: 'high',
    /** 普通优先级 */
    NORMAL: 'normal',
    /** 低优先级 */
    LOW: 'low'
  }
} as const