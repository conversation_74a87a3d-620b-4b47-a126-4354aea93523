/**
 * 应用相关常量定义
 */

/**
 * 应用状态常量
 */
export const APP_STATUS = {
  /** 未加载 */
  NOT_LOADED: 'NOT_LOADED',
  /** 加载中 */
  LOADING: 'LOADING',
  /** 已加载 */
  LOADED: 'LOADED',
  /** 启动中 */
  BOOTSTRAPPING: 'BOOTSTRAPPING',
  /** 已启动 */
  BOOTSTRAPPED: 'BOOTSTRAPPED',
  /** 挂载中 */
  MOUNTING: 'MOUNTING',
  /** 已挂载 */
  MOUNTED: 'MOUNTED',
  /** 卸载中 */
  UNMOUNTING: 'UNMOUNTING',
  /** 已卸载 */
  UNMOUNTED: 'UNMOUNTED',
  /** 错误状态 */
  ERROR: 'ERROR',
  /** 跳过状态 */
  SKIP: 'SKIP'
} as const

/**
 * 应用类型常量
 */
export const APP_TYPE = {
  /** 基座应用 */
  MAIN: 'main',
  /** 子应用 */
  SUB: 'sub',
  /** 微件应用 */
  WIDGET: 'widget',
  /** 插件应用 */
  PLUGIN: 'plugin'
} as const

/**
 * 应用加载模式常量
 */
export const LOAD_MODE = {
  /** 预加载 */
  PRELOAD: 'preload',
  /** 懒加载 */
  LAZY: 'lazy',
  /** 按需加载 */
  ON_DEMAND: 'on-demand',
  /** 立即加载 */
  IMMEDIATE: 'immediate'
} as const

/**
 * 应用优先级常量
 */
export const APP_PRIORITY = {
  /** 最高优先级 */
  HIGHEST: 100,
  /** 高优先级 */
  HIGH: 75,
  /** 普通优先级 */
  NORMAL: 50,
  /** 低优先级 */
  LOW: 25,
  /** 最低优先级 */
  LOWEST: 0
} as const

/**
 * 应用生命周期钩子常量
 */
export const LIFECYCLE_HOOKS = {
  /** 加载前 */
  BEFORE_LOAD: 'beforeLoad',
  /** 加载后 */
  AFTER_LOAD: 'afterLoad',
  /** 启动前 */
  BEFORE_BOOTSTRAP: 'beforeBootstrap',
  /** 启动后 */
  AFTER_BOOTSTRAP: 'afterBootstrap',
  /** 挂载前 */
  BEFORE_MOUNT: 'beforeMount',
  /** 挂载后 */
  AFTER_MOUNT: 'afterMount',
  /** 卸载前 */
  BEFORE_UNMOUNT: 'beforeUnmount',
  /** 卸载后 */
  AFTER_UNMOUNT: 'afterUnmount',
  /** 更新前 */
  BEFORE_UPDATE: 'beforeUpdate',
  /** 更新后 */
  AFTER_UPDATE: 'afterUpdate',
  /** 错误处理 */
  ON_ERROR: 'onError'
} as const

/**
 * 应用配置默认值
 */
export const APP_CONFIG_DEFAULTS = {
  /** 默认超时时间（毫秒） */
  TIMEOUT: 30000,
  /** 默认重试次数 */
  MAX_RETRIES: 3,
  /** 默认缓存时间（毫秒） */
  CACHE_TTL: 300000,
  /** 默认预加载延迟（毫秒） */
  PRELOAD_DELAY: 1000,
  /** 默认沙箱类型 */
  SANDBOX_TYPE: 'proxy',
  /** 默认CSS隔离 */
  CSS_ISOLATION: true,
  /** 默认JS隔离 */
  JS_ISOLATION: true,
  /** 默认全局变量隔离 */
  GLOBAL_ISOLATION: true
} as const

/**
 * 应用事件常量
 */
export const APP_EVENTS = {
  /** 应用注册 */
  REGISTERED: 'app:registered',
  /** 应用注销 */
  UNREGISTERED: 'app:unregistered',
  /** 应用加载开始 */
  LOAD_START: 'app:loadStart',
  /** 应用加载完成 */
  LOAD_END: 'app:loadEnd',
  /** 应用启动开始 */
  BOOTSTRAP_START: 'app:bootstrapStart',
  /** 应用启动完成 */
  BOOTSTRAP_END: 'app:bootstrapEnd',
  /** 应用挂载开始 */
  MOUNT_START: 'app:mountStart',
  /** 应用挂载完成 */
  MOUNT_END: 'app:mountEnd',
  /** 应用卸载开始 */
  UNMOUNT_START: 'app:unmountStart',
  /** 应用卸载完成 */
  UNMOUNT_END: 'app:unmountEnd',
  /** 应用状态变更 */
  STATUS_CHANGED: 'app:statusChanged',
  /** 应用错误 */
  ERROR: 'app:error',
  /** 应用激活 */
  ACTIVATED: 'app:activated',
  /** 应用失活 */
  DEACTIVATED: 'app:deactivated'
} as const

/**
 * 应用资源类型常量
 */
export const RESOURCE_TYPE = {
  /** JavaScript文件 */
  SCRIPT: 'script',
  /** CSS文件 */
  STYLE: 'style',
  /** HTML文件 */
  HTML: 'html',
  /** JSON文件 */
  JSON: 'json',
  /** 图片文件 */
  IMAGE: 'image',
  /** 字体文件 */
  FONT: 'font',
  /** 其他文件 */
  OTHER: 'other'
} as const

/**
 * 应用容器选择器常量
 */
export const CONTAINER_SELECTORS = {
  /** 默认容器ID */
  DEFAULT_ID: 'micro-app-container',
  /** 默认容器类名 */
  DEFAULT_CLASS: 'micro-app-wrapper',
  /** 容器属性前缀 */
  ATTR_PREFIX: 'data-micro-app',
  /** 应用名称属性 */
  APP_NAME_ATTR: 'data-micro-app-name',
  /** 应用状态属性 */
  APP_STATUS_ATTR: 'data-micro-app-status'
} as const

/**
 * 应用路由常量
 */
export const ROUTE_CONFIG = {
  /** 默认路由模式 */
  DEFAULT_MODE: 'hash',
  /** 路由参数分隔符 */
  PARAM_SEPARATOR: '/',
  /** 查询参数分隔符 */
  QUERY_SEPARATOR: '?',
  /** 哈希分隔符 */
  HASH_SEPARATOR: '#',
  /** 路由通配符 */
  WILDCARD: '*',
  /** 路由参数前缀 */
  PARAM_PREFIX: ':'
} as const

/**
 * 应用通信常量
 */
export const COMMUNICATION = {
  /** 默认通信通道 */
  DEFAULT_CHANNEL: 'micro-app-channel',
  /** 消息类型前缀 */
  MESSAGE_PREFIX: 'micro-app:',
  /** 广播消息类型 */
  BROADCAST_TYPE: 'broadcast',
  /** 点对点消息类型 */
  P2P_TYPE: 'p2p',
  /** 请求消息类型 */
  REQUEST_TYPE: 'request',
  /** 响应消息类型 */
  RESPONSE_TYPE: 'response'
} as const

/**
 * 应用性能指标常量
 */
export const PERFORMANCE_METRICS = {
  /** 加载时间 */
  LOAD_TIME: 'loadTime',
  /** 启动时间 */
  BOOTSTRAP_TIME: 'bootstrapTime',
  /** 挂载时间 */
  MOUNT_TIME: 'mountTime',
  /** 首屏渲染时间 */
  FIRST_PAINT: 'firstPaint',
  /** 首次内容绘制时间 */
  FIRST_CONTENTFUL_PAINT: 'firstContentfulPaint',
  /** 最大内容绘制时间 */
  LARGEST_CONTENTFUL_PAINT: 'largestContentfulPaint',
  /** 首次输入延迟 */
  FIRST_INPUT_DELAY: 'firstInputDelay',
  /** 累积布局偏移 */
  CUMULATIVE_LAYOUT_SHIFT: 'cumulativeLayoutShift',
  /** 内存使用量 */
  MEMORY_USAGE: 'memoryUsage',
  /** 包大小 */
  BUNDLE_SIZE: 'bundleSize'
} as const

/**
 * 应用缓存策略常量
 */
export const CACHE_STRATEGY = {
  /** 无缓存 */
  NO_CACHE: 'no-cache',
  /** 内存缓存 */
  MEMORY: 'memory',
  /** 本地存储缓存 */
  LOCAL_STORAGE: 'localStorage',
  /** 会话存储缓存 */
  SESSION_STORAGE: 'sessionStorage',
  /** IndexedDB缓存 */
  INDEXED_DB: 'indexedDB',
  /** 浏览器缓存 */
  BROWSER: 'browser'
} as const

/**
 * 应用错误类型常量
 */
export const ERROR_TYPES = {
  /** 加载错误 */
  LOAD_ERROR: 'LOAD_ERROR',
  /** 启动错误 */
  BOOTSTRAP_ERROR: 'BOOTSTRAP_ERROR',
  /** 挂载错误 */
  MOUNT_ERROR: 'MOUNT_ERROR',
  /** 卸载错误 */
  UNMOUNT_ERROR: 'UNMOUNT_ERROR',
  /** 运行时错误 */
  RUNTIME_ERROR: 'RUNTIME_ERROR',
  /** 网络错误 */
  NETWORK_ERROR: 'NETWORK_ERROR',
  /** 配置错误 */
  CONFIG_ERROR: 'CONFIG_ERROR',
  /** 依赖错误 */
  DEPENDENCY_ERROR: 'DEPENDENCY_ERROR'
} as const

/**
 * 应用版本管理常量
 */
export const VERSION_MANAGEMENT = {
  /** 版本号分隔符 */
  VERSION_SEPARATOR: '.',
  /** 预发布版本标识 */
  PRERELEASE_IDENTIFIER: '-',
  /** 构建版本标识 */
  BUILD_IDENTIFIER: '+',
  /** 默认版本号 */
  DEFAULT_VERSION: '1.0.0',
  /** 版本比较结果 */
  COMPARE_RESULT: {
    LESS: -1,
    EQUAL: 0,
    GREATER: 1
  }
} as const

/**
 * 应用依赖管理常量
 */
export const DEPENDENCY_MANAGEMENT = {
  /** 依赖类型 */
  TYPES: {
    /** 运行时依赖 */
    RUNTIME: 'runtime',
    /** 开发依赖 */
    DEV: 'dev',
    /** 可选依赖 */
    OPTIONAL: 'optional',
    /** 对等依赖 */
    PEER: 'peer'
  },
  /** 依赖解析策略 */
  RESOLUTION_STRATEGY: {
    /** 严格模式 */
    STRICT: 'strict',
    /** 宽松模式 */
    LOOSE: 'loose',
    /** 自动模式 */
    AUTO: 'auto'
  }
} as const

/**
 * 应用安全常量
 */
export const SECURITY = {
  /** 内容安全策略 */
  CSP: {
    /** 默认源 */
    DEFAULT_SRC: "'self'",
    /** 脚本源 */
    SCRIPT_SRC: "'self' 'unsafe-inline' 'unsafe-eval'",
    /** 样式源 */
    STYLE_SRC: "'self' 'unsafe-inline'",
    /** 图片源 */
    IMG_SRC: "'self' data: blob:",
    /** 字体源 */
    FONT_SRC: "'self' data:",
    /** 连接源 */
    CONNECT_SRC: "'self'"
  },
  /** 权限策略 */
  PERMISSIONS: {
    /** 相机权限 */
    CAMERA: 'camera',
    /** 麦克风权限 */
    MICROPHONE: 'microphone',
    /** 地理位置权限 */
    GEOLOCATION: 'geolocation',
    /** 通知权限 */
    NOTIFICATIONS: 'notifications',
    /** 全屏权限 */
    FULLSCREEN: 'fullscreen'
  }
} as const

/**
 * 应用国际化常量
 */
export const I18N = {
  /** 默认语言 */
  DEFAULT_LOCALE: 'zh-CN',
  /** 支持的语言 */
  SUPPORTED_LOCALES: ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'],
  /** 语言文件扩展名 */
  FILE_EXTENSION: '.json',
  /** 语言文件目录 */
  LOCALE_DIR: 'locales',
  /** 命名空间分隔符 */
  NAMESPACE_SEPARATOR: ':',
  /** 插值分隔符 */
  INTERPOLATION_PREFIX: '{{',
  INTERPOLATION_SUFFIX: '}}'
} as const

/**
 * 应用主题常量
 */
export const THEME = {
  /** 默认主题 */
  DEFAULT: 'default',
  /** 深色主题 */
  DARK: 'dark',
  /** 浅色主题 */
  LIGHT: 'light',
  /** 主题变量前缀 */
  CSS_VAR_PREFIX: '--micro-app-',
  /** 主题类名前缀 */
  CLASS_PREFIX: 'micro-app-theme-'
} as const

/**
 * 应用调试常量
 */
export const DEBUG = {
  /** 调试模式环境变量 */
  ENV_VAR: 'MICRO_APP_DEBUG',
  /** 调试日志前缀 */
  LOG_PREFIX: '[MicroApp]',
  /** 调试面板ID */
  PANEL_ID: 'micro-app-debug-panel',
  /** 调试工具类名 */
  TOOLS_CLASS: 'micro-app-debug-tools'
} as const

/**
 * 应用监控常量
 */
export const MONITORING = {
  /** 监控事件前缀 */
  EVENT_PREFIX: 'micro-app:monitor:',
  /** 性能监控间隔（毫秒） */
  PERFORMANCE_INTERVAL: 5000,
  /** 错误监控间隔（毫秒） */
  ERROR_INTERVAL: 1000,
  /** 内存监控间隔（毫秒） */
  MEMORY_INTERVAL: 10000,
  /** 监控数据保留时间（毫秒） */
  DATA_RETENTION: 3600000 // 1小时
} as const