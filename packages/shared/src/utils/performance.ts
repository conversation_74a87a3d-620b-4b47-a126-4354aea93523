/**
 * 性能监控工具
 * 提供性能测量、监控和优化功能
 */

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  /** 开始时间 */
  startTime: number
  /** 结束时间 */
  endTime?: number
  /** 持续时间 */
  duration?: number
  /** 内存使用情况 */
  memory?: {
    used: number
    total: number
    limit: number
  }
  /** 自定义标记 */
  marks?: Record<string, number>
  /** 自定义测量 */
  measures?: Record<string, number>
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map()
  private observers: PerformanceObserver[] = []

  /**
   * 开始性能测量
   */
  start(name: string): void {
    const startTime = performance.now()
    
    this.metrics.set(name, {
      startTime,
      marks: {},
      measures: {}
    })

    // 使用Performance API标记
    if (performance.mark) {
      performance.mark(`${name}-start`)
    }
  }

  /**
   * 结束性能测量
   */
  end(name: string): PerformanceMetrics | null {
    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`性能测量 ${name} 不存在`)
      return null
    }

    const endTime = performance.now()
    const duration = endTime - metric.startTime

    // 更新指标
    metric.endTime = endTime
    metric.duration = duration

    // 获取内存信息
    if ('memory' in performance) {
      const memory = (performance as any).memory
      metric.memory = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      }
    }

    // 使用Performance API标记和测量
    if (performance.mark && performance.measure) {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
    }

    return metric
  }

  /**
   * 添加性能标记
   */
  mark(measureName: string, markName: string): void {
    const metric = this.metrics.get(measureName)
    if (!metric) {
      console.warn(`性能测量 ${measureName} 不存在`)
      return
    }

    const markTime = performance.now()
    metric.marks![markName] = markTime - metric.startTime

    // 使用Performance API标记
    if (performance.mark) {
      performance.mark(`${measureName}-${markName}`)
    }
  }

  /**
   * 添加自定义测量
   */
  measure(measureName: string, customMeasureName: string, startMark: string, endMark: string): void {
    const metric = this.metrics.get(measureName)
    if (!metric || !metric.marks) {
      console.warn(`性能测量 ${measureName} 不存在或没有标记`)
      return
    }

    const startTime = metric.marks[startMark]
    const endTime = metric.marks[endMark]

    if (startTime !== undefined && endTime !== undefined) {
      metric.measures![customMeasureName] = endTime - startTime
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics(name: string): PerformanceMetrics | null {
    return this.metrics.get(name) || null
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics(): Map<string, PerformanceMetrics> {
    return new Map(this.metrics)
  }

  /**
   * 清除性能指标
   */
  clear(name?: string): void {
    if (name) {
      this.metrics.delete(name)
      // 清除Performance API标记
      if (performance.clearMarks) {
        performance.clearMarks(`${name}-start`)
        performance.clearMarks(`${name}-end`)
      }
      if (performance.clearMeasures) {
        performance.clearMeasures(name)
      }
    } else {
      this.metrics.clear()
      if (performance.clearMarks) {
        performance.clearMarks()
      }
      if (performance.clearMeasures) {
        performance.clearMeasures()
      }
    }
  }

  /**
   * 启动性能观察器
   */
  startObserver(types: string[] = ['measure', 'navigation', 'resource']): void {
    if (!PerformanceObserver) {
      console.warn('PerformanceObserver 不支持')
      return
    }

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        console.log(`性能条目: ${entry.name}, 类型: ${entry.entryType}, 持续时间: ${entry.duration}ms`)
      })
    })

    try {
      observer.observe({ entryTypes: types })
      this.observers.push(observer)
    } catch (error) {
      console.error('启动性能观察器失败:', error)
    }
  }

  /**
   * 停止所有性能观察器
   */
  stopObservers(): void {
    this.observers.forEach(observer => {
      observer.disconnect()
    })
    this.observers = []
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const report: string[] = ['=== 性能报告 ===']
    
    for (const [name, metric] of this.metrics) {
      report.push(`\n测量名称: ${name}`)
      report.push(`开始时间: ${metric.startTime.toFixed(2)}ms`)
      
      if (metric.endTime) {
        report.push(`结束时间: ${metric.endTime.toFixed(2)}ms`)
        report.push(`持续时间: ${metric.duration!.toFixed(2)}ms`)
      }

      if (metric.memory) {
        report.push(`内存使用: ${(metric.memory.used / 1024 / 1024).toFixed(2)}MB`)
        report.push(`内存总量: ${(metric.memory.total / 1024 / 1024).toFixed(2)}MB`)
      }

      if (metric.marks && Object.keys(metric.marks).length > 0) {
        report.push('标记:')
        for (const [markName, markTime] of Object.entries(metric.marks)) {
          report.push(`  ${markName}: ${markTime.toFixed(2)}ms`)
        }
      }

      if (metric.measures && Object.keys(metric.measures).length > 0) {
        report.push('测量:')
        for (const [measureName, measureTime] of Object.entries(metric.measures)) {
          report.push(`  ${measureName}: ${measureTime.toFixed(2)}ms`)
        }
      }
    }

    return report.join('\n')
  }
}

/**
 * 性能装饰器
 */
export function performanceDecorator(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const measureName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function (...args: any[]) {
      const monitor = getGlobalPerformanceMonitor()
      
      monitor.start(measureName)
      
      try {
        const result = await originalMethod.apply(this, args)
        monitor.end(measureName)
        return result
      } catch (error) {
        monitor.end(measureName)
        throw error
      }
    }

    return descriptor
  }
}

/**
 * 性能测量函数包装器
 */
export function measurePerformance<T extends (...args: any[]) => any>(
  fn: T,
  name?: string
): T {
  const measureName = name || fn.name || '匿名函数'
  
  return (async (...args: any[]) => {
    const monitor = getGlobalPerformanceMonitor()
    
    monitor.start(measureName)
    
    try {
      const result = await fn(...args)
      monitor.end(measureName)
      return result
    } catch (error) {
      monitor.end(measureName)
      throw error
    }
  }) as T
}

/**
 * 资源加载性能监控
 */
export class ResourcePerformanceMonitor {
  private resourceTimings: Map<string, PerformanceResourceTiming> = new Map()

  /**
   * 开始监控资源加载
   */
  startMonitoring(): void {
    if (!PerformanceObserver) {
      console.warn('PerformanceObserver 不支持')
      return
    }

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries() as PerformanceResourceTiming[]
      
      entries.forEach(entry => {
        this.resourceTimings.set(entry.name, entry)
      })
    })

    try {
      observer.observe({ entryTypes: ['resource'] })
    } catch (error) {
      console.error('启动资源性能监控失败:', error)
    }
  }

  /**
   * 获取资源加载时间
   */
  getResourceTiming(url: string): PerformanceResourceTiming | null {
    return this.resourceTimings.get(url) || null
  }

  /**
   * 获取所有资源时间
   */
  getAllResourceTimings(): Map<string, PerformanceResourceTiming> {
    return new Map(this.resourceTimings)
  }

  /**
   * 分析慢资源
   */
  getSlowResources(threshold: number = 1000): PerformanceResourceTiming[] {
    const slowResources: PerformanceResourceTiming[] = []
    
    for (const timing of this.resourceTimings.values()) {
      if (timing.duration > threshold) {
        slowResources.push(timing)
      }
    }

    return slowResources.sort((a, b) => b.duration - a.duration)
  }

  /**
   * 生成资源性能报告
   */
  generateResourceReport(): string {
    const report: string[] = ['=== 资源性能报告 ===']
    const timings = Array.from(this.resourceTimings.values())
    
    // 按持续时间排序
    timings.sort((a, b) => b.duration - a.duration)
    
    timings.forEach(timing => {
      report.push(`\n资源: ${timing.name}`)
      report.push(`类型: ${timing.initiatorType}`)
      report.push(`大小: ${timing.transferSize || 0} bytes`)
      report.push(`持续时间: ${timing.duration.toFixed(2)}ms`)
      report.push(`DNS查询: ${(timing.domainLookupEnd - timing.domainLookupStart).toFixed(2)}ms`)
      report.push(`TCP连接: ${(timing.connectEnd - timing.connectStart).toFixed(2)}ms`)
      report.push(`请求时间: ${(timing.responseStart - timing.requestStart).toFixed(2)}ms`)
      report.push(`响应时间: ${(timing.responseEnd - timing.responseStart).toFixed(2)}ms`)
    })

    return report.join('\n')
  }
}

/**
 * FPS监控器
 */
export class FPSMonitor {
  private fps = 0
  private frameCount = 0
  private lastTime = 0
  private isRunning = false
  private animationId: number | null = null
  private callbacks: Array<(fps: number) => void> = []

  /**
   * 开始FPS监控
   */
  start(): void {
    if (this.isRunning) return

    this.isRunning = true
    this.lastTime = performance.now()
    this.frameCount = 0
    this.tick()
  }

  /**
   * 停止FPS监控
   */
  stop(): void {
    this.isRunning = false
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  /**
   * 获取当前FPS
   */
  getFPS(): number {
    return this.fps
  }

  /**
   * 添加FPS变化回调
   */
  onFPSChange(callback: (fps: number) => void): void {
    this.callbacks.push(callback)
  }

  /**
   * 移除FPS变化回调
   */
  offFPSChange(callback: (fps: number) => void): void {
    const index = this.callbacks.indexOf(callback)
    if (index > -1) {
      this.callbacks.splice(index, 1)
    }
  }

  private tick = (): void => {
    if (!this.isRunning) return

    const currentTime = performance.now()
    this.frameCount++

    if (currentTime - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime))
      this.frameCount = 0
      this.lastTime = currentTime

      // 通知回调
      this.callbacks.forEach(callback => callback(this.fps))
    }

    this.animationId = requestAnimationFrame(this.tick)
  }
}

/**
 * 内存监控器
 */
export class MemoryMonitor {
  private samples: Array<{ timestamp: number; memory: any }> = []
  private intervalId: NodeJS.Timeout | null = null

  /**
   * 开始内存监控
   */
  start(interval: number = 1000): void {
    if (this.intervalId) return

    this.intervalId = setInterval(() => {
      this.takeSample()
    }, interval)
  }

  /**
   * 停止内存监控
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }

  /**
   * 获取内存样本
   */
  private takeSample(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      this.samples.push({
        timestamp: Date.now(),
        memory: {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        }
      })

      // 保持最近100个样本
      if (this.samples.length > 100) {
        this.samples.shift()
      }
    }
  }

  /**
   * 获取当前内存使用情况
   */
  getCurrentMemory(): any {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        usedMB: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2),
        totalMB: (memory.totalJSHeapSize / 1024 / 1024).toFixed(2),
        limitMB: (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)
      }
    }
    return null
  }

  /**
   * 获取内存使用趋势
   */
  getMemoryTrend(): Array<{ timestamp: number; memory: any }> {
    return [...this.samples]
  }

  /**
   * 检测内存泄漏
   */
  detectMemoryLeak(threshold: number = 50): boolean {
    if (this.samples.length < 10) return false

    const recent = this.samples.slice(-10)
    const growth = recent[recent.length - 1].memory.used - recent[0].memory.used
    const growthMB = growth / 1024 / 1024

    return growthMB > threshold
  }
}

// 全局性能监控器实例
let globalPerformanceMonitor: PerformanceMonitor | null = null

/**
 * 获取全局性能监控器
 */
export function getGlobalPerformanceMonitor(): PerformanceMonitor {
  if (!globalPerformanceMonitor) {
    globalPerformanceMonitor = new PerformanceMonitor()
  }
  return globalPerformanceMonitor
}

/**
 * 创建性能监控器
 */
export function createPerformanceMonitor(): PerformanceMonitor {
  return new PerformanceMonitor()
}

/**
 * 创建资源性能监控器
 */
export function createResourcePerformanceMonitor(): ResourcePerformanceMonitor {
  return new ResourcePerformanceMonitor()
}

/**
 * 创建FPS监控器
 */
export function createFPSMonitor(): FPSMonitor {
  return new FPSMonitor()
}

/**
 * 创建内存监控器
 */
export function createMemoryMonitor(): MemoryMonitor {
  return new MemoryMonitor()
}

/**
 * 性能工具函数
 */
export const PerformanceUtils = {
  /**
   * 测量函数执行时间
   */
  async measureTime<T>(fn: () => Promise<T> | T, name?: string): Promise<{ result: T; duration: number }> {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    const duration = end - start

    if (name) {
      console.log(`${name} 执行时间: ${duration.toFixed(2)}ms`)
    }

    return { result, duration }
  },

  /**
   * 获取页面加载性能
   */
  getPageLoadPerformance(): any {
    if (!performance.timing) return null

    const timing = performance.timing
    return {
      // DNS查询时间
      dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
      // TCP连接时间
      tcpConnect: timing.connectEnd - timing.connectStart,
      // 请求时间
      request: timing.responseStart - timing.requestStart,
      // 响应时间
      response: timing.responseEnd - timing.responseStart,
      // DOM解析时间
      domParse: timing.domContentLoadedEventStart - timing.responseEnd,
      // 资源加载时间
      resourceLoad: timing.loadEventStart - timing.domContentLoadedEventEnd,
      // 总加载时间
      totalLoad: timing.loadEventEnd - timing.navigationStart
    }
  },

  /**
   * 获取首次内容绘制时间
   */
  getFCP(): number | null {
    if (!performance.getEntriesByType) return null

    const entries = performance.getEntriesByType('paint')
    const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
    return fcpEntry ? fcpEntry.startTime : null
  },

  /**
   * 获取最大内容绘制时间
   */
  getLCP(): Promise<number | null> {
    return new Promise((resolve) => {
      if (!PerformanceObserver) {
        resolve(null)
        return
      }

      let lcp = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        lcp = lastEntry.startTime
      })

      try {
        observer.observe({ entryTypes: ['largest-contentful-paint'] })
        
        // 10秒后停止观察
        setTimeout(() => {
          observer.disconnect()
          resolve(lcp || null)
        }, 10000)
      } catch (error) {
        resolve(null)
      }
    })
  }
}