/**
 * 验证工具
 * 提供数据验证、类型检查等功能
 */

/**
 * 验证规则类型
 */
export type ValidationRule<T = any> = {
  validator: (value: T) => boolean | Promise<boolean>
  message: string
  required?: boolean
}

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean
  errors: string[]
}

/**
 * 基础验证器
 */
export class Validator {
  private rules: Map<string, ValidationRule[]> = new Map()

  /**
   * 添加验证规则
   */
  addRule<T>(field: string, rule: ValidationRule<T>): void {
    if (!this.rules.has(field)) {
      this.rules.set(field, [])
    }
    this.rules.get(field)!.push(rule)
  }

  /**
   * 验证单个字段
   */
  async validateField(field: string, value: any): Promise<ValidationResult> {
    const rules = this.rules.get(field) || []
    const errors: string[] = []

    for (const rule of rules) {
      try {
        const isValid = await rule.validator(value)
        if (!isValid) {
          errors.push(rule.message)
        }
      } catch (error) {
        errors.push(`验证规则执行失败: ${error}`)
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证对象
   */
  async validate(data: Record<string, any>): Promise<ValidationResult> {
    const allErrors: string[] = []

    for (const [field, rules] of this.rules) {
      const value = data[field]
      
      // 检查必填字段
      const hasRequiredRule = rules.some(rule => rule.required)
      if (hasRequiredRule && (value === undefined || value === null || value === '')) {
        allErrors.push(`${field} 是必填字段`)
        continue
      }

      // 如果字段为空且不是必填，跳过验证
      if (value === undefined || value === null || value === '') {
        continue
      }

      const result = await this.validateField(field, value)
      allErrors.push(...result.errors)
    }

    return {
      valid: allErrors.length === 0,
      errors: allErrors
    }
  }

  /**
   * 清空规则
   */
  clear(): void {
    this.rules.clear()
  }

  /**
   * 移除字段规则
   */
  removeField(field: string): void {
    this.rules.delete(field)
  }
}

/**
 * 内置验证规则
 */
export const ValidationRules = {
  /**
   * 必填验证
   */
  required(message: string = '此字段是必填的'): ValidationRule {
    return {
      validator: (value) => value !== undefined && value !== null && value !== '',
      message,
      required: true
    }
  },

  /**
   * 字符串长度验证
   */
  length(min: number, max?: number, message?: string): ValidationRule<string> {
    return {
      validator: (value) => {
        if (typeof value !== 'string') return false
        const len = value.length
        if (max !== undefined) {
          return len >= min && len <= max
        }
        return len >= min
      },
      message: message || `长度必须在 ${min}${max ? ` 到 ${max}` : ' 以上'} 之间`
    }
  },

  /**
   * 最小长度验证
   */
  minLength(min: number, message?: string): ValidationRule<string> {
    return {
      validator: (value) => typeof value === 'string' && value.length >= min,
      message: message || `最小长度为 ${min}`
    }
  },

  /**
   * 最大长度验证
   */
  maxLength(max: number, message?: string): ValidationRule<string> {
    return {
      validator: (value) => typeof value === 'string' && value.length <= max,
      message: message || `最大长度为 ${max}`
    }
  },

  /**
   * 数值范围验证
   */
  range(min: number, max: number, message?: string): ValidationRule<number> {
    return {
      validator: (value) => typeof value === 'number' && value >= min && value <= max,
      message: message || `值必须在 ${min} 到 ${max} 之间`
    }
  },

  /**
   * 最小值验证
   */
  min(min: number, message?: string): ValidationRule<number> {
    return {
      validator: (value) => typeof value === 'number' && value >= min,
      message: message || `最小值为 ${min}`
    }
  },

  /**
   * 最大值验证
   */
  max(max: number, message?: string): ValidationRule<number> {
    return {
      validator: (value) => typeof value === 'number' && value <= max,
      message: message || `最大值为 ${max}`
    }
  },

  /**
   * 邮箱验证
   */
  email(message: string = '请输入有效的邮箱地址'): ValidationRule<string> {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return {
      validator: (value) => typeof value === 'string' && emailRegex.test(value),
      message
    }
  },

  /**
   * URL验证
   */
  url(message: string = '请输入有效的URL'): ValidationRule<string> {
    return {
      validator: (value) => {
        if (typeof value !== 'string') return false
        try {
          new URL(value)
          return true
        } catch {
          return false
        }
      },
      message
    }
  },

  /**
   * 正则表达式验证
   */
  pattern(regex: RegExp, message: string): ValidationRule<string> {
    return {
      validator: (value) => typeof value === 'string' && regex.test(value),
      message
    }
  },

  /**
   * 数字验证
   */
  numeric(message: string = '必须是数字'): ValidationRule {
    return {
      validator: (value) => !isNaN(Number(value)),
      message
    }
  },

  /**
   * 整数验证
   */
  integer(message: string = '必须是整数'): ValidationRule {
    return {
      validator: (value) => Number.isInteger(Number(value)),
      message
    }
  },

  /**
   * 手机号验证（中国）
   */
  phone(message: string = '请输入有效的手机号'): ValidationRule<string> {
    const phoneRegex = /^1[3-9]\d{9}$/
    return {
      validator: (value) => typeof value === 'string' && phoneRegex.test(value),
      message
    }
  },

  /**
   * 身份证号验证（中国）
   */
  idCard(message: string = '请输入有效的身份证号'): ValidationRule<string> {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return {
      validator: (value) => typeof value === 'string' && idCardRegex.test(value),
      message
    }
  },

  /**
   * 自定义验证
   */
  custom<T>(validator: (value: T) => boolean | Promise<boolean>, message: string): ValidationRule<T> {
    return {
      validator,
      message
    }
  }
}

/**
 * 类型检查工具
 */
export const TypeUtils = {
  /**
   * 检查是否为字符串
   */
  isString(value: any): value is string {
    return typeof value === 'string'
  },

  /**
   * 检查是否为数字
   */
  isNumber(value: any): value is number {
    return typeof value === 'number' && !isNaN(value)
  },

  /**
   * 检查是否为布尔值
   */
  isBoolean(value: any): value is boolean {
    return typeof value === 'boolean'
  },

  /**
   * 检查是否为对象
   */
  isObject(value: any): value is object {
    return value !== null && typeof value === 'object' && !Array.isArray(value)
  },

  /**
   * 检查是否为数组
   */
  isArray(value: any): value is any[] {
    return Array.isArray(value)
  },

  /**
   * 检查是否为函数
   */
  isFunction(value: any): value is Function {
    return typeof value === 'function'
  },

  /**
   * 检查是否为undefined
   */
  isUndefined(value: any): value is undefined {
    return value === undefined
  },

  /**
   * 检查是否为null
   */
  isNull(value: any): value is null {
    return value === null
  },

  /**
   * 检查是否为null或undefined
   */
  isNullOrUndefined(value: any): value is null | undefined {
    return value === null || value === undefined
  },

  /**
   * 检查是否为空值（null、undefined、空字符串）
   */
  isEmpty(value: any): boolean {
    return value === null || value === undefined || value === ''
  },

  /**
   * 检查是否为空对象
   */
  isEmptyObject(value: any): boolean {
    return this.isObject(value) && Object.keys(value).length === 0
  },

  /**
   * 检查是否为空数组
   */
  isEmptyArray(value: any): boolean {
    return this.isArray(value) && value.length === 0
  },

  /**
   * 检查是否为Date对象
   */
  isDate(value: any): value is Date {
    return value instanceof Date && !isNaN(value.getTime())
  },

  /**
   * 检查是否为Promise
   */
  isPromise(value: any): value is Promise<any> {
    return value && typeof value.then === 'function'
  },

  /**
   * 检查是否为正则表达式
   */
  isRegExp(value: any): value is RegExp {
    return value instanceof RegExp
  },

  /**
   * 检查是否为Error对象
   */
  isError(value: any): value is Error {
    return value instanceof Error
  },

  /**
   * 获取值的类型
   */
  getType(value: any): string {
    if (value === null) return 'null'
    if (value === undefined) return 'undefined'
    if (Array.isArray(value)) return 'array'
    if (value instanceof Date) return 'date'
    if (value instanceof RegExp) return 'regexp'
    if (value instanceof Error) return 'error'
    return typeof value
  }
}

/**
 * 数据清理工具
 */
export const DataUtils = {
  /**
   * 去除字符串首尾空格
   */
  trim(value: any): any {
    return typeof value === 'string' ? value.trim() : value
  },

  /**
   * 转换为数字
   */
  toNumber(value: any): number | null {
    if (typeof value === 'number') return value
    if (typeof value === 'string') {
      const num = Number(value)
      return isNaN(num) ? null : num
    }
    return null
  },

  /**
   * 转换为布尔值
   */
  toBoolean(value: any): boolean {
    if (typeof value === 'boolean') return value
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true'
    }
    return Boolean(value)
  },

  /**
   * 转换为字符串
   */
  toString(value: any): string {
    if (value === null || value === undefined) return ''
    return String(value)
  },

  /**
   * 深度清理对象（移除空值）
   */
  cleanObject(obj: any): any {
    if (!TypeUtils.isObject(obj)) return obj

    const cleaned: any = {}
    for (const [key, value] of Object.entries(obj)) {
      if (!TypeUtils.isEmpty(value)) {
        if (TypeUtils.isObject(value)) {
          const cleanedValue = this.cleanObject(value)
          if (!TypeUtils.isEmptyObject(cleanedValue)) {
            cleaned[key] = cleanedValue
          }
        } else if (TypeUtils.isArray(value)) {
          const cleanedArray = (value as any[]).filter(item => !TypeUtils.isEmpty(item))
          if (cleanedArray.length > 0) {
            cleaned[key] = cleanedArray
          }
        } else {
          cleaned[key] = value
        }
      }
    }
    return cleaned
  },

  /**
   * 标准化数据
   */
  normalize(data: Record<string, any>, rules: Record<string, (value: any) => any>): Record<string, any> {
    const normalized: Record<string, any> = {}
    
    for (const [key, value] of Object.entries(data)) {
      if (rules[key]) {
        normalized[key] = rules[key](value)
      } else {
        normalized[key] = value
      }
    }
    
    return normalized
  }
}

/**
 * 创建验证器
 */
export function createValidator(): Validator {
  return new Validator()
}

/**
 * 快速验证函数
 */
export async function validate(
  data: Record<string, any>,
  rules: Record<string, ValidationRule[]>
): Promise<ValidationResult> {
  const validator = new Validator()
  
  for (const [field, fieldRules] of Object.entries(rules)) {
    for (const rule of fieldRules) {
      validator.addRule(field, rule)
    }
  }
  
  return validator.validate(data)
}

/**
 * 验证单个值
 */
export async function validateValue<T>(
  value: T,
  rules: ValidationRule<T>[]
): Promise<ValidationResult> {
  const errors: string[] = []
  
  for (const rule of rules) {
    try {
      const isValid = await rule.validator(value)
      if (!isValid) {
        errors.push(rule.message)
      }
    } catch (error) {
      errors.push(`验证规则执行失败: ${error}`)
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}