/**
 * 安全工具
 * 提供XSS防护、CSRF防护、内容安全策略等安全功能
 */

/**
 * XSS防护工具
 */
export class XSSProtection {
  /**
   * HTML实体编码映射
   */
  private static readonly HTML_ENTITIES: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;'
  }

  /**
   * 转义HTML特殊字符
   */
  static escapeHtml(text: string): string {
    return text.replace(/[&<>"'/]/g, (match) => {
      return XSSProtection.HTML_ENTITIES[match] || match
    })
  }

  /**
   * 反转义HTML实体
   */
  static unescapeHtml(html: string): string {
    const entityMap: Record<string, string> = {}
    Object.entries(XSSProtection.HTML_ENTITIES).forEach(([char, entity]) => {
      entityMap[entity] = char
    })

    return html.replace(/&amp;|&lt;|&gt;|&quot;|&#x27;|&#x2F;/g, (match) => {
      return entityMap[match] || match
    })
  }

  /**
   * 清理危险的HTML标签和属性
   */
  static sanitizeHtml(html: string, options: {
    allowedTags?: string[]
    allowedAttributes?: string[]
    removeScripts?: boolean
  } = {}): string {
    const {
      allowedTags = ['p', 'br', 'strong', 'em', 'u', 'i', 'b', 'span', 'div'],
      allowedAttributes = ['class', 'id', 'style'],
      removeScripts = true
    } = options

    let sanitized = html

    // 移除脚本标签
    if (removeScripts) {
      sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      sanitized = sanitized.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      sanitized = sanitized.replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
      sanitized = sanitized.replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
    }

    // 移除危险的事件处理器
    sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
    sanitized = sanitized.replace(/\s*on\w+\s*=\s*[^>\s]+/gi, '')

    // 移除javascript:协议
    sanitized = sanitized.replace(/javascript:/gi, '')

    // 移除data:协议（除了图片）
    sanitized = sanitized.replace(/data:(?!image\/)/gi, '')

    return sanitized
  }

  /**
   * 验证URL是否安全
   */
  static isUrlSafe(url: string): boolean {
    try {
      const urlObj = new URL(url)
      
      // 检查协议
      const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:']
      if (!allowedProtocols.includes(urlObj.protocol)) {
        return false
      }

      // 检查是否包含危险字符
      const dangerousPatterns = [
        /javascript:/i,
        /data:/i,
        /vbscript:/i,
        /file:/i,
        /ftp:/i
      ]

      return !dangerousPatterns.some(pattern => pattern.test(url))
    } catch {
      return false
    }
  }

  /**
   * 清理用户输入
   */
  static sanitizeInput(input: string, options: {
    maxLength?: number
    allowHtml?: boolean
    removeNewlines?: boolean
  } = {}): string {
    const {
      maxLength = 1000,
      allowHtml = false,
      removeNewlines = false
    } = options

    let sanitized = input

    // 限制长度
    if (sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength)
    }

    // 移除换行符
    if (removeNewlines) {
      sanitized = sanitized.replace(/[\r\n]/g, ' ')
    }

    // 处理HTML
    if (!allowHtml) {
      sanitized = XSSProtection.escapeHtml(sanitized)
    } else {
      sanitized = XSSProtection.sanitizeHtml(sanitized)
    }

    return sanitized.trim()
  }
}

/**
 * CSRF防护工具
 */
export class CSRFProtection {
  private static readonly TOKEN_LENGTH = 32
  private static readonly TOKEN_HEADER = 'X-CSRF-Token'
  private static readonly TOKEN_STORAGE_KEY = 'csrf_token'

  /**
   * 生成CSRF令牌
   */
  static generateToken(): string {
    const array = new Uint8Array(CSRFProtection.TOKEN_LENGTH)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 设置CSRF令牌到存储
   */
  static setToken(token?: string): string {
    const csrfToken = token || CSRFProtection.generateToken()
    
    try {
      sessionStorage.setItem(CSRFProtection.TOKEN_STORAGE_KEY, csrfToken)
    } catch {
      // 如果sessionStorage不可用，使用内存存储
      (window as any).__csrf_token = csrfToken
    }

    return csrfToken
  }

  /**
   * 获取CSRF令牌
   */
  static getToken(): string | null {
    try {
      return sessionStorage.getItem(CSRFProtection.TOKEN_STORAGE_KEY)
    } catch {
      return (window as any).__csrf_token || null
    }
  }

  /**
   * 验证CSRF令牌
   */
  static validateToken(token: string): boolean {
    const storedToken = CSRFProtection.getToken()
    return storedToken !== null && storedToken === token
  }

  /**
   * 为请求添加CSRF令牌
   */
  static addTokenToRequest(request: RequestInit): RequestInit {
    const token = CSRFProtection.getToken()
    if (!token) {
      throw new Error('CSRF令牌不存在')
    }

    const headers = new Headers(request.headers)
    headers.set(CSRFProtection.TOKEN_HEADER, token)

    return {
      ...request,
      headers
    }
  }

  /**
   * 为表单添加CSRF令牌
   */
  static addTokenToForm(form: HTMLFormElement): void {
    const token = CSRFProtection.getToken()
    if (!token) {
      throw new Error('CSRF令牌不存在')
    }

    // 移除现有的CSRF令牌字段
    const existingTokenField = form.querySelector('input[name="csrf_token"]')
    if (existingTokenField) {
      existingTokenField.remove()
    }

    // 添加新的CSRF令牌字段
    const tokenField = document.createElement('input')
    tokenField.type = 'hidden'
    tokenField.name = 'csrf_token'
    tokenField.value = token
    form.appendChild(tokenField)
  }
}

/**
 * 内容安全策略工具
 */
export class CSPManager {
  private static policies: Map<string, string> = new Map()

  /**
   * 设置CSP策略
   */
  static setPolicy(directive: string, value: string): void {
    CSPManager.policies.set(directive, value)
    CSPManager.updateMetaTag()
  }

  /**
   * 获取CSP策略
   */
  static getPolicy(directive: string): string | undefined {
    return CSPManager.policies.get(directive)
  }

  /**
   * 移除CSP策略
   */
  static removePolicy(directive: string): void {
    CSPManager.policies.delete(directive)
    CSPManager.updateMetaTag()
  }

  /**
   * 获取完整的CSP字符串
   */
  static getPolicyString(): string {
    const policyParts: string[] = []
    
    for (const [directive, value] of CSPManager.policies) {
      policyParts.push(`${directive} ${value}`)
    }

    return policyParts.join('; ')
  }

  /**
   * 更新页面的CSP meta标签
   */
  private static updateMetaTag(): void {
    // 移除现有的CSP meta标签
    const existingMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]')
    if (existingMeta) {
      existingMeta.remove()
    }

    // 添加新的CSP meta标签
    if (CSPManager.policies.size > 0) {
      const meta = document.createElement('meta')
      meta.httpEquiv = 'Content-Security-Policy'
      meta.content = CSPManager.getPolicyString()
      document.head.appendChild(meta)
    }
  }

  /**
   * 设置默认的安全策略
   */
  static setDefaultSecurePolicy(): void {
    CSPManager.setPolicy('default-src', "'self'")
    CSPManager.setPolicy('script-src', "'self' 'unsafe-inline'")
    CSPManager.setPolicy('style-src', "'self' 'unsafe-inline'")
    CSPManager.setPolicy('img-src', "'self' data: https:")
    CSPManager.setPolicy('font-src', "'self' https:")
    CSPManager.setPolicy('connect-src', "'self'")
    CSPManager.setPolicy('frame-ancestors', "'none'")
    CSPManager.setPolicy('base-uri', "'self'")
    CSPManager.setPolicy('form-action', "'self'")
  }

  /**
   * 设置开发环境策略
   */
  static setDevelopmentPolicy(): void {
    CSPManager.setPolicy('default-src', "'self'")
    CSPManager.setPolicy('script-src', "'self' 'unsafe-inline' 'unsafe-eval'")
    CSPManager.setPolicy('style-src', "'self' 'unsafe-inline'")
    CSPManager.setPolicy('img-src', "'self' data: https: http:")
    CSPManager.setPolicy('font-src', "'self' https: http:")
    CSPManager.setPolicy('connect-src', "'self' ws: wss:")
  }
}

/**
 * 安全头部管理器
 */
export class SecurityHeaders {
  /**
   * 设置安全响应头
   */
  static setSecurityHeaders(response: Response): Response {
    const headers = new Headers(response.headers)

    // X-Content-Type-Options
    headers.set('X-Content-Type-Options', 'nosniff')

    // X-Frame-Options
    headers.set('X-Frame-Options', 'DENY')

    // X-XSS-Protection
    headers.set('X-XSS-Protection', '1; mode=block')

    // Referrer-Policy
    headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

    // Strict-Transport-Security (仅HTTPS)
    if (location.protocol === 'https:') {
      headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
    }

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers
    })
  }

  /**
   * 验证请求头的安全性
   */
  static validateRequestHeaders(headers: Headers): boolean {
    // 检查User-Agent
    const userAgent = headers.get('User-Agent')
    if (!userAgent || userAgent.length < 10) {
      return false
    }

    // 检查可疑的头部
    const suspiciousHeaders = ['X-Forwarded-For', 'X-Real-IP', 'X-Originating-IP']
    for (const header of suspiciousHeaders) {
      const value = headers.get(header)
      if (value && this.containsSuspiciousContent(value)) {
        return false
      }
    }

    return true
  }

  /**
   * 检查内容是否包含可疑字符
   */
  private static containsSuspiciousContent(content: string): boolean {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i,
      /eval\(/i,
      /expression\(/i
    ]

    return suspiciousPatterns.some(pattern => pattern.test(content))
  }
}

/**
 * 输入验证器
 */
export class InputValidator {
  /**
   * 验证邮箱格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && email.length <= 254
  }

  /**
   * 验证URL格式
   */
  static isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      return ['http:', 'https:'].includes(urlObj.protocol)
    } catch {
      return false
    }
  }

  /**
   * 验证手机号格式（中国）
   */
  static isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  /**
   * 验证身份证号格式（中国）
   */
  static isValidIdCard(idCard: string): boolean {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  }

  /**
   * 验证密码强度
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean
    score: number
    feedback: string[]
  } {
    const feedback: string[] = []
    let score = 0

    // 长度检查
    if (password.length < 8) {
      feedback.push('密码长度至少8位')
    } else if (password.length >= 12) {
      score += 2
    } else {
      score += 1
    }

    // 包含小写字母
    if (/[a-z]/.test(password)) {
      score += 1
    } else {
      feedback.push('密码应包含小写字母')
    }

    // 包含大写字母
    if (/[A-Z]/.test(password)) {
      score += 1
    } else {
      feedback.push('密码应包含大写字母')
    }

    // 包含数字
    if (/\d/.test(password)) {
      score += 1
    } else {
      feedback.push('密码应包含数字')
    }

    // 包含特殊字符
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      score += 1
    } else {
      feedback.push('密码应包含特殊字符')
    }

    // 避免常见密码
    const commonPasswords = ['123456', 'password', 'admin', 'qwerty', '123456789']
    if (commonPasswords.includes(password.toLowerCase())) {
      score = 0
      feedback.push('请避免使用常见密码')
    }

    return {
      isValid: score >= 4 && password.length >= 8,
      score,
      feedback
    }
  }

  /**
   * 验证SQL注入
   */
  static containsSqlInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(--|\/\*|\*\/)/,
      /(\b(CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\()/i,
      /(\b(WAITFOR|DELAY)\s+)/i
    ]

    return sqlPatterns.some(pattern => pattern.test(input))
  }

  /**
   * 验证XSS攻击
   */
  static containsXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/i,
      /vbscript:/i,
      /onload\s*=/i,
      /onerror\s*=/i,
      /onclick\s*=/i,
      /onmouseover\s*=/i
    ]

    return xssPatterns.some(pattern => pattern.test(input))
  }
}

/**
 * 加密工具
 */
export class CryptoUtils {
  /**
   * 生成随机字符串
   */
  static generateRandomString(length: number = 16): string {
    const array = new Uint8Array(length)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 生成UUID
   */
  static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * 简单哈希函数（用于非安全场景）
   */
  static simpleHash(str: string): string {
    let hash = 0
    if (str.length === 0) return hash.toString()
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(16)
  }

  /**
   * Base64编码
   */
  static base64Encode(str: string): string {
    return btoa(unescape(encodeURIComponent(str)))
  }

  /**
   * Base64解码
   */
  static base64Decode(str: string): string {
    return decodeURIComponent(escape(atob(str)))
  }

  /**
   * 使用Web Crypto API进行SHA-256哈希
   */
  static async sha256(message: string): Promise<string> {
    const msgBuffer = new TextEncoder().encode(message)
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }
}

/**
 * 安全工具集合
 */
export const SecurityUtils = {
  XSSProtection,
  CSRFProtection,
  CSPManager,
  SecurityHeaders,
  InputValidator,
  CryptoUtils,

  /**
   * 初始化安全设置
   */
  initSecurity(options: {
    enableCSRF?: boolean
    enableCSP?: boolean
    cspMode?: 'strict' | 'development'
  } = {}): void {
    const {
      enableCSRF = true,
      enableCSP = true,
      cspMode = 'strict'
    } = options

    // 初始化CSRF保护
    if (enableCSRF) {
      CSRFProtection.setToken()
    }

    // 初始化CSP
    if (enableCSP) {
      if (cspMode === 'development') {
        CSPManager.setDevelopmentPolicy()
      } else {
        CSPManager.setDefaultSecurePolicy()
      }
    }
  },

  /**
   * 全面的输入清理
   */
  sanitizeInput(input: string, options: {
    maxLength?: number
    allowHtml?: boolean
    removeNewlines?: boolean
    checkXSS?: boolean
    checkSQLInjection?: boolean
  } = {}): string {
    const {
      maxLength = 1000,
      allowHtml = false,
      removeNewlines = false,
      checkXSS = true,
      checkSQLInjection = true
    } = options

    // 检查XSS
    if (checkXSS && InputValidator.containsXSS(input)) {
      throw new Error('输入包含潜在的XSS攻击代码')
    }

    // 检查SQL注入
    if (checkSQLInjection && InputValidator.containsSqlInjection(input)) {
      throw new Error('输入包含潜在的SQL注入代码')
    }

    // 清理输入
    return XSSProtection.sanitizeInput(input, {
      maxLength,
      allowHtml,
      removeNewlines
    })
  }
}