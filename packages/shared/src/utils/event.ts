/**
 * 事件处理工具
 * 提供统一的事件管理和处理功能
 */

/**
 * 事件处理器类型
 */
export type EventHandler<T = any> = (data: T) => void

/**
 * 事件监听器选项
 */
export interface EventListenerOptions {
  /** 是否只执行一次 */
  once?: boolean
  /** 是否在捕获阶段执行 */
  capture?: boolean
  /** 是否为被动监听器 */
  passive?: boolean
}

/**
 * 事件总线类
 * 提供发布订阅模式的事件管理
 */
export class EventBus {
  private events: Record<string, EventHandler[]> = {}

  /**
   * 订阅事件
   */
  on<T = any>(event: string, handler: EventHandler<T>): void {
    if (!this.events[event]) {
      this.events[event] = []
    }
    this.events[event].push(handler)
  }

  /**
   * 取消订阅事件
   */
  off<T = any>(event: string, handler?: EventHandler<T>): void {
    if (!this.events[event]) {
      return
    }

    if (handler) {
      const index = this.events[event].indexOf(handler)
      if (index > -1) {
        this.events[event].splice(index, 1)
      }
    } else {
      delete this.events[event]
    }
  }

  /**
   * 触发事件
   */
  emit<T = any>(event: string, data?: T): void {
    if (!this.events[event]) {
      return
    }

    this.events[event].forEach(handler => {
      try {
        handler(data)
      } catch (error) {
        console.error(`事件处理器执行失败 [${event}]:`, error)
      }
    })
  }

  /**
   * 一次性订阅事件
   */
  once<T = any>(event: string, handler: EventHandler<T>): void {
    const onceHandler = (data: T) => {
      handler(data)
      this.off(event, onceHandler)
    }
    this.on(event, onceHandler)
  }

  /**
   * 清空所有事件
   */
  clear(): void {
    this.events = {}
  }

  /**
   * 获取事件列表
   */
  getEvents(): string[] {
    return Object.keys(this.events)
  }

  /**
   * 获取事件监听器数量
   */
  getListenerCount(event: string): number {
    return this.events[event]?.length || 0
  }
}

/**
 * 添加DOM事件监听器
 */
export function addEventListener(
  element: Element,
  type: string,
  listener: EventListenerOrEventListenerObject,
  options?: boolean | AddEventListenerOptions
): void {
  element.addEventListener(type, listener, options)
}

/**
 * 移除DOM事件监听器
 */
export function removeEventListener(
  element: Element,
  type: string,
  listener: EventListenerOrEventListenerObject,
  options?: boolean | EventListenerOptions
): void {
  element.removeEventListener(type, listener, options)
}

/**
 * 触发自定义事件
 */
export function dispatchEvent(element: Element, eventName: string, detail?: any): boolean {
  const event = new CustomEvent(eventName, {
    detail,
    bubbles: true,
    cancelable: true
  })
  return element.dispatchEvent(event)
}

/**
 * 阻止事件默认行为
 */
export function preventDefault(event: Event): void {
  event.preventDefault()
}

/**
 * 阻止事件冒泡
 */
export function stopPropagation(event: Event): void {
  event.stopPropagation()
}

/**
 * 阻止事件默认行为和冒泡
 */
export function stopEvent(event: Event): void {
  preventDefault(event)
  stopPropagation(event)
}

/**
 * 事件委托
 */
export function delegate(
  container: Element,
  selector: string,
  type: string,
  handler: (event: Event, target: Element) => void
): () => void {
  const delegateHandler = (event: Event) => {
    const target = event.target as Element
    const matchedElement = target.closest(selector)
    
    if (matchedElement && container.contains(matchedElement)) {
      handler(event, matchedElement)
    }
  }

  container.addEventListener(type, delegateHandler)

  // 返回清理函数
  return () => {
    container.removeEventListener(type, delegateHandler)
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | null = null

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      func(...args)
      timeoutId = null
    }, delay)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCallTime = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastCallTime >= delay) {
      func(...args)
      lastCallTime = now
    }
  }
}

/**
 * 等待事件触发
 */
export function waitForEvent(
  element: Element,
  eventType: string,
  timeout?: number
): Promise<Event> {
  return new Promise((resolve, reject) => {
    let timeoutId: ReturnType<typeof setTimeout> | null = null

    const handler = (event: Event) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      element.removeEventListener(eventType, handler)
      resolve(event)
    }

    element.addEventListener(eventType, handler, { once: true })

    if (timeout) {
      timeoutId = setTimeout(() => {
        element.removeEventListener(eventType, handler)
        reject(new Error(`等待事件 ${eventType} 超时`))
      }, timeout)
    }
  })
}

/**
 * 键盘事件工具
 */
export const KeyboardUtils = {
  /**
   * 检查是否按下了指定键
   */
  isKey(event: KeyboardEvent, key: string): boolean {
    return event.key === key || event.code === key
  },

  /**
   * 检查是否按下了修饰键
   */
  hasModifier(event: KeyboardEvent, modifier: 'ctrl' | 'alt' | 'shift' | 'meta'): boolean {
    switch (modifier) {
      case 'ctrl':
        return event.ctrlKey
      case 'alt':
        return event.altKey
      case 'shift':
        return event.shiftKey
      case 'meta':
        return event.metaKey
      default:
        return false
    }
  },

  /**
   * 创建键盘快捷键处理器
   */
  createShortcutHandler(shortcuts: Record<string, () => void>) {
    return (event: KeyboardEvent) => {
      const key = event.key.toLowerCase()
      const modifiers = []
      
      if (event.ctrlKey) modifiers.push('ctrl')
      if (event.altKey) modifiers.push('alt')
      if (event.shiftKey) modifiers.push('shift')
      if (event.metaKey) modifiers.push('meta')
      
      const shortcut = modifiers.length > 0 ? `${modifiers.join('+')}+${key}` : key
      
      if (shortcuts[shortcut]) {
        preventDefault(event)
        shortcuts[shortcut]()
      }
    }
  }
}

/**
 * 鼠标事件工具
 */
export const MouseUtils = {
  /**
   * 获取鼠标相对于元素的位置
   */
  getRelativePosition(event: MouseEvent, element: Element): { x: number; y: number } {
    const rect = element.getBoundingClientRect()
    return {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
  },

  /**
   * 检查是否为左键点击
   */
  isLeftClick(event: MouseEvent): boolean {
    return event.button === 0
  },

  /**
   * 检查是否为右键点击
   */
  isRightClick(event: MouseEvent): boolean {
    return event.button === 2
  },

  /**
   * 检查是否为中键点击
   */
  isMiddleClick(event: MouseEvent): boolean {
    return event.button === 1
  }
}

/**
 * 触摸事件工具
 */
export const TouchUtils = {
  /**
   * 获取触摸点相对于元素的位置
   */
  getRelativePosition(touch: Touch, element: Element): { x: number; y: number } {
    const rect = element.getBoundingClientRect()
    return {
      x: touch.clientX - rect.left,
      y: touch.clientY - rect.top
    }
  },

  /**
   * 计算两个触摸点之间的距离
   */
  getDistance(touch1: Touch, touch2: Touch): number {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  },

  /**
   * 计算触摸点的中心位置
   */
  getCenter(touches: TouchList): { x: number; y: number } {
    let x = 0
    let y = 0
    
    for (let i = 0; i < touches.length; i++) {
      x += touches[i].clientX
      y += touches[i].clientY
    }
    
    return {
      x: x / touches.length,
      y: y / touches.length
    }
  }
}

/**
 * 事件管理器类
 * 提供更高级的事件管理功能
 */
export class EventManager {
  private listeners: Map<Element, Map<string, EventListenerOrEventListenerObject[]>> = new Map()

  /**
   * 添加事件监听器
   */
  add(
    element: Element,
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions
  ): void {
    // 记录监听器
    if (!this.listeners.has(element)) {
      this.listeners.set(element, new Map())
    }
    
    const elementListeners = this.listeners.get(element)!
    if (!elementListeners.has(type)) {
      elementListeners.set(type, [])
    }
    
    elementListeners.get(type)!.push(listener)
    
    // 添加监听器
    element.addEventListener(type, listener, options)
  }

  /**
   * 移除事件监听器
   */
  remove(
    element: Element,
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | EventListenerOptions
  ): void {
    // 移除记录
    const elementListeners = this.listeners.get(element)
    if (elementListeners) {
      const typeListeners = elementListeners.get(type)
      if (typeListeners) {
        const index = typeListeners.indexOf(listener)
        if (index > -1) {
          typeListeners.splice(index, 1)
        }
        
        if (typeListeners.length === 0) {
          elementListeners.delete(type)
        }
      }
      
      if (elementListeners.size === 0) {
        this.listeners.delete(element)
      }
    }
    
    // 移除监听器
    element.removeEventListener(type, listener, options)
  }

  /**
   * 移除元素的所有事件监听器
   */
  removeAll(element: Element): void {
    const elementListeners = this.listeners.get(element)
    if (elementListeners) {
      elementListeners.forEach((listeners, type) => {
        listeners.forEach(listener => {
          element.removeEventListener(type, listener)
        })
      })
      this.listeners.delete(element)
    }
  }

  /**
   * 清空所有事件监听器
   */
  clear(): void {
    this.listeners.forEach((elementListeners, element) => {
      elementListeners.forEach((listeners, type) => {
        listeners.forEach(listener => {
          element.removeEventListener(type, listener)
        })
      })
    })
    this.listeners.clear()
  }

  /**
   * 获取元素的监听器数量
   */
  getListenerCount(element: Element, type?: string): number {
    const elementListeners = this.listeners.get(element)
    if (!elementListeners) {
      return 0
    }
    
    if (type) {
      return elementListeners.get(type)?.length || 0
    }
    
    let count = 0
    elementListeners.forEach(listeners => {
      count += listeners.length
    })
    return count
  }
}

/**
 * 创建事件处理器包装器
 */
export function createEventWrapper(
  handler: EventListenerOrEventListenerObject,
  options: {
    preventDefault?: boolean
    stopPropagation?: boolean
    debounce?: number
    throttle?: number
  } = {}
): EventListenerOrEventListenerObject {
  let wrappedHandler = handler

  // 应用防抖
  if (options.debounce) {
    const originalHandler = wrappedHandler as EventListener
    wrappedHandler = debounce(originalHandler, options.debounce)
  }

  // 应用节流
  if (options.throttle) {
    const originalHandler = wrappedHandler as EventListener
    wrappedHandler = throttle(originalHandler, options.throttle)
  }

  // 包装事件处理
  const finalHandler = (event: Event) => {
    if (options.preventDefault) {
      preventDefault(event)
    }
    
    if (options.stopPropagation) {
      stopPropagation(event)
    }
    
    if (typeof wrappedHandler === 'function') {
      wrappedHandler(event)
    } else if (wrappedHandler && 'handleEvent' in wrappedHandler) {
      wrappedHandler.handleEvent(event)
    }
  }

  return finalHandler
}

/**
 * 创建事件监听器装饰器
 */
export function eventListener(
  type: string,
  options?: boolean | AddEventListenerOptions
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = function (this: any, element: Element) {
      const handler = originalMethod.bind(this)
      element.addEventListener(type, handler, options)
      
      // 返回清理函数
      return () => {
        element.removeEventListener(type, handler, options)
      }
    }

    return descriptor
  }
}

/**
 * 事件工具函数集合
 */
export const EventUtils = {
  /**
   * 检查事件是否来自指定元素
   */
  isFromElement(event: Event, element: Element): boolean {
    return event.target === element || element.contains(event.target as Node)
  },

  /**
   * 获取事件路径
   */
  getEventPath(event: Event): EventTarget[] {
    if ('composedPath' in event && typeof event.composedPath === 'function') {
      return event.composedPath()
    }
    
    // 兼容性处理
    const path: EventTarget[] = []
    let target = event.target as Element | null
    
    while (target) {
      path.push(target)
      target = target.parentElement
    }
    
    path.push(document, window)
    return path
  },

  /**
   * 检查事件是否支持
   */
  isEventSupported(eventName: string, element?: Element): boolean {
    const testElement = element || document.createElement('div')
    const eventProperty = `on${eventName.toLowerCase()}`
    return eventProperty in testElement
  },

  /**
   * 创建事件对象
   */
  createEvent(type: string, options: EventInit = {}): Event {
    if (typeof Event === 'function') {
      return new Event(type, options)
    }
    
    // 兼容性处理
    const event = document.createEvent('Event')
    event.initEvent(type, options.bubbles || false, options.cancelable || false)
    return event
  },

  /**
   * 创建自定义事件对象
   */
  createCustomEvent(type: string, options: CustomEventInit = {}): CustomEvent {
    if (typeof CustomEvent === 'function') {
      return new CustomEvent(type, options)
    }
    
    // 兼容性处理
    const event = document.createEvent('CustomEvent')
    event.initCustomEvent(
      type,
      options.bubbles || false,
      options.cancelable || false,
      options.detail
    )
    return event
  }
}

// 创建全局事件总线实例
export const globalEventBus = new EventBus()

// 创建全局事件管理器实例
export const globalEventManager = new EventManager()

// 导出默认事件总线
export default globalEventBus