/**
 * 存储工具
 * 提供统一的本地存储、会话存储和内存存储接口
 */

/**
 * 存储接口
 */
export interface StorageInterface {
  get<T = any>(key: string): T | null
  set<T = any>(key: string, value: T, ttl?: number): void
  remove(key: string): void
  clear(): void
  has(key: string): boolean
  keys(): string[]
  size(): number
}

/**
 * 存储项接口
 */
interface StorageItem<T = any> {
  value: T
  timestamp: number
  ttl?: number
}

/**
 * 本地存储适配器
 */
export class LocalStorageAdapter implements StorageInterface {
  private prefix: string

  constructor(prefix: string = 'micro-core:') {
    this.prefix = prefix
  }

  private getKey(key: string): string {
    return `${this.prefix}${key}`
  }

  private isExpired(item: StorageItem): boolean {
    if (!item.ttl) return false
    return Date.now() - item.timestamp > item.ttl
  }

  get<T = any>(key: string): T | null {
    try {
      const fullKey = this.getKey(key)
      const data = localStorage.getItem(fullKey)
      
      if (!data) return null

      const item: StorageItem<T> = JSON.parse(data)
      
      if (this.isExpired(item)) {
        this.remove(key)
        return null
      }

      return item.value
    } catch (error) {
      console.error('LocalStorage 读取失败:', error)
      return null
    }
  }

  set<T = any>(key: string, value: T, ttl?: number): void {
    try {
      const fullKey = this.getKey(key)
      const item: StorageItem<T> = {
        value,
        timestamp: Date.now(),
        ttl
      }
      
      localStorage.setItem(fullKey, JSON.stringify(item))
    } catch (error) {
      console.error('LocalStorage 写入失败:', error)
    }
  }

  remove(key: string): void {
    try {
      const fullKey = this.getKey(key)
      localStorage.removeItem(fullKey)
    } catch (error) {
      console.error('LocalStorage 删除失败:', error)
    }
  }

  clear(): void {
    try {
      const keys = this.keys()
      keys.forEach(key => this.remove(key))
    } catch (error) {
      console.error('LocalStorage 清空失败:', error)
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  keys(): string[] {
    try {
      const keys: string[] = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(this.prefix)) {
          keys.push(key.slice(this.prefix.length))
        }
      }
      return keys
    } catch (error) {
      console.error('LocalStorage 获取键列表失败:', error)
      return []
    }
  }

  size(): number {
    return this.keys().length
  }
}

/**
 * 会话存储适配器
 */
export class SessionStorageAdapter implements StorageInterface {
  private prefix: string

  constructor(prefix: string = 'micro-core:') {
    this.prefix = prefix
  }

  private getKey(key: string): string {
    return `${this.prefix}${key}`
  }

  private isExpired(item: StorageItem): boolean {
    if (!item.ttl) return false
    return Date.now() - item.timestamp > item.ttl
  }

  get<T = any>(key: string): T | null {
    try {
      const fullKey = this.getKey(key)
      const data = sessionStorage.getItem(fullKey)
      
      if (!data) return null

      const item: StorageItem<T> = JSON.parse(data)
      
      if (this.isExpired(item)) {
        this.remove(key)
        return null
      }

      return item.value
    } catch (error) {
      console.error('SessionStorage 读取失败:', error)
      return null
    }
  }

  set<T = any>(key: string, value: T, ttl?: number): void {
    try {
      const fullKey = this.getKey(key)
      const item: StorageItem<T> = {
        value,
        timestamp: Date.now(),
        ttl
      }
      
      sessionStorage.setItem(fullKey, JSON.stringify(item))
    } catch (error) {
      console.error('SessionStorage 写入失败:', error)
    }
  }

  remove(key: string): void {
    try {
      const fullKey = this.getKey(key)
      sessionStorage.removeItem(fullKey)
    } catch (error) {
      console.error('SessionStorage 删除失败:', error)
    }
  }

  clear(): void {
    try {
      const keys = this.keys()
      keys.forEach(key => this.remove(key))
    } catch (error) {
      console.error('SessionStorage 清空失败:', error)
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  keys(): string[] {
    try {
      const keys: string[] = []
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i)
        if (key && key.startsWith(this.prefix)) {
          keys.push(key.slice(this.prefix.length))
        }
      }
      return keys
    } catch (error) {
      console.error('SessionStorage 获取键列表失败:', error)
      return []
    }
  }

  size(): number {
    return this.keys().length
  }
}

/**
 * 内存存储适配器
 */
export class MemoryStorageAdapter implements StorageInterface {
  private storage: Map<string, StorageItem> = new Map()

  private isExpired(item: StorageItem): boolean {
    if (!item.ttl) return false
    return Date.now() - item.timestamp > item.ttl
  }

  get<T = any>(key: string): T | null {
    const item = this.storage.get(key)
    
    if (!item) return null

    if (this.isExpired(item)) {
      this.remove(key)
      return null
    }

    return item.value
  }

  set<T = any>(key: string, value: T, ttl?: number): void {
    const item: StorageItem<T> = {
      value,
      timestamp: Date.now(),
      ttl
    }
    
    this.storage.set(key, item)
  }

  remove(key: string): void {
    this.storage.delete(key)
  }

  clear(): void {
    this.storage.clear()
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  keys(): string[] {
    return Array.from(this.storage.keys())
  }

  size(): number {
    return this.storage.size
  }
}

/**
 * 存储管理器
 */
export class StorageManager {
  private adapters: Map<string, StorageInterface> = new Map()
  private defaultAdapter: string = 'localStorage'

  constructor() {
    // 注册默认适配器
    this.registerAdapter('localStorage', new LocalStorageAdapter())
    this.registerAdapter('sessionStorage', new SessionStorageAdapter())
    this.registerAdapter('memory', new MemoryStorageAdapter())
  }

  /**
   * 注册存储适配器
   */
  registerAdapter(name: string, adapter: StorageInterface): void {
    this.adapters.set(name, adapter)
  }

  /**
   * 获取存储适配器
   */
  getAdapter(name?: string): StorageInterface {
    const adapterName = name || this.defaultAdapter
    const adapter = this.adapters.get(adapterName)
    
    if (!adapter) {
      throw new Error(`存储适配器 ${adapterName} 不存在`)
    }
    
    return adapter
  }

  /**
   * 设置默认适配器
   */
  setDefaultAdapter(name: string): void {
    if (!this.adapters.has(name)) {
      throw new Error(`存储适配器 ${name} 不存在`)
    }
    this.defaultAdapter = name
  }

  /**
   * 获取数据
   */
  get<T = any>(key: string, adapter?: string): T | null {
    return this.getAdapter(adapter).get<T>(key)
  }

  /**
   * 设置数据
   */
  set<T = any>(key: string, value: T, ttl?: number, adapter?: string): void {
    this.getAdapter(adapter).set(key, value, ttl)
  }

  /**
   * 删除数据
   */
  remove(key: string, adapter?: string): void {
    this.getAdapter(adapter).remove(key)
  }

  /**
   * 清空数据
   */
  clear(adapter?: string): void {
    this.getAdapter(adapter).clear()
  }

  /**
   * 检查是否存在
   */
  has(key: string, adapter?: string): boolean {
    return this.getAdapter(adapter).has(key)
  }

  /**
   * 获取所有键
   */
  keys(adapter?: string): string[] {
    return this.getAdapter(adapter).keys()
  }

  /**
   * 获取存储大小
   */
  size(adapter?: string): number {
    return this.getAdapter(adapter).size()
  }

  /**
   * 获取可用的适配器列表
   */
  getAvailableAdapters(): string[] {
    return Array.from(this.adapters.keys())
  }
}

/**
 * 检查存储是否可用
 */
export function isStorageAvailable(type: 'localStorage' | 'sessionStorage'): boolean {
  try {
    const storage = window[type]
    const testKey = '__storage_test__'
    storage.setItem(testKey, 'test')
    storage.removeItem(testKey)
    return true
  } catch {
    return false
  }
}

/**
 * 获取存储使用情况
 */
export function getStorageUsage(type: 'localStorage' | 'sessionStorage'): {
  used: number
  total: number
  available: number
  percentage: number
} {
  try {
    const storage = window[type]
    let used = 0
    
    for (let key in storage) {
      if (storage.hasOwnProperty(key)) {
        used += storage[key].length + key.length
      }
    }
    
    // 估算总容量（通常为5MB）
    const total = 5 * 1024 * 1024
    const available = total - used
    const percentage = (used / total) * 100
    
    return {
      used,
      total,
      available,
      percentage
    }
  } catch {
    return {
      used: 0,
      total: 0,
      available: 0,
      percentage: 0
    }
  }
}

/**
 * 清理过期数据
 */
export function cleanExpiredData(adapter: StorageInterface): number {
  const keys = adapter.keys()
  let cleanedCount = 0
  
  keys.forEach(key => {
    // 尝试获取数据，如果过期会自动删除
    const value = adapter.get(key)
    if (value === null) {
      cleanedCount++
    }
  })
  
  return cleanedCount
}

// 创建默认存储管理器实例
export const storageManager = new StorageManager()

// 便捷方法
export const localStorage = storageManager.getAdapter('localStorage')
export const sessionStorage = storageManager.getAdapter('sessionStorage')
export const memoryStorage = storageManager.getAdapter('memory')

// 导出常用方法
export const {
  get: getItem,
  set: setItem,
  remove: removeItem,
  clear: clearStorage,
  has: hasItem,
  keys: getKeys,
  size: getSize
} = storageManager