/**
 * 日志工具
 * 提供统一的日志记录、格式化和输出功能
 */

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  /** 时间戳 */
  timestamp: number
  /** 日志级别 */
  level: LogLevel
  /** 日志消息 */
  message: string
  /** 日志标签 */
  tag?: string
  /** 额外数据 */
  data?: any
  /** 错误对象 */
  error?: Error
  /** 调用栈 */
  stack?: string
}

/**
 * 日志输出器接口
 */
export interface LogAppender {
  /** 输出日志 */
  append(entry: LogEntry): void
  /** 清理资源 */
  destroy?(): void
}

/**
 * 控制台日志输出器
 */
export class ConsoleAppender implements LogAppender {
  private colors: Record<LogLevel, string> = {
    [LogLevel.DEBUG]: '#6B7280',
    [LogLevel.INFO]: '#3B82F6',
    [LogLevel.WARN]: '#F59E0B',
    [LogLevel.ERROR]: '#EF4444',
    [LogLevel.FATAL]: '#DC2626'
  }

  append(entry: LogEntry): void {
    const { timestamp, level, message, tag, data, error } = entry
    const time = new Date(timestamp).toISOString()
    const levelName = LogLevel[level]
    const color = this.colors[level]

    // 构建日志消息
    let logMessage = `[${time}] [${levelName}]`
    if (tag) {
      logMessage += ` [${tag}]`
    }
    logMessage += ` ${message}`

    // 根据级别选择输出方法
    const logMethod = this.getLogMethod(level)
    
    if (typeof window !== 'undefined' && window.console) {
      // 浏览器环境，使用颜色
      logMethod(`%c${logMessage}`, `color: ${color}`)
      
      if (data !== undefined) {
        console.log('数据:', data)
      }
      
      if (error) {
        console.error('错误:', error)
      }
    } else {
      // Node.js环境
      logMethod(logMessage)
      
      if (data !== undefined) {
        logMethod('数据:', data)
      }
      
      if (error) {
        logMethod('错误:', error)
      }
    }
  }

  private getLogMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.DEBUG:
        return console.debug || console.log
      case LogLevel.INFO:
        return console.info || console.log
      case LogLevel.WARN:
        return console.warn || console.log
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        return console.error || console.log
      default:
        return console.log
    }
  }
}

/**
 * 内存日志输出器
 */
export class MemoryAppender implements LogAppender {
  private logs: LogEntry[] = []
  private maxSize: number

  constructor(maxSize: number = 1000) {
    this.maxSize = maxSize
  }

  append(entry: LogEntry): void {
    this.logs.push(entry)
    
    // 保持日志数量在限制内
    if (this.logs.length > this.maxSize) {
      this.logs.shift()
    }
  }

  /**
   * 获取所有日志
   */
  getLogs(): LogEntry[] {
    return [...this.logs]
  }

  /**
   * 获取指定级别的日志
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level)
  }

  /**
   * 获取指定标签的日志
   */
  getLogsByTag(tag: string): LogEntry[] {
    return this.logs.filter(log => log.tag === tag)
  }

  /**
   * 清空日志
   */
  clear(): void {
    this.logs = []
  }

  /**
   * 导出日志为JSON
   */
  exportToJson(): string {
    return JSON.stringify(this.logs, null, 2)
  }

  destroy(): void {
    this.clear()
  }
}

/**
 * 本地存储日志输出器
 */
export class LocalStorageAppender implements LogAppender {
  private storageKey: string
  private maxSize: number

  constructor(storageKey: string = 'app_logs', maxSize: number = 500) {
    this.storageKey = storageKey
    this.maxSize = maxSize
  }

  append(entry: LogEntry): void {
    try {
      const logs = this.getLogs()
      logs.push(entry)
      
      // 保持日志数量在限制内
      if (logs.length > this.maxSize) {
        logs.splice(0, logs.length - this.maxSize)
      }
      
      localStorage.setItem(this.storageKey, JSON.stringify(logs))
    } catch (error) {
      // 存储失败时回退到控制台
      console.error('日志存储失败:', error)
    }
  }

  /**
   * 获取存储的日志
   */
  getLogs(): LogEntry[] {
    try {
      const logsJson = localStorage.getItem(this.storageKey)
      return logsJson ? JSON.parse(logsJson) : []
    } catch {
      return []
    }
  }

  /**
   * 清空存储的日志
   */
  clear(): void {
    try {
      localStorage.removeItem(this.storageKey)
    } catch (error) {
      console.error('清空日志失败:', error)
    }
  }

  destroy(): void {
    this.clear()
  }
}

/**
 * 远程日志输出器
 */
export class RemoteAppender implements LogAppender {
  private endpoint: string
  private batchSize: number
  private flushInterval: number
  private buffer: LogEntry[] = []
  private timer: NodeJS.Timeout | null = null

  constructor(
    endpoint: string,
    options: {
      batchSize?: number
      flushInterval?: number
    } = {}
  ) {
    this.endpoint = endpoint
    this.batchSize = options.batchSize || 10
    this.flushInterval = options.flushInterval || 5000

    // 启动定时刷新
    this.startFlushTimer()
  }

  append(entry: LogEntry): void {
    this.buffer.push(entry)
    
    // 达到批量大小时立即发送
    if (this.buffer.length >= this.batchSize) {
      this.flush()
    }
  }

  /**
   * 刷新缓冲区
   */
  private async flush(): void {
    if (this.buffer.length === 0) return

    const logs = [...this.buffer]
    this.buffer = []

    try {
      await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ logs })
      })
    } catch (error) {
      console.error('发送日志失败:', error)
      // 发送失败时重新加入缓冲区
      this.buffer.unshift(...logs)
    }
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer(): void {
    this.timer = setInterval(() => {
      this.flush()
    }, this.flushInterval)
  }

  destroy(): void {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    
    // 最后一次刷新
    this.flush()
  }
}

/**
 * 日志记录器
 */
export class Logger {
  private tag: string
  private level: LogLevel
  private appenders: LogAppender[]
  private enabled: boolean

  constructor(
    tag: string = 'App',
    level: LogLevel = LogLevel.INFO,
    appenders: LogAppender[] = [new ConsoleAppender()]
  ) {
    this.tag = tag
    this.level = level
    this.appenders = appenders
    this.enabled = true
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel): void {
    this.level = level
  }

  /**
   * 获取日志级别
   */
  getLevel(): LogLevel {
    return this.level
  }

  /**
   * 启用/禁用日志
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled
  }

  /**
   * 添加日志输出器
   */
  addAppender(appender: LogAppender): void {
    this.appenders.push(appender)
  }

  /**
   * 移除日志输出器
   */
  removeAppender(appender: LogAppender): void {
    const index = this.appenders.indexOf(appender)
    if (index > -1) {
      this.appenders.splice(index, 1)
    }
  }

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, data?: any, error?: Error): void {
    if (!this.enabled || level < this.level) {
      return
    }

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      tag: this.tag,
      data,
      error,
      stack: error?.stack || (new Error().stack)
    }

    // 输出到所有输出器
    this.appenders.forEach(appender => {
      try {
        appender.append(entry)
      } catch (err) {
        console.error('日志输出器错误:', err)
      }
    })
  }

  /**
   * 调试日志
   */
  debug(message: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, data)
  }

  /**
   * 信息日志
   */
  info(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data)
  }

  /**
   * 警告日志
   */
  warn(message: string, data?: any): void {
    this.log(LogLevel.WARN, message, data)
  }

  /**
   * 错误日志
   */
  error(message: string, error?: Error, data?: any): void {
    this.log(LogLevel.ERROR, message, data, error)
  }

  /**
   * 致命错误日志
   */
  fatal(message: string, error?: Error, data?: any): void {
    this.log(LogLevel.FATAL, message, data, error)
  }

  /**
   * 性能日志
   */
  perf(operation: string, duration: number, data?: any): void {
    this.info(`性能: ${operation} 耗时 ${duration.toFixed(2)}ms`, data)
  }

  /**
   * 创建子日志记录器
   */
  child(tag: string): Logger {
    return new Logger(`${this.tag}.${tag}`, this.level, this.appenders)
  }

  /**
   * 销毁日志记录器
   */
  destroy(): void {
    this.appenders.forEach(appender => {
      if (appender.destroy) {
        appender.destroy()
      }
    })
    this.appenders = []
  }
}

/**
 * 日志管理器
 */
export class LoggerManager {
  private static instance: LoggerManager
  private loggers: Map<string, Logger> = new Map()
  private globalLevel: LogLevel = LogLevel.INFO
  private globalAppenders: LogAppender[] = [new ConsoleAppender()]

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): LoggerManager {
    if (!LoggerManager.instance) {
      LoggerManager.instance = new LoggerManager()
    }
    return LoggerManager.instance
  }

  /**
   * 设置全局日志级别
   */
  setGlobalLevel(level: LogLevel): void {
    this.globalLevel = level
    this.loggers.forEach(logger => {
      logger.setLevel(level)
    })
  }

  /**
   * 设置全局日志输出器
   */
  setGlobalAppenders(appenders: LogAppender[]): void {
    this.globalAppenders = appenders
  }

  /**
   * 添加全局日志输出器
   */
  addGlobalAppender(appender: LogAppender): void {
    this.globalAppenders.push(appender)
    this.loggers.forEach(logger => {
      logger.addAppender(appender)
    })
  }

  /**
   * 获取日志记录器
   */
  getLogger(tag: string): Logger {
    if (!this.loggers.has(tag)) {
      const logger = new Logger(tag, this.globalLevel, [...this.globalAppenders])
      this.loggers.set(tag, logger)
    }
    return this.loggers.get(tag)!
  }

  /**
   * 销毁所有日志记录器
   */
  destroy(): void {
    this.loggers.forEach(logger => {
      logger.destroy()
    })
    this.loggers.clear()
  }
}

/**
 * 日志工具函数
 */
export const LogUtils = {
  /**
   * 格式化日志消息
   */
  formatMessage(template: string, ...args: any[]): string {
    return template.replace(/{(\d+)}/g, (match, index) => {
      const argIndex = parseInt(index, 10)
      return args[argIndex] !== undefined ? String(args[argIndex]) : match
    })
  },

  /**
   * 获取调用栈信息
   */
  getStackTrace(): string {
    const stack = new Error().stack
    return stack ? stack.split('\n').slice(2).join('\n') : ''
  },

  /**
   * 序列化对象为日志字符串
   */
  serialize(obj: any): string {
    try {
      if (obj === null || obj === undefined) {
        return String(obj)
      }
      
      if (typeof obj === 'string') {
        return obj
      }
      
      if (obj instanceof Error) {
        return `${obj.name}: ${obj.message}\n${obj.stack}`
      }
      
      return JSON.stringify(obj, null, 2)
    } catch {
      return '[无法序列化的对象]'
    }
  },

  /**
   * 创建性能日志装饰器
   */
  performanceLog(logger: Logger, operation?: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value
      const operationName = operation || `${target.constructor.name}.${propertyKey}`

      descriptor.value = async function (...args: any[]) {
        const startTime = performance.now()
        
        try {
          const result = await originalMethod.apply(this, args)
          const duration = performance.now() - startTime
          logger.perf(operationName, duration)
          return result
        } catch (error) {
          const duration = performance.now() - startTime
          logger.error(`${operationName} 执行失败`, error as Error, { duration })
          throw error
        }
      }

      return descriptor
    }
  }
}

// 默认日志记录器实例
const defaultLogger = LoggerManager.getInstance().getLogger('MicroCore')

/**
 * 便捷的日志函数
 */
export const log = {
  debug: (message: string, data?: any) => defaultLogger.debug(message, data),
  info: (message: string, data?: any) => defaultLogger.info(message, data),
  warn: (message: string, data?: any) => defaultLogger.warn(message, data),
  error: (message: string, error?: Error, data?: any) => defaultLogger.error(message, error, data),
  fatal: (message: string, error?: Error, data?: any) => defaultLogger.fatal(message, error, data),
  perf: (operation: string, duration: number, data?: any) => defaultLogger.perf(operation, duration, data)
}

/**
 * 创建日志记录器
 */
export function createLogger(tag: string, level?: LogLevel, appenders?: LogAppender[]): Logger {
  return new Logger(tag, level, appenders)
}

/**
 * 获取日志记录器
 */
export function getLogger(tag: string): Logger {
  return LoggerManager.getInstance().getLogger(tag)
}

/**
 * 配置全局日志设置
 */
export function configureLogging(options: {
  level?: LogLevel
  appenders?: LogAppender[]
  enableConsole?: boolean
  enableMemory?: boolean
  enableLocalStorage?: boolean
  remoteEndpoint?: string
}): void {
  const {
    level = LogLevel.INFO,
    appenders,
    enableConsole = true,
    enableMemory = false,
    enableLocalStorage = false,
    remoteEndpoint
  } = options

  const manager = LoggerManager.getInstance()
  
  // 设置全局级别
  manager.setGlobalLevel(level)

  // 配置输出器
  if (appenders) {
    manager.setGlobalAppenders(appenders)
  } else {
    const globalAppenders: LogAppender[] = []
    
    if (enableConsole) {
      globalAppenders.push(new ConsoleAppender())
    }
    
    if (enableMemory) {
      globalAppenders.push(new MemoryAppender())
    }
    
    if (enableLocalStorage) {
      globalAppenders.push(new LocalStorageAppender())
    }
    
    if (remoteEndpoint) {
      globalAppenders.push(new RemoteAppender(remoteEndpoint))
    }
    
    manager.setGlobalAppenders(globalAppenders)
  }
}

// 导出类型和枚举
export type {
  LogEntry,
  LogAppender
}

export {
  LogLevel,
  ConsoleAppender,
  MemoryAppender,
  LocalStorageAppender,
  RemoteAppender,
  Logger,
  LoggerManager
}