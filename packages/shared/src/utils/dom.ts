/**
 * DOM 操作工具
 * 提供跨框架的DOM操作方法
 */

/**
 * 查找DOM元素
 */
export function querySelector(selector: string, context?: Element | Document): Element | null {
  const root = context || document
  return root.querySelector(selector)
}

/**
 * 查找多个DOM元素
 */
export function querySelectorAll(selector: string, context?: Element | Document): NodeListOf<Element> {
  const root = context || document
  return root.querySelectorAll(selector)
}

/**
 * 创建DOM元素
 */
export function createElement<K extends keyof HTMLElementTagNameMap>(
  tagName: K,
  attributes?: Record<string, string>,
  textContent?: string
): HTMLElementTagNameMap[K] {
  const element = document.createElement(tagName)
  
  if (attributes) {
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value)
    })
  }
  
  if (textContent) {
    element.textContent = textContent
  }
  
  return element
}

/**
 * 添加CSS类
 */
export function addClass(element: Element, className: string): void {
  element.classList.add(className)
}

/**
 * 移除CSS类
 */
export function removeClass(element: Element, className: string): void {
  element.classList.remove(className)
}

/**
 * 切换CSS类
 */
export function toggleClass(element: Element, className: string): boolean {
  return element.classList.toggle(className)
}

/**
 * 检查是否包含CSS类
 */
export function hasClass(element: Element, className: string): boolean {
  return element.classList.contains(className)
}

/**
 * 设置元素样式
 */
export function setStyle(element: HTMLElement, styles: Partial<CSSStyleDeclaration>): void {
  Object.assign(element.style, styles)
}

/**
 * 获取元素样式
 */
export function getStyle(element: Element, property: string): string {
  return window.getComputedStyle(element).getPropertyValue(property)
}

/**
 * 插入HTML内容
 */
export function insertHTML(element: Element, html: string, position: InsertPosition = 'beforeend'): void {
  element.insertAdjacentHTML(position, html)
}

/**
 * 移除DOM元素
 */
export function removeElement(element: Element): void {
  element.parentNode?.removeChild(element)
}

/**
 * 获取元素位置信息
 */
export function getElementRect(element: Element): DOMRect {
  return element.getBoundingClientRect()
}

/**
 * 检查元素是否在视口中
 */
export function isElementInViewport(element: Element): boolean {
  const rect = getElementRect(element)
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  )
}

/**
 * 滚动到指定元素
 */
export function scrollToElement(element: Element, options?: ScrollIntoViewOptions): void {
  element.scrollIntoView(options)
}

/**
 * 获取元素的文本内容
 */
export function getTextContent(element: Element): string {
  return element.textContent || ''
}

/**
 * 设置元素的文本内容
 */
export function setTextContent(element: Element, text: string): void {
  element.textContent = text
}

/**
 * 检查元素是否匹配选择器
 */
export function matches(element: Element, selector: string): boolean {
  return element.matches(selector)
}

/**
 * 查找最近的匹配祖先元素
 */
export function closest(element: Element, selector: string): Element | null {
  return element.closest(selector)
}

/**
 * 获取元素的所有子元素
 */
export function getChildren(element: Element): Element[] {
  return Array.from(element.children)
}

/**
 * 获取元素的父元素
 */
export function getParent(element: Element): Element | null {
  return element.parentElement
}

/**
 * 克隆元素
 */
export function cloneElement(element: Element, deep: boolean = true): Element {
  return element.cloneNode(deep) as Element
}

/**
 * 检查是否为DOM元素
 */
export function isElement(obj: any): obj is Element {
  return obj instanceof Element
}

/**
 * 检查是否为HTML元素
 */
export function isHTMLElement(obj: any): obj is HTMLElement {
  return obj instanceof HTMLElement
}

/**
 * 等待DOM加载完成
 */
export function waitForDOMReady(): Promise<void> {
  return new Promise((resolve) => {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => resolve(), { once: true })
    } else {
      resolve()
    }
  })
}

/**
 * 创建文档片段
 */
export function createDocumentFragment(): DocumentFragment {
  return document.createDocumentFragment()
}

/**
 * 批量添加元素到文档片段
 */
export function appendToFragment(fragment: DocumentFragment, elements: Element[]): DocumentFragment {
  elements.forEach(element => fragment.appendChild(element))
  return fragment
}