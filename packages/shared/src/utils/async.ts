/**
 * 异步处理工具
 * 提供Promise、异步队列、重试等功能
 */

/**
 * 延迟执行
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 超时Promise
 */
export function timeout<T>(promise: Promise<T>, ms: number, errorMessage?: string): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(errorMessage || `操作超时 (${ms}ms)`))
      }, ms)
    })
  ])
}

/**
 * 重试配置
 */
export interface RetryOptions {
  /** 最大重试次数 */
  maxRetries: number
  /** 重试延迟（毫秒） */
  delay?: number
  /** 指数退避因子 */
  backoffFactor?: number
  /** 最大延迟时间 */
  maxDelay?: number
  /** 重试条件判断函数 */
  shouldRetry?: (error: any, attempt: number) => boolean
  /** 重试前的回调 */
  onRetry?: (error: any, attempt: number) => void
}

/**
 * 重试执行异步函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  options: RetryOptions
): Promise<T> {
  const {
    maxRetries,
    delay: initialDelay = 1000,
    backoffFactor = 2,
    maxDelay = 30000,
    shouldRetry = () => true,
    onRetry
  } = options

  let lastError: any
  let currentDelay = initialDelay

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error

      if (attempt === maxRetries || !shouldRetry(error, attempt)) {
        throw error
      }

      if (onRetry) {
        onRetry(error, attempt)
      }

      await delay(Math.min(currentDelay, maxDelay))
      currentDelay *= backoffFactor
    }
  }

  throw lastError
}

/**
 * 并发控制器
 */
export class ConcurrencyController {
  private running = 0
  private queue: Array<() => void> = []

  constructor(private maxConcurrency: number) {}

  /**
   * 执行异步任务
   */
  async execute<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const run = async () => {
        this.running++
        try {
          const result = await task()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.running--
          this.processQueue()
        }
      }

      if (this.running < this.maxConcurrency) {
        run()
      } else {
        this.queue.push(run)
      }
    })
  }

  private processQueue(): void {
    if (this.queue.length > 0 && this.running < this.maxConcurrency) {
      const next = this.queue.shift()
      if (next) {
        next()
      }
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): { running: number; queued: number; maxConcurrency: number } {
    return {
      running: this.running,
      queued: this.queue.length,
      maxConcurrency: this.maxConcurrency
    }
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.queue = []
  }
}

/**
 * 异步队列
 */
export class AsyncQueue<T = any> {
  private queue: Array<() => Promise<T>> = []
  private processing = false
  private results: T[] = []
  private errors: any[] = []

  /**
   * 添加任务到队列
   */
  add(task: () => Promise<T>): void {
    this.queue.push(task)
  }

  /**
   * 批量添加任务
   */
  addAll(tasks: Array<() => Promise<T>>): void {
    this.queue.push(...tasks)
  }

  /**
   * 处理队列（串行）
   */
  async processSerial(): Promise<T[]> {
    if (this.processing) {
      throw new Error('队列正在处理中')
    }

    this.processing = true
    this.results = []
    this.errors = []

    try {
      for (const task of this.queue) {
        try {
          const result = await task()
          this.results.push(result)
        } catch (error) {
          this.errors.push(error)
        }
      }
    } finally {
      this.processing = false
    }

    return this.results
  }

  /**
   * 处理队列（并行）
   */
  async processParallel(): Promise<T[]> {
    if (this.processing) {
      throw new Error('队列正在处理中')
    }

    this.processing = true
    this.results = []
    this.errors = []

    try {
      const promises = this.queue.map(async (task, index) => {
        try {
          return await task()
        } catch (error) {
          this.errors[index] = error
          return null
        }
      })

      const results = await Promise.all(promises)
      this.results = results.filter(result => result !== null) as T[]
    } finally {
      this.processing = false
    }

    return this.results
  }

  /**
   * 处理队列（限制并发）
   */
  async processWithConcurrency(maxConcurrency: number): Promise<T[]> {
    const controller = new ConcurrencyController(maxConcurrency)
    const tasks = this.queue.map(task => () => controller.execute(task))
    
    this.queue = tasks
    return this.processParallel()
  }

  /**
   * 获取队列状态
   */
  getStatus(): {
    queueLength: number
    processing: boolean
    resultsCount: number
    errorsCount: number
  } {
    return {
      queueLength: this.queue.length,
      processing: this.processing,
      resultsCount: this.results.length,
      errorsCount: this.errors.length
    }
  }

  /**
   * 清空队列
   */
  clear(): void {
    if (this.processing) {
      throw new Error('无法清空正在处理的队列')
    }
    this.queue = []
    this.results = []
    this.errors = []
  }

  /**
   * 获取结果
   */
  getResults(): T[] {
    return [...this.results]
  }

  /**
   * 获取错误
   */
  getErrors(): any[] {
    return [...this.errors]
  }
}

/**
 * 批处理器
 */
export class BatchProcessor<T, R> {
  private batch: T[] = []
  private timer: NodeJS.Timeout | null = null

  constructor(
    private processor: (items: T[]) => Promise<R[]>,
    private options: {
      batchSize: number
      maxWaitTime: number
    }
  ) {}

  /**
   * 添加项目到批处理
   */
  async add(item: T): Promise<R> {
    return new Promise((resolve, reject) => {
      this.batch.push(item)

      // 记录resolve函数，用于后续调用
      const itemIndex = this.batch.length - 1
      ;(item as any).__resolve = resolve
      ;(item as any).__reject = reject
      ;(item as any).__index = itemIndex

      // 如果达到批处理大小，立即处理
      if (this.batch.length >= this.options.batchSize) {
        this.processBatch()
      } else {
        // 设置定时器，确保不会等待太久
        if (!this.timer) {
          this.timer = setTimeout(() => {
            this.processBatch()
          }, this.options.maxWaitTime)
        }
      }
    })
  }

  /**
   * 处理当前批次
   */
  private async processBatch(): Promise<void> {
    if (this.batch.length === 0) return

    const currentBatch = [...this.batch]
    this.batch = []

    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }

    try {
      const results = await this.processor(currentBatch)
      
      currentBatch.forEach((item, index) => {
        const resolve = (item as any).__resolve
        if (resolve && results[index] !== undefined) {
          resolve(results[index])
        }
      })
    } catch (error) {
      currentBatch.forEach(item => {
        const reject = (item as any).__reject
        if (reject) {
          reject(error)
        }
      })
    }
  }

  /**
   * 强制处理当前批次
   */
  async flush(): Promise<void> {
    await this.processBatch()
  }

  /**
   * 获取当前批次大小
   */
  getBatchSize(): number {
    return this.batch.length
  }
}

/**
 * 可取消的Promise
 */
export class CancellablePromise<T> {
  private promise: Promise<T>
  private cancelled = false
  private cancelCallback?: () => void

  constructor(
    executor: (
      resolve: (value: T) => void,
      reject: (reason?: any) => void,
      onCancel: (callback: () => void) => void
    ) => void
  ) {
    this.promise = new Promise<T>((resolve, reject) => {
      const onCancel = (callback: () => void) => {
        this.cancelCallback = callback
      }

      executor(
        (value) => {
          if (!this.cancelled) {
            resolve(value)
          }
        },
        (reason) => {
          if (!this.cancelled) {
            reject(reason)
          }
        },
        onCancel
      )
    })
  }

  /**
   * 取消Promise
   */
  cancel(): void {
    if (this.cancelled) return

    this.cancelled = true
    if (this.cancelCallback) {
      this.cancelCallback()
    }
  }

  /**
   * 检查是否已取消
   */
  isCancelled(): boolean {
    return this.cancelled
  }

  /**
   * 获取Promise
   */
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null
  ): Promise<TResult1 | TResult2> {
    return this.promise.then(onfulfilled, onrejected)
  }

  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null
  ): Promise<T | TResult> {
    return this.promise.catch(onrejected)
  }

  finally(onfinally?: (() => void) | null): Promise<T> {
    return this.promise.finally(onfinally)
  }
}

/**
 * 创建可取消的Promise
 */
export function createCancellablePromise<T>(
  executor: (
    resolve: (value: T) => void,
    reject: (reason?: any) => void,
    onCancel: (callback: () => void) => void
  ) => void
): CancellablePromise<T> {
  return new CancellablePromise(executor)
}

/**
 * Promise工具函数
 */
export const PromiseUtils = {
  /**
   * 等待所有Promise完成（忽略错误）
   */
  async allSettled<T>(promises: Promise<T>[]): Promise<Array<{ status: 'fulfilled' | 'rejected'; value?: T; reason?: any }>> {
    return Promise.allSettled(promises).then(results =>
      results.map(result => ({
        status: result.status,
        ...(result.status === 'fulfilled' ? { value: result.value } : { reason: result.reason })
      }))
    )
  },

  /**
   * 竞速Promise（返回第一个成功的）
   */
  async any<T>(promises: Promise<T>[]): Promise<T> {
    return new Promise((resolve, reject) => {
      let rejectedCount = 0
      const errors: any[] = []

      promises.forEach((promise, index) => {
        promise
          .then(resolve)
          .catch(error => {
            errors[index] = error
            rejectedCount++
            if (rejectedCount === promises.length) {
              reject(new AggregateError(errors, '所有Promise都被拒绝'))
            }
          })
      })
    })
  },

  /**
   * 映射Promise数组
   */
  async map<T, R>(
    items: T[],
    mapper: (item: T, index: number) => Promise<R>,
    concurrency?: number
  ): Promise<R[]> {
    if (concurrency) {
      const controller = new ConcurrencyController(concurrency)
      const promises = items.map((item, index) =>
        controller.execute(() => mapper(item, index))
      )
      return Promise.all(promises)
    } else {
      return Promise.all(items.map(mapper))
    }
  },

  /**
   * 过滤Promise数组
   */
  async filter<T>(
    items: T[],
    predicate: (item: T, index: number) => Promise<boolean>
  ): Promise<T[]> {
    const results = await Promise.all(items.map(predicate))
    return items.filter((_, index) => results[index])
  },

  /**
   * 串行执行Promise
   */
  async series<T>(tasks: Array<() => Promise<T>>): Promise<T[]> {
    const results: T[] = []
    for (const task of tasks) {
      results.push(await task())
    }
    return results
  }
}

/**
 * 创建异步队列
 */
export function createAsyncQueue<T = any>(): AsyncQueue<T> {
  return new AsyncQueue<T>()
}

/**
 * 创建并发控制器
 */
export function createConcurrencyController(maxConcurrency: number): ConcurrencyController {
  return new ConcurrencyController(maxConcurrency)
}

/**
 * 创建批处理器
 */
export function createBatchProcessor<T, R>(
  processor: (items: T[]) => Promise<R[]>,
  options: { batchSize: number; maxWaitTime: number }
): BatchProcessor<T, R> {
  return new BatchProcessor(processor, options)
}