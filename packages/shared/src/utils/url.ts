/**
 * URL 处理工具
 * 提供URL解析、构建和操作方法
 */

/**
 * URL参数类型
 */
export type URLParams = Record<string, string | number | boolean | undefined>

/**
 * 解析URL参数
 */
export function parseURLParams(url: string = window.location.href): URLParams {
  const urlObj = new URL(url)
  const params: URLParams = {}
  
  urlObj.searchParams.forEach((value, key) => {
    // 尝试转换为数字或布尔值
    if (value === 'true') {
      params[key] = true
    } else if (value === 'false') {
      params[key] = false
    } else if (!isNaN(Number(value)) && value !== '') {
      params[key] = Number(value)
    } else {
      params[key] = value
    }
  })
  
  return params
}

/**
 * 构建URL参数字符串
 */
export function buildURLParams(params: URLParams): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value))
    }
  })
  
  return searchParams.toString()
}

/**
 * 添加URL参数
 */
export function addURLParams(url: string, params: URLParams): string {
  const urlObj = new URL(url)
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      urlObj.searchParams.set(key, String(value))
    }
  })
  
  return urlObj.toString()
}

/**
 * 移除URL参数
 */
export function removeURLParams(url: string, keys: string[]): string {
  const urlObj = new URL(url)
  
  keys.forEach(key => {
    urlObj.searchParams.delete(key)
  })
  
  return urlObj.toString()
}

/**
 * 获取URL参数值
 */
export function getURLParam(key: string, url: string = window.location.href): string | null {
  const urlObj = new URL(url)
  return urlObj.searchParams.get(key)
}

/**
 * 检查URL是否包含指定参数
 */
export function hasURLParam(key: string, url: string = window.location.href): boolean {
  const urlObj = new URL(url)
  return urlObj.searchParams.has(key)
}

/**
 * 解析URL各部分
 */
export interface ParsedURL {
  protocol: string
  hostname: string
  port: string
  pathname: string
  search: string
  hash: string
  origin: string
  params: URLParams
}

export function parseURL(url: string): ParsedURL {
  const urlObj = new URL(url)
  
  return {
    protocol: urlObj.protocol,
    hostname: urlObj.hostname,
    port: urlObj.port,
    pathname: urlObj.pathname,
    search: urlObj.search,
    hash: urlObj.hash,
    origin: urlObj.origin,
    params: parseURLParams(url)
  }
}

/**
 * 构建完整URL
 */
export function buildURL(options: {
  protocol?: string
  hostname?: string
  port?: string | number
  pathname?: string
  params?: URLParams
  hash?: string
}): string {
  const {
    protocol = 'https:',
    hostname = 'localhost',
    port,
    pathname = '/',
    params = {},
    hash
  } = options
  
  let url = `${protocol}//${hostname}`
  
  if (port) {
    url += `:${port}`
  }
  
  url += pathname
  
  const paramString = buildURLParams(params)
  if (paramString) {
    url += `?${paramString}`
  }
  
  if (hash) {
    url += `#${hash.startsWith('#') ? hash.slice(1) : hash}`
  }
  
  return url
}

/**
 * 检查URL是否有效
 */
export function isValidURL(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 检查是否为绝对URL
 */
export function isAbsoluteURL(url: string): boolean {
  return /^https?:\/\//.test(url)
}

/**
 * 检查是否为相对URL
 */
export function isRelativeURL(url: string): boolean {
  return !isAbsoluteURL(url) && !url.startsWith('//')
}

/**
 * 将相对URL转换为绝对URL
 */
export function toAbsoluteURL(url: string, base: string = window.location.origin): string {
  if (isAbsoluteURL(url)) {
    return url
  }
  
  return new URL(url, base).toString()
}

/**
 * 获取URL的基础路径
 */
export function getBasePath(url: string): string {
  const urlObj = new URL(url)
  const pathParts = urlObj.pathname.split('/').filter(Boolean)
  pathParts.pop() // 移除文件名
  return '/' + pathParts.join('/')
}

/**
 * 获取URL的文件名
 */
export function getFileName(url: string): string {
  const urlObj = new URL(url)
  const pathParts = urlObj.pathname.split('/').filter(Boolean)
  return pathParts[pathParts.length - 1] || ''
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(url: string): string {
  const fileName = getFileName(url)
  const dotIndex = fileName.lastIndexOf('.')
  return dotIndex > 0 ? fileName.slice(dotIndex + 1) : ''
}

/**
 * 连接URL路径
 */
export function joinURLPaths(...paths: string[]): string {
  return paths
    .map(path => path.replace(/^\/+|\/+$/g, ''))
    .filter(Boolean)
    .join('/')
}

/**
 * 规范化URL路径
 */
export function normalizePath(path: string): string {
  const parts = path.split('/').filter(Boolean)
  const normalizedParts: string[] = []
  
  for (const part of parts) {
    if (part === '..') {
      normalizedParts.pop()
    } else if (part !== '.') {
      normalizedParts.push(part)
    }
  }
  
  return '/' + normalizedParts.join('/')
}

/**
 * 检查两个URL是否同源
 */
export function isSameOrigin(url1: string, url2: string): boolean {
  try {
    const urlObj1 = new URL(url1)
    const urlObj2 = new URL(url2)
    return urlObj1.origin === urlObj2.origin
  } catch {
    return false
  }
}

/**
 * 获取URL的域名
 */
export function getDomain(url: string): string {
  try {
    return new URL(url).hostname
  } catch {
    return ''
  }
}

/**
 * 检查是否为本地URL
 */
export function isLocalURL(url: string): boolean {
  const hostname = getDomain(url)
  return hostname === 'localhost' || hostname === '127.0.0.1' || hostname.endsWith('.local')
}

/**
 * 编码URL组件
 */
export function encodeURLComponent(str: string): string {
  return encodeURIComponent(str)
}

/**
 * 解码URL组件
 */
export function decodeURLComponent(str: string): string {
  try {
    return decodeURIComponent(str)
  } catch {
    return str
  }
}

/**
 * 清理URL（移除多余的斜杠等）
 */
export function cleanURL(url: string): string {
  return url
    .replace(/([^:]\/)\/+/g, '$1') // 移除多余的斜杠
    .replace(/\/+$/, '') // 移除末尾斜杠
}

/**
 * 获取当前页面URL信息
 */
export function getCurrentURL(): ParsedURL {
  return parseURL(window.location.href)
}

/**
 * 更新当前页面URL参数
 */
export function updateCurrentURLParams(params: URLParams, replace: boolean = false): void {
  const url = addURLParams(window.location.href, params)
  
  if (replace) {
    window.history.replaceState(null, '', url)
  } else {
    window.history.pushState(null, '', url)
  }
}

/**
 * 移除当前页面URL参数
 */
export function removeCurrentURLParams(keys: string[], replace: boolean = false): void {
  const url = removeURLParams(window.location.href, keys)
  
  if (replace) {
    window.history.replaceState(null, '', url)
  } else {
    window.history.pushState(null, '', url)
  }
}

/**
 * URL模板工具
 */
export class URLTemplate {
  private template: string

  constructor(template: string) {
    this.template = template
  }

  /**
   * 填充模板参数
   */
  fill(params: Record<string, string | number>): string {
    let result = this.template
    
    Object.entries(params).forEach(([key, value]) => {
      const placeholder = `{${key}}`
      result = result.replace(new RegExp(placeholder, 'g'), String(value))
    })
    
    return result
  }

  /**
   * 提取模板中的参数名
   */
  getParamNames(): string[] {
    const matches = this.template.match(/\{([^}]+)\}/g)
    return matches ? matches.map(match => match.slice(1, -1)) : []
  }

  /**
   * 检查是否包含指定参数
   */
  hasParam(name: string): boolean {
    return this.template.includes(`{${name}}`)
  }
}

/**
 * 创建URL模板
 */
export function createURLTemplate(template: string): URLTemplate {
  return new URLTemplate(template)
}