/**
 * 适配器相关类型定义
 */

/**
 * 框架类型枚举
 */
export enum FrameworkType {
  /** React框架 */
  REACT = 'react',
  /** Vue 2框架 */
  VUE2 = 'vue2',
  /** Vue 3框架 */
  VUE3 = 'vue3',
  /** Angular框架 */
  ANGULAR = 'angular',
  /** Svelte框架 */
  SVELTE = 'svelte',
  /** Solid框架 */
  SOLID = 'solid',
  /** Preact框架 */
  PREACT = 'preact',
  /** 原生HTML */
  VANILLA = 'vanilla',
  /** jQuery */
  JQUERY = 'jquery'
}

/**
 * 构建工具类型枚举
 */
export enum BuilderType {
  /** Webpack */
  WEBPACK = 'webpack',
  /** Vite */
  VITE = 'vite',
  /** Rollup */
  ROLLUP = 'rollup',
  /** Parcel */
  PARCEL = 'parcel',
  /** esbuild */
  ESBUILD = 'esbuild',
  /** SWC */
  SWC = 'swc',
  /** Turbopack */
  TURBOPACK = 'turbopack'
}

/**
 * 适配器状态枚举
 */
export enum AdapterStatus {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 已初始化 */
  INITIALIZED = 'initialized',
  /** 运行中 */
  RUNNING = 'running',
  /** 已停止 */
  STOPPED = 'stopped',
  /** 错误状态 */
  ERROR = 'error'
}

/**
 * 适配器配置接口
 */
export interface AdapterConfig {
  /** 适配器名称 */
  name: string
  /** 框架类型 */
  framework: FrameworkType
  /** 构建工具类型 */
  builder?: BuilderType
  /** 版本信息 */
  version?: string
  /** 入口文件 */
  entry?: string
  /** 输出目录 */
  output?: string
  /** 公共路径 */
  publicPath?: string
  /** 是否启用热更新 */
  enableHMR?: boolean
  /** 是否启用代码分割 */
  enableCodeSplitting?: boolean
  /** 是否启用Tree Shaking */
  enableTreeShaking?: boolean
  /** 环境变量 */
  env?: Record<string, string>
  /** 自定义配置 */
  customConfig?: Record<string, any>
  /** 插件配置 */
  plugins?: PluginConfig[]
  /** 加载器配置 */
  loaders?: LoaderConfig[]
}

/**
 * 插件配置接口
 */
export interface PluginConfig {
  /** 插件名称 */
  name: string
  /** 插件选项 */
  options?: Record<string, any>
  /** 是否启用 */
  enabled?: boolean
}

/**
 * 加载器配置接口
 */
export interface LoaderConfig {
  /** 加载器名称 */
  name: string
  /** 文件匹配规则 */
  test: string | RegExp
  /** 加载器选项 */
  options?: Record<string, any>
  /** 是否启用 */
  enabled?: boolean
}

/**
 * 适配器接口
 */
export interface Adapter {
  /** 适配器ID */
  id: string
  /** 适配器名称 */
  name: string
  /** 框架类型 */
  framework: FrameworkType
  /** 构建工具类型 */
  builder?: BuilderType
  /** 适配器状态 */
  status: AdapterStatus
  /** 适配器配置 */
  config: AdapterConfig
  /** 创建时间 */
  createTime: number
  
  /** 初始化适配器 */
  initialize(): Promise<void>
  /** 启动适配器 */
  start(): Promise<void>
  /** 停止适配器 */
  stop(): Promise<void>
  /** 销毁适配器 */
  destroy(): Promise<void>
  /** 构建应用 */
  build(): Promise<BuildResult>
  /** 开发模式启动 */
  dev(): Promise<DevResult>
  /** 获取构建配置 */
  getBuildConfig(): any
  /** 更新配置 */
  updateConfig(config: Partial<AdapterConfig>): void
  /** 获取统计信息 */
  getStats(): AdapterStats
}

/**
 * 构建结果接口
 */
export interface BuildResult {
  /** 是否成功 */
  success: boolean
  /** 构建时间 */
  buildTime: number
  /** 输出文件 */
  outputFiles: string[]
  /** 资源映射 */
  assets: Record<string, string>
  /** 构建统计 */
  stats: BuildStats
  /** 错误信息 */
  errors?: string[]
  /** 警告信息 */
  warnings?: string[]
}

/**
 * 开发结果接口
 */
export interface DevResult {
  /** 是否成功 */
  success: boolean
  /** 开发服务器URL */
  serverUrl: string
  /** 端口号 */
  port: number
  /** 是否启用热更新 */
  hmrEnabled: boolean
  /** 错误信息 */
  errors?: string[]
  /** 警告信息 */
  warnings?: string[]
}

/**
 * 构建统计接口
 */
export interface BuildStats {
  /** 总文件数 */
  totalFiles: number
  /** 总大小（字节） */
  totalSize: number
  /** 压缩后大小（字节） */
  compressedSize: number
  /** 构建时间（毫秒） */
  buildTime: number
  /** 各类型文件统计 */
  fileTypes: Record<string, {
    count: number
    size: number
  }>
}

/**
 * 适配器统计接口
 */
export interface AdapterStats {
  /** 构建次数 */
  buildCount: number
  /** 成功构建次数 */
  successfulBuilds: number
  /** 失败构建次数 */
  failedBuilds: number
  /** 平均构建时间 */
  avgBuildTime: number
  /** 最后构建时间 */
  lastBuildTime?: number
  /** 错误次数 */
  errorCount: number
  /** 警告次数 */
  warningCount: number
}

/**
 * React适配器配置接口
 */
export interface ReactAdapterConfig extends AdapterConfig {
  framework: FrameworkType.REACT
  /** React版本 */
  reactVersion?: string
  /** 是否启用JSX */
  enableJSX?: boolean
  /** JSX运行时 */
  jsxRuntime?: 'classic' | 'automatic'
  /** 是否启用Fast Refresh */
  enableFastRefresh?: boolean
  /** TypeScript配置 */
  typescript?: {
    enabled: boolean
    configFile?: string
  }
}

/**
 * Vue适配器配置接口
 */
export interface VueAdapterConfig extends AdapterConfig {
  framework: FrameworkType.VUE2 | FrameworkType.VUE3
  /** Vue版本 */
  vueVersion?: string
  /** 是否启用SFC */
  enableSFC?: boolean
  /** 模板编译选项 */
  templateOptions?: {
    compilerOptions?: any
    transformAssetUrls?: boolean
  }
  /** 样式处理选项 */
  styleOptions?: {
    preprocessors?: string[]
    scoped?: boolean
    modules?: boolean
  }
}

/**
 * Angular适配器配置接口
 */
export interface AngularAdapterConfig extends AdapterConfig {
  framework: FrameworkType.ANGULAR
  /** Angular版本 */
  angularVersion?: string
  /** 是否启用AOT */
  enableAOT?: boolean
  /** 是否启用Ivy */
  enableIvy?: boolean
  /** 构建优化选项 */
  optimization?: {
    bundleBudgets?: any[]
    extractLicenses?: boolean
    namedChunks?: boolean
  }
}

/**
 * 框架适配器接口
 */
export interface FrameworkAdapter extends Adapter {
  /** 框架特定的生命周期钩子 */
  lifecycleHooks: {
    beforeMount?: () => Promise<void>
    afterMount?: () => Promise<void>
    beforeUnmount?: () => Promise<void>
    afterUnmount?: () => Promise<void>
  }
  /** 获取应用实例 */
  getAppInstance(): any
  /** 挂载应用 */
  mount(container: HTMLElement): Promise<void>
  /** 卸载应用 */
  unmount(): Promise<void>
  /** 更新应用 */
  update(props?: any): Promise<void>
}

/**
 * 构建适配器接口
 */
export interface BuildAdapter extends Adapter {
  /** 监听文件变化 */
  watch(callback: (event: FileChangeEvent) => void): void
  /** 停止监听 */
  unwatch(): void
  /** 获取依赖图 */
  getDependencyGraph(): DependencyGraph
  /** 分析包大小 */
  analyzeBundleSize(): Promise<BundleAnalysis>
  /** 优化构建 */
  optimize(): Promise<OptimizationResult>
}

/**
 * 文件变化事件接口
 */
export interface FileChangeEvent {
  /** 事件类型 */
  type: 'add' | 'change' | 'unlink'
  /** 文件路径 */
  path: string
  /** 文件统计信息 */
  stats?: any
  /** 事件时间戳 */
  timestamp: number
}

/**
 * 依赖图接口
 */
export interface DependencyGraph {
  /** 节点列表 */
  nodes: DependencyNode[]
  /** 边列表 */
  edges: DependencyEdge[]
  /** 入口节点 */
  entryNodes: string[]
  /** 循环依赖 */
  circularDependencies: string[][]
}

/**
 * 依赖节点接口
 */
export interface DependencyNode {
  /** 节点ID */
  id: string
  /** 文件路径 */
  path: string
  /** 文件大小 */
  size: number
  /** 文件类型 */
  type: string
  /** 是否为入口文件 */
  isEntry: boolean
  /** 依赖数量 */
  dependencyCount: number
  /** 被依赖数量 */
  dependentCount: number
}

/**
 * 依赖边接口
 */
export interface DependencyEdge {
  /** 源节点ID */
  from: string
  /** 目标节点ID */
  to: string
  /** 依赖类型 */
  type: 'static' | 'dynamic' | 'weak'
  /** 导入语句 */
  importStatement?: string
}

/**
 * 包分析结果接口
 */
export interface BundleAnalysis {
  /** 总大小 */
  totalSize: number
  /** 压缩后大小 */
  compressedSize: number
  /** 模块列表 */
  modules: ModuleInfo[]
  /** 资源列表 */
  assets: AssetInfo[]
  /** 代码分割信息 */
  chunks: ChunkInfo[]
  /** 重复模块 */
  duplicateModules: string[]
}

/**
 * 模块信息接口
 */
export interface ModuleInfo {
  /** 模块ID */
  id: string
  /** 模块名称 */
  name: string
  /** 模块大小 */
  size: number
  /** 压缩后大小 */
  compressedSize: number
  /** 模块类型 */
  type: string
  /** 所属chunk */
  chunks: string[]
  /** 依赖模块 */
  dependencies: string[]
}

/**
 * 资源信息接口
 */
export interface AssetInfo {
  /** 资源名称 */
  name: string
  /** 资源大小 */
  size: number
  /** 压缩后大小 */
  compressedSize: number
  /** 资源类型 */
  type: string
  /** 是否为入口资源 */
  isEntry: boolean
  /** 相关chunk */
  chunks: string[]
}

/**
 * 代码块信息接口
 */
export interface ChunkInfo {
  /** 代码块ID */
  id: string
  /** 代码块名称 */
  name: string
  /** 代码块大小 */
  size: number
  /** 压缩后大小 */
  compressedSize: number
  /** 是否为入口块 */
  isEntry: boolean
  /** 是否为初始块 */
  isInitial: boolean
  /** 包含的模块 */
  modules: string[]
  /** 父代码块 */
  parents: string[]
  /** 子代码块 */
  children: string[]
}

/**
 * 优化结果接口
 */
export interface OptimizationResult {
  /** 是否成功 */
  success: boolean
  /** 优化前大小 */
  beforeSize: number
  /** 优化后大小 */
  afterSize: number
  /** 节省的大小 */
  savedSize: number
  /** 节省百分比 */
  savedPercentage: number
  /** 优化时间 */
  optimizationTime: number
  /** 应用的优化策略 */
  appliedOptimizations: string[]
  /** 优化建议 */
  suggestions: string[]
}

/**
 * 适配器工厂接口
 */
export interface AdapterFactory {
  /** 注册适配器 */
  registerAdapter(type: FrameworkType | BuilderType, adapterClass: new (config: AdapterConfig) => Adapter): void
  /** 创建适配器 */
  createAdapter(config: AdapterConfig): Promise<Adapter>
  /** 获取适配器 */
  getAdapter(type: FrameworkType | BuilderType): new (config: AdapterConfig) => Adapter | undefined
  /** 获取所有适配器 */
  getAllAdapters(): Array<{ type: FrameworkType | BuilderType; adapterClass: new (config: AdapterConfig) => Adapter }>
  /** 检测框架类型 */
  detectFramework(projectPath: string): Promise<FrameworkType | undefined>
  /** 检测构建工具类型 */
  detectBuilder(projectPath: string): Promise<BuilderType | undefined>
}

/**
 * 适配器管理器接口
 */
export interface AdapterManager {
  /** 创建适配器 */
  create(config: AdapterConfig): Promise<Adapter>
  /** 获取适配器 */
  get(id: string): Adapter | undefined
  /** 获取所有适配器 */
  getAll(): Adapter[]
  /** 启动适配器 */
  start(id: string): Promise<void>
  /** 停止适配器 */
  stop(id: string): Promise<void>
  /** 销毁适配器 */
  destroy(id: string): Promise<void>
  /** 销毁所有适配器 */
  destroyAll(): Promise<void>
  /** 获取统计信息 */
  getStats(): AdapterManagerStats
}

/**
 * 适配器管理器统计接口
 */
export interface AdapterManagerStats {
  /** 总适配器数 */
  totalAdapters: number
  /** 运行中适配器数 */
  runningAdapters: number
  /** 停止的适配器数 */
  stoppedAdapters: number
  /** 错误的适配器数 */
  errorAdapters: number
  /** 按框架类型分组统计 */
  byFramework: Record<FrameworkType, number>
  /** 按构建工具分组统计 */
  byBuilder: Record<BuilderType, number>
}

/**
 * 适配器事件类型枚举
 */
export enum AdapterEventType {
  /** 适配器创建 */
  CREATED = 'adapter:created',
  /** 适配器初始化 */
  INITIALIZED = 'adapter:initialized',
  /** 适配器启动 */
  STARTED = 'adapter:started',
  /** 适配器停止 */
  STOPPED = 'adapter:stopped',
  /** 适配器销毁 */
  DESTROYED = 'adapter:destroyed',
  /** 适配器错误 */
  ERROR = 'adapter:error',
  /** 构建开始 */
  BUILD_START = 'adapter:buildStart',
  /** 构建完成 */
  BUILD_COMPLETE = 'adapter:buildComplete',
  /** 构建失败 */
  BUILD_FAILED = 'adapter:buildFailed'
}

/**
 * 适配器事件接口
 */
export interface AdapterEvent {
  /** 事件类型 */
  type: AdapterEventType
  /** 事件目标 */
  target: Adapter
  /** 事件数据 */
  data?: any
  /** 事件时间戳 */
  timestamp: number
  /** 错误信息 */
  error?: Error
}

/**
 * 适配器插件接口
 */
export interface AdapterPlugin {
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 支持的适配器类型 */
  supportedAdapters: (FrameworkType | BuilderType)[]
  /** 插件安装 */
  install(adapter: Adapter): void | Promise<void>
  /** 插件卸载 */
  uninstall(adapter: Adapter): void | Promise<void>
  /** 插件配置 */
  configure?(options: any): void
}

/**
 * 适配器中间件接口
 */
export interface AdapterMiddleware {
  /** 中间件名称 */
  name: string
  /** 处理构建前 */
  beforeBuild?(adapter: Adapter): Promise<void>
  /** 处理构建后 */
  afterBuild?(adapter: Adapter, result: BuildResult): Promise<void>
  /** 处理开发前 */
  beforeDev?(adapter: Adapter): Promise<void>
  /** 处理开发后 */
  afterDev?(adapter: Adapter, result: DevResult): Promise<void>
  /** 处理错误 */
  onError?(adapter: Adapter, error: Error): Promise<void>
}

/**
 * 适配器配置验证器接口
 */
export interface AdapterConfigValidator {
  /** 验证配置 */
  validate(config: AdapterConfig): ValidationResult
  /** 获取默认配置 */
  getDefaultConfig(framework: FrameworkType): AdapterConfig
  /** 合并配置 */
  mergeConfig(base: AdapterConfig, override: Partial<AdapterConfig>): AdapterConfig
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 是否有效 */
  valid: boolean
  /** 错误信息 */
  errors: string[]
  /** 警告信息 */
  warnings: string[]
  /** 建议 */
  suggestions: string[]
}

/**
 * 适配器性能监控器接口
 */
export interface AdapterPerformanceMonitor {
  /** 开始监控 */
  start(adapter: Adapter): void
  /** 停止监控 */
  stop(adapter: Adapter): void
  /** 获取性能指标 */
  getMetrics(adapter: Adapter): AdapterPerformanceMetrics
  /** 重置指标 */
  reset(adapter: Adapter): void
}

/**
 * 适配器性能指标接口
 */
export interface AdapterPerformanceMetrics {
  /** 适配器ID */
  adapterId: string
  /** 初始化时间 */
  initTime: number
  /** 构建时间 */
  buildTime: number
  /** 内存使用 */
  memoryUsage: number
  /** CPU使用率 */
  cpuUsage: number
  /** 文件系统操作次数 */
  fsOperations: number
  /** 网络请求次数 */
  networkRequests: number
  /** 缓存命中率 */
  cacheHitRate: number
}

/**
 * 适配器缓存接口
 */
export interface AdapterCache {
  /** 获取缓存 */
  get<T = any>(key: string): T | undefined
  /** 设置缓存 */
  set<T = any>(key: string, value: T, ttl?: number): void
  /** 删除缓存 */
  delete(key: string): boolean
  /** 清空缓存 */
  clear(): void
  /** 获取缓存大小 */
  size(): number
  /** 获取缓存统计 */
  getStats(): CacheStats
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  /** 缓存项数量 */
  itemCount: number
  /** 缓存大小（字节） */
  size: number
  /** 命中次数 */
  hits: number
  /** 未命中次数 */
  misses: number
  /** 命中率 */
  hitRate: number
}