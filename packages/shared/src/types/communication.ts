/**
 * 通信相关类型定义
 */

/**
 * 通信类型枚举
 */
export enum CommunicationType {
  /** 事件总线 */
  EVENT_BUS = 'eventBus',
  /** 消息通道 */
  MESSAGE_CHANNEL = 'messageChannel',
  /** PostMessage */
  POST_MESSAGE = 'postMessage',
  /** 自定义事件 */
  CUSTOM_EVENT = 'customEvent',
  /** 共享状态 */
  SHARED_STATE = 'sharedState',
  /** 服务调用 */
  SERVICE_CALL = 'serviceCall'
}

/**
 * 消息类型枚举
 */
export enum MessageType {
  /** 普通消息 */
  NORMAL = 'normal',
  /** 请求消息 */
  REQUEST = 'request',
  /** 响应消息 */
  RESPONSE = 'response',
  /** 广播消息 */
  BROADCAST = 'broadcast',
  /** 系统消息 */
  SYSTEM = 'system',
  /** 错误消息 */
  ERROR = 'error'
}

/**
 * 消息优先级枚举
 */
export enum MessagePriority {
  /** 低优先级 */
  LOW = 0,
  /** 普通优先级 */
  NORMAL = 1,
  /** 高优先级 */
  HIGH = 2,
  /** 紧急优先级 */
  URGENT = 3
}

/**
 * 通信状态枚举
 */
export enum CommunicationStatus {
  /** 未连接 */
  DISCONNECTED = 'disconnected',
  /** 连接中 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 重连中 */
  RECONNECTING = 'reconnecting',
  /** 错误状态 */
  ERROR = 'error'
}

/**
 * 基础消息接口
 */
export interface BaseMessage<T = any> {
  /** 消息ID */
  id: string
  /** 消息类型 */
  type: MessageType
  /** 发送者 */
  from: string
  /** 接收者 */
  to?: string | string[]
  /** 消息主题 */
  topic?: string
  /** 消息数据 */
  data?: T
  /** 消息时间戳 */
  timestamp: number
  /** 消息优先级 */
  priority?: MessagePriority
  /** 消息过期时间 */
  expireTime?: number
  /** 是否需要确认 */
  needAck?: boolean
  /** 重试次数 */
  retryCount?: number
  /** 最大重试次数 */
  maxRetries?: number
  /** 自定义头部 */
  headers?: Record<string, any>
}

/**
 * 请求消息接口
 */
export interface RequestMessage<T = any> extends BaseMessage<T> {
  type: MessageType.REQUEST
  /** 请求方法 */
  method?: string
  /** 请求参数 */
  params?: any[]
  /** 超时时间 */
  timeout?: number
}

/**
 * 响应消息接口
 */
export interface ResponseMessage<T = any> extends BaseMessage<T> {
  type: MessageType.RESPONSE
  /** 请求ID */
  requestId: string
  /** 是否成功 */
  success: boolean
  /** 错误信息 */
  error?: string
  /** 错误代码 */
  errorCode?: string | number
}

/**
 * 广播消息接口
 */
export interface BroadcastMessage<T = any> extends BaseMessage<T> {
  type: MessageType.BROADCAST
  /** 广播范围 */
  scope?: 'all' | 'group' | 'custom'
  /** 目标组 */
  targetGroup?: string
  /** 排除目标 */
  exclude?: string[]
}

/**
 * 系统消息接口
 */
export interface SystemMessage<T = any> extends BaseMessage<T> {
  type: MessageType.SYSTEM
  /** 系统事件类型 */
  eventType: string
  /** 系统级别 */
  level?: 'info' | 'warn' | 'error' | 'fatal'
}

/**
 * 错误消息接口
 */
export interface ErrorMessage extends BaseMessage {
  type: MessageType.ERROR
  /** 错误对象 */
  error: Error
  /** 错误上下文 */
  context?: any
  /** 错误堆栈 */
  stack?: string
}

/**
 * 消息联合类型
 */
export type Message = BaseMessage | RequestMessage | ResponseMessage | BroadcastMessage | SystemMessage | ErrorMessage

/**
 * 消息处理器类型
 */
export type MessageHandler<T = any> = (message: T) => void | Promise<void>

/**
 * 消息过滤器类型
 */
export type MessageFilter<T = any> = (message: T) => boolean

/**
 * 消息转换器类型
 */
export type MessageTransformer<T = any, R = any> = (message: T) => R

/**
 * 消息中间件类型
 */
export type MessageMiddleware<T = any> = (message: T, next: () => void) => void | Promise<void>

/**
 * 通信配置接口
 */
export interface CommunicationConfig {
  /** 通信类型 */
  type: CommunicationType
  /** 是否启用 */
  enabled?: boolean
  /** 最大连接数 */
  maxConnections?: number
  /** 连接超时时间 */
  connectionTimeout?: number
  /** 消息超时时间 */
  messageTimeout?: number
  /** 重连间隔 */
  reconnectInterval?: number
  /** 最大重连次数 */
  maxReconnectAttempts?: number
  /** 是否启用消息队列 */
  enableMessageQueue?: boolean
  /** 消息队列大小 */
  messageQueueSize?: number
  /** 是否启用消息持久化 */
  enablePersistence?: boolean
  /** 是否启用消息压缩 */
  enableCompression?: boolean
  /** 是否启用消息加密 */
  enableEncryption?: boolean
  /** 加密密钥 */
  encryptionKey?: string
  /** 自定义配置 */
  [key: string]: any
}

/**
 * 通信通道接口
 */
export interface CommunicationChannel {
  /** 通道ID */
  id: string
  /** 通道名称 */
  name: string
  /** 通道类型 */
  type: CommunicationType
  /** 通道状态 */
  status: CommunicationStatus
  /** 通道配置 */
  config: CommunicationConfig
  /** 创建时间 */
  createTime: number
  /** 连接时间 */
  connectTime?: number
  
  /** 连接通道 */
  connect(): Promise<void>
  /** 断开通道 */
  disconnect(): Promise<void>
  /** 发送消息 */
  send(message: Message): Promise<void>
  /** 接收消息 */
  receive(handler: MessageHandler): void
  /** 订阅主题 */
  subscribe(topic: string, handler: MessageHandler): () => void
  /** 取消订阅 */
  unsubscribe(topic: string, handler?: MessageHandler): void
  /** 广播消息 */
  broadcast(message: BroadcastMessage): Promise<void>
  /** 请求响应 */
  request<T = any, R = any>(message: RequestMessage<T>): Promise<ResponseMessage<R>>
  /** 添加中间件 */
  use(middleware: MessageMiddleware): void
  /** 添加过滤器 */
  filter(filter: MessageFilter): void
  /** 获取统计信息 */
  getStats(): ChannelStats
}

/**
 * 通道统计信息接口
 */
export interface ChannelStats {
  /** 发送消息数 */
  sentMessages: number
  /** 接收消息数 */
  receivedMessages: number
  /** 失败消息数 */
  failedMessages: number
  /** 平均响应时间 */
  avgResponseTime: number
  /** 连接时长 */
  connectionDuration: number
  /** 错误次数 */
  errorCount: number
}

/**
 * 事件总线接口
 */
export interface EventBus {
  /** 发布事件 */
  emit(event: string, data?: any): void
  /** 订阅事件 */
  on(event: string, handler: MessageHandler): () => void
  /** 订阅一次事件 */
  once(event: string, handler: MessageHandler): () => void
  /** 取消订阅 */
  off(event: string, handler?: MessageHandler): void
  /** 清空所有订阅 */
  clear(): void
  /** 获取订阅者数量 */
  listenerCount(event: string): number
  /** 获取所有事件 */
  eventNames(): string[]
}

/**
 * 消息队列接口
 */
export interface MessageQueue {
  /** 入队消息 */
  enqueue(message: Message): void
  /** 出队消息 */
  dequeue(): Message | undefined
  /** 查看队首消息 */
  peek(): Message | undefined
  /** 队列大小 */
  size(): number
  /** 是否为空 */
  isEmpty(): boolean
  /** 清空队列 */
  clear(): void
  /** 按优先级排序 */
  sort(): void
}

/**
 * 消息路由器接口
 */
export interface MessageRouter {
  /** 添加路由规则 */
  addRoute(pattern: string | RegExp, handler: MessageHandler): void
  /** 移除路由规则 */
  removeRoute(pattern: string | RegExp): void
  /** 路由消息 */
  route(message: Message): void
  /** 获取所有路由 */
  getRoutes(): Array<{ pattern: string | RegExp; handler: MessageHandler }>
}

/**
 * 消息代理接口
 */
export interface MessageBroker {
  /** 注册通道 */
  registerChannel(channel: CommunicationChannel): void
  /** 注销通道 */
  unregisterChannel(channelId: string): void
  /** 获取通道 */
  getChannel(channelId: string): CommunicationChannel | undefined
  /** 获取所有通道 */
  getAllChannels(): CommunicationChannel[]
  /** 转发消息 */
  forward(message: Message, targetChannels?: string[]): Promise<void>
  /** 广播消息 */
  broadcast(message: BroadcastMessage): Promise<void>
  /** 启动代理 */
  start(): Promise<void>
  /** 停止代理 */
  stop(): Promise<void>
}

/**
 * 通信管理器接口
 */
export interface CommunicationManager {
  /** 创建通道 */
  createChannel(config: CommunicationConfig): Promise<CommunicationChannel>
  /** 销毁通道 */
  destroyChannel(channelId: string): Promise<void>
  /** 获取通道 */
  getChannel(channelId: string): CommunicationChannel | undefined
  /** 获取所有通道 */
  getAllChannels(): CommunicationChannel[]
  /** 发送消息 */
  send(channelId: string, message: Message): Promise<void>
  /** 广播消息 */
  broadcast(message: BroadcastMessage, channels?: string[]): Promise<void>
  /** 请求响应 */
  request<T = any, R = any>(channelId: string, message: RequestMessage<T>): Promise<ResponseMessage<R>>
  /** 订阅消息 */
  subscribe(channelId: string, topic: string, handler: MessageHandler): () => void
  /** 取消订阅 */
  unsubscribe(channelId: string, topic: string, handler?: MessageHandler): void
  /** 获取统计信息 */
  getStats(): CommunicationStats
}

/**
 * 通信统计信息接口
 */
export interface CommunicationStats {
  /** 总通道数 */
  totalChannels: number
  /** 活跃通道数 */
  activeChannels: number
  /** 总消息数 */
  totalMessages: number
  /** 成功消息数 */
  successMessages: number
  /** 失败消息数 */
  failedMessages: number
  /** 平均响应时间 */
  avgResponseTime: number
  /** 错误率 */
  errorRate: number
  /** 吞吐量（消息/秒） */
  throughput: number
}

/**
 * 消息序列化器接口
 */
export interface MessageSerializer {
  /** 序列化消息 */
  serialize(message: Message): string | ArrayBuffer
  /** 反序列化消息 */
  deserialize(data: string | ArrayBuffer): Message
}

/**
 * 消息压缩器接口
 */
export interface MessageCompressor {
  /** 压缩消息 */
  compress(data: string | ArrayBuffer): Promise<ArrayBuffer>
  /** 解压消息 */
  decompress(data: ArrayBuffer): Promise<string | ArrayBuffer>
}

/**
 * 消息加密器接口
 */
export interface MessageEncryptor {
  /** 加密消息 */
  encrypt(data: string | ArrayBuffer, key: string): Promise<ArrayBuffer>
  /** 解密消息 */
  decrypt(data: ArrayBuffer, key: string): Promise<string | ArrayBuffer>
}

/**
 * Worker配置接口
 */
export interface WorkerConfig extends Omit<CommunicationConfig, 'type'> {
  /** Worker类型 */
  type: 'dedicated' | 'shared' | 'service'
  /** 通信类型 */
  communicationType?: CommunicationType
  /** Worker脚本URL */
  scriptUrl: string
  /** Worker选项 */
  options?: WorkerOptions
}

/**
 * 消息监控器接口
 */
export interface MessageMonitor {
  /** 开始监控 */
  start(): void
  /** 停止监控 */
  stop(): void
  /** 记录消息 */
  record(message: Message, direction: 'in' | 'out'): void
  /** 获取监控数据 */
  getMetrics(): MessageMetrics
  /** 重置监控数据 */
  reset(): void
}

/**
 * 消息指标接口
 */
export interface MessageMetrics {
  /** 消息总数 */
  totalCount: number
  /** 入站消息数 */
  inboundCount: number
  /** 出站消息数 */
  outboundCount: number
  /** 错误消息数 */
  errorCount: number
  /** 平均消息大小 */
  avgMessageSize: number
  /** 消息频率（消息/秒） */
  messageRate: number
  /** 错误率 */
  errorRate: number
  /** 延迟统计 */
  latency: {
    min: number
    max: number
    avg: number
    p50: number
    p90: number
    p99: number
  }
}

/**
 * 通信事件类型
 */
export enum CommunicationEventType {
  /** 通道连接 */
  CHANNEL_CONNECTED = 'channel:connected',
  /** 通道断开 */
  CHANNEL_DISCONNECTED = 'channel:disconnected',
  /** 通道错误 */
  CHANNEL_ERROR = 'channel:error',
  /** 消息发送 */
  MESSAGE_SENT = 'message:sent',
  /** 消息接收 */
  MESSAGE_RECEIVED = 'message:received',
  /** 消息失败 */
  MESSAGE_FAILED = 'message:failed',
  /** 消息超时 */
  MESSAGE_TIMEOUT = 'message:timeout'
}

/**
 * 通信事件接口
 */
export interface CommunicationEvent {
  /** 事件类型 */
  type: CommunicationEventType
  /** 事件目标 */
  target: CommunicationChannel | Message
  /** 事件数据 */
  data?: any
  /** 事件时间戳 */
  timestamp: number
  /** 错误信息 */
  error?: Error
}

/**
 * 跨域通信配置接口
 */
export interface CrossOriginConfig {
  /** 允许的源 */
  allowedOrigins: string[]
  /** 允许的方法 */
  allowedMethods?: string[]
  /** 允许的头部 */
  allowedHeaders?: string[]
  /** 是否允许凭证 */
  allowCredentials?: boolean
  /** 预检请求缓存时间 */
  maxAge?: number
}

/**
 * WebSocket通信配置接口
 */
export interface WebSocketConfig extends CommunicationConfig {
  /** WebSocket URL */
  url: string
  /** 协议 */
  protocols?: string | string[]
  /** 心跳间隔 */
  heartbeatInterval?: number
  /** 心跳超时 */
  heartbeatTimeout?: number
  /** 是否自动重连 */
  autoReconnect?: boolean
}

/**
 * Worker通信配置接口
 */
export interface WorkerConfig extends CommunicationConfig {
  /** Worker脚本路径 */
  scriptPath: string
  /** Worker类型 */
  type?: 'dedicated' | 'shared' | 'service'
  /** Worker选项 */
  options?: WorkerOptions
}

/**
 * SharedWorker通信配置接口
 */
export interface SharedWorkerConfig extends WorkerConfig {
  type: 'shared'
  /** 端口名称 */
  portName?: string
}

/**
 * ServiceWorker通信配置接口
 */
export interface ServiceWorkerConfig extends WorkerConfig {
  type: 'service'
  /** 作用域 */
  scope?: string
  /** 更新策略 */
  updateViaCache?: 'imports' | 'all' | 'none'
}

/**
 * BroadcastChannel通信配置接口
 */
export interface BroadcastChannelConfig extends CommunicationConfig {
  /** 通道名称 */
  channelName: string
}

/**
 * 消息传递选项接口
 */
export interface MessageOptions {
  /** 传输对象 */
  transfer?: Transferable[]
  /** 是否克隆 */
  clone?: boolean
  /** 序列化器 */
  serializer?: MessageSerializer
  /** 压缩器 */
  compressor?: MessageCompressor
  /** 加密器 */
  encryptor?: MessageEncryptor
}

/**
 * 通信适配器接口
 */
export interface CommunicationAdapter {
  /** 适配器名称 */
  name: string
  /** 适配器类型 */
  type: CommunicationType
  /** 是否支持当前环境 */
  isSupported(): boolean
  /** 创建通道 */
  createChannel(config: CommunicationConfig): Promise<CommunicationChannel>
  /** 销毁通道 */
  destroyChannel(channel: CommunicationChannel): Promise<void>
}

/**
 * 通信工厂接口
 */
export interface CommunicationFactory {
  /** 注册适配器 */
  registerAdapter(adapter: CommunicationAdapter): void
  /** 获取适配器 */
  getAdapter(type: CommunicationType): CommunicationAdapter | undefined
  /** 获取所有适配器 */
  getAllAdapters(): CommunicationAdapter[]
  /** 创建通道 */
  createChannel(config: CommunicationConfig): Promise<CommunicationChannel>
  /** 选择最优适配器 */
  selectOptimalAdapter(requirements?: Partial<CommunicationConfig>): CommunicationType
}