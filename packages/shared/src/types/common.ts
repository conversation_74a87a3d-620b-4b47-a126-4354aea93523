/**
 * 通用类型定义
 */

/**
 * 基础配置接口
 */
export interface BaseConfig {
  /** 名称 */
  name: string
  /** 版本 */
  version?: string
  /** 描述 */
  description?: string
  /** 是否启用 */
  enabled?: boolean
  /** 自定义属性 */
  [key: string]: any
}

/**
 * 生命周期钩子函数类型
 */
export type LifecycleHook = () => Promise<void> | void

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void | Promise<void>

/**
 * 错误处理器类型
 */
export type ErrorHandler = (error: Error, context?: any) => void

/**
 * 异步函数类型
 */
export type AsyncFunction<T = any, R = any> = (...args: T[]) => Promise<R>

/**
 * 回调函数类型
 */
export type Callback<T = any, R = void> = (data: T) => R

/**
 * 可选的回调函数类型
 */
export type OptionalCallback<T = any, R = void> = Callback<T, R> | undefined

/**
 * 键值对类型
 */
export type KeyValuePair<K = string, V = any> = {
  key: K
  value: V
}

/**
 * 字典类型
 */
export type Dictionary<T = any> = Record<string, T>

/**
 * 可为空的类型
 */
export type Nullable<T> = T | null

/**
 * 可选的类型
 */
export type Optional<T> = T | undefined

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * 提取函数参数类型
 */
export type ExtractArgs<T> = T extends (...args: infer A) => any ? A : never

/**
 * 提取函数返回类型
 */
export type ExtractReturn<T> = T extends (...args: any[]) => infer R ? R : never

/**
 * 提取Promise类型
 */
export type ExtractPromise<T> = T extends Promise<infer U> ? U : T

/**
 * 联合类型转交集类型
 */
export type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never

/**
 * 获取对象的值类型
 */
export type ValueOf<T> = T[keyof T]

/**
 * 条件类型
 */
export type If<C extends boolean, T, F> = C extends true ? T : F

/**
 * 非空类型
 */
export type NonNullable<T> = T extends null | undefined ? never : T

/**
 * 数组元素类型
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never

/**
 * 构造函数类型
 */
export type Constructor<T = {}> = new (...args: any[]) => T

/**
 * 抽象构造函数类型
 */
export type AbstractConstructor<T = {}> = abstract new (...args: any[]) => T

/**
 * 类类型
 */
export type Class<T = {}> = Constructor<T>

/**
 * 混入类型
 */
export type Mixin<T extends Constructor> = T & Constructor

/**
 * 时间戳类型
 */
export type Timestamp = number

/**
 * URL类型
 */
export type URL = string

/**
 * HTML字符串类型
 */
export type HTMLString = string

/**
 * CSS字符串类型
 */
export type CSSString = string

/**
 * JavaScript代码字符串类型
 */
export type JSString = string

/**
 * JSON字符串类型
 */
export type JSONString = string

/**
 * 文件路径类型
 */
export type FilePath = string

/**
 * 目录路径类型
 */
export type DirectoryPath = string

/**
 * MIME类型
 */
export type MimeType = string

/**
 * 颜色值类型
 */
export type Color = string

/**
 * 尺寸类型
 */
export interface Size {
  width: number
  height: number
}

/**
 * 位置类型
 */
export interface Position {
  x: number
  y: number
}

/**
 * 矩形区域类型
 */
export interface Rectangle extends Position, Size {}

/**
 * 范围类型
 */
export interface Range<T = number> {
  min: T
  max: T
}

/**
 * 分页信息类型
 */
export interface Pagination {
  /** 当前页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 总数量 */
  total?: number
  /** 总页数 */
  totalPages?: number
}

/**
 * 排序信息类型
 */
export interface Sort {
  /** 排序字段 */
  field: string
  /** 排序方向 */
  order: 'asc' | 'desc'
}

/**
 * 查询条件类型
 */
export interface Query {
  /** 分页信息 */
  pagination?: Pagination
  /** 排序信息 */
  sort?: Sort[]
  /** 过滤条件 */
  filters?: Dictionary<any>
  /** 搜索关键词 */
  keyword?: string
}

/**
 * API响应类型
 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 错误信息 */
  message?: string
  /** 错误代码 */
  code?: string | number
  /** 时间戳 */
  timestamp?: Timestamp
}

/**
 * 分页响应类型
 */
export interface PagedResponse<T = any> extends ApiResponse<T[]> {
  /** 分页信息 */
  pagination: Pagination
}

/**
 * 文件信息类型
 */
export interface FileInfo {
  /** 文件名 */
  name: string
  /** 文件大小（字节） */
  size: number
  /** 文件类型 */
  type: MimeType
  /** 最后修改时间 */
  lastModified: Timestamp
  /** 文件路径 */
  path?: FilePath
  /** 文件内容（可选） */
  content?: string | ArrayBuffer
}

/**
 * 环境信息类型
 */
export interface Environment {
  /** 是否为开发环境 */
  isDevelopment: boolean
  /** 是否为生产环境 */
  isProduction: boolean
  /** 是否为测试环境 */
  isTest: boolean
  /** 浏览器信息 */
  browser?: {
    name?: string
    version?: string
    userAgent?: string
  }
  /** 操作系统信息 */
  os?: {
    name?: string
    version?: string
    platform?: string
  }
  /** 设备信息 */
  device?: {
    type: 'desktop' | 'mobile' | 'tablet'
    isMobile: boolean
    isTablet: boolean
    isDesktop: boolean
  }
}

/**
 * 性能指标类型
 */
export interface PerformanceMetrics {
  /** 开始时间 */
  startTime: number
  /** 结束时间 */
  endTime?: number
  /** 持续时间 */
  duration?: number
  /** 内存使用情况 */
  memory?: {
    used: number
    total: number
    limit: number
  }
  /** 自定义标记 */
  marks?: Dictionary<number>
  /** 自定义测量 */
  measures?: Dictionary<number>
}

/**
 * 缓存项类型
 */
export interface CacheItem<T = any> {
  /** 缓存键 */
  key: string
  /** 缓存值 */
  value: T
  /** 过期时间 */
  expireTime?: Timestamp
  /** 创建时间 */
  createTime: Timestamp
  /** 访问次数 */
  accessCount?: number
  /** 最后访问时间 */
  lastAccessTime?: Timestamp
}

/**
 * 事件类型
 */
export interface BaseEvent<T = any> {
  /** 事件类型 */
  type: string
  /** 事件数据 */
  data?: T
  /** 事件时间戳 */
  timestamp: Timestamp
  /** 事件源 */
  source?: string
  /** 是否可取消 */
  cancelable?: boolean
  /** 是否已取消 */
  cancelled?: boolean
}

/**
 * 配置变更事件类型
 */
export interface ConfigChangeEvent extends BaseEvent {
  type: 'config:change'
  data: {
    key: string
    oldValue: any
    newValue: any
  }
}

/**
 * 状态变更事件类型
 */
export interface StateChangeEvent extends BaseEvent {
  type: 'state:change'
  data: {
    from: string
    to: string
    context?: any
  }
}

/**
 * 错误事件类型
 */
export interface ErrorEvent extends BaseEvent {
  type: 'error'
  data: {
    error: Error
    context?: any
  }
}

/**
 * 通用事件类型联合
 */
export type CommonEvent = ConfigChangeEvent | StateChangeEvent | ErrorEvent | BaseEvent

/**
 * 工具函数类型
 */
export interface Utils {
  /** 深度克隆 */
  deepClone<T>(obj: T): T
  /** 深度合并 */
  deepMerge<T>(...objects: Partial<T>[]): T
  /** 防抖函数 */
  debounce<T extends (...args: any[]) => any>(fn: T, delay: number): T
  /** 节流函数 */
  throttle<T extends (...args: any[]) => any>(fn: T, delay: number): T
  /** 生成唯一ID */
  generateId(): string
  /** 格式化日期 */
  formatDate(date: Date, format: string): string
  /** 解析查询字符串 */
  parseQuery(query: string): Dictionary<string>
  /** 构建查询字符串 */
  buildQuery(params: Dictionary<any>): string
}

/**
 * 验证器类型
 */
export interface Validator<T = any> {
  /** 验证函数 */
  validate(value: T): boolean | Promise<boolean>
  /** 错误消息 */
  message: string
}

/**
 * 序列化器类型
 */
export interface Serializer<T = any> {
  /** 序列化 */
  serialize(data: T): string
  /** 反序列化 */
  deserialize(data: string): T
}

/**
 * 存储适配器类型
 */
export interface StorageAdapter {
  /** 获取值 */
  getItem(key: string): string | null
  /** 设置值 */
  setItem(key: string, value: string): void
  /** 移除值 */
  removeItem(key: string): void
  /** 清空存储 */
  clear(): void
  /** 获取所有键 */
  keys(): string[]
}

/**
 * 日志级别类型
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal'

/**
 * 日志条目类型
 */
export interface LogEntry {
  /** 时间戳 */
  timestamp: Timestamp
  /** 日志级别 */
  level: LogLevel
  /** 日志消息 */
  message: string
  /** 日志标签 */
  tag?: string
  /** 额外数据 */
  data?: any
  /** 错误对象 */
  error?: Error
  /** 调用栈 */
  stack?: string
}

/**
 * 任务类型
 */
export interface Task<T = any> {
  /** 任务ID */
  id: string
  /** 任务名称 */
  name: string
  /** 任务执行函数 */
  execute: () => Promise<T> | T
  /** 任务优先级 */
  priority?: number
  /** 任务状态 */
  status?: 'pending' | 'running' | 'completed' | 'failed'
  /** 创建时间 */
  createTime: Timestamp
  /** 开始时间 */
  startTime?: Timestamp
  /** 结束时间 */
  endTime?: Timestamp
  /** 任务结果 */
  result?: T
  /** 任务错误 */
  error?: Error
}

/**
 * 队列类型
 */
export interface Queue<T = any> {
  /** 入队 */
  enqueue(item: T): void
  /** 出队 */
  dequeue(): T | undefined
  /** 查看队首 */
  peek(): T | undefined
  /** 队列大小 */
  size(): number
  /** 是否为空 */
  isEmpty(): boolean
  /** 清空队列 */
  clear(): void
}

/**
 * 栈类型
 */
export interface Stack<T = any> {
  /** 入栈 */
  push(item: T): void
  /** 出栈 */
  pop(): T | undefined
  /** 查看栈顶 */
  peek(): T | undefined
  /** 栈大小 */
  size(): number
  /** 是否为空 */
  isEmpty(): boolean
  /** 清空栈 */
  clear(): void
}

/**
 * 观察者模式接口
 */
export interface Observer<T = any> {
  /** 更新方法 */
  update(data: T): void
}

/**
 * 被观察者模式接口
 */
export interface Observable<T = any> {
  /** 添加观察者 */
  addObserver(observer: Observer<T>): void
  /** 移除观察者 */
  removeObserver(observer: Observer<T>): void
  /** 通知观察者 */
  notifyObservers(data: T): void
}

/**
 * 发布订阅模式接口
 */
export interface PubSub<T = any> {
  /** 订阅 */
  subscribe(event: string, callback: EventListener<T>): () => void
  /** 取消订阅 */
  unsubscribe(event: string, callback?: EventListener<T>): void
  /** 发布 */
  publish(event: string, data?: T): void
  /** 清空订阅 */
  clear(): void
}

/**
 * 状态机接口
 */
export interface StateMachine<S = string, E = string> {
  /** 当前状态 */
  currentState: S
  /** 状态转换 */
  transition(event: E): boolean
  /** 是否可以转换 */
  canTransition(event: E): boolean
  /** 获取所有状态 */
  getStates(): S[]
  /** 获取所有事件 */
  getEvents(): E[]
}

/**
 * 插件接口
 */
export interface Plugin {
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 插件描述 */
  description?: string
  /** 插件依赖 */
  dependencies?: string[]
  /** 插件安装 */
  install?(context: any): void | Promise<void>
  /** 插件卸载 */
  uninstall?(context: any): void | Promise<void>
  /** 插件启用 */
  enable?(context: any): void | Promise<void>
  /** 插件禁用 */
  disable?(context: any): void | Promise<void>
}

/**
 * 中间件接口
 */
export interface Middleware<T = any, R = any> {
  /** 中间件处理函数 */
  handle(context: T, next: () => Promise<R>): Promise<R>
}

/**
 * 过滤器接口
 */
export interface Filter<T = any> {
  /** 过滤函数 */
  filter(item: T): boolean
}

/**
 * 转换器接口
 */
export interface Transformer<T = any, R = any> {
  /** 转换函数 */
  transform(input: T): R
}

/**
 * 比较器接口
 */
export interface Comparator<T = any> {
  /** 比较函数 */
  compare(a: T, b: T): number
}

/**
 * 迭代器接口
 */
export interface Iterator<T = any> {
  /** 是否有下一个 */
  hasNext(): boolean
  /** 获取下一个 */
  next(): T
  /** 重置 */
  reset(): void
}

/**
 * 可迭代接口
 */
export interface Iterable<T = any> {
  /** 获取迭代器 */
  iterator(): Iterator<T>
}