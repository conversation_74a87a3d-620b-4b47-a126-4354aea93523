/**
 * 插件相关类型定义
 */

/**
 * 插件类型枚举
 */
export enum PluginType {
  /** 核心插件 */
  CORE = 'core',
  /** 路由插件 */
  ROUTER = 'router',
  /** 通信插件 */
  COMMUNICATION = 'communication',
  /** 认证插件 */
  AUTH = 'auth',
  /** 开发工具插件 */
  DEV_TOOLS = 'devTools',
  /** 性能插件 */
  PERFORMANCE = 'performance',
  /** 错误处理插件 */
  ERROR_HANDLER = 'errorHandler',
  /** 自定义插件 */
  CUSTOM = 'custom'
}

/**
 * 插件状态枚举
 */
export enum PluginStatus {
  /** 未安装 */
  UNINSTALLED = 'uninstalled',
  /** 安装中 */
  INSTALLING = 'installing',
  /** 已安装 */
  INSTALLED = 'installed',
  /** 启用中 */
  ENABLING = 'enabling',
  /** 已启用 */
  ENABLED = 'enabled',
  /** 禁用中 */
  DISABLING = 'disabling',
  /** 已禁用 */
  DISABLED = 'disabled',
  /** 卸载中 */
  UNINSTALLING = 'uninstalling',
  /** 错误状态 */
  ERROR = 'error'
}

/**
 * 插件优先级枚举
 */
export enum PluginPriority {
  /** 最高优先级 */
  HIGHEST = 100,
  /** 高优先级 */
  HIGH = 75,
  /** 普通优先级 */
  NORMAL = 50,
  /** 低优先级 */
  LOW = 25,
  /** 最低优先级 */
  LOWEST = 0
}

/**
 * 插件事件类型枚举
 */
export enum PluginEventType {
  /** 插件安装 */
  INSTALLED = 'plugin:installed',
  /** 插件卸载 */
  UNINSTALLED = 'plugin:uninstalled',
  /** 插件启用 */
  ENABLED = 'plugin:enabled',
  /** 插件禁用 */
  DISABLED = 'plugin:disabled',
  /** 插件错误 */
  ERROR = 'plugin:error',
  /** 插件更新 */
  UPDATED = 'plugin:updated',
  /** 插件配置变更 */
  CONFIG_CHANGED = 'plugin:configChanged'
}

/**
 * 插件配置接口
 */
export interface PluginConfig {
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 插件描述 */
  description?: string
  /** 插件作者 */
  author?: string
  /** 插件主页 */
  homepage?: string
  /** 插件类型 */
  type: PluginType
  /** 插件优先级 */
  priority?: PluginPriority
  /** 插件依赖 */
  dependencies?: string[]
  /** 插件冲突 */
  conflicts?: string[]
  /** 最小核心版本 */
  minCoreVersion?: string
  /** 最大核心版本 */
  maxCoreVersion?: string
  /** 支持的环境 */
  supportedEnvironments?: string[]
  /** 插件选项 */
  options?: Record<string, any>
  /** 是否自动启用 */
  autoEnable?: boolean
  /** 是否必需插件 */
  required?: boolean
  /** 插件标签 */
  tags?: string[]
  /** 插件许可证 */
  license?: string
}

/**
 * 插件元数据接口
 */
export interface PluginMetadata extends PluginConfig {
  /** 插件ID */
  id: string
  /** 插件路径 */
  path: string
  /** 插件入口文件 */
  entry: string
  /** 插件大小 */
  size: number
  /** 安装时间 */
  installTime: number
  /** 最后更新时间 */
  lastUpdateTime?: number
  /** 插件哈希值 */
  hash: string
  /** 插件签名 */
  signature?: string
}

/**
 * 插件接口
 */
export interface Plugin {
  /** 插件元数据 */
  metadata: PluginMetadata
  /** 插件状态 */
  status: PluginStatus
  /** 插件实例 */
  instance?: any
  /** 插件上下文 */
  context?: PluginContext
  
  /** 安装插件 */
  install(context: PluginContext): Promise<void>
  /** 卸载插件 */
  uninstall(context: PluginContext): Promise<void>
  /** 启用插件 */
  enable(context: PluginContext): Promise<void>
  /** 禁用插件 */
  disable(context: PluginContext): Promise<void>
  /** 更新插件 */
  update?(newVersion: string): Promise<void>
  /** 配置插件 */
  configure?(options: Record<string, any>): Promise<void>
  /** 获取插件信息 */
  getInfo(): PluginInfo
  /** 验证插件 */
  validate?(): Promise<ValidationResult>
}

/**
 * 插件上下文接口
 */
export interface PluginContext {
  /** 核心实例 */
  core: any
  /** 插件管理器 */
  pluginManager: any
  /** 事件总线 */
  eventBus: any
  /** 日志记录器 */
  logger: any
  /** 配置管理器 */
  config: any
  /** 工具函数 */
  utils: any
  /** 插件API */
  api: PluginAPI
  /** 插件存储 */
  storage: any
  /** 插件缓存 */
  cache: any
}

/**
 * 插件API接口
 */
export interface PluginAPI {
  /** 注册钩子 */
  registerHook(name: string, handler: Function): void
  /** 调用钩子 */
  callHook(name: string, ...args: any[]): Promise<any>
  /** 注册服务 */
  registerService(name: string, service: any): void
  /** 获取服务 */
  getService(name: string): any
  /** 注册中间件 */
  registerMiddleware(name: string, middleware: Function): void
  /** 注册过滤器 */
  registerFilter(name: string, filter: Function): void
  /** 发送事件 */
  emit(event: string, data?: any): void
  /** 监听事件 */
  on(event: string, handler: Function): () => void
  /** 获取配置 */
  getConfig(key?: string): any
  /** 设置配置 */
  setConfig(key: string, value: any): void
}

/**
 * 插件信息接口
 */
export interface PluginInfo {
  /** 插件元数据 */
  metadata: PluginMetadata
  /** 插件状态 */
  status: PluginStatus
  /** 运行时信息 */
  runtime: {
    /** 启动时间 */
    startTime?: number
    /** 运行时长 */
    uptime?: number
    /** 内存使用 */
    memoryUsage?: number
    /** 错误次数 */
    errorCount: number
    /** 最后错误 */
    lastError?: Error
  }
  /** 统计信息 */
  stats: {
    /** 调用次数 */
    callCount: number
    /** 平均响应时间 */
    avgResponseTime: number
    /** 成功次数 */
    successCount: number
    /** 失败次数 */
    failureCount: number
  }
}

/**
 * 插件钩子接口
 */
export interface PluginHook {
  /** 钩子名称 */
  name: string
  /** 钩子类型 */
  type: 'sync' | 'async' | 'parallel' | 'waterfall'
  /** 钩子处理器 */
  handlers: PluginHookHandler[]
  /** 钩子优先级 */
  priority: number
  /** 是否可取消 */
  cancelable?: boolean
}

/**
 * 插件钩子处理器接口
 */
export interface PluginHookHandler {
  /** 处理器ID */
  id: string
  /** 插件ID */
  pluginId: string
  /** 处理器函数 */
  handler: Function
  /** 处理器优先级 */
  priority: number
  /** 是否启用 */
  enabled: boolean
}

/**
 * 插件管理器接口
 */
export interface PluginManager {
  /** 安装插件 */
  install(pluginPath: string, options?: InstallOptions): Promise<Plugin>
  /** 卸载插件 */
  uninstall(pluginId: string): Promise<void>
  /** 启用插件 */
  enable(pluginId: string): Promise<void>
  /** 禁用插件 */
  disable(pluginId: string): Promise<void>
  /** 获取插件 */
  get(pluginId: string): Plugin | undefined
  /** 获取所有插件 */
  getAll(): Plugin[]
  /** 获取已启用插件 */
  getEnabled(): Plugin[]
  /** 获取已禁用插件 */
  getDisabled(): Plugin[]
  /** 搜索插件 */
  search(query: PluginSearchQuery): Plugin[]
  /** 更新插件 */
  update(pluginId: string, newVersion?: string): Promise<void>
  /** 重载插件 */
  reload(pluginId: string): Promise<void>
  /** 验证插件 */
  validate(pluginPath: string): Promise<ValidationResult>
  /** 获取插件依赖 */
  getDependencies(pluginId: string): string[]
  /** 解析依赖冲突 */
  resolveDependencies(pluginIds: string[]): Promise<string[]>
  /** 获取统计信息 */
  getStats(): PluginManagerStats
}

/**
 * 安装选项接口
 */
export interface InstallOptions {
  /** 是否强制安装 */
  force?: boolean
  /** 是否自动启用 */
  autoEnable?: boolean
  /** 是否跳过依赖检查 */
  skipDependencyCheck?: boolean
  /** 是否跳过冲突检查 */
  skipConflictCheck?: boolean
  /** 安装超时时间 */
  timeout?: number
  /** 自定义选项 */
  customOptions?: Record<string, any>
}

/**
 * 插件搜索查询接口
 */
export interface PluginSearchQuery {
  /** 搜索关键词 */
  keyword?: string
  /** 插件类型 */
  type?: PluginType
  /** 插件状态 */
  status?: PluginStatus
  /** 插件标签 */
  tags?: string[]
  /** 作者 */
  author?: string
  /** 版本范围 */
  versionRange?: string
  /** 排序方式 */
  sortBy?: 'name' | 'version' | 'installTime' | 'priority'
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
  /** 分页信息 */
  pagination?: {
    page: number
    pageSize: number
  }
}

/**
 * 插件管理器统计接口
 */
export interface PluginManagerStats {
  /** 总插件数 */
  totalPlugins: number
  /** 已安装插件数 */
  installedPlugins: number
  /** 已启用插件数 */
  enabledPlugins: number
  /** 已禁用插件数 */
  disabledPlugins: number
  /** 错误插件数 */
  errorPlugins: number
  /** 按类型分组统计 */
  byType: Record<PluginType, number>
  /** 按状态分组统计 */
  byStatus: Record<PluginStatus, number>
  /** 内存使用情况 */
  memoryUsage: {
    total: number
    average: number
    max: number
  }
  /** 性能统计 */
  performance: {
    avgInstallTime: number
    avgEnableTime: number
    avgCallTime: number
  }
}

/**
 * 插件事件接口
 */
export interface PluginEvent {
  /** 事件类型 */
  type: PluginEventType
  /** 事件目标 */
  target: Plugin
  /** 事件数据 */
  data?: any
  /** 事件时间戳 */
  timestamp: number
  /** 错误信息 */
  error?: Error
}

/**
 * 插件加载器接口
 */
export interface PluginLoader {
  /** 加载插件 */
  load(pluginPath: string): Promise<any>
  /** 卸载插件 */
  unload(pluginId: string): Promise<void>
  /** 重载插件 */
  reload(pluginId: string): Promise<any>
  /** 验证插件 */
  validate(pluginPath: string): Promise<ValidationResult>
  /** 解析插件元数据 */
  parseMetadata(pluginPath: string): Promise<PluginMetadata>
  /** 检查插件兼容性 */
  checkCompatibility(metadata: PluginMetadata): Promise<boolean>
}

/**
 * 插件注册表接口
 */
export interface PluginRegistry {
  /** 注册插件 */
  register(plugin: Plugin): void
  /** 注销插件 */
  unregister(pluginId: string): void
  /** 获取插件 */
  get(pluginId: string): Plugin | undefined
  /** 获取所有插件 */
  getAll(): Plugin[]
  /** 检查插件是否存在 */
  has(pluginId: string): boolean
  /** 清空注册表 */
  clear(): void
  /** 获取插件数量 */
  size(): number
}

/**
 * 插件钩子管理器接口
 */
export interface PluginHookManager {
  /** 注册钩子 */
  registerHook(name: string, type: PluginHook['type'], priority?: number): void
  /** 注销钩子 */
  unregisterHook(name: string): void
  /** 添加钩子处理器 */
  addHandler(hookName: string, handler: PluginHookHandler): void
  /** 移除钩子处理器 */
  removeHandler(hookName: string, handlerId: string): void
  /** 调用钩子 */
  callHook(name: string, ...args: any[]): Promise<any>
  /** 获取钩子 */
  getHook(name: string): PluginHook | undefined
  /** 获取所有钩子 */
  getAllHooks(): PluginHook[]
  /** 清空钩子 */
  clear(): void
}

/**
 * 插件服务注册表接口
 */
export interface PluginServiceRegistry {
  /** 注册服务 */
  register(name: string, service: any, pluginId: string): void
  /** 注销服务 */
  unregister(name: string): void
  /** 获取服务 */
  get(name: string): any
  /** 获取所有服务 */
  getAll(): Record<string, any>
  /** 检查服务是否存在 */
  has(name: string): boolean
  /** 获取服务提供者 */
  getProvider(name: string): string | undefined
  /** 清空服务 */
  clear(): void
}

/**
 * 插件中间件管理器接口
 */
export interface PluginMiddlewareManager {
  /** 注册中间件 */
  register(name: string, middleware: Function, pluginId: string): void
  /** 注销中间件 */
  unregister(name: string): void
  /** 执行中间件链 */
  execute(name: string, context: any): Promise<any>
  /** 获取中间件 */
  get(name: string): Function | undefined
  /** 获取所有中间件 */
  getAll(): Record<string, Function>
  /** 清空中间件 */
  clear(): void
}

/**
 * 插件配置管理器接口
 */
export interface PluginConfigManager {
  /** 获取插件配置 */
  get(pluginId: string, key?: string): any
  /** 设置插件配置 */
  set(pluginId: string, key: string, value: any): void
  /** 删除插件配置 */
  delete(pluginId: string, key?: string): void
  /** 获取所有插件配置 */
  getAll(): Record<string, any>
  /** 保存配置 */
  save(): Promise<void>
  /** 加载配置 */
  load(): Promise<void>
  /** 重置配置 */
  reset(pluginId?: string): void
}

/**
 * 插件存储接口
 */
export interface PluginStorage {
  /** 获取数据 */
  get(pluginId: string, key: string): any
  /** 设置数据 */
  set(pluginId: string, key: string, value: any): void
  /** 删除数据 */
  delete(pluginId: string, key: string): void
  /** 清空插件数据 */
  clear(pluginId: string): void
  /** 获取插件所有数据 */
  getAll(pluginId: string): Record<string, any>
  /** 检查数据是否存在 */
  has(pluginId: string, key: string): boolean
  /** 获取数据大小 */
  size(pluginId: string): number
}

/**
 * 插件缓存接口
 */
export interface PluginCache {
  /** 获取缓存 */
  get(pluginId: string, key: string): any
  /** 设置缓存 */
  set(pluginId: string, key: string, value: any, ttl?: number): void
  /** 删除缓存 */
  delete(pluginId: string, key: string): void
  /** 清空插件缓存 */
  clear(pluginId: string): void
  /** 检查缓存是否存在 */
  has(pluginId: string, key: string): boolean
  /** 获取缓存统计 */
  getStats(pluginId: string): CacheStats
}

/**
 * 插件安全管理器接口
 */
export interface PluginSecurityManager {
  /** 验证插件签名 */
  verifySignature(pluginPath: string): Promise<boolean>
  /** 检查插件权限 */
  checkPermissions(pluginId: string, permission: string): boolean
  /** 授予权限 */
  grantPermission(pluginId: string, permission: string): void
  /** 撤销权限 */
  revokePermission(pluginId: string, permission: string): void
  /** 获取插件权限 */
  getPermissions(pluginId: string): string[]
  /** 沙箱执行 */
  sandboxExecute(pluginId: string, code: string): Promise<any>
  /** 检查恶意代码 */
  scanMaliciousCode(pluginPath: string): Promise<SecurityScanResult>
}

/**
 * 安全扫描结果接口
 */
export interface SecurityScanResult {
  /** 是否安全 */
  safe: boolean
  /** 风险级别 */
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  /** 发现的问题 */
  issues: SecurityIssue[]
  /** 扫描时间 */
  scanTime: number
}

/**
 * 安全问题接口
 */
export interface SecurityIssue {
  /** 问题类型 */
  type: string
  /** 问题描述 */
  description: string
  /** 风险级别 */
  severity: 'low' | 'medium' | 'high' | 'critical'
  /** 问题位置 */
  location?: {
    file: string
    line: number
    column: number
  }
  /** 修复建议 */
  suggestion?: string
}

/**
 * 插件性能监控器接口
 */
export interface PluginPerformanceMonitor {
  /** 开始监控 */
  start(pluginId: string): void
  /** 停止监控 */
  stop(pluginId: string): void
  /** 记录性能数据 */
  record(pluginId: string, metric: string, value: number): void
  /** 获取性能指标 */
  getMetrics(pluginId: string): PluginPerformanceMetrics
  /** 重置指标 */
  reset(pluginId: string): void
  /** 获取所有插件指标 */
  getAllMetrics(): Record<string, PluginPerformanceMetrics>
}

/**
 * 插件性能指标接口
 */
export interface PluginPerformanceMetrics {
  /** 插件ID */
  pluginId: string
  /** 启动时间 */
  startupTime: number
  /** 平均执行时间 */
  avgExecutionTime: number
  /** 内存使用 */
  memoryUsage: number
  /** CPU使用率 */
  cpuUsage: number
  /** 调用次数 */
  callCount: number
  /** 错误次数 */
  errorCount: number
  /** 自定义指标 */
  customMetrics: Record<string, number>
}

/**
 * 插件市场接口
 */
export interface PluginMarket {
  /** 搜索插件 */
  search(query: MarketSearchQuery): Promise<MarketPlugin[]>
  /** 获取插件详情 */
  getPluginDetails(pluginId: string): Promise<MarketPluginDetails>
  /** 下载插件 */
  download(pluginId: string, version?: string): Promise<string>
  /** 获取插件版本列表 */
  getVersions(pluginId: string): Promise<string[]>
  /** 获取热门插件 */
  getPopular(limit?: number): Promise<MarketPlugin[]>
  /** 获取最新插件 */
  getLatest(limit?: number): Promise<MarketPlugin[]>
  /** 获取推荐插件 */
  getRecommended(limit?: number): Promise<MarketPlugin[]>
  /** 提交插件 */
  submit(pluginPackage: PluginPackage): Promise<void>
  /** 更新插件 */
  updatePlugin(pluginId: string, pluginPackage: PluginPackage): Promise<void>
}

/**
 * 市场搜索查询接口
 */
export interface MarketSearchQuery {
  /** 搜索关键词 */
  keyword?: string
  /** 插件类型 */
  type?: PluginType
  /** 插件标签 */
  tags?: string[]
  /** 作者 */
  author?: string
  /** 最小评分 */
  minRating?: number
  /** 排序方式 */
  sortBy?: 'name' | 'downloads' | 'rating' | 'updated'
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
  /** 分页信息 */
  pagination?: {
    page: number
    pageSize: number
  }
}

/**
 * 市场插件接口
 */
export interface MarketPlugin {
  /** 插件ID */
  id: string
  /** 插件名称 */
  name: string
  /** 插件描述 */
  description: string
  /** 插件版本 */
  version: string
  /** 插件作者 */
  author: string
  /** 插件类型 */
  type: PluginType
  /** 插件标签 */
  tags: string[]
  /** 下载次数 */
  downloads: number
  /** 评分 */
  rating: number
  /** 评价数量 */
  reviewCount: number
  /** 最后更新时间 */
  lastUpdated: number
  /** 插件图标 */
  icon?: string
  /** 插件截图 */
  screenshots?: string[]
}

/**
 * 市场插件详情接口
 */
export interface MarketPluginDetails extends MarketPlugin {
  /** 详细描述 */
  longDescription: string
  /** 更新日志 */
  changelog: string
  /** 插件主页 */
  homepage?: string
  /** 源码仓库 */
  repository?: string
  /** 问题反馈 */
  issues?: string
  /** 许可证 */
  license: string
  /** 依赖列表 */
  dependencies: string[]
  /** 支持的版本 */
  supportedVersions: string[]
  /** 文档链接 */
  documentation?: string
  /** 演示链接 */
  demo?: string
  /** 评价列表 */
  reviews: PluginReview[]
}

/**
 * 插件评价接口
 */
export interface PluginReview {
  /** 评价ID */
  id: string
  /** 用户名 */
  username: string
  /** 评分 */
  rating: number
  /** 评价内容 */
  content: string
  /** 评价时间 */
  timestamp: number
  /** 插件版本 */
  version: string
}

/**
 * 插件包接口
 */
export interface PluginPackage {
  /** 插件元数据 */
  metadata: PluginMetadata
  /** 插件文件 */
  files: PluginFile[]
  /** 插件文档 */
  documentation?: string
  /** 更新日志 */
  changelog?: string
  /** 许可证文本 */
  licenseText?: string
}

/**
 * 插件文件接口
 */
export interface PluginFile {
  /** 文件路径 */
  path: string
  /** 文件内容 */
  content: string | Buffer
  /** 文件类型 */
  type: string
  /** 文件大小 */
  size: number
  /** 文件哈希 */
  hash: string
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 是否有效 */
  valid: boolean
  /** 错误信息 */
  errors: string[]
  /** 警告信息 */
  warnings: string[]
  /** 建议 */
  suggestions: string[]
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  /** 缓存项数量 */
  itemCount: number
  /** 缓存大小 */
  size: number
  /** 命中次数 */
  hits: number
  /** 未命中次数 */
  misses: number
  /** 命中率 */
  hitRate: number
}