/**
 * 错误相关类型定义
 */

/**
 * 错误代码枚举
 */
export enum ErrorCode {
  // 通用错误 (1000-1999)
  /** 未知错误 */
  UNKNOWN = 'UNKNOWN',
  /** 无效参数 */
  INVALID_ARGUMENT = 'INVALID_ARGUMENT',
  /** 权限不足 */
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  /** 资源未找到 */
  NOT_FOUND = 'NOT_FOUND',
  /** 资源已存在 */
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  /** 操作超时 */
  TIMEOUT = 'TIMEOUT',
  /** 操作取消 */
  CANCELLED = 'CANCELLED',
  /** 内部错误 */
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  /** 不支持的操作 */
  UNSUPPORTED_OPERATION = 'UNSUPPORTED_OPERATION',
  /** 配置错误 */
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',

  // 应用相关错误 (2000-2999)
  /** 应用未找到 */
  APP_NOT_FOUND = 'APP_NOT_FOUND',
  /** 应用已存在 */
  APP_ALREADY_EXISTS = 'APP_ALREADY_EXISTS',
  /** 应用加载失败 */
  APP_LOAD_FAILED = 'APP_LOAD_FAILED',
  /** 应用启动失败 */
  APP_START_FAILED = 'APP_START_FAILED',
  /** 应用停止失败 */
  APP_STOP_FAILED = 'APP_STOP_FAILED',
  /** 应用卸载失败 */
  APP_UNLOAD_FAILED = 'APP_UNLOAD_FAILED',
  /** 应用状态无效 */
  APP_INVALID_STATE = 'APP_INVALID_STATE',
  /** 应用配置无效 */
  APP_INVALID_CONFIG = 'APP_INVALID_CONFIG',
  /** 应用依赖缺失 */
  APP_MISSING_DEPENDENCY = 'APP_MISSING_DEPENDENCY',
  /** 应用版本不兼容 */
  APP_VERSION_INCOMPATIBLE = 'APP_VERSION_INCOMPATIBLE',

  // 资源相关错误 (3000-3999)
  /** 资源加载失败 */
  RESOURCE_LOAD_FAILED = 'RESOURCE_LOAD_FAILED',
  /** 资源解析失败 */
  RESOURCE_PARSE_FAILED = 'RESOURCE_PARSE_FAILED',
  /** 资源缓存失败 */
  RESOURCE_CACHE_FAILED = 'RESOURCE_CACHE_FAILED',
  /** 资源版本冲突 */
  RESOURCE_VERSION_CONFLICT = 'RESOURCE_VERSION_CONFLICT',
  /** 资源大小超限 */
  RESOURCE_SIZE_EXCEEDED = 'RESOURCE_SIZE_EXCEEDED',
  /** 资源类型不支持 */
  RESOURCE_TYPE_UNSUPPORTED = 'RESOURCE_TYPE_UNSUPPORTED',
  /** 资源校验失败 */
  RESOURCE_VALIDATION_FAILED = 'RESOURCE_VALIDATION_FAILED',

  // 沙箱相关错误 (4000-4999)
  /** 沙箱创建失败 */
  SANDBOX_CREATE_FAILED = 'SANDBOX_CREATE_FAILED',
  /** 沙箱销毁失败 */
  SANDBOX_DESTROY_FAILED = 'SANDBOX_DESTROY_FAILED',
  /** 沙箱隔离失败 */
  SANDBOX_ISOLATION_FAILED = 'SANDBOX_ISOLATION_FAILED',
  /** 沙箱策略不支持 */
  SANDBOX_STRATEGY_UNSUPPORTED = 'SANDBOX_STRATEGY_UNSUPPORTED',
  /** 沙箱权限违规 */
  SANDBOX_PERMISSION_VIOLATION = 'SANDBOX_PERMISSION_VIOLATION',
  /** 沙箱内存泄漏 */
  SANDBOX_MEMORY_LEAK = 'SANDBOX_MEMORY_LEAK',

  // 插件相关错误 (5000-5999)
  /** 插件未找到 */
  PLUGIN_NOT_FOUND = 'PLUGIN_NOT_FOUND',
  /** 插件已存在 */
  PLUGIN_ALREADY_EXISTS = 'PLUGIN_ALREADY_EXISTS',
  /** 插件安装失败 */
  PLUGIN_INSTALL_FAILED = 'PLUGIN_INSTALL_FAILED',
  /** 插件卸载失败 */
  PLUGIN_UNINSTALL_FAILED = 'PLUGIN_UNINSTALL_FAILED',
  /** 插件启用失败 */
  PLUGIN_ENABLE_FAILED = 'PLUGIN_ENABLE_FAILED',
  /** 插件禁用失败 */
  PLUGIN_DISABLE_FAILED = 'PLUGIN_DISABLE_FAILED',
  /** 插件配置无效 */
  PLUGIN_INVALID_CONFIG = 'PLUGIN_INVALID_CONFIG',
  /** 插件依赖冲突 */
  PLUGIN_DEPENDENCY_CONFLICT = 'PLUGIN_DEPENDENCY_CONFLICT',
  /** 插件版本不兼容 */
  PLUGIN_VERSION_INCOMPATIBLE = 'PLUGIN_VERSION_INCOMPATIBLE',
  /** 插件权限不足 */
  PLUGIN_INSUFFICIENT_PERMISSIONS = 'PLUGIN_INSUFFICIENT_PERMISSIONS',

  // 通信相关错误 (6000-6999)
  /** 通信连接失败 */
  COMMUNICATION_CONNECTION_FAILED = 'COMMUNICATION_CONNECTION_FAILED',
  /** 通信超时 */
  COMMUNICATION_TIMEOUT = 'COMMUNICATION_TIMEOUT',
  /** 消息格式无效 */
  COMMUNICATION_INVALID_MESSAGE = 'COMMUNICATION_INVALID_MESSAGE',
  /** 通道未找到 */
  COMMUNICATION_CHANNEL_NOT_FOUND = 'COMMUNICATION_CHANNEL_NOT_FOUND',
  /** 通道已关闭 */
  COMMUNICATION_CHANNEL_CLOSED = 'COMMUNICATION_CHANNEL_CLOSED',
  /** 消息发送失败 */
  COMMUNICATION_SEND_FAILED = 'COMMUNICATION_SEND_FAILED',
  /** 消息接收失败 */
  COMMUNICATION_RECEIVE_FAILED = 'COMMUNICATION_RECEIVE_FAILED',

  // 路由相关错误 (7000-7999)
  /** 路由未找到 */
  ROUTER_ROUTE_NOT_FOUND = 'ROUTER_ROUTE_NOT_FOUND',
  /** 路由配置无效 */
  ROUTER_INVALID_CONFIG = 'ROUTER_INVALID_CONFIG',
  /** 路由导航失败 */
  ROUTER_NAVIGATION_FAILED = 'ROUTER_NAVIGATION_FAILED',
  /** 路由守卫拒绝 */
  ROUTER_GUARD_REJECTED = 'ROUTER_GUARD_REJECTED',
  /** 路由参数无效 */
  ROUTER_INVALID_PARAMS = 'ROUTER_INVALID_PARAMS',

  // 存储相关错误 (8000-8999)
  /** 存储空间不足 */
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',
  /** 存储访问被拒绝 */
  STORAGE_ACCESS_DENIED = 'STORAGE_ACCESS_DENIED',
  /** 存储数据损坏 */
  STORAGE_DATA_CORRUPTED = 'STORAGE_DATA_CORRUPTED',
  /** 存储操作失败 */
  STORAGE_OPERATION_FAILED = 'STORAGE_OPERATION_FAILED',
  /** 存储键无效 */
  STORAGE_INVALID_KEY = 'STORAGE_INVALID_KEY',

  // 网络相关错误 (9000-9999)
  /** 网络连接失败 */
  NETWORK_CONNECTION_FAILED = 'NETWORK_CONNECTION_FAILED',
  /** 网络超时 */
  NETWORK_TIMEOUT = 'NETWORK_TIMEOUT',
  /** 网络中断 */
  NETWORK_INTERRUPTED = 'NETWORK_INTERRUPTED',
  /** HTTP错误 */
  NETWORK_HTTP_ERROR = 'NETWORK_HTTP_ERROR',
  /** 跨域错误 */
  NETWORK_CORS_ERROR = 'NETWORK_CORS_ERROR'
}

/**
 * 错误级别枚举
 */
export enum ErrorLevel {
  /** 调试级别 */
  DEBUG = 'debug',
  /** 信息级别 */
  INFO = 'info',
  /** 警告级别 */
  WARN = 'warn',
  /** 错误级别 */
  ERROR = 'error',
  /** 致命错误级别 */
  FATAL = 'fatal'
}

/**
 * 错误类别枚举
 */
export enum ErrorCategory {
  /** 系统错误 */
  SYSTEM = 'system',
  /** 应用错误 */
  APPLICATION = 'application',
  /** 用户错误 */
  USER = 'user',
  /** 网络错误 */
  NETWORK = 'network',
  /** 安全错误 */
  SECURITY = 'security',
  /** 性能错误 */
  PERFORMANCE = 'performance',
  /** 兼容性错误 */
  COMPATIBILITY = 'compatibility'
}

/**
 * 错误上下文接口
 */
export interface ErrorContext {
  /** 错误发生的组件 */
  component?: string
  /** 错误发生的方法 */
  method?: string
  /** 错误发生的文件 */
  file?: string
  /** 错误发生的行号 */
  line?: number
  /** 错误发生的列号 */
  column?: number
  /** 用户代理信息 */
  userAgent?: string
  /** URL信息 */
  url?: string
  /** 用户ID */
  userId?: string
  /** 会话ID */
  sessionId?: string
  /** 请求ID */
  requestId?: string
  /** 时间戳 */
  timestamp?: number
  /** 额外数据 */
  extra?: Record<string, any>
}

/**
 * 错误堆栈帧接口
 */
export interface ErrorStackFrame {
  /** 函数名 */
  functionName?: string
  /** 文件名 */
  fileName?: string
  /** 行号 */
  lineNumber?: number
  /** 列号 */
  columnNumber?: number
  /** 源码 */
  source?: string
}

/**
 * 微前端核心错误类
 */
export class MicroCoreError extends Error {
  /** 错误代码 */
  public readonly code: ErrorCode
  /** 错误级别 */
  public readonly level: ErrorLevel
  /** 错误类别 */
  public readonly category: ErrorCategory
  /** 错误上下文 */
  public readonly context?: ErrorContext
  /** 原始错误 */
  public readonly originalError?: Error
  /** 错误堆栈帧 */
  public readonly stackFrames?: ErrorStackFrame[]
  /** 错误ID */
  public readonly id: string
  /** 错误时间戳 */
  public readonly timestamp: number
  /** 是否可恢复 */
  public readonly recoverable: boolean
  /** 重试次数 */
  public retryCount: number
  /** 最大重试次数 */
  public readonly maxRetries: number

  constructor(
    message: string,
    code: ErrorCode = ErrorCode.UNKNOWN,
    options: {
      level?: ErrorLevel
      category?: ErrorCategory
      context?: ErrorContext
      originalError?: Error
      recoverable?: boolean
      maxRetries?: number
    } = {}
  ) {
    super(message)
    
    this.name = 'MicroCoreError'
    this.code = code
    this.level = options.level || ErrorLevel.ERROR
    this.category = options.category || ErrorCategory.SYSTEM
    this.context = options.context
    this.originalError = options.originalError
    this.recoverable = options.recoverable || false
    this.maxRetries = options.maxRetries || 0
    this.retryCount = 0
    this.id = this.generateErrorId()
    this.timestamp = Date.now()
    
    // 解析堆栈信息
    this.stackFrames = this.parseStackTrace()
    
    // 保持原型链
    Object.setPrototypeOf(this, MicroCoreError.prototype)
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `${this.code}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 解析堆栈信息
   */
  private parseStackTrace(): ErrorStackFrame[] {
    if (!this.stack) return []
    
    const frames: ErrorStackFrame[] = []
    const lines = this.stack.split('\n').slice(1) // 跳过第一行错误消息
    
    for (const line of lines) {
      const frame = this.parseStackFrame(line.trim())
      if (frame) {
        frames.push(frame)
      }
    }
    
    return frames
  }

  /**
   * 解析单个堆栈帧
   */
  private parseStackFrame(line: string): ErrorStackFrame | null {
    // Chrome/V8 格式: at functionName (fileName:lineNumber:columnNumber)
    const chromeMatch = line.match(/at\s+(.+?)\s+\((.+?):(\d+):(\d+)\)/)
    if (chromeMatch) {
      return {
        functionName: chromeMatch[1],
        fileName: chromeMatch[2],
        lineNumber: parseInt(chromeMatch[3]),
        columnNumber: parseInt(chromeMatch[4])
      }
    }
    
    // Firefox 格式: functionName@fileName:lineNumber:columnNumber
    const firefoxMatch = line.match(/(.+?)@(.+?):(\d+):(\d+)/)
    if (firefoxMatch) {
      return {
        functionName: firefoxMatch[1],
        fileName: firefoxMatch[2],
        lineNumber: parseInt(firefoxMatch[3]),
        columnNumber: parseInt(firefoxMatch[4])
      }
    }
    
    return null
  }

  /**
   * 转换为JSON格式
   */
  toJSON(): ErrorJSON {
    return {
      id: this.id,
      name: this.name,
      message: this.message,
      code: this.code,
      level: this.level,
      category: this.category,
      context: this.context,
      stackFrames: this.stackFrames,
      timestamp: this.timestamp,
      recoverable: this.recoverable,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      originalError: this.originalError ? {
        name: this.originalError.name,
        message: this.originalError.message,
        stack: this.originalError.stack
      } : undefined
    }
  }

  /**
   * 转换为字符串格式
   */
  toString(): string {
    let result = `${this.name} [${this.code}]: ${this.message}`
    
    if (this.context) {
      result += `\n  Context: ${JSON.stringify(this.context, null, 2)}`
    }
    
    if (this.originalError) {
      result += `\n  Original Error: ${this.originalError.toString()}`
    }
    
    return result
  }

  /**
   * 创建子错误
   */
  createChildError(
    message: string,
    code: ErrorCode,
    options: Partial<ErrorContext> = {}
  ): MicroCoreError {
    return new MicroCoreError(message, code, {
      level: this.level,
      category: this.category,
      context: { ...this.context, ...options },
      originalError: this
    })
  }

  /**
   * 检查是否可以重试
   */
  canRetry(): boolean {
    return this.recoverable && this.retryCount < this.maxRetries
  }

  /**
   * 增加重试次数
   */
  incrementRetry(): void {
    this.retryCount++
  }

  /**
   * 检查是否为特定错误代码
   */
  is(code: ErrorCode): boolean {
    return this.code === code
  }

  /**
   * 检查是否为特定错误类别
   */
  isCategory(category: ErrorCategory): boolean {
    return this.category === category
  }

  /**
   * 检查是否为特定错误级别
   */
  isLevel(level: ErrorLevel): boolean {
    return this.level === level
  }
}

/**
 * 错误JSON格式接口
 */
export interface ErrorJSON {
  /** 错误ID */
  id: string
  /** 错误名称 */
  name: string
  /** 错误消息 */
  message: string
  /** 错误代码 */
  code: ErrorCode
  /** 错误级别 */
  level: ErrorLevel
  /** 错误类别 */
  category: ErrorCategory
  /** 错误上下文 */
  context?: ErrorContext
  /** 错误堆栈帧 */
  stackFrames?: ErrorStackFrame[]
  /** 错误时间戳 */
  timestamp: number
  /** 是否可恢复 */
  recoverable: boolean
  /** 重试次数 */
  retryCount: number
  /** 最大重试次数 */
  maxRetries: number
  /** 原始错误 */
  originalError?: {
    name: string
    message: string
    stack?: string
  }
}

/**
 * 错误处理器接口
 */
export interface ErrorHandler {
  /** 处理错误 */
  handle(error: MicroCoreError): Promise<void> | void
  /** 是否可以处理该错误 */
  canHandle(error: MicroCoreError): boolean
  /** 处理器优先级 */
  priority: number
}

/**
 * 错误报告器接口
 */
export interface ErrorReporter {
  /** 报告错误 */
  report(error: MicroCoreError): Promise<void>
  /** 批量报告错误 */
  reportBatch(errors: MicroCoreError[]): Promise<void>
  /** 配置报告器 */
  configure(config: ErrorReporterConfig): void
}

/**
 * 错误报告器配置接口
 */
export interface ErrorReporterConfig {
  /** 报告端点 */
  endpoint?: string
  /** API密钥 */
  apiKey?: string
  /** 批量大小 */
  batchSize?: number
  /** 报告间隔 */
  reportInterval?: number
  /** 最大重试次数 */
  maxRetries?: number
  /** 过滤器 */
  filter?: (error: MicroCoreError) => boolean
  /** 转换器 */
  transformer?: (error: MicroCoreError) => any
}

/**
 * 错误管理器接口
 */
export interface ErrorManager {
  /** 注册错误处理器 */
  registerHandler(handler: ErrorHandler): void
  /** 注销错误处理器 */
  unregisterHandler(handler: ErrorHandler): void
  /** 处理错误 */
  handleError(error: Error | MicroCoreError): Promise<void>
  /** 创建错误 */
  createError(
    message: string,
    code: ErrorCode,
    options?: Partial<{
      level: ErrorLevel
      category: ErrorCategory
      context: ErrorContext
      originalError: Error
      recoverable: boolean
      maxRetries: number
    }>
  ): MicroCoreError
  /** 获取错误统计 */
  getStats(): ErrorStats
  /** 清空错误历史 */
  clearHistory(): void
  /** 设置错误报告器 */
  setReporter(reporter: ErrorReporter): void
}

/**
 * 错误统计接口
 */
export interface ErrorStats {
  /** 总错误数 */
  totalErrors: number
  /** 按级别分组 */
  byLevel: Record<ErrorLevel, number>
  /** 按类别分组 */
  byCategory: Record<ErrorCategory, number>
  /** 按代码分组 */
  byCode: Record<ErrorCode, number>
  /** 最近错误 */
  recentErrors: MicroCoreError[]
  /** 最频繁错误 */
  mostFrequentErrors: Array<{
    code: ErrorCode
    count: number
    lastOccurrence: number
  }>
}

/**
 * 错误边界接口
 */
export interface ErrorBoundary {
  /** 捕获错误 */
  catchError(error: Error, errorInfo?: any): void
  /** 重置错误状态 */
  resetError(): void
  /** 获取错误状态 */
  hasError(): boolean
  /** 获取错误信息 */
  getError(): MicroCoreError | null
}

/**
 * 错误恢复策略接口
 */
export interface ErrorRecoveryStrategy {
  /** 策略名称 */
  name: string
  /** 是否可以恢复该错误 */
  canRecover(error: MicroCoreError): boolean
  /** 执行恢复 */
  recover(error: MicroCoreError): Promise<boolean>
  /** 恢复优先级 */
  priority: number
}

/**
 * 错误监控器接口
 */
export interface ErrorMonitor {
  /** 开始监控 */
  start(): void
  /** 停止监控 */
  stop(): void
  /** 监控错误 */
  monitor(error: MicroCoreError): void
  /** 获取监控统计 */
  getStats(): ErrorMonitorStats
  /** 设置阈值 */
  setThreshold(threshold: ErrorThreshold): void
  /** 添加监控器 */
  addListener(listener: ErrorMonitorListener): void
  /** 移除监控器 */
  removeListener(listener: ErrorMonitorListener): void
}

/**
 * 错误监控统计接口
 */
export interface ErrorMonitorStats {
  /** 监控开始时间 */
  startTime: number
  /** 监控持续时间 */
  duration: number
  /** 错误率 */
  errorRate: number
  /** 错误趋势 */
  errorTrend: Array<{
    timestamp: number
    count: number
  }>
  /** 错误分布 */
  errorDistribution: Record<string, number>
}

/**
 * 错误阈值接口
 */
export interface ErrorThreshold {
  /** 错误率阈值 */
  errorRate?: number
  /** 错误数量阈值 */
  errorCount?: number
  /** 时间窗口 */
  timeWindow?: number
  /** 特定错误代码阈值 */
  codeThresholds?: Record<ErrorCode, number>
}

/**
 * 错误监控监听器接口
 */
export interface ErrorMonitorListener {
  /** 阈值超出时触发 */
  onThresholdExceeded(stats: ErrorMonitorStats): void
  /** 错误率变化时触发 */
  onErrorRateChanged(oldRate: number, newRate: number): void
  /** 新错误类型出现时触发 */
  onNewErrorType(error: MicroCoreError): void
}

/**
 * 错误工厂接口
 */
export interface ErrorFactory {
  /** 创建应用错误 */
  createAppError(message: string, appName: string, context?: ErrorContext): MicroCoreError
  /** 创建资源错误 */
  createResourceError(message: string, resourceUrl: string, context?: ErrorContext): MicroCoreError
  /** 创建沙箱错误 */
  createSandboxError(message: string, sandboxId: string, context?: ErrorContext): MicroCoreError
  /** 创建插件错误 */
  createPluginError(message: string, pluginId: string, context?: ErrorContext): MicroCoreError
  /** 创建通信错误 */
  createCommunicationError(message: string, channel: string, context?: ErrorContext): MicroCoreError
  /** 创建网络错误 */
  createNetworkError(message: string, url: string, status?: number, context?: ErrorContext): MicroCoreError
  /** 从原生错误创建 */
  fromNativeError(error: Error, code?: ErrorCode, context?: ErrorContext): MicroCoreError
}

/**
 * 错误序列化器接口
 */
export interface ErrorSerializer {
  /** 序列化错误 */
  serialize(error: MicroCoreError): string
  /** 反序列化错误 */
  deserialize(data: string): MicroCoreError
  /** 序列化为JSON */
  toJSON(error: MicroCoreError): ErrorJSON
  /** 从JSON反序列化 */
  fromJSON(json: ErrorJSON): MicroCoreError
}

/**
 * 错误过滤器接口
 */
export interface ErrorFilter {
  /** 过滤器名称 */
  name: string
  /** 是否应该过滤该错误 */
  shouldFilter(error: MicroCoreError): boolean
  /** 过滤器优先级 */
  priority: number
}

/**
 * 错误转换器接口
 */
export interface ErrorTransformer {
  /** 转换器名称 */
  name: string
  /** 转换错误 */
  transform(error: MicroCoreError): MicroCoreError
  /** 是否可以转换该错误 */
  canTransform(error: MicroCoreError): boolean
  /** 转换器优先级 */
  priority: number
}

/**
 * 错误聚合器接口
 */
export interface ErrorAggregator {
  /** 聚合错误 */
  aggregate(errors: MicroCoreError[]): ErrorAggregation
  /** 按时间聚合 */
  aggregateByTime(errors: MicroCoreError[], interval: number): TimeBasedAggregation[]
  /** 按类型聚合 */
  aggregateByType(errors: MicroCoreError[]): TypeBasedAggregation[]
  /** 按组件聚合 */
  aggregateByComponent(errors: MicroCoreError[]): ComponentBasedAggregation[]
}

/**
 * 错误聚合结果接口
 */
export interface ErrorAggregation {
  /** 总错误数 */
  totalCount: number
  /** 唯一错误数 */
  uniqueCount: number
  /** 时间范围 */
  timeRange: {
    start: number
    end: number
  }
  /** 按级别分组 */
  byLevel: Record<ErrorLevel, number>
  /** 按类别分组 */
  byCategory: Record<ErrorCategory, number>
  /** 按代码分组 */
  byCode: Record<ErrorCode, number>
  /** 最频繁错误 */
  topErrors: Array<{
    code: ErrorCode
    count: number
    percentage: number
  }>
}

/**
 * 基于时间的聚合接口
 */
export interface TimeBasedAggregation {
  /** 时间戳 */
  timestamp: number
  /** 错误数量 */
  count: number
  /** 错误详情 */
  errors: MicroCoreError[]
}

/**
 * 基于类型的聚合接口
 */
export interface TypeBasedAggregation {
  /** 错误代码 */
  code: ErrorCode
  /** 错误数量 */
  count: number
  /** 错误百分比 */
  percentage: number
  /** 错误示例 */
  examples: MicroCoreError[]
}

/**
 * 基于组件的聚合接口
 */
export interface ComponentBasedAggregation {
  /** 组件名称 */
  component: string
  /** 错误数量 */
  count: number
  /** 错误百分比 */
  percentage: number
  /** 错误分布 */
  distribution: Record<ErrorCode, number>
}