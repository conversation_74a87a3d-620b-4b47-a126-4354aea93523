/**
 * 沙箱相关类型定义
 */

/**
 * 沙箱类型枚举
 */
export enum SandboxType {
  /** Proxy沙箱 */
  PROXY = 'proxy',
  /** DefineProperty沙箱 */
  DEFINE_PROPERTY = 'defineProperty',
  /** iframe沙箱 */
  IFRAME = 'iframe',
  /** WebComponent沙箱 */
  WEB_COMPONENT = 'webComponent',
  /** 命名空间沙箱 */
  NAMESPACE = 'namespace',
  /** 联邦沙箱 */
  FEDERATION = 'federation'
}

/**
 * 沙箱状态枚举
 */
export enum SandboxStatus {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 已初始化 */
  INITIALIZED = 'initialized',
  /** 激活中 */
  ACTIVATING = 'activating',
  /** 已激活 */
  ACTIVE = 'active',
  /** 停用中 */
  DEACTIVATING = 'deactivating',
  /** 已停用 */
  INACTIVE = 'inactive',
  /** 销毁中 */
  DESTROYING = 'destroying',
  /** 已销毁 */
  DESTROYED = 'destroyed',
  /** 错误状态 */
  ERROR = 'error'
}

/**
 * 沙箱事件类型
 */
export enum SandboxEventType {
  /** 沙箱创建 */
  CREATED = 'sandbox:created',
  /** 沙箱初始化 */
  INITIALIZED = 'sandbox:initialized',
  /** 沙箱激活 */
  ACTIVATED = 'sandbox:activated',
  /** 沙箱停用 */
  DEACTIVATED = 'sandbox:deactivated',
  /** 沙箱销毁 */
  DESTROYED = 'sandbox:destroyed',
  /** 沙箱错误 */
  ERROR = 'sandbox:error',
  /** 全局变量访问 */
  GLOBAL_ACCESS = 'sandbox:globalAccess',
  /** 全局变量修改 */
  GLOBAL_MODIFY = 'sandbox:globalModify'
}

/**
 * 沙箱配置接口
 */
export interface SandboxConfig {
  /** 沙箱名称 */
  name: string
  /** 沙箱类型 */
  type: SandboxType
  /** 是否启用严格模式 */
  strict?: boolean
  /** 是否启用JavaScript隔离 */
  enableJSIsolation?: boolean
  /** 是否启用CSS隔离 */
  enableCSSIsolation?: boolean
  /** 是否启用全局变量隔离 */
  enableGlobalIsolation?: boolean
  /** 白名单全局变量 */
  globalWhitelist?: string[]
  /** 黑名单全局变量 */
  globalBlacklist?: string[]
  /** 自定义全局变量 */
  customGlobals?: Record<string, any>
  /** 沙箱容器元素 */
  container?: HTMLElement | string
  /** 沙箱样式前缀 */
  stylePrefix?: string
  /** 是否启用性能监控 */
  enablePerformanceMonitor?: boolean
  /** 是否启用调试模式 */
  debug?: boolean
  /** 超时时间（毫秒） */
  timeout?: number
  /** 自定义配置 */
  [key: string]: any
}

/**
 * 沙箱环境接口
 */
export interface SandboxEnvironment {
  /** 全局对象 */
  global: any
  /** 文档对象 */
  document?: Document
  /** 窗口对象 */
  window?: Window
  /** 位置对象 */
  location?: Location
  /** 历史对象 */
  history?: History
  /** 导航对象 */
  navigator?: Navigator
  /** 控制台对象 */
  console?: Console
  /** 自定义属性 */
  [key: string]: any
}

/**
 * 沙箱快照接口
 */
export interface SandboxSnapshot {
  /** 快照ID */
  id: string
  /** 快照时间 */
  timestamp: number
  /** 全局变量快照 */
  globals: Record<string, any>
  /** DOM快照 */
  dom?: string
  /** 样式快照 */
  styles?: string[]
  /** 事件监听器快照 */
  listeners?: Array<{
    element: string
    event: string
    handler: string
  }>
}

/**
 * 沙箱隔离配置接口
 */
export interface IsolationConfig {
  /** JavaScript隔离配置 */
  javascript?: {
    /** 是否启用 */
    enabled: boolean
    /** 白名单API */
    whitelist?: string[]
    /** 黑名单API */
    blacklist?: string[]
    /** 自定义API */
    customAPIs?: Record<string, any>
  }
  /** CSS隔离配置 */
  css?: {
    /** 是否启用 */
    enabled: boolean
    /** 样式前缀 */
    prefix?: string
    /** 是否启用Shadow DOM */
    shadowDOM?: boolean
    /** 样式作用域 */
    scoped?: boolean
  }
  /** 全局变量隔离配置 */
  global?: {
    /** 是否启用 */
    enabled: boolean
    /** 白名单变量 */
    whitelist?: string[]
    /** 黑名单变量 */
    blacklist?: string[]
    /** 只读变量 */
    readonly?: string[]
  }
}

/**
 * 沙箱策略接口
 */
export interface SandboxStrategy {
  /** 策略名称 */
  name: string
  /** 策略类型 */
  type: SandboxType
  /** 是否支持当前环境 */
  isSupported(): boolean
  /** 创建沙箱 */
  create(config: SandboxConfig): Promise<Sandbox>
  /** 销毁沙箱 */
  destroy(sandbox: Sandbox): Promise<void>
}

/**
 * 沙箱接口
 */
export interface Sandbox {
  /** 沙箱ID */
  id: string
  /** 沙箱名称 */
  name: string
  /** 沙箱类型 */
  type: SandboxType
  /** 沙箱状态 */
  status: SandboxStatus
  /** 沙箱配置 */
  config: SandboxConfig
  /** 沙箱环境 */
  environment: SandboxEnvironment
  /** 创建时间 */
  createTime: number
  /** 激活时间 */
  activeTime?: number
  /** 停用时间 */
  inactiveTime?: number
  
  /** 初始化沙箱 */
  initialize(): Promise<void>
  /** 激活沙箱 */
  activate(): Promise<void>
  /** 停用沙箱 */
  deactivate(): Promise<void>
  /** 销毁沙箱 */
  destroy(): Promise<void>
  /** 执行代码 */
  execute(code: string): Promise<any>
  /** 获取全局变量 */
  getGlobal(key: string): any
  /** 设置全局变量 */
  setGlobal(key: string, value: any): void
  /** 删除全局变量 */
  deleteGlobal(key: string): boolean
  /** 创建快照 */
  createSnapshot(): SandboxSnapshot
  /** 恢复快照 */
  restoreSnapshot(snapshot: SandboxSnapshot): void
  /** 清理资源 */
  cleanup(): void
  /** 添加事件监听器 */
  addEventListener(type: SandboxEventType, listener: (event: SandboxEvent) => void): void
  /** 移除事件监听器 */
  removeEventListener(type: SandboxEventType, listener: (event: SandboxEvent) => void): void
}

/**
 * 沙箱事件接口
 */
export interface SandboxEvent {
  /** 事件类型 */
  type: SandboxEventType
  /** 事件目标 */
  target: Sandbox
  /** 事件数据 */
  data?: any
  /** 事件时间戳 */
  timestamp: number
  /** 是否可取消 */
  cancelable?: boolean
  /** 是否已取消 */
  cancelled?: boolean
}

/**
 * 沙箱工厂接口
 */
export interface SandboxFactory {
  /** 注册策略 */
  registerStrategy(strategy: SandboxStrategy): void
  /** 获取策略 */
  getStrategy(type: SandboxType): SandboxStrategy | undefined
  /** 获取所有策略 */
  getAllStrategies(): SandboxStrategy[]
  /** 选择最优策略 */
  selectOptimalStrategy(config?: Partial<SandboxConfig>): SandboxType
  /** 创建沙箱 */
  createSandbox(config: SandboxConfig): Promise<Sandbox>
  /** 销毁沙箱 */
  destroySandbox(sandbox: Sandbox): Promise<void>
}

/**
 * 沙箱管理器接口
 */
export interface SandboxManager {
  /** 创建沙箱 */
  create(config: SandboxConfig): Promise<Sandbox>
  /** 获取沙箱 */
  get(id: string): Sandbox | undefined
  /** 获取所有沙箱 */
  getAll(): Sandbox[]
  /** 激活沙箱 */
  activate(id: string): Promise<void>
  /** 停用沙箱 */
  deactivate(id: string): Promise<void>
  /** 销毁沙箱 */
  destroy(id: string): Promise<void>
  /** 销毁所有沙箱 */
  destroyAll(): Promise<void>
  /** 清理无用沙箱 */
  cleanup(): Promise<void>
  /** 获取沙箱统计信息 */
  getStats(): SandboxStats
}

/**
 * 沙箱统计信息接口
 */
export interface SandboxStats {
  /** 总数量 */
  total: number
  /** 活跃数量 */
  active: number
  /** 非活跃数量 */
  inactive: number
  /** 错误数量 */
  error: number
  /** 内存使用情况 */
  memory: {
    /** 已使用内存（字节） */
    used: number
    /** 总内存（字节） */
    total: number
    /** 内存使用率 */
    usage: number
  }
  /** 性能指标 */
  performance: {
    /** 平均创建时间（毫秒） */
    avgCreateTime: number
    /** 平均激活时间（毫秒） */
    avgActivateTime: number
    /** 平均执行时间（毫秒） */
    avgExecuteTime: number
  }
}

/**
 * Proxy沙箱配置接口
 */
export interface ProxySandboxConfig extends SandboxConfig {
  /** 是否启用属性拦截 */
  enablePropertyIntercept?: boolean
  /** 是否启用函数调用拦截 */
  enableFunctionIntercept?: boolean
  /** 自定义拦截器 */
  customInterceptors?: Record<string, (target: any, property: string, value?: any) => any>
}

/**
 * iframe沙箱配置接口
 */
export interface IframeSandboxConfig extends SandboxConfig {
  /** iframe源 */
  src?: string
  /** 沙箱属性 */
  sandbox?: string
  /** 是否允许脚本 */
  allowScripts?: boolean
  /** 是否允许表单 */
  allowForms?: boolean
  /** 是否允许弹窗 */
  allowPopups?: boolean
  /** 是否允许同源 */
  allowSameOrigin?: boolean
}

/**
 * WebComponent沙箱配置接口
 */
export interface WebComponentSandboxConfig extends SandboxConfig {
  /** 自定义元素标签名 */
  tagName?: string
  /** 是否使用Shadow DOM */
  useShadowDOM?: boolean
  /** Shadow DOM模式 */
  shadowMode?: 'open' | 'closed'
  /** 自定义元素选项 */
  elementOptions?: ElementDefinitionOptions
}

/**
 * 命名空间沙箱配置接口
 */
export interface NamespaceSandboxConfig extends SandboxConfig {
  /** 命名空间前缀 */
  namespacePrefix?: string
  /** 是否启用命名空间隔离 */
  enableNamespaceIsolation?: boolean
  /** 自定义命名空间映射 */
  namespaceMapping?: Record<string, string>
}

/**
 * 联邦沙箱配置接口
 */
export interface FederationSandboxConfig extends SandboxConfig {
  /** 联邦配置 */
  federation?: {
    /** 远程模块 */
    remotes?: Record<string, string>
    /** 共享依赖 */
    shared?: Record<string, any>
    /** 暴露模块 */
    exposes?: Record<string, string>
  }
}

/**
 * 沙箱性能监控接口
 */
export interface SandboxPerformanceMonitor {
  /** 开始监控 */
  start(sandbox: Sandbox): void
  /** 停止监控 */
  stop(sandbox: Sandbox): void
  /** 获取性能数据 */
  getMetrics(sandbox: Sandbox): SandboxPerformanceMetrics
  /** 重置性能数据 */
  reset(sandbox: Sandbox): void
}

/**
 * 沙箱性能指标接口
 */
export interface SandboxPerformanceMetrics {
  /** 沙箱ID */
  sandboxId: string
  /** 创建时间（毫秒） */
  createTime: number
  /** 激活时间（毫秒） */
  activateTime: number
  /** 执行时间（毫秒） */
  executeTime: number
  /** 内存使用（字节） */
  memoryUsage: number
  /** CPU使用率 */
  cpuUsage: number
  /** 事件数量 */
  eventCount: number
  /** 错误数量 */
  errorCount: number
  /** 自定义指标 */
  customMetrics?: Record<string, number>
}

/**
 * 沙箱调试器接口
 */
export interface SandboxDebugger {
  /** 启用调试 */
  enable(sandbox: Sandbox): void
  /** 禁用调试 */
  disable(sandbox: Sandbox): void
  /** 设置断点 */
  setBreakpoint(sandbox: Sandbox, line: number, column?: number): void
  /** 移除断点 */
  removeBreakpoint(sandbox: Sandbox, line: number, column?: number): void
  /** 单步执行 */
  step(sandbox: Sandbox): void
  /** 继续执行 */
  continue(sandbox: Sandbox): void
  /** 获取调用栈 */
  getCallStack(sandbox: Sandbox): string[]
  /** 获取变量值 */
  getVariable(sandbox: Sandbox, name: string): any
  /** 设置变量值 */
  setVariable(sandbox: Sandbox, name: string, value: any): void
}

/**
 * 沙箱安全策略接口
 */
export interface SandboxSecurityPolicy {
  /** 检查代码安全性 */
  checkCode(code: string): SecurityCheckResult
  /** 检查API调用安全性 */
  checkAPICall(api: string, args: any[]): SecurityCheckResult
  /** 检查全局变量访问安全性 */
  checkGlobalAccess(key: string, value?: any): SecurityCheckResult
  /** 获取安全规则 */
  getRules(): SecurityRule[]
  /** 添加安全规则 */
  addRule(rule: SecurityRule): void
  /** 移除安全规则 */
  removeRule(ruleId: string): void
}

/**
 * 安全检查结果接口
 */
export interface SecurityCheckResult {
  /** 是否通过 */
  passed: boolean
  /** 风险级别 */
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  /** 错误消息 */
  message?: string
  /** 建议 */
  suggestions?: string[]
}

/**
 * 安全规则接口
 */
export interface SecurityRule {
  /** 规则ID */
  id: string
  /** 规则名称 */
  name: string
  /** 规则描述 */
  description: string
  /** 规则类型 */
  type: 'code' | 'api' | 'global' | 'custom'
  /** 规则模式 */
  pattern: string | RegExp
  /** 风险级别 */
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  /** 是否启用 */
  enabled: boolean
  /** 检查函数 */
  check?: (input: any) => SecurityCheckResult
}