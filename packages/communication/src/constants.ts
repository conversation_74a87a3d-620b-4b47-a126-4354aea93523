/**
 * 通信包常量定义
 */

import { CommunicationStrategyType, MessageType, MessagePriority } from './enums'

/**
 * 通信策略列表
 */
export const COMMUNICATION_STRATEGIES: CommunicationStrategyType[] = [
  CommunicationStrategyType.EVENT_BUS,
  CommunicationStrategyType.POST_MESSAGE,
  CommunicationStrategyType.SHARED_STATE,
  CommunicationStrategyType.BROADCAST_CHANNEL,
  CommunicationStrategyType.WEBSOCKET,
  CommunicationStrategyType.STORAGE
]

/**
 * 默认通信配置
 */
export const DEFAULT_COMMUNICATION_CONFIG = {
  enabled: true,
  timeout: 5000,
  retryCount: 3,
  bufferSize: 1000,
  compression: false,
  encryption: false,
  defaultTimeout: 5000,
  maxMessageSize: 1024 * 1024, // 1MB
  maxSubscriptions: 1000,
  enablePerformanceMonitoring: true,
  enablePersistence: false
}

/**
 * 通信事件类型
 */
export const COMMUNICATION_EVENTS = {
  STRATEGY_ADDED: 'strategyAdded',
  STRATEGY_REMOVED: 'strategyRemoved',
  MESSAGE_SENT: 'messageSent',
  MESSAGE_RECEIVED: 'messageReceived',
  SUBSCRIBED: 'subscribed',
  UNSUBSCRIBED: 'unsubscribed',
  STARTED: 'started',
  STOPPED: 'stopped',
  DESTROYED: 'destroyed',
  ERROR: 'error'
} as const

/**
 * 消息类型列表
 */
export const MESSAGE_TYPES: MessageType[] = [
  MessageType.EVENT,
  MessageType.REQUEST,
  MessageType.RESPONSE,
  MessageType.NOTIFICATION,
  MessageType.COMMAND,
  MessageType.QUERY,
  MessageType.STATE,
  MessageType.ERROR
]

/**
 * 消息优先级列表
 */
export const MESSAGE_PRIORITIES: MessagePriority[] = [
  MessagePriority.LOW,
  MessagePriority.NORMAL,
  MessagePriority.HIGH,
  MessagePriority.URGENT
]

/**
 * 默认消息配置
 */
export const DEFAULT_MESSAGE_CONFIG = {
  type: MessageType.EVENT,
  priority: MessagePriority.NORMAL,
  ttl: 60000, // 1分钟
  retryCount: 3,
  timeout: 5000
}

/**
 * 默认超时时间
 */
export const DEFAULT_TIMEOUT = 5000

/**
 * 最大消息大小（字节）
 */
export const MAX_MESSAGE_SIZE = 1024 * 1024 // 1MB

/**
 * 最大订阅数量
 */
export const MAX_SUBSCRIBERS = 1000

/**
 * 心跳间隔（毫秒）
 */
export const HEARTBEAT_INTERVAL = 30000 // 30秒

/**
 * 重连间隔（毫秒）
 */
export const RECONNECT_INTERVAL = 5000 // 5秒

/**
 * 最大重连次数
 */
export const MAX_RECONNECT_ATTEMPTS = 5

/**
 * 通信错误类型
 */
export const COMMUNICATION_ERROR_TYPES = {
  STRATEGY_NOT_FOUND: 'STRATEGY_NOT_FOUND',
  STRATEGY_DISABLED: 'STRATEGY_DISABLED',
  STRATEGY_CREATION_FAILED: 'STRATEGY_CREATION_FAILED',
  MESSAGE_VALIDATION_FAILED: 'MESSAGE_VALIDATION_FAILED',
  MESSAGE_SIZE_EXCEEDED: 'MESSAGE_SIZE_EXCEEDED',
  SUBSCRIPTION_LIMIT_EXCEEDED: 'SUBSCRIPTION_LIMIT_EXCEEDED',
  TIMEOUT: 'TIMEOUT',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERIALIZATION_ERROR: 'SERIALIZATION_ERROR',
  ENCRYPTION_ERROR: 'ENCRYPTION_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  UNKNOWN_STRATEGY: 'UNKNOWN_STRATEGY',
  NO_STRATEGY_AVAILABLE: 'NO_STRATEGY_AVAILABLE',
  START_FAILED: 'START_FAILED',
  STOP_FAILED: 'STOP_FAILED',
  DESTROY_FAILED: 'DESTROY_FAILED'
} as const

/**
 * EventBus 默认配置
 */
export const DEFAULT_EVENT_BUS_CONFIG = {
  maxListeners: 100,
  enableWildcard: true,
  namespace: 'micro-core'
}

/**
 * PostMessage 默认配置
 */
export const DEFAULT_POST_MESSAGE_CONFIG = {
  targetOrigin: '*',
  verifyOrigin: true,
  allowedOrigins: []
}

/**
 * SharedState 默认配置
 */
export const DEFAULT_SHARED_STATE_CONFIG = {
  initialState: {},
  deepWatch: true,
  persistence: false
}

/**
 * BroadcastChannel 默认配置
 */
export const DEFAULT_BROADCAST_CHANNEL_CONFIG = {
  channelName: 'micro-core-channel',
  autoReconnect: true
}

/**
 * WebSocket 默认配置
 */
export const DEFAULT_WEBSOCKET_CONFIG = {
  protocols: [],
  heartbeatInterval: HEARTBEAT_INTERVAL,
  reconnectInterval: RECONNECT_INTERVAL,
  maxReconnectAttempts: MAX_RECONNECT_ATTEMPTS,
  autoReconnect: true
}

/**
 * Storage 默认配置
 */
export const DEFAULT_STORAGE_CONFIG = {
  storageType: 'localStorage' as const,
  keyPrefix: 'micro-core-',
  pollingInterval: 1000,
  enablePolling: true
}

/**
 * 策略优先级（按性能排序）
 */
export const STRATEGY_PRIORITY = [
  CommunicationStrategyType.SHARED_STATE,
  CommunicationStrategyType.EVENT_BUS,
  CommunicationStrategyType.BROADCAST_CHANNEL,
  CommunicationStrategyType.POST_MESSAGE,
  CommunicationStrategyType.WEBSOCKET,
  CommunicationStrategyType.STORAGE
]

/**
 * 策略特性映射
 */
export const STRATEGY_FEATURES = {
  [CommunicationStrategyType.EVENT_BUS]: {
    crossOrigin: false,
    crossTab: false,
    persistence: false,
    realtime: true,
    reliability: 'high',
    performance: 'high'
  },
  [CommunicationStrategyType.POST_MESSAGE]: {
    crossOrigin: true,
    crossTab: false,
    persistence: false,
    realtime: true,
    reliability: 'medium',
    performance: 'medium'
  },
  [CommunicationStrategyType.SHARED_STATE]: {
    crossOrigin: false,
    crossTab: false,
    persistence: false,
    realtime: true,
    reliability: 'high',
    performance: 'high'
  },
  [CommunicationStrategyType.BROADCAST_CHANNEL]: {
    crossOrigin: false,
    crossTab: true,
    persistence: false,
    realtime: true,
    reliability: 'medium',
    performance: 'medium'
  },
  [CommunicationStrategyType.WEBSOCKET]: {
    crossOrigin: true,
    crossTab: true,
    persistence: false,
    realtime: true,
    reliability: 'medium',
    performance: 'medium'
  },
  [CommunicationStrategyType.STORAGE]: {
    crossOrigin: false,
    crossTab: true,
    persistence: true,
    realtime: false,
    reliability: 'low',
    performance: 'low'
  }
} as const

/**
 * 消息头常量
 */
export const MESSAGE_HEADERS = {
  CONTENT_TYPE: 'content-type',
  CONTENT_ENCODING: 'content-encoding',
  CORRELATION_ID: 'correlation-id',
  REPLY_TO: 'reply-to',
  TIMESTAMP: 'timestamp',
  TTL: 'ttl',
  PRIORITY: 'priority',
  SOURCE: 'source',
  TARGET: 'target'
} as const

/**
 * 内容类型
 */
export const CONTENT_TYPES = {
  JSON: 'application/json',
  TEXT: 'text/plain',
  BINARY: 'application/octet-stream',
  XML: 'application/xml',
  HTML: 'text/html'
} as const

/**
 * 编码类型
 */
export const ENCODING_TYPES = {
  NONE: 'none',
  GZIP: 'gzip',
  DEFLATE: 'deflate',
  BR: 'br'
} as const

/**
 * 加密算法
 */
export const ENCRYPTION_ALGORITHMS = {
  AES_256_GCM: 'aes-256-gcm',
  AES_192_GCM: 'aes-192-gcm',
  AES_128_GCM: 'aes-128-gcm',
  CHACHA20_POLY1305: 'chacha20-poly1305'
} as const

/**
 * 性能监控指标
 */
export const PERFORMANCE_METRICS = {
  MESSAGE_SEND_TIME: 'messageSendTime',
  MESSAGE_RECEIVE_TIME: 'messageReceiveTime',
  ROUND_TRIP_TIME: 'roundTripTime',
  THROUGHPUT: 'throughput',
  ERROR_RATE: 'errorRate',
  CONNECTION_TIME: 'connectionTime',
  RECONNECTION_COUNT: 'reconnectionCount'
} as const

/**
 * 日志级别
 */
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error'
} as const