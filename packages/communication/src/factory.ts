/**
 * 通信策略工厂
 * 负责创建和管理不同类型的通信策略
 */

import type { CommunicationStrategy, CommunicationConfig } from './types'
import { CommunicationStrategyType } from './enums'
import { CommunicationError } from './errors'

// 导入策略实现
import { EventBusStrategy } from './strategies/event-bus'
import { PostMessageStrategy } from './strategies/post-message'
import { SharedStateStrategy } from './strategies/shared-state'
import { BroadcastChannelStrategy } from './strategies/broadcast-channel'
import { WebSocketStrategy } from './strategies/websocket'
import { StorageStrategy } from './strategies/storage'

export interface CommunicationStrategyFactoryConfig {
  defaultStrategy?: CommunicationStrategyType
  enabledStrategies?: CommunicationStrategyType[]
  strategyConfigs?: Record<string, any>
}

export class CommunicationStrategyFactory {
  private config: CommunicationStrategyFactoryConfig
  private strategyRegistry: Map<CommunicationStrategyType, typeof CommunicationStrategy>

  constructor(config: CommunicationStrategyFactoryConfig = {}) {
    this.config = {
      defaultStrategy: CommunicationStrategyType.EVENT_BUS,
      enabledStrategies: Object.values(CommunicationStrategyType),
      ...config
    }

    this.strategyRegistry = new Map()
    this.registerDefaultStrategies()
  }

  /**
   * 创建通信策略
   */
  createStrategy(type: CommunicationStrategyType | string, config: CommunicationConfig = {}): CommunicationStrategy {
    const strategyType = typeof type === 'string' ? type as CommunicationStrategyType : type

    if (!this.isStrategyEnabled(strategyType)) {
      throw new CommunicationError(`策略 ${strategyType} 未启用`, 'STRATEGY_DISABLED')
    }

    const StrategyClass = this.getStrategyClass(strategyType)
    if (!StrategyClass) {
      throw new CommunicationError(`未知的策略类型: ${strategyType}`, 'UNKNOWN_STRATEGY')
    }

    try {
      // 合并配置
      const finalConfig = {
        ...this.config.strategyConfigs?.[strategyType],
        ...config
      }

      return new StrategyClass(finalConfig) as CommunicationStrategy
    } catch (error) {
      throw new CommunicationError(`创建策略失败: ${error}`, 'STRATEGY_CREATION_FAILED')
    }
  }

  /**
   * 注册策略
   */
  registerStrategy(type: CommunicationStrategyType, StrategyClass: typeof CommunicationStrategy): void {
    this.strategyRegistry.set(type, StrategyClass)
  }

  /**
   * 取消注册策略
   */
  unregisterStrategy(type: CommunicationStrategyType): void {
    this.strategyRegistry.delete(type)
  }

  /**
   * 获取可用策略列表
   */
  getAvailableStrategies(): CommunicationStrategyType[] {
    return Array.from(this.strategyRegistry.keys()).filter(type => 
      this.isStrategyEnabled(type)
    )
  }

  /**
   * 检查策略是否可用
   */
  isStrategyAvailable(type: CommunicationStrategyType): boolean {
    return this.strategyRegistry.has(type) && this.isStrategyEnabled(type)
  }

  /**
   * 获取默认策略
   */
  getDefaultStrategy(): CommunicationStrategyType {
    return this.config.defaultStrategy!
  }

  /**
   * 设置默认策略
   */
  setDefaultStrategy(type: CommunicationStrategyType): void {
    if (!this.isStrategyAvailable(type)) {
      throw new CommunicationError(`策略 ${type} 不可用`, 'STRATEGY_UNAVAILABLE')
    }
    this.config.defaultStrategy = type
  }

  /**
   * 自动选择最佳策略
   */
  selectBestStrategy(requirements?: {
    reliability?: boolean
    performance?: boolean
    crossOrigin?: boolean
    persistence?: boolean
  }): CommunicationStrategyType {
    const available = this.getAvailableStrategies()
    
    if (available.length === 0) {
      throw new CommunicationError('没有可用的通信策略', 'NO_AVAILABLE_STRATEGY')
    }

    // 如果没有特殊要求，返回默认策略
    if (!requirements) {
      return this.config.defaultStrategy!
    }

    // 根据需求选择策略
    if (requirements.crossOrigin) {
      if (available.includes(CommunicationStrategyType.POST_MESSAGE)) {
        return CommunicationStrategyType.POST_MESSAGE
      }
      if (available.includes(CommunicationStrategyType.WEBSOCKET)) {
        return CommunicationStrategyType.WEBSOCKET
      }
    }

    if (requirements.persistence) {
      if (available.includes(CommunicationStrategyType.STORAGE)) {
        return CommunicationStrategyType.STORAGE
      }
    }

    if (requirements.performance) {
      if (available.includes(CommunicationStrategyType.SHARED_STATE)) {
        return CommunicationStrategyType.SHARED_STATE
      }
      if (available.includes(CommunicationStrategyType.EVENT_BUS)) {
        return CommunicationStrategyType.EVENT_BUS
      }
    }

    if (requirements.reliability) {
      if (available.includes(CommunicationStrategyType.WEBSOCKET)) {
        return CommunicationStrategyType.WEBSOCKET
      }
      if (available.includes(CommunicationStrategyType.BROADCAST_CHANNEL)) {
        return CommunicationStrategyType.BROADCAST_CHANNEL
      }
    }

    // 返回第一个可用策略
    return available[0]
  }

  /**
   * 批量创建策略
   */
  createMultipleStrategies(configs: Array<{
    type: CommunicationStrategyType
    config?: CommunicationConfig
  }>): CommunicationStrategy[] {
    return configs.map(({ type, config }) => this.createStrategy(type, config))
  }

  /**
   * 检测浏览器支持
   */
  detectBrowserSupport(): Record<CommunicationStrategyType, boolean> {
    const support: Record<CommunicationStrategyType, boolean> = {} as any

    // 检测 EventBus 支持（总是支持）
    support[CommunicationStrategyType.EVENT_BUS] = true

    // 检测 PostMessage 支持
    support[CommunicationStrategyType.POST_MESSAGE] = typeof window !== 'undefined' && 'postMessage' in window

    // 检测 SharedState 支持（总是支持）
    support[CommunicationStrategyType.SHARED_STATE] = true

    // 检测 BroadcastChannel 支持
    support[CommunicationStrategyType.BROADCAST_CHANNEL] = typeof window !== 'undefined' && 'BroadcastChannel' in window

    // 检测 WebSocket 支持
    support[CommunicationStrategyType.WEBSOCKET] = typeof window !== 'undefined' && 'WebSocket' in window

    // 检测 Storage 支持
    support[CommunicationStrategyType.STORAGE] = typeof window !== 'undefined' && 'localStorage' in window

    return support
  }

  /**
   * 获取策略信息
   */
  getStrategyInfo(type: CommunicationStrategyType): {
    name: string
    description: string
    features: string[]
    limitations: string[]
    browserSupport: boolean
  } {
    const browserSupport = this.detectBrowserSupport()

    const strategyInfo = {
      [CommunicationStrategyType.EVENT_BUS]: {
        name: '事件总线',
        description: '基于事件发布订阅模式的同步通信',
        features: ['高性能', '同步通信', '类型安全'],
        limitations: ['仅限同一页面', '无持久化']
      },
      [CommunicationStrategyType.POST_MESSAGE]: {
        name: 'PostMessage',
        description: '基于 window.postMessage 的跨域通信',
        features: ['跨域支持', '安全性好', '浏览器原生支持'],
        limitations: ['异步通信', '需要窗口引用']
      },
      [CommunicationStrategyType.SHARED_STATE]: {
        name: '共享状态',
        description: '基于共享内存对象的状态同步',
        features: ['状态同步', '高性能', '响应式更新'],
        limitations: ['仅限同一页面', '内存占用']
      },
      [CommunicationStrategyType.BROADCAST_CHANNEL]: {
        name: '广播频道',
        description: '基于 BroadcastChannel API 的跨标签页通信',
        features: ['跨标签页', '同源通信', '实时性好'],
        limitations: ['现代浏览器支持', '同源限制']
      },
      [CommunicationStrategyType.WEBSOCKET]: {
        name: 'WebSocket',
        description: '基于 WebSocket 的实时双向通信',
        features: ['实时通信', '跨域支持', '可靠性高'],
        limitations: ['需要服务器支持', '网络依赖']
      },
      [CommunicationStrategyType.STORAGE]: {
        name: '存储通信',
        description: '基于 localStorage/sessionStorage 的持久化通信',
        features: ['持久化', '跨标签页', '离线支持'],
        limitations: ['存储限制', '轮询机制', '性能较低']
      }
    }

    return {
      ...strategyInfo[type],
      browserSupport: browserSupport[type]
    }
  }

  /**
   * 注册默认策略
   */
  private registerDefaultStrategies(): void {
    this.strategyRegistry.set(CommunicationStrategyType.EVENT_BUS, EventBusStrategy as any)
    this.strategyRegistry.set(CommunicationStrategyType.POST_MESSAGE, PostMessageStrategy as any)
    this.strategyRegistry.set(CommunicationStrategyType.SHARED_STATE, SharedStateStrategy as any)
    this.strategyRegistry.set(CommunicationStrategyType.BROADCAST_CHANNEL, BroadcastChannelStrategy as any)
    this.strategyRegistry.set(CommunicationStrategyType.WEBSOCKET, WebSocketStrategy as any)
    this.strategyRegistry.set(CommunicationStrategyType.STORAGE, StorageStrategy as any)
  }

  /**
   * 获取策略类
   */
  private getStrategyClass(type: CommunicationStrategyType): typeof CommunicationStrategy | undefined {
    return this.strategyRegistry.get(type)
  }

  /**
   * 检查策略是否启用
   */
  private isStrategyEnabled(type: CommunicationStrategyType): boolean {
    return this.config.enabledStrategies?.includes(type) ?? true
  }
}