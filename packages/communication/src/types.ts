/**
 * 通信包类型定义
 */

import { CommunicationStrategyType, CommunicationState, MessageType, MessagePriority } from './enums'

/**
 * 消息接口
 */
export interface Message {
  /** 消息ID */
  id: string
  /** 消息类型 */
  type: MessageType
  /** 主题/频道 */
  topic: string
  /** 消息数据 */
  data: any
  /** 时间戳 */
  timestamp: number
  /** 优先级 */
  priority: MessagePriority
  /** 消息来源 */
  source: string
  /** 目标接收者 */
  target?: string
  /** 关联ID（用于请求-响应） */
  correlationId?: string
  /** 消息头 */
  headers?: Record<string, any>
  /** TTL（生存时间） */
  ttl?: number
}

/**
 * 消息处理器
 */
export type MessageHandler = (message: Message) => void | Promise<void>

/**
 * 消息过滤器
 */
export type MessageFilter = (message: Message) => boolean

/**
 * 消息转换器
 */
export type MessageTransformer = (message: Message) => Message

/**
 * 通信策略接口
 */
export interface CommunicationStrategy {
  /** 策略名称 */
  readonly name: string
  /** 策略类型 */
  readonly type: CommunicationStrategyType
  /** 当前状态 */
  readonly state: CommunicationState

  /** 启动策略 */
  start(): void
  /** 停止策略 */
  stop(): void
  /** 销毁策略 */
  destroy(): void
  /** 发送消息 */
  send(message: Message): void
  /** 设置消息处理器 */
  onMessage(handler: MessageHandler): void
  /** 移除消息处理器 */
  offMessage(handler: MessageHandler): void
  /** 获取配置 */
  getConfig(): CommunicationConfig
  /** 更新配置 */
  updateConfig(config: Partial<CommunicationConfig>): void
}

/**
 * 通信配置
 */
export interface CommunicationConfig {
  /** 策略名称 */
  name?: string
  /** 是否启用 */
  enabled?: boolean
  /** 超时时间 */
  timeout?: number
  /** 重试次数 */
  retryCount?: number
  /** 缓冲区大小 */
  bufferSize?: number
  /** 是否启用压缩 */
  compression?: boolean
  /** 是否启用加密 */
  encryption?: boolean
  /** 自定义配置 */
  [key: string]: any
}

/**
 * 通信管理器配置
 */
export interface CommunicationManagerConfig extends CommunicationConfig {
  /** 默认策略列表 */
  defaultStrategies?: CommunicationStrategyType[]
  /** 默认超时时间 */
  defaultTimeout?: number
  /** 最大消息大小 */
  maxMessageSize?: number
  /** 最大订阅数 */
  maxSubscriptions?: number
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean
  /** 是否启用消息持久化 */
  enablePersistence?: boolean
  /** 消息过滤器 */
  messageFilters?: MessageFilter[]
  /** 消息转换器 */
  messageTransformers?: MessageTransformer[]
  /** 错误处理器 */
  errorHandler?: (error: Error, message?: Message) => void
}

/**
 * 订阅选项
 */
export interface SubscriptionOptions {
  /** 消息过滤器 */
  filter?: MessageFilter
  /** 消息转换器 */
  transformer?: MessageTransformer
  /** 是否只接收一次 */
  once?: boolean
  /** 优先级 */
  priority?: MessagePriority
  /** 超时时间 */
  timeout?: number
}

/**
 * 发布选项
 */
export interface PublishOptions {
  /** 消息类型 */
  type?: MessageType
  /** 优先级 */
  priority?: MessagePriority
  /** 目标接收者 */
  target?: string
  /** 使用的策略 */
  strategy?: string
  /** 超时时间 */
  timeout?: number
  /** 消息来源 */
  source?: string
  /** 关联ID */
  correlationId?: string
  /** 消息头 */
  headers?: Record<string, any>
  /** TTL */
  ttl?: number
}

/**
 * 通信事件
 */
export interface CommunicationEvent {
  /** 事件类型 */
  type: string
  /** 时间戳 */
  timestamp: number
  /** 事件数据 */
  data: any
}

/**
 * 通信事件监听器
 */
export type CommunicationEventListener = (event: CommunicationEvent) => void

/**
 * 通信统计信息
 */
export interface CommunicationStats {
  /** 策略数量 */
  strategiesCount: number
  /** 订阅数量 */
  subscriptionsCount: number
  /** 已发送消息数 */
  messagesSent: number
  /** 已接收消息数 */
  messagesReceived: number
  /** 丢弃消息数 */
  messagesDropped: number
  /** 错误数量 */
  errorCount: number
  /** 平均延迟 */
  averageLatency: number
  /** 当前状态 */
  state: CommunicationState
}

/**
 * 通信指标
 */
export interface CommunicationMetrics {
  /** 已发送消息数 */
  messagesSent: number
  /** 已接收消息数 */
  messagesReceived: number
  /** 丢弃消息数 */
  messagesDropped: number
  /** 平均延迟 */
  averageLatency: number
  /** 错误数量 */
  errorCount: number
  /** 活跃订阅数 */
  activeSubscriptions: number
  /** 总延迟时间 */
  totalLatency: number
}

/**
 * EventBus 配置
 */
export interface EventBusConfig extends CommunicationConfig {
  /** 最大监听器数量 */
  maxListeners?: number
  /** 是否启用通配符 */
  enableWildcard?: boolean
  /** 事件命名空间 */
  namespace?: string
}

/**
 * PostMessage 配置
 */
export interface PostMessageConfig extends CommunicationConfig {
  /** 目标窗口 */
  targetWindow?: Window
  /** 目标源 */
  targetOrigin?: string
  /** 是否验证源 */
  verifyOrigin?: boolean
  /** 允许的源列表 */
  allowedOrigins?: string[]
}

/**
 * SharedState 配置
 */
export interface SharedStateConfig extends CommunicationConfig {
  /** 初始状态 */
  initialState?: any
  /** 是否启用深度监听 */
  deepWatch?: boolean
  /** 状态持久化 */
  persistence?: boolean
}

/**
 * BroadcastChannel 配置
 */
export interface BroadcastChannelConfig extends CommunicationConfig {
  /** 频道名称 */
  channelName?: string
  /** 是否自动重连 */
  autoReconnect?: boolean
}

/**
 * WebSocket 配置
 */
export interface WebSocketConfig extends CommunicationConfig {
  /** WebSocket URL */
  url?: string
  /** 协议 */
  protocols?: string | string[]
  /** 心跳间隔 */
  heartbeatInterval?: number
  /** 重连间隔 */
  reconnectInterval?: number
  /** 最大重连次数 */
  maxReconnectAttempts?: number
  /** 是否自动重连 */
  autoReconnect?: boolean
}

/**
 * Storage 配置
 */
export interface StorageConfig extends CommunicationConfig {
  /** 存储类型 */
  storageType?: 'localStorage' | 'sessionStorage'
  /** 存储键前缀 */
  keyPrefix?: string
  /** 轮询间隔 */
  pollingInterval?: number
  /** 是否启用轮询 */
  enablePolling?: boolean
}