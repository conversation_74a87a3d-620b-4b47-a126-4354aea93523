/**
 * 微前端通信包
 * 提供应用间通信的完整解决方案
 */

// 导出通信管理器
export { CommunicationManager } from './manager'

// 导出通信策略
export { EventBusStrategy } from './strategies/event-bus'
export { PostMessageStrategy } from './strategies/post-message'
export { SharedStateStrategy } from './strategies/shared-state'
export { BroadcastChannelStrategy } from './strategies/broadcast-channel'
export { WebSocketStrategy } from './strategies/websocket'
export { StorageStrategy } from './strategies/storage'

// 导出通信工厂
export { CommunicationStrategyFactory } from './factory'

// 导出工具函数
export {
  validateMessage,
  serializeMessage,
  deserializeMessage,
  createMessageId,
  isValidMessageType,
  formatMessage
} from './utils'

// 导出类型定义
export type {
  CommunicationStrategy,
  CommunicationConfig,
  CommunicationManagerConfig,
  Message,
  MessageHandler,
  MessageFilter,
  MessageTransformer,
  CommunicationEvent,
  CommunicationEventListener,
  CommunicationStats,
  CommunicationMetrics,
  EventBusConfig,
  PostMessageConfig,
  SharedStateConfig,
  BroadcastChannelConfig,
  WebSocketConfig,
  StorageConfig,
  MessagePriority,
  MessageType,
  CommunicationStrategyType,
  CommunicationState,
  SubscriptionOptions,
  PublishOptions
} from './types'

// 导出常量
export {
  COMMUNICATION_STRATEGIES,
  DEFAULT_COMMUNICATION_CONFIG,
  COMMUNICATION_EVENTS,
  MESSAGE_TYPES,
  MESSAGE_PRIORITIES,
  DEFAULT_TIMEOUT,
  MAX_MESSAGE_SIZE
} from './constants'

// 导出枚举
export {
  CommunicationStrategyType,
  CommunicationState,
  MessageType,
  MessagePriority
} from './enums'

// 导出错误类
export {
  CommunicationError,
  MessageValidationError,
  SerializationError
} from './errors'