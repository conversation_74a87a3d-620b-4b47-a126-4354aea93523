/**
 * SharedState 通信策略实现
 * 基于共享状态的通信机制
 */

import type { 
  CommunicationStrategy, 
  Message, 
  MessageHandler, 
  CommunicationConfig 
} from '../types'
import type { 
  SharedStateConfig, 
  BaseStrategy, 
  StrategyState, 
  StrategyPerformanceMetrics 
} from './types'
import { CommunicationStrategyType } from '../enums'
import { DEFAULT_SHARED_STATE_CONFIG } from '../constants'

/**
 * 状态变更监听器
 */
interface StateListener {
  id: string
  path: string
  handler: MessageHandler
  deep: boolean
}

/**
 * 状态历史记录
 */
interface StateHistory {
  timestamp: number
  state: any
  action: string
  path?: string
}

/**
 * SharedState 通信策略
 */
export class SharedStateStrategy implements BaseStrategy {
  private readonly name: string = CommunicationStrategyType.SHARED_STATE
  private readonly config: Required<SharedStateConfig>
  private state: StrategyState = 'idle' as StrategyState
  private sharedState: any = {}
  private listeners: Map<string, StateListener[]> = new Map()
  private history: StateHistory[] = []
  private metrics: StrategyPerformanceMetrics
  private eventHandlers: Map<string, Function[]> = new Map()
  private nextListenerId = 1
  private stateProxy: any = null

  constructor(config: SharedStateConfig = {}) {
    this.config = { ...DEFAULT_SHARED_STATE_CONFIG, ...config }
    this.metrics = this.initializeMetrics()
    this.sharedState = { ...this.config.initialState }
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): StrategyPerformanceMetrics {
    return {
      latency: 0,
      throughput: 0,
      errorRate: 0,
      reliability: 1,
      availability: 1,
      connectionTime: 0,
      reconnectionCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0
    }
  }

  /**
   * 启动策略
   */
  async start(): Promise<void> {
    if (this.state !== 'idle') {
      throw new Error(`SharedState 策略已在运行中，当前状态: ${this.state}`)
    }

    try {
      this.state = 'connecting' as StrategyState
      await this.connect()
      this.state = 'connected' as StrategyState
      this.emit('connected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 停止策略
   */
  async stop(): Promise<void> {
    if (this.state === 'idle' || this.state === 'disconnected') {
      return
    }

    try {
      this.state = 'disconnecting' as StrategyState
      await this.disconnect()
      this.state = 'disconnected' as StrategyState
      this.emit('disconnected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 销毁策略
   */
  async destroy(): Promise<void> {
    await this.stop()
    this.listeners.clear()
    this.history = []
    this.sharedState = {}
    this.stateProxy = null
    this.eventHandlers.clear()
    this.state = 'destroyed' as StrategyState
  }

  /**
   * 连接
   */
  async connect(): Promise<void> {
    const startTime = Date.now()

    // 创建状态代理
    this.stateProxy = this.createStateProxy()

    // 从持久化存储恢复状态
    if (this.config.persistence) {
      await this.loadPersistedState()
    }

    this.metrics.connectionTime = Date.now() - startTime
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    // 保存状态到持久化存储
    if (this.config.persistence) {
      await this.persistState()
    }

    this.stateProxy = null
  }

  /**
   * 重连
   */
  async reconnect(): Promise<void> {
    await this.disconnect()
    await this.connect()
    this.metrics.reconnectionCount++
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.state === 'connected'
  }

  /**
   * 发送消息（更新状态）
   */
  async send(message: Message): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('SharedState 策略未连接')
    }

    const startTime = Date.now()

    try {
      // 验证消息
      this.validateMessage(message)

      // 解析状态路径和值
      const { path, value, action } = this.parseMessage(message)

      // 更新状态
      const oldValue = this.getStateValue(path)
      this.setStateValue(path, value)

      // 记录历史
      if (this.config.enableHistory) {
        this.addToHistory(action, path)
      }

      // 触发监听器
      this.notifyListeners(path, value, oldValue)

      // 更新指标
      this.updateSendMetrics(startTime, message)
      this.emit('messageSent', message)

    } catch (error) {
      this.handleError(error as Error)
      throw error
    }
  }

  /**
   * 订阅状态变更
   */
  subscribe(topic: string, handler: MessageHandler): () => void {
    if (!this.isConnected()) {
      throw new Error('SharedState 策略未连接')
    }

    const listenerId = `listener_${this.nextListenerId++}`
    const listener: StateListener = {
      id: listenerId,
      path: topic,
      handler,
      deep: this.config.deepWatch
    }

    const listeners = this.listeners.get(topic) || []
    listeners.push(listener)
    this.listeners.set(topic, listeners)

    this.emit('subscribed', topic)

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(topic, listenerId)
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(topic: string, listenerId?: string): void {
    const listeners = this.listeners.get(topic)
    if (listeners) {
      const filteredListeners = listeners.filter(listener => 
        !listenerId || listener.id !== listenerId
      )
      
      if (filteredListeners.length === 0) {
        this.listeners.delete(topic)
      } else {
        this.listeners.set(topic, filteredListeners)
      }
    }

    this.emit('unsubscribed', topic)
  }

  /**
   * 一次性订阅
   */
  once(topic: string, handler: MessageHandler): () => void {
    const wrappedHandler: MessageHandler = (message) => {
      handler(message)
      this.unsubscribe(topic)
    }

    return this.subscribe(topic, wrappedHandler)
  }

  /**
   * 获取状态值
   */
  getState(path?: string): any {
    if (!path) {
      return { ...this.sharedState }
    }

    return this.getStateValue(path)
  }

  /**
   * 设置状态值
   */
  setState(path: string, value: any): void {
    const message: Message = {
      id: `state_${Date.now()}`,
      topic: path,
      payload: { path, value, action: 'set' },
      timestamp: Date.now()
    }

    this.send(message)
  }

  /**
   * 合并状态
   */
  mergeState(path: string, value: any): void {
    const message: Message = {
      id: `merge_${Date.now()}`,
      topic: path,
      payload: { path, value, action: 'merge' },
      timestamp: Date.now()
    }

    this.send(message)
  }

  /**
   * 删除状态
   */
  deleteState(path: string): void {
    const message: Message = {
      id: `delete_${Date.now()}`,
      topic: path,
      payload: { path, action: 'delete' },
      timestamp: Date.now()
    }

    this.send(message)
  }

  /**
   * 获取历史记录
   */
  getHistory(): StateHistory[] {
    return [...this.history]
  }

  /**
   * 清空历史记录
   */
  clearHistory(): void {
    this.history = []
  }

  /**
   * 获取策略名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取策略状态
   */
  get state(): StrategyState {
    return this.state
  }

  /**
   * 获取性能指标
   */
  getMetrics(): StrategyPerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics()
  }

  /**
   * 事件监听
   */
  on<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string) || []
    handlers.push(listener)
    this.eventHandlers.set(event as string, handlers)
  }

  /**
   * 移除事件监听
   */
  off<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      const index = handlers.indexOf(listener)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit<K extends keyof any>(event: K, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          this.handleError(error as Error)
        }
      })
    }
  }

  /**
   * 创建状态代理
   */
  private createStateProxy(): any {
    return new Proxy(this.sharedState, {
      set: (target, property, value) => {
        const path = String(property)
        const oldValue = target[property]
        
        target[property] = value
        
        // 触发监听器
        this.notifyListeners(path, value, oldValue)
        
        return true
      },
      
      get: (target, property) => {
        return target[property]
      },
      
      deleteProperty: (target, property) => {
        const path = String(property)
        const oldValue = target[property]
        
        delete target[property]
        
        // 触发监听器
        this.notifyListeners(path, undefined, oldValue)
        
        return true
      }
    })
  }

  /**
   * 验证消息
   */
  private validateMessage(message: Message): void {
    if (!message.topic) {
      throw new Error('消息主题不能为空')
    }

    if (!message.payload) {
      throw new Error('消息负载不能为空')
    }

    if (this.config.enableValidation && this.config.validator) {
      if (!this.config.validator(message.payload)) {
        throw new Error('消息验证失败')
      }
    }
  }

  /**
   * 解析消息
   */
  private parseMessage(message: Message): { path: string; value: any; action: string } {
    const { path, value, action = 'set' } = message.payload
    
    if (!path) {
      throw new Error('状态路径不能为空')
    }

    return { path, value, action }
  }

  /**
   * 获取状态值
   */
  private getStateValue(path: string): any {
    const keys = path.split('.')
    let current = this.sharedState

    for (const key of keys) {
      if (current === null || current === undefined) {
        return undefined
      }
      current = current[key]
    }

    return current
  }

  /**
   * 设置状态值
   */
  private setStateValue(path: string, value: any): void {
    const keys = path.split('.')
    const lastKey = keys.pop()!
    let current = this.sharedState

    // 创建嵌套对象路径
    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }

    // 设置值
    if (typeof value === 'object' && value !== null && typeof current[lastKey] === 'object') {
      // 合并对象
      current[lastKey] = { ...current[lastKey], ...value }
    } else {
      current[lastKey] = value
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(path: string, newValue: any, oldValue: any): void {
    // 精确匹配
    const exactListeners = this.listeners.get(path) || []
    
    // 深度监听匹配
    const deepListeners: StateListener[] = []
    if (this.config.deepWatch) {
      this.listeners.forEach((listeners, listenerPath) => {
        if (path.startsWith(listenerPath + '.') || listenerPath.startsWith(path + '.')) {
          deepListeners.push(...listeners.filter(l => l.deep))
        }
      })
    }

    const allListeners = [...exactListeners, ...deepListeners]

    allListeners.forEach(listener => {
      try {
        const message: Message = {
          id: `state_change_${Date.now()}`,
          topic: path,
          payload: { path, newValue, oldValue },
          timestamp: Date.now()
        }

        listener.handler(message)
        this.metrics.messagesReceived++
      } catch (error) {
        this.handleError(error as Error)
      }
    })
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(action: string, path?: string): void {
    const historyEntry: StateHistory = {
      timestamp: Date.now(),
      state: { ...this.sharedState },
      action,
      path
    }

    this.history.push(historyEntry)

    // 限制历史记录大小
    if (this.history.length > this.config.maxHistorySize) {
      this.history.shift()
    }
  }

  /**
   * 加载持久化状态
   */
  private async loadPersistedState(): Promise<void> {
    try {
      const key = this.config.storageKey || 'micro-core-shared-state'
      const stored = localStorage.getItem(key)
      
      if (stored) {
        const parsedState = JSON.parse(stored)
        this.sharedState = { ...this.config.initialState, ...parsedState }
      }
    } catch (error) {
      console.warn('加载持久化状态失败:', error)
    }
  }

  /**
   * 持久化状态
   */
  private async persistState(): Promise<void> {
    try {
      const key = this.config.storageKey || 'micro-core-shared-state'
      localStorage.setItem(key, JSON.stringify(this.sharedState))
    } catch (error) {
      console.warn('持久化状态失败:', error)
    }
  }

  /**
   * 更新发送指标
   */
  private updateSendMetrics(startTime: number, message: Message): void {
    const endTime = Date.now()
    const latency = endTime - startTime

    this.metrics.latency = (this.metrics.latency + latency) / 2
    this.metrics.messagesSent++
    this.metrics.bytesTransferred += this.calculateMessageSize(message)
    this.metrics.throughput = this.metrics.messagesSent / ((endTime - startTime) / 1000)
  }

  /**
   * 计算消息大小
   */
  private calculateMessageSize(message: Message): number {
    try {
      return JSON.stringify(message).length
    } catch {
      return 0
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    this.metrics.errorRate = (this.metrics.errorRate + 1) / (this.metrics.messagesSent + this.metrics.messagesReceived + 1)
    console.error('SharedState 策略错误:', error)
  }
}