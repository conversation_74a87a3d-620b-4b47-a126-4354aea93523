/**
 * 通信策略导出
 */

export { EventBusStrategy } from './event-bus'
export { PostMessageStrategy } from './post-message'
export { SharedStateStrategy } from './shared-state'
export { BroadcastChannelStrategy } from './broadcast-channel'
export { WebSocketStrategy } from './websocket'
export { StorageStrategy } from './storage'

export type {
  EventBusConfig,
  PostMessageConfig,
  SharedStateConfig,
  BroadcastChannelConfig,
  WebSocketConfig,
  StorageConfig
} from './types'