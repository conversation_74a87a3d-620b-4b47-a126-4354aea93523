/**
 * WebSocket 通信策略实现
 * 基于 WebSocket 的实时双向通信机制
 */

import type { 
  CommunicationStrategy, 
  Message, 
  MessageHandler, 
  CommunicationConfig 
} from '../types'
import type { 
  WebSocketConfig, 
  BaseStrategy, 
  StrategyState, 
  StrategyPerformanceMetrics 
} from './types'
import { CommunicationStrategyType } from '../enums'
import { DEFAULT_WEBSOCKET_CONFIG } from '../constants'

/**
 * WebSocket 监听器信息
 */
interface WebSocketListener {
  id: string
  topic: string
  handler: MessageHandler
}

/**
 * WebSocket 通信策略
 */
export class WebSocketStrategy implements BaseStrategy {
  private readonly name: string = CommunicationStrategyType.WEBSOCKET
  private readonly config: Required<WebSocketConfig>
  private state: StrategyState = 'idle' as StrategyState
  private socket: WebSocket | null = null
  private listeners: Map<string, WebSocketListener[]> = new Map()
  private metrics: StrategyPerformanceMetrics
  private eventHandlers: Map<string, Function[]> = new Map()
  private nextListenerId = 1
  private reconnectTimer: NodeJS.Timeout | null = null
  private heartbeatTimer: NodeJS.Timeout | null = null
  private reconnectAttempts = 0
  private messageQueue: Message[] = []

  constructor(config: WebSocketConfig = {}) {
    this.config = { ...DEFAULT_WEBSOCKET_CONFIG, ...config }
    this.metrics = this.initializeMetrics()
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): StrategyPerformanceMetrics {
    return {
      latency: 0,
      throughput: 0,
      errorRate: 0,
      reliability: 1,
      availability: 1,
      connectionTime: 0,
      reconnectionCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0
    }
  }

  /**
   * 启动策略
   */
  async start(): Promise<void> {
    if (this.state !== 'idle') {
      throw new Error(`WebSocket 策略已在运行中，当前状态: ${this.state}`)
    }

    try {
      this.state = 'connecting' as StrategyState
      await this.connect()
      this.state = 'connected' as StrategyState
      this.emit('connected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 停止策略
   */
  async stop(): Promise<void> {
    if (this.state === 'idle' || this.state === 'disconnected') {
      return
    }

    try {
      this.state = 'disconnecting' as StrategyState
      await this.disconnect()
      this.state = 'disconnected' as StrategyState
      this.emit('disconnected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 销毁策略
   */
  async destroy(): Promise<void> {
    await this.stop()
    this.listeners.clear()
    this.messageQueue = []
    this.eventHandlers.clear()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    this.state = 'destroyed' as StrategyState
  }

  /**
   * 连接
   */
  async connect(): Promise<void> {
    const startTime = Date.now()

    return new Promise((resolve, reject) => {
      try {
        if (!this.config.url) {
          throw new Error('WebSocket URL 未配置')
        }

        // 创建 WebSocket 连接
        this.socket = new WebSocket(this.config.url, this.config.protocols)

        // 设置二进制数据类型
        if (this.config.enableBinaryMessages) {
          this.socket.binaryType = this.config.binaryType || 'arraybuffer'
        }

        // 连接成功
        this.socket.onopen = () => {
          this.metrics.connectionTime = Date.now() - startTime
          this.reconnectAttempts = 0
          
          // 启动心跳
          this.startHeartbeat()
          
          // 发送队列中的消息
          this.flushMessageQueue()
          
          resolve()
        }

        // 接收消息
        this.socket.onmessage = (event) => {
          this.handleMessage(event)
        }

        // 连接关闭
        this.socket.onclose = (event) => {
          this.handleClose(event)
        }

        // 连接错误
        this.socket.onerror = (event) => {
          const error = new Error(`WebSocket 连接错误: ${event}`)
          this.handleError(error)
          reject(error)
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.socket) {
      this.socket.close(1000, '正常关闭')
      this.socket = null
    }
  }

  /**
   * 重连
   */
  async reconnect(): Promise<void> {
    if (!this.config.autoReconnect) {
      return
    }

    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      throw new Error('重连次数已达上限')
    }

    try {
      await this.disconnect()
      await new Promise(resolve => setTimeout(resolve, this.config.reconnectInterval))
      await this.connect()
      
      this.metrics.reconnectionCount++
      this.reconnectAttempts++
      
    } catch (error) {
      this.scheduleReconnect()
      throw error
    }
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.state === 'connected' && 
           this.socket !== null && 
           this.socket.readyState === WebSocket.OPEN
  }

  /**
   * 发送消息
   */
  async send(message: Message): Promise<void> {
    const startTime = Date.now()

    try {
      // 验证消息
      this.validateMessage(message)

      // 如果未连接，将消息加入队列
      if (!this.isConnected()) {
        this.messageQueue.push(message)
        return
      }

      // 包装消息
      const wrappedMessage = this.wrapMessage(message)

      // 序列化消息
      const serializedMessage = this.serializeMessage(wrappedMessage)

      // 发送消息
      this.socket!.send(serializedMessage)

      // 更新指标
      this.updateSendMetrics(startTime, message)
      this.emit('messageSent', message)

    } catch (error) {
      this.handleError(error as Error)
      throw error
    }
  }

  /**
   * 订阅消息
   */
  subscribe(topic: string, handler: MessageHandler): () => void {
    const listenerId = `listener_${this.nextListenerId++}`
    const listener: WebSocketListener = {
      id: listenerId,
      topic,
      handler
    }

    const listeners = this.listeners.get(topic) || []
    listeners.push(listener)
    this.listeners.set(topic, listeners)

    this.emit('subscribed', topic)

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(topic, listenerId)
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(topic: string, listenerId?: string): void {
    const listeners = this.listeners.get(topic)
    if (listeners) {
      const filteredListeners = listeners.filter(listener => 
        !listenerId || listener.id !== listenerId
      )
      
      if (filteredListeners.length === 0) {
        this.listeners.delete(topic)
      } else {
        this.listeners.set(topic, filteredListeners)
      }
    }

    this.emit('unsubscribed', topic)
  }

  /**
   * 一次性订阅
   */
  once(topic: string, handler: MessageHandler): () => void {
    const wrappedHandler: MessageHandler = (message) => {
      handler(message)
      this.unsubscribe(topic)
    }

    return this.subscribe(topic, wrappedHandler)
  }

  /**
   * 获取策略名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取策略状态
   */
  get state(): StrategyState {
    return this.state
  }

  /**
   * 获取性能指标
   */
  getMetrics(): StrategyPerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics()
  }

  /**
   * 事件监听
   */
  on<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string) || []
    handlers.push(listener)
    this.eventHandlers.set(event as string, handlers)
  }

  /**
   * 移除事件监听
   */
  off<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      const index = handlers.indexOf(listener)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit<K extends keyof any>(event: K, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          this.handleError(error as Error)
        }
      })
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      // 反序列化消息
      const messageData = this.deserializeMessage(event.data)

      // 解包消息
      const message = this.unwrapMessage(messageData)
      if (!message) {
        return
      }

      // 处理心跳响应
      if (message.topic === '__heartbeat__') {
        return
      }

      // 查找匹配的监听器
      const listeners = this.listeners.get(message.topic) || []
      
      // 执行处理器
      listeners.forEach(listener => {
        try {
          listener.handler(message)
          this.metrics.messagesReceived++
        } catch (error) {
          this.handleError(error as Error)
        }
      })

      this.emit('messageReceived', message)

    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 处理连接关闭
   */
  private handleClose(event: CloseEvent): void {
    this.stopHeartbeat()

    if (event.code !== 1000 && this.config.autoReconnect) {
      // 非正常关闭，尝试重连
      this.scheduleReconnect()
    }
  }

  /**
   * 验证消息
   */
  private validateMessage(message: Message): void {
    if (!message.topic) {
      throw new Error('消息主题不能为空')
    }

    if (message.payload === undefined) {
      throw new Error('消息负载不能为空')
    }
  }

  /**
   * 包装消息
   */
  private wrapMessage(message: Message): any {
    return {
      type: 'micro-core-websocket',
      timestamp: Date.now(),
      ...message
    }
  }

  /**
   * 解包消息
   */
  private unwrapMessage(data: any): Message | null {
    if (!data || data.type !== 'micro-core-websocket') {
      return null
    }

    return {
      id: data.id,
      topic: data.topic,
      payload: data.payload,
      timestamp: data.timestamp,
      source: data.source,
      target: data.target
    }
  }

  /**
   * 序列化消息
   */
  private serializeMessage(message: any): string | ArrayBuffer {
    if (this.config.enableBinaryMessages && message.payload instanceof ArrayBuffer) {
      // 二进制消息处理
      return message.payload
    }

    // JSON 序列化
    return JSON.stringify(message)
  }

  /**
   * 反序列化消息
   */
  private deserializeMessage(data: string | ArrayBuffer): any {
    if (data instanceof ArrayBuffer) {
      // 二进制消息处理
      return { payload: data }
    }

    // JSON 反序列化
    return JSON.parse(data as string)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (this.heartbeatTimer) {
      return
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        const heartbeatMessage = {
          type: 'micro-core-websocket',
          topic: '__heartbeat__',
          payload: { timestamp: Date.now() },
          timestamp: Date.now()
        }

        try {
          this.socket!.send(JSON.stringify(heartbeatMessage))
        } catch (error) {
          this.handleError(error as Error)
        }
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 刷新消息队列
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!
      this.send(message).catch(error => {
        console.warn('发送队列消息失败:', error)
      })
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (!this.config.autoReconnect || this.reconnectTimer) {
      return
    }

    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = null
      try {
        await this.reconnect()
      } catch (error) {
        console.warn('重连失败:', error)
      }
    }, this.config.reconnectInterval)
  }

  /**
   * 更新发送指标
   */
  private updateSendMetrics(startTime: number, message: Message): void {
    const endTime = Date.now()
    const latency = endTime - startTime

    this.metrics.latency = (this.metrics.latency + latency) / 2
    this.metrics.messagesSent++
    this.metrics.bytesTransferred += this.calculateMessageSize(message)
    this.metrics.throughput = this.metrics.messagesSent / ((endTime - startTime) / 1000)
  }

  /**
   * 计算消息大小
   */
  private calculateMessageSize(message: Message): number {
    try {
      return JSON.stringify(message).length
    } catch {
      return 0
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    this.metrics.errorRate = (this.metrics.errorRate + 1) / (this.metrics.messagesSent + this.metrics.messagesReceived + 1)
    
    // 如果是连接错误且启用自动重连，则尝试重连
    if (this.config.autoReconnect && this.state === 'connected') {
      this.scheduleReconnect()
    }
    
    console.error('WebSocket 策略错误:', error)
  }
}