/**
 * PostMessage 通信策略实现
 * 基于 window.postMessage 的跨域通信机制
 */

import type { 
  CommunicationStrategy, 
  Message, 
  MessageHandler, 
  CommunicationConfig 
} from '../types'
import type { 
  PostMessageConfig, 
  BaseStrategy, 
  StrategyState, 
  StrategyPerformanceMetrics 
} from './types'
import { CommunicationStrategyType } from '../enums'
import { DEFAULT_POST_MESSAGE_CONFIG } from '../constants'

/**
 * PostMessage 监听器信息
 */
interface PostMessageListener {
  id: string
  topic: string
  handler: MessageHandler
  targetWindow?: Window
  targetOrigin?: string
}

/**
 * PostMessage 通信策略
 */
export class PostMessageStrategy implements BaseStrategy {
  private readonly name: string = CommunicationStrategyType.POST_MESSAGE
  private readonly config: Required<PostMessageConfig>
  private state: StrategyState = 'idle' as StrategyState
  private listeners: Map<string, PostMessageListener[]> = new Map()
  private metrics: StrategyPerformanceMetrics
  private eventHandlers: Map<string, Function[]> = new Map()
  private messageListener: ((event: MessageEvent) => void) | null = null
  private nextListenerId = 1
  private pendingMessages: Map<string, { resolve: Function; reject: Function; timeout: NodeJS.Timeout }> = new Map()

  constructor(config: PostMessageConfig = {}) {
    this.config = { ...DEFAULT_POST_MESSAGE_CONFIG, ...config }
    this.metrics = this.initializeMetrics()
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): StrategyPerformanceMetrics {
    return {
      latency: 0,
      throughput: 0,
      errorRate: 0,
      reliability: 1,
      availability: 1,
      connectionTime: 0,
      reconnectionCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0
    }
  }

  /**
   * 启动策略
   */
  async start(): Promise<void> {
    if (this.state !== 'idle') {
      throw new Error(`PostMessage 策略已在运行中，当前状态: ${this.state}`)
    }

    try {
      this.state = 'connecting' as StrategyState
      await this.connect()
      this.state = 'connected' as StrategyState
      this.emit('connected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 停止策略
   */
  async stop(): Promise<void> {
    if (this.state === 'idle' || this.state === 'disconnected') {
      return
    }

    try {
      this.state = 'disconnecting' as StrategyState
      await this.disconnect()
      this.state = 'disconnected' as StrategyState
      this.emit('disconnected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 销毁策略
   */
  async destroy(): Promise<void> {
    await this.stop()
    this.listeners.clear()
    this.pendingMessages.clear()
    this.eventHandlers.clear()
    this.state = 'destroyed' as StrategyState
  }

  /**
   * 连接
   */
  async connect(): Promise<void> {
    const startTime = Date.now()

    // 设置全局消息监听器
    this.messageListener = (event: MessageEvent) => {
      this.handleMessage(event)
    }

    window.addEventListener('message', this.messageListener)
    this.metrics.connectionTime = Date.now() - startTime
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.messageListener) {
      window.removeEventListener('message', this.messageListener)
      this.messageListener = null
    }

    // 清理待处理的消息
    this.pendingMessages.forEach(({ timeout, reject }) => {
      clearTimeout(timeout)
      reject(new Error('连接已断开'))
    })
    this.pendingMessages.clear()
  }

  /**
   * 重连
   */
  async reconnect(): Promise<void> {
    await this.disconnect()
    await this.connect()
    this.metrics.reconnectionCount++
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.state === 'connected'
  }

  /**
   * 发送消息
   */
  async send(message: Message, targetWindow?: Window, targetOrigin?: string): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('PostMessage 策略未连接')
    }

    const startTime = Date.now()

    try {
      // 验证消息
      this.validateMessage(message)

      // 确定目标窗口和源
      const target = targetWindow || window.parent
      const origin = targetOrigin || this.config.targetOrigin

      // 包装消息
      const wrappedMessage = this.wrapMessage(message)

      // 发送消息
      target.postMessage(wrappedMessage, origin)

      // 处理响应（如果需要）
      if (message.expectResponse) {
        await this.waitForResponse(message.id!)
      }

      // 更新指标
      this.updateSendMetrics(startTime, message)
      this.emit('messageSent', message)

    } catch (error) {
      this.handleError(error as Error)
      throw error
    }
  }

  /**
   * 订阅消息
   */
  subscribe(topic: string, handler: MessageHandler, targetWindow?: Window, targetOrigin?: string): () => void {
    if (!this.isConnected()) {
      throw new Error('PostMessage 策略未连接')
    }

    const listenerId = `listener_${this.nextListenerId++}`
    const listener: PostMessageListener = {
      id: listenerId,
      topic,
      handler,
      targetWindow,
      targetOrigin
    }

    const listeners = this.listeners.get(topic) || []
    listeners.push(listener)
    this.listeners.set(topic, listeners)

    this.emit('subscribed', topic)

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(topic, listenerId)
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(topic: string, listenerId?: string): void {
    const listeners = this.listeners.get(topic)
    if (listeners) {
      const filteredListeners = listeners.filter(listener => 
        !listenerId || listener.id !== listenerId
      )
      
      if (filteredListeners.length === 0) {
        this.listeners.delete(topic)
      } else {
        this.listeners.set(topic, filteredListeners)
      }
    }

    this.emit('unsubscribed', topic)
  }

  /**
   * 一次性订阅
   */
  once(topic: string, handler: MessageHandler, targetWindow?: Window, targetOrigin?: string): () => void {
    const wrappedHandler: MessageHandler = (message) => {
      handler(message)
      this.unsubscribe(topic)
    }

    return this.subscribe(topic, wrappedHandler, targetWindow, targetOrigin)
  }

  /**
   * 获取策略名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取策略状态
   */
  get state(): StrategyState {
    return this.state
  }

  /**
   * 获取性能指标
   */
  getMetrics(): StrategyPerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics()
  }

  /**
   * 事件监听
   */
  on<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string) || []
    handlers.push(listener)
    this.eventHandlers.set(event as string, handlers)
  }

  /**
   * 移除事件监听
   */
  off<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      const index = handlers.indexOf(listener)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit<K extends keyof any>(event: K, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          this.handleError(error as Error)
        }
      })
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      // 验证消息来源
      if (this.config.verifyOrigin && !this.isOriginAllowed(event.origin)) {
        return
      }

      // 解析消息
      const message = this.unwrapMessage(event.data)
      if (!message) {
        return
      }

      // 处理响应消息
      if (message.isResponse && message.correlationId) {
        this.handleResponse(message)
        return
      }

      // 查找匹配的监听器
      const listeners = this.listeners.get(message.topic) || []
      
      // 执行处理器
      listeners.forEach(listener => {
        try {
          listener.handler(message)
          this.metrics.messagesReceived++
        } catch (error) {
          this.handleError(error as Error)
        }
      })

      this.emit('messageReceived', message)

    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 验证消息
   */
  private validateMessage(message: Message): void {
    if (!message.topic) {
      throw new Error('消息主题不能为空')
    }

    if (message.payload === undefined) {
      throw new Error('消息负载不能为空')
    }
  }

  /**
   * 包装消息
   */
  private wrapMessage(message: Message): any {
    return {
      type: 'micro-core-message',
      timestamp: Date.now(),
      ...message
    }
  }

  /**
   * 解包消息
   */
  private unwrapMessage(data: any): Message | null {
    if (!data || data.type !== 'micro-core-message') {
      return null
    }

    return {
      id: data.id,
      topic: data.topic,
      payload: data.payload,
      timestamp: data.timestamp,
      source: data.source,
      target: data.target,
      correlationId: data.correlationId,
      isResponse: data.isResponse,
      expectResponse: data.expectResponse
    }
  }

  /**
   * 检查源是否被允许
   */
  private isOriginAllowed(origin: string): boolean {
    if (this.config.allowedOrigins.length === 0) {
      return true
    }

    return this.config.allowedOrigins.includes(origin) || 
           this.config.allowedOrigins.includes('*')
  }

  /**
   * 等待响应
   */
  private waitForResponse(messageId: string): Promise<Message> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingMessages.delete(messageId)
        reject(new Error(`消息 ${messageId} 响应超时`))
      }, this.config.timeout)

      this.pendingMessages.set(messageId, { resolve, reject, timeout })
    })
  }

  /**
   * 处理响应消息
   */
  private handleResponse(message: Message): void {
    const pending = this.pendingMessages.get(message.correlationId!)
    if (pending) {
      clearTimeout(pending.timeout)
      this.pendingMessages.delete(message.correlationId!)
      pending.resolve(message)
    }
  }

  /**
   * 更新发送指标
   */
  private updateSendMetrics(startTime: number, message: Message): void {
    const endTime = Date.now()
    const latency = endTime - startTime

    this.metrics.latency = (this.metrics.latency + latency) / 2
    this.metrics.messagesSent++
    this.metrics.bytesTransferred += this.calculateMessageSize(message)
    this.metrics.throughput = this.metrics.messagesSent / ((endTime - startTime) / 1000)
  }

  /**
   * 计算消息大小
   */
  private calculateMessageSize(message: Message): number {
    try {
      return JSON.stringify(message).length
    } catch {
      return 0
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    this.metrics.errorRate = (this.metrics.errorRate + 1) / (this.metrics.messagesSent + this.metrics.messagesReceived + 1)
    console.error('PostMessage 策略错误:', error)
  }
}