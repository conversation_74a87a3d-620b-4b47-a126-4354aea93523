/**
 * BroadcastChannel 通信策略实现
 * 基于 BroadcastChannel API 的跨标签页通信机制
 */

import type { 
  CommunicationStrategy, 
  Message, 
  MessageHandler, 
  CommunicationConfig 
} from '../types'
import type { 
  BroadcastChannelConfig, 
  BaseStrategy, 
  StrategyState, 
  StrategyPerformanceMetrics 
} from './types'
import { CommunicationStrategyType } from '../enums'
import { DEFAULT_BROADCAST_CHANNEL_CONFIG } from '../constants'

/**
 * BroadcastChannel 监听器信息
 */
interface ChannelListener {
  id: string
  topic: string
  handler: MessageHandler
}

/**
 * BroadcastChannel 通信策略
 */
export class BroadcastChannelStrategy implements BaseStrategy {
  private readonly name: string = CommunicationStrategyType.BROADCAST_CHANNEL
  private readonly config: Required<BroadcastChannelConfig>
  private state: StrategyState = 'idle' as StrategyState
  private channel: BroadcastChannel | null = null
  private listeners: Map<string, ChannelListener[]> = new Map()
  private metrics: StrategyPerformanceMetrics
  private eventHandlers: Map<string, Function[]> = new Map()
  private nextListenerId = 1
  private reconnectTimer: NodeJS.Timeout | null = null
  private reconnectAttempts = 0

  constructor(config: BroadcastChannelConfig = {}) {
    this.config = { ...DEFAULT_BROADCAST_CHANNEL_CONFIG, ...config }
    this.metrics = this.initializeMetrics()
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): StrategyPerformanceMetrics {
    return {
      latency: 0,
      throughput: 0,
      errorRate: 0,
      reliability: 1,
      availability: 1,
      connectionTime: 0,
      reconnectionCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0
    }
  }

  /**
   * 启动策略
   */
  async start(): Promise<void> {
    if (this.state !== 'idle') {
      throw new Error(`BroadcastChannel 策略已在运行中，当前状态: ${this.state}`)
    }

    try {
      this.state = 'connecting' as StrategyState
      await this.connect()
      this.state = 'connected' as StrategyState
      this.emit('connected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 停止策略
   */
  async stop(): Promise<void> {
    if (this.state === 'idle' || this.state === 'disconnected') {
      return
    }

    try {
      this.state = 'disconnecting' as StrategyState
      await this.disconnect()
      this.state = 'disconnected' as StrategyState
      this.emit('disconnected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 销毁策略
   */
  async destroy(): Promise<void> {
    await this.stop()
    this.listeners.clear()
    this.eventHandlers.clear()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    this.state = 'destroyed' as StrategyState
  }

  /**
   * 连接
   */
  async connect(): Promise<void> {
    const startTime = Date.now()

    try {
      // 检查浏览器支持
      if (!window.BroadcastChannel) {
        throw new Error('浏览器不支持 BroadcastChannel API')
      }

      // 创建广播频道
      this.channel = new BroadcastChannel(this.config.channelName)

      // 设置消息监听器
      this.channel.onmessage = (event) => {
        this.handleMessage(event)
      }

      // 设置错误监听器
      this.channel.onmessageerror = (event) => {
        this.handleError(new Error('BroadcastChannel 消息错误'))
      }

      this.metrics.connectionTime = Date.now() - startTime
      this.reconnectAttempts = 0

    } catch (error) {
      throw new Error(`BroadcastChannel 连接失败: ${error}`)
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.channel) {
      this.channel.close()
      this.channel = null
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 重连
   */
  async reconnect(): Promise<void> {
    if (!this.config.autoReconnect) {
      return
    }

    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      throw new Error('重连次数已达上限')
    }

    try {
      await this.disconnect()
      await new Promise(resolve => setTimeout(resolve, this.config.reconnectDelay))
      await this.connect()
      
      this.metrics.reconnectionCount++
      this.reconnectAttempts++
      
    } catch (error) {
      this.scheduleReconnect()
      throw error
    }
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.state === 'connected' && this.channel !== null
  }

  /**
   * 发送消息
   */
  async send(message: Message): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('BroadcastChannel 策略未连接')
    }

    const startTime = Date.now()

    try {
      // 验证消息
      this.validateMessage(message)

      // 包装消息
      const wrappedMessage = this.wrapMessage(message)

      // 压缩消息（如果启用）
      const finalMessage = this.config.enableCompression 
        ? await this.compressMessage(wrappedMessage)
        : wrappedMessage

      // 加密消息（如果启用）
      const encryptedMessage = this.config.enableEncryption
        ? await this.encryptMessage(finalMessage)
        : finalMessage

      // 发送消息
      this.channel!.postMessage(encryptedMessage)

      // 更新指标
      this.updateSendMetrics(startTime, message)
      this.emit('messageSent', message)

    } catch (error) {
      this.handleError(error as Error)
      throw error
    }
  }

  /**
   * 订阅消息
   */
  subscribe(topic: string, handler: MessageHandler): () => void {
    if (!this.isConnected()) {
      throw new Error('BroadcastChannel 策略未连接')
    }

    const listenerId = `listener_${this.nextListenerId++}`
    const listener: ChannelListener = {
      id: listenerId,
      topic,
      handler
    }

    const listeners = this.listeners.get(topic) || []
    listeners.push(listener)
    this.listeners.set(topic, listeners)

    this.emit('subscribed', topic)

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(topic, listenerId)
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(topic: string, listenerId?: string): void {
    const listeners = this.listeners.get(topic)
    if (listeners) {
      const filteredListeners = listeners.filter(listener => 
        !listenerId || listener.id !== listenerId
      )
      
      if (filteredListeners.length === 0) {
        this.listeners.delete(topic)
      } else {
        this.listeners.set(topic, filteredListeners)
      }
    }

    this.emit('unsubscribed', topic)
  }

  /**
   * 一次性订阅
   */
  once(topic: string, handler: MessageHandler): () => void {
    const wrappedHandler: MessageHandler = (message) => {
      handler(message)
      this.unsubscribe(topic)
    }

    return this.subscribe(topic, wrappedHandler)
  }

  /**
   * 获取策略名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取策略状态
   */
  get state(): StrategyState {
    return this.state
  }

  /**
   * 获取性能指标
   */
  getMetrics(): StrategyPerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics()
  }

  /**
   * 事件监听
   */
  on<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string) || []
    handlers.push(listener)
    this.eventHandlers.set(event as string, handlers)
  }

  /**
   * 移除事件监听
   */
  off<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      const index = handlers.indexOf(listener)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit<K extends keyof any>(event: K, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          this.handleError(error as Error)
        }
      })
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      let messageData = event.data

      // 解密消息（如果启用）
      if (this.config.enableEncryption) {
        messageData = this.decryptMessage(messageData)
      }

      // 解压消息（如果启用）
      if (this.config.enableCompression) {
        messageData = this.decompressMessage(messageData)
      }

      // 解包消息
      const message = this.unwrapMessage(messageData)
      if (!message) {
        return
      }

      // 查找匹配的监听器
      const listeners = this.listeners.get(message.topic) || []
      
      // 执行处理器
      listeners.forEach(listener => {
        try {
          listener.handler(message)
          this.metrics.messagesReceived++
        } catch (error) {
          this.handleError(error as Error)
        }
      })

      this.emit('messageReceived', message)

    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 验证消息
   */
  private validateMessage(message: Message): void {
    if (!message.topic) {
      throw new Error('消息主题不能为空')
    }

    if (message.payload === undefined) {
      throw new Error('消息负载不能为空')
    }
  }

  /**
   * 包装消息
   */
  private wrapMessage(message: Message): any {
    return {
      type: 'micro-core-broadcast',
      timestamp: Date.now(),
      ...message
    }
  }

  /**
   * 解包消息
   */
  private unwrapMessage(data: any): Message | null {
    if (!data || data.type !== 'micro-core-broadcast') {
      return null
    }

    return {
      id: data.id,
      topic: data.topic,
      payload: data.payload,
      timestamp: data.timestamp,
      source: data.source,
      target: data.target
    }
  }

  /**
   * 压缩消息
   */
  private async compressMessage(message: any): Promise<any> {
    // 简单的压缩实现，实际项目中可以使用更高效的压缩算法
    try {
      const jsonString = JSON.stringify(message)
      // 这里可以集成 pako 或其他压缩库
      return {
        compressed: true,
        data: jsonString // 实际应该是压缩后的数据
      }
    } catch (error) {
      console.warn('消息压缩失败:', error)
      return message
    }
  }

  /**
   * 解压消息
   */
  private decompressMessage(data: any): any {
    try {
      if (data.compressed) {
        // 这里应该是解压缩逻辑
        return JSON.parse(data.data)
      }
      return data
    } catch (error) {
      console.warn('消息解压失败:', error)
      return data
    }
  }

  /**
   * 加密消息
   */
  private async encryptMessage(message: any): Promise<any> {
    if (!this.config.encryptionKey) {
      return message
    }

    try {
      // 简单的加密实现，实际项目中应该使用更安全的加密算法
      const jsonString = JSON.stringify(message)
      // 这里可以集成 crypto-js 或 Web Crypto API
      return {
        encrypted: true,
        data: btoa(jsonString) // 简单的 base64 编码，实际应该是加密后的数据
      }
    } catch (error) {
      console.warn('消息加密失败:', error)
      return message
    }
  }

  /**
   * 解密消息
   */
  private decryptMessage(data: any): any {
    if (!data.encrypted || !this.config.encryptionKey) {
      return data
    }

    try {
      // 这里应该是解密逻辑
      const decryptedString = atob(data.data)
      return JSON.parse(decryptedString)
    } catch (error) {
      console.warn('消息解密失败:', error)
      return data
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (!this.config.autoReconnect || this.reconnectTimer) {
      return
    }

    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = null
      try {
        await this.reconnect()
      } catch (error) {
        console.warn('重连失败:', error)
      }
    }, this.config.reconnectDelay)
  }

  /**
   * 更新发送指标
   */
  private updateSendMetrics(startTime: number, message: Message): void {
    const endTime = Date.now()
    const latency = endTime - startTime

    this.metrics.latency = (this.metrics.latency + latency) / 2
    this.metrics.messagesSent++
    this.metrics.bytesTransferred += this.calculateMessageSize(message)
    this.metrics.throughput = this.metrics.messagesSent / ((endTime - startTime) / 1000)
  }

  /**
   * 计算消息大小
   */
  private calculateMessageSize(message: Message): number {
    try {
      return JSON.stringify(message).length
    } catch {
      return 0
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    this.metrics.errorRate = (this.metrics.errorRate + 1) / (this.metrics.messagesSent + this.metrics.messagesReceived + 1)
    
    // 如果是连接错误且启用自动重连，则尝试重连
    if (this.config.autoReconnect && this.state === 'connected') {
      this.scheduleReconnect()
    }
    
    console.error('BroadcastChannel 策略错误:', error)
  }
}