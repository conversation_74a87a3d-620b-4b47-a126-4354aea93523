/**
 * Storage 通信策略实现
 * 基于浏览器存储的跨标签页通信机制
 */

import type { 
  CommunicationStrategy, 
  Message, 
  MessageHandler, 
  CommunicationConfig 
} from '../types'
import type { 
  StorageConfig, 
  BaseStrategy, 
  StrategyState, 
  StrategyPerformanceMetrics 
} from './types'
import { CommunicationStrategyType } from '../enums'
import { DEFAULT_STORAGE_CONFIG } from '../constants'

/**
 * Storage 监听器信息
 */
interface StorageListener {
  id: string
  topic: string
  handler: MessageHandler
}

/**
 * 存储消息格式
 */
interface StorageMessage {
  id: string
  topic: string
  payload: any
  timestamp: number
  source?: string
  target?: string
  ttl?: number
}

/**
 * Storage 通信策略
 */
export class StorageStrategy implements BaseStrategy {
  private readonly name: string = CommunicationStrategyType.STORAGE
  private readonly config: Required<StorageConfig>
  private state: StrategyState = 'idle' as StrategyState
  private listeners: Map<string, StorageListener[]> = new Map()
  private metrics: StrategyPerformanceMetrics
  private eventHandlers: Map<string, Function[]> = new Map()
  private nextListenerId = 1
  private pollingTimer: NodeJS.Timeout | null = null
  private lastPollingTime = 0
  private storage: Storage

  constructor(config: StorageConfig = {}) {
    this.config = { ...DEFAULT_STORAGE_CONFIG, ...config }
    this.metrics = this.initializeMetrics()
    
    // 选择存储类型
    this.storage = this.config.storageType === 'sessionStorage' 
      ? sessionStorage 
      : localStorage
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): StrategyPerformanceMetrics {
    return {
      latency: 0,
      throughput: 0,
      errorRate: 0,
      reliability: 1,
      availability: 1,
      connectionTime: 0,
      reconnectionCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0
    }
  }

  /**
   * 启动策略
   */
  async start(): Promise<void> {
    if (this.state !== 'idle') {
      throw new Error(`Storage 策略已在运行中，当前状态: ${this.state}`)
    }

    try {
      this.state = 'connecting' as StrategyState
      await this.connect()
      this.state = 'connected' as StrategyState
      this.emit('connected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 停止策略
   */
  async stop(): Promise<void> {
    if (this.state === 'idle' || this.state === 'disconnected') {
      return
    }

    try {
      this.state = 'disconnecting' as StrategyState
      await this.disconnect()
      this.state = 'disconnected' as StrategyState
      this.emit('disconnected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 销毁策略
   */
  async destroy(): Promise<void> {
    await this.stop()
    this.listeners.clear()
    this.eventHandlers.clear()
    
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
    }
    
    this.state = 'destroyed' as StrategyState
  }

  /**
   * 连接
   */
  async connect(): Promise<void> {
    const startTime = Date.now()

    try {
      // 检查存储可用性
      this.checkStorageAvailability()

      // 设置存储事件监听器（仅对 localStorage 有效）
      if (this.config.storageType === 'localStorage') {
        window.addEventListener('storage', this.handleStorageEvent.bind(this))
      }

      // 启动轮询（如果启用）
      if (this.config.enablePolling) {
        this.startPolling()
      }

      // 清理过期消息
      this.cleanupExpiredMessages()

      this.metrics.connectionTime = Date.now() - startTime

    } catch (error) {
      throw new Error(`Storage 连接失败: ${error}`)
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    // 移除存储事件监听器
    if (this.config.storageType === 'localStorage') {
      window.removeEventListener('storage', this.handleStorageEvent.bind(this))
    }

    // 停止轮询
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
    }
  }

  /**
   * 重连
   */
  async reconnect(): Promise<void> {
    await this.disconnect()
    await this.connect()
    this.metrics.reconnectionCount++
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.state === 'connected'
  }

  /**
   * 发送消息
   */
  async send(message: Message): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Storage 策略未连接')
    }

    const startTime = Date.now()

    try {
      // 验证消息
      this.validateMessage(message)

      // 包装消息
      const storageMessage = this.wrapMessage(message)

      // 生成存储键
      const storageKey = this.generateStorageKey(message.topic, storageMessage.id)

      // 压缩消息（如果启用）
      const finalMessage = this.config.enableCompression 
        ? await this.compressMessage(storageMessage)
        : storageMessage

      // 加密消息（如果启用）
      const encryptedMessage = this.config.enableEncryption
        ? await this.encryptMessage(finalMessage)
        : finalMessage

      // 检查存储大小限制
      this.checkStorageSize(encryptedMessage)

      // 存储消息
      this.storage.setItem(storageKey, JSON.stringify(encryptedMessage))

      // 更新指标
      this.updateSendMetrics(startTime, message)
      this.emit('messageSent', message)

    } catch (error) {
      this.handleError(error as Error)
      throw error
    }
  }

  /**
   * 订阅消息
   */
  subscribe(topic: string, handler: MessageHandler): () => void {
    if (!this.isConnected()) {
      throw new Error('Storage 策略未连接')
    }

    const listenerId = `listener_${this.nextListenerId++}`
    const listener: StorageListener = {
      id: listenerId,
      topic,
      handler
    }

    const listeners = this.listeners.get(topic) || []
    listeners.push(listener)
    this.listeners.set(topic, listeners)

    this.emit('subscribed', topic)

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(topic, listenerId)
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(topic: string, listenerId?: string): void {
    const listeners = this.listeners.get(topic)
    if (listeners) {
      const filteredListeners = listeners.filter(listener => 
        !listenerId || listener.id !== listenerId
      )
      
      if (filteredListeners.length === 0) {
        this.listeners.delete(topic)
      } else {
        this.listeners.set(topic, filteredListeners)
      }
    }

    this.emit('unsubscribed', topic)
  }

  /**
   * 一次性订阅
   */
  once(topic: string, handler: MessageHandler): () => void {
    const wrappedHandler: MessageHandler = (message) => {
      handler(message)
      this.unsubscribe(topic)
    }

    return this.subscribe(topic, wrappedHandler)
  }

  /**
   * 获取策略名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取策略状态
   */
  get state(): StrategyState {
    return this.state
  }

  /**
   * 获取性能指标
   */
  getMetrics(): StrategyPerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics()
  }

  /**
   * 事件监听
   */
  on<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string) || []
    handlers.push(listener)
    this.eventHandlers.set(event as string, handlers)
  }

  /**
   * 移除事件监听
   */
  off<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      const index = handlers.indexOf(listener)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit<K extends keyof any>(event: K, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          this.handleError(error as Error)
        }
      })
    }
  }

  /**
   * 处理存储事件
   */
  private handleStorageEvent(event: StorageEvent): void {
    try {
      // 只处理我们的消息
      if (!event.key || !event.key.startsWith(this.config.keyPrefix)) {
        return
      }

      // 解析新值
      if (!event.newValue) {
        return
      }

      const messageData = JSON.parse(event.newValue)
      
      // 解密消息（如果启用）
      const decryptedMessage = this.config.enableEncryption
        ? this.decryptMessage(messageData)
        : messageData

      // 解压消息（如果启用）
      const decompressedMessage = this.config.enableCompression
        ? this.decompressMessage(decryptedMessage)
        : decryptedMessage

      // 解包消息
      const message = this.unwrapMessage(decompressedMessage)
      if (!message) {
        return
      }

      // 检查消息是否过期
      if (this.isMessageExpired(decompressedMessage)) {
        this.removeExpiredMessage(event.key)
        return
      }

      // 查找匹配的监听器
      const listeners = this.listeners.get(message.topic) || []
      
      // 执行处理器
      listeners.forEach(listener => {
        try {
          listener.handler(message)
          this.metrics.messagesReceived++
        } catch (error) {
          this.handleError(error as Error)
        }
      })

      this.emit('messageReceived', message)

    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 启动轮询
   */
  private startPolling(): void {
    if (this.pollingTimer) {
      return
    }

    this.lastPollingTime = Date.now()

    this.pollingTimer = setInterval(() => {
      this.pollForMessages()
    }, this.config.pollingInterval)
  }

  /**
   * 轮询消息
   */
  private pollForMessages(): void {
    try {
      const currentTime = Date.now()
      const keys = Object.keys(this.storage)

      for (const key of keys) {
        if (!key.startsWith(this.config.keyPrefix)) {
          continue
        }

        try {
          const messageData = JSON.parse(this.storage.getItem(key) || '{}')
          
          // 检查消息时间戳
          if (messageData.timestamp <= this.lastPollingTime) {
            continue
          }

          // 检查消息是否过期
          if (this.isMessageExpired(messageData)) {
            this.removeExpiredMessage(key)
            continue
          }

          // 解密和解压消息
          let processedMessage = messageData
          if (this.config.enableEncryption) {
            processedMessage = this.decryptMessage(processedMessage)
          }
          if (this.config.enableCompression) {
            processedMessage = this.decompressMessage(processedMessage)
          }

          // 解包消息
          const message = this.unwrapMessage(processedMessage)
          if (!message) {
            continue
          }

          // 查找匹配的监听器
          const listeners = this.listeners.get(message.topic) || []
          
          // 执行处理器
          listeners.forEach(listener => {
            try {
              listener.handler(message)
              this.metrics.messagesReceived++
            } catch (error) {
              this.handleError(error as Error)
            }
          })

          this.emit('messageReceived', message)

        } catch (error) {
          // 忽略单个消息的解析错误
          continue
        }
      }

      this.lastPollingTime = currentTime

    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 检查存储可用性
   */
  private checkStorageAvailability(): void {
    try {
      const testKey = `${this.config.keyPrefix}test`
      this.storage.setItem(testKey, 'test')
      this.storage.removeItem(testKey)
    } catch (error) {
      throw new Error(`存储不可用: ${error}`)
    }
  }

  /**
   * 验证消息
   */
  private validateMessage(message: Message): void {
    if (!message.topic) {
      throw new Error('消息主题不能为空')
    }

    if (message.payload === undefined) {
      throw new Error('消息负载不能为空')
    }
  }

  /**
   * 包装消息
   */
  private wrapMessage(message: Message): StorageMessage {
    return {
      id: message.id || `msg_${Date.now()}_${Math.random()}`,
      topic: message.topic,
      payload: message.payload,
      timestamp: Date.now(),
      source: message.source,
      target: message.target,
      ttl: message.ttl || 60000 // 默认1分钟过期
    }
  }

  /**
   * 解包消息
   */
  private unwrapMessage(data: StorageMessage): Message | null {
    if (!data || !data.topic) {
      return null
    }

    return {
      id: data.id,
      topic: data.topic,
      payload: data.payload,
      timestamp: data.timestamp,
      source: data.source,
      target: data.target
    }
  }

  /**
   * 生成存储键
   */
  private generateStorageKey(topic: string, messageId: string): string {
    return `${this.config.keyPrefix}${topic}_${messageId}`
  }

  /**
   * 检查消息是否过期
   */
  private isMessageExpired(message: StorageMessage): boolean {
    if (!message.ttl) {
      return false
    }

    return Date.now() - message.timestamp > message.ttl
  }

  /**
   * 移除过期消息
   */
  private removeExpiredMessage(key: string): void {
    try {
      this.storage.removeItem(key)
    } catch (error) {
      // 忽略删除错误
    }
  }

  /**
   * 清理过期消息
   */
  private cleanupExpiredMessages(): void {
    try {
      const keys = Object.keys(this.storage)

      for (const key of keys) {
        if (!key.startsWith(this.config.keyPrefix)) {
          continue
        }

        try {
          const messageData = JSON.parse(this.storage.getItem(key) || '{}')
          if (this.isMessageExpired(messageData)) {
            this.removeExpiredMessage(key)
          }
        } catch (error) {
          // 如果解析失败，也删除这个键
          this.removeExpiredMessage(key)
        }
      }
    } catch (error) {
      console.warn('清理过期消息失败:', error)
    }
  }

  /**
   * 检查存储大小限制
   */
  private checkStorageSize(message: any): void {
    const messageSize = JSON.stringify(message).length
    
    if (messageSize > this.config.maxStorageSize) {
      throw new Error(`消息大小 ${messageSize} 超过限制 ${this.config.maxStorageSize}`)
    }
  }

  /**
   * 压缩消息
   */
  private async compressMessage(message: any): Promise<any> {
    // 简单的压缩实现
    try {
      const jsonString = JSON.stringify(message)
      return {
        compressed: true,
        data: jsonString // 实际应该是压缩后的数据
      }
    } catch (error) {
      console.warn('消息压缩失败:', error)
      return message
    }
  }

  /**
   * 解压消息
   */
  private decompressMessage(data: any): any {
    try {
      if (data.compressed) {
        return JSON.parse(data.data)
      }
      return data
    } catch (error) {
      console.warn('消息解压失败:', error)
      return data
    }
  }

  /**
   * 加密消息
   */
  private async encryptMessage(message: any): Promise<any> {
    if (!this.config.encryptionKey) {
      return message
    }

    try {
      const jsonString = JSON.stringify(message)
      return {
        encrypted: true,
        data: btoa(jsonString) // 简单的 base64 编码
      }
    } catch (error) {
      console.warn('消息加密失败:', error)
      return message
    }
  }

  /**
   * 解密消息
   */
  private decryptMessage(data: any): any {
    if (!data.encrypted || !this.config.encryptionKey) {
      return data
    }

    try {
      const decryptedString = atob(data.data)
      return JSON.parse(decryptedString)
    } catch (error) {
      console.warn('消息解密失败:', error)
      return data
    }
  }

  /**
   * 更新发送指标
   */
  private updateSendMetrics(startTime: number, message: Message): void {
    const endTime = Date.now()
    const latency = endTime - startTime

    this.metrics.latency = (this.metrics.latency + latency) / 2
    this.metrics.messagesSent++
    this.metrics.bytesTransferred += this.calculateMessageSize(message)
    this.metrics.throughput = this.metrics.messagesSent / ((endTime - startTime) / 1000)
  }

  /**
   * 计算消息大小
   */
  private calculateMessageSize(message: Message): number {
    try {
      return JSON.stringify(message).length
    } catch {
      return 0
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    this.metrics.errorRate = (this.metrics.errorRate + 1) / (this.metrics.messagesSent + this.metrics.messagesReceived + 1)
    console.error('Storage 策略错误:', error)
  }
}