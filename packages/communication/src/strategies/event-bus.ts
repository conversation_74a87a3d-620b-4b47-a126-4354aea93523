/**
 * EventBus 通信策略实现
 * 基于事件总线的同步通信机制
 */

import type { 
  CommunicationStrategy, 
  Message, 
  MessageHandler, 
  CommunicationConfig 
} from '../types'
import type { 
  EventBusConfig, 
  BaseStrategy, 
  StrategyState, 
  StrategyPerformanceMetrics 
} from './types'
import { CommunicationStrategyType } from '../enums'
import { DEFAULT_EVENT_BUS_CONFIG } from '../constants'

/**
 * 事件监听器信息
 */
interface EventListener {
  id: string
  handler: MessageHandler
  once: boolean
  namespace?: string
  pattern?: RegExp
}

/**
 * EventBus 通信策略
 */
export class EventBusStrategy implements BaseStrategy {
  private readonly name: string = CommunicationStrategyType.EVENT_BUS
  private readonly config: Required<EventBusConfig>
  private state: StrategyState = 'idle' as StrategyState
  private listeners: Map<string, EventListener[]> = new Map()
  private wildcardListeners: EventListener[] = []
  private metrics: StrategyPerformanceMetrics
  private eventHandlers: Map<string, Function[]> = new Map()
  private nextListenerId = 1

  constructor(config: EventBusConfig = {}) {
    this.config = { ...DEFAULT_EVENT_BUS_CONFIG, ...config }
    this.metrics = this.initializeMetrics()
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): StrategyPerformanceMetrics {
    return {
      latency: 0,
      throughput: 0,
      errorRate: 0,
      reliability: 1,
      availability: 1,
      connectionTime: 0,
      reconnectionCount: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0
    }
  }

  /**
   * 启动策略
   */
  async start(): Promise<void> {
    if (this.state !== 'idle') {
      throw new Error(`EventBus 策略已在运行中，当前状态: ${this.state}`)
    }

    try {
      this.state = 'connecting' as StrategyState
      await this.connect()
      this.state = 'connected' as StrategyState
      this.emit('connected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 停止策略
   */
  async stop(): Promise<void> {
    if (this.state === 'idle' || this.state === 'disconnected') {
      return
    }

    try {
      this.state = 'disconnecting' as StrategyState
      await this.disconnect()
      this.state = 'disconnected' as StrategyState
      this.emit('disconnected')
    } catch (error) {
      this.state = 'error' as StrategyState
      this.emit('error', error as Error)
      throw error
    }
  }

  /**
   * 销毁策略
   */
  async destroy(): Promise<void> {
    await this.stop()
    this.listeners.clear()
    this.wildcardListeners = []
    this.eventHandlers.clear()
    this.state = 'destroyed' as StrategyState
  }

  /**
   * 连接
   */
  async connect(): Promise<void> {
    const startTime = Date.now()
    // EventBus 是同步的，无需实际连接
    this.metrics.connectionTime = Date.now() - startTime
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    // 清理所有监听器
    this.listeners.clear()
    this.wildcardListeners = []
  }

  /**
   * 重连
   */
  async reconnect(): Promise<void> {
    await this.disconnect()
    await this.connect()
    this.metrics.reconnectionCount++
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.state === 'connected'
  }

  /**
   * 发送消息
   */
  async send(message: Message): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('EventBus 策略未连接')
    }

    const startTime = Date.now()

    try {
      // 验证消息
      this.validateMessage(message)

      // 查找匹配的监听器
      const listeners = this.findListeners(message.topic)

      // 异步或同步执行处理器
      if (this.config.enableAsync) {
        await this.executeHandlersAsync(listeners, message)
      } else {
        this.executeHandlersSync(listeners, message)
      }

      // 更新指标
      this.updateSendMetrics(startTime, message)
      this.emit('messageSent', message)

    } catch (error) {
      this.handleError(error as Error)
      throw error
    }
  }

  /**
   * 订阅消息
   */
  subscribe(topic: string, handler: MessageHandler): () => void {
    if (!this.isConnected()) {
      throw new Error('EventBus 策略未连接')
    }

    const listenerId = `listener_${this.nextListenerId++}`
    const listener: EventListener = {
      id: listenerId,
      handler,
      once: false,
      namespace: this.extractNamespace(topic)
    }

    // 处理通配符订阅
    if (this.config.enableWildcard && this.isWildcardTopic(topic)) {
      listener.pattern = this.createWildcardPattern(topic)
      this.wildcardListeners.push(listener)
    } else {
      const listeners = this.listeners.get(topic) || []
      
      // 检查监听器数量限制
      if (listeners.length >= this.config.maxListeners) {
        throw new Error(`主题 ${topic} 的监听器数量已达到上限: ${this.config.maxListeners}`)
      }

      listeners.push(listener)
      this.listeners.set(topic, listeners)
    }

    this.emit('subscribed', topic)

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(topic, listenerId)
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(topic: string, listenerId?: string): void {
    if (this.isWildcardTopic(topic)) {
      // 移除通配符监听器
      this.wildcardListeners = this.wildcardListeners.filter(listener => 
        !listenerId || listener.id !== listenerId
      )
    } else {
      const listeners = this.listeners.get(topic)
      if (listeners) {
        const filteredListeners = listeners.filter(listener => 
          !listenerId || listener.id !== listenerId
        )
        
        if (filteredListeners.length === 0) {
          this.listeners.delete(topic)
        } else {
          this.listeners.set(topic, filteredListeners)
        }
      }
    }

    this.emit('unsubscribed', topic)
  }

  /**
   * 一次性订阅
   */
  once(topic: string, handler: MessageHandler): () => void {
    const wrappedHandler: MessageHandler = (message) => {
      handler(message)
      this.unsubscribe(topic)
    }

    return this.subscribe(topic, wrappedHandler)
  }

  /**
   * 获取策略名称
   */
  getName(): string {
    return this.name
  }

  /**
   * 获取策略状态
   */
  get state(): StrategyState {
    return this.state
  }

  /**
   * 获取性能指标
   */
  getMetrics(): StrategyPerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = this.initializeMetrics()
  }

  /**
   * 事件监听
   */
  on<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string) || []
    handlers.push(listener)
    this.eventHandlers.set(event as string, handlers)
  }

  /**
   * 移除事件监听
   */
  off<K extends keyof any>(event: K, listener: any): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      const index = handlers.indexOf(listener)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit<K extends keyof any>(event: K, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event as string)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          this.handleError(error as Error)
        }
      })
    }
  }

  /**
   * 验证消息
   */
  private validateMessage(message: Message): void {
    if (!message.topic) {
      throw new Error('消息主题不能为空')
    }

    if (message.payload === undefined) {
      throw new Error('消息负载不能为空')
    }
  }

  /**
   * 查找匹配的监听器
   */
  private findListeners(topic: string): EventListener[] {
    const listeners: EventListener[] = []

    // 精确匹配
    const exactListeners = this.listeners.get(topic) || []
    listeners.push(...exactListeners)

    // 通配符匹配
    if (this.config.enableWildcard) {
      const wildcardMatches = this.wildcardListeners.filter(listener => 
        listener.pattern?.test(topic)
      )
      listeners.push(...wildcardMatches)
    }

    return listeners
  }

  /**
   * 同步执行处理器
   */
  private executeHandlersSync(listeners: EventListener[], message: Message): void {
    listeners.forEach(listener => {
      try {
        listener.handler(message)
        this.metrics.messagesReceived++
      } catch (error) {
        this.handleError(error as Error)
      }
    })
  }

  /**
   * 异步执行处理器
   */
  private async executeHandlersAsync(listeners: EventListener[], message: Message): Promise<void> {
    const promises = listeners.map(async listener => {
      try {
        await listener.handler(message)
        this.metrics.messagesReceived++
      } catch (error) {
        this.handleError(error as Error)
      }
    })

    await Promise.all(promises)
  }

  /**
   * 更新发送指标
   */
  private updateSendMetrics(startTime: number, message: Message): void {
    const endTime = Date.now()
    const latency = endTime - startTime

    this.metrics.latency = (this.metrics.latency + latency) / 2
    this.metrics.messagesSent++
    this.metrics.bytesTransferred += this.calculateMessageSize(message)
    this.metrics.throughput = this.metrics.messagesSent / ((endTime - startTime) / 1000)
  }

  /**
   * 计算消息大小
   */
  private calculateMessageSize(message: Message): number {
    try {
      return JSON.stringify(message).length
    } catch {
      return 0
    }
  }

  /**
   * 提取命名空间
   */
  private extractNamespace(topic: string): string | undefined {
    if (!this.config.namespace) return undefined
    
    const parts = topic.split(':')
    return parts.length > 1 ? parts[0] : undefined
  }

  /**
   * 检查是否为通配符主题
   */
  private isWildcardTopic(topic: string): boolean {
    return topic.includes('*') || topic.includes('?')
  }

  /**
   * 创建通配符模式
   */
  private createWildcardPattern(topic: string): RegExp {
    const escaped = topic
      .replace(/[.+^${}()|[\]\\]/g, '\\$&')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.')
    
    return new RegExp(`^${escaped}$`)
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    this.metrics.errorRate = (this.metrics.errorRate + 1) / (this.metrics.messagesSent + this.metrics.messagesReceived + 1)
    
    if (this.config.errorHandler) {
      this.config.errorHandler(error)
    } else {
      console.error('EventBus 策略错误:', error)
    }
  }
}