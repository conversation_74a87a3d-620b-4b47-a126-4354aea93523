/**
 * 通信策略类型定义
 */

import type { CommunicationStrategy } from '../types'

/**
 * EventBus 策略配置
 */
export interface EventBusConfig {
  maxListeners?: number
  enableWildcard?: boolean
  namespace?: string
  delimiter?: string
  enableAsync?: boolean
  errorHandler?: (error: Error) => void
}

/**
 * PostMessage 策略配置
 */
export interface PostMessageConfig {
  targetOrigin?: string
  verifyOrigin?: boolean
  allowedOrigins?: string[]
  timeout?: number
  enableRetry?: boolean
  retryCount?: number
  retryDelay?: number
}

/**
 * SharedState 策略配置
 */
export interface SharedStateConfig {
  initialState?: Record<string, any>
  deepWatch?: boolean
  persistence?: boolean
  storageKey?: string
  enableHistory?: boolean
  maxHistorySize?: number
  enableValidation?: boolean
  validator?: (state: any) => boolean
}

/**
 * BroadcastChannel 策略配置
 */
export interface BroadcastChannelConfig {
  channelName?: string
  autoReconnect?: boolean
  reconnectDelay?: number
  maxReconnectAttempts?: number
  enableCompression?: boolean
  enableEncryption?: boolean
  encryptionKey?: string
}

/**
 * WebSocket 策略配置
 */
export interface WebSocketConfig {
  url?: string
  protocols?: string[]
  heartbeatInterval?: number
  reconnectInterval?: number
  maxReconnectAttempts?: number
  autoReconnect?: boolean
  enableCompression?: boolean
  enableBinaryMessages?: boolean
  binaryType?: 'blob' | 'arraybuffer'
  headers?: Record<string, string>
}

/**
 * Storage 策略配置
 */
export interface StorageConfig {
  storageType?: 'localStorage' | 'sessionStorage' | 'indexedDB'
  keyPrefix?: string
  pollingInterval?: number
  enablePolling?: boolean
  enableCompression?: boolean
  enableEncryption?: boolean
  encryptionKey?: string
  maxStorageSize?: number
}

/**
 * 策略工厂配置
 */
export interface StrategyFactoryConfig {
  defaultStrategy?: string
  fallbackStrategies?: string[]
  enableAutoFallback?: boolean
  strategyPriority?: string[]
  enablePerformanceMonitoring?: boolean
  enableCaching?: boolean
  cacheSize?: number
  cacheTTL?: number
}

/**
 * 策略性能指标
 */
export interface StrategyPerformanceMetrics {
  latency: number
  throughput: number
  errorRate: number
  reliability: number
  availability: number
  connectionTime: number
  reconnectionCount: number
  messagesSent: number
  messagesReceived: number
  bytesTransferred: number
}

/**
 * 策略状态
 */
export enum StrategyState {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTING = 'disconnecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  DESTROYED = 'destroyed'
}

/**
 * 策略事件
 */
export interface StrategyEvents {
  stateChanged: (state: StrategyState) => void
  connected: () => void
  disconnected: () => void
  error: (error: Error) => void
  messageReceived: (message: any) => void
  messageSent: (message: any) => void
  performanceUpdate: (metrics: StrategyPerformanceMetrics) => void
}

/**
 * 策略基类接口
 */
export interface BaseStrategy extends CommunicationStrategy {
  readonly state: StrategyState
  readonly metrics: StrategyPerformanceMetrics
  
  connect(): Promise<void>
  disconnect(): Promise<void>
  reconnect(): Promise<void>
  isConnected(): boolean
  getMetrics(): StrategyPerformanceMetrics
  resetMetrics(): void
  
  on<K extends keyof StrategyEvents>(event: K, listener: StrategyEvents[K]): void
  off<K extends keyof StrategyEvents>(event: K, listener: StrategyEvents[K]): void
  emit<K extends keyof StrategyEvents>(event: K, ...args: Parameters<StrategyEvents[K]>): void
}

/**
 * 消息序列化器接口
 */
export interface MessageSerializer {
  serialize(message: any): string | ArrayBuffer
  deserialize(data: string | ArrayBuffer): any
  getContentType(): string
}

/**
 * 消息压缩器接口
 */
export interface MessageCompressor {
  compress(data: string | ArrayBuffer): Promise<ArrayBuffer>
  decompress(data: ArrayBuffer): Promise<string | ArrayBuffer>
  getCompressionRatio(): number
}

/**
 * 消息加密器接口
 */
export interface MessageEncryptor {
  encrypt(data: string | ArrayBuffer, key: string): Promise<ArrayBuffer>
  decrypt(data: ArrayBuffer, key: string): Promise<string | ArrayBuffer>
  generateKey(): string
  validateKey(key: string): boolean
}

/**
 * 连接管理器接口
 */
export interface ConnectionManager {
  connect(): Promise<void>
  disconnect(): Promise<void>
  reconnect(): Promise<void>
  isConnected(): boolean
  getConnectionState(): StrategyState
  setReconnectStrategy(strategy: ReconnectStrategy): void
}

/**
 * 重连策略接口
 */
export interface ReconnectStrategy {
  shouldReconnect(attempt: number, error?: Error): boolean
  getDelay(attempt: number): number
  reset(): void
}

/**
 * 指数退避重连策略配置
 */
export interface ExponentialBackoffConfig {
  initialDelay?: number
  maxDelay?: number
  multiplier?: number
  jitter?: boolean
  maxAttempts?: number
}

/**
 * 固定间隔重连策略配置
 */
export interface FixedIntervalConfig {
  interval?: number
  maxAttempts?: number
}

/**
 * 线性退避重连策略配置
 */
export interface LinearBackoffConfig {
  initialDelay?: number
  increment?: number
  maxDelay?: number
  maxAttempts?: number
}

/**
 * 消息缓冲区接口
 */
export interface MessageBuffer {
  add(message: any): void
  get(): any[]
  clear(): void
  size(): number
  isFull(): boolean
  isEmpty(): boolean
}

/**
 * 消息过滤器接口
 */
export interface MessageFilter {
  filter(message: any): boolean
  addRule(rule: FilterRule): void
  removeRule(ruleId: string): void
  clearRules(): void
}

/**
 * 过滤规则
 */
export interface FilterRule {
  id: string
  condition: (message: any) => boolean
  action: 'allow' | 'deny'
  priority: number
}

/**
 * 消息路由器接口
 */
export interface MessageRouter {
  route(message: any): string[]
  addRoute(pattern: string, targets: string[]): void
  removeRoute(pattern: string): void
  clearRoutes(): void
}

/**
 * 路由规则
 */
export interface RouteRule {
  pattern: string | RegExp
  targets: string[]
  priority: number
  condition?: (message: any) => boolean
}

/**
 * 负载均衡器接口
 */
export interface LoadBalancer {
  selectTarget(targets: string[], message?: any): string
  updateTargets(targets: string[]): void
  getTargets(): string[]
  getMetrics(): Record<string, any>
}

/**
 * 负载均衡策略
 */
export enum LoadBalancingStrategy {
  ROUND_ROBIN = 'round-robin',
  RANDOM = 'random',
  LEAST_CONNECTIONS = 'least-connections',
  WEIGHTED_ROUND_ROBIN = 'weighted-round-robin',
  HASH = 'hash'
}

/**
 * 目标权重配置
 */
export interface TargetWeight {
  target: string
  weight: number
}

/**
 * 健康检查器接口
 */
export interface HealthChecker {
  check(target: string): Promise<boolean>
  startMonitoring(targets: string[]): void
  stopMonitoring(): void
  getHealthStatus(): Record<string, boolean>
  on(event: 'healthChanged', listener: (target: string, healthy: boolean) => void): void
}

/**
 * 健康检查配置
 */
export interface HealthCheckConfig {
  interval?: number
  timeout?: number
  retryCount?: number
  healthyThreshold?: number
  unhealthyThreshold?: number
}

/**
 * 断路器接口
 */
export interface CircuitBreaker {
  execute<T>(fn: () => Promise<T>): Promise<T>
  getState(): CircuitBreakerState
  getMetrics(): CircuitBreakerMetrics
  reset(): void
}

/**
 * 断路器状态
 */
export enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half-open'
}

/**
 * 断路器指标
 */
export interface CircuitBreakerMetrics {
  successCount: number
  failureCount: number
  timeoutCount: number
  successRate: number
  averageResponseTime: number
  lastFailureTime?: number
}

/**
 * 断路器配置
 */
export interface CircuitBreakerConfig {
  failureThreshold?: number
  timeout?: number
  resetTimeout?: number
  monitoringPeriod?: number
  expectedExceptionPredicate?: (error: Error) => boolean
}