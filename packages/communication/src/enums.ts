/**
 * 通信包枚举定义
 */

/**
 * 通信策略类型
 */
export enum CommunicationStrategyType {
  /** 事件总线 */
  EVENT_BUS = 'eventbus',
  /** PostMessage */
  POST_MESSAGE = 'postmessage',
  /** 共享状态 */
  SHARED_STATE = 'sharedstate',
  /** 广播频道 */
  BROADCAST_CHANNEL = 'broadcastchannel',
  /** WebSocket */
  WEBSOCKET = 'websocket',
  /** 存储通信 */
  STORAGE = 'storage'
}

/**
 * 通信状态
 */
export enum CommunicationState {
  /** 未激活 */
  INACTIVE = 'inactive',
  /** 激活中 */
  ACTIVE = 'active',
  /** 连接中 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 断开连接中 */
  DISCONNECTING = 'disconnecting',
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 错误状态 */
  ERROR = 'error',
  /** 已销毁 */
  DESTROYED = 'destroyed'
}

/**
 * 消息类型
 */
export enum MessageType {
  /** 事件消息 */
  EVENT = 'event',
  /** 请求消息 */
  REQUEST = 'request',
  /** 响应消息 */
  RESPONSE = 'response',
  /** 通知消息 */
  NOTIFICATION = 'notification',
  /** 命令消息 */
  COMMAND = 'command',
  /** 查询消息 */
  QUERY = 'query',
  /** 状态消息 */
  STATE = 'state',
  /** 错误消息 */
  ERROR = 'error'
}

/**
 * 消息优先级
 */
export enum MessagePriority {
  /** 低优先级 */
  LOW = 'low',
  /** 普通优先级 */
  NORMAL = 'normal',
  /** 高优先级 */
  HIGH = 'high',
  /** 紧急优先级 */
  URGENT = 'urgent'
}

/**
 * 通信事件类型
 */
export enum CommunicationEventType {
  /** 策略已添加 */
  STRATEGY_ADDED = 'strategyAdded',
  /** 策略已移除 */
  STRATEGY_REMOVED = 'strategyRemoved',
  /** 消息已发送 */
  MESSAGE_SENT = 'messageSent',
  /** 消息已接收 */
  MESSAGE_RECEIVED = 'messageReceived',
  /** 已订阅 */
  SUBSCRIBED = 'subscribed',
  /** 已取消订阅 */
  UNSUBSCRIBED = 'unsubscribed',
  /** 已启动 */
  STARTED = 'started',
  /** 已停止 */
  STOPPED = 'stopped',
  /** 已销毁 */
  DESTROYED = 'destroyed',
  /** 发生错误 */
  ERROR = 'error'
}