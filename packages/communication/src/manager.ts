/**
 * 通信管理器
 * 负责管理多种通信策略和消息路由
 */

import type {
  CommunicationStrategy,
  CommunicationManagerConfig,
  Message,
  MessageHandler,
  CommunicationEvent,
  CommunicationEventListener,
  CommunicationStats,
  CommunicationMetrics,
  SubscriptionOptions,
  PublishOptions
} from './types'
import { CommunicationStrategyFactory } from './factory'
import { CommunicationState, MessageType, MessagePriority } from './enums'
import { COMMUNICATION_EVENTS, DEFAULT_COMMUNICATION_CONFIG } from './constants'
import { CommunicationError } from './errors'
import { validateMessage, createMessageId } from './utils'

export class CommunicationManager {
  private strategies: Map<string, CommunicationStrategy> = new Map()
  private subscriptions: Map<string, Map<string, MessageHandler>> = new Map()
  private factory: CommunicationStrategyFactory
  private config: CommunicationManagerConfig
  private state: CommunicationState = CommunicationState.INACTIVE
  private metrics: CommunicationMetrics
  private eventListeners: Map<string, CommunicationEventListener[]> = new Map()

  constructor(config: CommunicationManagerConfig = {}) {
    this.config = {
      ...DEFAULT_COMMUNICATION_CONFIG,
      ...config
    }

    this.factory = new CommunicationStrategyFactory()
    
    this.metrics = {
      messagesSent: 0,
      messagesReceived: 0,
      messagesDropped: 0,
      averageLatency: 0,
      errorCount: 0,
      activeSubscriptions: 0,
      totalLatency: 0
    }

    // 初始化默认策略
    if (this.config.defaultStrategies) {
      this.config.defaultStrategies.forEach(strategyType => {
        this.addStrategy(strategyType, strategyType)
      })
    }
  }

  /**
   * 添加通信策略
   */
  addStrategy(name: string, type: string, config?: any): void {
    try {
      const strategy = this.factory.createStrategy(type, config)
      this.strategies.set(name, strategy)
      
      // 设置消息处理器
      strategy.onMessage((message: Message) => {
        this.handleIncomingMessage(message)
      })

      this.emitEvent('strategyAdded', { strategyName: name, strategyType: type })
    } catch (error) {
      throw new CommunicationError(`添加策略失败: ${error}`, 'STRATEGY_ADD_FAILED')
    }
  }

  /**
   * 移除通信策略
   */
  removeStrategy(name: string): void {
    const strategy = this.strategies.get(name)
    if (!strategy) {
      throw new CommunicationError(`策略 ${name} 不存在`, 'STRATEGY_NOT_FOUND')
    }

    try {
      strategy.destroy()
      this.strategies.delete(name)
      this.emitEvent('strategyRemoved', { strategyName: name })
    } catch (error) {
      throw new CommunicationError(`移除策略失败: ${error}`, 'STRATEGY_REMOVE_FAILED')
    }
  }

  /**
   * 发布消息
   */
  publish(topic: string, data: any, options: PublishOptions = {}): void {
    const message: Message = {
      id: createMessageId(),
      type: options.type || MessageType.EVENT,
      topic,
      data,
      timestamp: Date.now(),
      priority: options.priority || MessagePriority.NORMAL,
      source: options.source || 'unknown',
      target: options.target
    }

    // 验证消息
    if (!validateMessage(message)) {
      this.metrics.messagesDropped++
      throw new CommunicationError('消息验证失败', 'MESSAGE_VALIDATION_FAILED')
    }

    const startTime = Date.now()

    try {
      // 选择策略发送消息
      const strategyName = options.strategy || this.selectStrategy(message)
      const strategy = this.strategies.get(strategyName)
      
      if (!strategy) {
        throw new CommunicationError(`策略 ${strategyName} 不存在`, 'STRATEGY_NOT_FOUND')
      }

      strategy.send(message)
      
      // 更新指标
      this.metrics.messagesSent++
      this.updateLatencyMetrics(Date.now() - startTime)
      
      this.emitEvent('messageSent', { message, strategy: strategyName })
    } catch (error) {
      this.metrics.errorCount++
      this.metrics.messagesDropped++
      this.emitEvent('error', { error, message })
      throw error
    }
  }

  /**
   * 订阅消息
   */
  subscribe(topic: string, handler: MessageHandler, options: SubscriptionOptions = {}): string {
    const subscriptionId = createMessageId()
    
    if (!this.subscriptions.has(topic)) {
      this.subscriptions.set(topic, new Map())
    }
    
    this.subscriptions.get(topic)!.set(subscriptionId, handler)
    this.metrics.activeSubscriptions++
    
    this.emitEvent('subscribed', { topic, subscriptionId, options })
    
    return subscriptionId
  }

  /**
   * 取消订阅
   */
  unsubscribe(topic: string, subscriptionId: string): void {
    const topicSubscriptions = this.subscriptions.get(topic)
    if (topicSubscriptions && topicSubscriptions.has(subscriptionId)) {
      topicSubscriptions.delete(subscriptionId)
      this.metrics.activeSubscriptions--
      
      if (topicSubscriptions.size === 0) {
        this.subscriptions.delete(topic)
      }
      
      this.emitEvent('unsubscribed', { topic, subscriptionId })
    }
  }

  /**
   * 请求-响应模式
   */
  async request(topic: string, data: any, options: PublishOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const requestId = createMessageId()
      const timeout = options.timeout || this.config.defaultTimeout || 5000
      
      // 设置响应监听器
      const responseHandler = (message: Message) => {
        if (message.correlationId === requestId) {
          this.unsubscribe(`${topic}:response`, responseSubscriptionId)
          clearTimeout(timeoutHandle)
          resolve(message.data)
        }
      }
      
      const responseSubscriptionId = this.subscribe(`${topic}:response`, responseHandler)
      
      // 设置超时
      const timeoutHandle = setTimeout(() => {
        this.unsubscribe(`${topic}:response`, responseSubscriptionId)
        reject(new CommunicationError('请求超时', 'REQUEST_TIMEOUT'))
      }, timeout)
      
      // 发送请求
      this.publish(topic, data, {
        ...options,
        type: MessageType.REQUEST,
        correlationId: requestId
      })
    })
  }

  /**
   * 响应请求
   */
  respond(topic: string, data: any, correlationId: string, options: PublishOptions = {}): void {
    this.publish(`${topic}:response`, data, {
      ...options,
      type: MessageType.RESPONSE,
      correlationId
    })
  }

  /**
   * 启动通信管理器
   */
  start(): void {
    if (this.state === CommunicationState.ACTIVE) {
      return
    }

    try {
      // 启动所有策略
      this.strategies.forEach((strategy, name) => {
        try {
          strategy.start()
        } catch (error) {
          console.warn(`启动策略 ${name} 失败:`, error)
        }
      })

      this.state = CommunicationState.ACTIVE
      this.emitEvent('started', {})
    } catch (error) {
      this.state = CommunicationState.ERROR
      throw new CommunicationError(`启动通信管理器失败: ${error}`, 'START_FAILED')
    }
  }

  /**
   * 停止通信管理器
   */
  stop(): void {
    if (this.state === CommunicationState.INACTIVE) {
      return
    }

    try {
      // 停止所有策略
      this.strategies.forEach((strategy, name) => {
        try {
          strategy.stop()
        } catch (error) {
          console.warn(`停止策略 ${name} 失败:`, error)
        }
      })

      this.state = CommunicationState.INACTIVE
      this.emitEvent('stopped', {})
    } catch (error) {
      this.state = CommunicationState.ERROR
      throw new CommunicationError(`停止通信管理器失败: ${error}`, 'STOP_FAILED')
    }
  }

  /**
   * 销毁通信管理器
   */
  destroy(): void {
    try {
      // 销毁所有策略
      this.strategies.forEach((strategy, name) => {
        try {
          strategy.destroy()
        } catch (error) {
          console.warn(`销毁策略 ${name} 失败:`, error)
        }
      })

      // 清理资源
      this.strategies.clear()
      this.subscriptions.clear()
      this.eventListeners.clear()

      this.state = CommunicationState.DESTROYED
      this.emitEvent('destroyed', {})
    } catch (error) {
      throw new CommunicationError(`销毁通信管理器失败: ${error}`, 'DESTROY_FAILED')
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): CommunicationStats {
    return {
      strategiesCount: this.strategies.size,
      subscriptionsCount: this.metrics.activeSubscriptions,
      messagesSent: this.metrics.messagesSent,
      messagesReceived: this.metrics.messagesReceived,
      messagesDropped: this.metrics.messagesDropped,
      errorCount: this.metrics.errorCount,
      averageLatency: this.metrics.averageLatency,
      state: this.state
    }
  }

  /**
   * 获取指标
   */
  getMetrics(): CommunicationMetrics {
    return { ...this.metrics }
  }

  /**
   * 事件监听
   */
  on(eventType: string, listener: CommunicationEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, [])
    }
    this.eventListeners.get(eventType)!.push(listener)
  }

  /**
   * 移除事件监听
   */
  off(eventType: string, listener: CommunicationEventListener): void {
    const listeners = this.eventListeners.get(eventType)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleIncomingMessage(message: Message): void {
    this.metrics.messagesReceived++
    
    const topicSubscriptions = this.subscriptions.get(message.topic)
    if (topicSubscriptions) {
      topicSubscriptions.forEach((handler, subscriptionId) => {
        try {
          handler(message)
        } catch (error) {
          this.metrics.errorCount++
          this.emitEvent('error', { error, message, subscriptionId })
        }
      })
    }
    
    this.emitEvent('messageReceived', { message })
  }

  /**
   * 选择通信策略
   */
  private selectStrategy(message: Message): string {
    // 简单的策略选择逻辑
    if (this.strategies.has('eventbus')) {
      return 'eventbus'
    }
    
    const firstStrategy = this.strategies.keys().next().value
    if (!firstStrategy) {
      throw new CommunicationError('没有可用的通信策略', 'NO_STRATEGY_AVAILABLE')
    }
    
    return firstStrategy
  }

  /**
   * 更新延迟指标
   */
  private updateLatencyMetrics(latency: number): void {
    this.metrics.totalLatency += latency
    this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.messagesSent
  }

  /**
   * 触发事件
   */
  private emitEvent(type: string, data: any): void {
    const event: CommunicationEvent = {
      type: type as any,
      timestamp: Date.now(),
      data
    }
    
    const listeners = this.eventListeners.get(type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event)
        } catch (error) {
          console.error('通信事件监听器执行失败:', error)
        }
      })
    }
  }
}